const pricingPlan = require("../controllers/pricing-plan-controller");
const verifyToken = require("../middlewares/verify-token");
const router = require("express").Router();

module.exports = (app) => {

    // ------------------------- pricing-plan -------------------------

    // create new pricing plan
    router.post("/create-plan", pricingPlan.createPlan);

    // get all pricing plans
    router.get("/plans", pricingPlan.getPlans);

    // update existing pricing plan by id
    router.put("/update-plan/:id", pricingPlan.updatePlan);

    // delete pricing plan by id
    router.delete("/delete-plan/:id", pricingPlan.deletePlan);
    
    app.use("/admin/api/v1", router);

};

