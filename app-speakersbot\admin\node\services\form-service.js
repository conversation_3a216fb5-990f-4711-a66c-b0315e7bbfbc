const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const {
  sequelize,
  FormType,
  FormQuestion,
  Speakers,
  SpeakerDetails,
} = require("../models");

const formService = {};

/* ------------------------- Helper functions ------------------------- */

/**
 * Normalize a title for duplicate checks:
 *  - lowercase
 *  - remove spaces, dashes, underscores
 */
function normalizeTitle(title = "") {
  return String(title).toLowerCase().replace(/[\s\-_]+/g, "");
}

/**
 * Parse various possible `options` inputs into a JSON string for DB storage.
 * Accepts:
 *  - array -> JSON.stringify(array)
 *  - object -> JSON.stringify(object)
 *  - string (either JSON string or "a,b,c") -> JSON.stringify(parsedArray)
 * Returns null when nothing valid supplied.
 */
function parseOptionsToJson(input) {
  if (input == null) return null;

  if (Array.isArray(input)) return JSON.stringify(input);

  if (typeof input === "object") {
    try {
      return JSON.stringify(input);
    } catch {
      return null;
    }
  }

  if (typeof input === "string") {
    // try parse as JSON
    try {
      const parsed = JSON.parse(input);
      // if parsed result is array or object, stringify and return
      if (Array.isArray(parsed) || typeof parsed === "object") {
        return JSON.stringify(parsed);
      }
    } catch {
      // not valid JSON — fallback to comma-split
      const arr = input.split(",").map((s) => s.trim()).filter(Boolean);
      if (arr.length > 0) return JSON.stringify(arr);
    }
  }

  return null;
}

/**
 * Get numeric last id used in FormQuestion (DB auto-increment id)
 * Returns number (0 if none)
 */
async function getLastQuestionNumericId(transaction) {
  const last = await FormQuestion.max("id", { transaction, paranoid: false });
  return last ? Number(last) : 0;
}

/**
 * Map array of FormQuestion instances to structured objects with options parsed
 * @param {Array} questions - array of FormQuestion instances
 * @returns {Array} - each question mapped to { id, question, placeholder, field_type, field_id, is_required, options }
 */
formService.structureQuestions = (questions = []) => {
  return questions.map((q) => {
    let options = null;
    if (q.options_json) {
      try {
        options = JSON.parse(q.options_json);
      } catch {
        options = null;
      }
    }

    return {
      id: q.id,
      question: q.question,
      placeholder: q.placeholder,
      field_type: q.field_type,
      field_id: q.field_id,
      is_required: q.is_required,
      options,
      is_deletable: q?.is_deletable,
    };
  });
}

/* ------------------------- Core service methods ------------------------- */

/**
 * Create a form with questions.
 * - Prevents duplicate title (normalized)
 * - Auto-assigns priority (max + 1)
 * - Auto-generates field_id as `field-${id}` (sequential based on FormQuestion.id)
 * - Serializes options into options_json
 * - Creates speaker details for existing speakers for each new question
 *
 * @param {string} title
 * @param {Array} questions  // each question: { question, placeholder, field_type, is_required, options }
 */
formService.createFormWithQuestions = async (title, questions = []) => {
  const transaction = await sequelize.transaction();
  try {
    if (!title || !String(title).trim()) {
      throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Title is required");
    }
    const normalized = normalizeTitle(title);

    // check duplicate (normalized)
    const existingForms = await FormType.findAll({ attributes: ["id", "title"], paranoid: false, transaction });
    const hasDuplicate = existingForms.some((f) => normalizeTitle(f.title) === normalized);
    if (hasDuplicate) {
      throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Form with this title already exists");
    }

    // priority (max + 1)
    const maxPriority = await FormType.max("priority", { transaction });
    const priority = maxPriority ? maxPriority + 1 : 1;

    // create form
    const createdForm = await FormType.create({ title: title.trim(), priority }, { transaction });

    // create questions if provided
    if (Array.isArray(questions) && questions.length > 0) {
      // base counter from last numeric(field) id
      let counter = await getLastQuestionNumericId(transaction);

      const prepared = questions.map((q) => {
        counter++;
        const qText = q.question ? String(q.question).trim() : null;
        const placeholder = q.placeholder ? String(q.placeholder).trim() : null;
        return {
          form_type_id: createdForm.id,
          question: qText,
          placeholder: placeholder,
          field_type: q.field_type,
          is_required: q.is_required ?? false,
          field_id: `field-${counter}`,
          options_json: ['Select', 'Multi-Select', 'Multi-Text', 'Radio'].includes(q?.field_type)
            ? parseOptionsToJson(q.options ?? q.options_json)
            : null,
        };
      });

      await FormQuestion.bulkCreate(prepared, { transaction });

      // sync speaker details for all existing speakers
      await formService.syncExistingSpeakers(prepared, transaction);
    }

    await transaction.commit();
    return createdForm;
  } catch (err) {
    await transaction.rollback();
    throw err;
  }
};

/**
 * Update form's title and/or priority.
 * If priorities array provided, will update those via updatePriorities.
 *
 * @param {number|string} formId
 * @param {Object} title 
 */
formService.updateForm = async (formId, title) => {
  try {
    if (!formId) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "formId is required");

    const getForm = await FormType.findByPk(formId);
    if (!getForm) throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Form not found");

    // title update with duplicate check (exclude current form)
    if (title && String(title).trim() !== getForm.title) {
      const normalized = normalizeTitle(title);
      const existingForms = await FormType.findAll({ attributes: ["id", "title"] });
      const hasDuplicate = existingForms.some(
        (f) => f.id !== Number(formId) && normalizeTitle(f.title) === normalized
      );
      if (hasDuplicate) {
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Form with this title already exists");
      }
      getForm.title = String(title).trim();
    }

    await getForm.save();

    return getForm;
  } catch (err) {
    throw err;
  }
};

/**
 * Update priorities (bulk).
 * @param {Array} priorities  [{ id, priority }, ...]
 */
formService.updatePriorities = async (priorities) => {
  try {
    for (const p of priorities) {
      if (!p || p.id == null || p.priority == null) continue;
      await FormType.update({ priority: p.priority }, { where: { id: p.id } });
    }
    const updatedForms = await FormType.findAll({ attributes: ["id", "title", "priority", "is_deletable"] });
    return updatedForms;
  } catch (err) {
    throw err;
  }
};

/**
 * Soft-delete a whole form and its related speaker details and questions.
 * (Assumes models have paranoid=true where applicable.)
 */
formService.deleteForm = async (formId) => {
  const transaction = await sequelize.transaction();
  try {
    const getForm = await FormType.findByPk(formId, { transaction });
    if (!getForm) throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Form not found");

    if(getForm.is_deletable === '0') {
      throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Form is not deletable");
    }

    const questions = await FormQuestion.findAll({
      where: { form_type_id: getForm.id },
      attributes: ["field_id"],
      transaction,
    });

    if (questions.length > 0) {
      const fieldIds = questions.map((q) => q.field_id).filter(Boolean);

      if (fieldIds.length > 0) {
        await SpeakerDetails.destroy({ where: { field_id: fieldIds }, transaction });
      }
      await FormQuestion.destroy({ where: { form_type_id: getForm.id }, transaction });
    }
    await getForm.destroy({ transaction });

    await transaction.commit();
    return true;
  } catch (err) {
    await transaction.rollback();
    throw err;
  }
};

/**
 * Get all forms ordered by priority ascending
 */
formService.getFormswithPriorities = async () => {
  const formsData = await FormType.findAll({
    attributes: ["id", "title", "priority", "is_deletable"],
    order: [["priority", "ASC"]],
  });

  if (!formsData || !formsData.length) {
    return [];
  }

  return formsData;
};

/**
 * Get a form (by id) and its questions
 */
formService.getFormAndQuestions = async (formId) => {
  const formData = await FormType.findOne({
    where: { id: Number(formId) },
    attributes: ["id", "title", "priority", "is_deletable"],
    include: [
      {
        model: FormQuestion,
        as: "questions",
        attributes: ["id", "question", "placeholder", "field_type", "field_id", "is_required", "options_json", "is_deletable"]
      },
    ],
  });

  if (!formData) return null;

  return formData;
};

/**
 * Create a single field for a form and create speaker-details rows for ALL speakers.
 *
 * @param {number|string} formId
 * @param {Object} fieldData { question, placeholder, field_type, is_required, options }
 */
formService.createFormField = async (formId, fieldData = {}) => {
  const transaction = await sequelize.transaction();
  try {
    const formData = await FormType.findByPk(formId, { transaction });
    if (!formData) throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Form not found");

    // compute next numeric id and field_id
    const lastNumeric = await getLastQuestionNumericId(transaction);
    const newNumeric = lastNumeric + 1;
    const newFieldId = `field-${newNumeric}`;

    const data = {
      form_type_id: formData.id,
      question: fieldData.question ? String(fieldData.question).trim() : null,
      placeholder: fieldData.placeholder ? String(fieldData.placeholder).trim() : null,
      field_type: fieldData.field_type,
      is_required: fieldData?.is_required ?? false,
      field_id: newFieldId,
      options_json: ['Select', 'Multi-Select', 'Multi-Text', 'Radio'].includes(fieldData?.field_type)
        ? parseOptionsToJson(fieldData.options ?? fieldData.options_json)
        : null,
    };

    const created = await FormQuestion.create(data, { transaction });

    if(created.field_id.split("-")[1] !== newNumeric.toString()) {
      await created.update({ field_id: newFieldId }, { transaction });
    }

    // sync for existing speakers
    await formService.syncExistingSpeakers([{ field_id: created.field_id, question: created.question }], transaction);

    await transaction.commit();
    return created;
  } catch (err) {
    await transaction.rollback();
    throw err;
  }
};

/**
 * Update an existing field (by PK id). Also updates SpeakerDetails.key values
 * if question text changed.
 *
 * @param {number} id (FormQuestion PK)
 * @param {Object} fieldData { question?, placeholder?, is_required?, options? }
 */
formService.updateFormField = async (id, fieldData = {}) => {
  const transaction = await sequelize.transaction();
  try {
    const field = await FormQuestion.findByPk(id, { transaction });
    if (!field) throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Field not found");

    const updates = {};

    if (fieldData.question !== undefined) {
      updates.question = String(fieldData.question).trim();
    }
    if (fieldData.placeholder !== undefined) {
      updates.placeholder = fieldData.placeholder ? String(fieldData.placeholder).trim() : null;
    }
    // if (fieldData.field_type !== undefined) {
    //   updates.field_type = fieldData.field_type;
    // }
    if (fieldData.is_required !== undefined) {
      updates.is_required = fieldData.is_required;
    }

    // options handling
    if (fieldData.options !== undefined || fieldData.options_json !== undefined) {
      const optionsRaw = fieldData.options ?? fieldData.options_json;
      updates.options_json = parseOptionsToJson(optionsRaw);
    }

    // if question text changed, update SpeakerDetails.key for this field_id
    if (updates.question && updates.question !== field.question) {
      await SpeakerDetails.update(
        { key: updates.question },
        { where: { field_id: field.field_id }, transaction }
      );
    }

    await field.update(updates, { transaction });

    await transaction.commit();
    return field;
  } catch (err) {
    await transaction.rollback();
    throw err;
  }
};

/**
 * Delete a field (soft delete). Accepts either numeric PK id or field_id string (field-#).
 * Also removes speaker details for that field_id.
 */
formService.deleteFormField = async (fieldId) => {
  const transaction = await sequelize.transaction();
  try {
    // Try find by PK id first
    let field = null;
    if (!isNaN(Number(fieldId))) {
      field = await FormQuestion.findByPk(Number(fieldId), { transaction });
    }

    // If not found by PK, try field_id
    if (!field) {
      field = await FormQuestion.findOne({ where: { field_id: fieldId }, transaction });
    }

    if (!field) throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Form field not found");

    if(field.is_deletable === '0') {
      throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Field is not deletable");
    }

    // delete speaker details for this field
    await SpeakerDetails.destroy({ where: { field_id: field.field_id }, transaction });

    // delete the field itself (soft delete paranoid)
    await field.destroy({ transaction });

    await transaction.commit();
    return true;
  } catch (err) {
    await transaction.rollback();
    throw err;
  }
};

/**
 * For every existing speaker, create SpeakerDetails entries for the provided questions.
 * `questions` may be objects containing { field_id, question } OR created FormQuestion instances.
 * transaction is optional but recommended when called from another transactional flow.
 */
formService.syncExistingSpeakers = async (questions, transaction = null) => {
  // no-op if nothing to do
  if (!Array.isArray(questions) || questions.length === 0) return;

  // fetch speakers
  const speakers = await Speakers.findAll({ attributes: ["id"], transaction });

  if (!Array.isArray(speakers) || speakers.length === 0) return;

  const toInsert = [];

  for (const s of speakers) {
    for (const q of questions) {
      const fieldId = q.field_id;
      const questionText = q.question;
      if (!fieldId) continue;

      toInsert.push({
        speaker_id: s.id,
        field_id: fieldId,
        key: questionText,
        value: '',
      });
    }
  }
  if (toInsert.length > 0) {
    await SpeakerDetails.bulkCreate(toInsert, { transaction });
  }
};

module.exports = formService;