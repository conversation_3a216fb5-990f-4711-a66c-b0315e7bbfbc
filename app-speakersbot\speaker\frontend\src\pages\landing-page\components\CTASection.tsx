import { But<PERSON> } from "@/components/ui/button";
import buttonGradient from "@/assets/images//CTA-Gradient.png";

const CTASection = () => {
  return (
    <section
      className="relative lg:py-[110px] py-[50px] lg:px-10 px-5"
      style={{
        background:
          "linear-gradient(270deg, rgba(0,0,0,1) 100%, rgba(2,2,2,1) 0%)",
      }}
    >
      {/* Purple gradient accent at bottom */}
      <div className="absolute inset-x-0 bottom-0 h-[154px]" />

      <div className="relative z-10 mx-auto flex flex-col lg:flex-row items-center justify-between max-w-[1820px] gap-10">
        <div className="basis-3/5">
          <p className="text-white font-poppins font-medium md:text-[22px] text-[20px] leading-relaxed flex-1">
            If I offered to find opportunities and pitch you for paid gigs every
            single day… in your voice…without you lifting a finger…
            <span className="font-bold text-[25px]">Would you say no?</span>
          </p>
        </div>

        <div className="flex justify-center basis-1/5">
          <Button
            size="lg"
            onClick={() => {
              const registrationForm =
                document.getElementById("registration-form");
              if (registrationForm) {
                registrationForm.scrollIntoView({ behavior: "smooth" });
              }
            }}
            className="bg-gradient-tertiary text-black font-poppins font-semibold xl:text-xl lg:text-lg text-base lg:px-12 px-8 lg:py-4 py-3 rounded-[18px] h-auto hover:opacity-90 transition-opacity shadow-lg flex-shrink-0"
          >
            Pre-Register Now
          </Button>
        </div>
      </div>
      <div
        className="absolute inset-0 bg-cover bg-right bg-no-repeat"
        style={{ backgroundImage: `url(${buttonGradient})` }}
      ></div>
    </section>
  );
};

export default CTASection;
