"""
Search Providers Module for Speaker Opportunity Scraper

This module provides integration with multiple search engines and APIs
to find speaking opportunities across different platforms. It includes
support for Tavily, Brave Search, SerpAPI, Exa, and Firecrawl with
comprehensive error handling and rate limiting.

Key Features:
- Multi-provider search support
- Rate limiting and error handling
- Proxy service integration
- Result normalization
- Fallback mechanisms

Supported Providers:
- Tavily: AI-powered search
- Brave Search: Privacy-focused search
- SerpAPI: Google search results
- Exa: AI-powered web search
- Firecrawl: Web scraping service

Author: Speaker <PERSON>t Team
Version: 2.0 (Optimized)
Last Updated: 2024
"""

import asyncio
import httpx
from typing import List, Dict, Optional
from tavily import TavilyClient

from app.config.config import config

def extract_root_error(error_message: str) -> str:
    """
    Extract the root error message without chaining.
    
    Args:
        error_message: The full error message that may contain chaining
        
    Returns:
        The root error message without chaining
    """
    # Split by common chaining patterns
    if ": " in error_message:
        parts = error_message.split(": ")
        # Return the last part which is usually the root error
        return parts[-1].strip()
    
    # If no chaining, return as is
    return error_message.strip()


# =============================================================================
# TAVILY SEARCH PROVIDER
# =============================================================================


async def tavily_search(query: str, want_results: int = 20, depth: str = "basic") -> List[Dict]:
    """
    Search using Tavily API with comprehensive error handling.
    
    This function performs a search using the Tavily API and normalizes
    the results to a consistent format. It includes rate limiting and
    error handling to ensure reliable operation.
    
    Args:
        query (str): Search query string
        want_results (int): Desired number of results (default: 20)
        depth (str): Search depth - "basic" or "advanced" (default: "basic")
        
    Returns:
        List[Dict]: List of normalized search results
        
    Example:
        >>> results = await tavily_search("AI conference 2024", 10)
        >>> print(f"Found {len(results)} results")
    """
    try:
        from app.config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    except Exception:
        from config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    
    # Create Tavily client
    api_key = config.TAVILY_API_KEY
    if not api_key or not api_key.strip():
        error_msg = "TAVILY_API_KEY not configured, skipping Tavily search"
        logger.error(error_msg)
        raise Exception(error_msg)
    
    try:
        client = TavilyClient(api_key=api_key.strip())
    except Exception as e:
        error_msg = f"Failed to create Tavily client: {e}"
        logger.error(error_msg)
        raise Exception(error_msg)
    
    ask_depth = "basic" if depth not in ("basic", "advanced") else depth
    
    # Build search parameters
    search_params = {
        "query": query,
        "max_results": want_results,
        "search_depth": ask_depth,
        "include_answer": False
    }
    
    try:
        res = client.search(**search_params)
        
        # Check for various error types in response
        if isinstance(res, dict) and "error" in res:
            error_text = res.get("error", "")
            if "rate limit" in error_text.lower() or "quota" in error_text.lower():
                error_msg = f"Tavily rate limit exceeded: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif "unauthorized" in error_text.lower() or "invalid" in error_text.lower():
                error_msg = f"Tavily authentication failed: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif "forbidden" in error_text.lower() or "access denied" in error_text.lower():
                error_msg = f"Tavily access forbidden: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif "server error" in error_text.lower() or "internal error" in error_text.lower():
                error_msg = f"Tavily server error: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            else:
                error_msg = f"Tavily error: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
        
        results = res.get("results", []) or []
        if results:
            logger.info(f"Tavily returned {len(results)} results")
            return results
        else:
            logger.info("Tavily returned 0 results")
    except Exception as e:
        # Extract the root error without chaining
        root_error = extract_root_error(str(e))
        logger.error(f"Tavily failed: {root_error}")
        raise Exception(root_error)
    
    # If primary search failed, try a simpler query
    if len(query) > 50:
        try:
            base_topic = query.split()[0]
            simple_query = f'{base_topic} "call for speakers"'
            logger.info(f"Tavily: Trying simple query: {simple_query}")
            res = client.search(
                query=simple_query, 
                max_results=ask_results, 
                search_depth="basic", 
                include_answer=False
            )
            results = res.get("results", []) or []
            if results:
                logger.info(f"Tavily simple query returned {len(results)} results")
                return results
        except Exception as e2:
            logger.warning(f"Tavily simple query failed: {e2}")
    
    return []


# =============================================================================
# BRAVE SEARCH PROVIDER
# =============================================================================

async def brave_search(query: str, want_results: int = 10) -> List[Dict]:
    """
    Search using Brave Search API with privacy-focused results.
    
    This function performs a search using the Brave Search API, which provides
    privacy-focused search results without tracking. It normalizes results
    to match the standard format used by other search providers.
    
    Args:
        query (str): Search query string
        want_results (int): Maximum number of results to return (default: 10, actual limit depends on your Brave API plan)
        
    Returns:
        List[Dict]: List of normalized search results
        
    Example:
        >>> results = await brave_search("tech conference 2024", 5)
        >>> print(f"Found {len(results)} results from Brave")
    """
    try:
        from app.config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    except Exception:
        from config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    
    api_key = config.BRAVE_API_KEY
    endpoint = config.BRAVE_ENDPOINT
    if not api_key:
        error_msg = "BRAVE_API_KEY missing; skipping Brave provider"
        logger.error(error_msg)
        raise Exception(error_msg)
    
    
    headers = {
        "Accept": "application/json",
        "X-Subscription-Token": api_key.strip(),
        "User-Agent": "Mozilla/5.0 SpeakersBot/2.0",
    }
    
    async def do_request(q: str, retry_count: int = 0) -> List[Dict]:
        try:
            # Add delay between requests to avoid rate limiting
            if retry_count > 0:
                await asyncio.sleep(2 ** retry_count)  # Exponential backoff
                
            params = {"q": q, "count": want_results}
            
            # Try ScrapeOps proxy first if available
            from app.services.proxy_service import proxy_service
            if proxy_service.is_available():
                try:
                    success, response, error = await proxy_service.make_request(
                        url=endpoint,
                        method="GET",
                        headers=headers,
                        params=params,
                        timeout=15.0
                    )
                    if success and response:
                        r = response
                        status = r.status_code
                    else:
                        logger.warning(f"ScrapeOps proxy failed for Brave search: {error}. Using direct request.")
                        raise Exception("Proxy failed, using direct request")
                except Exception as e:
                    logger.debug(f"ScrapeOps proxy error for Brave search: {e}. Using direct request.")
                    # Fall through to direct request
                    async with httpx.AsyncClient(timeout=15.0) as client:
                        r = await client.get(endpoint, params=params, headers=headers)
                        status = r.status_code
            else:
                async with httpx.AsyncClient(timeout=15.0) as client:
                    r = await client.get(endpoint, params=params, headers=headers)
                    status = r.status_code
            
            if status == 429:  # Rate limited
                if retry_count < 2:
                    logger.info(f"Brave rate limited, retrying in {2 ** (retry_count + 1)} seconds...")
                    return await do_request(q, retry_count + 1)
                else:
                    error_msg = "Brave rate limit exceeded after retries"
                    logger.error(error_msg)
                    raise Exception(error_msg)
            elif status == 401:  # Unauthorized
                error_msg = f"Brave authentication failed: {r.text[:200]}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif status == 403:  # Forbidden
                error_msg = f"Brave access forbidden: {r.text[:200]}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif status == 422:  # Validation error (query too long)
                error_msg = f"Brave validation error for query length: {len(q)} chars"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif status >= 500:  # Server errors
                error_msg = f"Brave server error ({status}): {r.text[:200]}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif status != 200:
                error_msg = f"Brave HTTP {status}: {r.text[:200]}"
                logger.error(error_msg)
                raise Exception(error_msg)
                
            d = r.json()
        except Exception as ex:
            # Extract the root error without chaining
            root_error = extract_root_error(str(ex))
            logger.error(f"Brave failed: {root_error}")
            raise Exception(root_error)
            
        items = ((d.get("web") or {}).get("results")) or []
        out_local: List[Dict] = []
        for it in items:
            if not isinstance(it, dict):
                continue
            u = (it.get("url") or "").strip()
            t = it.get("title") or ""
            c = it.get("description") or it.get("snippet") or ""
            if u:
                out_local.append({"url": u, "title": t, "content": c})
        return out_local

    # Start with a compact query to avoid 422 errors
    if len(query) > 150:  # Brave has query length limits
        base_topic = query.split(" site:")[0].strip().split()[0]
        compact_query = f'{base_topic} "call for speakers" conference -site:linkedin.com'
        logger.info(f"Brave: Using compact query ({len(compact_query)} chars): {compact_query}")
        out = await do_request(compact_query)
    else:
        out = await do_request(query)
    
    # If still no results, try an even simpler query
    if not out and len(query) > 50:
        base_topic = query.split()[0]  # Just the first word
        simple_query = f'{base_topic} "call for speakers"'
        logger.info(f"Brave: Trying simple query: {simple_query}")
        out = await do_request(simple_query)
    
    if not out:
        logger.info(f"Brave returned 0 results for all query variations")
    else:
        logger.info(f"Brave returned {len(out)} results")
    return out


# =============================================================================
# SERPAPI SEARCH PROVIDER
# =============================================================================

async def serpapi_search(query: str, want_results: int = 10) -> List[Dict]:
    """
    Search using SerpAPI (Google Search Results) with comprehensive error handling.
    
    This function uses SerpAPI to get Google search results, which provides
    access to Google's search index without rate limiting issues. It includes
    query optimization and result normalization.
    
    Args:
        query (str): Search query string
        want_results (int): Desired number of results (default: 10)
        
    Returns:
        List[Dict]: List of normalized search results
        
    Example:
        >>> results = await serpapi_search("conference call for speakers", 15)
        >>> print(f"Found {len(results)} results from SerpAPI")
    """
    try:
        from app.config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    except Exception:
        from config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    
    api_key = config.SERPAPI_API_KEY
    if not api_key:
        error_msg = "SERPAPI_API_KEY missing; skipping SerpAPI provider"
        logger.error(error_msg)
        raise Exception(error_msg)
    
    # Try multiple import methods for SerpAPI
    GoogleSearch = None
    try:
        from serpapi import GoogleSearch
        logger.info("SerpAPI: Using serpapi.GoogleSearch import")
    except ImportError:
        try:
            from serpapi.google_search import GoogleSearch
            logger.info("SerpAPI: Using serpapi.google_search.GoogleSearch import")
        except ImportError:
            try:
                import serpapi
                GoogleSearch = serpapi.GoogleSearch
                logger.info("SerpAPI: Using serpapi.GoogleSearch attribute")
            except (ImportError, AttributeError):
                try:
                    # Alternative: use requests directly to SerpAPI
                    import httpx
                    return await serpapi_direct_request(query, api_key, want_results)
                except Exception as e:
                    # Extract the root error without chaining
                    root_error = extract_root_error(str(e))
                    logger.error(f"SerpAPI failed: {root_error}")
                    raise Exception(root_error)
    
    if not GoogleSearch:
        error_msg = "SerpAPI GoogleSearch class not available"
        logger.error(error_msg)
        raise Exception(error_msg)
    
    # Shorten query if too long to avoid API errors
    if len(query) > 200:
        # Extract key terms for shorter query
        base_topic = query.split(" site:")[0].strip().split()[0]
        short_query = f'{base_topic} "call for speakers" conference -site:linkedin.com'
        logger.info(f"SerpAPI: Shortened query from {len(query)} to {len(short_query)} chars")
        query = short_query
    
    params = {
        "q": query,
        "api_key": api_key.strip(),
        "engine": "google",
        "num": max(1, min(10, want_results)),
        "gl": "us",  # geolocation
        "hl": "en"   # language
    }
    
    try:
        search = GoogleSearch(params)
        res = search.get_dict() or {}
        logger.info(f"SerpAPI Google search completed, keys: {list(res.keys())}")
        
        # Check for various error types in response
        if "error" in res:
            error_text = res.get("error", "")
            if "run out of searches" in error_text or "quota" in error_text.lower():
                error_msg = f"SerpAPI rate limit exceeded: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif "invalid" in error_text.lower() or "unauthorized" in error_text.lower():
                error_msg = f"SerpAPI authentication failed: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif "forbidden" in error_text.lower() or "access denied" in error_text.lower():
                error_msg = f"SerpAPI access forbidden: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif "server error" in error_text.lower() or "internal error" in error_text.lower():
                error_msg = f"SerpAPI server error: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            else:
                error_msg = f"SerpAPI error: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            
    except Exception as e:
        # Extract the root error without chaining
        root_error = extract_root_error(str(e))
        logger.error(f"SerpAPI failed: {root_error}")
        raise Exception(root_error)
    
    out: List[Dict] = []
    for r in (res.get("organic_results") or []):
        if not isinstance(r, dict):
            continue
        url = (r.get("link") or "").strip()
        title = r.get("title") or ""
        content = r.get("snippet") or ""
        if url:
            out.append({"url": url, "title": title, "content": content})
    
    # Try DuckDuckGo via SerpAPI as a secondary engine
    try:
        ddg_params = {
            "q": query,
            "api_key": api_key.strip(),
            "engine": "duckduckgo"
        }
        ddg_search = GoogleSearch(ddg_params)
        ddg_res = ddg_search.get_dict() or {}
        # Note: seen_db filtering is now handled at the batch processing level
        for r in (ddg_res.get("organic_results") or []) + (ddg_res.get("results") or []):
            if not isinstance(r, dict):
                continue
            url = (r.get("link") or r.get("url") or "").strip()
            if not url:
                continue
            title = r.get("title") or ""
            content = r.get("snippet") or r.get("description") or ""
            out.append({"url": url, "title": title, "content": content})
    except Exception as e:
        logger.info(f"SerpAPI DuckDuckGo engine failed: {e}")
    
    if not out:
        logger.warning(f"SerpAPI returned 0 results for query='{query}' – available keys: {list((res or {}).keys())}")
    else:
        logger.info(f"SerpAPI returned {len(out)} results for query='{query}'")
    return out


async def serpapi_direct_request(query: str, api_key: str, want_results: int = 10) -> List[Dict]:
    """
    Direct SerpAPI request with comprehensive error handling.
    
    This function makes a direct request to the SerpAPI service with
    proper error handling, rate limiting, and result normalization.
    
    Args:
        query (str): Search query string
        api_key (str): SerpAPI API key
        want_results (int): Desired number of results (default: 10)
        
    Returns:
        List[Dict]: List of normalized search results
        
    Example:
        >>> results = await serpapi_direct_request("AI conference", "api_key", 10)
        >>> print(f"Found {len(results)} results")
    """
    try:
        from app.config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    except Exception:
        from config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    
    try:
        import httpx
        params = {
            "q": query,
            "api_key": api_key.strip(),
            "engine": "google",
            "num": max(1, min(10, want_results)),
            "format": "json"
        }
        
        # Try ScrapeOps proxy first if available
        from app.services.proxy_service import proxy_service
        if proxy_service.is_available():
            try:
                success, response, error = await proxy_service.make_request(
                    url="https://serpapi.com/search",
                    method="GET",
                    params=params,
                    timeout=15.0
                )
                if success and response:
                    if response.status_code == 429:  # Rate limit
                        error_msg = f"SerpAPI rate limit exceeded: {response.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    elif response.status_code == 401:  # Unauthorized
                        error_msg = f"SerpAPI authentication failed: {response.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    elif response.status_code == 403:  # Forbidden
                        error_msg = f"SerpAPI access forbidden: {response.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    elif response.status_code >= 500:  # Server errors
                        error_msg = f"SerpAPI server error ({response.status_code}): {response.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    elif response.status_code != 200:
                        error_msg = f"SerpAPI direct request failed: {response.status_code} - {response.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    
                    data = response.json()
                    out: List[Dict] = []
                    for r in (data.get("organic_results") or []):
                        if not isinstance(r, dict):
                            continue
                        url = (r.get("link") or "").strip()
                        title = r.get("title") or ""
                        content = r.get("snippet") or ""
                        if url:
                            out.append({"url": url, "title": title, "content": content})
                    
                    logger.info(f"SerpAPI direct request returned {len(out)} results")
                    return out
                else:
                    logger.warning(f"ScrapeOps proxy failed for SerpAPI: {error}. Using direct request.")
            except Exception as e:
                logger.debug(f"ScrapeOps proxy error for SerpAPI: {e}. Using direct request.")
        
        # Fallback to direct request
        async with httpx.AsyncClient(timeout=15.0) as client:
            response = await client.get("https://serpapi.com/search", params=params)
            if response.status_code == 429:  # Rate limit
                error_msg = f"SerpAPI rate limit exceeded: {response.text[:200]}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif response.status_code == 401:  # Unauthorized
                error_msg = f"SerpAPI authentication failed: {response.text[:200]}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif response.status_code == 403:  # Forbidden
                error_msg = f"SerpAPI access forbidden: {response.text[:200]}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif response.status_code >= 500:  # Server errors
                error_msg = f"SerpAPI server error ({response.status_code}): {response.text[:200]}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif response.status_code != 200:
                error_msg = f"SerpAPI direct request failed: {response.status_code} - {response.text[:200]}"
                logger.error(error_msg)
                raise Exception(error_msg)
            
            data = response.json()
            out: List[Dict] = []
            for r in (data.get("organic_results") or []):
                if not isinstance(r, dict):
                    continue
                url = (r.get("link") or "").strip()
                title = r.get("title") or ""
                content = r.get("snippet") or ""
                if url:
                    out.append({"url": url, "title": title, "content": content})
            
            logger.info(f"SerpAPI direct request returned {len(out)} results")
            return out
            
    except Exception as e:
        # Extract the root error without chaining
        root_error = extract_root_error(str(e))
        logger.error(f"SerpAPI failed: {root_error}")
        raise Exception(root_error)


# =============================================================================
# EXA SEARCH PROVIDER
# =============================================================================

async def exa_search(query: str, want_results: int = 20) -> List[Dict]:
    """
    Search using Exa API with AI-powered web search.
    
    This function uses the Exa API to perform AI-powered web searches
    that can find more relevant and recent content. It includes
    comprehensive error handling and result normalization.
    
    Args:
        query (str): Search query string
        want_results (int): Desired number of results (default: 20)
        
    Returns:
        List[Dict]: List of normalized search results
        
    Example:
        >>> results = await exa_search("machine learning conference 2024", 8)
        >>> print(f"Found {len(results)} results from Exa")
    """
    try:
        from app.config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    except Exception:
        from config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    
    api_key = config.EXA_API_KEY
    if not api_key:
        error_msg = "EXA_API_KEY missing; skipping Exa provider"
        logger.error(error_msg)
        raise Exception(error_msg)
    try:
        from exa_py import Exa  # type: ignore
    except Exception as e:
        error_msg = f"exa_py import failed: {e}"
        logger.error(error_msg)
        raise Exception(error_msg)
    try:
        client = Exa(api_key=api_key)
        res = client.search(query, num_results=want_results)
        
        # Check for various error types in response
        if isinstance(res, dict) and "error" in res:
            error_text = res.get("error", "")
            if "rate limit" in error_text.lower() or "quota" in error_text.lower():
                error_msg = f"Exa rate limit exceeded: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif "unauthorized" in error_text.lower() or "invalid" in error_text.lower():
                error_msg = f"Exa authentication failed: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif "forbidden" in error_text.lower() or "access denied" in error_text.lower():
                error_msg = f"Exa access forbidden: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            elif "server error" in error_text.lower() or "internal error" in error_text.lower():
                error_msg = f"Exa server error: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
            else:
                error_msg = f"Exa error: {error_text}"
                logger.error(error_msg)
                raise Exception(error_msg)
                
    except Exception as e:
        # Extract the root error without chaining
        root_error = extract_root_error(str(e))
        logger.error(f"Exa failed: {root_error}")
        raise Exception(root_error)
    items = []
    try:
        items = res.get("results", []) if isinstance(res, dict) else getattr(res, "results", [])
    except Exception:
        items = []
    out: List[Dict] = []
    for r in items or []:
        if isinstance(r, dict):
            url = (r.get("url") or "").strip()
            title = r.get("title") or ""
            content = r.get("text") or r.get("snippet") or r.get("description") or ""
        else:
            url = (getattr(r, "url", "") or "").strip()
            title = getattr(r, "title", "") or ""
            content = getattr(r, "text", "") or getattr(r, "highlight", "") or ""
        if url:
            out.append({"url": url, "title": title, "content": content})
    logger.info(f"Exa returned {len(out)} results for query='{query}'")
    return out


# =============================================================================
# FIRECRAWL SEARCH PROVIDER
# =============================================================================

async def firecrawl_search(query: str, want_results: int = 10) -> List[Dict]:
    """
    Search using Firecrawl API with web scraping capabilities.
    
    This function uses the Firecrawl API to perform web searches and
    scrape content from web pages. It provides access to full page
    content and includes comprehensive error handling.
    
    Args:
        query (str): Search query string
        want_results (int): Desired number of results (default: 10)
        
    Returns:
        List[Dict]: List of normalized search results
        
    Example:
        >>> results = await firecrawl_search("data science conference", 5)
        >>> print(f"Found {len(results)} results from Firecrawl")
    """
    try:
        from app.config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    except Exception:
        from config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    
    api_key = config.FIRECRAWL_API_KEY
    endpoint = config.FIRECRAWL_ENDPOINT
    if not api_key:
        error_msg = "FIRECRAWL_API_KEY missing; skipping Firecrawl provider"
        logger.error(error_msg)
        raise Exception(error_msg)
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }
    payload = {"query": query, "limit": max(1, min(10, want_results))}
    try:
        # Try ScrapeOps proxy first if available
        from app.services.proxy_service import proxy_service
        if proxy_service.is_available():
            try:
                success, response, error = await proxy_service.make_request(
                    url=endpoint,
                    method="POST",
                    headers=headers,
                    json=payload,
                    timeout=15.0
                )
                if success and response:
                    status = response.status_code
                    if status == 429:  # Rate limit
                        error_msg = f"Firecrawl rate limit exceeded: {response.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    elif status == 401:  # Unauthorized
                        error_msg = f"Firecrawl authentication failed: {response.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    elif status == 403:  # Forbidden
                        error_msg = f"Firecrawl access forbidden: {response.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    elif status >= 500:  # Server errors
                        error_msg = f"Firecrawl server error ({status}): {response.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    elif status != 200:
                        error_msg = f"Firecrawl HTTP {status}: {response.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    data = response.json()
                else:
                    logger.warning(f"ScrapeOps proxy failed for Firecrawl: {error}. Using direct request.")
                    raise Exception("Proxy failed, using direct request")
            except Exception as e:
                logger.debug(f"ScrapeOps proxy error for Firecrawl: {e}. Using direct request.")
                # Fall through to direct request
                async with httpx.AsyncClient(timeout=15.0) as client:
                    resp = await client.post(endpoint, json=payload, headers=headers)
                    status = resp.status_code
                    if status == 429:  # Rate limit
                        error_msg = f"Firecrawl rate limit exceeded: {resp.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    elif status == 401:  # Unauthorized
                        error_msg = f"Firecrawl authentication failed: {resp.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    elif status == 403:  # Forbidden
                        error_msg = f"Firecrawl access forbidden: {resp.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    elif status >= 500:  # Server errors
                        error_msg = f"Firecrawl server error ({status}): {resp.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    elif status != 200:
                        error_msg = f"Firecrawl HTTP {status}: {resp.text[:200]}"
                        logger.error(error_msg)
                        raise Exception(error_msg)
                    data = resp.json()
        else:
            async with httpx.AsyncClient(timeout=15.0) as client:
                resp = await client.post(endpoint, json=payload, headers=headers)
                status = resp.status_code
                if status == 429:  # Rate limit
                    error_msg = f"Firecrawl rate limit exceeded: {resp.text[:200]}"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                elif status == 401:  # Unauthorized
                    error_msg = f"Firecrawl authentication failed: {resp.text[:200]}"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                elif status == 403:  # Forbidden
                    error_msg = f"Firecrawl access forbidden: {resp.text[:200]}"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                elif status >= 500:  # Server errors
                    error_msg = f"Firecrawl server error ({status}): {resp.text[:200]}"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                elif status != 200:
                    error_msg = f"Firecrawl HTTP {status}: {resp.text[:200]}"
                    logger.error(error_msg)
                    raise Exception(error_msg)
                data = resp.json()
    except Exception as e:
        # Extract the root error without chaining
        root_error = extract_root_error(str(e))
        logger.error(f"Firecrawl failed: {root_error}")
        raise Exception(root_error)
    results = (data.get("data") or data.get("results") or [])
    out: List[Dict] = []
    for r in results:
        if not isinstance(r, dict):
            continue
        url = (r.get("url") or "").strip()
        title = r.get("title") or r.get("domain") or ""
        content = r.get("description") or r.get("snippet") or ""
        if url:
            out.append({"url": url, "title": title, "content": content})
    if not out:
        logger.info(f"Firecrawl returned 0 results for query='{query}'")
    else:
        logger.info(f"Firecrawl returned {len(out)} results for query='{query}'")
    return out
