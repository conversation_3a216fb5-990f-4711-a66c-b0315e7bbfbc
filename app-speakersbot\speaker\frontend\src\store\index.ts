import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import authReducer from './slices/authSlice';
import { subscriptionApi } from '../apis/subscriptionApi';
import { dashboardApi } from '../apis/dashboardApi';
import { authApi } from '../apis/authApi';
import { categoryApi } from '../apis/categoryApi';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    [subscriptionApi.reducerPath]: subscriptionApi.reducer,
    [dashboardApi.reducerPath]: dashboardApi.reducer,
    [authApi.reducerPath]: authApi.reducer,
    [categoryApi.reducerPath]: categoryApi.reducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }).concat(subscriptionApi.middleware,dashboardApi.middleware, authApi.middleware, categoryApi.middleware),
  devTools: process.env.NODE_ENV !== 'production',
});

// Setup listeners for RTK Query
setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
