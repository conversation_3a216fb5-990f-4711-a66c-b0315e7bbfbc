"""
S3 Service Helper
Handles PDF uploads to S3 bucket in DSA PDF folder and file downloads from S3.
"""

import boto3
import logging
import os
import httpx
from typing import Optional
from app.config.config import config

logger = logging.getLogger(__name__)

class S3Service:
    """Service to handle S3 operations including PDF uploads and file downloads."""
    
    def __init__(self):
        # Validate S3 configuration
        if not config.S3_BUCKET_NAME:
            raise ValueError("bucketname environment variable is required")
        if not config.AWS_ACCESS_KEY_ID:
            raise ValueError("access_key environment variable is required")
        if not config.AWS_SECRET_ACCESS_KEY:
            raise ValueError("secrete_key environment variable is required")
            
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=config.AWS_ACCESS_KEY_ID,
            aws_secret_access_key=config.AWS_SECRET_ACCESS_KEY,
            region_name=config.AWS_REGION
        )
        self.bucket_name = config.S3_BUCKET_NAME
    
    def upload_pdf(self, pdf_bytes: bytes, filename: str) -> Optional[str]:
        """Upload PDF to S3 and return the URL."""
        try:
            # Create S3 key with DSA PDF folder
            s3_key = f"dsa-pdf/{filename}"
            
            # Upload to S3
            self.s3_client.put_object(
                Bucket=self.bucket_name,
                Key=s3_key,
                Body=pdf_bytes,
                ContentType='application/pdf',
                ACL='public-read'
            )
            
            # Generate URL
            pdf_url = f"https://{self.bucket_name}.s3.{config.AWS_REGION}.amazonaws.com/{s3_key}"
            logger.info(f"PDF uploaded successfully: {pdf_url}")
            return pdf_url
            
        except Exception as e:
            logger.error(f"Error uploading PDF to S3: {e}")
            return None
    
    def download_file(self, file_url: str, local_path: str) -> bool:
        """
        Download file from S3 URL to local path.
        
        Args:
            file_url: S3 URL of the file
            local_path: Local file path where the file will be saved
            
        Returns:
            True if download successful, False otherwise
            
        Raises:
            RuntimeError: If download fails
        """
        try:
            # Ensure directory exists for local_path
            local_dir = os.path.dirname(local_path)
            if local_dir:
                os.makedirs(local_dir, exist_ok=True)
            
            # Download file from S3 using httpx (synchronous)
            with httpx.Client(timeout=300.0) as client:  # 5 minute timeout for large files
                response = client.get(file_url)
                
                if response.status_code != 200:
                    error_msg = f"Failed to download file from S3: HTTP {response.status_code}"
                    logger.error(error_msg)
                    raise RuntimeError(error_msg)
                
                # Save file to local path
                file_size = len(response.content)
                with open(local_path, 'wb') as f:
                    f.write(response.content)
                
                logger.info(f"Downloaded file from S3: {file_size} bytes saved to {local_path}")
                
                # Verify file was saved
                if os.path.exists(local_path) and os.path.getsize(local_path) > 0:
                    return True
                else:
                    error_msg = "Downloaded file is empty or doesn't exist"
                    logger.error(error_msg)
                    raise RuntimeError(error_msg)
                
        except httpx.HTTPError as e:
            error_msg = f"Error downloading file from S3: {str(e)}"
            logger.error(error_msg)
            # Cleanup partial download if it exists
            if os.path.exists(local_path):
                try:
                    os.remove(local_path)
                except Exception:
                    pass
            raise RuntimeError(error_msg)
        except RuntimeError:
            # Re-raise RuntimeError as-is
            raise
        except Exception as e:
            error_msg = f"Unexpected error downloading file from S3: {str(e)}"
            logger.error(error_msg, exc_info=True)
            # Cleanup partial download if it exists
            if os.path.exists(local_path):
                try:
                    os.remove(local_path)
                except Exception:
                    pass
            raise RuntimeError(error_msg)
