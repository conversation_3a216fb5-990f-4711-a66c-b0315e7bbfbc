const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const {FormType} = require('../models');
const connection = require("../models/connection")

const csvFilePath = path.join(__dirname, 'csv', 'form_groups.csv');

async function insertFormTypes() {
  try {
    await connection.authenticate();
    console.log('Database connection established.');

    const formTypes = [];

    // Check if CSV file exists
    if (!fs.existsSync(csvFilePath)) {
      console.error(`CSV file not found at: ${csvFilePath}`);
      console.log('Please create a CSV file with the following columns:');
      console.log('priority,title');
      console.log('Example:');
      console.log('1,Speaker Registration Form');
      console.log('2,Speaker Profile Update Form');
      console.log('3,Opportunity Application Form');
      process.exit(1);
    }

    // Map CSV headers to model fields
    const headerMap = {
      'priority': 'priority',
      'title': 'title'
    };
   
    let p=0;
    // Helper to convert values to correct types
    function convertValue(key, value) {
      if (value === undefined || value === null) return null;
      value = value.trim();
      if (value === '') return null;
      
      if (key === 'priority') {
        const n = parseInt(value, 10);
        return isNaN(n) ? 1 : n;
      }
      
      return value;
    }

    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('data', (row) => {
        // Map CSV row to model fields and convert types
        const mapped = {};
        for (const [csvKey, modelKey] of Object.entries(headerMap)) {
          if (row.hasOwnProperty(csvKey)) {
            mapped[modelKey] = convertValue(modelKey, row[csvKey]);
          }
        }
        
        // Add timestamps
        mapped.created_at = new Date();
        mapped.updated_at = new Date();
        
        formTypes.push(mapped);
      })
      .on('end', async () => {
        console.log(`Read ${formTypes.length} form types from CSV.`);

        let success = 0;
        let fail = 0;

        for (const formTypeData of formTypes) {
          try {
            // Check if form type already exists to avoid duplicates
            const existingFormType = await FormType.findOne({
              where: { title: formTypeData.title }
            });

            if (existingFormType) {
              console.log(`Form type "${formTypeData.title}" already exists, skipping...`);
              continue;
            }

            await FormType.create(formTypeData);
            console.log(`✓ Inserted form type: ${formTypeData.title} (Priority: ${formTypeData.priority})`);
            success++;
          } catch (err) {
            fail++;
            console.error(`✗ Failed to insert form type "${formTypeData.title}":`, err.message);
          }
        }

        console.log(`\nForm type insertion complete!`);
        console.log(`Success: ${success}`);
        console.log(`Failed: ${fail}`);
        console.log(`Total processed: ${formTypes.length}`);

        // Display all form types in the database
        const allFormTypes = await FormType.findAll({
          order: [['priority', 'ASC']]
        });
        
        console.log('\nCurrent form types in database:');
        allFormTypes.forEach((formType, index) => {
          console.log(`${index + 1}. [Priority: ${formType.priority}] ${formType.title}`);
        });

        process.exit(0);
      })
      .on('error', (error) => {
        console.error('Error reading CSV file:', error);
        process.exit(1);
      });
  } catch (error) {
    console.error('Unable to connect to the database:', error);
    process.exit(1);
  }
}

// Run the migration
insertFormTypes();