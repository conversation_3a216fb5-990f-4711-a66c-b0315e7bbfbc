import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FileText, Calendar, User, Bell, ExternalLink } from 'lucide-react';

export function HomeTab() {
  return (
    <div className="space-y-4">
      {/* Quick Stats */}
      <div className="grid grid-cols-2 gap-3">
        <Card className="bg-surface border-border">
          <CardContent className="p-3">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-primary" />
              <div>
                <p className="text-xs text-foreground-muted">Active Ops</p>
                <p className="text-lg font-semibold text-foreground">12</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="bg-surface border-border">
          <CardContent className="p-3">
            <div className="flex items-center gap-2">
              <Bell className="h-4 w-4 text-primary" />
              <div>
                <p className="text-xs text-foreground-muted">New Alerts</p>
                <p className="text-lg font-semibold text-foreground">3</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card className="bg-surface border-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm text-foreground">Quick Actions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <Button variant="outline" size="sm" className="w-full justify-start gap-2">
            <FileText className="h-4 w-4" />
            Update Profile
          </Button>
          <Button variant="outline" size="sm" className="w-full justify-start gap-2">
            <Calendar className="h-4 w-4" />
            Browse Opportunities
          </Button>
          <Button variant="outline" size="sm" className="w-full justify-start gap-2">
            <ExternalLink className="h-4 w-4" />
            Open Portal
          </Button>
        </CardContent>
      </Card>

      {/* Recent Activity */}
      <Card className="bg-surface border-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm text-foreground">Recent Activity</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-xs text-foreground">Applied to Tech Conference 2024</p>
              <p className="text-xs text-foreground-muted">2 hours ago</p>
            </div>
            <Badge variant="secondary" className="text-xs">Pending</Badge>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-xs text-foreground">Profile updated</p>
              <p className="text-xs text-foreground-muted">1 day ago</p>
            </div>
            <Badge variant="default" className="text-xs">Completed</Badge>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex-1">
              <p className="text-xs text-foreground">New opportunity match</p>
              <p className="text-xs text-foreground-muted">2 days ago</p>
            </div>
            <Badge variant="outline" className="text-xs">New</Badge>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}