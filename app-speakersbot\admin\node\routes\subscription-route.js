const subscriptionController = require("../controllers/subscription-controller");
const router = require("express").Router();
const verifyToken = require("../middlewares/verify-token");

module.exports = (app) => { 

    // ------------------------- subscription -------------------------

    // get all subscriptions
    router.get("/subscriptions", subscriptionController.getSubscriptions);
    router.get("/subscriptions/export", subscriptionController.exportSubscriptions);
    
    app.use("/admin/api/v1", router);

}