import {
  useGetPrimaryCategoriesQuery,
  useSubmitRegistrationMutation,
} from "@/apis/categoryApi";
import ButtonLoader from "@/components/common/ButtonLoader";
import { Combobox } from "@/components/ui/combobox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { useInfiniteSubcategories } from "@/hooks/useInfiniteSubcategories";
import { useEffect, useState } from "react";
import { Controller, useForm } from "react-hook-form";

// Form data type
type RegistrationFormData = {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  city: string;
  state: string;
  country: string;
  primaryCategory: string;
  subCategory: string;
};

const RegistrationForm = () => {
  const [selectedPrimaryCategory, setSelectedPrimaryCategory] =
    useState<string>("");
  const [isSuccess, setIsSuccess] = useState(false);
  const [formError, setFormError] = useState<string>("");
  const { toast } = useToast();

  const {
    register,
    handleSubmit,
    control,
    watch,
    setValue,
    reset,
    formState: { errors, isSubmitting, isValid },
  } = useForm<RegistrationFormData>({
    mode: "onBlur", // Validate on blur to avoid constant validation
  });

  // Watch primary category changes
  const watchedPrimaryCategory = watch("primaryCategory");

  // Debug form values
  const formValues = watch();
  // Fetch primary categories
  const { data: primaryCategoriesData, isLoading: primaryCategoriesLoading } =
    useGetPrimaryCategoriesQuery();

  // Fetch subcategories with infinite scroll
  const {
    subcategories,
    isLoading: subCategoriesLoading,
    isLoadingMore,
    hasMore,
    loadMore,
    isFetching: isFetchingSubcategories,
  } = useInfiniteSubcategories({
    primaryCategoryId: selectedPrimaryCategory,
    enabled: !!selectedPrimaryCategory,
  });

  // Registration submission mutation
  const [submitRegistration, { isLoading: isSubmittingRegistration }] =
    useSubmitRegistrationMutation();

  // Reset subcategory when primary category changes
  useEffect(() => {
    if (watchedPrimaryCategory !== selectedPrimaryCategory) {
      setSelectedPrimaryCategory(watchedPrimaryCategory);
      setValue("subCategory", "");
    }
  }, [watchedPrimaryCategory, selectedPrimaryCategory, setValue]);

  // Clear form error when user starts typing
  useEffect(() => {
    if (formError) {
      const timer = setTimeout(() => {
        setFormError("");
      }, 5000); // Clear error after 5 seconds
      return () => clearTimeout(timer);
    }
  }, [formError]);

  const onSubmit = async (data: RegistrationFormData) => {
    // Clear any previous form errors
    setFormError("");

    try {
      await submitRegistration(data).unwrap();

      // Show success toast
      toast({
        title: "Registration Successful!",
        description:
          "Thank you for joining our early access program. We'll be in touch soon!",
        variant: "default",
      });

      // Reset form after successful submission
      reset();
      setSelectedPrimaryCategory("");
      setIsSuccess(true);
      setFormError(""); // Clear any errors on success
    } catch (error: any) {
      // Handle API errors
      const errorMessage =
        error?.data?.error?.message ||
        error?.data?.message ||
        error?.message ||
        "Something went wrong. Please try again.";
      
      setFormError(errorMessage);

      // Show error toast
      toast({
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  return (
    <div
      className="max-w-5xl mx-auto lg:mt-[50px] md:mt-[50px] mt-[30px] pt-50"
      id="registration-form"
    >
      <div className="bg-black rounded-[25px] border border-gradient-border lg:py-8 lg:px-12 py-5 px-5 shadow-[0_19px_13px_-11px_rgba(0,0,0,1)]">
        <div className="mb-7 lg:mb-10 text-center">
          <h2 className="text-white font-poppins font-medium lg:text-[36px] md:text-[28px] text-[24px]">
            Reserve Your Spot Today
          </h2>
          <p className="text-white font-segoe lg:text-base text-sm leading-relaxed mt-2">
            Complete your profile now and secure early access to AI-powered
            speaker opportunities.
          </p>
        </div>

        {/* Success Message */}
        {isSuccess && (
          <div className="mb-8 p-6 bg-green-900/20 border border-green-500/30 rounded-lg">
            <div className="flex items-center justify-center mb-4">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
            </div>
            <h3 className="text-green-400 font-poppins font-semibold text-xl text-center mb-2">
              Registration Successful!
            </h3>
            <p className="text-green-300 font-segoe text-center">
              Thank you for joining our early access program. We'll be in touch
              soon with exclusive speaker opportunities!
            </p>
          </div>
        )}

        {!isSuccess ? (
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-5">
            {/* First Name & Last Name */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              <div className="space-y-2">
                <Label
                  htmlFor="firstName"
                  className="text-white font-segoe font-semibold text-sm"
                >
                  First Name *
                </Label>
                <Input
                  id="firstName"
                  placeholder="Your name"
                  {...register("firstName", { 
                    required: "First name is required",
                    minLength: { value: 2, message: "First name must be at least 2 characters" }
                  })}
                  className={`bg-input-background/50 border-input-border/50 text-white placeholder:text-[#676767] font-segoe text-base rounded-[14px] ${
                    errors.firstName ? "border-red-500" : ""
                  }`}
                />
                {errors.firstName && (
                  <p className="text-red-500 lg:text-sm text-xs font-segoe">
                    {errors.firstName.message}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="lastName"
                  className="text-white font-segoe font-semibold text-sm"
                >
                  Last Name *
                </Label>
                <Input
                  id="lastName"
                  placeholder="Your last name"
                  {...register("lastName", { 
                    required: "Last name is required",
                    minLength: { value: 2, message: "Last name must be at least 2 characters" }
                  })}
                  className={`bg-input-background/50 border-input-border/50 text-white placeholder:text-[#676767] font-segoe text-base rounded-[14px] ${
                    errors.lastName ? "border-red-500" : ""
                  }`}
                />
                {errors.lastName && (
                  <p className="text-red-500 lg:text-sm text-xs font-segoe">
                    {errors.lastName.message}
                  </p>
                )}
              </div>
            </div>

            {/* Email & Phone */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              <div className="space-y-2">
                <Label
                  htmlFor="email"
                  className="text-white font-segoe font-semibold text-sm"
                >
                  Email *
                </Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  {...register("email", { 
                    required: "Email is required",
                    pattern: {
                      value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                      message: "Please enter a valid email address"
                    }
                  })}
                  className={`bg-input-background/50 border-input-border/50 text-white placeholder:text-[#676767] font-segoe text-base rounded-[14px] ${
                    errors.email ? "border-red-500" : ""
                  }`}
                />
                {errors.email && (
                  <p className="text-red-500 lg:text-sm text-xs font-segoe">
                    {errors.email.message}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="phone"
                  className="text-white font-segoe font-semibold text-sm"
                >
                  Phone Number *
                </Label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="+****************"
                  {...register("phone", { 
                    required: "Phone number is required",
                    minLength: { value: 10, message: "Phone number must be at least 10 characters" }
                  })}
                  className={`bg-input-background/50 border-input-border/50 text-white placeholder:text-[#676767] font-segoe text-base rounded-[14px] ${
                    errors.phone ? "border-red-500" : ""
                  }`}
                />
                {errors.phone && (
                  <p className="text-red-500 lg:text-sm text-xs font-segoe">
                    {errors.phone.message}
                  </p>
                )}
              </div>
            </div>

            {/* City, State, Country */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
              <div className="space-y-2">
                <Label
                  htmlFor="city"
                  className="text-white font-segoe font-semibold text-sm"
                >
                  City *
                </Label>
                <Input
                  id="city"
                  placeholder="City"
                  {...register("city", { 
                    required: "City is required",
                    minLength: { value: 2, message: "City must be at least 2 characters" }
                  })}
                  className={`bg-input-background/50 border-input-border/50 text-white placeholder:text-[#676767] font-segoe text-base rounded-[14px] ${
                    errors.city ? "border-red-500" : ""
                  }`}
                />
                {errors.city && (
                  <p className="text-red-500 lg:text-sm text-xs font-segoe">
                    {errors.city.message}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="state"
                  className="text-white font-segoe font-semibold text-sm"
                >
                  State *
                </Label>
                <Input
                  id="state"
                  placeholder="Your State"
                  {...register("state", { 
                    required: "State is required",
                    minLength: { value: 2, message: "State must be at least 2 characters" }
                  })}
                  className={`bg-input-background/50 border-input-border/50 text-white placeholder:text-[#676767] font-segoe text-base rounded-[14px] ${
                    errors.state ? "border-red-500" : ""
                  }`}
                />
                {errors.state && (
                  <p className="text-red-500 lg:text-sm text-xs font-segoe">
                    {errors.state.message}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="country"
                  className="text-white font-segoe font-semibold text-sm"
                >
                  Country *
                </Label>
                <Input
                  id="country"
                  placeholder="Your Country"
                  {...register("country", { 
                    required: "Country is required",
                    minLength: { value: 2, message: "Country must be at least 2 characters" }
                  })}
                  className={`bg-input-background/50 border-input-border/50 text-white placeholder:text-[#676767] font-segoe text-base rounded-[14px] ${
                    errors.country ? "border-red-500" : ""
                  }`}
                />
                {errors.country && (
                  <p className="text-red-500 lg:text-sm text-xs font-segoe">
                    {errors.country.message}
                  </p>
                )}
              </div>
            </div>

            {/* Primary Category & Subcategory */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              <div className="space-y-2">
                <Label
                  htmlFor="primaryCategory"
                  className="text-white font-segoe font-semibold text-sm"
                >
                  Primary Category *
                </Label>
                <Controller
                  name="primaryCategory"
                  control={control}
                  rules={{ required: "Primary category is required" }}
                  render={({ field }) => (
                    <Combobox
                      value={field.value}
                      onValueChange={(value) => {
                        field.onChange(value);
                        setSelectedPrimaryCategory(value);
                      }}
                      placeholder="Select primary category"
                      searchPlaceholder="Search categories..."
                      emptyText="No categories found."
                      disabled={primaryCategoriesLoading}
                      options={
                        primaryCategoriesData?.data?.map((category) => ({
                          value: category.id.toString(),
                          label: category.name,
                        })) || []
                      }
                      className={errors.primaryCategory ? "border-red-500" : ""}
                    />
                  )}
                />
                {errors.primaryCategory && (
                  <p className="text-red-500 lg:text-sm text-xs font-segoe">
                    {errors.primaryCategory.message}
                  </p>
                )}
              </div>
              <div className="space-y-2">
                <Label
                  htmlFor="subCategory"
                  className="text-white font-segoe font-semibold text-sm"
                >
                  Subcategory *
                </Label>
                <Controller
                  name="subCategory"
                  control={control}
                  rules={{ required: "Subcategory is required" }}
                  render={({ field }) => (
                    <Combobox
                      value={field.value}
                      onValueChange={field.onChange}
                      placeholder={
                        !selectedPrimaryCategory
                          ? "Select a primary category first"
                          : subCategoriesLoading || isFetchingSubcategories
                          ? "Loading subcategories..."
                          : "Select subcategory"
                      }
                      searchPlaceholder="Search subcategories..."
                      emptyText={
                        subCategoriesLoading || isFetchingSubcategories
                          ? "Loading subcategories..."
                          : "No subcategories found."
                      }
                      // disabled={!selectedPrimaryCategory}
                      options={subcategories.map((subcategory) => ({
                        value: subcategory.id.toString(),
                        label: subcategory.name,
                      }))}
                      onScrollToBottom={loadMore}
                      hasMore={hasMore}
                      isLoadingMore={isLoadingMore || subCategoriesLoading || isFetchingSubcategories}
                      className={errors.subCategory ? "border-red-500" : ""}
                    />
                  )}
                />
                {errors.subCategory && (
                  <p className="text-red-500 lg:text-sm text-xs font-segoe">
                    {errors.subCategory.message}
                  </p>
                )}
              </div>
            </div>

             {/* Error Message - Only show API errors */}
             {formError && (
               <div className="mb-4 p-4 bg-red-900/20 border border-red-500/30 rounded-lg">
                 <div className="flex items-center">
                   <svg
                     className="w-5 h-5 text-red-400 mr-3 flex-shrink-0"
                     fill="none"
                     stroke="currentColor"
                     viewBox="0 0 24 24"
                   >
                     <path
                       strokeLinecap="round"
                       strokeLinejoin="round"
                       strokeWidth={2}
                       d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                     />
                   </svg>
                   <p className="text-red-400 font-segoe text-sm">{formError}</p>
                 </div>
               </div>
             )}

            {/* Submit Button */}
            <div className="flex justify-center">
            <ButtonLoader
              type="submit"
              size="lg"
              loading={isSubmittingRegistration}
              disabled={isSubmittingRegistration}
              className="mt-4 bg-gradient-tertiary text-tertiary-foreground font-poppins font-semibold xl:text-xl lg:text-lg text-base lg:px-12 px-8 lg:py-4 py-3 rounded-[18px] h-auto hover:opacity-90 transition-opacity shadow-lg w-full disabled:opacity-50"
            >
              {isSubmittingRegistration ? "Submitting..." : "Join the Waitlist"}
            </ButtonLoader>
            </div>
          </form>
        ) : (
          <div className="text-center">
            <ButtonLoader
              onClick={() => {
                setIsSuccess(false);
                reset();
                setSelectedPrimaryCategory("");
                setFormError("");
              }}
              className="bg-gradient-tertiary text-tertiary-foreground font-poppins font-semibold xl:text-xl text-lg lg:px-12 px-8 lg:py-4 py-3 rounded-[18px] h-auto hover:opacity-90 transition-opacity shadow-lg"
            >
              Submit Another Registration
            </ButtonLoader>
          </div>
        )}
      </div>
    </div>
  );
};

export default RegistrationForm;
