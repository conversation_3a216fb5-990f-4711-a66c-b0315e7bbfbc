import React, { useState, useMemo, useCallback, useRef } from 'react';
import { InboxOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { Opportunity } from '../types';
import { IMPORT_COLUMNS, validateCsvRow, transformCsvRowToOpportunity, findDuplicateOpportunities } from '../utils/import-export';
import { useDownloadSampleCsvMutation } from '../apis/opportunitiesApi';
import Papa from 'papaparse';
import ButtonLoader from './common/ButtonLoader';

// shadCN components
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../components/ui/dialog';
import { Button } from '../components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Alert, AlertDescription, AlertTitle } from '../components/ui/alert';
import { Table, TableBody, Table<PERSON>ell, TableHead, TableHeader, TableRow } from '../components/ui/table';
interface CsvImportModalProps {
  visible: boolean;
  onCancel: () => void;
  onImport: (payload: Opportunity[] | { file: File }, options: ImportOptions) => void;
  existingOpportunities: Opportunity[];
}
interface ImportOptions {
  skipInvalid: boolean;
  duplicateAction: 'skip' | 'update' | 'duplicate';
}
interface ParsedRow {
  data: any;
  index: number;
  errors: string[];
  isDuplicate: boolean;
  duplicateOf?: Opportunity;
}
const CsvImportModal: React.FC<CsvImportModalProps> = ({
  visible,
  onCancel,
  onImport,
  existingOpportunities
}) => {
  // Active wizard step acts like tabs: 'upload' → 'mapping' → 'preview'
  // UI below conditionally renders the content for the current step
  const [step, setStep] = useState<'upload' | 'mapping' | 'preview'>('upload');
  const [csvData, setCsvData] = useState<any[]>([]);
  const [csvHeaders, setCsvHeaders] = useState<string[]>([]);
  const [columnMapping, setColumnMapping] = useState<Record<string, string>>({});
  const [importOptions, setImportOptions] = useState<ImportOptions>({
    skipInvalid: true,
    duplicateAction: 'skip'
  });
  const [isDragging, setIsDragging] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  
  // API mutation for downloading sample CSV
  const [downloadSampleCsvMutation, { isLoading: isDownloading }] = useDownloadSampleCsvMutation();
  
  // Build the preview rows only when on 'preview' tab and data exists.
  // Performs validation and duplicate detection for the first 20 rows to keep UI fast.
  const parsedRows = useMemo((): ParsedRow[] => {
    if (step !== 'preview' || csvData.length === 0) return [];
    return csvData.slice(0, 20).map((row, index) => {
      const errors = validateCsvRow(row, columnMapping);
      const transformedRow = transformCsvRowToOpportunity(row, columnMapping);
      const duplicates = findDuplicateOpportunities(transformedRow, existingOpportunities);
      return {
        data: row,
        index,
        errors,
        isDuplicate: duplicates.length > 0,
        duplicateOf: duplicates[0]
      };
    });
  }, [csvData, columnMapping, existingOpportunities, step]);
  
  // Aggregate quick stats to summarize the preview status in the header of the preview tab
  const stats = useMemo(() => {
    const total = csvData.length;
    const previewed = parsedRows.length;
    const valid = parsedRows.filter(row => row.errors.length === 0).length;
    const invalid = parsedRows.filter(row => row.errors.length > 0).length;
    const duplicates = parsedRows.filter(row => row.isDuplicate).length;
    return {
      total,
      previewed,
      valid,
      invalid,
      duplicates
    };
  }, [parsedRows, csvData]);
  
  // Parse uploaded CSV and attempt auto-mapping based on header similarity to known fields
  const parseCsvFile = (file: File) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: results => {
        setCsvData(results.data);
        setCsvHeaders(results.meta.fields || []);

        // Auto-map columns based on similar names
        const autoMapping: Record<string, string> = {};
        IMPORT_COLUMNS.forEach(col => {
          const matchingHeader = results.meta.fields?.find(header => header.toLowerCase().includes(col.field.toLowerCase()) || col.label.toLowerCase().includes(header.toLowerCase()));
          if (matchingHeader) {
            autoMapping[col.field] = matchingHeader;
          }
        });
        setColumnMapping(autoMapping);
      },
      error: error => {
        console.error('CSV parsing error:', error);
      }
    });
  };
  const handleFileUpload = useCallback((file: File | null) => {
    if (!file) return;
    setSelectedFile(file);
    parseCsvFile(file);
  }, []);
  
  // Construct final payload from preview data and options, then hand off to parent
  const handleImport = () => {
    const validRows = importOptions.skipInvalid ? parsedRows.filter(row => row.errors.length === 0) : parsedRows;
    const opportunities: Opportunity[] = validRows.map(row => {
      const baseData = transformCsvRowToOpportunity(row.data, columnMapping);
      return {
        ...baseData,
        id: `op_${Date.now()}_${row.index}`,
        notes: [],
        matchedCount: 0,
        interestedCount: 0,
        acceptedCount: 0,
        rejectedCount: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      } as Opportunity;
    });
    onImport(opportunities, importOptions);
    handleReset();
  };
  
  // Reset wizard to initial tab and clear in-memory CSV state
  const handleReset = () => {
    setStep('upload');
    setCsvData([]);
    setCsvHeaders([]);
    setColumnMapping({});
    onCancel();
  };
  
  // Download sample CSV template from API
  const downloadSampleCsv = async () => {
    try {
      const result = await downloadSampleCsvMutation({}).unwrap();
      
      // Create blob from the response
      const blob = new Blob([result], {
        type: 'text/csv;charset=utf-8;'
      });
      
      // Create download link
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', 'sample-opportunities.csv');
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      // Clean up the URL object
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Failed to download sample CSV:', error);
    }
  };
  
  // Tab 1: Upload & basic validation entry point
  const renderUploadStep = () => (
    <div className="p-6">
      <div className="text-center mb-6">
        <h4 className="text-lg font-semibold">Import Opportunities</h4>
        <p className="text-sm text-muted-foreground">Upload your CSV file to import opportunities in bulk</p>
      </div>

      <div className="text-center mb-4">
        <ButtonLoader
          variant="outline" 
          onClick={downloadSampleCsv} 
          disabled={isDownloading}
          className="mb-4"
          loading={isDownloading}
        >
          📥 Download Sample CSV Template
        </ButtonLoader>
      </div>

      <div
        className={`border-2 border-dashed rounded-xl p-10 text-center transition-colors ${isDragging ? 'border-primary bg-muted' : 'border-border'}`}
        onDragOver={(e) => { e.preventDefault(); setIsDragging(true); }}
        onDragLeave={() => setIsDragging(false)}
        onDrop={(e) => { e.preventDefault(); setIsDragging(false); const f = e.dataTransfer.files?.[0]; handleFileUpload(f || null); }}
      >
        <div className="flex flex-col items-center justify-center gap-2">
          <InboxOutlined />
          <p className="text-sm">Click to choose or drag CSV file here to upload</p>
          <p className="text-xs text-muted-foreground">Required fields: Title, Organization, Deadline</p>
          {csvHeaders.length > 0 && (
            <p className="text-xs text-green-500">File loaded. Detected {csvHeaders.length} columns.</p>
          )}
          <input
            ref={fileInputRef}
            type="file"
            accept=".csv"
            className="hidden"
            onChange={(e) => handleFileUpload(e.target.files?.[0] || null)}
          />
          <Button
            className="mt-2"
            variant="outline"
            type="button"
            onClick={() => fileInputRef.current?.click()}
          >
            Select CSV File
          </Button>
        </div>
      </div>

      <div className="mt-6 flex gap-2 justify-end">
        <Button variant="outline" onClick={handleReset}>Cancel</Button>
        <Button onClick={() => selectedFile && onImport({ file: selectedFile }, importOptions)} disabled={!selectedFile}>
          Import Opportunities
        </Button>
      </div>
    </div>
  );
  
  // Tab 2: Map CSV headers to Opportunity fields before preview/import
  const renderMappingStep = () => <div className="p-6">
      <h4 className="text-lg font-semibold">Map CSV Columns</h4>
      <p className="text-sm text-muted-foreground">Map your CSV columns to opportunity fields. Required fields are marked with *.</p>
      <div className="mt-4">
        {IMPORT_COLUMNS.map(col => <div key={col.field} className="mb-3">
            <div className="flex items-center gap-3">
              <span className={`text-sm font-medium ${col.required ? '' : 'text-muted-foreground'}`}>{col.label} {col.required && '*'}</span>
              <Select value={columnMapping[col.field] || ''} onValueChange={(value) => setColumnMapping(prev => ({ ...prev, [col.field]: value }))}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Select CSV column" />
                </SelectTrigger>
                <SelectContent>
                  {csvHeaders.map(header => (
                    <SelectItem key={header} value={header}>{header}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>)}
      </div>
      <div className="mt-6 flex gap-2">
        <Button variant="outline" onClick={() => setStep('upload')}>Back</Button>
        <Button onClick={() => setStep('preview')}>
          Preview Import
        </Button>
      </div>
    </div>;
  
  // Tab 3: Preview import results, highlighting invalid rows and possible duplicates
  const renderPreviewStep = () => <div className="p-6 flex-1 flex flex-col">
      <h4 className="text-lg font-semibold">Import Preview</h4>
      
      {/* Import Statistics */}
      <Alert className="mb-4">
        <AlertTitle>{`Ready to import ${stats.total} opportunities`}</AlertTitle>
        <AlertDescription>
          <div>
            <span>Valid: {stats.valid} | </span>
            <span className="text-orange-500">Invalid: {stats.invalid} | </span>
            <span className="text-muted-foreground">Duplicates: {stats.duplicates}</span>
            <br />
            <span className="text-muted-foreground">Showing first {stats.previewed} rows for preview</span>
          </div>
        </AlertDescription>
      </Alert>

      {/* Import Options */}
      <div className="mb-4 space-y-3">
        <div>
          <span className="text-sm font-medium">Import Options:</span>
        </div>
        <div className="flex items-center gap-3">
          <span className="text-sm">Skip invalid rows:</span>
          <Select value={String(importOptions.skipInvalid)} onValueChange={(value) => setImportOptions(prev => ({ ...prev, skipInvalid: value === 'true' }))}>
            <SelectTrigger className="w-[120px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value={'true'}>Yes</SelectItem>
              <SelectItem value={'false'}>No</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="flex items-center gap-3">
          <span className="text-sm">Duplicate handling:</span>
          <Select value={importOptions.duplicateAction} onValueChange={(value) => setImportOptions(prev => ({ ...prev, duplicateAction: value as ImportOptions['duplicateAction'] }))}>
            <SelectTrigger className="w-[220px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="skip">Skip duplicates</SelectItem>
              <SelectItem value="update">Update existing</SelectItem>
              <SelectItem value="duplicate">Create duplicates</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Preview Table */}
      {/* shadCN Table */}
      <div className="overflow-x-auto overflow-y-auto flex-1 no-scrollbar">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[80px]">Status</TableHead>
              {Object.keys(columnMapping).map((field) => (
                <TableHead key={field} className="min-w-[150px]">
                  {IMPORT_COLUMNS.find(c => c.field === field)?.label || field}
                </TableHead>
              ))}
              <TableHead>Issues</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {parsedRows.map((record) => (
              <TableRow key={record.index} className={record.errors.length > 0 ? 'row-error' : record.isDuplicate ? 'row-warning' : ''}>
                <TableCell>
                  {record.errors.length > 0 ? (
                    <ExclamationCircleOutlined className="text-destructive" />
                  ) : record.isDuplicate ? (
                    <ExclamationCircleOutlined className="text-orange-500" />
                  ) : (
                    <CheckCircleOutlined className="text-green-500" />
                  )}
                </TableCell>
                {Object.keys(columnMapping).map((field) => (
                  <TableCell key={field} className="truncate max-w-[220px]">
                    {record.data?.[columnMapping[field]]}
                  </TableCell>
                ))}
                <TableCell>
                  {(() => {
                    const issues: string[] = [...record.errors];
                    if (record.isDuplicate) issues.push(`Duplicate of: ${record.duplicateOf?.title}`);
                    return (
                      <div className="space-y-1">
                        {issues.map((issue, i) => (
                          <div key={i} className="text-red-500 text-xs">{issue}</div>
                        ))}
                      </div>
                    );
                  })()}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="mt-6 flex gap-2 justify-end flex-shrink-0">
        <Button variant="outline" onClick={() => setStep('mapping')}>Back</Button>
        <Button variant="outline" onClick={handleReset}>Cancel</Button>
        <Button onClick={handleImport} disabled={stats.valid === 0}>
          Import {importOptions.skipInvalid ? stats.valid : stats.total} Opportunities
        </Button>
      </div>
    </div>;
  return (
    <Dialog open={visible} onOpenChange={(open) => { if (!open) handleReset(); }}>
      <DialogContent className="max-w-[1000px] max-h-[90vh] flex flex-col overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Import Opportunities from CSV</DialogTitle>
        </DialogHeader>
        {/* Render active tab content based on current wizard step */}
        <div className="min-h-96 flex-1 flex flex-col">
          {step === 'upload' && renderUploadStep()}
          {step === 'mapping' && renderMappingStep()}
          {step === 'preview' && renderPreviewStep()}
        </div>
      </DialogContent>
    </Dialog>
  );
};
export default CsvImportModal;