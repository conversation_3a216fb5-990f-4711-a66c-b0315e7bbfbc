const { DataTypes } = require("sequelize");
const connection = require("../connection");

const Speakers = connection.define("Speakers", {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    comment: 'Primary key for the speaker',
  },
  title: {
    type: DataTypes.STRING,
    comment: 'Title or honorific of the speaker'
  },
  name: {
    type: DataTypes.STRING,
    comment: 'Full name of the speaker'
  },
  email: {
    type: DataTypes.STRING,
    comment: 'Email address of the speaker'
  },
  phone_number: {
    type: DataTypes.STRING,
    comment: 'Phone number of the speaker'
  },
  city: {
    type: DataTypes.STRING,
    comment: 'City where the speaker is based'
  },
  state: {
    type: DataTypes.STRING,
    comment: 'State where the speaker is based'
  },
  affiliate_id: {
    type: DataTypes.INTEGER,
    comment: 'Affiliate ID associated with the speaker'
  },
  referral_speaker_id: {
    type: DataTypes.INTEGER,
    comment: 'Speaker ID associated with the speaker',
  },
  referral_code: {
    type: DataTypes.STRING,
    comment: 'Referral code for the speaker'
  },
   xp_points: {
    type: DataTypes.INTEGER,
    defaultValue: 0,
    comment: 'XP points accumulated by the speaker'
  },
  password: {
    type: DataTypes.STRING,
    comment: 'Password of the speaker'
  },
  linkedin: {
    type: DataTypes.STRING,
    comment: 'LinkedIn profile URL of the speaker'
  },
  bio: {
    type: DataTypes.TEXT,
    comment: 'Biography of the speaker'
  },
  status: {
    type: DataTypes.ENUM('active', 'inactive', 'pending', 'payment_failed', 'cancelled'),
    defaultValue: 'active',
    comment: 'Status of the speaker (active or inactive)'
  },
  email_verified: {
    type: DataTypes.ENUM('0', '1'),
    defaultValue: '0',
    comment: 'Whether the speaker email is verified'
  },
  headshot: {
    type: DataTypes.STRING,
    comment: 'URL or path to the speaker headshot image'
  },
  company: {
    type: DataTypes.STRING,
    comment: 'Company or organization of the speaker'
  },
  source: {
    type: DataTypes.STRING,
    comment: 'Source from where the speaker was added'
  },
  stripe_customer_id: {
    type: DataTypes.STRING,
    comment: 'Stripe customer ID associated with the speaker'
  },
  primary_category: {
    type: DataTypes.STRING,
    comment: 'Primary category of the speaker'
  },
  subcategory: {
    type: DataTypes.STRING,
    comment: 'Subcategory of the speaker'
  },
  topic: {
    type: DataTypes.STRING,
    comment: 'Main topic of the speaker'
  },
  country: {
    type: DataTypes.STRING,
    comment: 'Country where the speaker is based'
  },
  speaker_website: {
    type: DataTypes.STRING,
    comment: 'Official website of the speaker'
  },
  learning_objectives: {
    type: DataTypes.TEXT,
    comment: 'Learning objectives of the speaker sessions'
  },
  takeaways: {
    type: DataTypes.TEXT,
    comment: 'Key takeaways from the speaker sessions'
  },
  challenges: {
    type: DataTypes.TEXT,
    comment: 'Challenges addressed by the speaker'
  },
  preferred_speaker_geography: {
    type: DataTypes.STRING,
    comment: 'Preferred geography for speaking engagements'
  },
  speaker_credentials: {
    type: DataTypes.TEXT,
    comment: 'Credentials of the speaker'
  },
  top_keywords: {
    type: DataTypes.TEXT,
    comment: 'Top keywords associated with the speaker'
  },
  differentiators: {
    type: DataTypes.TEXT,
    comment: 'Unique differentiators of the speaker'
  },
  password_changed_at: {
    type: DataTypes.DATE,
    comment: 'Date and time when the speaker password was changed'
  },
  stripe_subscription_id: {
    type: DataTypes.STRING,
    comment: 'Stripe subscription ID associated with the speaker'
  },
  is_customer:{
    type: DataTypes.TINYINT,
    defaultValue: 0,
    comment: 'Indicates if the speaker is a customer'
  },
  
  created_at: {
    type: DataTypes.DATE,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Date and time when the speaker was created'
  },
  updated_at: {
    type: DataTypes.DATE,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Date and time when the speaker was updated'
  }

}, {
  tableName: 'speakers',
  timestamps: true,
  createdAt: "created_at",
  updatedAt: "updated_at",


});

module.exports = Speakers;
