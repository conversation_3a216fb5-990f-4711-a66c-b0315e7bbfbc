
const { Sequelize } = require('sequelize');
const CONFIG = require('../config/config');

const db = CONFIG.database;
const connection = new Sequelize(db.database, db.user, db.password, {
	host: db.host,
	dialect: db.dialect,
	logging: db.logging,
	pool: db.pool
});

(async () => {
	try {
		await connection.authenticate();
		console.log('Connection has been established successfully.');
	} catch (error) {
		console.error('Unable to connect to the database:', error);
	}
})();

module.exports = connection;

