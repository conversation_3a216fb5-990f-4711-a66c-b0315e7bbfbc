const CustomError = require("../helpers/custome-error");
const ApiResponse = require("../helpers/api-response");
const CONFIG = require("../config/config");
const ResponseCodes = require("../helpers/response-codes");

// Global error handling middleware
exports.exceptionHandling = (error, req, res, next) => {
  console.log(error); // Log error for debugging

  // If it's a CustomError
  if (error instanceof CustomError) {
    // Handle redirect errors if redirect URL is specified
    if (error.redirectTo) return res.redirect(error.redirectTo);

    // Determine the message based on the status code
    const statusMessage = Object.keys(ResponseCodes.RESPONSE_CODES).find(
      (key) => ResponseCodes.RESPONSE_CODES[key] === error.statusCode
    );
    const message = ResponseCodes.RESPONSE_MESSAGES[statusMessage] || error.message;


    // Send structured error response
    return res.status(error.statusCode).json(
      ApiResponse({
        error: {
          code: error.statusCode,
          message: error.message,
          error: message,
        }
      })
    );
  } else {
    // Generic errors
    const statusCode = error.status > 0
      ? error.status
      : error.statusCode > 0
        ? error.statusCode
        : 500;

    const message = error.message || "Something went wrong";

    return res.status(statusCode).json(
      ApiResponse({
        error: {
          code: statusCode,
          message,
          error: CONFIG.NODE_ENV === "dev" ? error.stack : message, // include stack trace optionally
        }
      })
    );
  }
};