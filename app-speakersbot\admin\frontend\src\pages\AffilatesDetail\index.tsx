import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom';
import { ArrowLeftOutlined, CopyOutlined } from '@ant-design/icons';
import { useAppState } from '../../state/AppStateProvider';
import StatusTag from '@/components/common/StatusTag';
import dayjs from 'dayjs';
import { useGetAffiliateByIdQuery, useGetAffiliateSpeakersQuery, useGetAffiliateInquiriesQuery, useLazyGetAffiliateInquiryRequestQuery, useUpdateAffiliateInquiryMutation } from '../../apis/affiliatesApi';
import { Card } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { Input } from '../../components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle } from '../../components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '../../components/ui/pagination';
import { Select as ShadcnSelect, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { useToast } from '../../hooks/use-toast';
import DatePicker from "react-multi-date-picker";
import LoadingSkeleton from '../../components/common/LoadingSkeleton';
import { Skeleton } from '../../components/ui/skeleton';
import { useDebouncedCallback } from '../../utils/debounce';
import { RotateCcw, Search } from 'lucide-react';
import clsx from 'clsx';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';

const AffiliateDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { affiliates } = useAppState();
  const { toast } = useToast();
  const userData=useSelector((state:RootState)=>state.auth.user)


  // Modal state management
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedInquiry, setSelectedInquiry] = useState<any>(null);
  const [isRescheduling, setIsRescheduling] = useState(false);
  const [newDate, setNewDate] = useState<any>(null);
  const [isConfirmed, setIsConfirmed] = useState(false);
  const [approvedSpeakers, setApprovedSpeakers] = useState<Set<string>>(new Set());

  const { data, isLoading, isError } = useGetAffiliateByIdQuery(id as string, { skip: !id });
  const affiliate = data?.affiliate;
  const [speakersPage, setSpeakersPage] = useState<number>(1);
  const [speakersPageSize, setSpeakersPageSize] = useState<number>(10);

  // Date range state for speakers
  const [dateRange, setDateRange] = React.useState<any>(null);

  // Search state for speakers
  const [speakerSearch, setSpeakerSearch] = useState<string>('');
  const [speakerSearchInput, setSpeakerSearchInput] = useState<string>('');
  const debouncedSetSpeakerSearch = useDebouncedCallback((v: string) => setSpeakerSearch(v), 400);
  const [statusFilter, setStatusFilter] = useState<string>('');

  // Filter states for inquiries
  const [inquirySearch, setInquirySearch] = useState<string>('');
  const [inquirySearchInput, setInquirySearchInput] = useState<string>('');
  const debouncedSetInquirySearch = useDebouncedCallback((v: string) => setInquirySearch(v), 400);
  const [inquiryStatusFilter, setInquiryStatusFilter] = useState<string>('');
  const [inquiryDateRange, setInquiryDateRange] = React.useState<any>(null);
  

  // Reset page when search, filter, or date range changes
  React.useEffect(() => {
    setSpeakersPage(1);
  }, [speakerSearch, statusFilter, dateRange]);

  React.useEffect(() => {
    setInquiriesPage(1);
  }, [inquirySearch, inquiryStatusFilter, inquiryDateRange]);

  // Convert status filter to API format
  const getStatusFilter = () => {
    if (statusFilter === 'active') return { status: true };
    if (statusFilter === 'inactive') return { status: false };
    if (statusFilter === 'all' || statusFilter === '') return undefined;
    return undefined;
  };

  // Convert date range to API format
  const getDateRangeFilter = () => {
    if (dateRange && Array.isArray(dateRange) && dateRange.length === 2 && dateRange[0] && dateRange[1]) {
      // Convert DateObject to Date if needed, or use format method
      const startDate = dateRange[0].toDate ? dateRange[0].toDate() : new Date(dateRange[0]);
      const endDate = dateRange[1].toDate ? dateRange[1].toDate() : new Date(dateRange[1]);

      return {
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0]
      };
    }
    return undefined;
  };

  // Convert inquiry status filter to API format
  const getInquiryStatusFilter = () => {
    // When "all" or empty, do not send a status filter
    if (inquiryStatusFilter === 'all' || inquiryStatusFilter === '') return undefined;

    // For any selected status, pass it through directly
    // Supported: pending, accepted, rejected, rescheduled
    return { status: inquiryStatusFilter } as any;
  };

  // Convert inquiry date range to API format
  const getInquiryDateRangeFilter = () => {
    if (inquiryDateRange && Array.isArray(inquiryDateRange) && inquiryDateRange.length === 2 && inquiryDateRange[0] && inquiryDateRange[1]) {
      // Convert DateObject to Date if needed, or use format method
      const startDate = inquiryDateRange[0].toDate ? inquiryDateRange[0].toDate() : new Date(inquiryDateRange[0]);
      const endDate = inquiryDateRange[1].toDate ? inquiryDateRange[1].toDate() : new Date(inquiryDateRange[1]);

      return {
        start_date: startDate.toISOString().split('T')[0],
        end_date: endDate.toISOString().split('T')[0]
      };
    }
    return undefined;
  };

  // Clear all filters
  const clearFilters = () => {
    setSpeakerSearch('');
    setSpeakerSearchInput('');
    setStatusFilter('');
    setDateRange(null);
  };

  // Clear all inquiry filters
  const clearInquiryFilters = () => {
    setInquirySearch('');
    setInquirySearchInput('');
    setInquiryStatusFilter('');
    setInquiryDateRange(null);
  };

  const { data: speakersData, isLoading: speakersLoading, isError: speakersError } = useGetAffiliateSpeakersQuery(
    {
      affiliateId: id as string,
      page: speakersPage,
      limit: speakersPageSize,
      search: speakerSearch || undefined,
      filter: getStatusFilter(),
      dateRange: getDateRangeFilter(),
    },
    { skip: !id }
  );
  // Load inquiries for this affiliate (using affiliate id in route)
  const [inquiriesPage, setInquiriesPage] = useState<number>(1);
  const [inquiriesPageSize, setInquiriesPageSize] = useState<number>(10);
  const { data: inquiriesData, isLoading: inquiriesLoading, isError: inquiriesError, refetch: refetchInquiries } = useGetAffiliateInquiriesQuery(
    {
      affiliateId: id as string,
      page: inquiriesPage,
      limit: inquiriesPageSize,
      search: inquirySearch || undefined,
      filter: getInquiryStatusFilter(),
      dateRange: getInquiryDateRangeFilter(),
    },
    { skip: !id }
  );

  // Lazy fetch for a single inquiry's details
  const [fetchInquiryDetails, { data: inquiryDetails, isFetching: isInquiryLoading }] = useLazyGetAffiliateInquiryRequestQuery();

  // Mutation for updating affiliate inquiry
  const [updateAffiliateInquiry, { isLoading: isUpdatingInquiry }] = useUpdateAffiliateInquiryMutation();

  // Merge fetched details into selected inquiry
  React.useEffect(() => {
    if (inquiryDetails) {
      setSelectedInquiry((prev: any) => ({
        ...prev,
        speakerName: inquiryDetails.speakerName ?? prev?.speakerName,
        speakerNote: inquiryDetails.speakerNote ?? prev?.speakerNote,
        inquiryDate: inquiryDetails.inquiryDate ?? prev?.inquiryDate,
        inquiryTime: inquiryDetails.inquiryTime ?? prev?.inquiryTime,
        status: inquiryDetails.status ?? prev?.status,
      }));
    }
  }, [inquiryDetails]);

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-4">
        {isError && (
          <Alert className="mb-4">
            <AlertDescription>Failed to load affiliate</AlertDescription>
          </Alert>
        )}
      </div>
    );
  }

  const loadingAffiliate = isLoading || !affiliate;

  if (isError) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] p-4">
        {isError && (
          <Alert className="mb-4">
            <AlertDescription>Failed to load affiliate</AlertDescription>
          </Alert>
        )}
        <h2 className="text-xl font-semibold mb-4">Affiliate Not Found</h2>
        <Button onClick={() => navigate('/admin/affiliates')}>Back to Affiliates</Button>
      </div>
    );
  }

  const handleCopyLink = (link: string) => {
    navigator.clipboard.writeText(link);
    toast({ description: 'Affiliate link copied to clipboard' });
  };

  // Modal handlers
  const handleViewRequest = async (inquiry: any) => {
    setSelectedInquiry(inquiry);
    setIsModalVisible(true);
    setIsRescheduling(false);
    setNewDate(null);
    setIsConfirmed(false);
    
    if (inquiry?.id) {
      try {
        await fetchInquiryDetails(inquiry.affiliateId).unwrap();
      } catch (error: any) {
        console.error('Error fetching inquiry details:', error);
        setIsModalVisible(false);
        setSelectedInquiry(null);
        
        // Handle specific error cases
        if (error?.data?.error?.message === "Affiliate inquiry not found") {
          toast({
            description: error?.data?.error?.message || "Affiliate inquiry not found. This inquiry may have been deleted or is no longer available.",
            variant: 'destructive'
          });
          setIsModalVisible(false);
          setSelectedInquiry(null);
          return;
        }
        
        // Handle other errors
        toast({
          description: error?.data?.error?.message || error?.message || 'Failed to load inquiry details',
          variant: 'destructive'
        });
       
      }
    }
  };



  const handleCloseModal = () => {
    setIsModalVisible(false);
    setSelectedInquiry(null);
    setIsRescheduling(false);
    setNewDate(null);
    setIsConfirmed(false);
  };

  const handleApprove = async () => {
    if (!selectedInquiry?.id) {
      toast({ description: 'No inquiry selected', variant: 'destructive' });
      return;
    }

    try {
      const payload = {
        inquiryId: selectedInquiry.id,
        note: inquiryDetails?.speakerNote,
        status: "accepted",
        // inquiry_date: "2025-09-23",
        // inquiry_time: "07:50:15"
      };

      const result = await updateAffiliateInquiry(payload).unwrap();

      // Check for success
      if (result?.success) {
        toast({ description: `Request for ${selectedInquiry?.speakerName} has been approved!` });
        setApprovedSpeakers(prev => new Set([...prev, selectedInquiry.key]));
        setIsConfirmed(true);
        await refetchInquiries();
        handleCloseModal();
      } else {
        // If we get a response but no clear success, still close modal and show success
        // This handles cases where the API doesn't return a clear success flag
        toast({ description: `Request for ${selectedInquiry?.speakerName} has been approved!` });
        setApprovedSpeakers(prev => new Set([...prev, selectedInquiry.key]));
        setIsConfirmed(true);
        await refetchInquiries();
        handleCloseModal();
      }
    } catch (error: any) {
      console.error('Error approving inquiry:', error);
      toast({
        description: error?.data?.message || error?.message || 'Failed to approve request',
        variant: 'destructive'
      });
    }
  };

  const handleReschedule = () => {
    if (newDate) {
      toast({ description: `Meeting rescheduled to ${dayjs(newDate).format('MMMM D, YYYY')} for ${selectedInquiry?.speakerName}` });
      setApprovedSpeakers(prev => new Set([...prev, selectedInquiry.key]));
      setIsConfirmed(true);
      setIsRescheduling(false);
    } else {
      toast({ description: 'Please select a new date' });
    }
  };

  const handleReject = async () => {
    if (!selectedInquiry?.id) {
      toast({ description: 'No inquiry selected', variant: 'destructive' });
      return;
    }

    try {
      const payload = {
        inquiryId: selectedInquiry.id,
        note: inquiryDetails?.speakerNote,
        status: "rejected",
      };

      const result = await updateAffiliateInquiry(payload).unwrap();

      // Check for success
      if (result?.success) {
        toast({ description: `Request for ${selectedInquiry?.speakerName} has been rejected.` });
        await refetchInquiries();
        handleCloseModal();
      } else {
        // If we get a response but no clear success, still close modal and show success
        // This handles cases where the API doesn't return a clear success flag
        toast({ description: `Request for ${selectedInquiry?.speakerName} has been rejected.` });
        await refetchInquiries();
        handleCloseModal();
      }
    } catch (error: any) {
      console.error('Error rejecting inquiry:', error);
      toast({
        description: error?.data?.message || error?.message || 'Failed to reject request',
        variant: 'destructive'
      });
    }
  };



  return (
    <div className="">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-start flex-col  gap-4">
          {
            userData.role.name.toLowerCase() !== "affiliate" &&
            <div className='bg-card border px-3 py-1 rounded-md text-sm cursor-pointer' onClick={() => navigate('/admin/affiliates')}>
              <ArrowLeftOutlined className="mr-2" />
              Back to Affiliates
            </div>
          }
          
          <h1 className="text-2xl font-bold m-0 text-foreground">
            {affiliate?.name}
          </h1>
        </div>
        <Button onClick={() => navigate(`/admin/affiliates/${id}/landing-page`)}>
          Landing page
        </Button>
      </div>

      {/* Performance Overview */}
      <Card className="mb-6 bg-tertiary">
        <div className="text-lg font-semibold p-4 pl-6">Performance Metrics</div>
        <div className="grid grid-cols-1 sm:grid-cols-4 gap-4 border-t p-4">
          {loadingAffiliate ? (
            <>
              <div className="p-3 rounded">
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-6 w-16" />
              </div>
              <div className="p-3 rounded">
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-6 w-16" />
              </div>
              <div className="p-3 rounded">
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-6 w-16" />
              </div>
              <div className="p-3 rounded">
                <Skeleton className="h-4 w-32 mb-2" />
                <Skeleton className="h-6 w-16" />
              </div>
            </>
          ) : (
            <>
              <div className="p-3 rounded">
                <div className="text-sm text-muted-foreground">Total Clicks</div>
                <div className="text-2xl font-semibold">{affiliate?.clicks || 0}</div>
              </div>
              <div className="p-3 rounded">
                <div className="text-sm text-muted-foreground">Signups</div>
                <div className="text-2xl font-semibold">{affiliate?.signups || 0}</div>
              </div>
              <div className="p-3">
                <div className="text-sm text-muted-foreground">Conversions</div>
                <div className="text-2xl font-semibold">{affiliate?.conversions || 0}</div>
              </div>
              <div className="p-3">
                <div className="text-sm text-muted-foreground">Total Commission</div>
                <div className="text-2xl font-semibold">{data?.breakdown?.total_commission || 0}</div>
              </div>
              
            </>
          )}
        </div>
      </Card>

      {/* Commission Breakdown */}
      

      {/* Affiliate Information */}
      <Card className="mb-6 bg-tertiary">
        <div className="text-lg font-semibold p-4 ">Affiliate Information</div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 p-4 border-t">
          {loadingAffiliate ? (
            <>
              <div>
                <Skeleton className="h-4 w-24 mb-2" />
                <Skeleton className="h-4 w-48" />
              </div>
              <div>
                <Skeleton className="h-4 w-28 mb-2" />
                <Skeleton className="h-4 w-40" />
              </div>
              <div>
                <Skeleton className="h-4 w-20 mb-2" />
                <Skeleton className="h-6 w-24" />
              </div>
              <div>
                <Skeleton className="h-4 w-20 mb-2" />
                <Skeleton className="h-4 w-32" />
              </div>
              <div className="sm:col-span-2">
                <Skeleton className="h-4 w-32 mb-2" />
                <div className="flex items-center gap-2">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-9 w-20" />
                </div>
              </div>
            </>
          ) : (
            <>
              <div>
                <span className="font-semibold text-sm">Email:</span> {affiliate.email}
              </div>
              <div>
                <span className="font-semibold text-sm">Affiliate ID:</span> <code>{affiliate.affiliateId}</code>
              </div>
              <div>
                <span className="font-semibold text-sm">Status:</span>
                <span className="ml-2 text-sm">
                  <StatusTag status={affiliate.status ? "Active" : "Inactive"} label={affiliate.status ? "Active" : "Inactive"} className={affiliate.status ? "border border-[#88E788]":"" }/>
                </span>
              </div>
              <div>
                <span className="font-semibold text-sm">Joined:</span> {new Date(affiliate.createdAt).toLocaleDateString()}
              </div>
              <div className="sm:col-span-2">
                <span className="font-semibold text-sm">Affiliate Link:</span>
                <div className="mt-2">
                  <div className="flex items-center gap-2">
                    <Input value={affiliate.link} readOnly className="flex-1" size={10} />
                    <Button size='sm' variant='outline' onClick={() => handleCopyLink(affiliate.link)}>
                      <CopyOutlined className="mr-1  size-sm" />
                      Copy
                    </Button>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>
      </Card>

      {/* Affiliated Speakers Log */}
      <Card className="mb-6 bg-tertiary">
        <div className="flex items-center justify-between p-4 border-b">
          <div className="text-lg font-semibold">Affiliated Speakers Log</div>
          <StatusTag status='active' label={`${speakersData?.pagination?.total ?? 0} speaker`} />
        </div>
        {speakersError && (
          <Alert className="mb-3"><AlertDescription>Failed to load speakers</AlertDescription></Alert>
        )}
        {!speakersLoading ? (
          <div className="p-4">
           
            <div className="grid grid-cols-[1fr_1fr_1fr_auto] gap-3 ">

              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder='Search speakers...'
                  value={speakerSearchInput}
                  onChange={(e) => { const v = e.target.value; setSpeakerSearchInput(v); debouncedSetSpeakerSearch(v); }}
                  className="pl-10"
                />
              </div>
              <ShadcnSelect value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Filter by status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </ShadcnSelect>
              <DatePicker
                value={dateRange}
                onChange={(dates) => setDateRange(dates as unknown as any)}
                range
                numberOfMonths={2}
                arrow={false}
                className="custom-calendar"
                placeholder="Select Date Range"
                inputClass="h-10 rounded-md border border-input bg-background w-full px-3 text-sm placeholder:text-foreground"
              />
              <Button
                variant="outline"
                onClick={clearFilters}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Clear Filters
              </Button>

            </div>
          </div>
        ) : (
          <>
            {/* <LoadingSkeleton type="table" rows={1} /> */}
            <div className="grid grid-cols-3 px-4 gap-4 pb-4">
              <Skeleton className="h-8 rounded" />
              <Skeleton className="h-8  rounded" />
              <Skeleton className="h-8  rounded" />
            </div>
          </>
        )
        }

        {!speakersLoading && (speakersData?.speakers?.length || 0) > 0 ? (
          <div className='mx-4 border rounded-md'>
            <Table className=''>
              <TableHeader>
                <TableRow>
                  <TableHead>Speaker Name</TableHead>
                  <TableHead>Join Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Commission</TableHead>
                </TableRow>
              </TableHeader>

              <TableBody>
                {(speakersData?.speakers || []).map((s: any) => (
                  <TableRow key={s.id}>
                    <TableCell>
                      <div
                        className="cursor-pointer hover:text-primary transition-colors"
                        onClick={() => navigate(`/admin/speakers/${s.id}`)}
                      >
                        <div className="font-medium text-primary hover:underline">{s.name}</div>
                        <div className="text-xs text-muted-foreground">{s.email}</div>
                      </div>
                    </TableCell>
                    <TableCell>{new Date(s.joinDate).toLocaleDateString()}</TableCell>
                    <TableCell><StatusTag status={s.status}  className={`capitalize ${s.status === 'active' ? "border border-[#88E788]" : ""}` } /></TableCell>
                   
                    <TableCell>
                      <span className="font-semibold text-green-600">${s.commission.toFixed(2)}</span>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        ) : !speakersLoading && (speakersData?.speakers?.length || 0) === 0 ? (
          <div className="text-center py-12">
            <div className="text-lg text-muted-foreground">No speakers found</div>
            <div className="text-sm text-muted-foreground mt-2">
              {speakerSearch || (statusFilter && statusFilter !== 'all') || dateRange
                ? 'Try adjusting your filters to see more results.'
                : 'No speakers have joined via this affiliate yet.'}
            </div>
          </div>
        ) : null}

        {!speakersLoading && (speakersData?.speakers?.length || 0) > 0 ? (
          <div className="flex items-center justify-between px-4 py-4">
            <span>
              Showing {((speakersData?.pagination?.page ?? 1) - 1) * speakersPageSize + 1} to {Math.min((speakersData?.pagination?.page ?? 1) * speakersPageSize, speakersData?.pagination?.total ?? 0)} of {speakersData?.pagination?.total ?? 0} speakers
            </span>

            <div className="flex items-center gap-3">
              <Pagination>
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious 
                      onClick={() => setSpeakersPage((p) => Math.max(1, p - 1))} 
                      aria-disabled={(speakersData?.pagination?.page ?? speakersPage) <= 1}
                      className={(speakersData?.pagination?.page ?? speakersPage) <= 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                  {(() => {
                    const totalPages = speakersData?.pagination?.totalPages ?? 1;
                    const current = speakersData?.pagination?.page ?? speakersPage;
                    const pages: number[] = [];
                    const start = Math.max(1, current - 2);
                    const end = Math.min(totalPages, start + 4);
                    if (start > 1) pages.push(1);
                    const showLeftEllipsis = start > 2;
                    const windowPages: number[] = [];
                    for (let p = start; p <= end; p++) windowPages.push(p);
                    const showRightEllipsis = end < (totalPages - 1);
                    return (
                      <>
                        {pages.map((p) => (
                          <PaginationItem key={`sp-first-${p}`} >
                            <PaginationLink isActive={p === current} size="sm" onClick={() => setSpeakersPage(p)}>
                              {p}
                            </PaginationLink>
                          </PaginationItem>
                        ))}
                        {showLeftEllipsis && (
                          <PaginationItem key="sp-left-ellipsis">
                            <PaginationEllipsis />
                          </PaginationItem>
                        )}
                        {windowPages.map((p) => (
                          <PaginationItem key={`sp-${p}`}>
                            <PaginationLink isActive={p === current} onClick={() => setSpeakersPage(p)}>
                              {p}
                            </PaginationLink>
                          </PaginationItem>
                        ))}
                        {showRightEllipsis && (
                          <PaginationItem key="sp-right-ellipsis">
                            <PaginationEllipsis />
                          </PaginationItem>
                        )}
                        {end < totalPages && (
                          <PaginationItem key={`sp-last-${totalPages}`}>
                            <PaginationLink isActive={totalPages === current} onClick={() => setSpeakersPage(totalPages)}>
                              {totalPages}
                            </PaginationLink>
                          </PaginationItem>
                        )}
                      </>
                    );
                  })()}
                  <PaginationItem>
                    <PaginationNext 
                      onClick={() => setSpeakersPage((p) => p + 1)} 
                      aria-disabled={(speakersData?.pagination?.page ?? speakersPage) >= (speakersData?.pagination?.totalPages ?? 1)}
                      className={(speakersData?.pagination?.page ?? speakersPage) >= (speakersData?.pagination?.totalPages ?? 1) ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
              
              <div className="flex items-center gap-3">
                <span className="text-sm text-muted-foreground hidden sm:block">Rows per page</span>
                <ShadcnSelect value={String(speakersPageSize)} onValueChange={(v) => { setSpeakersPageSize(Number(v)); setSpeakersPage(1); }}>
                  <SelectTrigger className="w-[80px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </ShadcnSelect>
              </div>
            </div>
          </div>
        ) : null}
      </Card>

      {/* Reached Out Inquiries */}
      <Card className="bg-tertiary">
        <div className="flex items-center justify-between p-4 border-b">
          <div className="text-lg font-semibold">Reached Out Inquiries</div>
          <StatusTag status='active' className="capitalize" label={`${inquiriesData?.pagination?.total ?? 0} inquiries`} />
        </div>

        {inquiriesError && (
          <Alert className="mb-3"><AlertDescription className=''>Failed to load inquiries</AlertDescription></Alert>
        )}
        <div className="p-4">
          
          <div className='grid grid-cols-[1fr_1fr_1fr_auto] gap-3 '>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder='Search inquiries...'
                value={inquirySearchInput}
                onChange={(e) => { const v = e.target.value; setInquirySearchInput(v); debouncedSetInquirySearch(v); }}
                className="pl-10"
              />
            </div>

            <ShadcnSelect value={inquiryStatusFilter} onValueChange={setInquiryStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="accepted">Approved</SelectItem>
                <SelectItem value="rejected">Rejected</SelectItem>
              </SelectContent>
            </ShadcnSelect>
            <DatePicker
              value={inquiryDateRange}
              onChange={(dates) => setInquiryDateRange(dates as unknown as any)}
              range
              numberOfMonths={2}
              arrow={false}
              className="custom-calendar"
              placeholder="Select Date Range"
              inputClass="h-10 rounded-md border border-input bg-black w-full px-3 text-sm placeholder:text-foreground"
            />
            <Button
              variant="outline"
              onClick={clearInquiryFilters}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Clear Filters
            </Button>

          </div>
        </div>

        {inquiriesLoading && (
          <>
            {/* Filters skeleton */}
            <div className="grid grid-cols-3 px-4 gap-4 pb-4">
              <Skeleton className="h-10 rounded" />
              <Skeleton className="h-10 rounded" />
              <Skeleton className="h-10 rounded" />
            </div>
            {/* Table skeleton */}
            <LoadingSkeleton type="table" rows={3} />
            {/* Pagination skeleton */}
            <div className="flex items-center justify-end px-4 py-4">
              <div className="flex items-center gap-2">
                <Skeleton className="h-8 w-8 rounded" />
                <Skeleton className="h-8 w-40 rounded" />
                <Skeleton className="h-8 w-8 rounded" />
              </div>
            </div>
          </>
        )}

        {!inquiriesLoading && (inquiriesData?.data?.length || 0) > 0 ? (
          <div className='mx-4 rounded border'>
            <Table className="">
              <TableHeader>
                <TableRow>
                  <TableHead>Speaker Name</TableHead>
                  {/* <TableHead>Speaker ID</TableHead> */}
                  <TableHead>Inquiry Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {(inquiriesData?.data || []).map((inq: any) => {
                  const isApproved = approvedSpeakers.has(String(inq.id));
                  return (
                    <TableRow key={inq.id} >
                      <TableCell>
                        <div
                          className="cursor-pointer hover:text-primary transition-colors"
                          onClick={() => navigate(`/admin/speakers/${inq.speakerId}`)}
                        >
                          <div className="font-medium text-primary hover:underline capitalize">{inq.speakerName || '—'}</div>
                        </div>
                      </TableCell>
                      {/* <TableCell>
                    <code className="text-xs bg-muted px-2 py-1 rounded">{inq.speakerId}</code>
                  </TableCell> */}
                      <TableCell>{inq.inquiryDate ? new Date(inq.inquiryDate).toLocaleDateString() : '—'}</TableCell>
                      <TableCell>
                        {inq?.status && (
                          <StatusTag status={inq?.status} className={`capitalize ${inq?.status === 'active' ? "border border-[#88E788]" : ""}`} />
                        )}
                      </TableCell>
                      <TableCell>
                        {/* {isApproved ? (
                          <div className="text-center">
                            <div className="text-xs text-muted-foreground mb-1">Status</div>
                            <div className="font-medium text-green-600">Approved</div>
                          </div>
                        ) : ( */}
                        <Button size="sm" onClick={() => handleViewRequest({
                          id: inq.id,
                          key: String(inq.id),
                          speakerName: inq.speakerName,
                          inquiryDate: inq.inquiryDate,
                          meetingDate: inq.inquiryDate,
                          speakerId: inq.speakerId,
                          affiliateId: inq.affiliateId

                        })}>
                          View Request
                        </Button>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>
        ) : !inquiriesLoading && (inquiriesData?.data?.length || 0) === 0 ? (
          <div className="text-center py-12">
            <div className="text-lg text-muted-foreground">No inquiries found</div>
            <div className="text-sm text-muted-foreground mt-2">
              {inquirySearch || inquiryStatusFilter !== 'all' 
                ? 'Try adjusting your filters to see more results.'
                : 'No inquiries have been submitted yet.'}
            </div>
          </div>
        ) : null}


        {
          !inquiriesLoading && (inquiriesData?.data?.length || 0) > 0 ? (
            <div className="flex items-center justify-between px-4 py-4">
              <span>
                Showing {((inquiriesData?.pagination?.page ?? 1) - 1) * inquiriesPageSize + 1} to {Math.min((inquiriesData?.pagination?.page ?? 1) * inquiriesPageSize, inquiriesData?.pagination?.total ?? 0)} of {inquiriesData?.pagination?.total ?? 0} inquiries
              </span>

              <div className="flex items-center gap-3">
                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious 
                        onClick={() => setInquiriesPage((p) => Math.max(1, p - 1))} 
                        aria-disabled={(inquiriesData?.pagination?.page ?? inquiriesPage) <= 1}
                        className={(inquiriesData?.pagination?.page ?? inquiriesPage) <= 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                      />
                    </PaginationItem>
                    {(() => {
                      const totalPages = inquiriesData?.pagination?.totalPages ?? 1;
                      const current = inquiriesData?.pagination?.page ?? inquiriesPage;
                      const pages: number[] = [];
                      const start = Math.max(1, current - 2);
                      const end = Math.min(totalPages, start + 4);
                      if (start > 1) pages.push(1);
                      const showLeftEllipsis = start > 2;
                      const windowPages: number[] = [];
                      for (let p = start; p <= end; p++) windowPages.push(p);
                      const showRightEllipsis = end < (totalPages - 1);
                      return (
                        <>
                          {pages.map((p) => (
                            <PaginationItem key={`inq-first-${p}`}>
                              <PaginationLink isActive={p === current} onClick={() => setInquiriesPage(p)}>
                                {p}
                              </PaginationLink>
                            </PaginationItem>
                          ))}
                          {showLeftEllipsis && (
                            <PaginationItem key="inq-left-ellipsis">
                              <PaginationEllipsis />
                            </PaginationItem>
                          )}
                          {windowPages.map((p) => (
                            <PaginationItem key={`inq-${p}`}>
                              <PaginationLink isActive={p === current} onClick={() => setInquiriesPage(p)}>
                                {p}
                              </PaginationLink>
                            </PaginationItem>
                          ))}
                          {showRightEllipsis && (
                            <PaginationItem key="inq-right-ellipsis">
                              <PaginationEllipsis />
                            </PaginationItem>
                          )}
                          {end < totalPages && (
                            <PaginationItem key={`inq-last-${totalPages}`}>
                              <PaginationLink isActive={totalPages === current} onClick={() => setInquiriesPage(totalPages)}>
                                {totalPages}
                              </PaginationLink>
                            </PaginationItem>
                          )}
                        </>
                      );
                    })()}
                    <PaginationItem>
                      <PaginationNext 
                        onClick={() => setInquiriesPage((p) => p + 1)} 
                        aria-disabled={(inquiriesData?.pagination?.page ?? inquiriesPage) >= (inquiriesData?.pagination?.totalPages ?? 1)}
                        className={(inquiriesData?.pagination?.page ?? inquiriesPage) >= (inquiriesData?.pagination?.totalPages ?? 1) ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>

                <div className="flex items-center gap-3">
                  <span className="text-sm text-muted-foreground hidden sm:block">Rows per page</span>
                  <ShadcnSelect value={String(inquiriesPageSize)} onValueChange={(v) => { setInquiriesPageSize(Number(v)); setInquiriesPage(1); }}>
                    <SelectTrigger className="w-[80px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="25">25</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </ShadcnSelect>
                </div>
              </div>
            </div>
          ) : null
        }


      </Card>

      {/* Request Details Modal */}
      <Dialog open={isModalVisible} onOpenChange={setIsModalVisible}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>{`Request from ${selectedInquiry?.speakerName || ''}`}</DialogTitle>
          </DialogHeader>
          {selectedInquiry && (
            <div className="space-y-4">
              {isConfirmed && (
                <div className="p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-md text-sm">
                  ✓ Request has been confirmed and processed
                </div>
              )}

              <Tabs defaultValue="details">
                <TabsList className="mb-2 bg-[#05244a] h-auto px-0 py-2 border-b border-transparent gap-8">
                  <TabsTrigger
                    value="details"
                    className="text-white rounded-none px-0 pb-2 data-[state=active]:text-cyan-400 data-[state=active]:border-b-2 data-[state=active]:border-[#009BC9] data-[state=active]:bg-tertiary"
                  >
                    Request Details
                  </TabsTrigger>
                  <TabsTrigger
                    value="history"
                    className="text-white rounded-none px-0 pb-1 data-[state=active]:text-cyan-400 data-[state=active]:border-b-2 data-[state=active]:border-[#009BC9] data-[state=active]:bg-tertiary"
                  >
                    History
                  </TabsTrigger>
                </TabsList>

                <TabsContent value="details" className="space-y-4 mt-0">
                  <div>
                    <div className="font-semibold mb-2">Speaker's Note:</div>
                    <div className="bg-muted/50 p-3 rounded border border-border text-sm">
                      {selectedInquiry.speakerNote || '—'}
                    </div>
                  </div>
                  <div>
                    <div className="font-semibold mb-2 text-sm">Requested Meeting Date:</div>
                    <div className="flex items-center gap-2">
                      <div className="text-lg">
                        {selectedInquiry.inquiryDate ? dayjs(selectedInquiry.inquiryDate).format("MMMM D, YYYY") : (selectedInquiry.meetingDate ? dayjs(selectedInquiry.meetingDate).format("MMMM D, YYYY") : '—')}
                      </div>
                    </div>
                    <div className="text-sm text-muted-foreground">Time: {selectedInquiry.inquiryTime || '—'}</div>
                  </div>
                  <div>
                    {/* <div className="font-semibold mb-2 text-sm">Status:</div>
                    <Badge variant={selectedInquiry.status ? 'default' : 'secondary'} className='text-xs'>
                      {selectedInquiry.status || 'Pending'}
                    </Badge> */}
                  </div>
                  <div className="pt-4 border-t border-border flex gap-2">
                    {!isConfirmed ? (
                      <Button onClick={handleApprove} disabled={isUpdatingInquiry}>
                        {isUpdatingInquiry ? 'Processing...' : 'Approve'}
                      </Button>
                    ) : (
                      <Button disabled>✓ Confirmed</Button>
                    )}
                    <Button variant="outline" className="border border-[#9CD2E766]" onClick={handleReject} disabled={isUpdatingInquiry}>{isUpdatingInquiry ? 'Processing...' : 'Reject'}</Button>
                    <Button variant="outline" className="border border-[#9CD2E766]" onClick={handleCloseModal}>Reschedule</Button>
                    <Button variant="outline" className="border border-[#9CD2E766]" onClick={handleCloseModal}>Close</Button>
                  </div>
                </TabsContent>

                <TabsContent value="history" className="space-y-3 mt-0">
                  <div className="text-sm">Action History</div>
                  <div className="space-y-2 text-sm front-semibold ">
                    <div className="flex items-start gap-2 bg-[#0016334D]">
                      {/* <div className="mt-1 h-2 w-2 rounded-full bg-primary" /> */}
                      <div className='border-l-4 border-[#0095C7] rounded-sm pl-2 flex gap-3 p-2'>
                        <div className="font-medium">Inquiry received</div>
                        <div className="">
                          {dayjs(selectedInquiry.inquiryDate || selectedInquiry.meetingDate).format("MMM D, YYYY")}
                        </div>
                      </div>
                    </div>
                    {isConfirmed && (
                      <div className="flex items-start gap-2">
                        <div className="mt-1 h-2 w-2 rounded-full bg-green-500" />
                        <div>
                          <div className="font-medium">Meeting confirmed</div>
                          <div className="text-muted-foreground">
                            {dayjs(newDate || selectedInquiry.meetingDate).format("MMM D, YYYY")}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </TabsContent>
              </Tabs>



            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default AffiliateDetail;