
const userController = require("../controllers/user-controller");
const router = require("express").Router();
const checkPermission = require("../middlewares/permission-middleware");
const verifyToken = require("../middlewares/verify-token");

module.exports = (app) => {

    // ------------------------- user -------------------------

    // create or update user
    router.post("/upsertUser", userController.upsertUser);
    
    // delete user by id
    router.delete("/deleteUser/:id", userController.deleteUser);
    
    // get user by id
    router.get("/getUser/:id", userController.getUser);
    
    // get all users
    router.get("/getUsers", userController.getUsers);
    
    router.get("/authUser", userController.authUser);
    
    app.use("/admin/api/v1", router);

};
