const { Sequelize } = require("sequelize");
const { Speakers, Subscriptions, AffiliateUsersDetails, sequelize, PricingPlan } = require("../models");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const { getStripe, determineMetadata, buildSessionConfig } = require("../helpers/stripe");

const stripeService = {};

stripeService.checkout = async (payload) => {
    try {
        const { plan_id, customer_email, referral_code } = payload;

        const [plan, registrationFee, speaker] = await Promise.all([
            PricingPlan.findOne({ where: { id: plan_id, is_active: '1' } }),
            PricingPlan.findOne({ where: { name: 'registration_fee', is_active: '1' } }),
            Speakers.findOne({ where: { email: customer_email } })
        ]);

        if (!plan) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid or inactive plan id");
        if (!speaker) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Speaker with given email does not exist");


        const isNewCustomer = !speaker.stripe_customer_id || speaker.is_customer === 0;
        const stripeCustomerId = speaker.stripe_customer_id;

        // Determine mode, line items, and metadata
        const { mode, line_items, metadata } = determineMetadata(
            plan,
            speaker,
            referral_code,
            isNewCustomer ? registrationFee : null
        );

        const stripeClient = await getStripe();
        const session = await stripeClient.checkout.sessions.create(
            buildSessionConfig(
                speaker,
                stripeCustomerId,
                "http://localhost:3000/success.html?session_id={CHECKOUT_SESSION_ID}",
                "http://localhost:3000/cancel.html",
                mode,
                line_items,
                metadata
            )
        );

        return { status: true, data: session.url, message: "Checkout session created successfully" };
    } catch (err) {
        console.error(err);
        throw err;
    }
};

//for handling successful payments for subscriptions
stripeService.invoicePaymentSucceeded = async (data) => {
    let transaction;
    transaction = await sequelize.transaction();

    try {
        const metadata = data?.parent?.subscription_details?.metadata
            || data?.lines?.data?.[0]?.metadata
            || {};
        const speakerId = metadata.speaker_id;
        const referralCode = metadata.referral_code;

        let stripeCustomerId = data?.customer;

        const is_customer = await Speakers.findOne({ where: { id: speakerId } });

        // Increment referral conversion count only for the speaker's first paid subscription
        if (is_customer && is_customer.is_customer === 0 && referralCode) {
            await AffiliateUsersDetails.update({
                conversion_count: Sequelize.literal('conversion_count + 1')
            }, {
                where: { referral_code: referralCode },
                transaction
            });
        }

        // Create subscription record in DB
        await Subscriptions.create({
            invoice_id: data.id,
            speaker_id: speakerId,
            plan_id: metadata.plan_id,
            stripe_subscription_id: data?.parent?.subscription_details?.subscription,
            status: 'active',
            start_date: new Date(data?.lines.data[0].period.start * 1000),
            end_date: new Date(data?.lines.data[0].period.end * 1000),
            amount: data?.amount_paid / 100,
            currency: data?.currency || 'usd',
            invoice_url: data.hosted_invoice_url,
            charge_id: data?.charge || null,
            created_at: new Date(),
            updated_at: new Date()
        }, { transaction });

        // Update speaker with customer + subscription
        await Speakers.update(
            {
                stripe_subscription_id: data?.parent?.subscription_details?.subscription,
                stripe_customer_id: stripeCustomerId,
                is_customer: 1,
                status: 'active'
            },
            { where: { id: speakerId }, transaction }
        );

        await transaction.commit();

    } catch (err) {
        await transaction.rollback();
        console.error("Error handling invoice.payment_succeeded:", err);
        throw err;
    }
};

/**
 * Handle Stripe invoice payment failed event.
 * Updates speaker and subscription status to inactive.
 * 
 * @param {Object} data - Stripe webhook data
 * @param {number} data.amount_due - Amount due for failed payment
 * @param {string} data.customer - Stripe customer ID
 * @param {string} data.subscription - Stripe subscription ID
 * @returns {Promise<void>}
 * @throws {Error} When database update fails
 */
stripeService.paymentFailed = async (paymentIntent) => {
    let transaction;
    const stripe = await getStripe('secret_key');

    transaction = await sequelize.transaction();
    try {
        let metadata = {};
        let subscriptionId = null;

        // Recurring payment (subscription)
        if (paymentIntent.invoice) {
            const invoice = await stripe.invoices.retrieve(paymentIntent.invoice, {
                expand: ["subscription"],
            });

            if (invoice.subscription) {
                subscriptionId = invoice.subscription.id;
                metadata = invoice.subscription.metadata || {};
            }
        } else {
            // One-time payment
            metadata = paymentIntent.metadata || {};
        }

        // Find speaker
        let speakerId = metadata.speaker_id;
        if (!speakerId && paymentIntent.customer) {
            const speaker = await Speakers.findOne({
                where: { stripe_customer_id: paymentIntent.customer },
                transaction,
            });
            if (speaker) speakerId = speaker.id;
        }

        // Save failed payment record
        await Subscriptions.create(
            {
                invoice_id: paymentIntent.id,
                speaker_id: speakerId || null,
                plan_id: metadata.plan_id || null,
                stripe_subscription_id: subscriptionId,
                status: "failed",
                start_date: null,
                end_date: null,
                amount: paymentIntent.amount / 100,
                currency: paymentIntent.currency || "usd",
                charge_id: paymentIntent.latest_charge || null,
                created_at: new Date(),
                updated_at: new Date(),
            },
            { transaction }
        );

        // Update speaker status
        if (speakerId) {
            await Speakers.update(
                { stripe_customer_id: paymentIntent.customer || null, status: "payment_failed" },
                { where: { id: speakerId }, transaction }
            );
        }

        await transaction.commit();
    } catch (err) {
        if (transaction) await transaction.rollback();
        console.error("Error handling failed payment:", err);
        throw err;
    }
};

// for handling one-time payments   
stripeService.handleCheckoutSession = async (session) => {
    let transaction;
    try {
        // one-time payment
        if (session.mode === 'payment') {

            const stripeClient = await getStripe();

            const metadata = session.metadata || {};
            const speakerId = metadata.speaker_id;
            const referralCode = metadata.referral_code;
            let stripeCustomerId = session.customer || null;

            const speaker = await Speakers.findByPk(speakerId);
            if (!speaker) {
                throw new Error(`SpeakerId not found`);
            }

            if (!stripeCustomerId && session.customer_email) {
                const customer = await stripeClient.customers.create({
                    email: session.customer_email,
                    name: metadata.speaker_name || speaker.name,
                    metadata: {
                        speaker_id: speakerId,
                        plan_id: metadata.plan_id,
                        referral_code: referralCode || ""
                    }
                });
                stripeCustomerId = customer.id;
            }

            transaction = await sequelize.transaction();

            try {
                if (!speaker.is_customer && referralCode) {
                    await AffiliateUsersDetails.update(
                        { conversion_count: Sequelize.literal('conversion_count + 1') },
                        { where: { referral_code: referralCode }, transaction }
                    );
                }

                await Subscriptions.create({
                    invoice_id: session.id,
                    speaker_id: speakerId,
                    plan_id: metadata.plan_id,
                    stripe_subscription_id: null,
                    status: 'active',
                    start_date: new Date(),
                    currency: session.currency || 'usd',
                    end_date: null,
                    amount: session.amount_total !== undefined ? session.amount_total / 100 : null,
                    invoice_url: null,
                    created_at: new Date(),
                    updated_at: new Date()
                }, { transaction });

                await Speakers.update(
                    { status: 'active', stripe_customer_id: stripeCustomerId, is_customer: 1 },
                    { where: { id: speakerId }, transaction }
                );

                await transaction.commit();
            } catch (dbErr) {
                if (transaction) await transaction.rollback();
                console.error('DB error in handleCheckoutSession transaction:', dbErr);
                throw dbErr;
            }
        }

    } catch (err) {
        console.error("Error handling checkout.session.completed (one-time):", err);
        throw err;
    }
}

stripeService.cancelSubscription = async (payload) => {
    let transaction;

    try {
        const { subscription_id, speaker_id, plan_id } = payload;
        if (!subscription_id) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Subscription id is required");

        const stripe = await getStripe('secret_key');
        const deleted = await stripe.subscriptions.del(subscription_id);

        if (deleted && deleted.status === 'canceled') {
            transaction = await sequelize.transaction();

            try {
                await Speakers.update(
                    { status: 'cancelled', stripe_subscription_id: null, updated_at: new Date() },
                    { where: { stripe_subscription_id: subscription_id }, transaction });

                await Subscriptions.create({ status: 'cancelled', speaker_id, plan_id, stripe_subscription_id: subscription_id, amount: 0, created_at: new Date(), updated_at: new Date() }, { transaction });

                await transaction.commit();
                return { status: true, message: "Subscription cancelled successfully" };

            } catch (dbErr) {
                if (transaction) await transaction.rollback();
                console.error('DB error in cancelSubscription transaction:', dbErr);
                throw dbErr;
            }
        } else {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Failed to cancel subscription");
        }
    } catch (err) {
        if (transaction) await transaction.rollback();
        console.error(err);
        throw err;
    }
};

stripeService.updateSubscription = async (payload) => {
    try{
        const { subscription_id, new_price_id, plan_id } = payload;
        if (!subscription_id || !new_price_id) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Subscription id and new price id are required");

        const stripe = await getStripe('secret_key');
        const subscription = await stripe.subscriptions.retrieve(subscription_id);

        if (!subscription) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Subscription not found");

        const updatedSubscription = await stripe.subscriptions.update(subscription_id, {
            items: [{
                id: subscription.items.data[0].id,
                price: new_price_id,
            }],
            metadata: { plan_id: plan_id },
            proration_behavior: 'create_prorations',
        });

        if (updatedSubscription) {
            return { status: true, message: "Subscription updated successfully", data: updatedSubscription };
        } else {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Failed to update subscription");
        }
    } catch (error){
        console.error(error);
        throw error;
    }
}


module.exports = stripeService;
