import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { User, Upload, Settings, Tag, FileText, Image, Trash2, Download, Plus, X } from 'lucide-react';

export function ProfileManagement() {
  const [personalInfo, setPersonalInfo] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    city: 'San Francisco',
    state: 'California',
    country: 'United States',
    company: 'TechCorp Inc.',
    jobTitle: 'Senior Software Engineer',
    linkedin: 'https://linkedin.com/in/alex<PERSON><PERSON><PERSON>',
    bio: 'Passionate software engineer with 8+ years of experience in full-stack development, cloud architecture, and team leadership.'
  });

  const [assets, setAssets] = useState([
    { id: 1, name: 'Speaker_OnePager_2024.pdf', type: 'pdf', size: '1.2 MB', uploadDate: '2024-01-15' },
    { id: 2, name: 'Professional_Headshot.jpg', type: 'image', size: '856 KB', uploadDate: '2024-01-10' },
    { id: 3, name: 'Speaking_Bio_Extended.pdf', type: 'pdf', size: '743 KB', uploadDate: '2024-01-05' }
  ]);

  const [preferences, setPreferences] = useState({
    topics: ['JavaScript', 'React', 'Node.js', 'Cloud Computing', 'DevOps', 'AI/ML'],
    industries: ['Technology', 'FinTech', 'Healthcare Tech', 'EdTech'],
    regions: ['North America', 'Europe', 'Asia Pacific'],
    travelWillingness: 'domestic-international',
    speakingTypes: ['Keynote', 'Technical Talk', 'Workshop', 'Panel Discussion']
  });

  const [newTag, setNewTag] = useState('');

  const handlePersonalInfoChange = (field: string, value: string) => {
    setPersonalInfo({ ...personalInfo, [field]: value });
  };

  const handleSaveProfile = () => {
    // Save to localStorage for now
    localStorage.setItem('speakerProfile', JSON.stringify(personalInfo));
    console.log('Profile saved:', personalInfo);
  };

  const handleDeleteAsset = (id: number) => {
    setAssets(assets.filter(asset => asset.id !== id));
  };

  const handleAddTag = (category: keyof typeof preferences, tag: string) => {
    const currentTags = preferences[category] as string[];
    if (tag.trim() && !currentTags.includes(tag.trim())) {
      setPreferences({
        ...preferences,
        [category]: [...currentTags, tag.trim()]
      });
      setNewTag('');
    }
  };

  const handleRemoveTag = (category: keyof typeof preferences, tag: string) => {
    const currentTags = preferences[category] as string[];
    setPreferences({
      ...preferences,
      [category]: currentTags.filter(t => t !== tag)
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Profile Management</h2>
          <p className="text-foreground-muted mt-1">Manage your speaker profile, assets, and preferences</p>
        </div>
        <Button onClick={handleSaveProfile} className="bg-primary hover:bg-primary-hover">
          Save Profile
        </Button>
      </div>

      <Tabs defaultValue="personal" className="w-full">
        <TabsList className="grid grid-cols-3 w-full max-w-lg bg-surface-elevated">
          <TabsTrigger value="personal" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Personal Info
          </TabsTrigger>
          <TabsTrigger value="assets" className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            Speaker Assets
          </TabsTrigger>
          <TabsTrigger value="preferences" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Preferences
          </TabsTrigger>
        </TabsList>

        {/* Personal Information Tab */}
        <TabsContent value="personal" className="space-y-6">
          <Card className="bg-surface border-border-subtle">
            <CardHeader>
              <CardTitle className="text-lg">Personal Information</CardTitle>
              <CardDescription>
                Update your basic contact and professional information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="name">Full Name</Label>
                  <Input
                    id="name"
                    value={personalInfo.name}
                    onChange={(e) => handlePersonalInfoChange('name', e.target.value)}
                    className="bg-surface-elevated border-border focus:border-primary"
                  />
                </div>
                <div>
                  <Label htmlFor="email">Email Address</Label>
                  <Input
                    id="email"
                    type="email"
                    value={personalInfo.email}
                    onChange={(e) => handlePersonalInfoChange('email', e.target.value)}
                    className="bg-surface-elevated border-border focus:border-primary"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="phone">Phone Number</Label>
                  <Input
                    id="phone"
                    value={personalInfo.phone}
                    onChange={(e) => handlePersonalInfoChange('phone', e.target.value)}
                    className="bg-surface-elevated border-border focus:border-primary"
                  />
                </div>
                <div>
                  <Label htmlFor="linkedin">LinkedIn URL</Label>
                  <Input
                    id="linkedin"
                    value={personalInfo.linkedin}
                    onChange={(e) => handlePersonalInfoChange('linkedin', e.target.value)}
                    className="bg-surface-elevated border-border focus:border-primary"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="city">City</Label>
                  <Input
                    id="city"
                    value={personalInfo.city}
                    onChange={(e) => handlePersonalInfoChange('city', e.target.value)}
                    className="bg-surface-elevated border-border focus:border-primary"
                  />
                </div>
                <div>
                  <Label htmlFor="state">State</Label>
                  <Input
                    id="state"
                    value={personalInfo.state}
                    onChange={(e) => handlePersonalInfoChange('state', e.target.value)}
                    className="bg-surface-elevated border-border focus:border-primary"
                  />
                </div>
                <div>
                  <Label htmlFor="country">Country</Label>
                  <Input
                    id="country"
                    value={personalInfo.country}
                    onChange={(e) => handlePersonalInfoChange('country', e.target.value)}
                    className="bg-surface-elevated border-border focus:border-primary"
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="company">Company</Label>
                  <Input
                    id="company"
                    value={personalInfo.company}
                    onChange={(e) => handlePersonalInfoChange('company', e.target.value)}
                    className="bg-surface-elevated border-border focus:border-primary"
                  />
                </div>
                <div>
                  <Label htmlFor="jobTitle">Job Title</Label>
                  <Input
                    id="jobTitle"
                    value={personalInfo.jobTitle}
                    onChange={(e) => handlePersonalInfoChange('jobTitle', e.target.value)}
                    className="bg-surface-elevated border-border focus:border-primary"
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="bio">Professional Bio</Label>
                <Textarea
                  id="bio"
                  value={personalInfo.bio}
                  onChange={(e) => handlePersonalInfoChange('bio', e.target.value)}
                  className="bg-surface-elevated border-border focus:border-primary min-h-[120px]"
                />
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Speaker Assets Tab */}
        <TabsContent value="assets" className="space-y-6">
          <Card className="bg-surface border-border-subtle">
            <CardHeader>
              <CardTitle className="text-lg">Speaker Assets</CardTitle>
              <CardDescription>
                Manage your speaking materials, one-pagers, and professional photos
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Upload Area */}
              <div className="border-2 border-dashed border-border rounded-lg p-6 text-center bg-surface-elevated">
                <Upload className="h-12 w-12 text-foreground-muted mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">Upload Speaker Assets</h3>
                <p className="text-foreground-muted mb-4">
                  Drag and drop files here, or click to browse
                </p>
                <Button className="bg-primary hover:bg-primary-hover">
                  <Plus className="h-4 w-4 mr-2" />
                  Choose Files
                </Button>
                <p className="text-sm text-foreground-muted mt-2">
                  PDF, DOC, DOCX, JPG, PNG up to 10MB
                </p>
              </div>

              <Separator />

              {/* Existing Assets */}
              <div className="space-y-3">
                <h4 className="font-medium text-foreground">Uploaded Assets</h4>
                {assets.map((asset) => (
                  <div key={asset.id} className="flex items-center justify-between p-3 bg-surface-elevated rounded-lg border border-border-subtle">
                    <div className="flex items-center gap-3">
                      {asset.type === 'pdf' ? (
                        <FileText className="h-8 w-8 text-destructive" />
                      ) : (
                        <Image className="h-8 w-8 text-primary" />
                      )}
                      <div>
                        <p className="text-sm font-medium text-foreground">{asset.name}</p>
                        <p className="text-xs text-foreground-muted">
                          {asset.size} • Uploaded {new Date(asset.uploadDate).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Button size="sm" variant="outline">
                        <Download className="h-3 w-3" />
                      </Button>
                      <Button size="sm" variant="outline" onClick={() => handleDeleteAsset(asset.id)}>
                        <Trash2 className="h-3 w-3 text-destructive" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Preferences Tab */}
        <TabsContent value="preferences" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Topics */}
            <Card className="bg-surface border-border-subtle">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg flex items-center gap-2">
                  <Tag className="h-5 w-5 text-primary" />
                  Topics & Expertise
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex flex-wrap gap-2">
                  {preferences.topics.map((topic) => (
                    <Badge key={topic} variant="secondary" className="flex items-center gap-1">
                      {topic}
                      <X 
                        className="h-3 w-3 cursor-pointer hover:text-destructive" 
                        onClick={() => handleRemoveTag('topics', topic)}
                      />
                    </Badge>
                  ))}
                </div>
                <div className="flex gap-2">
                  <Input
                    placeholder="Add topic..."
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    onKeyPress={(e) => {
                      if (e.key === 'Enter') {
                        handleAddTag('topics', newTag);
                      }
                    }}
                    className="bg-surface-elevated border-border"
                  />
                  <Button 
                    size="sm" 
                    onClick={() => handleAddTag('topics', newTag)}
                    className="bg-primary hover:bg-primary-hover"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Industries */}
            <Card className="bg-surface border-border-subtle">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg">Industries</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex flex-wrap gap-2">
                  {preferences.industries.map((industry) => (
                    <Badge key={industry} variant="secondary" className="flex items-center gap-1">
                      {industry}
                      <X 
                        className="h-3 w-3 cursor-pointer hover:text-destructive" 
                        onClick={() => handleRemoveTag('industries', industry)}
                      />
                    </Badge>
                  ))}
                </div>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleAddTag('industries', 'New Industry')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Industry
                </Button>
              </CardContent>
            </Card>

            {/* Regions */}
            <Card className="bg-surface border-border-subtle">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg">Preferred Regions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex flex-wrap gap-2">
                  {preferences.regions.map((region) => (
                    <Badge key={region} variant="secondary" className="flex items-center gap-1">
                      {region}
                      <X 
                        className="h-3 w-3 cursor-pointer hover:text-destructive" 
                        onClick={() => handleRemoveTag('regions', region)}
                      />
                    </Badge>
                  ))}
                </div>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleAddTag('regions', 'New Region')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Region
                </Button>
              </CardContent>
            </Card>

            {/* Speaking Types */}
            <Card className="bg-surface border-border-subtle">
              <CardHeader className="pb-4">
                <CardTitle className="text-lg">Speaking Types</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex flex-wrap gap-2">
                  {preferences.speakingTypes.map((type) => (
                    <Badge key={type} variant="secondary" className="flex items-center gap-1">
                      {type}
                      <X 
                        className="h-3 w-3 cursor-pointer hover:text-destructive" 
                        onClick={() => handleRemoveTag('speakingTypes', type)}
                      />
                    </Badge>
                  ))}
                </div>
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => handleAddTag('speakingTypes', 'New Type')}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Type
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
