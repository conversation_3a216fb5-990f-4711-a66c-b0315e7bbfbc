"""
Affiliate Scraper Module
Optimized affiliate scraping functionality organized into specialized modules

This module provides comprehensive web scraping capabilities for extracting
affiliate program information from websites. It includes robust error handling,
retry mechanisms, and multiple extraction strategies for different types of data.

Modules:
- database: Database operations for affiliate data
- extractors: Specialized data extraction functions
- page_loader: Web page loading and parsing utilities
- utils: Common utility functions
- constants: Configuration constants

Author: digitalspeakeragent.ai
Version: 2.0.0
"""

from .database import AffiliateDatabase
from .extractors import AffiliateExtractors
from .page_loader import AffiliatePageLoader
from .utils import AffiliateUtils

__all__ = [
    'AffiliateDatabase',
    'AffiliateExtractors', 
    'AffiliatePageLoader',
    'AffiliateUtils'
]
