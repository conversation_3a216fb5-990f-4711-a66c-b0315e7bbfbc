const userModel = require("../models/schema/users")
const roleModel = require("../models/schema/roles")
const permissionsModel = require("../models/schema/permissions");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES } = require("../helpers/response-codes");

module.exports = function (requiredPermissions = []) {
  return async (req, res, next) => {
    try {
      const user = await userModel.findByPk(1, {
        include: [
          {
            model: roleModel,
            as: 'role',
             include: [{ model: permissionsModel, as: 'permissions', attributes: ['name'], through: { attributes: [] } }]
          }
        ]
      });

      if (!user) return  next(new CustomError(RESPONSE_CODES.NOT_FOUND, "User not found"));

      const userPermissions = user.role.permissions.map(p => p.name);

      const hasPermission = requiredPermissions.every(p => userPermissions.includes(p));

      if (!hasPermission) return  next(new CustomError(RESPONSE_CODES.ACCESS_NOT, "Access denied"));

      next();
    } catch (err) {
      next(err);
    }
  };
};
