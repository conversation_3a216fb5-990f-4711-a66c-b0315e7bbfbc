const { DataTypes } = require("sequelize");
const connection = require("../connection");


const SpeakerOpportunity = connection.define("SpeakerOpportunity", {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    comment: 'Primary key for the speaker opportunity record',
  },
  speaker_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Foreign key referencing the speaker',
  },
  opportunity_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Foreign key referencing the opportunity',
  },
  speaker_email: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: 'Email address of the speaker',
  },
  overall_score: {
    type: DataTypes.FLOAT,
    allowNull: true,
    comment: 'Overall match score for the speaker and opportunity',
  },
  topic_score: {
    type: DataTypes.FLOAT,
    allowNull: true,
    comment: 'Score for topic relevance',
  },
  primary_category_score: {
    type: DataTypes.FLOAT,
    allowNull: true,
    comment: 'Score for primary category match',
  },
  subcategory_score: {
    type: DataTypes.FLOAT,
    allowNull: true,
    comment: 'Score for secondary category match',
  },
  geo_location_score: {
    type: DataTypes.FLOAT,
    allowNull: true,
    comment: 'Score for geographic/location match',
  },
  match_strength: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Strengths in the speaker-opportunity match',
  },
  match_concern: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Concerns in the speaker-opportunity match',
  },
  recommendation: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Recommendation for the speaker regarding the opportunity',
  },
  application_submitted: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Indicates if the application has been submitted',
  },
  match_considerations: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Considerations for the match',
  },
  ai_match: {
    type: DataTypes.BOOLEAN,
    defaultValue: false,
    comment: 'Indicates if AI matching was used',
  },
  speaker_dsa_url: {
    type: DataTypes.STRING,
    allowNull: true,
    comment: "URL for the speaker's DSA profile"
  },
  speaker_prefill_data: {
    type: DataTypes.JSON,
    comment: "Pre-filled data for the speaker's application"
  },
  status: {
    type: DataTypes.ENUM('pending', 'accepted', 'rejected', 'interested', 'applied', 'booked', 'interviewing'),
    defaultValue: 'pending',
    comment: 'Status of the speaker opportunity',
  },
  reasons: {
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Reasons for the current status',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Record creation timestamp',
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Record last update timestamp',
  },
  deleted_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Record deletion timestamp(soft delete)',
  }
}, {
  tableName: 'speaker_opportunities',
  timestamps: true,
  createdAt: "created_at",
  updatedAt: "updated_at",
  paranoid: true,
  deletedAt: "deleted_at"
});

module.exports = SpeakerOpportunity;
