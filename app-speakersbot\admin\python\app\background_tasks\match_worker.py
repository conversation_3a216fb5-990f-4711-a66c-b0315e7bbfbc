"""
Optimized Match Worker - Background Processing for Speaker Matching
================================================================

This module provides optimized background processing for speaker matching operations,
combining vector similarity search, rule-based scoring, and manual analysis.

Key Features:
- Optimized hybrid scoring with 40/30/20/10 weight distribution
- Efficient batch processing for multiple opportunities
- Robust error handling and fallback mechanisms
- Performance monitoring and logging
- Clean separation of concerns

Author: Speaker Bot Team  
Version: 2.0 (Optimized)
Last Updated: 2024
"""

import logging
import time
from datetime import datetime, date
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass

from app.helpers.database_manager import db_manager

from app.config.config import config
from app.models.speakers import Speaker
from app.models.speaker_opportunities import SpeakerOpportunity
from app.models.opportunities import SpeakerOpportunity as Opportunity
from app.models.speaker_details import SpeakerDetails
from app.services.speaker_matching_service import matching_service
from app.helpers.matching_helpers import (
    calculate_primary_category_score,
    calculate_subcategory_score,
    calculate_geo_location_score,
    calculate_topic_score,
    generate_match_reasons,
    generate_match_strengths,
    generate_match_concerns,
    generate_recommendation,
    generate_match_considerations
)

# Configure structured logging
logger = logging.getLogger(__name__)

@dataclass
class WorkerConfig:
    """Configuration constants for match worker operations."""
    MIN_SCORE_THRESHOLD: float = 0.58
    BATCH_PROCESSING_SIZE: int = 50
    MAX_RETRIES: int = 3
    RETRY_DELAY: float = 1.0
    
    # Scoring weights (40/30/20/10 distribution)
    PRIMARY_CATEGORY_WEIGHT: float = 0.40
    SUBCATEGORY_WEIGHT: float = 0.30
    GEO_LOCATION_WEIGHT: float = 0.20
    TOPIC_WEIGHT: float = 0.10

@dataclass
class WorkerStats:
    """Statistics tracking for match worker operations."""
    total_processing_time: float = 0.0
    opportunities_processed: int = 0
    matches_created: int = 0
    llm_enhanced: int = 0
    errors_encountered: int = 0

class OptimizedMatchWorker:
    """
    Optimized background worker for processing speaker matches.
    
    This worker orchestrates the complete matching process:
    1. Speaker data validation from MySQL
    2. Get all future opportunities from MySQL database
    3. Hybrid scoring calculation for all opportunities
    4. Manual analysis for detailed matching
    5. Database persistence with error handling
    """
    
    def __init__(self):
        """Initialize the match worker with optimized database connections."""
        self.config = WorkerConfig()
        self.stats = WorkerStats()
        # Use centralized database manager instead of creating new engine
        self.SessionLocal = db_manager.SessionLocal
        
        logger.info("OptimizedMatchWorker initialized with centralized database manager")

    def process_speaker_matching(self, speaker_id: int, batch_size: int = 50) -> Dict[str, Any]:
        """
        Process comprehensive speaker matching with batch processing.
        Args:
            speaker_id: Unique identifier for the speaker
            batch_size: Number of opportunities to process per batch
        Returns:
            Dictionary containing matching results and statistics
        """
        start_time = time.time()
        try:
            # Step 1: Validate speaker existence
            speaker_data = self._validate_and_prepare_speaker(speaker_id)
            if not speaker_data:
                return self._create_error_response("Speaker validation failed")
            
            # Step 2: Check opportunities availability in MySQL
            if not matching_service.check_opportunities_availability():
                return self._create_error_response("No future opportunities available in MySQL")
            
            # Step 3: Process opportunities in batches
            all_matches = []
            offset = 0
            batch_number = 1
            
            while True:
                logger.info(f"Processing batch {batch_number} for speaker {speaker_id} (offset={offset})")
                
                # Get batch of opportunities
                opportunities = matching_service.get_all_future_opportunities(speaker_id, limit=batch_size, offset=offset)
                
                if not opportunities:
                    logger.info(f"No more opportunities found at offset {offset}")
                    break
                
                # Calculate hybrid scores for this batch
                batch_matches = self._calculate_hybrid_scores_batch(speaker_data, opportunities)
                
                if batch_matches:
                    all_matches.extend(batch_matches)
                    # Save batch immediately to reduce memory usage
                    matching_service.save_matches_to_database(batch_matches)
                    logger.info(f"Batch {batch_number}: Processed {len(batch_matches)} matches")
                
                # Move to next batch
                offset += batch_size
                batch_number += 1
                
                # Check if we have more opportunities
                total_count = matching_service.get_opportunities_count()
                if offset >= total_count:
                    break
            
            processing_time = time.time() - start_time
            
            return {
                "success": True,
                "message": f"Successfully processed {len(all_matches)} matches for speaker {speaker_id} in {processing_time:.2f}s",
                "total_matches": len(all_matches),
                "processing_time": processing_time,
                "batches_processed": batch_number - 1,
            }
            
        except Exception as e:
            self.stats.errors_encountered += 1
            processing_time = time.time() - start_time
            return {
                "success": False,
                "error": f"Match processing failed after {processing_time:.2f}s: {str(e)}"
            }

    def _validate_and_prepare_speaker(self, speaker_id: int) -> Optional[Dict[str, Any]]:
        """
        Validate speaker existence and prepare comprehensive data.
        
        Args:
            speaker_id: Unique identifier for the speaker
            
        Returns:
            Speaker data dictionary or None if validation fails
        """
        try:
            speaker_data, error = matching_service.get_speaker_data(speaker_id)
            if error:
                logger.error(f"Speaker validation failed: {error}")
                return None
                
            if not speaker_data:
                logger.error(f"No speaker data returned for ID {speaker_id}")
                return None
                
            return speaker_data
            
        except Exception as e:
            logger.error(f"Speaker validation error: {e}")
            return None

    def _calculate_hybrid_scores_batch(self, speaker_data: Dict[str, Any], 
                                    opportunities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Calculate hybrid scores for opportunities with optimized batch processing.
        Args:
            speaker_data: Comprehensive speaker information
            opportunities: List of opportunities from MySQL database
        Returns:
            List of match dictionaries with calculated scores
        """
        start_time = time.time()
        all_matches = []
                
        try:
            # Process opportunities individually for scoring with smart optimization
            for i, opportunity in enumerate(opportunities):
                try:
                    # Calculate individual scores
                    primary_category_score = calculate_primary_category_score(speaker_data, opportunity)
                    subcategory_score = calculate_subcategory_score(speaker_data, opportunity)
                    geo_location_score = calculate_geo_location_score(speaker_data, opportunity)
                    topic_score = calculate_topic_score(speaker_data, opportunity)
                    
                    # Calculate weighted overall score
                    overall_score = (
                        primary_category_score * self.config.PRIMARY_CATEGORY_WEIGHT +
                        subcategory_score * self.config.SUBCATEGORY_WEIGHT +
                        geo_location_score * self.config.GEO_LOCATION_WEIGHT +
                        topic_score * self.config.TOPIC_WEIGHT
                    )
                    
                    # Scale to 1-10 range
                    overall_score = round(overall_score * 10, 2)
                    
                    # Create match record
                    match = {
                        "speaker_id": speaker_data["speaker_id"],
                        "opportunity_id": opportunity["opportunity_id"],
                        "speaker_email": speaker_data.get("email", ""),
                        "overall_score": overall_score,
                        "topic_score": round(topic_score * 10, 2),
                        "primary_category_score": round(primary_category_score * 10, 2),
                        "subcategory_score": round(subcategory_score * 10, 2),
                        "geo_location_score": round(geo_location_score * 10, 2),
                        "match_strength": generate_match_strengths(topic_score, primary_category_score, subcategory_score, geo_location_score),
                        "match_concern": generate_match_concerns(topic_score, primary_category_score, subcategory_score, geo_location_score),
                        "recommendation": generate_recommendation(overall_score, "Matched"),
                        "match_considerations": generate_match_considerations(opportunity)
                    }
                    
                    all_matches.append(match)
                    
                except Exception as e:
                    logger.error(f"Error processing opportunity {i}: {e}")
                    continue
            
            processing_time = time.time() - start_time
            logger.info(f"Hybrid scoring completed in {processing_time:.2f}s - Processed {len(all_matches)} matches")
            
            return all_matches
            
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Hybrid scoring failed after {processing_time:.2f}s: {e}")
            return []
    

    def _prepare_opportunity_data(self, opp: Dict[str, Any]) -> Dict[str, Any]:
        """
        Prepare opportunity data for scoring.
        Args:
            opp: Opportunity dictionary from MySQL database
        Returns:
            Formatted opportunity data for scoring
        """
        return {
            "id": opp["opportunity_id"],
            "title": opp.get("title", ""),
            "description": opp.get("description", ""),
            "subcategory": opp.get("subcategory", ""),
            "organization": opp.get("organization", ""),
            "event_type": opp.get("event_type", ""),
            "industry": opp.get("industry", ""),
            "city": opp.get("city", ""),
            "state": opp.get("state", ""),
            "country": opp.get("country", ""),
            "venue": opp.get("venue", ""),
            "is_virtual": opp.get("is_virtual", False),
            "start_date": opp.get("start_date"),
            "end_date": opp.get("end_date", ""),
            "vector_similarity_score": 0.0  # No vector similarity anymore
        }

    def _calculate_hybrid_scores(self, speaker_data: Dict[str, Any], 
                               opportunity_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate hybrid scores using optimized algorithm.
        Args:
            speaker_data: Comprehensive speaker information
            opportunity_data: Opportunity information for scoring
        Returns:
            Dictionary containing all calculated scores and metadata
        """
        # No vector similarity score needed - using MySQL only
        # Calculate individual category scores
        primary_category_score = calculate_primary_category_score(
            speaker_data, opportunity_data
        )
        subcategory_score = calculate_subcategory_score(
            speaker_data, opportunity_data
        )
        geo_location_score = calculate_geo_location_score(
            speaker_data, opportunity_data
        )
        topic_score = calculate_topic_score(
            speaker_data, opportunity_data
        )
        # Calculate weighted overall score
        overall_score = (
            self.config.PRIMARY_CATEGORY_WEIGHT * primary_category_score +
            self.config.SUBCATEGORY_WEIGHT * subcategory_score +
            self.config.GEO_LOCATION_WEIGHT * geo_location_score +
            self.config.TOPIC_WEIGHT * topic_score
        )
        # Round overall score to 2 decimal places
        overall_score = round(overall_score, 3)
        
        # Convert scores from 0-1 range to 0-10 range by multiplying by 10
        scaled_scores = {
            'overall_score': round(overall_score * 10, 2),
            'primary_category_score': round(primary_category_score * 10, 2),
            'subcategory_score': round(subcategory_score * 10, 2),
            'geo_location_score': round(geo_location_score * 10, 2),
            'topic_score': round(topic_score * 10, 2)
        }
        # Determine match status
        status = "Matched" if overall_score >= self.config.MIN_SCORE_THRESHOLD else "Not Matched"
        
        # Generate analysis fields
        analysis_fields = self._generate_analysis_fields(
            topic_score, primary_category_score, subcategory_score, 
            geo_location_score, overall_score, opportunity_data
        )
        
        return {
            **scaled_scores,
            'vector_similarity_score': 0.0,  # No vector similarity - MySQL only
            'status': status,
            'application_submitted': False,
            'ai_match': False,  # Manual scoring only - no LLM enhancement
            **analysis_fields
        }

# _validate_vector_score method removed - no longer needed with MySQL-only approach


    def _generate_analysis_fields(self, topic_score: float, primary_category_score: float,
                                subcategory_score: float, geo_location_score: float,
                                overall_score: float, opportunity_data: Dict[str, Any]) -> Dict[str, str]:
        """Generate analysis fields for the match."""
            
        return {
            'reasons': generate_match_reasons(
                topic_score, primary_category_score, subcategory_score,
                geo_location_score, overall_score
            ),
            'match_strength': generate_match_strengths(
                topic_score, primary_category_score, subcategory_score, geo_location_score
            ),
            'match_concern': generate_match_concerns(
                topic_score, primary_category_score, subcategory_score, geo_location_score
            ),
            'recommendation': generate_recommendation(overall_score, 
                "Matched" if overall_score >= self.config.MIN_SCORE_THRESHOLD else "Not Matched"),
            'match_consideration': generate_match_considerations(opportunity_data)
        }
    

    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error response."""
        return {
            "success": False,
            "error": error_message,
            "matches": [],
        }


# Create singleton instance for global access
match_worker = OptimizedMatchWorker()