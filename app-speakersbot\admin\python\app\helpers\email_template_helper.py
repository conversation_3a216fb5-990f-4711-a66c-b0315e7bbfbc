from typing import Dict, Any

def create_speaker_email_template(speaker_data: Dict[str, Any], opportunity_data: Dict[str, Any]) -> Dict[str, str]:
    """
    Create a natural-looking email template for speaker opportunity application.
    
    Args:
        speaker_data: Dictionary containing speaker information
        opportunity_data: Dictionary containing opportunity information
    
    Returns:
        Dictionary with email template data
    """
    # Build location string naturally
    location_parts = []
    if speaker_data.get('city'):
        location_parts.append(speaker_data['city'])
    if speaker_data.get('state'):
        location_parts.append(speaker_data['state'])
    if speaker_data.get('country'):
        location_parts.append(speaker_data['country'])
    location = ', '.join(location_parts) if location_parts else 'Not specified'
    
    # Build contact info naturally
    contact_info = []
    if speaker_data.get('phone_number'):
        contact_info.append(speaker_data['phone_number'])
    if speaker_data.get('linkedin'):
        contact_info.append(f"LinkedIn: {speaker_data['linkedin']}")
    if speaker_data.get('speaker_website'):
        contact_info.append(f"Website: {speaker_data['speaker_website']}")
    
    contact_string = '\n'.join(contact_info) if contact_info else ''
    
    # Extract professional title/role
    professional_role = speaker_data.get('company', '')
    if speaker_data.get('title'):
        professional_role = f"{speaker_data['title']} at {professional_role}" if professional_role else speaker_data['title']
    
    # Build expertise section
    expertise_parts = []
    if speaker_data.get('primary_category'):
        expertise_parts.append(speaker_data['primary_category'])
    if speaker_data.get('subcategory'):
        expertise_parts.append(speaker_data['subcategory'])
    if speaker_data.get('topic'):
        expertise_parts.append(speaker_data['topic'])
    
    expertise_text = ', '.join(expertise_parts) if expertise_parts else 'my area of expertise'
    
    # Build bio section with fallbacks
    bio_text = speaker_data.get('bio', '')
    if not bio_text and speaker_data.get('speaker_credentials'):
        bio_text = speaker_data['speaker_credentials']
    if not bio_text:
        bio_text = f"I have extensive experience in {expertise_text} and am passionate about sharing knowledge with others."
    
    # Build differentiators section
    differentiators_text = speaker_data.get('differentiators', '')
    if not differentiators_text and speaker_data.get('top_keywords'):
        differentiators_text = f"My expertise spans {speaker_data['top_keywords']}"
    if not differentiators_text:
        differentiators_text = 'my unique perspective and extensive experience in the field'
    
    # Build learning objectives section
    learning_objectives_text = speaker_data.get('learning_objectives', '')
    if not learning_objectives_text and speaker_data.get('takeaways'):
        learning_objectives_text = speaker_data['takeaways']
    if not learning_objectives_text:
        learning_objectives_text = 'key concepts and practical applications that drive real-world impact'
    
    # Build challenges section if available
    challenges_text = ''
    if speaker_data.get('challenges'):
        challenges_text = f"\n\nI understand the challenges organizations face with {speaker_data['challenges']} and can provide practical solutions and insights."
    
    # Build availability section
    availability_text = ''
    if speaker_data.get('preferred_speaker_geography'):
        availability_text = f" I am particularly interested in {speaker_data['preferred_speaker_geography']} engagements."
    
    # Enhanced subject line with value proposition
    event_title = opportunity_data.get('title', 'Speaking Opportunity')
    speaker_name = speaker_data.get('name', 'Speaker')
    subject = f"Speaker Proposal: {speaker_name} | {event_title}"
    
    # Build professional signature
    signature_lines = [speaker_name]
    if professional_role:
        signature_lines.append(professional_role)
    if location != 'Not specified':
        signature_lines.append(f"📍 {location}")
    
    signature_lines.append("")  # Empty line
    
    if speaker_data.get('phone_number'):
        signature_lines.append(f"📞 {speaker_data['phone_number']}")
    if speaker_data.get('email'):
        signature_lines.append(f"✉️ {speaker_data['email']}")
    if speaker_data.get('linkedin'):
        signature_lines.append(f"🔗 LinkedIn: {speaker_data['linkedin']}")
    if speaker_data.get('speaker_website'):
        signature_lines.append(f"🌐 Website: {speaker_data['speaker_website']}")
    
    signature = '\n'.join(signature_lines)
    
    # Enhanced email body with professional structure
    body = f"""Subject: Speaker Proposal for {event_title}

Dear Event Organizer,

I hope this message finds you well. I am writing to express my strong interest in speaking at {event_title} and contributing to what I'm confident will be an exceptional event.

ABOUT ME
I am {speaker_name}, {professional_role} based in {location}. With deep expertise in {expertise_text}, I bring a unique perspective that combines practical experience with actionable insights.

{bio_text}

WHAT I BRING TO YOUR EVENT
• {differentiators_text}
• Proven ability to help audiences understand {learning_objectives_text}
• Engaging presentation style that resonates with diverse audiences
• Real-world experience addressing {challenges_text if challenges_text else 'industry challenges'}

MY VALUE PROPOSITION
Through my presentations, I help audiences:
• Gain practical, actionable insights they can implement immediately
• Understand complex concepts through clear, relatable examples
• Connect theory with real-world applications
• Leave with concrete next steps for their professional development

AVAILABILITY & FLEXIBILITY
I am available for both virtual and in-person engagements and can adapt my presentation style to meet your specific audience needs.{availability_text}

NEXT STEPS
I would welcome the opportunity to discuss how I can contribute to your event's success. I'm available for a brief call to:
• Discuss specific presentation topics that align with your event goals
• Share examples of my previous speaking engagements
• Answer any questions about my expertise and approach

I'm confident that my experience and passion for {expertise_text} would make me a valuable addition to your speaker lineup.

Thank you for considering my application. I look forward to hearing from you soon.

Best regards,

{signature}

---
This email was sent regarding: {event_title}
Event URL: {opportunity_data.get('event_url', 'N/A')}
        """.strip()
    
    template = {
        "from": speaker_data.get("email", ""),
        "to": opportunity_data.get("email", ""),
        "subject": subject,
        "body": body
    }
    
    return template
