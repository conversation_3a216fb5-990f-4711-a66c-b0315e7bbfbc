import { configureStore } from '@reduxjs/toolkit';
import { setupListeners } from '@reduxjs/toolkit/query';
import authReducer from './slices/authSlice';
import appReducer from './slices/appSlice';
import rbacReducer from './slices/rbacSlice';
import { rbacMiddleware } from '../middleware/rbacMiddleware';
import {
  authApi,
  usersApi,
  speakersApi,
  opportunitiesApi,
  affiliatesApi,
  analyticsApi,
  settingsApi,
  scrapingApi,
  pricingPlanApi,
  matchingApi,
  dashboardApi,
  fileUploadApi,
  formApi,
} from '../apis';

export const store = configureStore({
  reducer: {
    // Redux slices
    auth: authReducer,
    app: appReducer,
    rbac: rbacReducer,
    // API slices
    [authApi.reducerPath]: authApi.reducer,
    [usersApi.reducerPath]: usersApi.reducer,
    [speakersApi.reducerPath]: speakersApi.reducer,
    [opportunitiesApi.reducerPath]: opportunitiesApi.reducer,
    [affiliatesApi.reducerPath]: affiliatesApi.reducer,
    [analyticsApi.reducerPath]: analyticsApi.reducer,
    [settingsApi.reducerPath]: settingsApi.reducer,
    [scrapingApi.reducerPath]: scrapingApi.reducer,
    [pricingPlanApi.reducerPath]: pricingPlanApi.reducer,
    [matchingApi.reducerPath]: matchingApi.reducer,
    [dashboardApi.reducerPath]: dashboardApi.reducer,
    [fileUploadApi.reducerPath]: fileUploadApi.reducer,
    [formApi.reducerPath]: formApi.reducer,
  },
  // Adding the api middleware enables caching, invalidation, polling,
  // and other useful features of `rtk-query`.
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }).concat(
      rbacMiddleware,
      authApi.middleware,
      usersApi.middleware,
      speakersApi.middleware,
      opportunitiesApi.middleware,
      affiliatesApi.middleware,
      analyticsApi.middleware,
      settingsApi.middleware,
      scrapingApi.middleware,
      pricingPlanApi.middleware,
      matchingApi.middleware,
      dashboardApi.middleware,
      fileUploadApi.middleware,
      formApi.middleware
    ),
  devTools: process.env.NODE_ENV !== 'production',
});

// optional, but required for refetchOnFocus/refetchOnReconnect behaviors
// see `setupListeners` docs - takes an optional callback as the 2nd arg for customization
setupListeners(store.dispatch);

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
