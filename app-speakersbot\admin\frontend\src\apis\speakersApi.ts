import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../store';

// Define the base URL for your API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';

export const speakersApi = createApi({
  reducerPath: 'speakersApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Get the token from the state
      const token = (getState() as RootState).auth?.token;
      
      // If we have a token, set the authorization header
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      
      // Set content type
      headers.set('content-type', 'application/json');
      
      return headers;
    },
  }),
  tagTypes: ['Speaker'],
  endpoints: (builder) => ({
    getSpeakers: builder.query({
      query: (params = {}) => ({
        url: '/speakers',
        params,
      }),
      providesTags: ['Speaker'],
    }),
    
    getSpeakerById: builder.query({
      query: (id) => `/speaker/${id}`,
      providesTags: (result, error, id) => [{ type: 'Speaker', id }],
    }),
    
    // GET /speaker/opportunity/:id → list opportunities for a speaker
    getSpeakerOpportunities: builder.query({
      query: ({ id, page = 1, limit = 15, filter, status }: { id: string | number; page?: number; limit?: number; filter?: Record<string, any>; status?: string }) => {
        const finalFilter = filter
          ? filter
          : (status && status !== 'all' ? { status } : undefined);
        return {
          url: `/speaker/opportunity/${id}`,
          params: {
            page,
            limit,
            ...(finalFilter ? { filter: JSON.stringify(finalFilter) } : {}),
          },
        };
      },
      providesTags: (result, error, { id }: { id: string | number }) => [{ type: 'Speaker', id }],
    }),

    // GET /speaker/history/:id → credit/points history for a speaker
    getSpeakerHistory: builder.query({
      query: ({ id, page = 1, limit = 10 }: { id: string | number; page?: number; limit?: number }) => ({
        url: `/speaker/history/${id}`,
        params: { page, limit },
      }),
      providesTags: (result, error, { id }: { id: string | number }) => [{ type: 'Speaker', id }],
    }),
    
    createSpeaker: builder.mutation({
      query: (speakerData) => ({
        url: '/speakers',
        method: 'POST',
        body: speakerData,
      }),
      invalidatesTags: ['Speaker'],
    }),
    
    updateSpeaker: builder.mutation({
      query: ({ id, name, email, subscriptionPlan, activityStatus }: { id: string | number; name: string; email: string; subscriptionPlan: string; activityStatus: string }) => ({
        url: `/speaker/${id}`,
        method: 'PUT',
        body: {
          name,
          email,
          subscriptionPlan: subscriptionPlan,
          activeStatus: activityStatus,
        },
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Speaker', id }],
    }),
    
    deleteSpeaker: builder.mutation({
      query: (id) => ({
        url: `/speakers/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Speaker'],
    }),

    // GET /cities → list of cities from speakers
    getCities: builder.query({
      query: () => ({
        // Absolute URL to hit admin cities endpoint directly
        url: '/speakers/cities',
      }),
    }),
  }),
});

export const {
  useGetSpeakersQuery,
  useGetSpeakerByIdQuery,
  useGetSpeakerOpportunitiesQuery,
  useGetSpeakerHistoryQuery,
  useCreateSpeakerMutation,
  useUpdateSpeakerMutation,
  useDeleteSpeakerMutation,
  useGetCitiesQuery,
} = speakersApi;