from app.config.config import config
from sqlalchemy import create_engine, Column, Integer, Text, DateTime, Enum, ForeignKey, JSON
from app.models.base import Base
import datetime as dt

class OpportunityForm(Base):
    __tablename__ = "opportunity_form"
    id = Column(Integer, primary_key=True, autoincrement=True, comment="Primary key for the opportunity form record")
    opportunity_id = Column(Integer, ForeignKey("opportunities.id"), comment="Foreign key referencing the opportunity")
    type = Column(Enum("email", "form"), comment="Type of submission (email or form)")
    form_data = Column(JSON, nullable=False, comment="Form data")
    form_url = Column(Text, nullable=True, comment="URL of the form")
    created_at = Column(DateTime, default=dt.datetime.utcnow, nullable=False, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=dt.datetime.utcnow, nullable=False, comment="Record last update timestamp")

def create_opportunity_form_table():
    db_url = config.get_database_url()
    engine = create_engine(db_url)
    Base.metadata.create_all(engine)

if __name__ == "__main__":
    create_opportunity_form_table()
    print("Table 'opportunity_form' created in MySQL database")
