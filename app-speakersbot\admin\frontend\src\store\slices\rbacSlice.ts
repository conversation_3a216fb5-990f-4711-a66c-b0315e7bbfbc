import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Permission types based on your database structure
export type Permission = 
  | 'read_dashboard'
  | 'read_opportunities'
  | 'write_opportunities'
  | 'read_speakers'
  | 'write_speakers'
  | 'read_matching_queue'
  | 'write_matching_queue'
  | 'read_users'
  | 'write_users'
  | 'read_form_types'
  | 'write_form_types'
  | 'read_scraping_logging'
  | 'write_scraping_topics'
  | 'write_gamification_data'
  | 'write_settings'
  | 'read_pricing_plans'
  | 'read_affiliates'
  | 'write_affiliates'
  | 'read_affiliate_users_details';

export interface Role {
  id: number;
  name: string;
  description: string;
  permissions: Permission[];
}

export interface RBACState {
  roles: Role[];
  userPermissions: Permission[];
  isLoading: boolean;
  error: string | null;
}

const initialState: RBACState = {
  roles: [],
  userPermissions: [],
  isLoading: false,
  error: null,
};

// Role definitions based on your requirements
const ROLE_DEFINITIONS: Record<string, Permission[]> = {
  'Admin': [
    'read_dashboard',
    'read_opportunities',
    'write_opportunities',
    'read_speakers',
    'write_speakers',
    'read_matching_queue',
    'write_matching_queue',
    'read_users',
    'write_users',
    'read_form_types',
    'write_form_types',
    'read_scraping_logging',
    'write_scraping_topics',
    'write_gamification_data',
    'write_settings',
    'read_pricing_plans',
    'read_affiliates',
    'write_affiliates',
    'read_affiliate_users_details',
  ],
  'Affiliate': [
    'read_affiliate_users_details',
  ],
};

const rbacSlice = createSlice({
  name: 'rbac',
  initialState,
  reducers: {
    setUserPermissions: (state, action: PayloadAction<Permission[]>) => {
      state.userPermissions = action.payload;
      state.error = null;
    },
    setRoles: (state, action: PayloadAction<Role[]>) => {
      state.roles = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    // Initialize permissions based on user role
    initializePermissions: (state, action: PayloadAction<{ roleName: string; customPermissions?: Permission[] }>) => {
      const { roleName, customPermissions } = action.payload;
      
      if (customPermissions) {
        state.userPermissions = customPermissions;
      } else {
        state.userPermissions = ROLE_DEFINITIONS[roleName] || [];
      }
      state.error = null;
    },
    // Add permission to user
    addPermission: (state, action: PayloadAction<Permission>) => {
      if (!state.userPermissions.includes(action.payload)) {
        state.userPermissions.push(action.payload);
      }
    },
    // Remove permission from user
    removePermission: (state, action: PayloadAction<Permission>) => {
      state.userPermissions = state.userPermissions.filter(
        permission => permission !== action.payload
      );
    },
    // Clear all permissions (logout)
    clearPermissions: (state) => {
      state.userPermissions = [];
      state.error = null;
    },
  },
});

export const {
  setUserPermissions,
  setRoles,
  setLoading,
  setError,
  clearError,
  initializePermissions,
  addPermission,
  removePermission,
  clearPermissions,
} = rbacSlice.actions;

export default rbacSlice.reducer;
