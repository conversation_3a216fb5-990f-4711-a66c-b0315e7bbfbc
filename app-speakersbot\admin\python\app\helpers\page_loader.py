"""
Page Loader Module for Speaker Opportunity Scraper
 
This module provides comprehensive page loading capabilities with proxy support,
content extraction, and error handling. It handles both HTML and PDF content
with intelligent fallback mechanisms.
 
Key Features:
- Proxy service integration (ScrapeOps)
- PDF content extraction
- HTML parsing and text extraction
- Error handling and fallbacks
- Content size optimization
- User agent spoofing
 
Author: Speaker Bot Team
Version: 2.0 (Optimized)
Last Updated: 2024
"""
 
import io
import re
from typing import Tuple, Optional
from bs4 import BeautifulSoup
from pdfminer.high_level import extract_text as pdf_extract_text
import httpx
 
from .utils import strip_html
 
# =============================================================================
# PAGE LOADING
# =============================================================================
 
async def load_page(url: str, timeout: float = 10.0, retry_count: int = 0) -> <PERSON><PERSON>[str, Optional[BeautifulSoup], str, bool]:
    """
    Load page content with ScrapeOps proxy support and fallback to direct requests.
   
    This function attempts to load page content using direct requests first,
    then falls back to proxy service if the direct request fails. It handles both
    HTML and PDF content with appropriate parsing.
   
    Args:
        url (str): URL to load
        timeout (float): Request timeout in seconds (default: 10.0)
        retry_count (int): Current retry count (0 = first attempt, >0 = retry attempts)
       
    Returns:
        Tuple[str, Optional[BeautifulSoup], str, bool]:
            (html, soup, text, is_pdf)
           
    Example:
        >>> html, soup, text, is_pdf = await load_page("https://example.com")
        >>> print(f"Loaded {len(text)} characters of text")
    """
    try:
        from app.config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    except Exception:
        from config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
   
    # Import proxy service
    from app.services.proxy_service import get_proxied_content, is_proxy_available
   
    try:
        # Determine if we should use proxy (only for retry attempts, not first attempt)
        use_proxy = retry_count > 0 and is_proxy_available()
       
        # Try proxy first if this is a retry attempt
        if use_proxy:
            logger.info(f"Retry attempt {retry_count} for {url} using proxy service")
            try:
                success, content, error = await get_proxied_content(url, timeout)
                if success and content:
                    if url.lower().endswith('.pdf'):
                        text = pdf_extract_text(io.BytesIO(content.encode())) if content else ""
                        logger.info(f"Successfully loaded PDF via proxy on retry {retry_count}")
                        return "", None, (text or "")[:300000], True
                    else:
                        html = content
                        soup = BeautifulSoup(html, 'html.parser')
                        txt = soup.get_text(" ", strip=True)
                        txt = re.sub(r"\s+", " ", txt)
                        logger.info(f"Successfully loaded HTML via proxy on retry {retry_count}")
                        return html, soup, txt[:300000], False
                else:
                    logger.warning(f"Proxy failed for {url} on retry {retry_count}: {error}")
            except Exception as e:
                logger.warning(f"Proxy error for {url} on retry {retry_count}: {e}")
       
        # Try direct request (first attempt or fallback after proxy failure)
        logger.info(f"Attempting direct request for {url} (attempt {retry_count + 1})")
       
        if url.lower().endswith('.pdf'):
            async with httpx.AsyncClient(follow_redirects=True, timeout=timeout) as cli:
                r = await cli.get(url)
                if r.status_code == 200:
                    text = pdf_extract_text(io.BytesIO(r.content)) if r.content else ""
                    logger.info(f"Successfully loaded PDF via direct request")
                    return "", None, (text or "")[:300000], True
        else:
            async with httpx.AsyncClient(
                follow_redirects=True,
                timeout=timeout,
                headers={"User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"}
            ) as cli:
                r = await cli.get(url)
                if r.status_code == 200 and r.text:
                    html = r.text
                    soup = BeautifulSoup(html, 'html.parser')
                    txt = soup.get_text(" ", strip=True)
                    txt = re.sub(r"\s+", " ", txt)
                    logger.info(f"Successfully loaded HTML via direct request")
                    return html, soup, txt[:300000], False
                else:
                    logger.warning(f"Failed to load {url} via direct request - Status: {r.status_code}")
               
    except Exception as e:
        logger.warning(f"Error loading page {url} on attempt {retry_count + 1}: {e}")
   
    return "", None, "", False
 
 
# =============================================================================
# QUICK TEXT FETCHING
# =============================================================================
 
async def fetch_page_text(url: str, timeout=5.0) -> str:
    """
    Fetch page text content for quick analysis with minimal overhead.
   
    This function provides a lightweight way to fetch text content from a URL
    for quick analysis without the full parsing overhead of load_page.
   
    Args:
        url (str): URL to fetch
        timeout (float): Request timeout in seconds (default: 5.0)
       
    Returns:
        str: Extracted text content (limited to 200,000 characters)
       
    Example:
        >>> text = await fetch_page_text("https://example.com")
        >>> print(f"Fetched {len(text)} characters")
    """
    try:
        async with httpx.AsyncClient(follow_redirects=True, timeout=timeout, headers={"User-Agent":"Mozilla/5.0"}) as cli:
            r = await cli.get(url)
            if r.status_code == 200 and r.text:
                # remove tags; keep it cheap
                txt = re.sub(r"<[^>]+>", " ", r.text)
                txt = re.sub(r"\s+", " ", txt)
                return txt[:200000]
    except Exception:
        pass
    return ""