const subscriptionController = require("../controllers/subscription-controller");
const router = require("express").Router();
const verifyToken = require("../middlewares/verify-token");

module.exports = (app) => { 

    // ------------------------- subscription -------------------------

    // get all subscriptions
    router.get("/subscriptions", subscriptionController.getSubscriptions);

    router.get("/subscription/history", subscriptionController.getSubscriptionHistory);
    
    app.use("/speaker/api/v1", router);

}