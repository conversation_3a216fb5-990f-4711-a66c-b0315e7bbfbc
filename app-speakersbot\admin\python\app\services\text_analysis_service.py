"""
Text Analysis Service
=====================
Analyzes text documents to extract linguistic metrics for voiceprint including:
- Average sentence length
- Pacing metrics (rhythm, sentence length variance, punctuation density)
- Tone variance (sentiment variance, tone shifts, dominant tone)

Uses OpenAI LLM for deep linguistic analysis.
"""

import io
import json
import re
from typing import Dict, Any

from app.config.logger import get_logger
from app.config.config import config
from openai import OpenAI
from docx import Document

logger = get_logger(__name__, file_name="voiceprint.log")

# PDF extraction
try:
    from pdfminer.high_level import extract_text_to_fp
    from pdfminer.layout import LAParams
    PDF_HIGH_LEVEL = True
except ImportError:
    PDF_HIGH_LEVEL = False
    LAParams = None

try:
    from pdfminer.pdfinterp import PDFResourceManager, PDFPageInterpreter
    from pdfminer.converter import TextConverter
    from pdfminer.pdfpage import PDFPage
    PDF_LOW_LEVEL = True
    if not PDF_HIGH_LEVEL:
        try:
            from pdfminer.layout import LAParams
        except ImportError:
            LAParams = None
except ImportError:
    PDF_LOW_LEVEL = False


def _normalize_extracted_text(text: str) -> str:
    """
    Normalize extracted text to reduce formatting artifacts and inconsistencies.
    
    Args:
        text: Raw extracted text
        
    Returns:
        Normalized text string
    """
    if not text:
        return text
    
    # Replace various Unicode spaces with regular space
    text = re.sub(r'[\u00A0\u1680\u2000-\u200B\u202F\u205F\u3000\uFEFF]', ' ', text)
    
    # Remove zero-width characters
    text = re.sub(r'[\u200B-\u200D\uFEFF]', '', text)
    
    # Normalize dashes and hyphens
    text = re.sub(r'[—–‐‑]', '-', text)
    
    # Normalize quotes
    text = re.sub(r'["""]', '"', text)
    text = re.sub(r'[\u2018\u2019\u201A\u201B]', "'", text)
    
    # Fix excessive whitespace
    text = re.sub(r'[ \t]+', ' ', text)
    
    # Normalize line breaks
    text = re.sub(r'\n{3,}', '\n\n', text)
    text = re.sub(r'\n(?=[a-z])', ' ', text)
    
    # Process paragraphs
    paragraphs = text.split('\n\n')
    normalized_paragraphs = []
    for para in paragraphs:
        para = re.sub(r'\n+', ' ', para)
        normalized_paragraphs.append(para.strip())
    text = '\n\n'.join(normalized_paragraphs)
    
    # Fix spacing around punctuation
    text = re.sub(r'\s+([.,!?;:])', r'\1', text)
    text = re.sub(r'([.,!?])(?![)\]}''"”\s])', r'\1 ', text)
    text = re.sub(r'\s+-\s+', ' - ', text)
    
    # Remove trailing whitespace
    lines = text.split('\n')
    text = '\n'.join(line.rstrip() for line in lines)
    
    # Final cleanup
    text = re.sub(r' +', ' ', text)
    
    return text.strip()


class TextAnalysisService:
    """Service for analyzing text documents and extracting voiceprint metrics."""
    
    def __init__(self):
        """Initialize the text analysis service."""
        self.openai_client = None
        if config.OPENAI_API_KEY:
            self.openai_client = OpenAI(api_key=config.OPENAI_API_KEY)
    
    def extract_from_pdf(self, file_path: str) -> str:
        """
        Extract text from PDF file.
        
        Args:
            file_path: Path to PDF file
            
        Returns:
            Extracted text string
            
        Raises:
            ValueError: If PDF extraction fails
        """
        
        try:
            with open(file_path, 'rb') as pdf_file:
                if PDF_HIGH_LEVEL:
                    output_string = io.StringIO()
                    extract_text_to_fp(
                        pdf_file,
                        output_string,
                        laparams=LAParams() if LAParams else None,
                        output_type='text',
                        codec='utf-8'
                    )
                    text = output_string.getvalue()
                elif PDF_LOW_LEVEL:
                    resource_manager = PDFResourceManager()
                    output_string = io.StringIO()
                    laparams = LAParams() if LAParams is not None else None
                    converter = TextConverter(resource_manager, output_string, laparams=laparams)
                    page_interpreter = PDFPageInterpreter(resource_manager, converter)
                    
                    pdf_file.seek(0)
                    for page in PDFPage.get_pages(pdf_file, caching=True, check_extractable=True):
                        page_interpreter.process_page(page)
                    
                    text = output_string.getvalue()
                    converter.close()
                    output_string.close()
                else:
                    raise Exception("PDF extraction libraries not available")
            
            logger.info(f"Extracted {len(text)} characters from PDF")
            normalized_text = _normalize_extracted_text(text)
            logger.info(f"Normalized PDF text: {len(normalized_text)} characters")
            return normalized_text
            
        except Exception as e:
            logger.error(f"Error extracting text from PDF: {e}")
            raise ValueError(f"Failed to extract text from PDF: {str(e)}")
    
    def extract_from_docx(self, file_path: str) -> str:
        """
        Extract text from DOCX file.
        
        Args:
            file_path: Path to DOCX file
            
        Returns:
            Extracted text string
            
        Raises:
            ValueError: If DOCX extraction fails
        """
        
        try:
            doc = Document(file_path)
            text_parts = []
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text)
            
            text = '\n\n'.join(text_parts)
            logger.info(f"Extracted {len(text)} characters from DOCX")
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error extracting text from DOCX: {e}")
            raise ValueError(f"Failed to extract text from DOCX: {str(e)}")
    
    def extract_from_txt(self, file_path: str) -> str:
        """
        Extract text from TXT file.
        
        Args:
            file_path: Path to TXT file
            
        Returns:
            Extracted text string
            
        Raises:
            ValueError: If TXT extraction fails
        """
        try:
            # Try UTF-8 first
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    text = f.read()
            except UnicodeDecodeError:
                # Fallback to latin-1
                with open(file_path, 'r', encoding='latin-1', errors='ignore') as f:
                    text = f.read()
            
            logger.info(f"Extracted {len(text)} characters from TXT")
            return text.strip()
            
        except Exception as e:
            logger.error(f"Error extracting text from TXT: {e}")
            raise ValueError(f"Failed to extract text from TXT: {str(e)}")
    
    def extract_text(self, file_path: str, file_type: str) -> str:
        """
        Extract text from file based on file type.
        
        Args:
            file_path: Path to the file
            file_type: File type (pdf, docx, txt)
            
        Returns:
            Extracted text string
            
        Raises:
            ValueError: If extraction fails or file type unsupported
        """
        if file_type == 'pdf':
            return self.extract_from_pdf(file_path)
        elif file_type == 'docx':
            return self.extract_from_docx(file_path)
        elif file_type == 'txt':
            return self.extract_from_txt(file_path)
        else:
            raise ValueError(f"Unsupported file type: {file_type}")
    
    def analyze_with_openai(self, text: str) -> Dict[str, Any]:
        """
        Analyze text with OpenAI to extract voiceprint metrics.
        
        Args:
            text: Input text to analyze (first 8000 chars)
            
        Returns:
            Dictionary with voiceprint metrics
            
        Raises:
            ValueError: If OpenAI API call fails
        """
        if not self.openai_client:
            raise ValueError("OpenAI API key not configured")
        
        prompt = f"""Analyze the following text and extract linguistic metrics in JSON format. Focus on:
1. Average sentence length (mean words per sentence)
2. Pacing/rhythm metrics:
   - Sentence length variance (statistical variance of sentence lengths)
   - Punctuation density (punctuation marks per 100 words)
   - Rhythm score (0-1, based on sentence length patterns and flow)
3. Tone variance:
   - Sentiment variance (0-1, variation in emotional tone)
   - Tone shifts (count of noticeable tone changes)
   - Dominant tone (one of: neutral, formal, informal, enthusiastic, professional, conversational, authoritative)
4. Figurative Language Density (metaphors, similes, analogies, idioms per 100 words)
5. Factual Claim Count (total number of factual statements, statistics, data points, or verifiable claims)
6. Numerical Density (numbers, percentages, statistics per 100 words)
7. Domain Vocabulary Density / Jargon Density (specialized terms, technical vocabulary, domain-specific words per 100 words)
8. Rhetorical Question Frequency (rhetorical questions per 100 words). 
   A rhetorical question is a question asked for effect or emphasis, not expecting an answer. 
   Examples: "Who doesn't want success?", "Isn't that amazing?", "Why would anyone do that?", 
   "How can we ignore this?", "What could be better than this?". 
   Count ALL questions that are rhetorical (asked for emphasis/persuasion) vs genuine information-seeking questions.

Text to analyze:
{text[:8000]}

Return ONLY a valid JSON object with this exact structure:
{{
    "average_sentence_length": <float>,
    "pacing": {{
        "sentence_length_variance": <float>,
        "punctuation_density": <float>,
        "rhythm_score": <float>
    }},
    "tone_variance": {{
        "sentiment_variance": <float>,
        "tone_shifts": <integer>,
        "dominant_tone": "<string>"
    }},
    "figurative_language_density": <float>,
    "factual_claim_count": <integer>,
    "numerical_density": <float>,
    "domain_vocabulary_density": <float>,
    "rhetorical_question_frequency": <float>
}}"""
        
        try:
            response = self.openai_client.chat.completions.create(
                model=getattr(config, 'OPENAI_MODEL', 'gpt-4o-mini'),
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                response_format={"type": "json_object"}
            )
            
            content = response.choices[0].message.content.strip()
            logger.info(f"OpenAI API call successful, response length: {len(content)} characters")
            
            try:
                metrics = json.loads(content)
                return metrics
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse OpenAI JSON response: {e}")
                # Fallback: extract JSON from response if wrapped in markdown
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    metrics = json.loads(json_match.group())
                    return metrics
                raise ValueError("Failed to parse OpenAI response")
                
        except Exception as e:
            logger.error(f"OpenAI API call failed: {e}")
            raise ValueError(f"OpenAI analysis failed: {str(e)}")
    
    def analyze_text(self, file_path: str, file_type: str) -> Dict[str, Any]:
        """
        Analyze text file and extract voiceprint metrics.
        
        Args:
            file_path: Path to the text file
            file_type: File type (pdf, docx, txt)
            
        Returns:
            Dictionary containing voiceprint text metrics
        """
        # Extract text
        extracted_text = self.extract_text(file_path, file_type)
        
        if not extracted_text or len(extracted_text.strip()) < 50:
            raise ValueError("Extracted text is too short for analysis (minimum 50 characters required)")
        
        # Analyze with OpenAI LLM
        try:
            llm_metrics = self.analyze_with_openai(extracted_text)
        except Exception as e:
            logger.error(f"OpenAI analysis failed: {e}")
            raise ValueError(f"Failed to analyze text with OpenAI: {str(e)}")
        
        # Build text metrics from OpenAI response
        text_metrics = {
            "average_sentence_length": llm_metrics.get("average_sentence_length", 0.0),
            "pacing": llm_metrics.get("pacing", {
                "sentence_length_variance": 0.0,
                "punctuation_density": 0.0,
                "rhythm_score": 0.5
            }),
            "tone_variance": llm_metrics.get("tone_variance", {
                "sentiment_variance": 0.3,
                "tone_shifts": 0,
                "dominant_tone": "neutral"
            }),
            "figurative_language_density": llm_metrics.get("figurative_language_density", 0.0),
            "factual_claim_count": llm_metrics.get("factual_claim_count", 0),
            "numerical_density": llm_metrics.get("numerical_density", 0.0),
            "domain_vocabulary_density": llm_metrics.get("domain_vocabulary_density", 0.0),
            "rhetorical_question_frequency": llm_metrics.get("rhetorical_question_frequency", 0.0)
        }
        
        return text_metrics

