import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../store';
 
// Define the base URL for your API
const API_BASE_URL = "http://localhost:8000"
 
export const dashboardApi = createApi({
  reducerPath: 'dashboardApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Get the token from the state
    //   const token = (getState() as RootState).auth?.token;
    const token= "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6MTQsImlhdCI6MTc1OTAzMTQxMSwiZXhwIjoxNzU5MDUzMDExfQ.mPFK49MEoRucd4GZhLYooP_rYYzJhBq6Wi_sgfAkdbE"
      
      // If we have a token, set the authorization header
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      
      // Set content type
      headers.set('content-type', 'application/json');
      
      return headers;
    },
  }),
  tagTypes: ['Opportunity'],
  endpoints: (builder) => ({
    // GET /speaker/api/v1/opportunity → get opportunities for speaker
    getOpportunities: builder.query({
      query: (params = {}) => ({
        url: '/speaker/api/v1/opportunity',
        params,
      }),
      providesTags: ['Opportunity'],
    }),
    
    // GET /opportunity/since-last-login → get opportunities since last login
    getAllOpportunitiesSinceLastLogin: builder.query({
      query: ({ search }: { search?: string } = {}) => ({
        url: '/opportunity/since-last-login',
        params: search ? { search } : {},
      }),
      providesTags: ['Opportunity'],
    }),
    
    // PUT /opportunity/update/:id → update opportunity status
    updateOpportunity: builder.mutation({
      query: ({ id, status, note }: { id: string | number; status: string; note: string }) => ({
        url: `/opportunity/update/${id}`,
        method: 'PUT',
        body: {
          status,
          note,
        },
      }),
      invalidatesTags: ['Opportunity'],
    }),
    
    // GET /opportunity/pipeline → get opportunity pipeline
    getOpportunityPipeline: builder.query({
      query: (params = {}) => ({
        url: '/opportunity/pipeline',
        params,
      }),
      providesTags: ['Opportunity'],
    }),
    
    // GET /opportunity/lost-opportunities-tracker → get lost opportunities tracker
    getLostOpportunitiesTracker: builder.query({
      query: (params = {}) => ({
        url: '/opportunity/lost-opportunities-tracker',
        params,
      }),
      providesTags: ['Opportunity'],
    }),
    
    // GET /opportunity/due-in-72-hours → get opportunities due in 72 hours
    getOpportunitiesDueIn72Hours: builder.query({
      query: (params = {}) => ({
        url: '/opportunity/due-in-72-hours',
        params,
      }),
      providesTags: ['Opportunity'],
    }),
  }),
});
 
export const {
  useGetOpportunitiesQuery,
  useGetAllOpportunitiesSinceLastLoginQuery,
  useUpdateOpportunityMutation,
  useGetOpportunityPipelineQuery,
  useGetLostOpportunitiesTrackerQuery,
  useGetOpportunitiesDueIn72HoursQuery,
} = dashboardApi;