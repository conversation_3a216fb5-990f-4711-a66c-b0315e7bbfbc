"""
Topic Distribution Helper Functions

This module contains pure helper functions for topic distribution logic.
It provides mathematical and algorithmic functions for distributing topics
across search engines without any database dependencies.

Key Features:
- Pure mathematical functions for topic distribution
- Systematic rotation algorithms
- Topic balancing across engines
- No database dependencies (separation of concerns)

Author: Speaker Bot Team
Version: 2.0 (Refactored)
Last Updated: 2024
"""

from typing import List, Dict, Tuple


# Search engines configuration - can be easily modified
SEARCH_ENGINES = ["exa", "tavily"]


def apply_topic_rotation(topic_ids: List[int], rotation_offset: int) -> List[int]:
    """
    Apply rotation to topic IDs for systematic distribution.
    
    Args:
        topic_ids: List of topic IDs to rotate
        rotation_offset: Number of positions to rotate
    
    Returns:
        Rotated list of topic IDs
        
    Example:
        >>> apply_topic_rotation([1, 2, 3, 4, 5], 2)
        [3, 4, 5, 1, 2]
    """
    if not topic_ids or rotation_offset == 0:
        return topic_ids.copy()
    
    # Normalize rotation offset to avoid unnecessary full rotations
    rotation_offset = rotation_offset % len(topic_ids)
    
    if rotation_offset == 0:
        return topic_ids.copy()
    
    return topic_ids[rotation_offset:] + topic_ids[:rotation_offset]


def distribute_topics_with_rotation(
    available_topic_ids: List[int], 
    round_number: int
) -> Dict[str, Dict[str, List[int]]]:
    """
    Production-ready topic distribution with systematic rotation logic.
    
    This function implements the exact distribution logic:
    1. Base topics: perfectly divisible among engines (each engine gets same number)
    2. Rotating topics: remaining topics rotate systematically through engines
    3. No engine ever gets "extra" topics - they get base + rotating topics
    
    Args:
        available_topic_ids: List of topic IDs available for this round
        round_number: Current round number for rotation calculation
        
    Returns:
        Dictionary with 'base_topics' and 'rotating_topics' for each engine
        
    Example with 6 topics, 5 engines:
        Base topics: 1 per engine (perfectly divisible)
        Rotating topics: 1 topic that rotates through engines
        
    Production Notes:
        - Handles any number of topics and engines dynamically
        - Systematic rotation ensures fair distribution over time
        - Optimized for performance with large datasets
    """
    if not available_topic_ids:
        return {engine: {"base_topics": [], "rotating_topics": []} for engine in SEARCH_ENGINES}
    
    total_topics = len(available_topic_ids)
    num_engines = len(SEARCH_ENGINES)
    
    # Calculate base topics per engine (perfectly divisible)
    base_topics_per_engine = total_topics // num_engines
    remaining_topics = total_topics % num_engines
    
    # Separate base topics and rotating topics
    base_topic_ids = available_topic_ids[:base_topics_per_engine * num_engines]
    rotating_topic_ids = available_topic_ids[base_topics_per_engine * num_engines:]
    
    # Calculate rotation offset for this round
    # Handle case where base_topic_ids might be empty (when topics < num_engines)
    if len(base_topic_ids) == 0:
        rotation_offset = 0
    else:
        rotation_offset = ((round_number - 1) * base_topics_per_engine) % len(base_topic_ids)
    
    # Apply rotation to base topics
    rotated_base_topics = apply_topic_rotation(base_topic_ids, rotation_offset)
    
    # Distribute base topics to engines
    distribution = {}
    topic_index = 0
    
    for engine in SEARCH_ENGINES:
        base_topics = []
        for _ in range(base_topics_per_engine):
            if topic_index < len(rotated_base_topics):
                base_topics.append(rotated_base_topics[topic_index])
                topic_index += 1
        distribution[engine] = {"base_topics": base_topics, "rotating_topics": []}
    
    # Distribute rotating topics systematically
    if rotating_topic_ids:
        # Calculate which engine gets the rotating topic in this round
        # Start from last engine and rotate backwards
        for i, topic_id in enumerate(rotating_topic_ids):
            engine_index = (len(SEARCH_ENGINES) - 1 - i - ((round_number - 1) % num_engines)) % len(SEARCH_ENGINES)
            engine = SEARCH_ENGINES[engine_index]
            distribution[engine]["rotating_topics"].append(topic_id)
    
    return distribution






def create_topic_id_to_name_mapping(topics_with_ids: List[Tuple[int, str]]) -> Dict[int, str]:
    """
    Create a mapping from topic IDs to topic names.
    
    Args:
        topics_with_ids: List of tuples containing (topic_id, topic_name)
        
    Returns:
        Dictionary mapping topic IDs to names
        
    Example:
        >>> create_topic_id_to_name_mapping([(1, 'AI'), (2, 'ML')])
        {1: 'AI', 2: 'ML'}
    """
    return {topic_id: topic_name for topic_id, topic_name in topics_with_ids}

