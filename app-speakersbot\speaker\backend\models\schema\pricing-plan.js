const connection = require('../connection');
const { DataTypes } = require('sequelize');

const PricingPlan = connection.define('PricingPlan', {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: 'Primary key for the pricing plan',
    },
    name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: 'Name of the pricing plan (must be unique)',
    },
    billing_interval: {
        type: DataTypes.ENUM('month', 'year','one-time'),
        defaultValue: 'month',
        comment: 'Billing interval for the plan (monthly or yearly)',
    },
    description: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Description of the pricing plan',
    },
    currency: {
        type: DataTypes.STRING(10),
        allowNull: false,
        defaultValue: 'USD',
        comment: 'Currency for the plan pricing',
    },
    amount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        comment: 'Amount charged for the plan',
    },
    stripe_price_id: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: 'Stripe price ID for payment integration',
    },
    stripe_product_id: {
        type: DataTypes.STRING(255),
        allowNull: true,
        comment: 'Stripe product ID for payment integration',
    },
    is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
        comment: 'Indicates if the plan is active',
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
        comment: 'Record creation timestamp',
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
        comment: 'Record last update timestamp',
    },
    deleted_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Record deletion timestamp(soft delete)',
    }

}, {
    tableName: 'pricing_plans',
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    paranoid: true,
    deletedAt: "deleted_at", 

});

module.exports = PricingPlan;
