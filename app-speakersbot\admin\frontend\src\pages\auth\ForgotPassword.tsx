import { useForgotPasswordMutation } from "@/apis/authApi";
import loginImg from "@/assets/images/login-img.png";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { MailOutlined, ArrowLeftOutlined } from "@ant-design/icons";
import React from "react";
import { Link, useNavigate } from "react-router-dom";
import loginLogo from "/login-logo.png";
import ButtonLoader from "@/components/common/ButtonLoader";

const ForgotPassword: React.FC = () => {
  const navigate = useNavigate();
  const [forgotPasswordMutation, { isLoading }] = useForgotPasswordMutation();
  const { toast } = useToast();

  const [email, setEmail] = React.useState("");
  const [emailError, setEmailError] = React.useState("");
  const [isSubmitted, setIsSubmitted] = React.useState(false);

  // Email validation function
  const validateEmail = (value: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(value);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);

    if (value && !validateEmail(value)) {
      setEmailError("Please enter a valid email address");
    } else {
      setEmailError("");
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    // Validate email before submission
    if (!validateEmail(email)) {
      setEmailError("Please enter a valid email address");
      return;
    }
    try {
      const result = await forgotPasswordMutation({
        email,
      }).unwrap();

      if (result.status) {
        toast({
          title: "Email Sent!",
          description: "Please check your email for password reset instructions.",
        });
        setIsSubmitted(true);
      }
    } catch (error: any) {
      console.error("Forgot password error:", error);
      toast({
        title: error?.data?.error?.message || "Failed to send reset email",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-foreground flex items-center justify-center p-6 bg-[hsla(217, 100%, 98%, 1)] bg-[#F4F8FF]">
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: `url(${loginImg})`,
          clipPath: "polygon(0 0, 70% 0, 65% 100%, 0% 100%)",
        }}
      />
      <div className="[width:min(1100px,100%)] grid grid-cols-[1.25fr_1fr] xl:grid-cols-[1.5fr_1fr] bg-foreground rounded-[50px] overflow-hidden border border-[hsl(var(--dashboard-light-blue)/0.35)] shadow-lg relative z-20">
        <div
          className="relative min-h-[520px] bg-cover bg-center saturate-125 m-4 mr-0 rounded-[50px] overflow-hidden"
          style={{
            backgroundImage: `url(${loginImg})`,
            clipPath: "polygon(0 0, 102% 0, 85% 100%, 0% 100%)",
          }}
        >
          <div className="absolute inset-0 bg-[linear-gradient(200deg,rgba(0,0,0,0.15)_10%,rgba(0,0,0,0.55)_70%)]" />
        </div>

        <div className="bg-foreground flex flex-col justify-around py-[50px] px-10">
          <div>
            <img
              src={loginLogo}
              alt="login"
              className="w-full h-full max-h-[50px] object-contain"
            />
          </div>
          <div className="flex-1 flex flex-col justify-center">
            <div className="text-center">
              <h1 className="m-0 text-[38px] font-bold tracking-[0.4px] bg-muted-gradient bg-clip-text text-transparent">
                Forgot Password?
              </h1>
              <div className="mt-1.5 text-[14px] text-[#464646]">
                {isSubmitted 
                  ? "Check your email for reset instructions" 
                  : "Enter your email address and we'll send you a link to reset your password"
                }
              </div>
            </div>

            {!isSubmitted ? (
              <form onSubmit={handleSubmit} className="px-5 py-5">
                <div>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="Enter your email address"
                    className={`h-11 rounded-[10px] bg-foreground text-background placeholder:text-background autofill:bg-foreground ${
                      emailError ? "border-red-500 focus:border-red-500" : ""
                    }`}
                    startIcon={<MailOutlined />}
                    value={email}
                    onChange={handleEmailChange}
                  />
                  {emailError && (
                    <p className="text-red-500 text-sm mt-1">{emailError}</p>
                  )}
                </div>

                <ButtonLoader
                  type="submit"
                  disabled={isLoading}
                  loading={isLoading}
                  className={`mt-5 h-12 w-full rounded-[50px] bg-muted-gradient border-0 shadow-[0_8px_24px_rgba(2,132,199,0.35)] text-white transition-transform duration-500 ${
                    isLoading
                      ? "animate-pulse cursor-wait"
                      : "hover:shadow-[0_12px_28px_rgba(2,132,199,0.45)] hover:-translate-y-0.5 active:translate-y-0 active:scale-[0.98]"
                  }`}
                >
                  Send Reset Link
                </ButtonLoader>
              </form>
            ) : (
              <div className="px-5 py-5 text-center">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800 text-sm">
                    We've sent a password reset link to <strong>{email}</strong>
                  </p>
                </div>
              </div>
            )}

            <div className="text-center">
              <Link
                to="/admin/login"
                className="inline-flex items-center text-sm text-[#464646] hover:text-[#2c5aa0] transition-colors"
              >
                <ArrowLeftOutlined className="mr-1" />
                Back to Login
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
