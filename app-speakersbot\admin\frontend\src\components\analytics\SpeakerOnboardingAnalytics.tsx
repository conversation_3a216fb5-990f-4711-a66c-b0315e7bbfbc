import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card";
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from "recharts";
import {
  UserPlus,
  Star,
  CheckCircle,
  Trophy,
  AlertTriangle,
} from "lucide-react";
import { Badge } from "../ui/badge";
import { Progress } from "../ui/progress";
import { Skeleton } from "../ui/skeleton";
import { useToast } from "../../hooks/use-toast";
import { useGetDashboardOnboardingQuery } from "../../apis/dashboardApi";
import { InvalidTokenHandler } from "../common/InvalidTokenHandler";

const SpeakerOnboardingAnalytics: React.FC = () => {
  // API Query
  const {
    data: onboardingData,
    isLoading,
    error,
  } = useGetDashboardOnboardingQuery(undefined);

  // State for onboarding data
  const [onboardingState, setOnboardingState] = useState<any>(null);
  const { toast } = useToast();

  // Effect to handle data and console logging
  useEffect(() => {
    if (onboardingData) {
      setOnboardingState(onboardingData);
      // Show success toast when data loads
      // toast({
      //   title: "Onboarding analytics loaded successfully",
      // });
    }
  }, [onboardingData, toast]);

  // Show error toast when there's an error
  useEffect(() => {
    if (error) {
      toast({
        title: "Failed to load onboarding data",
        variant: "destructive",
      });
    }
  }, [error, toast]);

  // Color palette from Emerging Categories chart
  const colors = [
    "hsl(var(--dashboard-dark-blue))",
    "hsl(var(--dashboard-medium-blue))",
    "hsl(var(--dashboard-light-blue))",
    "#10b981",
    "#f59e0b",
    "#ef4444",
    "#8b5cf6",
    "#06b6d4",
    "#84cc16",
    "#f97316",
  ];

  // Transform signup source data from API dynamically
  const signupSourceData = onboardingState?.data?.signupSource
    ? Object.entries(onboardingState.data.signupSource)
        .filter(([_, count]) => (count as number) > 0) // Only include sources with data
        .map(([source, count]) => {
          const total = Object.values(onboardingState.data.signupSource).reduce(
            (sum: number, value) => sum + (value as number),
            0
          ) as number;
          const countNum = count as number;
          return {
            source: source.charAt(0).toUpperCase() + source.slice(1),
            count: countNum,
            percentage: total > 0 ? (countNum / total) * 100 : 0, // Keep as number for chart calculation
            displayValue: countNum, // Real value from backend for display
          };
        })
    : [];

  // Transform profile completion heatmap data from API
  const profileCompletionHeatmap = onboardingState?.data
    ?.profileCompletionHeatmap
    ? onboardingState.data.profileCompletionHeatmap.map((item: any) => ({
        field:
          item.field.charAt(0).toUpperCase() +
          item.field.slice(1).replace("_", " "),
        completion: item.percentage, // Now it's already a number
        color: "hsl(var(--dashboard-dark-blue))",
      }))
    : [];

  // Quality score data not available in API response
  const qualityScoreData: any[] = [];

  // Transform incomplete fields data from API
  const incompleteFieldsData = onboardingState?.data?.topIncompleteFields
    ? onboardingState.data.topIncompleteFields.map((item: any) => ({
        field: item.field,
        missing: item.missingCount,
        percentage: item.percentMissing, // Now it's already provided as percentage
      }))
    : [];

  // Transform intake form completion data from API
  const intakeFormData = onboardingState?.data?.intakeFromComplationFlow
    ? onboardingState.data.intakeFromComplationFlow.map((item: any) => ({
        step: item.step,
        completion: item.completionRate, // Now it's already a number
        dropoff: item.dropoff, // Now it's already a number
      }))
    : [];

  // Transform weekly onboarding trends data from API
  const weeklyOnboardingData = onboardingState?.data?.weeklyOnboardingTrends
    ? onboardingState.data.weeklyOnboardingTrends.map((item: any) => ({
        week: item.week,
        new: item.newSignups || 0,
        completed: item.completed || 0,
        dropped: item.droppedOff || 0,
      }))
    : [];

  return (
    <div className="space-y-6">
      <InvalidTokenHandler error={error} />
      {isLoading ? (
        <div>
          <Skeleton className="h-5 w-72" />
          <Skeleton className="h-4 w-96 mt-2" />
        </div>
      ) : (
        <div>
          <h3 className="text-xl font-semibold text-foreground mb-2">
            Speaker Onboarding & Profile Health
          </h3>
          <p className="text-muted-foreground text-sm mb-6">
            Monitor speaker registration funnel and profile completion rates
          </p>
        </div>
      )}

      {/* Show error state */}
      {error && (
        <Card className="bg-tertiary border-border">
          <CardContent className="p-8 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Error Loading Data
            </h3>
            <p className="text-muted-foreground">
              Unable to fetch onboarding analytics data. Please try again later.
            </p>
          </CardContent>
        </Card>
      )}

      {isLoading && (
        <>
          {/* KPI Cards Skeleton */}
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
            {[...Array(4)].map((_, index) => (
              <Card key={index} className="bg-tertiary border-border">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <Skeleton className="h-4 w-24 mb-2" />
                      <Skeleton className="h-8 w-16" />
                    </div>
                    <Skeleton className="h-6 w-6 rounded" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Charts Skeleton */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            {[...Array(4)].map((_, index) => (
              <Card key={index} className="bg-tertiary border-border">
                <CardHeader>
                  <Skeleton className="h-6 w-48" />
                </CardHeader>
                <CardContent>
                  <Skeleton className="h-[300px] w-full" />
                </CardContent>
              </Card>
            ))}
            {/* Last skeleton spans full width */}
            <Card className="bg-tertiary border-border xl:col-span-2">
              <CardHeader>
                <Skeleton className="h-6 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-[300px] w-full" />
              </CardContent>
            </Card>
          </div>
        </>
      )}

      {/* Show data when loaded and no error */}
      {!isLoading && !error && (
        <>
          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
            <Card className="bg-tertiary border-border">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      New Speakers (30d)
                    </p>
                    <p className="text-2xl font-bold text-foreground">
                      {onboardingState?.data?.summary?.newSpeakers || 0}
                    </p>
                  </div>
                  <UserPlus className="h-6 w-6 text-dashboard-dark-blue" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-tertiary border-border">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Profile Completion
                    </p>
                    <p className="text-2xl font-bold text-foreground">
                      {onboardingState?.data?.summary?.profileCompletion
                        ? `${onboardingState.data.summary.profileCompletion}%`
                        : "0%"}
                    </p>
                  </div>
                  <CheckCircle className="h-6 w-6 text-dashboard-medium-blue" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-tertiary border-border">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Avg Quality Score
                    </p>
                    <p className="text-2xl font-bold text-foreground">
                      {onboardingState?.data?.summary?.avgQualityScore
                        ? `${onboardingState.data.summary.avgQualityScore.toFixed(
                            1
                          )}/100`
                        : "0/100"}
                    </p>
                  </div>
                  <Trophy className="h-6 w-6 text-dashboard-light-blue" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-tertiary border-border">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      STAiGENT Completion
                    </p>
                    <p className="text-2xl font-bold text-foreground">
                      {onboardingState?.data?.summary?.intakeCompletion
                        ? `${onboardingState.data.summary.intakeCompletion}%`
                        : "0%"}
                    </p>
                  </div>
                  <Star className="h-6 w-6 text-dashboard-light-blue" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <Card className="bg-tertiary border-border">
              <CardHeader>
                <CardTitle className="text-foreground">
                  Speaker Signup Sources
                </CardTitle>
              </CardHeader>
              <CardContent>
                {signupSourceData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <PieChart>
                      <Pie
                        data={signupSourceData}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="percentage"
                        label={({ source, displayValue }: any) =>
                          `${source}: ${displayValue}`
                        }
                      >
                        {signupSourceData.map((entry, index) => (
                          <Cell
                            key={`cell-${index}`}
                            fill={colors[index % colors.length]}
                          />
                        ))}
                      </Pie>
                      <Tooltip
                        formatter={(value, name, props) => [
                          `${props.payload.displayValue} (${Number(
                            value
                          ).toFixed(1)}%)`,
                          props.payload.source,
                        ]}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                    <p>No signup data available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="bg-tertiary border-border">
              <CardHeader>
                <CardTitle className="text-foreground">
                  Profile Completion Heatmap
                </CardTitle>
              </CardHeader>
              <CardContent>
                {profileCompletionHeatmap.length > 0 ? (
                  <div className="space-y-4">
                    {profileCompletionHeatmap.map((item, index: number) => (
                      <div
                        key={index}
                        className="flex items-center justify-between"
                      >
                        <div className="flex items-center gap-3 flex-1">
                          <span className="text-sm font-medium text-foreground w-24">
                            {item.field}
                          </span>
                          <Progress
                            value={item.completion || 0}
                            className="flex-1"
                          />
                          <span className="text-sm text-muted-foreground w-12">
                            {item.completion}%
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                    <p>No profile completion data available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="bg-tertiary border-border">
              <CardHeader>
                <CardTitle className="text-foreground">
                  Top Incomplete Fields
                </CardTitle>
              </CardHeader>
              <CardContent>
                {incompleteFieldsData.length > 0 ? (
                  <div className="space-y-3">
                    {incompleteFieldsData.map((item, index) => (
                      <div
                        key={index}
                        className="flex items-center justify-between p-3 rounded-lg bg-muted/30"
                      >
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-1">
                            <span className="text-sm font-medium text-foreground">
                              {item.field}
                            </span>
                            <Badge variant="secondary">
                              {item.percentage}%
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {item.missing} speakers missing
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                    <p>No incomplete fields data available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            <Card className="bg-tertiary border-border">
              <CardHeader>
                <CardTitle className="text-foreground">
                STAiGENT Form Completion Flow
                </CardTitle>
              </CardHeader>
              <CardContent>
                {intakeFormData.length > 0 ? (
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={intakeFormData}>
                      <CartesianGrid
                        strokeDasharray="3 3"
                        stroke="hsl(var(--border))"
                      />
                      <XAxis
                        dataKey="step"
                        stroke="hsl(var(--muted-foreground))"
                        tickFormatter={(value) =>
                          value.length > 5
                            ? `${value.substring(0, 5)}...`
                            : value
                        }
                      />
                      <YAxis stroke="hsl(var(--muted-foreground))" />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: "hsl(var(--card))",
                          border: "1px solid hsl(var(--border))",
                          borderRadius: "6px",
                        }}
                      />
                      <Legend />
                      <Bar
                        dataKey="completion"
                        fill="hsl(var(--dashboard-dark-blue))"
                        name="Completion %"
                      />
                      <Bar
                        dataKey="dropoff"
                        fill="hsl(var(--dashboard-medium-blue))"
                        name="Drop-off %"
                      />
                    </BarChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                    <p>No STAiGENT form data available</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          <Card className="bg-tertiary border-border">
            <CardHeader>
              <CardTitle className="text-foreground">
                Weekly Onboarding Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              {weeklyOnboardingData.length > 0 ? (
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={weeklyOnboardingData}>
                    <CartesianGrid
                      strokeDasharray="3 3"
                      stroke="hsl(var(--border))"
                    />
                    <XAxis
                      dataKey="week"
                      stroke="hsl(var(--muted-foreground))"
                    />
                    <YAxis stroke="hsl(var(--muted-foreground))" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: "hsl(var(--card))",
                        border: "1px solid hsl(var(--border))",
                        borderRadius: "6px",
                      }}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="new"
                      stroke="hsl(var(--dashboard-dark-blue))"
                      name="New Signups"
                      strokeWidth={2}
                    />
                    <Line
                      type="monotone"
                      dataKey="completed"
                      stroke="hsl(var(--dashboard-medium-blue))"
                      name="Completed"
                      strokeWidth={2}
                    />
                    <Line
                      type="monotone"
                      dataKey="dropped"
                      stroke="hsl(var(--dashboard-light-blue))"
                      name="Dropped Off"
                      strokeWidth={2}
                    />
                  </LineChart>
                </ResponsiveContainer>
              ) : (
                <div className="flex items-center justify-center h-[300px] text-muted-foreground">
                  <p>No weekly trends data available</p>
                </div>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
};

export default SpeakerOnboardingAnalytics;
