const dashboardController = require("../controllers/dashboard-controller");
const router = require("express").Router();

module.exports = (app) => {
   
    // pipeline section analytics
    router.get("/pipeline",dashboardController.getDashboardPipeline);

    // onboarding section analytics
    router.get("/onboarding",dashboardController.getDashboardOnboarding);

    // revenue section analytics
    router.get("/revenue", dashboardController.getRevenueMetrics);

    // categories section analytics
    router.get("/categories", dashboardController.getCategoryAnalytics);

     router.get('/matching',dashboardController.getMatchingAnalytics);
    // feedback setion analytics
    router.get("/feedback", dashboardController.getFeedbackAnalytics)

    // health section analytics
    router.get("/health", dashboardController.getHealthAnalytics);

    // add category (other field at categories section)
    router.post("/add/category", dashboardController.addCategory);

    router.get("/affiliate", dashboardController.getAffiliateAnalytics);
       
    // new monthly revenue analytics
    router.get("/revenue/monthly", dashboardController.getMonthlyRevenueAnalytics);

    app.use("/admin/api/v1/dashboard", router);
};
