import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "../../../components/ui/card";
import { Skeleton } from "../../../components/ui/skeleton";

const RowSkeleton: React.FC = () => (
  <div className="grid grid-cols-6 gap-4 items-center py-3 border-b">
    <Skeleton className="h-6 w-full" />
    <Skeleton className="h-6 w-full" />
    <div className="flex items-center gap-2">
      <Skeleton className="h-6 w-full" />
      <Skeleton className="h-6 w-8" />
    </div>
    <Skeleton className="h-6 w-full rounded-full" />
    <Skeleton className="h-6 w-full" />
    <Skeleton className="h-6 w-full rounded-md" />
  </div>
);

const MatchingTableSkeleton: React.FC = () => {
  return (
    <div>
      {/* Table */}
      <Card className="bg-tertiary border rounded-xl">
        <CardContent className="p-4">
          <div className="grid grid-cols-6 gap-4 py-2 border-b text-sm text-muted-foreground">
            <div>Speaker Info</div>
            <div>Event Name</div>
            <div>Match %</div>
            <div>Status</div>
            <div>Start-End Date</div>
            <div>Actions</div>
          </div>
          {Array.from({ length: 10 }).map((_, i) => (
            <RowSkeleton key={i} />
          ))}
        </CardContent>
      </Card>
    </div>
  );
};

export default MatchingTableSkeleton;
