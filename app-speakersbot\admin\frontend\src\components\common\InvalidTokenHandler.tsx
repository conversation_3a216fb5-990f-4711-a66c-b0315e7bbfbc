import { useEffect, useRef } from "react";
import { toast } from "sonner";
import { useAuth } from "../../state/AuthContext";

export function InvalidTokenHandler({ error }: { error: any }) {
  const shown = useRef(false);
  const { logout } = useAuth();

  useEffect(() => {
    if (
      error &&
      (error?.data?.error?.message === "Invalid or expired token" ||
        error?.data?.error?.message === "Invalid token" ||
        error?.data?.error?.message === "token is required for authentication") &&
      !shown.current
    ) {
      toast.error("Token expired! Please log in again.");
      shown.current = true;
      logout();
    }
  }, [error, logout]);

  if (error && error?.data?.error?.message === "Invalid Token") {
    return null;
  }
  return null;
}
