interface FeatureCardProps {
  icon?: string;
  title: string;
  description: string;
}

export function FeatureCard({ icon, title, description }: FeatureCardProps) {
  return (
    <div className="bg-black rounded-[25px] border border-gradient-border lg:p-6 p-4 transition-all duration-300 max-w-[450px] lg:basis-[calc(33.33%-30px)] md:basis-[calc(50%-12px)] basis-full">
      <div className="flex flex-col items-center text-center">
        <div className="lg:size-[60px] size-[50px] flex items-center justify-center bg-gradient-tertiary rounded-full text-tertiary-foreground lg:p-4 p-3">
          <img
            src={icon}
            alt={title}
            className="w-full h-full object-contain"
          />
        </div>
        <h3 className="text-white font-poppins font-medium lg:text-[22px] md:text-xl text-lg mt-5">
          {title}
        </h3>
        <p className="text-white font-poppins font-light lg:text-base text-sm !leading-relaxed mt-2">
          {description}
        </p>
      </div>
    </div>
  );
}
