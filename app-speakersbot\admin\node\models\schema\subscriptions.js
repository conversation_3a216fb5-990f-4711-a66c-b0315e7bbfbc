
const { DataTypes } = require("sequelize");
const connection = require("../connection");


const Subscriptions = connection.define("Subscriptions", {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: 'Primary key for the subscription record',
    },
    speaker_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Foreign key referencing the speaker',
    },
    affiliate_id:{ 
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    plan_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Foreign key referencing the subscription plan',
    },
    affiliate_commission:{
        type: DataTypes.DECIMAL(10, 2),
        allowNull: true,
        comment: 'Commission amount for the affiliate',
    },
    stripe_subscription_id: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'Stripe subscription ID for payment tracking',
    },
    status: {
        type: DataTypes.ENUM('active', 'expired','failed','cancelled'),
        allowNull: false,
        comment: 'Status of the subscription',
    },
    start_date: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Date when the subscription started',
    },
    end_date: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Date when the subscription ended (nullable)',
    },
    amount: {
        type: DataTypes.DECIMAL(10, 2),
        allowNull: false,
        comment: 'Amount charged for the subscription',
    },
    currency: {
        type: DataTypes.STRING(10),
        allowNull: false,
        defaultValue: 'usd',
        comment: 'Currency for the subscription amount',
    },
    invoice_id: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'Stripe invoice ID associated with the subscription',
    },
    invoice_url: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'URL to view the invoice on Stripe',
    },
    charge_id: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'Stripe charge ID associated with the payment',
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: 'Record creation timestamp',
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: 'Record last update timestamp',
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    }
}, {
    tableName: 'subscriptions',
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at"

});

module.exports = Subscriptions;