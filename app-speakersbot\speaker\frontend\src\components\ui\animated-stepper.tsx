import React from 'react';
import { cn } from '@/lib/utils';
import { CheckCircle } from 'lucide-react';

interface Step {
  id: number;
  title: string;
  icon: React.ElementType;
  description: string;
}

interface AnimatedStepperProps {
  steps: Step[];
  currentStep: number;
  className?: string;
  onStepClick?: (stepId: number) => void;
}

export function AnimatedStepper({ steps, currentStep, className, onStepClick }: AnimatedStepperProps) {
  return (
    <div className={cn("w-full bg-surface/50 border border-border/50 p-6 rounded-2xl", className)}>
      <div className="relative">
        {/* Progress Line */}
        <div className="absolute top-6 left-8 right-8 h-0.5 bg-border/30 rounded-full">
          <div 
            className="h-full bg-primary rounded-full transition-all duration-700 ease-out shadow-glow"
            style={{ 
              width: `${((currentStep - 1) / (steps.length - 1)) * 100}%` 
            }}
          />
        </div>
        
        {/* Steps Container */}
        <div className="relative flex justify-between px-4">
          {steps.map((step, index) => {
            const isCompleted = step.id < currentStep;
            const isCurrent = step.id === currentStep;
            const isPending = step.id > currentStep;
            
            return (
              <div
                key={step.id}
                className="flex flex-col items-center group min-w-0 flex-1"
              >
                {/* Step Circle */}
                <div
                  className={cn(
                    "relative w-12 h-12 rounded-full flex items-center justify-center transition-all duration-500 transform",
                    "border-2 z-10",
                    isCompleted && "bg-primary border-primary text-primary-foreground scale-110 shadow-glow cursor-pointer hover:scale-115",
                    isCurrent && "bg-primary border-primary text-primary-foreground scale-125 shadow-elevated animate-pulse cursor-pointer hover:scale-130",
                    isPending && "bg-surface border-border text-foreground-muted scale-100"
                  )}
                  onClick={() => (isCompleted || isCurrent) && onStepClick?.(step.id)}
                >
                  {isCompleted ? (
                    <CheckCircle className="w-6 h-6 animate-scale-in" />
                  ) : isCurrent ? (
                    <step.icon className="w-6 h-6 animate-wiggle" />
                  ) : (
                    <step.icon className="w-5 h-5" />
                  )}
                  
                  {/* Glow Effect */}
                  {isCurrent && (
                    <div className="absolute inset-0 rounded-full bg-primary/30 animate-ping" />
                  )}
                </div>
                
                {/* Step Title */}
                <div className="mt-3 text-center px-2">
                  <div
                    className={cn(
                      "text-sm font-medium transition-all duration-300",
                      isCompleted && "text-foreground font-semibold",
                      isCurrent && "text-foreground font-bold text-base",
                      isPending && "text-foreground-muted"
                    )}
                  >
                    {step.title}
                  </div>
                  
                  {/* Step Number/Status Indicator */}
                  <div
                    className={cn(
                      "text-xs mt-1 transition-all duration-300",
                      isCompleted && "text-foreground-muted",
                      isCurrent && "text-foreground font-medium",
                      isPending && "text-foreground-subtle"
                    )}
                  >
                    {isCompleted ? "Complete" : isCurrent ? "In Progress" : `Step ${step.id}`}
                  </div>
                </div>
                
                {/* Completion Celebration */}
                {isCompleted && (
                  <div className="absolute -top-2 -right-2 w-4 h-4 bg-success rounded-full animate-bounce">
                    <div className="absolute inset-0 bg-success rounded-full animate-ping opacity-75" />
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}