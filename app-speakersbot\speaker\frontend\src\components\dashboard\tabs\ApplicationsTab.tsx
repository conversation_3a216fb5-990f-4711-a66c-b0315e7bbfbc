import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  FileText, 
  ExternalLink, 
  MessageSquare
} from 'lucide-react';
import { FilledFormModal } from '@/components/ui/filled-form-modal';

export function ApplicationsTab() {
  const [isFormModalOpen, setIsFormModalOpen] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState('');
  
  const [applications] = useState([
    {
      id: 1,
      title: 'Tech Innovation Summit 2024',
      organizer: 'Innovation Corp',
      status: 'Applied',
      appliedDate: '2024-01-15',
      deadline: '2024-02-28',
      fee: '$3,500',
      statusHistory: [
        { status: 'Applied', date: '2024-01-15', note: 'Application submitted with portfolio' }
      ],
      proofLinks: [
        { type: 'Event Registration Form', url: 'https://techinnovationsummit.com/register' },
        { type: 'Event Website', url: 'https://techinnovationsummit.com' }
      ],
      docs: ['Speaker_Bio.pdf', 'Headshot.jpg'],
      organizerReplies: [],
      nextFollowUp: '2024-02-01'
    },
    {
      id: 2,
      title: 'DevOps Conference 2024',
      organizer: 'Cloud Native Foundation',
      status: 'Interviewing',
      appliedDate: '2024-01-10',
      deadline: '2024-02-20',
      fee: '$4,200',
      statusHistory: [
        { status: 'Applied', date: '2024-01-10', note: 'Initial application' },
        { status: 'Interviewing', date: '2024-01-25', note: 'Moved to interview stage' }
      ],
      proofLinks: [
        { type: 'Event Registration Form', url: 'https://devopsconf2024.com/register' },
        { type: 'Event Website', url: 'https://devopsconf2024.com' }
      ],
      docs: ['Technical_Resume.pdf', 'Sample_Talk.pptx'],
      organizerReplies: [
        { date: '2024-01-25', message: 'Great application! We\'d like to schedule an interview.', from: 'Sarah Johnson' }
      ],
      nextFollowUp: '2024-02-05'
    },
    {
      id: 3,
      title: 'AI Ethics Workshop',
      organizer: 'Ethics Institute',
      status: 'Decision',
      appliedDate: '2024-01-05',
      deadline: '2024-02-15',
      fee: '$2,800',
      statusHistory: [
        { status: 'Applied', date: '2024-01-05', note: 'Application submitted' },
        { status: 'Interviewing', date: '2024-01-18', note: 'Phone interview completed' },
        { status: 'Decision', date: '2024-01-30', note: 'Awaiting final decision' }
      ],
      proofLinks: [
        { type: 'Event Registration Form', url: 'https://aiethicsworkshop.org/register' },
        { type: 'Event Website', url: 'https://aiethicsworkshop.org' }
      ],
      docs: ['Ethics_Presentation.pdf'],
      organizerReplies: [
        { date: '2024-01-18', message: 'Interview went well. Decision expected by Feb 5th.', from: 'Dr. Michael Chen' }
      ],
      nextFollowUp: '2024-02-05'
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Applied': return 'default';
      case 'Interviewing': return 'secondary';
      case 'Decision': return 'outline';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Applied': return <Clock className="h-4 w-4" />;
      case 'Interviewing': return <AlertCircle className="h-4 w-4" />;
      case 'Decision': return <CheckCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const addFollowUpReminder = (applicationId: number, days: number) => {
    const newDate = new Date();
    newDate.setDate(newDate.getDate() + days);
    console.log(`Follow-up reminder set for application ${applicationId} on ${newDate.toISOString().split('T')[0]}`);
  };

  return (
    <div className="space-y-6">
      <Card className="bg-surface">
        <CardHeader>
          <CardTitle>Applications Tracker</CardTitle>
          <CardDescription>
            Monitor your speaking opportunity applications and their progress
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {applications.map((app) => (
              <div key={app.id} className="border border-border rounded-lg p-6 bg-card">
                {/* Application Header */}
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h3 className="text-lg font-semibold text-foreground">{app.title}</h3>
                    <p className="text-sm text-muted-foreground">{app.organizer}</p>
                  </div>
                  <div className="text-right">
                    <Badge variant={getStatusColor(app.status)} className="mb-2">
                      {getStatusIcon(app.status)}
                      <span className="ml-1">{app.status}</span>
                    </Badge>
                    <p className="text-sm font-medium text-primary">{app.fee}</p>
                  </div>
                </div>

                {/* Timeline */}
                <div className="mb-4">
                  <h4 className="text-sm font-medium text-foreground mb-2">Status Timeline</h4>
                  <div className="space-y-2">
                    {app.statusHistory.map((entry, index) => (
                      <div key={index} className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-primary rounded-full mt-2"></div>
                        <div>
                          <p className="text-sm font-medium text-foreground">{entry.status}</p>
                          <p className="text-xs text-muted-foreground">{entry.date} - {entry.note}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Proof Links & Docs */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                    <h4 className="text-sm font-medium text-foreground mb-2">Event Links</h4>
                    <div className="space-y-1">
                      {app.proofLinks.map((link, index) => (
                        <div key={index} className="flex items-center gap-2">
                          <ExternalLink className="h-3 w-3" />
                          <a href={link.url} target="_blank" rel="noopener noreferrer" className="text-xs text-primary hover:underline">
                            {link.type}
                          </a>
                        </div>
                      ))}
                    </div>
                  </div>
                  <div>
                    <h4 className="text-sm font-medium text-foreground mb-2">Uploaded Documents</h4>
                    <div className="space-y-1">
                      {app.docs.map((doc, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          className="h-7 text-xs justify-start gap-2"
                          onClick={() => {
                            setSelectedDocument(doc);
                            setIsFormModalOpen(true);
                          }}
                        >
                          <FileText className="h-3 w-3" />
                          {doc}
                        </Button>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Organizer Replies */}
                {app.organizerReplies.length > 0 && (
                  <div className="mb-4">
                    <h4 className="text-sm font-medium text-foreground mb-2">Organizer Replies</h4>
                    <div className="space-y-2">
                      {app.organizerReplies.map((reply, index) => (
                        <div key={index} className="bg-surface p-3 rounded-md">
                          <div className="flex items-center gap-2 mb-1">
                            <MessageSquare className="h-3 w-3" />
                            <span className="text-xs font-medium">{reply.from}</span>
                            <span className="text-xs text-muted-foreground">{reply.date}</span>
                          </div>
                          <p className="text-sm text-foreground">{reply.message}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

              </div>
            ))}
          </div>
        </CardContent>
      </Card>
      
      {/* Filled Form Modal */}
      <FilledFormModal
        isOpen={isFormModalOpen}
        onClose={() => {
          setIsFormModalOpen(false);
          setSelectedDocument('');
        }}
        fileName={selectedDocument}
      />
    </div>
  );
}