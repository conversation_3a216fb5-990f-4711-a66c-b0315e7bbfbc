class CustomError extends Error {
  constructor(statusCode = 500, message, actualError) {
    super();
    // assign the error class name in your custom error
    this.name = this.constructor.name

    if (!message) message = 'Something went wrong please try again'
    this.message = message
    this.statusCode = statusCode // Changed from 'code' to 'statusCode'
    this.code = statusCode // Keep both for compatibility
    if (actualError) console.log("Original Error:", actualError)
    // this.actualError = actualError
  }
}

module.exports = CustomError    