const stripeController = require("../controllers/stripe-controller");
const verifyToken = require("../middlewares/verify-token");
const router = require("express").Router();

module.exports = (app) => {

    // ------------------------- stripe -------------------------

    // create stripe checkout session
    router.post("/checkout", stripeController.checkout);
    router.post("/cancel-subscription", stripeController.cancelSubscription);
    router.post('/update-subscription', stripeController.updateSubscription);
    app.use("/admin/api/v1/payment", router);

}