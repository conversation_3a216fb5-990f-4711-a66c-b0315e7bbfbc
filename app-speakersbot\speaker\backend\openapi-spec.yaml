openapi: 3.0.3
info:
  title: SpeakersBot Admin API
  description: |
    Comprehensive API documentation for the SpeakersBot Admin System.
    This API provides endpoints for managing speakers, opportunities, users, roles, subscriptions, and more.
    
    ## Authentication
    Most endpoints require JWT authentication. Include the token in the Authorization header:
    ```
    Authorization: Bearer <your-jwt-token>
    ```
    
    ## Rate Limiting
    API requests are rate-limited to prevent abuse.
    
    ## Error Handling
    All endpoints return standardized error responses with appropriate HTTP status codes.
  version: 1.0.0
  contact:
    name: SpeakersBot Admin System
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:3000
    description: Development server
  - url: https://api.speakersbot.com
    description: Production server

security:
  - BearerAuth: []

tags:
  - name: Authentication
    description: User authentication and authorization
  - name: Users
    description: User management operations
  - name: Roles
    description: Role and permission management
  - name: Speakers
    description: Speaker profile management
  - name: Opportunities
    description: Speaking opportunity management
  - name: Subscriptions
    description: Subscription and payment management
  - name: Gamification
    description: Points and gamification system
  - name: Pricing Plans
    description: Pricing plan management
  - name: Settings
    description: Application settings
  - name: Stripe
    description: Stripe payment integration

paths:
  # Authentication Endpoints
  /auth/login:
    post:
      tags:
        - Authentication
      summary: User login
      description: Authenticate user credentials and return JWT token
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - email
                - password
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                password:
                  type: string
                  format: password
                  example: "securePassword123"
      responses:
        '200':
          description: Login successful
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/LoginResponse'
        '400':
          description: Bad request
          content:
            application/json:
              example:
                status: 400
                message: "Bad request"
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '401':
          description: Invalid password
          content:
            application/json:
              example:
                status: 401
                message: "Password is incorrect"
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: User not found
          content:
            application/json:
              example:
                status: 404
                message: "Not found."
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              example:
                status: 500
                message: "Internal server error."
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # User Management Endpoints
  /api/upsertUser:
    post:
      tags:
        - Users
      summary: Upsert user
      description: |
        Create a new user or update an existing user.
        - If `id` is provided in the request body, the user with that ID will be updated (fields: name, password, role, status).
        - If no `id` is provided, a new user will be created.
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: User ID (for update)
                  example: 1
                name:
                  type: string
                  description: User's full name
                  example: "John Doe"
                email:
                  type: string
                  format: email
                  description: User's email address
                  example: "<EMAIL>"
                password:
                  type: string
                  format: password
                  description: User's password
                  example: "securePassword123"
                phone:
                  type: string
                  description: User's phone number
                  example: "+1234567890"
                roleId:
                  type: integer
                  description: Role ID to assign
                  example: 2
                status:
                  type: boolean
                  description: User active status
                  example: true
      responses:
        '200':
          description: User upserted successfully
          content:
            application/json:
              example:
                success: true
                message: "User created/updated successfully"
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: Bad request (validation, missing fields, email exists)
          content:
            application/json:
              example:
                status: 400
                message: "User with this email already exists"
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error or validation error
          content:
            application/json:
              example:
                status: 500
                message: "Validation error: Validation len on password failed"
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/deleteUser/{id}:
    delete:
      tags:
        - Users
      summary: Delete user
      description: Soft delete a user from the system
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: User ID to delete
      responses:
        '200':
          description: User deleted successfully
          content:
            application/json:
              example:
                success: true
                message: "User deleted successfully"
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: Bad request
          content:
            application/json:
              example:
                status: 400
                message: "Bad request"
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Access denied
          content:
            application/json:
              example:
                status: 404
                message: "User not found"
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '400':
          description: User not found
          content:
            application/json:
              example:
                status: 400
                message: "UserId is required"
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /api/getUser/{id}:
    get:
      tags:
        - Users
      summary: Get user by ID
      description: Retrieve a specific user with role and permissions
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: User ID to retrieve
      responses:
        '200':
          description: User retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserResponse'
        '400':
          description: Bad request
          content:
            application/json:
              example:
                status: 400
                message: "Bad request"
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '403':
          description: Access denied
          content:
            application/json:
              example:
                status: 404
                message: "User not found"
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '400':
          description: User not found
          content:
            application/json:
              example:
                status: 400
                message: "UserId is required"
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /api/getUser/{id}:

  /api/getUsers:
    get:
      tags:
        - Users
      summary: Get all users
      description: Retrieve paginated list of users with filtering and search
      parameters:
        - $ref: '#/components/parameters/Page'
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Search'
        - name: filter
          in: query
          schema:
            type: string
          description: JSON filter object for users
          example: '{"isActive":true,"roleId":2}'
        - name: sort
          in: query
          schema:
            type: string
          description: JSON sort object
          example: '{"column":"name","direction":"ASC"}'
      responses:
        '200':
          description: Users retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UsersListResponse'
        '400':
          description: Bad request
          content:
            application/json:
              example:
                status: 400
                message: "Bad request"
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  # Role Management Endpoints
  /api/createRole:
    post:
      tags:
        - Roles
      summary: Create new role
      description: Create a new role with permissions
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateRoleRequest'
      responses:
        '201':
          description: Role created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          description: Validation error (role exists or invalid permissions)
          content:
            application/json:
              examples:
                roleExists:
                  summary: Role already exists
                  value:
                    status: false
                    message: "Role already exists"
                invalidPermissions:
                  summary: No valid permissions found for given IDs
                  value:
                    status: false
                    message: "No valid permissions found for given IDs"
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal server error
          content:
            application/json:
              example:
                status: false
                message: "Error creating role"
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/updateRole/{id}:
    put:
      tags:
        - Roles
      summary: Update role
      description: Update an existing role and its permissions
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Role ID to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateRoleRequest'
      responses:
        '200':
          description: Role updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'

  /api/deleteRole:
    delete:
      tags:
        - Roles
      summary: Delete role
      description: Delete a role (soft delete if users are assigned)
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                id:
                  type: integer
                  description: Role ID to delete
      responses:
        '200':
          description: Role deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '403':
          $ref: '#/components/responses/Forbidden'
        '409':
          description: Cannot delete role with assigned users
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          $ref: '#/components/responses/InternalError'

  /api/getRoles:
    get:
      tags:
        - Roles
      summary: Get all roles
      description: Retrieve all roles with their permissions
      security: []
      responses:
        '200':
          description: Roles retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RolesListResponse'
        '500':
          $ref: '#/components/responses/InternalError'

  # Speaker Management Endpoints
  /api/speakers:
    get:
      tags:
        - Speakers
      summary: Get all speakers
      description: Retrieve paginated list of speakers with filtering and search
      parameters:
        - $ref: '#/components/parameters/Page'
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Search'
        - name: filter
          in: query
          schema:
            type: string
          description: JSON filter object for speakers
        - name: sort
          in: query
          schema:
            type: string
          description: JSON sort object
      responses:
        '200':
          description: Speakers retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SpeakersListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'

  /api/speaker/{id}:
    get:
      tags:
        - Speakers
      summary: Get speaker by ID
      description: Retrieve a specific speaker's detailed information
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Speaker ID to retrieve
      responses:
        '200':
          description: Speaker retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SpeakerResponse'
    put:
      tags:
        - Speakers
      summary: Update speaker
      description: Update speaker information
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Speaker ID to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateSpeakerRequest'
      responses:
        '200':
          description: Speaker updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          $ref: '#/components/responses/BadRequest'

  /api/export/speakers:
    get:
      tags:
        - Speakers
      summary: Export speakers
      description: Export speakers data to CSV format
      security: []
      responses:
        '200':
          description: CSV file with speakers data
          content:
            text/csv:
              schema:
                type: string
                format: binary
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'

  # Opportunity Management Endpoints
  /api/opportunities:
    get:
      tags:
        - Opportunities
      summary: Get all opportunities
      description: Retrieve paginated list of opportunities with filtering, search, and date range
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/Page'
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Search'
        - name: dateRange
          in: query
          schema:
            type: string
          description: JSON date range filter
          example: '{"start_date":"2024-10-09","end_date":"2024-11-09"}'
        - name: filter
          in: query
          schema:
            type: string
          description: JSON filter object for opportunities
        - name: sort
          in: query
          schema:
            type: string
          description: JSON sort object
      responses:
        '200':
          description: Opportunities retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OpportunitiesListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'

  /api/opportunity:
    post:
      tags:
        - Opportunities
      summary: Create new opportunity
      description: Create a new speaking opportunity
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateOpportunityRequest'
      responses:
        '201':
          description: Opportunity created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OpportunityResponse'
        '400':
          $ref: '#/components/responses/BadRequest'

  /api/opportunity/{id}:
    get:
      tags:
        - Opportunities
      summary: Get opportunity by ID
      description: Retrieve a specific opportunity with associated speakers
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Opportunity ID to retrieve
        - name: status
          in: query
          schema:
            type: string
            enum: [interested, applied, accepted, rejected]
          description: Filter speakers by status
        - name: sort
          in: query
          schema:
            type: string
          description: Sort speakers by score fields
      responses:
        '200':
          description: Opportunity retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/OpportunityDetailResponse'

    put:
      tags:
        - Opportunities
      summary: Update opportunity
      description: Update an existing opportunity
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Opportunity ID to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateOpportunityRequest'
      responses:
        '200':
          description: Opportunity updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          $ref: '#/components/responses/BadRequest'

  /api/export/opportunities:
    post:
      tags:
        - Opportunities
      summary: Export opportunities
      description: Export selected opportunities to CSV format
      security: []
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                ids:
                  type: array
                  items:
                    type: integer
                  description: Array of opportunity IDs to export
      responses:
        '200':
          description: CSV file with opportunities data
          content:
            text/csv:
              schema:
                type: string
                format: binary
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'

  # Subscription Management Endpoints
  /api/subscriptions:
    get:
      tags:
        - Subscriptions
      summary: Get subscriptions
      description: Retrieve subscriptions grouped by speakers
      parameters:
        - $ref: '#/components/parameters/Page'
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Search'
      responses:
        '200':
          description: Subscriptions retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SubscriptionsResponse'
        '500':
          $ref: '#/components/responses/InternalError'

  # Gamification Endpoints
  /api/gamification-points:
    get:
      tags:
        - Gamification
      summary: Get gamification points
      description: |
        Retrieve gamification points data grouped by speakers with filtering options.
        
        **Filters Available:**
        - **source**: Search by source (partial match, case-insensitive)
        - **name**: Search by speaker name (partial match, case-insensitive) 
        - **type**: Filter by type - exact match for "earn" or "redeem"
      security:
        - BearerAuth: []
      parameters:
        - $ref: '#/components/parameters/Page'
        - $ref: '#/components/parameters/Limit'
        - $ref: '#/components/parameters/Search'
        - name: source
          in: query
          schema:
            type: string
          description: Search by source (partial match, case-insensitive)
          example: "registration"
        - name: name
          in: query
          schema:
            type: string
          description: Search by speaker name (partial match, case-insensitive)
          example: "john"
        - name: type
          in: query
          schema:
            type: string
            enum: [earn, redeem]
          description: Filter by gamification type (exact match)
          example: "earn"
      responses:
        '200':
          description: Points data retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/GamificationResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          description: Unauthorized - Invalid or missing JWT token
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          $ref: '#/components/responses/InternalError'

  # Pricing Plan Endpoints
  /api/create-plan:
    post:
      tags:
        - Pricing Plans
      summary: Create pricing plan
      description: Create a new pricing plan with Stripe integration
      security:
        - BearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreatePricingPlanRequest'
      responses:
        '200':
          description: Pricing plan created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Success"
                  data:
                    $ref: '#/components/schemas/PricingPlan'
        '400':
          description: Bad request - Missing required fields
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 400
                      message:
                        type: string
                        example: "Missing required fields"
                      error:
                        type: string
                        example: "Bad request."

  /api/plans:
    get:
      tags:
        - Pricing Plans
      summary: Get all pricing plans
      description: Retrieve all active pricing plans
      security:
        - BearerAuth: []
      responses:
        '200':
          description: Pricing plans retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Success"
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/PricingPlan'
        '404':
          description: No active plans found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "No active plans found"
                  data:
                    type: array
                    example: []

  /api/update-plan/{id}:
    put:
      tags:
        - Pricing Plans
      summary: Update pricing plan
      description: Update an existing pricing plan and sync with Stripe
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Pricing plan ID to update
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdatePricingPlanRequest'
      responses:
        '200':
          description: Pricing plan updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Success"
                  data:
                    $ref: '#/components/schemas/PricingPlan'
        '400':
          description: Bad request - Update failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Plan update failed"
        '404':
          description: Plan not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: "Plan not found"
                      error:
                        type: string
                        example: "Not found."

  /api/delete-plan/{id}:
    delete:
      tags:
        - Pricing Plans
      summary: Delete pricing plan
      description: Deactivate a pricing plan (soft delete)
      security:
        - BearerAuth: []
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Pricing plan ID to delete
      responses:
        '200':
          description: Pricing plan deactivated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: "Success"
                  data:
                    $ref: '#/components/schemas/PricingPlan'
        '400':
          description: Bad request - Deletion failed
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  message:
                    type: string
                    example: "Plan deletion failed"
        '404':
          description: Plan not found
          content:
            application/json:
              schema:
                type: object
                properties:
                  status:
                    type: boolean
                    example: false
                  error:
                    type: object
                    properties:
                      code:
                        type: integer
                        example: 404
                      message:
                        type: string
                        example: "Plan not found"
                      error:
                        type: string
                        example: "Not found."

  # Settings Endpoints
  /api/settings:
    get:
      tags:
        - Settings
      summary: Get all settings
      description: Retrieve all application settings
      responses:
        '200':
          description: Settings retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SettingsResponse'
        '500':
          $ref: '#/components/responses/InternalError'

    post:
      tags:
        - Settings
      summary: Save setting
      description: Create or update application settings
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SaveSettingRequest'
      responses:
        '200':
          description: Setting saved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SuccessResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalError'

  /api/settings/{id}:
    get:
      tags:
        - Settings
      summary: Get setting by ID
      description: Retrieve a specific setting
      parameters:
        - name: id
          in: path
          required: true
          schema:
            type: integer
          description: Setting ID to retrieve
      responses:
        '200':
          description: Setting retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SettingResponse'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalError'

  # Stripe Payment Endpoints
  /api/stripe/checkout:
    post:
      tags:
        - Stripe
      summary: Create checkout session
      description: Create a Stripe checkout session for payment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CheckoutRequest'
      responses:
        '200':
          description: Checkout session created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CheckoutResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalError'

  /api/stripe/customers:
    post:
      tags:
        - Stripe
      summary: Get Stripe customers
      description: Retrieve Stripe customer information
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                customerId:
                  type: string
                  description: Stripe customer ID
      responses:
        '200':
          description: Customers retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StripeCustomersResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalError'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token for authentication

  parameters:
    Page:
      name: page
      in: query
      schema:
        type: integer
        minimum: 1
        default: 1
      description: Page number for pagination

    Limit:
      name: limit
      in: query
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10
      description: Number of items per page

    Search:
      name: search
      in: query
      schema:
        type: string
      description: Search term for filtering results

  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Forbidden:
      description: Access forbidden
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    InternalError:
      description: Internal server error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'

  schemas:
    # Common Response Schemas
    SuccessResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "completed successfully"

    ErrorResponse:
      type: object
      properties:
        status:
          type: boolean
          example: false
        message:
          type: string
          example: "Error message description"
        error:
          type: string
          example: "Bad request"

    PaginationData:
      type: object
      properties:
        totalPage:
          type: integer
          example: 10
        page:
          type: integer
          example: 1
        total:
          type: integer
          example: 100
        limit:
          type: integer
          example: 10

    # Authentication Schemas
    LoginResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Login successful"
        data:
          type: object
          properties:
            token:
              type: string
              example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

    # User Schemas
    User:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "John Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        phone:
          type: string
          example: "+1234567890"
        isActive:
          type: boolean
          example: true
        roleId:
          type: integer
          example: 2
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    CreateUserRequest:
      type: object
      required:
        - name
        - email
        - password
      properties:
        name:
          type: string
          example: "John Doe"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        password:
          type: string
          format: password
          example: "securePassword123"
        phone:
          type: string
          example: "+1234567890"
        roleId:
          type: integer
          example: 2

    UserResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "User retrieved successfully"
        data:
          $ref: '#/components/schemas/User'

    UsersListResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Users retrieved successfully"
        data:
          type: object
          properties:
            users:
              type: array
              items:
                $ref: '#/components/schemas/User'
        pagination:
          $ref: '#/components/schemas/PaginationData'

    # Role Schemas
    Role:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "Administrator"
        description:
          type: string
          example: "Full system access"
        permissions:
          type: array
          items:
            type: string
          example: ["create_user", "delete_user", "manage_roles"]

    CreateRoleRequest:
      type: object
      required:
        - name
        - permissions
      properties:
        name:
          type: string
          example: "Editor"
        description:
          type: string
          example: "Content editing permissions"
        permissions:
          type: array
          items:
            type: string
          example: [1, 2, 3]

    UpdateRoleRequest:
      type: object
      properties:
        name:
          type: string
          example: "Senior Editor"
        description:
          type: string
          example: "Advanced content editing permissions"
        permissions:
          type: array
          items:
            type: string
          example: ["create_content", "edit_content", "publish_content"]

    RolesListResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Roles retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/Role'

    # Speaker Schemas
    Speaker:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "Jane Smith"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        bio:
          type: string
          example: "Experienced technology speaker"
        expertise:
          type: array
          items:
            type: string
          example: ["AI", "Machine Learning", "Data Science"]
        location:
          type: string
          example: "San Francisco, CA"
        website:
          type: string
          format: uri
          example: "https://janesmith.com"

    UpdateSpeakerRequest:
      type: object
      properties:
        name:
          type: string
          example: "Jane Smith"
        email:
          type: string
          format: email
          example: "<EMAIL>"
        bio:
          type: string
          example: "Updated bio information"
        expertise:
          type: array
          items:
            type: string
          example: ["AI", "Machine Learning"]

    SpeakerResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Speaker retrieved successfully"
        data:
          $ref: '#/components/schemas/Speaker'

    SpeakersListResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Speakers retrieved successfully"
        data:
          type: object
          properties:
            speakers:
              type: array
              items:
                $ref: '#/components/schemas/Speaker'
        pagination:
          $ref: '#/components/schemas/PaginationData'

    # Opportunity Schemas
    Opportunity:
      type: object
      properties:
        id:
          type: integer
          example: 1
        title:
          type: string
          example: "Tech Conference 2024"
        organization:
          type: string
          example: "TechCorp"
        event_type:
          type: string
          example: "Conference"
        description:
          type: string
          example: "Annual technology conference"
        start_date:
          type: string
          format: date
          example: "2024-12-01"
        end_date:
          type: string
          format: date
          example: "2024-12-03"
        city:
          type: string
          example: "San Francisco"
        state:
          type: string
          example: "CA"
        country:
          type: string
          example: "USA"
        is_active:
          type: boolean
          example: true

    CreateOpportunityRequest:
      type: object
      required:
        - title
        - start_date
        - end_date
      properties:
        title:
          type: string
          example: "Tech Conference 2024"
        organization:
          type: string
          example: "TechCorp"
        event_type:
          type: string
          example: "Conference"
        description:
          type: string
          example: "Annual technology conference"
        start_date:
          type: string
          format: date
          example: "2024-12-01"
        end_date:
          type: string
          format: date
          example: "2024-12-03"
        city:
          type: string
          example: "San Francisco"
        is_active:
          type: boolean
          example: true

    UpdateOpportunityRequest:
      type: object
      properties:
        title:
          type: string
          example: "Updated Tech Conference 2024"
        description:
          type: string
          example: "Updated description"
        is_active:
          type: boolean
          example: false

    OpportunityResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Opportunity created successfully"
        data:
          $ref: '#/components/schemas/Opportunity'

    OpportunityDetailResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Opportunity retrieved successfully"
        data:
          type: object
          properties:
            opportunity:
              $ref: '#/components/schemas/Opportunity'
            speakerCount:
              type: integer
              example: 5

    OpportunitiesListResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Opportunities retrieved successfully"
        data:
          type: object
          properties:
            opportunities:
              type: array
              items:
                $ref: '#/components/schemas/Opportunity'
            summary:
              type: object
              properties:
                totalOpportunities:
                  type: integer
                  example: 50
                active:
                  type: integer
                  example: 30
                totalMatches:
                  type: integer
                  example: 100
                acceptedMatches:
                  type: integer
                  example: 25
        pagination:
          $ref: '#/components/schemas/PaginationData'

    # Subscription Schemas
    SubscriptionsResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Subscriptions retrieved successfully"
        data:
          type: array
          items:
            type: object
            properties:
              speakerName:
                type: string
                example: "John Doe"
              subscriptions:
                type: array
                items:
                  type: object
                  properties:
                    id:
                      type: integer
                      example: 1
                    planName:
                      type: string
                      example: "Premium Plan"
                    status:
                      type: string
                      example: "active"

    # Gamification Schemas
    GamificationResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Gamification data fetched successfully"
        data:
          type: object
          properties:
            gamification_data:
              type: array
              items:
                type: object
                properties:
                  speaker:
                    type: string
                    example: "John Doe"
                    description: "Speaker name"
                  points:
                    type: array
                    items:
                      type: object
                      properties:
                        id:
                          type: integer
                          example: 1
                        speaker_id:
                          type: integer
                          example: 1
                        plan_id:
                          type: integer
                          example: 1
                        source:
                          type: string
                          example: "registration"
                          description: "Source of the points (registration, event, etc.)"
                        type:
                          type: string
                          enum: [earn, redeem]
                          example: "earn"
                          description: "Type of gamification action"
                        points:
                          type: integer
                          example: 100
                          description: "Points earned or redeemed"
                        created_at:
                          type: string
                          format: date-time
                          example: "2024-01-15T10:30:00Z"
                        updated_at:
                          type: string
                          format: date-time
                          example: "2024-01-15T10:30:00Z"
            pageData:
              $ref: '#/components/schemas/PaginationData'

    # Pricing Plan Schemas
    PricingPlan:
      type: object
      properties:
        id:
          type: integer
          example: 1
        name:
          type: string
          example: "Premium Plan"
        description:
          type: string
          example: "Full access to all features"
        amount:
          type: number
          format: float
          example: 99.99
          description: "Price amount in USD"
        currency:
          type: string
          example: "usd"
          description: "Currency code"
        billing_interval:
          type: string
          enum: ["month", "year"]
          example: "month"
          description: "Billing interval for the subscription"
        stripe_product_id:
          type: string
          example: "prod_1234567890"
          description: "Stripe product ID"
        stripe_price_id:
          type: string
          example: "price_1234567890"
          description: "Stripe price ID"
        is_active:
          type: string
          enum: ["0", "1"]
          example: "1"
          description: "Whether the plan is active (1) or inactive (0)"
        created_at:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"
        updated_at:
          type: string
          format: date-time
          example: "2024-01-15T10:30:00Z"

    CreatePricingPlanRequest:
      type: object
      required:
        - name
        - billing_interval
        - amount
      properties:
        name:
          type: string
          example: "Basic Plan"
          description: "Name of the pricing plan"
        description:
          type: string
          example: "Essential features for small teams"
          description: "Description of the pricing plan"
        amount:
          type: number
          format: float
          example: 29.99
          minimum: 0.01
          description: "Price amount in USD (will be converted to cents for Stripe)"
        billing_interval:
          type: string
          enum: ["month", "year"]
          example: "month"
          description: "Billing interval for the subscription"

    UpdatePricingPlanRequest:
      type: object
      properties:
        name:
          type: string
          example: "Updated Basic Plan"
          description: "Updated name of the pricing plan"
        description:
          type: string
          example: "Updated description with new features"
          description: "Updated description of the pricing plan"
        amount:
          type: number
          format: float
          example: 39.99
          minimum: 0.01
          description: "Updated price amount in USD"
        billing_interval:
          type: string
          enum: ["month", "year"]
          example: "month"
          description: "Updated billing interval for the subscription"

    PricingPlansResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Pricing plans retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/PricingPlan'

    # Settings Schemas
    Setting:
      type: object
      properties:
        id:
          type: integer
          example: 1
        key:
          type: string
          example: "app_name"
        value:
          type: string
          example: "SpeakersBot"
        description:
          type: string
          example: "Application name setting"

    SaveSettingRequest:
      type: object
      required:
        - key
        - value
      properties:
        key:
          type: string
          example: "theme_color"
        value:
          type: string
          example: "#007bff"
        description:
          type: string
          example: "Primary theme color"

    SettingResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Setting retrieved successfully"
        data:
          $ref: '#/components/schemas/Setting'

    SettingsResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Settings retrieved successfully"
        data:
          type: array
          items:
            $ref: '#/components/schemas/Setting'

    # Stripe Schemas
    CheckoutRequest:
      type: object
      required:
        - plan_id
        - customer_email
      properties:
        plan_id:
          type: integer
          example: 1
        customer_email:
          type: string
          example: "<EMAIL>"
        referral_code:
          type: string
          example: "referralCode123"

    CheckoutResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Checkout session created successfully"
        data:
          type: object
          properties:
            sessionId:
              type: string
              example: "cs_test_123456789"
            url:
              type: string
              format: uri
              example: "https://checkout.stripe.com/pay/cs_test_123456789"

    StripeCustomersResponse:
      type: object
      properties:
        status:
          type: boolean
          example: true
        message:
          type: string
          example: "Customers retrieved successfully"
        data:
          type: array
          items:
            type: object
            properties:
              id:
                type: string
                example: "cus_123456789"
              email:
                type: string
                format: email
                example: "<EMAIL>"
              name:
                type: string
                example: "John Doe"
