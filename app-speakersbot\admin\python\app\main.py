from fastapi import FastAP<PERSON>
from fastapi import Request
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from app.config.config import config
from app.background_tasks.scheduler import OpportunityScheduler
from app.background_tasks.scrape_worker import run_worker_loop
from app.config.logger import cleanup_logging
import threading

# Import routers from api folder
from app.controllers.prefill_controller import router as fill_form_router
from app.controllers.matches_controller import router as simple_hybrid_router
from app.controllers.affiliates_controller import router as affiliate_router
from app.controllers.dsa_reports_controller import router as digital_form_router
from app.controllers.discovery_controller import router as discovery_worker_router
from app.controllers.voiceprint_controller import router as voiceprint_router
from app.config.logger import get_logger
logger = get_logger(__name__, file_name="scraper.log")

@asynccontextmanager
async def lifespan(app: FastAPI):

    scheduler = OpportunityScheduler()
    scheduler.start_scheduler()
    # Start the worker if enabled
    worker_thread = None
    if config.WORKER_ENABLED:
        worker_thread = threading.Thread(target=run_worker_loop, daemon=True)
        worker_thread.start()
    else:
        logger.info("Worker is disabled in configuration")
    
    yield
    # Stop the scheduler when app shuts down
    scheduler.stop_scheduler()
    # Clean up logging handlers
    cleanup_logging()
    logger.info("Logging handlers cleaned up")

app = FastAPI(lifespan=lifespan)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allows all origins
    allow_credentials=True,
    allow_methods=["*"],  # Allows all methods
    allow_headers=["*"],  # Allows all headers
)

app.include_router(fill_form_router)
app.include_router(simple_hybrid_router)
app.include_router(affiliate_router)
app.include_router(digital_form_router)
app.include_router(discovery_worker_router)
app.include_router(voiceprint_router)

@app.get("/")
def read_root():
    return {"message": "Ping Successfully"}