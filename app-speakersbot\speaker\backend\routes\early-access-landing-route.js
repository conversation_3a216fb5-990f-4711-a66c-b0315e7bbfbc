
const publicController = require("../controllers/early-access-landing-controller");
const router = require("express").Router();

module.exports = (app) => {

    // Public routes (no authentication required)
    router.post("/landing-register", publicController.landingPageSpeaker);

    // fetch all the primary_Category
    router.get("/primary-category", publicController.getPrimaryCategories);

    // fetch all the sub_Category based on primary category id
    router.get("/sub-category/:primaryCategoryId", publicController.getSubCategories);

    app.use("/speaker/api/early-access", router);

};