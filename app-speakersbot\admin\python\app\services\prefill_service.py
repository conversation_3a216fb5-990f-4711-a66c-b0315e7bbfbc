from fastapi import HTTPException
from app.helpers.database_manager import db_manager
from app.models.speakers import Speaker
from app.models.speaker_details import SpeakerDetails
from app.models.opportunities import SpeakerOpportunity
from app.models.opportunity_form import OpportunityForm
from app.models.speaker_opportunities import SpeakerOpportunity as SpeakerOpportunityMatch
from app.helpers.email_template_helper import create_speaker_email_template
from openai import OpenAI
from app.config.config import config
import json
import logging

# Set up logging
logger = logging.getLogger(__name__)
openai_client = OpenAI(api_key=config.OPENAI_API_KEY)

def fill_form_fields_with_llm(form_fields, speaker_data):
    """
    Use LLM to intelligently fill form fields based on speaker data.
    Args:
        form_fields: List of form field names to fill
        speaker_data: Dictionary containing speaker information
    Returns:
        List of filled field objects
    """
    try:
        # No opportunity data needed - using speaker data only
        
        # Prepare speaker data for LLM using actual field names from speaker_details
        speaker_context = f"""
            Speaker Information:
            - Name: {speaker_data.get('Name', 'N/A')}
            - Email: {speaker_data.get('Email', 'N/A')}
            - Phone Number: {speaker_data.get('Phone Number', 'N/A')}
            - City: {speaker_data.get('City', 'N/A')}
            - State: {speaker_data.get('State', 'N/A')}
            - Country: {speaker_data.get('Country', 'N/A')}
            - Speaker Website: {speaker_data.get('Speaker Website', 'N/A')}
            - LinkedIn: {speaker_data.get('LinkedIn', 'N/A')}
            - Title: {speaker_data.get('Title', 'N/A')}
            - Bio: {speaker_data.get('Bio', 'N/A')}
            - Primary Category: {speaker_data.get('Primary Category', 'N/A')}
            - Subcategory: {speaker_data.get('Subcategory', 'N/A')}
            - Topic: {speaker_data.get('Topic', 'N/A')}
            - Learning Objectives: {speaker_data.get('Learning Objectives', 'N/A')}
            - Takeaways: {speaker_data.get('Takeaways', 'N/A')}
            - Challenges: {speaker_data.get('Challenges', 'N/A')}
            - Preferred Speaker Geography: {speaker_data.get('Preferred Speaker Geography', 'N/A')}
            - Speaker Credentials: {speaker_data.get('Speaker Credentials', 'N/A')}
            - Top Keywords: {speaker_data.get('Top Keywords', 'N/A')}
            - Differentiators: {speaker_data.get('Differentiators', 'N/A')}
            """
        
        # Create prompt for LLM
        prompt = f"""
            You are an expert assistant that fills speaker application forms intelligently. Given the speaker's profile, fill ONLY fields where speaker data is available.

            {speaker_context}

            Form fields to fill: {form_fields}

            CRITICAL REQUIREMENTS:
            1. ONLY fill fields where speaker data is NOT null/empty/N/A - SKIP fields with missing data
            2. Use the EXACT field names provided in the form_fields list
            3. Return ONLY a JSON object with field_name: value pairs for fields with available data
            4. Do not include any explanations or additional text
            5. If speaker data is null/empty/N/A for a field, DO NOT include that field in your response

            FIELD MAPPING GUIDELINES:
            - Name fields: Use speaker's full name, or extract first/last as needed
            - Email fields: Use speaker's email address
            - Phone fields: Use speaker's phone number
            - Company/Organization fields: Use speaker's company
            - Title/Position fields: Use speaker's title
            - Bio/Abstract/Description fields: Use speaker's bio, topic, or create relevant content based on expertise
            - LinkedIn fields: Use speaker's LinkedIn URL
            - Website fields: Use speaker's speaker website
            - Location fields: Use speaker's city, state, country
            - Expertise/Topic fields: Use speaker's primary category, subcategory, or topic
            - Presentation type fields (solo, panel, fireside): Use "Yes" or "No" based on context
            - Session description fields: Create detailed content using bio + topic + expertise

            ABSTRACT/SESSION DESCRIPTION STRATEGY:
            For fields asking for session descriptions, abstracts, or proposals:
            - Combine speaker's bio with their topic/expertise
            - Create compelling session content that showcases their knowledge
            - Use format: "In this session, I will share insights on [topic] based on my experience in [expertise]. Key takeaways include [relevant points from bio]."
            - Minimum 50 words, maximum 200 words

            IMPORTANT: Use the exact field names from this list: {form_fields}
            IMPORTANT: Only include fields where speaker data is available (not null/empty/N/A)

            Example response format (only fields with available data):
            {{
            "your_name_required": "John Smith",
            "your_email_required": "<EMAIL>", 
            "your_company_or_organization_name": "Tech Corp",
            "your_position_in_company": "Senior Developer",
            "provide_a_detailed_description_of_your_proposed_session": "In this session, I will share insights on AI and machine learning based on my 10+ years experience in software development. Key takeaways include practical implementation strategies, common pitfalls to avoid, and real-world case studies from my work at Tech Corp."
            }}
            
            NOTE: If speaker data is missing for any field (like phone, linkedin, etc.), do NOT include that field in the response.
            """
        
        response = openai_client.chat.completions.create(
            model="gpt-4o-mini",
            messages=[{"role": "user", "content": prompt}],
            temperature=0.1
        )
        llm_result = response.choices[0].message.content.strip()
        # Parse LLM response
        try:
            filled_values = json.loads(llm_result)
            # Validate that all fields are filled
            empty_fields = [field for field in form_fields if not filled_values.get(field, "").strip()]
            if empty_fields:
                # Create enhanced prompt for empty fields
                enhanced_prompt = f"""
                    {prompt}

                    ATTENTION: The following fields were left empty and MUST be filled: {empty_fields}

                    For each empty field, provide a meaningful value based on the speaker data:
                    - If it's a description/abstract field, create compelling content using bio + topic
                    - If it's a presentation type field, use "Yes" or "No" 
                    - If it's a confirmation field, repeat the related field value
                    - NEVER leave any field empty - always provide a value

                    Return the complete JSON with ALL fields filled, including the previously empty ones.
                    """
                
                # Retry with enhanced prompt
                retry_response = openai_client.chat.completions.create(
                    model="gpt-4o-mini",
                    messages=[{"role": "user", "content": enhanced_prompt}],
                    temperature=0.3,
                    max_tokens=2000
                )
                
                retry_result = retry_response.choices[0].message.content.strip()
                
                try:
                    filled_values = json.loads(retry_result)
                    # Check again for empty fields
                    final_empty_fields = [field for field in form_fields if not filled_values.get(field, "").strip()]
                    if final_empty_fields:
                        # Fill remaining empty fields with default values
                        for field in final_empty_fields:
                            if any(keyword in field.lower() for keyword in ['description', 'abstract', 'proposal', 'session']):
                                filled_values[field] = f"Session on {speaker_data.get('topic', 'technology')} - {speaker_data.get('bio', 'Expert presentation')}"
                            elif any(keyword in field.lower() for keyword in ['solo', 'panel', 'fireside']):
                                filled_values[field] = "Yes"
                            elif 'confirm' in field.lower():
                                filled_values[field] = filled_values.get(field.replace('confirm_', '').replace('_confirm', ''), '')
                            else:
                                filled_values[field] = "N/A"
                    else:
                        logger.info(f"✅ All fields filled successfully after retry")
                        
                except json.JSONDecodeError as retry_e:
                    logger.error(f"❌ Retry JSON parsing failed: {retry_e}")
                    logger.error(f"📝 Raw retry response: {retry_result}")
                    # Use original filled_values even if retry failed
                    
        except json.JSONDecodeError as e:
            logger.error(f"❌ JSON parsing failed: {e}")
            logger.error(f"📝 Raw LLM response: {llm_result}")
            # Fallback: create empty fields
            filled_values = {}
        
        # Create field objects
        filled_fields = []
        for field_name in form_fields:
            field_value = filled_values.get(field_name, "")
            
            # Determine field type
            field_type = "text"
            if "email" in field_name.lower():
                field_type = "email"
            elif "phone" in field_name.lower() or "tel" in field_name.lower():
                field_type = "tel"
            
            filled_field = {
                "name": field_name,
                "type": field_type,
                "value": str(field_value) if field_value else ""
            }
            filled_fields.append(filled_field)
        return filled_fields
        
    except Exception as e:
        # Fallback: return empty fields
        filled_fields = []
        for field_name in form_fields:
            filled_field = {
                "name": field_name,
                "type": "text",
                "value": ""
            }
            filled_fields.append(filled_field)
        return filled_fields

def prefill_speaker_opportunity(speaker_id: int, opportunity_id: int):
    """
    Prefill speaker opportunity data based on opportunity form type.
    Args:
        speaker_id: ID of the speaker
        opportunity_id: ID of the opportunity
    Returns:
        Dictionary with prefill result
    """
    try:
        with db_manager.get_session() as session:
            # Get speaker details (using speaker_details table like digital PDF service)
            speaker_details = session.query(SpeakerDetails).filter_by(speaker_id=speaker_id).all()
            if not speaker_details:
                raise HTTPException(status_code=404, detail="Speaker details not found")
            # Get opportunity form data
            opportunity_form = session.query(OpportunityForm).filter_by(opportunity_id=opportunity_id).first()
            if not opportunity_form:
                # Check if opportunity exists at all
                opportunity_exists = session.query(SpeakerOpportunity).filter_by(id=opportunity_id).first()
                if not opportunity_exists:
                    return {
                        "success": False,
                        "error": f"Opportunity {opportunity_id} not found in opportunities table",
                        "speaker_id": speaker_id,
                        "opportunity_id": opportunity_id
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Opportunity form not found for opportunity {opportunity_id}. Opportunity exists but has no form data.",
                        "speaker_id": speaker_id,
                        "opportunity_id": opportunity_id
                    }
            
            # Get speaker opportunity match record
            speaker_opportunity = session.query(SpeakerOpportunityMatch).filter_by(
                speaker_id=speaker_id, 
                opportunity_id=opportunity_id
            ).first()
            if not speaker_opportunity:
                return {
                    "success": False,
                    "error": f"Speaker opportunity match not found for speaker {speaker_id} and opportunity {opportunity_id}",
                    "speaker_id": speaker_id,
                    "opportunity_id": opportunity_id
                }
            
            # Convert speaker_details to dict (same approach as digital PDF service)
            speaker_data = {}
            
            # Fields to exclude (same as digital PDF service)
            remove_fields = [
                'When you walk off stage, do you usually feel more energized by the crowd or drained and ready for quiet?',
                'Do you prefer sticking to a clear plan and checklist, or adapting in the moment as things unfold?',
                'What pushes you hardest when preparing a talk — making it flawless, inspiring people, solving problems, or connecting deeply?',
                'When an audience challenges you, do you win them with stories and enthusiasm, with facts and data, or by inviting collaboration?',
                'Do you naturally weave themes like gratitude, fairness, hope, or strategy into your message?',
                'If you weren\'t speaking, would you rather be hands-on building something, exploring ideas, leading a team, or helping people grow?',
                'Headshot',
                'Voice Clip Upload',
                'Video Clip Upload',
                'Logo',
                'Additional Headshots',
                'One-sheet PDF',
                'Sizzle Reel',
                'Past Talk Decks',
                'Press Coverage Links',
                'TED or TEDx Talk Link',
                'TEDx Transcript Upload',
                'Affiliate Program Participation',
                'Live Speaking Fee',
                'Refer New Affiliates',
                'Affiliate Notes'
            ]
            
            # Build speaker_data from speaker_details (same as digital PDF service)
            for detail in speaker_details:
                if detail.key and detail.value:
                    if detail.key not in remove_fields:
                        speaker_data[detail.key] = detail.value
            
            # Print data being passed to LLM
            print(f"Data being passed to LLM: {speaker_data}")
            prefill_data = None
            
            if opportunity_form.type == "form":
                # Handle form type - fill form fields with speaker data
                form_data = opportunity_form.form_data
                # Ensure form_data is parsed as JSON if it's a string
                if isinstance(form_data, str):
                    try:
                        form_data = json.loads(form_data)
                    except json.JSONDecodeError:
                        form_data = {}
                elif form_data is None:
                    form_data = {}
                
                # Handle the new structure where form_data is directly an array of field names
                if isinstance(form_data, list):
                    # Filter out upload/file fields
                    non_upload_fields = []
                    for field_name in form_data:
                        if not any(upload_keyword in field_name.lower() for upload_keyword in 
                                 ['upload', 'file', 'attachment', 'document', 'pdf', 'image', 'photo', 'cv', 'resume']):
                            non_upload_fields.append(field_name)
                    
                    filled_fields = fill_form_fields_with_llm(non_upload_fields, speaker_data)
                    # Create the filled form data structure
                    prefill_data = {
                        "form_url": opportunity_form.form_url or "",
                        "fields": filled_fields
                    }
                else:
                    # Fallback for old structure or simple key-value structure
                    prefill_data = {}
                    for key, value in form_data.items():
                        if key in speaker_data and speaker_data[key] is not None:
                            prefill_data[key] = speaker_data[key]
                        else:
                            prefill_data[key] = value
            
            elif opportunity_form.type == "email":
                # Handle email type - create email template
                email_data = opportunity_form.form_data
                # Ensure form_data is parsed as JSON if it's a string
                if isinstance(email_data, str):
                    try:
                        email_data = json.loads(email_data)
                    except json.JSONDecodeError:
                        email_data = {}
                elif email_data is None:
                    email_data = {}
                
                opportunity_data = {
                    "email": email_data.get("email", ""),
                    "title": email_data.get("title", "Speaking Opportunity"),
                    "organization": email_data.get("organization", "Event Organizer")
                }
                
                prefill_data = create_speaker_email_template(speaker_data, opportunity_data)
            
            # Update speaker opportunity with prefill data
            speaker_opportunity.speaker_prefill_data = prefill_data
            session.commit()
            return {
                "success": True,
                "speaker_id": speaker_id,
                "opportunity_id": opportunity_id,
                "form_type": opportunity_form.type,
                "prefill_data": prefill_data
            }
            
    except Exception as e:
        logger.error(f"❌ ERROR in prefill_speaker_opportunity: {str(e)}")
        logger.error(f"🔍 Speaker ID: {speaker_id}, Opportunity ID: {opportunity_id}")
        return {
            "success": False,
            "error": f"Error prefilling data: {str(e)}",
            "speaker_id": speaker_id,
            "opportunity_id": opportunity_id
        }