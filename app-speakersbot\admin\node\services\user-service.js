const { RESPONSE_CODES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");
const { Users, Roles, AffiliateUsersDetails, Permissions, RolePermissions, sequelize, Settings } = require("../models");
const { parsePagination, getPagingData, parseJSONSafely, buildSearchWhereClause, buildFilterWhereClause, generateReferralCode } = require("../helpers/app-hepler");
const bcrypt = require('bcrypt');
const { Op } = require('sequelize');
const axios = require('axios');

const userService = {};

// ------------------------- user-service -------------------------

/**
 * Helper function to get table field names dynamically.
 * Extracts field names from Sequelize model attributes.
 * 
 * @param {Object} model - Sequelize model instance
 * @returns {Array<string>} Array of field names
 */
const getTableFields = (model) => {
    return Object.keys(model.rawAttributes);
};


/**
 * Create or update a user with role assignment.
 * Handles both creation and update operations 
 * Validates role existence and assigns permissions accordingly.
 * 
 * @param {Object} createReq - The request object containing user data
 * @param {Object} createReq.body - User data
 * @param {string} createReq.body.name - User name
 * @param {string} createReq.body.email - User email
 * @param {string} createReq.body.password - User password
 * @param {number} createReq.body.role_id - Role ID to assign
 * @param {string} [createReq.body.status] - User status (default: active)
 * @returns {Promise<Object>} Result of user creation or update
 * @throws {CustomError} When role not found, email exists, or validation fails
 */

userService.upsertUser = async (createReq) => {
    const userData = createReq.body;

    if (userData.website_url && !userData.website) {
        userData.website = userData.website_url;
    }

    try {
        if (userData.id) {
            // Update existing user 
            const user = await Users.findByPk(userData.id);

            if (!user) {
                throw new CustomError(RESPONSE_CODES.NOT_FOUND, "User not found");
            }

            // Check if email is being updated and if it already exists

            if (userData.email) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email cannot be updated");
            }
            if (userData.role_id) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Role cannot be updated");
            }

            if (userData.permission_ids) {
                userData.permission_ids = JSON.stringify(userData.permission_ids);
            }
            // Handle password update
            if (userData.password) {
                const isPasswordValid = await bcrypt.compare(userData.password, user.password);
                if (isPasswordValid) {
                    throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "New password cannot be the same as the old password");
                }
                userData.password = await bcrypt.hash(userData.password, 10);
            }

            // Check if role is being updated

            // Get field names dynamically from both tables
            const userFields = getTableFields(Users);
            const affiliateFields = getTableFields(AffiliateUsersDetails);

            // Separate user data and affiliate data
            const affiliateData = {};
            const userUpdateData = {};

            Object.keys(userData).forEach(key => {
                if (affiliateFields.includes(key) && !userFields.includes(key)) {
                    // Field exists only in affiliate table
                    affiliateData[key] = userData[key];
                } else if (userFields.includes(key)) {
                    // Field exists in user table (or both tables, prioritize user table)
                    userUpdateData[key] = userData[key];
                }
                // Ignore fields that don't exist in either table
            });

            // Update user data
            await Users.update(userUpdateData, { where: { id: userData.id }});

            // If user is affiliate, update affiliate details
            if (createReq.roleId == 2 || user.role_id == 2) {

                // const role = await Roles.findByPk(userData.role_id);
                if (user.role_id == 2) {

                    // Check if affiliate record exists
                    let affiliateRecord = await AffiliateUsersDetails.findOne({
                        where: { affiliate_id: userData.id }
                    });


                    if (affiliateRecord) {
                        // Update existing affiliate record
                        if (Object.keys(affiliateData).length > 0) {
                            await AffiliateUsersDetails.update(affiliateData, {
                                where: { affiliate_id: userData.id },
                            });
                        }
                    }
                    console.log("Affiliate record updated:", affiliateData);
                    if (affiliateData.website) {
                        try {
                            console.log("Affiliate website updated, calling scraper API...");
                            // Use GET request with query parameters instead of POST with JSON body
                            const encodedUrl = encodeURIComponent(affiliateData.website);
                            const apiUrl = `http://3.21.155.27:8000/affiliate/scraper?url=${encodedUrl}&affiliate_id=${userData.id}`;
                            console.log("API URL:", apiUrl);

                            const response = await axios.post(
                                apiUrl,
                                {
                                    headers: {
                                        accept: 'application/json'
                                    }
                                }
                            );
                        } catch (apiError) {
                            console.error("Scraper API error:", apiError.message);
                            if (apiError.response) {
                                console.error("API response:", apiError.response.status, apiError.response.data);
                            }
                        }
                    }
                }
            }


            return {
                status: true,
                message: "User updated successfully"
            };
        } else {
            // Create new user 

            if (!userData.email) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email is required");
            }

            if (!userData.role_id) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Role is required");
            }


            //    if(!userData.permission_ids && userData.role_id === 1) { 
            //     throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Permission is required");
            //    }

            if (userData.permission_ids) {
                userData.permission_ids = JSON.stringify(userData.permission_ids);
            } else {
                userData.permission_ids = JSON.stringify([]);
            }

            const isUserExist = await Users.findOne({ where: { email: userData.email } });


            if (isUserExist) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "User with this email already exists");
            }

            if (userData.password) {
                userData.password = await bcrypt.hash(userData.password, 10);
            }

            if (userData.role_id) {
                const isRoleExist = await Roles.findByPk(userData.role_id);
                if (!isRoleExist) {
                    throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Role not found");
                }
            }

            if (userData.referral_code) {
                const existingReferralCode = await AffiliateUsersDetails.findOne({
                    where: { referral_code: userData.referral_code }
                });
                if (existingReferralCode) {
                    throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Referral code already exists");
                }
            }


            // Get field names dynamically from both tables
            const userFields = getTableFields(Users);
            const affiliateFields = getTableFields(AffiliateUsersDetails);

            // Separate user data and affiliate data
            const affiliateData = {};
            const userCreateData = {};

            Object.keys(userData).forEach(key => {
                if (affiliateFields.includes(key) && !userFields.includes(key)) {
                    // Field exists only in affiliate table
                    affiliateData[key] = userData[key];
                } else if (userFields.includes(key)) {
                    // Field exists in user table (or both tables, prioritize user table)
                    userCreateData[key] = userData[key];
                }
                // Ignore fields that don't exist in either table
            });

            // Create user
            const newUser = await Users.create(userCreateData);

            // If user is affiliate, create affiliate details

            if (userData.role_id) {
                const role = await Roles.findByPk(userData.role_id);

                if (role && role.id == 2) {

                    if (!affiliateData.referral_code) {
                        //generate referral code
                        affiliateData.referral_code =  'AFF' + Math.random().toString(36).substring(2, 8).toUpperCase();
                    }
                    const metadata = await Settings.findOne({ where: { key: 'affiliate_landing' } });

                    const affiliateRecordData = {
                        affiliate_id: newUser.id,
                        commission: affiliateData.commission || 0,
                        commission_type: affiliateData.commission_type || 'percentage',
                        click_count: affiliateData.click_count || 0,
                        signup_count: affiliateData.signup_count || 0,
                        conversion_count: affiliateData.conversion_count || 0,
                        referral_code: affiliateData.referral_code,
                        legal_business_name: affiliateData.legal_business_name || null,
                        website: affiliateData.website || null,
                        metadata: metadata ? JSON.parse(metadata.value) : "",
                        commission_type: affiliateData.commission_type || 'percentage',

                    };

                    await AffiliateUsersDetails.create(affiliateRecordData);
                    if (affiliateRecordData.website) {

                        try {
                            const encodedUrl = encodeURIComponent(affiliateRecordData.website);
                            const apiUrl = `http://3.21.155.27:8000/affiliate/scraper?url=${encodedUrl}&affiliate_id=${affiliateRecordData.affiliate_id}`;
                            console.log("API URL:", apiUrl);

                            const response = await axios.post(
                                apiUrl,
                                {
                                    headers: {
                                        accept: 'application/json'
                                    }
                                }
                            );
                        } catch (apiError) {
                            console.error("Scraper API error:", apiError.message);
                            if (apiError.response) {
                                console.error("API response:", apiError.response.status, apiError.response.data);
                            }
                        }
                    }
                }
            }
            return {
                status: true,
                message: "User created/updated successfully"
            };
        }
    } catch (error) {        
        console.error("Error creating/updating user:", error);
        throw error;
    }
};

/**
 * Delete a user by ID.
 * @param {Object} deleteReq - The request object containing user ID.
 * @returns {Promise<Object>} Result of user deletion.
 */
userService.deleteUser = async (deleteReq) => {
    const userId = deleteReq.params.id;

    try {
        if (!userId) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "User ID is required");
        }

        const user = await Users.findByPk(userId);
        if (!user) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "User not found");
        }

        await Users.destroy({ where: { id: userId } });

        return {
            status: true,
            message: "User deleted successfully"
        };
    } catch (error) {
        console.error("Error deleting user:", error);
        throw error;
    }
};


/**
 * Get a user by ID.
 * @param {Object} getReq - The request object containing user ID.
 * @returns {Promise<Object>} Result of user retrieval.
 */
userService.getUser = async (getReq) => {
    const userId = getReq.params.id;

    try {
        if (!userId) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "User ID is required");
        }

        const userDetails = await Users.findByPk(userId, {
            include: [{
                model: Roles,
                as: 'role',
                include: [{ model: Permissions, as: 'permissions', attributes: ['name'], through: { attributes: [] } }]
            }],
            attributes: { exclude: ['password'] }
        });

        if (!userDetails) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "User not found");
        }

        const plainUser = userDetails.get({ plain: true });

        if (userDetails.role_id == 2) {
            const affiliateDetails = await AffiliateUsersDetails.findOne({ where: { affiliate_id: userId } });
            if (affiliateDetails) {
                const affiliateData = affiliateDetails.get({ plain: true });
                // Remove affiliate_id and id, merge other fields to top level
                const { affiliate_id, id, ...affiliateFields } = affiliateData;
                Object.assign(plainUser, affiliateFields);
            }
        }

        if (plainUser.role && Array.isArray(plainUser.role.permissions)) {
            plainUser.role.permissions = plainUser.role.permissions.map(p => p.name);
        }

        return {
            status: true,
            message: "User retrieved successfully",
            data: plainUser
        };
    } catch (error) {
        console.error("Error retrieving user:", error);
        throw error;
    }
};


/**
 * Get all users.
 * @param {Object} getReq - The request object containing user ID.
 * @returns {Promise<Object>} Result of user retrieval.
 */
// userService.getUsers = async (getReq) => {
//     try {

//         const { page, limit, offset } = parsePagination(getReq.query);



//         const search = getReq.query.search;
//         const filter = parseJSONSafely(getReq.query.filter, "Invalid JSON filter");

//         // Build base where from filter (exact matches)
//         let where = buildFilterWhereClause(filter);

//         // Extract role-specific filters from filter object
//         const roleWhere = {};
//         let requireRole = false;
//         if (filter && typeof filter === 'object') {
//             if (filter.role_id) {
//                 roleWhere.id = filter.role_id;
//                 requireRole = true;
//                 delete where.role_id;
//             }
//             if (filter.role) {
//                 roleWhere.name = filter.role;
//                 requireRole = true;
//                 delete where.role;
//             }
//         }

//         // Apply search across name and email
//         if (search) {
//             const searchWhere = buildSearchWhereClause(search, ['name', 'email']);
//             where = { ...where, ...searchWhere };
//         }

//         const includeRole = {
//             model: Roles,
//             as: 'role',
//             include: [{ model: Permissions, as: 'permissions', attributes: ['name'], through: { attributes: [] } }]
//         };
//         if (requireRole) {
//             includeRole.where = roleWhere;
//             includeRole.required = true;
//         }

//         const users = await Users.findAll({
//             where,
//             include: [includeRole],
//             attributes: { exclude: ['password'] },
//             limit,
//             offset,
//             order: [['id', 'DESC']]
//         });

//         const usersPlain = users.map(u => {
//             const pu = u.get({ plain: true });
//             if (pu.role && Array.isArray(pu.role.permissions)) {
//                 pu.role.permissions = pu.role.permissions.map(p => p.name);
//             }
//             return pu;
//         });

//         const totalUsers = await Users.count({
//             where,
//             include: requireRole ? [includeRole] : [],
//             distinct: true,
//             col: 'id'
//         });
//         const pageData = getPagingData(totalUsers, limit, page);

//         return {
//             status: true,
//             message: "Users retrieved successfully",
//             data: { users: usersPlain, pageData }
//         };
//     } catch (error) {
//         console.error("Error retrieving users:", error);
//         throw error;
//     }
// };
userService.getUsers = async (getReq) => {
    try {
        const { page, limit, offset } = parsePagination(getReq.query);


        const search = getReq.query.search;
        const filter = parseJSONSafely(getReq.query.filter, "Invalid JSON filter");
        const sort = parseJSONSafely(getReq.query.sort, "Invalid JSON sort");

        // Build base where from filter (exact matches)
        let where = buildFilterWhereClause(filter);

        // 🔹 Get logged-in user's role
        const currentRole = await Roles.findByPk(getReq.roleId);
        if (!currentRole) {
            throw new Error("Invalid roleId provided");
        }

        // Prepare role include
        const includeRole = {
            model: Roles,
            as: "role",
            include: [
                {
                    model: Permissions,
                    as: "permissions",
                    attributes: ["name"],
                    through: { attributes: [] },
                },
            ],
        };

        // 🔹 Prepare include models
        const includeModels = [includeRole];

        // 🔹 Apply restrictions based on logged-in role
        if (getReq.roleId == 1) {
            // Super Admin → see everyone (no filter)
            includeModels.push({
                model: AffiliateUsersDetails,
                as: "affiliateUsersDetails",
                attributes: { exclude: [] }, // all affiliate attributes
                required: false,
            });
        } else if (getReq.roleId == 2) {
            // Affiliate → only see Affiliates
            includeRole.where = { id: 2 };
            includeRole.required = true;

            includeModels.push({
                model: AffiliateUsersDetails,
                as: "affiliateUsersDetails",
                attributes: { exclude: [] }, // all affiliate attributes
                required: false,
            });
        } else {
            // System Admin or normal users → exclude Super Admins
            includeRole.where = { id: { [Op.ne]: 1 } };
            includeRole.required = true;
        }

        // Extract role-specific filters from query filter
        const roleWhere = {};
        if (filter && typeof filter === "object") {
            if (filter.role_id) {
                roleWhere.id = filter.role_id;
                delete where.role_id;
            }
            if (filter.role) {
                roleWhere.name = filter.role;
                delete where.role;
            }
        }
        if (Object.keys(roleWhere).length) {
            includeRole.where = { ...(includeRole.where || {}), ...roleWhere };
            includeRole.required = true;
        }

        // Apply search across name and email
        if (search) {
            const searchWhere = buildSearchWhereClause(search, ["name", "email"]);
            where = { ...where, ...searchWhere };
        }

        // Build order clause
        let order = [['id', 'DESC']];
        if (sort && Object.keys(sort).length > 0) {
            order = Object.keys(sort).map((field) => [
                field,
                String(sort[field]).toUpperCase() === 'ASC' ? 'ASC' : 'DESC',
            ]);
        }

        // 🔹 Fetch users
        const users = await Users.findAll({
            where,
            include: includeModels,
            attributes: { exclude: ["password"] },
            limit,
            offset,
            order
        });

        // 🔹 Clean response
        const usersPlain = await Promise.all(users.map(async (u) => {
            const pu = u.get({ plain: true });

            if (pu.role && Array.isArray(pu.role.permissions)) {
                pu.role.permissions = pu.role.permissions.map((p) => p.name);
            }

            // If user is affiliate, flatten affiliateUsersDetails fields to top-level
            if (pu.role_id == 2 && Array.isArray(pu.affiliateUsersDetails) && pu.affiliateUsersDetails.length > 0) {
                // Remove affiliate_id and id from affiliate details, keep only user.id
                const { affiliate_id, id: affiliateDetailsId, ...affiliateDetails } = pu.affiliateUsersDetails[0];
                Object.assign(pu, affiliateDetails);
                // Only user.id remains as identifier
                delete pu.affiliateUsersDetails;
            } else {
                delete pu.affiliateUsersDetails;
            }

            return pu;
        }));

        // 🔹 Count total
        const totalUsers = await Users.count({
            where,
            include: includeModels,
            distinct: true,
            col: "id",
        });

        const pageData = getPagingData(totalUsers, limit, page);

        return {
            status: true,
            message: "Users retrieved successfully",
            data: { users: usersPlain, pageData },
        };
    } catch (error) {
        console.error("Error retrieving users:", error);
        throw error;
    }
};


userService.authUser = async (authReq) => {
    const userId = authReq.userId;
    const user = await Users.findOne(
        {
            where: { id: userId },
            include: [{
                model: Roles,
                as: 'role'
            }],
            attributes: { exclude: ['password'] }
        });

    if (!user) {
        throw new CustomError(RESPONSE_CODES.NOT_FOUND, "User not found");
    }

    const plainUser = user.get({ plain: true });

    // Fetch permissions based on permission_ids array and add to role
    if (plainUser.permission_ids) {
        try {
            const permissionIds = JSON.parse(plainUser.permission_ids);
            if (Array.isArray(permissionIds) && permissionIds.length > 0) {
                const permissions = await Permissions.findAll({
                    where: { id: permissionIds },
                    attributes: ['name'] // Only fetch name
                });
                plainUser.role.permissions = permissions.map(p => p.name); // Add to role object
            } else {
                plainUser.role.permissions = [];
            }
        } catch (error) {
            console.error('Error parsing permission_ids:', error);
            plainUser.role.permissions = [];
        }
    } else {
        plainUser.role.permissions = [];
    }

    return {
        status: true,
        message: "User authenticated successfully",
        data: plainUser
    };
};

module.exports = userService;
