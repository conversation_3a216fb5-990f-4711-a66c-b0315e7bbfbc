"""
Affiliate Utils
Common utility functions for affiliate scraping
"""

import re
import json
import logging
from typing import Dict, Any, Optional
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from config.logger import get_logger
logger = get_logger(__name__, file_name="scraper.log")
from .constants import (
    COLOR_PATTERNS, COLOR_FIELD_MAPPING, NAMED_COLORS
)


class AffiliateUtils:
    """Common utility functions for affiliate scraping"""
    
    def __init__(self):
        """Initialize utils"""
        pass
    
    def organize_scraped_data(self, existing_metadata: Dict[str, Any], scraped_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Organize scraped data into the proper metadata structure format
        Args:
            existing_metadata (Dict[str, Any]): Existing metadata from database
            scraped_data (Dict[str, Any]): New scraped data
        Returns:
            Dict[str, Any]: Organized metadata with scraped data integrated
        """
        try:
            # Start with existing metadata structure
            organized_metadata = existing_metadata.copy()
            # Initialize sections if they don't exist
            if 'branding' not in organized_metadata:
                organized_metadata['branding'] = {}
            if 'value_to_speakers' not in organized_metadata:
                organized_metadata['value_to_speakers'] = {}
            if 'admin_and_compliance' not in organized_metadata:
                organized_metadata['admin_and_compliance'] = {}
            if 'free_offer' not in organized_metadata:
                organized_metadata['free_offer'] = {}
            # Map scraped data to appropriate sections
            # Branding section
            if scraped_data.get('logo_url') and scraped_data['logo_url'] != "No logo found":
                organized_metadata['branding']['logo_url'] = scraped_data['logo_url']
            
            if scraped_data.get('product_images') and scraped_data['product_images'] != "No product images found":
                # Convert product images string to array
                images = [img.strip() for img in scraped_data['product_images'].split(';') if img.strip()]
                organized_metadata['branding']['images'] = images
            
            if scraped_data.get('color_codes') and scraped_data['color_codes'] != "No color codes found":
                # Extract color codes and map them
                color_text = scraped_data['color_codes']
                if 'Primary:' in color_text:
                    primary_color = color_text.split('Primary:')[1].split(',')[0].strip()
                    organized_metadata['branding']['primary_color'] = primary_color
                if 'Secondary:' in color_text:
                    secondary_color = color_text.split('Secondary:')[1].split(',')[0].strip()
                    organized_metadata['branding']['secondary_color'] = secondary_color
                if 'Accent:' in color_text:
                    accent_color = color_text.split('Accent:')[1].strip()
                    organized_metadata['branding']['accent_color'] = accent_color
            
            # Extract additional specific color fields from the website
            self._extract_specific_colors(organized_metadata, scraped_data)
            if scraped_data.get('font_stack') and scraped_data['font_stack'] != "No font stack found":
                organized_metadata['branding']['font_stack'] = scraped_data['font_stack']
            
            # Value to speakers section
            if scraped_data.get('learning_objectives') and scraped_data['learning_objectives'] != "No learning objectives found":
                # Convert to array format
                objectives = [obj.strip() for obj in scraped_data['learning_objectives'].split(';') if obj.strip()]
                organized_metadata['value_to_speakers']['takeaways'] = objectives
            
            if scraped_data.get('challenges_solved') and scraped_data['challenges_solved'] != "No challenges solved found":
                # Convert to array format
                challenges = [challenge.strip() for challenge in scraped_data['challenges_solved'].split(';') if challenge.strip()]
                organized_metadata['value_to_speakers']['challenges'] = challenges
            
            if scraped_data.get('core_benefit') and scraped_data['core_benefit'] != "No core benefit found":
                organized_metadata['value_to_speakers']['core_benefit'] = scraped_data['core_benefit']
            
            if scraped_data.get('differentiator') and scraped_data['differentiator'] != "No differentiator found":
                organized_metadata['value_to_speakers']['differentiator'] = scraped_data['differentiator']
            
            if scraped_data.get('overarching_offer') and scraped_data['overarching_offer'] != "No overarching offer found":
                organized_metadata['value_to_speakers']['overarching_offer'] = scraped_data['overarching_offer']
            
            # Admin and compliance section
            if scraped_data.get('promo_channels') and scraped_data['promo_channels'] != "No promo channels specified":
                # Convert to array format
                channels = [channel.strip() for channel in scraped_data['promo_channels'].split(';') if channel.strip()]
                organized_metadata['admin_and_compliance']['promo_channels'] = channels
            
            if scraped_data.get('reach_estimate') and scraped_data['reach_estimate'] != "Reach estimate not specified":
                organized_metadata['admin_and_compliance']['reach_estimate'] = scraped_data['reach_estimate']
            
            if scraped_data.get('business_entity_type') and scraped_data['business_entity_type'] != "Business entity type not specified":
                organized_metadata['admin_and_compliance']['business_type_entity'] = scraped_data['business_entity_type']
            
            if scraped_data.get('digital_signature') and scraped_data['digital_signature'] != "Digital signature not specified":
                organized_metadata['admin_and_compliance']['digital_signature'] = scraped_data['digital_signature']
            
            if scraped_data.get('terms_acceptance') and scraped_data['terms_acceptance'] != "Terms acceptance not specified":
                # Check if terms acceptance is required
                terms_required = "required" in scraped_data['terms_acceptance'].lower() or "accept" in scraped_data['terms_acceptance'].lower()
                organized_metadata['admin_and_compliance']['accept_terms_and_conditions'] = terms_required
            
            if scraped_data.get('tax_docs') and scraped_data['tax_docs'] != "Tax document requirements not specified":
                organized_metadata['admin_and_compliance']['tax_document_requirements'] = scraped_data['tax_docs']
            
            # Add page information
            if scraped_data.get('page_title'):
                organized_metadata['page_info'] = {
                    'title': scraped_data['page_title'],
                    'meta_description': scraped_data.get('meta_description', ''),
                    'scraped_url': scraped_data.get('scraped_url', ''),
                    'scraped_at': scraped_data.get('scraped_at', '')
                }
            return organized_metadata
            
        except Exception as e:
            logger.error(f"Error organizing scraped data: {e}")
            # Fallback to simple merge if organization fails
            return {**existing_metadata, **scraped_data}
    
    def _extract_specific_colors(self, organized_metadata: Dict[str, Any], scraped_data: Dict[str, Any]) -> None:
        """
        Extract specific color fields from scraped data and update only existing fields
        
        Args:
            organized_metadata (Dict[str, Any]): The organized metadata structure
            scraped_data (Dict[str, Any]): The scraped data from website
        """
        try:
            # Extract colors from CSS and inline styles
            colors_found = self._extract_colors_from_website(scraped_data.get('scraped_url', ''))
            # Only update existing color fields, don't add new ones
            existing_color_fields = [
                'text_color', 'title_color', 'button_color', 'background_color', 
                'button_text_color', 'primary_color', 'secondary_color', 'accent_color'
            ]
            updated_count = 0
            for field_name in existing_color_fields:
                if field_name in colors_found and colors_found[field_name]:
                    # Only update if the field already exists in branding section
                    if 'branding' in organized_metadata and field_name in organized_metadata['branding']:
                        organized_metadata['branding'][field_name] = colors_found[field_name]
                        updated_count += 1
                    else:
                        logger.debug(f"Skipped {field_name} - field doesn't exist in branding section")
            
        except Exception as e:
            logger.warning(f"Error extracting specific colors: {e}")
    
    def _extract_colors_from_website(self, url: str) -> Dict[str, str]:
        """
        Extract specific color values from website CSS and styles
        Args:
            url (str): The website URL to extract colors from
        Returns:
            Dict[str, str]: Dictionary of color field names and their values
        """
        colors = {}
        try:
            if not url:
                return colors
            return colors

        except Exception as e:
            logger.warning(f"Error extracting colors from website {url}: {e}")
            return colors
    
    def _extract_colors_from_css(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract colors from CSS style tags"""
        colors = {}
        try:
            style_tags = soup.find_all('style')
            for style in style_tags:
                if style.string:
                    css_content = style.string
                    
                    for pattern in COLOR_PATTERNS:
                        matches = re.findall(pattern, css_content, re.IGNORECASE)
                        for match in matches:
                            color_value = match.strip()
                            if self._is_valid_color(color_value):
                                colors[f'css_{len(colors)}'] = color_value
        except Exception as e:
            logger.warning(f"Error extracting colors from CSS: {e}")
        return colors
    
    def _extract_colors_from_inline_styles(self, soup: BeautifulSoup) -> Dict[str, str]:
        """Extract colors from inline style attributes"""
        colors = {}
        try:
            elements_with_style = soup.find_all(attrs={"style": True})
            for element in elements_with_style:
                style = element['style']
                # Look for color properties
                color_patterns = [
                    r'color:\s*([^;]+)',
                    r'background-color:\s*([^;]+)',
                    r'border-color:\s*([^;]+)'
                ]
                for pattern in color_patterns:
                    matches = re.findall(pattern, style, re.IGNORECASE)
                    for match in matches:
                        color_value = match.strip()
                        if self._is_valid_color(color_value):
                            colors[f'inline_{len(colors)}'] = color_value
        except Exception as e:
            logger.warning(f"Error extracting colors from inline styles: {e}")
        return colors
    
    def _map_colors_to_fields(self, all_colors: Dict[str, str]) -> Dict[str, str]:
        """Map extracted colors to specific field names - only for existing fields"""
        mapped_colors = {}
        try:
            # Convert all colors to a list for analysis
            color_list = list(all_colors.values())
            if not color_list:
                return mapped_colors
            # Remove duplicates while preserving order
            unique_colors = list(dict.fromkeys(color_list))
            # Map colors to fields based on position
            for i, color in enumerate(unique_colors[:8]):  # Limit to 8 colors max
                if i in COLOR_FIELD_MAPPING:
                    mapped_colors[COLOR_FIELD_MAPPING[i]] = color
        except Exception as e:
            logger.warning(f"Error mapping colors to fields: {e}")
        return mapped_colors
    
    def _is_valid_color(self, color_value: str) -> bool:
        """Check if a color value is valid"""
        if not color_value:
            return False
            
        # Remove whitespace
        color_value = color_value.strip()
        # Check for hex colors
        if re.match(r'^#[0-9a-fA-F]{3,6}$', color_value):
            return True
        # Check for rgb/rgba colors
        if re.match(r'^rgba?\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*(,\s*[\d.]+\s*)?\)$', color_value):
            return True
        # Check for hsl/hsla colors
        if re.match(r'^hsla?\(\s*\d+\s*,\s*\d+%\s*,\s*\d+%\s*(,\s*[\d.]+\s*)?\)$', color_value):
            return True
        # Check for named colors
        if color_value.lower() in NAMED_COLORS:
            return True
        return False
