"""
Centralized configuration management for the application.
All environment variables are loaded and managed here.
"""

import os
from dotenv import load_dotenv
from typing import Optional

# Load environment variables from .env file
load_dotenv()


def _env_bool(key: str, default: str = "false") -> bool:
    """Parse boolean-like environment variables robustly."""
    val = os.environ.get(key, default)
    return str(val).strip().lower() in ("1", "true", "yes", "on")

class Config:
    """Application configuration class."""
    
    # Database Configuration
    DATABASE_URL: str = os.environ.get("DATABASE_URL", "")
    
    # Individual database configuration (fallback)
    DB_HOST: str = os.environ.get("DB_HOST", "localhost")
    DB_USER: str = os.environ.get("DB_USER", "root")
    DB_PASSWORD: str = os.environ.get("DB_PASSWORD", "")
    DB_NAME: str = os.environ.get("DB_NAME", "speaker_bot")
    DB_DIALECT: str = os.environ.get("DB_DIALECT", "mysql")
    
    # Construct database URL if not provided
    @classmethod
    def _construct_database_url(cls) -> str:
        """Construct database URL from individual components or use provided URL."""
        if cls.DATABASE_URL:
            # Clean up the DATABASE_URL if it has a prefix
            url = cls.DATABASE_URL.strip()
            if url.startswith("DATABASE_URL="):
                url = url[13:]  # Remove "DATABASE_URL=" prefix
            
            # Convert mysql:// to mysql+pymysql://
            if url.startswith("mysql://"):
                url = url.replace("mysql://", "mysql+pymysql://")
            elif url.startswith("mysql+pymysql+pymysql://"):
                # Fix double pymysql
                url = url.replace("mysql+pymysql+pymysql://", "mysql+pymysql://")
            
            return url
        else:
            # Construct URL from individual components
            if cls.DB_PASSWORD:
                return f"mysql+pymysql://{cls.DB_USER}:{cls.DB_PASSWORD}@{cls.DB_HOST}/{cls.DB_NAME}"
            else:
                return f"mysql+pymysql://{cls.DB_USER}@{cls.DB_HOST}/{cls.DB_NAME}"
    
    # OpenAI Configuration
    OPENAI_API_KEY: str = os.environ.get("OPENAI_API_KEY", "")
    
    # Search API Keys
    TAVILY_API_KEY: str = os.environ.get("TAVILY_API_KEY", "")
    BRAVE_API_KEY: str = os.environ.get("BRAVE_API_KEY", "")
    BRAVE_ENDPOINT: str = os.environ.get("BRAVE_ENDPOINT", "https://api.search.brave.com/res/v1/web/search")
    SERPAPI_API_KEY: str = os.environ.get("SERPAPI_API_KEY", "")
    EXA_API_KEY: str = os.environ.get("EXA_API_KEY", "")
    FIRECRAWL_API_KEY: str = os.environ.get("FIRECRAWL_API_KEY", "")
    FIRECRAWL_ENDPOINT: str = os.environ.get("FIRECRAWL_ENDPOINT", "https://api.firecrawl.dev/v1/search")
    
    
    # LLM Configuration
    OPENAI_MODEL: str = os.environ.get("OPENAI_MODEL", "gpt-4")
    OPENAI_EMBEDDING_MODEL: str = os.environ.get("OPENAI_EMBEDDING_MODEL", "text-embedding-3-small")
    OPENAI_TEMPERATURE: float = float(os.environ.get("OPENAI_TEMPERATURE", "0.3"))
    
    # ScrapeOps / Proxy Aggregator Configuration
    SCRAPEOPS_API_KEY: str = os.environ.get("SCRAPEOPS_API_KEY", "")
    SCRAPEOPS_PROXY_ENABLED: bool = _env_bool("SCRAPEOPS_PROXY_ENABLED", "false")
    SCRAPEOPS_COUNTRY: str = os.environ.get("SCRAPEOPS_COUNTRY", "")
    SCRAPEOPS_RENDER_JS: bool = _env_bool("SCRAPEOPS_RENDER_JS", "false")
    
    # Scraper Concurrency Configuration
    MAX_CONCURRENT_TOPICS: int = int(os.environ.get("MAX_CONCURRENT_TOPICS", "1"))
    MAX_CONCURRENT_URLS: int = int(os.environ.get("MAX_CONCURRENT_URLS", "1"))
    REQUEST_DELAY_BETWEEN_BATCHES: float = float(os.environ.get("REQUEST_DELAY_BETWEEN_BATCHES", "1.0"))
    
    # Scheduler Configuration (DEPRECATED - Using Workers Now)
    SCHEDULER_INTERVAL_MINUTES: int = int(os.environ.get("SCHEDULER_INTERVAL_MINUTES", "30"))
    DISCOVERY_INTERVAL_MINUTES: int = int(os.environ.get("DISCOVERY_INTERVAL_MINUTES", "45"))
    
    # Worker Configuration
    WORKER_ENABLED: bool = _env_bool("WORKER_ENABLED", "true")
    WORKER_POLL_INTERVAL_SECONDS: float = float(os.environ.get("WORKER_POLL_INTERVAL_SECONDS", "9.0"))
    WORKER_BATCH_SIZE: int = int(os.environ.get("WORKER_BATCH_SIZE", "1"))
    
    # AWS S3 Configuration
    AWS_ACCESS_KEY_ID: str = os.environ.get("access_key", "")
    AWS_SECRET_ACCESS_KEY: str = os.environ.get("secrete_key", "")
    AWS_REGION: str = os.environ.get("region", "us-east-1")
    S3_BUCKET_NAME: str = os.environ.get("bucketname", "")
    
    MAX_RETRIES_COUNT: int = int(os.environ.get("MAX_RETRIES_COUNT", "3"))
    
    @classmethod
    def validate_required_keys(cls) -> list[str]:
        """Validate that all required environment variables are set."""
        missing_keys = []
        
        # Check database configuration - either DATABASE_URL or individual DB components
        if not cls.DATABASE_URL and not (cls.DB_HOST and cls.DB_USER and cls.DB_NAME):
            missing_keys.append("DATABASE_URL or (DB_HOST, DB_USER, DB_NAME)")
        if not cls.OPENAI_API_KEY:
            missing_keys.append("OPENAI_API_KEY")
            
        return missing_keys
    
    @classmethod
    def get_database_url(cls) -> str:
        """Get the database URL with proper MySQL driver."""
        return cls._construct_database_url()
    
    @classmethod
    def is_search_api_available(cls, api_name: str) -> bool:
        """Check if a specific search API is available."""
        api_keys = {
            "tavily": cls.TAVILY_API_KEY,
            "brave": cls.BRAVE_API_KEY,
            "serpapi": cls.SERPAPI_API_KEY,
            "exa": cls.EXA_API_KEY,
            "firecrawl": cls.FIRECRAWL_API_KEY
        }
        return bool(api_keys.get(api_name.lower(), ""))

# Create a global config instance
config = Config()
