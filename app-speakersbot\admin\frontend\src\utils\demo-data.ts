// Demo data generator for initial app seeding
import dayjs from 'dayjs';
import type {
  User, Speaker, Opportunity, Match, IntakeFormVersion, ScrapingLog, 
  ScrapingTopic, Affiliate, Invite, ActivityLogEntry, PricingPlan, AppSettings
} from '../types';

export const generateDemoUsers = (): User[] => [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'super_admin',
    status: 'active',
    createdAt: dayjs().subtract(30, 'days').toISOString(),
    updatedAt: dayjs().subtract(5, 'days').toISOString(),
  },
  {
    id: '2', 
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'system_admin',
    status: 'active',
    createdAt: dayjs().subtract(20, 'days').toISOString(),
    updatedAt: dayjs().subtract(2, 'days').toISOString(),
  },
  {
    id: '3',
    name: '<PERSON>ilia<PERSON>',
    email: '<EMAIL>', 
    role: 'affiliate',
    status: 'active',
    createdAt: dayjs().subtract(10, 'days').toISOString(),
    updatedAt: dayjs().subtract(1, 'days').toISOString(),
  },
  {
    id: '4',
    name: 'Emma Rodriguez',
    email: '<EMAIL>',
    role: 'system_admin',
    status: 'active',
    createdAt: dayjs().subtract(25, 'days').toISOString(),
    updatedAt: dayjs().subtract(3, 'days').toISOString(),
  },
  {
    id: '5',
    name: 'David Chen',
    email: '<EMAIL>',
    role: 'affiliate',
    status: 'active',
    createdAt: dayjs().subtract(15, 'days').toISOString(),
    updatedAt: dayjs().subtract(1, 'days').toISOString(),
  },
  {
    id: '6',
    name: 'Lisa Thompson',
    email: '<EMAIL>',
    role: 'affiliate',
    status: 'inactive',
    createdAt: dayjs().subtract(40, 'days').toISOString(),
    updatedAt: dayjs().subtract(7, 'days').toISOString(),
  },
  {
    id: '7',
    name: 'James Wilson',
    email: '<EMAIL>',
    role: 'affiliate',
    status: 'active',
    createdAt: dayjs().subtract(12, 'days').toISOString(),
    updatedAt: dayjs().subtract(2, 'days').toISOString(),
  },
  {
    id: '8',
    name: 'Maria Garcia',
    email: '<EMAIL>',
    role: 'affiliate',
    status: 'active',
    createdAt: dayjs().subtract(8, 'days').toISOString(),
    updatedAt: dayjs().subtract(1, 'days').toISOString(),
  },
  {
    id: '9',
    name: 'Robert Kim',
    email: '<EMAIL>',
    role: 'system_admin',
    status: 'suspended',
    createdAt: dayjs().subtract(35, 'days').toISOString(),
    updatedAt: dayjs().subtract(10, 'days').toISOString(),
  },
  {
    id: '10',
    name: 'Sophie Laurent',
    email: '<EMAIL>',
    role: 'affiliate',
    status: 'active',
    createdAt: dayjs().subtract(6, 'days').toISOString(),
    updatedAt: dayjs().subtract(1, 'days').toISOString(),
  },
  {
    id: '11',
    name: 'Ahmed Hassan',
    email: '<EMAIL>',
    role: 'affiliate',
    status: 'inactive',
    createdAt: dayjs().subtract(22, 'days').toISOString(),
    updatedAt: dayjs().subtract(5, 'days').toISOString(),
  },
  {
    id: '12',
    name: 'Jessica Park',
    email: '<EMAIL>',
    role: 'affiliate',
    status: 'active',
    createdAt: dayjs().subtract(18, 'days').toISOString(),
    updatedAt: dayjs().subtract(2, 'days').toISOString(),
  },
  {
    id: '13',
    name: 'Michael Brown',
    email: '<EMAIL>',
    role: 'affiliate',
    status: 'active',
    createdAt: dayjs().subtract(14, 'days').toISOString(),
    updatedAt: dayjs().subtract(3, 'days').toISOString(),
  },
  {
    id: '14',
    name: 'Anna Kowalski',
    email: '<EMAIL>',
    role: 'affiliate',
    status: 'suspended',
    createdAt: dayjs().subtract(28, 'days').toISOString(),
    updatedAt: dayjs().subtract(8, 'days').toISOString(),
  },
  {
    id: '15',
    name: 'Carlos Silva',
    email: '<EMAIL>',
    role: 'affiliate',
    status: 'active',
    createdAt: dayjs().subtract(9, 'days').toISOString(),
    updatedAt: dayjs().subtract(1, 'days').toISOString(),
  },
  {
    id: '16',
    name: 'Jennifer Wu',
    email: '<EMAIL>',
    role: 'affiliate',
    status: 'active',
    createdAt: dayjs().subtract(5, 'days').toISOString(),
    updatedAt: dayjs().subtract(1, 'days').toISOString(),
  },
  {
    id: '17',
    name: 'Thomas Mueller',
    email: '<EMAIL>',
    role: 'affiliate',
    status: 'inactive',
    createdAt: dayjs().subtract(32, 'days').toISOString(),
    updatedAt: dayjs().subtract(12, 'days').toISOString(),
  },
  {
    id: '18',
    name: 'Priya Patel',
    email: '<EMAIL>',
    role: 'affiliate',
    status: 'active',
    createdAt: dayjs().subtract(4, 'days').toISOString(),
    updatedAt: dayjs().subtract(1, 'days').toISOString(),
  },
];

export const generateDemoSpeakers = (): Speaker[] => {
  const names = [
    'Dr. Emily Chen', 'Marcus Johnson', 'Lisa Rodriguez', 'James Thompson', 'Sarah Kim',
    'Ahmed Hassan', 'Maria Gonzalez', 'David Park', 'Jennifer Wu', 'Michael Brown',
    'Priya Patel', 'Robert Taylor', 'Anna Kowalski', 'Carlos Silva', 'Emma Wilson',
    'Raj Sharma', 'Nicole Davis', 'Alex Chang', 'Sophie Laurent', 'Kevin O\'Brien',
    'Fatima Al-Rashid', 'Thomas Mueller', 'Grace Zhang', 'Daniel Santos', 'Rachel Green',
    'Omar Khalil', 'Julia Schmidt', 'Andrew Lee', 'Zara Adams', 'Francesco Rossi',
    'Dr. Aisha Patel', 'Benjamin Carter', 'Yuki Tanaka', 'Isabella Martinez', 'Chen Wei',
    'Dmitri Volkov', 'Amara Okafor', 'Lucas Anderson', 'Mei Lin', 'Hassan Ali',
    'Victoria Clarke', 'Rajesh Kumar', 'Elena Popov', 'João Silva', 'Leila Nasri',
    'Dr. Kwame Asante', 'Natasha Ivanova', 'Diego Morales', 'Amal Zahra', 'Olaf Eriksson'
  ];
  
  const expertiseAreas = [
    'AI & Machine Learning', 'Healthcare Innovation', 'FinTech', 'Green Energy', 
    'Education Technology', 'Marketing & Sales', 'Blockchain', 'Cybersecurity',
    'Digital Transformation', 'Entrepreneurship', 'Leadership', 'Data Science',
    'IoT & Smart Cities', 'Biotechnology', 'Cloud Computing', 'UX/UI Design',
    'Sustainability', 'Robotics', 'DevOps', 'Product Management'
  ];
  
  const companies = [
    'TechCorp', 'Innovation Labs', 'Global Solutions', 'Future Systems', 'DataDriven Inc',
    'CloudFirst', 'AI Ventures', 'Smart Analytics', 'Digital Works', 'NextGen Tech',
    'Blockchain Solutions', 'Green Innovation', 'HealthTech Plus', 'EduTech Pro', 'FinTech Elite',
    'Cyber Defense Co', 'UX Design Studio', 'Robotics Lab', 'Sustainability Group', 'DevOps Masters'
  ];
  
  const jobTitles = [
    'CEO', 'CTO', 'VP of Engineering', 'Head of AI', 'Senior Director', 'Principal Engineer',
    'Lead Data Scientist', 'Head of Product', 'VP of Marketing', 'Chief Innovation Officer',
    'Director of Technology', 'Senior Consultant', 'Research Director', 'Head of Strategy',
    'VP of Operations', 'Chief Architect', 'Senior Manager', 'Head of Design', 'Principal Consultant'
  ];

  const cities = [
    'New York', 'London', 'Berlin', 'Singapore', 'Mumbai', 'Toronto',
    'San Francisco', 'Amsterdam', 'Sydney', 'Tokyo', 'Dubai', 'Stockholm',
    'Barcelona', 'Austin', 'Tel Aviv', 'Boston', 'Vancouver', 'Seoul'
  ];

  const subscriptionPlans = ['basic', 'premium', 'enterprise'];
  const activityStatuses = ['active', 'inactive'];

  const speakers: Speaker[] = [];
  
  // Generate base speakers with names
  for (let i = 0; i < Math.min(names.length, 50); i++) {
    const name = names[i];
    const primaryExpertise = expertiseAreas[i % expertiseAreas.length];
    const secondaryExpertise = expertiseAreas[(i + 1) % expertiseAreas.length];
    const preferredCities = [
      cities[Math.floor(Math.random() * cities.length)],
      cities[Math.floor(Math.random() * cities.length)],
      'Remote'
    ];
    
    const company = companies[i % companies.length];
    const title = jobTitles[i % jobTitles.length];
    const yearsExp = 3 + Math.floor(Math.random() * 20);
    const willingToTravel = Math.random() > 0.3; // 70% willing to travel
    
    // Generate additional expertise areas
    const otherExpertise = [];
    const numOtherAreas = Math.floor(Math.random() * 3) + 1;
    for (let j = 0; j < numOtherAreas; j++) {
      const randomArea = expertiseAreas[Math.floor(Math.random() * expertiseAreas.length)];
      if (randomArea !== primaryExpertise && !otherExpertise.includes(randomArea)) {
        otherExpertise.push(randomArea);
      }
    }
    
    speakers.push({
      id: `sp${i + 1}`,
      name,
      email: `${name.toLowerCase().replace(/[^a-z]/g, '')}@${Math.random() > 0.5 ? 'tech' : 'consulting'}.com`,
      subscriptionPlan: subscriptionPlans[Math.floor(Math.random() * subscriptionPlans.length)] as any,
      activityStatus: (Math.random() > 0.1 ? 'active' : 'inactive') as any,
      gamificationPoints: Math.floor(Math.random() * 2000) + 100,
      intakeData: {
        primaryExpertise,
        otherExpertise,
        bio: `Expert in ${primaryExpertise.toLowerCase()} with ${yearsExp}+ years experience`,
        preferredGeography: preferredCities,
        yearsOfExperience: yearsExp,
        willingToTravel,
        company,
        title,
        speakerWebsite: `https://${name.toLowerCase().replace(/[^a-z]/g, '')}.com`
      },
      matchingScore: 60 + Math.floor(Math.random() * 40),
      createdAt: dayjs().subtract(Math.floor(Math.random() * 180), 'days').toISOString(),
      updatedAt: dayjs().subtract(Math.floor(Math.random() * 10), 'days').toISOString(),
    });
  }

  // Generate additional speakers to reach 300
  for (let i = 50; i < 300; i++) {
    const firstNames = ['Alex', 'Jordan', 'Taylor', 'Casey', 'Morgan', 'Jamie', 'Avery', 'Riley', 'Cameron', 'Drew'];
    const lastNames = ['Smith', 'Johnson', 'Williams', 'Brown', 'Jones', 'Garcia', 'Miller', 'Davis', 'Rodriguez', 'Martinez'];
    
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const lastName = lastNames[Math.floor(Math.random() * lastNames.length)];
    const name = `${firstName} ${lastName}`;
    
    const primaryExpertise = expertiseAreas[Math.floor(Math.random() * expertiseAreas.length)];
    const secondaryExpertise = expertiseAreas[Math.floor(Math.random() * expertiseAreas.length)];
    
    const company = companies[i % companies.length];
    const title = jobTitles[i % jobTitles.length];
    const yearsExp = 2 + Math.floor(Math.random() * 25);
    const willingToTravel = Math.random() > 0.4; // 60% willing to travel
    
    // Generate additional expertise areas
    const otherExpertise = [];
    const numOtherAreas = Math.floor(Math.random() * 4) + 1;
    for (let j = 0; j < numOtherAreas; j++) {
      const randomArea = expertiseAreas[Math.floor(Math.random() * expertiseAreas.length)];
      if (randomArea !== primaryExpertise && randomArea !== secondaryExpertise && !otherExpertise.includes(randomArea)) {
        otherExpertise.push(randomArea);
      }
    }
    
    speakers.push({
      id: `sp${i + 1}`,
      name,
      email: `${firstName.toLowerCase()}.${lastName.toLowerCase()}@example.com`,
      subscriptionPlan: subscriptionPlans[Math.floor(Math.random() * subscriptionPlans.length)] as any,
      activityStatus: (Math.random() > 0.15 ? 'active' : 'inactive') as any,
      gamificationPoints: Math.floor(Math.random() * 2000) + 100,
      intakeData: {
        primaryExpertise,
        otherExpertise,
        bio: `Professional in ${primaryExpertise.toLowerCase()} with extensive experience`,
        preferredGeography: [
          cities[Math.floor(Math.random() * cities.length)],
          cities[Math.floor(Math.random() * cities.length)],
          'Remote'
        ],
        yearsOfExperience: yearsExp,
        willingToTravel,
        company,
        title,
        speakerWebsite: `https://${firstName.toLowerCase()}${lastName.toLowerCase()}.com`
      },
      matchingScore: 50 + Math.floor(Math.random() * 50),
      createdAt: dayjs().subtract(Math.floor(Math.random() * 365), 'days').toISOString(),
      updatedAt: dayjs().subtract(Math.floor(Math.random() * 30), 'days').toISOString(),
    });
  }

  return speakers;
};

export const generateDemoOpportunities = (): Opportunity[] => {
  const opportunityTitles = [
    'AI Summit 2024 - Keynote Speaker', 'Tech Innovation Expo 2025', 'Green Energy World Conference',
    'Future of Work Summit', 'Healthcare Innovation Forum', 'FinTech Revolution Conference',
    'Digital Marketing Mastery Workshop', 'Cybersecurity Leaders Summit', 'Blockchain & Web3 Conference',
    'Sustainable Business Forum', 'EdTech Innovation Summit', 'Data Science & Analytics Conference',
    'Cloud Computing Excellence', 'UX/UI Design Workshop', 'Startup Pitch Panel Discussion',
    'Leadership in Crisis Webinar', 'IoT & Smart Cities Forum', 'Biotech Breakthrough Summit',
    'DevOps Best Practices Workshop', 'Product Management Masterclass'
  ];

  const organizations = [
    'TechCorp Events', 'Innovation Partners', 'Global Summit Group', 'Future Events Ltd',
    'Healthcare Connect', 'FinTech Alliance', 'Marketing Pro Events', 'Cyber Defense Forum',
    'Blockchain Society', 'Green Future Events', 'EdTech Collective', 'Data Leaders Group',
    'Cloud Excellence', 'Design Hub', 'Austin Startup Hub', 'Leadership Institute',
    'Smart City Alliance', 'Biotech Innovations', 'DevOps Community', 'Product Guild'
  ];

  const cities = [
    'New York, NY', 'London, UK', 'Berlin, Germany', 'Singapore', 'Mumbai, India', 'Toronto, Canada',
    'San Francisco, CA', 'Amsterdam, Netherlands', 'Sydney, Australia', 'Tokyo, Japan', 'Dubai, UAE',
    'Stockholm, Sweden', 'Barcelona, Spain', 'Austin, TX', 'Tel Aviv, Israel', 'Boston, MA'
  ];

  const categories = ['keynote', 'panel', 'workshop', 'webinar', 'conference', 'summit'];
  const eventTypes = ['Conference', 'Workshop', 'Webinar', 'Summit', 'Forum', 'Panel Discussion'];
  const statuses = ['active', 'inactive', 'completed'];

    const opportunities: Opportunity[] = [];

  for (let i = 0; i < 200; i++) {
    const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)];
    const title = i < opportunityTitles.length ? 
      opportunityTitles[i] : 
      `${eventType} ${2024 + Math.floor(Math.random() * 2)}`;
    
    const organization = organizations[i % organizations.length];
    const location = cities[Math.floor(Math.random() * cities.length)];
    const category = categories[Math.floor(Math.random() * categories.length)];
    const status = i < 160 ? 'active' : statuses[Math.floor(Math.random() * statuses.length)]; // Most active
    
    const eventDate = dayjs().add(Math.floor(Math.random() * 180) + 7, 'days');
    const deadline = eventDate.subtract(Math.floor(Math.random() * 30) + 5, 'days');
    
    // Generate realistic engagement numbers
    const matchedCount = Math.floor(Math.random() * 15) + 1;
    const interestedCount = Math.floor(matchedCount * (0.3 + Math.random() * 0.7));
    const acceptedCount = Math.floor(interestedCount * (0.2 + Math.random() * 0.5));
    const rejectedCount = Math.floor(matchedCount * (0.1 + Math.random() * 0.3));
    
    opportunities.push({
      id: `op${i + 1}`,
      title,
      description: `Professional ${category} focusing on industry insights and networking`,
      organization,
      category: category as any,
      status: status as any,
      budget: Math.floor(Math.random() * 8000) + 1000,
      location,
      eventDate: eventDate.toISOString(),
      deadline: deadline.toISOString(),
      requirements: ['Professional experience', 'Public speaking skills', 'Industry expertise'],
      tags: ['Professional', 'Industry', 'Networking', 'Innovation'],
      externalUrl: `https://${organization.toLowerCase().replace(/[^a-z]/g, '')}.com/event/${i + 1}`,
      eventType,
      notes: Math.random() > 0.7 ? [{
        id: `n${i + 1}`,
        content: 'Client has specific requirements for this opportunity',
        author: 'Event Manager',
        timestamp: dayjs().subtract(Math.floor(Math.random() * 10), 'days').toISOString(),
      }] : [],
      matchedCount,
      interestedCount,
      acceptedCount,
      rejectedCount,
      createdAt: dayjs().subtract(Math.floor(Math.random() * 90), 'days').toISOString(),
      updatedAt: dayjs().subtract(Math.floor(Math.random() * 5), 'days').toISOString(),
    });
  }

  return opportunities;
};

export const generateDemoMatches = (): Match[] => {
  const matches: Match[] = [];
  const statuses = ['accepted', 'rejected', 'interested', 'pending'];
  const statusDistribution = [200, 300, 250, 100]; // Increased rejected matches
  
  const rejectionReasons = [
    'Speaker expertise does not match event requirements',
    'Speaker availability conflicts with event dates',
    'Budget constraints - speaker fee too high',
    'Speaker location too far from event venue',
    'Insufficient speaking experience for this level of event',
    'Speaker declined due to scheduling conflicts',
    'Event requirements do not align with speaker interests',
    'Speaker portfolio lacks relevant case studies',
    'Previous client feedback was not satisfactory',
    'Speaker requested terms that exceed budget',
    'Technical expertise level below event standards',
    'Speaker communication style does not fit event format'
  ];
  
  let matchId = 1;
  
  // Add specific matches for opportunity "op1" with more data
  const op1Matches = [
    { speakerId: 'sp1', status: 'accepted', matchingScore: 95 },
    { speakerId: 'sp2', status: 'interested', matchingScore: 87 },
    { speakerId: 'sp3', status: 'pending', matchingScore: 82 },
    { speakerId: 'sp4', status: 'rejected', matchingScore: 78, rejectionReason: 'Speaker expertise does not match event requirements' },
    { speakerId: 'sp5', status: 'rejected', matchingScore: 72, rejectionReason: 'Budget constraints - speaker fee too high' },
    { speakerId: 'sp6', status: 'accepted', matchingScore: 89 },
    { speakerId: 'sp7', status: 'interested', matchingScore: 85 },
    { speakerId: 'sp8', status: 'rejected', matchingScore: 68, rejectionReason: 'Speaker availability conflicts with event dates' },
    { speakerId: 'sp9', status: 'pending', matchingScore: 76 },
    { speakerId: 'sp10', status: 'rejected', matchingScore: 65, rejectionReason: 'Insufficient speaking experience for this level of event' },
    { speakerId: 'sp11', status: 'interested', matchingScore: 81 },
    { speakerId: 'sp12', status: 'rejected', matchingScore: 58, rejectionReason: 'Previous client feedback was not satisfactory' },
    { speakerId: 'sp13', status: 'accepted', matchingScore: 92 },
    { speakerId: 'sp14', status: 'pending', matchingScore: 74 },
    { speakerId: 'sp15', status: 'rejected', matchingScore: 62, rejectionReason: 'Speaker location too far from event venue' },
    { speakerId: 'sp16', status: 'interested', matchingScore: 79 },
    { speakerId: 'sp17', status: 'rejected', matchingScore: 56, rejectionReason: 'Technical expertise level below event standards' },
    { speakerId: 'sp18', status: 'accepted', matchingScore: 88 },
    { speakerId: 'sp19', status: 'rejected', matchingScore: 64, rejectionReason: 'Speaker declined due to scheduling conflicts' },
    { speakerId: 'sp20', status: 'pending', matchingScore: 77 },
    { speakerId: 'sp21', status: 'rejected', matchingScore: 59, rejectionReason: 'Event requirements do not align with speaker interests' },
    { speakerId: 'sp22', status: 'interested', matchingScore: 83 },
    { speakerId: 'sp23', status: 'rejected', matchingScore: 61, rejectionReason: 'Speaker portfolio lacks relevant case studies' },
    { speakerId: 'sp24', status: 'accepted', matchingScore: 90 },
    { speakerId: 'sp25', status: 'rejected', matchingScore: 57, rejectionReason: 'Speaker communication style does not fit event format' }
  ];

  // Add more specific matches for speaker sp5 across different opportunities
  const sp5AdditionalMatches = [
    { opportunityId: 'op2', status: 'accepted', matchingScore: 88 },
    { opportunityId: 'op3', status: 'interested', matchingScore: 85 },
    { opportunityId: 'op4', status: 'pending', matchingScore: 79 },
    { opportunityId: 'op5', status: 'rejected', matchingScore: 68, rejectionReason: 'Speaker fee exceeded budget limits' },
    { opportunityId: 'op6', status: 'accepted', matchingScore: 91 },
    { opportunityId: 'op7', status: 'interested', matchingScore: 82 },
    { opportunityId: 'op8', status: 'pending', matchingScore: 75 },
    { opportunityId: 'op9', status: 'rejected', matchingScore: 63, rejectionReason: 'Time zone conflicts for virtual event' },
    { opportunityId: 'op10', status: 'accepted', matchingScore: 87 },
    { opportunityId: 'op11', status: 'interested', matchingScore: 80 },
    { opportunityId: 'op12', status: 'pending', matchingScore: 73 },
    { opportunityId: 'op13', status: 'rejected', matchingScore: 60, rejectionReason: 'Client preferred different expertise focus' },
    { opportunityId: 'op14', status: 'accepted', matchingScore: 89 },
    { opportunityId: 'op15', status: 'interested', matchingScore: 84 },
    { opportunityId: 'op16', status: 'pending', matchingScore: 76 },
    { opportunityId: 'op17', status: 'rejected', matchingScore: 65, rejectionReason: 'Scheduling conflict with other commitments' }
  ];

  // Add more specific matches for speaker sp1 across different opportunities  
  const sp1AdditionalMatches = [
    { opportunityId: 'op2', status: 'interested', matchingScore: 92 },
    { opportunityId: 'op3', status: 'accepted', matchingScore: 96 },
    { opportunityId: 'op4', status: 'interested', matchingScore: 89 },
    { opportunityId: 'op5', status: 'accepted', matchingScore: 94 },
    { opportunityId: 'op6', status: 'rejected', matchingScore: 78, rejectionReason: 'Budget constraints - speaker fee too high' },
    { opportunityId: 'op7', status: 'interested', matchingScore: 87 },
    { opportunityId: 'op8', status: 'accepted', matchingScore: 93 },
    { opportunityId: 'op9', status: 'interested', matchingScore: 85 },
    { opportunityId: 'op10', status: 'rejected', matchingScore: 72, rejectionReason: 'Speaker availability conflicts with event dates' },
    { opportunityId: 'op11', status: 'accepted', matchingScore: 97 },
    { opportunityId: 'op12', status: 'interested', matchingScore: 90 },
    { opportunityId: 'op13', status: 'accepted', matchingScore: 95 },
    { opportunityId: 'op14', status: 'interested', matchingScore: 86 },
    { opportunityId: 'op15', status: 'rejected', matchingScore: 69, rejectionReason: 'Event format not aligned with speaker style' },
    { opportunityId: 'op16', status: 'accepted', matchingScore: 91 },
    { opportunityId: 'op17', status: 'interested', matchingScore: 88 },
    { opportunityId: 'op18', status: 'accepted', matchingScore: 93 },
    { opportunityId: 'op19', status: 'interested', matchingScore: 84 },
    { opportunityId: 'op20', status: 'rejected', matchingScore: 75, rejectionReason: 'Speaker location too far from event venue' }
  ];

  // Add op1 specific matches
  op1Matches.forEach((matchData, index) => {
    const match: any = {
      id: `m${matchId}`,
      speakerId: matchData.speakerId,
      opportunityId: 'op1',
      status: matchData.status,
      matchingScore: matchData.matchingScore,
      createdAt: dayjs().subtract(Math.floor(Math.random() * 60), 'days').toISOString(),
      updatedAt: dayjs().subtract(Math.floor(Math.random() * 10), 'days').toISOString(),
    };
    
    if (matchData.rejectionReason) {
      match.rejectionReason = matchData.rejectionReason;
    }
    
    matches.push(match);
    matchId++;
  });

  // Add sp5 specific matches across different opportunities
  sp5AdditionalMatches.forEach((matchData) => {
    const match: any = {
      id: `m${matchId}`,
      speakerId: 'sp5',
      opportunityId: matchData.opportunityId,
      status: matchData.status,
      matchingScore: matchData.matchingScore,
      createdAt: dayjs().subtract(Math.floor(Math.random() * 60), 'days').toISOString(),
      updatedAt: dayjs().subtract(Math.floor(Math.random() * 10), 'days').toISOString(),
    };
    
    if (matchData.rejectionReason) {
      match.rejectionReason = matchData.rejectionReason;
    }
    
    matches.push(match);
    matchId++;
  });

  // Add sp1 specific matches across different opportunities
  sp1AdditionalMatches.forEach((matchData) => {
    const match: any = {
      id: `m${matchId}`,
      speakerId: 'sp1',
      opportunityId: matchData.opportunityId,
      status: matchData.status,
      matchingScore: matchData.matchingScore,
      createdAt: dayjs().subtract(Math.floor(Math.random() * 60), 'days').toISOString(),
      updatedAt: dayjs().subtract(Math.floor(Math.random() * 10), 'days').toISOString(),
    };
    
    if (matchData.rejectionReason) {
      match.rejectionReason = matchData.rejectionReason;
    }
    
    matches.push(match);
    matchId++;
  });
  
  // Generate matches with realistic distribution for other opportunities
  for (let statusIndex = 0; statusIndex < statuses.length; statusIndex++) {
    const status = statuses[statusIndex] as any;
    const count = statusDistribution[statusIndex];
    
    for (let i = 0; i < count; i++) {
      const speakerId = `sp${Math.floor(Math.random() * 300) + 1}`; // Updated to use 300 speakers
      const opportunityId = `op${Math.floor(Math.random() * 200) + 2}`; // Skip op1 since we handled it above
      
      const match: any = {
        id: `m${matchId}`,
        speakerId,
        opportunityId,
        status,
        matchingScore: 50 + Math.floor(Math.random() * 50),
        createdAt: dayjs().subtract(Math.floor(Math.random() * 60), 'days').toISOString(),
        updatedAt: dayjs().subtract(Math.floor(Math.random() * 10), 'days').toISOString(),
      };
      
      // Add rejection reason for rejected matches
      if (status === 'rejected') {
        match.rejectionReason = rejectionReasons[Math.floor(Math.random() * rejectionReasons.length)];
      }
      
      matches.push(match);
      matchId++;
    }
  }
  
  return matches;
};

export const generateDemoIntakeForm = (): IntakeFormVersion => ({
  id: 'if1',
  version: 1,
  fields: [
    {
      id: 'f1',
      name: 'name',
      label: 'Full Name',
      type: 'text',
      required: true,
      order: 1,
    },
    {
      id: 'f2', 
      name: 'email',
      label: 'Email Address',
      type: 'email',
      required: true,
      order: 2,
    },
    {
      id: 'f3',
      name: 'expertise',
      label: 'Areas of Expertise',
      type: 'multiselect',
      options: ['Technology', 'Business', 'Healthcare', 'Education', 'Finance'],
      required: true,
      order: 3,
    },
  ],
  isActive: true,
  createdAt: dayjs().subtract(30, 'days').toISOString(),
  author: 'System Admin',
});

// Removed generateDemoScrapingData - now using API data directly
/*
  logs: [
    {
      id: 'sl1',
      startedAt: dayjs().subtract(2, 'hours').toISOString(),
      endedAt: dayjs().subtract(1, 'hours').toISOString(),
      status: 'success' as const,
      topic: 'Tech Conferences',
      itemsCollected: 145,
    },
    {
      id: 'sl2',
      startedAt: dayjs().subtract(6, 'hours').toISOString(),
      endedAt: dayjs().subtract(5, 'hours').toISOString(),
      status: 'error' as const,
      topic: 'Business Events',
      itemsCollected: 0,
      error: 'API rate limit exceeded',
    },
    {
      id: 'sl3',
      startedAt: dayjs().subtract(12, 'hours').toISOString(),
      endedAt: dayjs().subtract(11, 'hours').toISOString(),
      status: 'success' as const,
      topic: 'Healthcare Innovation',
      itemsCollected: 89,
    },
    {
      id: 'sl4',
      startedAt: dayjs().subtract(18, 'hours').toISOString(),
      endedAt: dayjs().subtract(17, 'hours').toISOString(),
      status: 'success' as const,
      topic: 'Marketing & Sales',
      itemsCollected: 201,
    },
    {
      id: 'sl5',
      startedAt: dayjs().subtract(1, 'days').toISOString(),
      endedAt: dayjs().subtract(1, 'days').add(45, 'minutes').toISOString(),
      status: 'error' as const,
      topic: 'Data Science',
      itemsCollected: 12,
      error: 'Connection timeout',
    },
    {
      id: 'sl6',
      startedAt: dayjs().subtract(1, 'days').subtract(4, 'hours').toISOString(),
      endedAt: dayjs().subtract(1, 'days').subtract(3, 'hours').toISOString(),
      status: 'success' as const,
      topic: 'Finance & Investment',
      itemsCollected: 67,
    },
    {
      id: 'sl7',
      startedAt: dayjs().subtract(1, 'days').subtract(8, 'hours').toISOString(),
      endedAt: dayjs().subtract(1, 'days').subtract(7, 'hours').toISOString(),
      status: 'success' as const,
      topic: 'Education Technology',
      itemsCollected: 134,
    },
    {
      id: 'sl8',
      startedAt: dayjs().subtract(2, 'days').toISOString(),
      endedAt: dayjs().subtract(2, 'days').add(1, 'hour').toISOString(),
      status: 'paused' as const,
      topic: 'Sustainability & Green Tech',
      itemsCollected: 23,
    },
    {
      id: 'sl9',
      startedAt: dayjs().subtract(2, 'days').subtract(6, 'hours').toISOString(),
      endedAt: dayjs().subtract(2, 'days').subtract(5, 'hours').toISOString(),
      status: 'error' as const,
      topic: 'Cybersecurity',
      itemsCollected: 0,
      error: 'Authentication failed',
    },
    {
      id: 'sl10',
      startedAt: dayjs().subtract(3, 'days').toISOString(),
      endedAt: dayjs().subtract(3, 'days').add(2, 'hours').toISOString(),
      status: 'success' as const,
      topic: 'Leadership & Management',
      itemsCollected: 178,
    },
    {
      id: 'sl11',
      startedAt: dayjs().subtract(3, 'days').subtract(8, 'hours').toISOString(),
      endedAt: dayjs().subtract(3, 'days').subtract(7, 'hours').toISOString(),
      status: 'success' as const,
      topic: 'Digital Transformation',
      itemsCollected: 93,
    },
    {
      id: 'sl12',
      startedAt: dayjs().subtract(4, 'days').toISOString(),
      endedAt: dayjs().subtract(4, 'days').add(1.5, 'hours').toISOString(),
      status: 'running' as const,
      topic: 'Product Development',
      itemsCollected: 56,
    },
    {
      id: 'sl13',
      startedAt: dayjs().subtract(4, 'days').subtract(12, 'hours').toISOString(),
      endedAt: dayjs().subtract(4, 'days').subtract(11, 'hours').toISOString(),
      status: 'success' as const,
      topic: 'Tech Conferences',
      itemsCollected: 298,
    },
    {
      id: 'sl14',
      startedAt: dayjs().subtract(5, 'days').toISOString(),
      endedAt: dayjs().subtract(5, 'days').add(3, 'hours').toISOString(),
      status: 'error' as const,
      topic: 'Business Events',
      itemsCollected: 45,
      error: 'Server error 500',
    },
    {
      id: 'sl15',
      startedAt: dayjs().subtract(6, 'days').toISOString(),
      endedAt: dayjs().subtract(6, 'days').add(2, 'hours').toISOString(),
      status: 'success' as const,
      topic: 'Healthcare Innovation',
      itemsCollected: 167,
    },
    {
      id: 'sl16',
      startedAt: dayjs().subtract(7, 'days').toISOString(),
      endedAt: dayjs().subtract(7, 'days').add(1, 'hour').toISOString(),
      status: 'success' as const,
      topic: 'Marketing & Sales',
      itemsCollected: 78,
    },
    {
      id: 'sl17',
      startedAt: dayjs().subtract(8, 'days').toISOString(),
      endedAt: dayjs().subtract(8, 'days').add(2.5, 'hours').toISOString(),
      status: 'paused' as const,
      topic: 'Finance & Investment',
      itemsCollected: 34,
    },
    {
      id: 'sl18',
      startedAt: dayjs().subtract(9, 'days').toISOString(),
      endedAt: dayjs().subtract(9, 'days').add(4, 'hours').toISOString(),
      status: 'success' as const,
      topic: 'Education Technology',
      itemsCollected: 245,
    },
  ] as ScrapingLog[],
  topics: [
    {
      id: 'st1',
      name: 'Tech Conferences',
      keywords: ['technology', 'AI', 'software', 'conference'],
      isActive: true,
      createdAt: dayjs().subtract(20, 'days').toISOString(),
    },
    {
      id: 'st2',
      name: 'Business Events', 
      keywords: ['business', 'startup', 'entrepreneur', 'summit'],
      isActive: true,
      createdAt: dayjs().subtract(15, 'days').toISOString(),
    },
    {
      id: 'st3',
      name: 'Healthcare Innovation',
      keywords: ['healthcare', 'medical', 'innovation', 'biotech', 'health'],
      isActive: true,
      createdAt: dayjs().subtract(12, 'days').toISOString(),
    },
    {
      id: 'st4',
      name: 'Marketing & Sales',
      keywords: ['marketing', 'sales', 'branding', 'advertising', 'growth'],
      isActive: true,
      createdAt: dayjs().subtract(10, 'days').toISOString(),
    },
    {
      id: 'st5',
      name: 'Data Science',
      keywords: ['data', 'analytics', 'machine learning', 'statistics', 'research'],
      isActive: false,
      createdAt: dayjs().subtract(8, 'days').toISOString(),
    },
    {
      id: 'st6',
      name: 'Finance & Investment',
      keywords: ['finance', 'investment', 'fintech', 'banking', 'cryptocurrency'],
      isActive: true,
      createdAt: dayjs().subtract(7, 'days').toISOString(),
    },
    {
      id: 'st7',
      name: 'Education Technology',
      keywords: ['education', 'learning', 'edtech', 'training', 'academic'],
      isActive: true,
      createdAt: dayjs().subtract(5, 'days').toISOString(),
    },
    {
      id: 'st8',
      name: 'Sustainability & Green Tech',
      keywords: ['sustainability', 'green', 'environment', 'renewable', 'climate'],
      isActive: true,
      createdAt: dayjs().subtract(4, 'days').toISOString(),
    },
    {
      id: 'st9',
      name: 'Cybersecurity',
      keywords: ['cybersecurity', 'security', 'privacy', 'blockchain', 'protection'],
      isActive: false,
      createdAt: dayjs().subtract(3, 'days').toISOString(),
    },
    {
      id: 'st10',
      name: 'Leadership & Management',
      keywords: ['leadership', 'management', 'corporate', 'executive', 'strategy'],
      isActive: true,
      createdAt: dayjs().subtract(2, 'days').toISOString(),
    },
    {
      id: 'st11',
      name: 'Digital Transformation',
      keywords: ['digital', 'transformation', 'automation', 'workflow', 'innovation'],
      isActive: true,
      createdAt: dayjs().subtract(1, 'days').toISOString(),
    },
    {
      id: 'st12',
      name: 'Product Development',
      keywords: ['product', 'development', 'design', 'UX', 'user experience'],
      isActive: true,
      createdAt: dayjs().subtract(6, 'hours').toISOString(),
    },
  ] as ScrapingTopic[],
});
*/

export const generateDemoAffiliates = (): Affiliate[] => [
  {
    id: '3',
    name: 'Mike Affiliate',
    email: '<EMAIL>',
    affiliateId: 'MA2024001',
    link: 'https://speakerbot.com/ref/MA2024001',
    clicks: 324,
    signups: 28,
    conversions: 12,
    createdAt: dayjs().subtract(10, 'days').toISOString(),
  },
  {
    id: '5',
    name: 'David Chen',
    email: '<EMAIL>',
    affiliateId: 'DC2024002',
    link: 'https://speakerbot.com/ref/DC2024002',
    clicks: 156,
    signups: 18,
    conversions: 8,
    createdAt: dayjs().subtract(15, 'days').toISOString(),
  },
  {
    id: '6',
    name: 'Lisa Thompson',
    email: '<EMAIL>',
    affiliateId: 'LT2024003',
    link: 'https://speakerbot.com/ref/LT2024003',
    clicks: 892,
    signups: 67,
    conversions: 34,
    createdAt: dayjs().subtract(40, 'days').toISOString(),
  },
  {
    id: '7',
    name: 'James Wilson',
    email: '<EMAIL>',
    affiliateId: 'JW2024004',
    link: 'https://speakerbot.com/ref/JW2024004',
    clicks: 445,
    signups: 32,
    conversions: 19,
    createdAt: dayjs().subtract(12, 'days').toISOString(),
  },
  {
    id: '8',
    name: 'Maria Garcia',
    email: '<EMAIL>',
    affiliateId: 'MG2024005',
    link: 'https://speakerbot.com/ref/MG2024005',
    clicks: 278,
    signups: 21,
    conversions: 11,
    createdAt: dayjs().subtract(8, 'days').toISOString(),
  },
  {
    id: '10',
    name: 'Sophie Laurent',
    email: '<EMAIL>',
    affiliateId: 'SL2024006',
    link: 'https://speakerbot.com/ref/SL2024006',
    clicks: 623,
    signups: 45,
    conversions: 23,
    createdAt: dayjs().subtract(6, 'days').toISOString(),
  },
  {
    id: '11',
    name: 'Ahmed Hassan',
    email: '<EMAIL>',
    affiliateId: 'AH2024007',
    link: 'https://speakerbot.com/ref/AH2024007',
    clicks: 367,
    signups: 29,
    conversions: 16,
    createdAt: dayjs().subtract(22, 'days').toISOString(),
  },
  {
    id: '12',
    name: 'Jessica Park',
    email: '<EMAIL>',
    affiliateId: 'JP2024008',
    link: 'https://speakerbot.com/ref/JP2024008',
    clicks: 712,
    signups: 58,
    conversions: 31,
    createdAt: dayjs().subtract(18, 'days').toISOString(),
  },
  {
    id: '13',
    name: 'Michael Brown',
    email: '<EMAIL>',
    affiliateId: 'MB2024009',
    link: 'https://speakerbot.com/ref/MB2024009',
    clicks: 189,
    signups: 14,
    conversions: 7,
    createdAt: dayjs().subtract(14, 'days').toISOString(),
  },
  {
    id: '14',
    name: 'Anna Kowalski',
    email: '<EMAIL>',
    affiliateId: 'AK2024010',
    link: 'https://speakerbot.com/ref/AK2024010',
    clicks: 534,
    signups: 41,
    conversions: 22,
    createdAt: dayjs().subtract(28, 'days').toISOString(),
  },
  {
    id: '15',
    name: 'Carlos Silva',
    email: '<EMAIL>',
    affiliateId: 'CS2024011',
    link: 'https://speakerbot.com/ref/CS2024011',
    clicks: 298,
    signups: 24,
    conversions: 13,
    createdAt: dayjs().subtract(9, 'days').toISOString(),
  },
  {
    id: '16',
    name: 'Jennifer Wu',
    email: '<EMAIL>',
    affiliateId: 'JWu2024012',
    link: 'https://speakerbot.com/ref/JWu2024012',
    clicks: 845,
    signups: 72,
    conversions: 38,
    createdAt: dayjs().subtract(5, 'days').toISOString(),
  },
  {
    id: '17',
    name: 'Thomas Mueller',
    email: '<EMAIL>',
    affiliateId: 'TM2024013',
    link: 'https://speakerbot.com/ref/TM2024013',
    clicks: 412,
    signups: 31,
    conversions: 17,
    createdAt: dayjs().subtract(32, 'days').toISOString(),
  },
  {
    id: '18',
    name: 'Priya Patel',
    email: '<EMAIL>',
    affiliateId: 'PP2024014',
    link: 'https://speakerbot.com/ref/PP2024014',
    clicks: 567,
    signups: 44,
    conversions: 25,
    createdAt: dayjs().subtract(4, 'days').toISOString(),
  },
];

export const generateDemoSettings = (): AppSettings => ({
  payment: {
    gateway: 'stripe',
    publicKey: 'pk_test_51234567890',
    webhookUrl: 'https://api.speakerbot.com/webhooks/stripe',
  },
  scrapingEnabled: true,
});

export const generateDemoPricingPlans = (): PricingPlan[] => [
  {
    id: 'plan1',
    name: 'Free',
    price: 0,
    features: ['Basic matching', 'Up to 3 opportunities per month'],
    isActive: true,
  },
  {
    id: 'plan2',
    name: 'Premium',
    price: 29,
    features: ['Advanced matching', 'Unlimited opportunities', 'Priority support'],
    isActive: true,
  },
];

export const generateDemoActivityLogs = (): ActivityLogEntry[] => {
  const speakers = generateDemoSpeakers();
  const opportunities = generateDemoOpportunities();
  const activities: ActivityLogEntry[] = [];

  // Generate 500 activity records
  for (let i = 0; i < 500; i++) {
    const speaker = speakers[Math.floor(Math.random() * speakers.length)];
    const opportunity = opportunities[Math.floor(Math.random() * opportunities.length)];
    
    const actionTypes = ['accepted', 'rejected', 'interested', 'speaker_registered', 'opportunity_created', 'affiliate_signup'];
    const actionType = actionTypes[Math.floor(Math.random() * actionTypes.length)];
    
    let details = '';
    let action = actionType;
    
    if (action === 'speaker_registered') {
      details = `New speaker registered: ${speaker.name}`;
    } else if (action === 'opportunity_created') {
      details = `New opportunity posted: ${opportunity.title}`;
    } else if (action === 'affiliate_signup') {
      details = 'New affiliate joined the program';
    } else if (action === 'accepted') {
      details = `${speaker.name} accepted ${opportunity.title}`;
    } else if (action === 'rejected') {
      details = `${speaker.name} rejected ${opportunity.title}`;
    } else if (action === 'interested') {
      details = `${speaker.name} showed interest in ${opportunity.title}`;
    }
    
    activities.push({
      id: `activity_${i + 1}`,
      userId: speaker.id,
      action: action,
      details,
      timestamp: dayjs().subtract(Math.floor(Math.random() * 30), 'days').subtract(Math.floor(Math.random() * 24), 'hours').toISOString(),
    });
  }
  
  // Sort by timestamp (newest first)
  return activities.sort((a, b) => dayjs(b.timestamp).unix() - dayjs(a.timestamp).unix());
};