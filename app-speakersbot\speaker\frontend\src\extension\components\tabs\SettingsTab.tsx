import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ExternalLink, Download, Info } from 'lucide-react';

export function SettingsTab() {
  return (
    <div className="space-y-4">
      {/* Extension Info */}
      <Card className="bg-surface border-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm text-foreground">Extension Info</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="text-xs text-foreground-muted">Version</span>
            <Badge variant="outline" className="text-xs">1.0.0</Badge>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-xs text-foreground-muted">Status</span>
            <Badge variant="default" className="text-xs">Connected</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Quick Settings */}
      <Card className="bg-surface border-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm text-foreground">Quick Settings</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <Button variant="outline" size="sm" className="w-full justify-start gap-2">
            <ExternalLink className="h-4 w-4" />
            Open Main Portal
          </Button>
          <Button variant="outline" size="sm" className="w-full justify-start gap-2">
            <Download className="h-4 w-4" />
            Export Data
          </Button>
          <Button variant="outline" size="sm" className="w-full justify-start gap-2">
            <Info className="h-4 w-4" />
            Help & Support
          </Button>
        </CardContent>
      </Card>

      {/* Advanced Settings */}
      <Card className="bg-surface border-border">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm text-foreground">Advanced</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-xs text-foreground-muted">
            Advanced settings and configuration options will be available in future updates.
          </p>
        </CardContent>
      </Card>
    </div>
  );
}