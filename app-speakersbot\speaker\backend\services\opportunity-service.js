const {
  RESPONSE_CODES,
  RESPONSE_MESSAGES,
} = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");
const {
  parsePagination,
  getPagingData,
  parseJSONSafely,
  buildSearchWhereClause,
  buildFilterWhereClause,
} = require("../helpers/app-hepler");
const {
  Opportunities,
  Speakers,
  SpeakerOpportunity,
  Streaks,
  sequelize,
  SpeakerOpportunityHistory,
} = require("../models");
const { Op } = require("sequelize");
const speakerHistoryService = require("./gamification-service");

const opportunityService = {};

// ------------------------- opportunity-service -------------------------
opportunityService.getAllOpportunities = async (getReq) => {
  try {
    // Get speaker ID from authenticated user
    const speakerId = getReq.speakerId || getReq.params.id;

    if (!speakerId) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Speaker ID is required"
      );
    }

    // Parse pagination
    const { page, limit, offset } = parsePagination(getReq.query);

    // Initialize where clauses
    let speakerOpportunityWhere = { speaker_id: speakerId };
    // start date should be greater than today because we want to show only upcoming opportunities
    let opportunityWhere = {
      is_active: true,
      start_date: { [Op.gte]: new Date() },
    };

    // Handle search functionality
    const searchTerm = getReq.query.search;
    if (searchTerm) {
      const searchWhere = buildSearchWhereClause(searchTerm, [
        "title",
        "organization",
        "event_type",
        "description",
        "industry",
        "venue",
        "city",
        "state",
        "country",
      ]);
      opportunityWhere = { ...opportunityWhere, ...searchWhere };
    }

    // Handle filter functionality
    const filter = parseJSONSafely(getReq.query.filter, "Invalid JSON filter");
    if (filter) {
      const filterWhere = buildFilterWhereClause(filter);

      // Separate filters for SpeakerOpportunity and Opportunities tables
      const { location, topic, eventDate, ...opportunityFilters } = filterWhere;
      // Add SpeakerOpportunity specific filters
      if (location) opportunityWhere.city = location;
      if (topic) opportunityWhere.event_type = topic;
      if (eventDate == "next30Days") {
        const today = new Date();
        const next30Days = new Date();
        next30Days.setDate(today.getDate() + 30);
        opportunityWhere.start_date = { [Op.between]: [today, next30Days] };
      } else if (eventDate == "next60Days") {
        const today = new Date();
        const next60Days = new Date();
        next60Days.setDate(today.getDate() + 60);
        opportunityWhere.start_date = { [Op.between]: [today, next60Days] };
      } else if (eventDate == "next90Days") {
        const today = new Date();
        const next90Days = new Date();
        next90Days.setDate(today.getDate() + 90);
        opportunityWhere.start_date = { [Op.between]: [today, next90Days] };
      }
      // Add Opportunities table filters
      opportunityWhere = { ...opportunityWhere, ...opportunityFilters };
    }

    // Handle date range filters
    const { start_date, end_date } = getReq.query;
    if (start_date || end_date) {
      const dateFilter = {};

      if (start_date) {
        dateFilter[Op.gte] = new Date(start_date);
      }

      if (end_date) {
        const endDateTime = new Date(end_date);
        endDateTime.setDate(endDateTime.getDate() + 1);
        dateFilter[Op.lt] = endDateTime;
      }

      opportunityWhere.start_date = dateFilter;
    }

    // Find opportunities with all filters applied
    const opportunities = await SpeakerOpportunity.findAll({
      where: speakerOpportunityWhere,
      attributes: ["id", "status", "overall_score", "created_at", "updated_at"],
      include: [
        {
          model: Opportunities,
          as: "opportunity",
          attributes: [
            "id",
            "title",
            "organization",
            "event_type",
            "description",
            "start_date",
            "end_date",
            "industry",
            [
              sequelize.fn(
                "CONCAT_WS",
                ", ",
                sequelize.col("venue"),
                sequelize.col("city"),
                sequelize.col("state"),
                sequelize.col("country")
              ),
              "location",
            ],
            "created_at",
            "updated_at",
          ],
          where: opportunityWhere,
        },
      ],
      limit: limit,
      offset: offset,
    });

    // Get total count for pagination
    const totalCount = await SpeakerOpportunity.count({
      where: speakerOpportunityWhere,
      include: [
        {
          model: Opportunities,
          as: "opportunity",
          where: opportunityWhere,
        },
      ],
    });

    const pageData = getPagingData(totalCount, limit, page);

    return {
      status: true,
      message: `Found ${opportunities.length} opportunities for speaker`,
      data: {
        opportunities,
        speakerId: parseInt(speakerId),
        totalCount: totalCount,
        pageData: pageData,
        appliedFilters: {
          search: searchTerm || null,
          filter: filter || null,
        },
      },
    };
  } catch (error) {
    console.error("Error in getAllOpportunities:", error);
    throw error;
  }
};

opportunityService.getAllTodayOpportunities = async (req) => {
  try {
    // Get speaker ID from authenticated user
    const speakerId = req.speakerId || req.userId || req.user?.id;

    if (!speakerId) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Speaker ID is required"
      );
    }
    // if not lastlogindate then we check for today's date at 00:00 hours
    const lastLoginDate = new Date();
    // Parse pagination
    // Start of the day (00:00:00)
    const startOfDay = new Date(lastLoginDate.setHours(0, 0, 0, 0));
    // End of the day (23:59:59.999)
    const endOfDay = new Date(lastLoginDate.setHours(23, 59, 59, 999));
    const { page, limit, offset } = parsePagination(req.query);

    // Initialize where clauses
    let speakerOpportunityWhere = {
      speaker_id: speakerId,
      created_at: {
        [Op.between]: [startOfDay, endOfDay],
      },
    };
    let opportunityWhere = {
      is_active: true,
      start_date: { [Op.gte]: new Date() }, // Only show future opportunities
    };
    // Handle search functionality
    const searchTerm = req.query.search;
    if (searchTerm) {
      const searchWhere = buildSearchWhereClause(searchTerm, [
        "title",
        "organization",
        "event_type",
        "description",
        "industry",
        "venue",
        "city",
        "state",
        "country",
      ]);
      opportunityWhere = { ...opportunityWhere, ...searchWhere };
    }

    // Handle filter functionality
    const filter = parseJSONSafely(req.query.filter, "Invalid JSON filter");
    console.log("filter", filter);
    if (filter) {
      const filterWhere = buildFilterWhereClause(filter);

      // Separate filters for SpeakerOpportunity and Opportunities tables
      const { location, topic, eventDate, ...opportunityFilters } = filterWhere;
      console.log("topic", topic);
      // Add SpeakerOpportunity specific filters
      if (location) opportunityWhere.city = location;
      if (topic) opportunityWhere.event_type = topic;
      if (eventDate == "next30Days") {
        const today = new Date();
        const next30Days = new Date();
        next30Days.setDate(today.getDate() + 30);
        opportunityWhere.start_date = { [Op.between]: [today, next30Days] };
      } else if (eventDate == "next60Days") {
        const today = new Date();
        const next60Days = new Date();
        next60Days.setDate(today.getDate() + 60);
        opportunityWhere.start_date = { [Op.between]: [today, next60Days] };
      } else if (eventDate == "next90Days") {
        const today = new Date();
        const next90Days = new Date();
        next90Days.setDate(today.getDate() + 90);
        opportunityWhere.start_date = { [Op.between]: [today, next90Days] };
      }
      // Add Opportunities table filters
      opportunityWhere = { ...opportunityWhere, ...opportunityFilters };
    }
    // Handle date range filters
    const { start_date, end_date } = req.query;
    if (start_date || end_date) {
      const dateFilter = {};

      if (start_date) {
        dateFilter[Op.gte] = new Date(start_date);
      }

      if (end_date) {
        const endDateTime = new Date(end_date);
        endDateTime.setDate(endDateTime.getDate() + 1);
        dateFilter[Op.lt] = endDateTime;
      }

      // Apply date filter to start_date while preserving created_at filter for last login
      opportunityWhere.start_date = dateFilter;
    }

    // Find opportunities since last login with all filters applied
    const opportunities = await SpeakerOpportunity.findAll({
      where: speakerOpportunityWhere,
      attributes: ["id", "status", "overall_score", "created_at", "updated_at"],
      include: [
        {
          model: Opportunities,
          as: "opportunity",
          attributes: [
            "id",
            "title",
            "organization",
            "event_type",
            "description",
            "start_date",
            "end_date",
            "industry",
            [
              sequelize.fn(
                "CONCAT_WS",
                ", ",
                sequelize.col("venue"),
                sequelize.col("city"),
                sequelize.col("state"),
                sequelize.col("country")
              ),
              "location",
            ],
            "created_at",
            "updated_at",
          ],
          where: opportunityWhere,
        },
      ],
      limit: limit,
      offset: offset,
      order: [["created_at", "DESC"]],
    });

    // Get total count for pagination
    const totalCount = await SpeakerOpportunity.count({
      where: speakerOpportunityWhere,
      include: [
        {
          model: Opportunities,
          as: "opportunity",
          where: opportunityWhere,
        },
      ],
    });

    const pageData = getPagingData(totalCount, limit, page);
    return {
      status: true,
      message: `Found ${opportunities.length} new opportunities since last login`,
      data: {
        opportunities,
        // speakerId: parseInt(speakerId),
        // lastLoginDate: lastLoginDate,
        totalCount: totalCount,
        pageData: pageData,
        // appliedFilters: {
        //   search: searchTerm || null,
        //   filter: filter || null,
        //   // expiredInLast72h: req.query.expiredInLast72h || null,
        //   dateRange: {
        //     start_date: start_date || null,
        //     end_date: end_date || null,
        //   },
        //   sinceLastLogin: lastLoginDate,
        // },
      },
    };
  } catch (error) {
    console.error("Error in getAllOpportunitiesSinceLastLogin:", error);
    throw error;
  }
};
/**
 * Update opportunity by id
 * @param {Object} updateReq - The request object containing update details.
 * @return {Promise<Object>} Result of the update operation.
 */

opportunityService.updateOpportunity = async (updateReq) => {
  try {
    const userId = updateReq.userId || updateReq.user?.id;
    const opportunityId = updateReq.params.id;

    if (!opportunityId) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Opportunity ID is required"
      );
    }

    // Check if the opportunity exists and if the speaker has access to it
    const existingOpportunity = await SpeakerOpportunity.findOne({
      where: { id: opportunityId },
    });
    if (!existingOpportunity) {
      throw new CustomError(
        RESPONSE_CODES.NOT_FOUND,
        "Opportunity not found or you don't have permission to update it"
      );
    }

    // Update the opportunity status in the speaker_opportunity relationship table
    const { status, note = "" } = updateReq.body;
    console.log("status", status);
    // Map status to gamification events
    const statusEventMap = {
      booked: "booked_opportunity",
      applied: "applied_opportunity",
    };

    // Trigger event points if valid status
    if (status && statusEventMap[status]) {
      await speakerHistoryService.callEventPoints(userId, statusEventMap[status]);
    }

    // Update the SpeakerOpportunity record using both speaker_id and opportunity_id
    await SpeakerOpportunity.update(
      { status: status.toLowerCase(), reasons: note },
      {
        where: {
          id: opportunityId,
          speaker_id: userId,
        },
      }
    );
    await SpeakerOpportunityHistory.create({
      speaker_opportunity_id: opportunityId,
      status: status.toLowerCase(),
      reason: note,
      created_at: new Date(),
      updated_at: new Date(),
      speaker_id: userId,
    });
    return {
      status: true,
      message: "Opportunity updated successfully",
    };
  } catch (error) {
    console.error("Error in updateOpportunity:", error);
    throw error;
  }
};

/**
 * Get opportunities pipeline data for the authenticated speaker.
 * @param {Object} req - The request object containing speakerId.
 * @return {Promise<Object>} Result containing pipeline data.
 */
// ------------------------- opportunity-service -------------------------

opportunityService.getOpportunitiesPipeline = async (req) => {
  try {
    // Get speaker ID from authenticated user
    const speakerId = req.userId || req.user?.id;

    // Extract date filters from query parameters
    const { start_date, end_date } = req.query || {};
    // console.log("start_date, end_date", start_date, end_date);
    // Build where clause for SpeakerOpportunity
    const whereClause = { speaker_id: speakerId };

    // Build where clause for Opportunities (date filtering)
    const opportunityWhereClause = { is_active: true };

    // Add date filtering if provided
    if (start_date || end_date) {
      const dateFilter = {};

      if (start_date) {
        dateFilter[Op.gte] = new Date(start_date);
      }

      if (end_date) {
        // Add 1 day to end_date to include the entire end date
        const endDateTime = new Date(end_date);
        endDateTime.setDate(endDateTime.getDate() + 1);
        dateFilter[Op.lt] = endDateTime;
      }

      opportunityWhereClause.start_date = dateFilter;
    }

    // Fetch opportunities pipeline data from the database
    const pipelineData = await SpeakerOpportunity.findAll({
      where: whereClause,
      attributes: ["status", "overall_score"],
      include: [
        {
          model: Opportunities,
          as: "opportunity",
          attributes: [
            "title",
            "organization",
            "event_type",
            "created_at",
            "start_date",
          ],
          where:
            Object.keys(opportunityWhereClause).length > 0
              ? opportunityWhereClause
              : undefined,
        },
      ],
      order: [["created_at", "DESC"]],
    });

    // Apply logic to mark pending opportunities as lost if start_date has passed
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const processedPipelineData = pipelineData.map((item) => {
      const opportunity = item.get({ plain: true });
      // Check if status is pending and start_date is less than today
      if (
        opportunity.status === "pending" &&
        opportunity.opportunity &&
        opportunity.opportunity.start_date &&
        new Date(opportunity.opportunity.start_date) < today
      ) {
        // Dynamically set status to lost
        opportunity.status = "lost";
      }

      return opportunity;
    });

    // Group opportunities by status
    const groupedPipelineData = {
      pending: [],
      accepted: [],
      rejected: [],
      interested: [],
      applied: [],
      interviewing: [],
      booked: [],
      lost: []
    };

    // Process each opportunity and format for frontend
    processedPipelineData.forEach((item) => {
      const formattedOpportunity = {
        id: item.opportunity?.id || null,
        title: item.opportunity?.title || 'N/A',
        organizer: item.opportunity?.organization || 'N/A',
        event_type: item.opportunity?.event_type || 'N/A',
        start_date: item.opportunity?.start_date || null,
        created_at: item.opportunity?.created_at || null,
        matchScore: item.overall_score || 0,
        status: item.status
      };

      // Add to appropriate status array
      if (groupedPipelineData.hasOwnProperty(item.status)) {
        groupedPipelineData[item.status].push(formattedOpportunity);
      }
    });

    return {
      status: true,
      message: "Opportunities pipeline data fetched successfully",
      data: groupedPipelineData,
      filters: {
        start_date: start_date || null,
        end_date: end_date || null,
        total_records: processedPipelineData.length,
      },
    };
  } catch (error) {
    console.error("Error in getOpportunitiesPipeline:", error);
    throw error;
  }
};

opportunityService.getLostOpportunitiesTracker = async (req) => {
  try {
    // Get speaker ID from authenticated user
    const { page, limit, offset } = parsePagination(req.query);
    const speakerId = req.userId || req.user?.id;
    if (!speakerId) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Speaker ID is required"
      );
    }
    const { count: totalCount, rows: allLostOpportunities } = await SpeakerOpportunity.findAndCountAll({
      where: {
        speaker_id: speakerId,
        status: "pending",
      },
      attributes: ["id", "status", "overall_score", "created_at", "updated_at"],
      include: [
        {
          model: Opportunities,
          as: "opportunity",
          where: {
            is_active: true,
            start_date: { [Op.lt]: new Date() }, // start_date has passed
          },
          attributes: [
            "title",
            "organization",
            "event_type",
            "start_date",
            "end_date",
          ],
        },
      ],
    });
    // Fetch lost opportunities for the speaker
    const lostOpportunities = await SpeakerOpportunity.findAll({
      where: {
        speaker_id: speakerId,
        status: "pending",
      },
      attributes: ["id", "status", "overall_score", "created_at", "updated_at"],
      include: [
        {
          model: Opportunities,
          as: "opportunity",
          where: {
            is_active: true,
            start_date: { [Op.lt]: new Date() }, // start_date has passed
          },
          attributes: [
            "title",
            "organization",
            "event_type",
            "start_date",
            "end_date",
          ],
        },
      ],
      limit,
      offset,
      order: [["created_at", "DESC"]],
    });
    // this also based on the logic that if status is pending and start_date has passed then we consider it as lost
    const performanceScore = allLostOpportunities
      .map((opportunity) => opportunity.overall_score)
      .reduce((a, b) => a + b, 0);
    return {
      status: true,
      message: "Lost opportunities fetched successfully",
      data: {
        lostOpportunities,
        totalLost: totalCount,
        performanceScore: (performanceScore / totalCount).toFixed(2) || 0,
        pagination: getPagingData(totalCount, limit, page),
      },
    };
  } catch (error) {
    console.error("Error in getLostOpportunitiesTracker:", error);
    throw error;
  }
};

opportunityService.getAllOpportunitiesDueIn72Hours = async (req) => {
  try {
    // Extract speaker ID from authenticated user session
    const speakerId = req.user?.id || req.userId;
    console.log("speakerId", speakerId);
    // Validate that speaker authentication is present
    if (!speakerId) {
      throw new CustomError(
        RESPONSE_CODES.UNAUTHORIZED,
        "Speaker authentication required"
      );
    }
    const now = new Date();
    const seventyTwoHoursLater = new Date(now.getTime() + 72 * 60 * 60 * 1000);
    // Parse pagination
    const { page, limit, offset } = parsePagination(req.query);
    // Find opportunities with start_date within the next 72 hours
    const { count: totalCount, rows: opportunities } =
      await SpeakerOpportunity.findAndCountAll({
        where: { speaker_id: speakerId },
        attributes: [
          "id",
          "status",
          "overall_score",
          "created_at",
          "updated_at",
        ],
        include: [
          {
            model: Opportunities,
            as: "opportunity",
            where: {
              is_active: true,
              start_date: { [Op.between]: [now, seventyTwoHoursLater] },
            },
            attributes: [
              "id",
              "title",
              "organization",
              "event_type",
              "description",
              "start_date",
              "end_date",
            ],
          },
        ],
        limit: limit,
        offset: offset,
        order: [["created_at", "DESC"]],
      });
    const pageData = getPagingData(totalCount, limit, page);
    return {
      status: true,
      message: "Opportunities due in 72 hours fetched successfully",
      data: opportunities,
      pagination: pageData,
    };
  } catch (error) {
    console.error("Error in getAllOpportunitiesDueIn72Hours:", error);
    throw error;
  }
};

opportunityService.getAllOpportunityTopics = async (req) => {
  try {
    // Fetch distinct event types from Opportunities table
    const topics = await Opportunities.findAll({
      attributes: [
        [sequelize.fn("DISTINCT", sequelize.col("event_type")), "event_type"],
      ],
    });
    const topicList = topics.map((t) => t.event_type).filter((t) => t); // Filter out null/undefined topics
    return {
      status: true,
      message: "Opportunity topics fetched successfully",
      data: topicList,
    };
  } catch (error) {
    console.error("Error in getAllOpportunityTopics:", error);
    throw error;
  }
};
opportunityService.getAllOpportunityLocations = async (req) => {
  try {
    // Fetch distinct locations (city, state, country) from Opportunities table
    const locations = await Opportunities.findAll({
      attributes: [[sequelize.fn("DISTINCT", sequelize.col("city")), "city"]],
    });
    const locationCities = locations.map(loc => loc.city).filter(city => city); // Filter out null/undefined cities
    return {
      status: true,
      message: "Opportunity locations fetched successfully",
      data: locationCities,
    };
  } catch (error) {
    console.error("Error in getAllOpportunityLocations:", error);
    throw error;
  }
};

module.exports = opportunityService;