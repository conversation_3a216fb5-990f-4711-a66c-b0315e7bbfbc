"""
Content Extraction Module for Speaker Opportunity Scraper

This module provides comprehensive content extraction capabilities for parsing
event descriptions, contact information, virtual event detection, and metadata
from various sources. It handles multiple content formats and provides intelligent
analysis for speaker opportunity identification.

Key Features:
- Virtual event detection
- Comprehensive description extraction
- Contact information parsing
- Form field analysis
- Content summarization
- Metadata extraction

Author: Speaker Bot Team
Version: 1.0
Last Updated: 2024
"""

from typing import Dict
from bs4 import BeautifulSoup
import logging
logger = logging.getLogger(__name__)

def extract_form_fields(soup: BeautifulSoup, base_url: str = '') -> Dict:
    """
    Extract speaker submission form fields - optimized for speaker opportunities only.
    
    This function focuses specifically on speaker submission forms, excluding
    newsletter, contact, sales, and enquiry forms. If no speaker forms are found,
    it searches for speaker submission links and scrapes forms from those URLs.
    
    Args:
        soup (BeautifulSoup): Parsed HTML object
        base_url (str): Base URL for resolving relative form actions
        
    Returns:
        Dict: Form data with form_url and field names, or empty dict if none found
    """    
    # Debug: Check what elements contain "submit" or "abstract" text
    soup.find_all(string=lambda text: text and ('submit' in text.lower() or 'abstract' in text.lower()))
    
    # First, look for abstract submission links (most specific)
    submission_link_url = _find_speaker_submission_link(soup, base_url)
    
    if submission_link_url:
        # Try to scrape form from the submission link URL
        form_data = _scrape_form_from_url(submission_link_url)
        if form_data:
            # Use the submission page URL as the form URL
            form_data['form_url'] = submission_link_url
            return form_data
    
    # If no abstract submission form found, try to find speaker submission forms on main page
    form_data = _find_speaker_submission_form(soup, base_url)
    
    if form_data:
        return form_data
    
    # If no submission links found, look for submission buttons
    submission_button_url = _find_speaker_submission_button(soup, base_url)
    
    if submission_button_url:
        # Try to scrape form from the submission button URL
        form_data = _scrape_form_from_url(submission_button_url)
        if form_data:
            # Use the submission page URL as the form URL
            form_data['form_url'] = submission_button_url
            return form_data
    
    # No speaker forms, links, or buttons found
    return {}


def _find_speaker_submission_form(soup: BeautifulSoup, base_url: str) -> Dict:
    """Find speaker submission forms, excluding newsletter/contact/sales forms."""
    forms = soup.find_all('form')
    
    for form in forms:
        form_text = form.get_text().lower()
        form_html = str(form).lower()
        
        # Skip non-speaker forms
        if _is_non_speaker_form(form_text, form_html):
            continue
            
        # Check if this is a speaker submission form
        if _is_speaker_submission_form(form, form_text, form_html):
            # Use the original page URL instead of form action URL
            form_url = base_url
            field_names = extract_speaker_fields_from_form(form)
                        
            if field_names:
                return {'form_url': form_url, 'fields': field_names}
    
    return {}


def _is_non_speaker_form(form_text: str, form_html: str) -> bool:
    """Check if form is newsletter, contact, sales, or enquiry form."""
    non_speaker_indicators = [
        'newsletter', 'subscribe', 'signup', 'sign up',
        'contact us', 'get in touch', 'reach out',
        'sales inquiry', 'sales enquiry', 'sales contact',
        'general inquiry', 'general enquiry', 'general contact',
        'support', 'help', 'feedback', 'complaint', 'search now',
        'venue country', 'venue state', 'event topic', 'by keywords',
        'select venue country', 'select venue state', 'select event topic',
        'registration', 'login', 'username', 'password', 'sign in', 'log in', 'keep me signed in', 'forgot password'
    ]
    
    # If form contains non-speaker indicators and no speaker indicators, skip it
    has_non_speaker = any(indicator in form_text for indicator in non_speaker_indicators)
    has_speaker = any(keyword in form_text for keyword in ['speaker', 'presenter', 'proposal', 'submission', 'abstract'])
    
    return has_non_speaker and not has_speaker


def _is_speaker_submission_form(form, form_text: str, form_html: str) -> bool:
    """Check if form is specifically for speaker submissions - RELAXED MODE."""
    
    # High-priority speaker keywords that must be present
    primary_speaker_keywords = [
        'call for speakers', 'cfp', 'speaker application', 'speaker proposal',
        'speaker submission', 'become a speaker', 'submit proposal', 'submit your proposal',
        'abstract submission', 'call for papers', 'speaker interest',
        'presenter application', 'keynote speaker', 'abstract title', 'abstract category'
    ]
    
    # Secondary speaker keywords (less strict)
    secondary_speaker_keywords = [
        'speaker', 'presenter', 'keynote', 'panelist', 'workshop',
        'proposal', 'application', 'submission', 'candidate',
        'speaking', 'presentation', 'session', 'talk', 'lecture',
        'abstract', 'track', 'university', 'institute', 'designation'
    ]
    
    # Check for primary keywords first (must have at least one)
    has_primary_keyword = any(keyword in form_text or keyword in form_html 
                             for keyword in primary_speaker_keywords)
    
    # Check form action URL for speaker keywords
    form_action = form.get('action', '').lower()
    has_action_keyword = any(keyword in form_action 
                            for keyword in primary_speaker_keywords + secondary_speaker_keywords)
    
    # Check form ID and class for speaker keywords
    form_id = form.get('id', '').lower()
    form_class = ' '.join(form.get('class', [])).lower()
    has_id_class_keyword = any(keyword in form_id or keyword in form_class 
                              for keyword in primary_speaker_keywords + secondary_speaker_keywords)
    
    # Check form labels and surrounding context
    labels = form.find_all('label')
    has_label_keyword = False
    for label in labels:
        label_text = label.get_text().lower()
        if any(keyword in label_text for keyword in primary_speaker_keywords):
            has_label_keyword = True
            break
    
    # Check surrounding context (headings, paragraphs near the form)
    form_parent = form.parent
    has_context_keyword = False
    if form_parent:
        context_text = form_parent.get_text().lower()
        if any(keyword in context_text for keyword in primary_speaker_keywords):
            has_context_keyword = True
    
    # Check for abstract-related fields (very specific to speaker submissions)
    form_inputs = form.find_all(['input', 'textarea', 'select'])
    has_abstract_fields = False
    for input_field in form_inputs:
        field_name = input_field.get('name', '').lower()
        field_placeholder = input_field.get('placeholder', '').lower()
        if any(keyword in field_name or keyword in field_placeholder 
               for keyword in ['abstract', 'track', 'category', 'university', 'institute', 'designation']):
            has_abstract_fields = True
            break
    
    # RELAXED REQUIREMENT: Accept if any indicator is present
    if has_primary_keyword or has_action_keyword or has_id_class_keyword or has_label_keyword or has_context_keyword or has_abstract_fields:
        return True
    
    # Additional check: If form has secondary keywords, verify it's not a general contact form
    has_secondary_keyword = any(keyword in form_text or keyword in form_html 
                              for keyword in secondary_speaker_keywords)
    
    if has_secondary_keyword:
        # Check if it's clearly NOT a speaker form (has contact/general indicators)
        non_speaker_indicators = [
            'contact us', 'get in touch', 'general inquiry', 'newsletter',
            'subscribe', 'feedback', 'support', 'help'
        ]
        has_non_speaker = any(indicator in form_text for indicator in non_speaker_indicators)
        
        # Only return True if it has secondary keywords AND no non-speaker indicators
        return not has_non_speaker
    
    return False


def _find_speaker_submission_link(soup: BeautifulSoup, base_url: str) -> str:
    """Find speaker submission links and return their URLs."""
    
    submission_link_patterns = [
        'abstract submission', 'abstract-submission', 'abstract_submission',
        'speaker submission', 'speaker-submission', 'speaker_submission',
        'call for speakers', 'call-for-speakers', 'call_for_speakers',
        'speaker application', 'speaker-application', 'speaker_application',
        'submit abstract', 'submit-abstract', 'submit_abstract',
        'proposal submission', 'proposal-submission', 'proposal_submission',
        'cfp', 'call for papers', 'call-for-papers', 'call_for_papers',
        'become a speaker', 'become-a-speaker', 'become_speaker',
        'speaking opportunity', 'speaking-opportunity', 'speaking_opportunity'
    ]
    
    # Look for links with submission text
    links = soup.find_all('a', href=True)
    
    for link in links:
        link_text = link.get_text().lower().strip()
        link_href = link.get('href', '').lower()
        
        # Check if link text or href contains submission patterns
        for pattern in submission_link_patterns:
            if pattern in link_text or pattern in link_href:
                # Build complete URL
                href = link.get('href')
                if href.startswith('http'):
                    return href
                elif href.startswith('/'):
                    from urllib.parse import urljoin
                    return urljoin(base_url, href)
                else:
                    from urllib.parse import urljoin
                    return urljoin(base_url, href)
    
    return ''


def _find_speaker_submission_button(soup: BeautifulSoup, base_url: str) -> str:
    """Find speaker submission buttons and return their URLs."""
    
    submission_button_patterns = [
        'submit abstract', 'submit proposal', 'submit your proposal', 'call for speakers',
        'speaker application', 'become a speaker', 'speaker submission',
        'apply to speak', 'speaker proposal', 'cfp', 'call for papers',
        'speaker interest', 'speaker inquiry', 'abstract submission', 'abstract-submission',
        'paper submission', 'submit', 'abstract', 'proposal', 'speaker', 'presentation'
    ]
    
    # Look for buttons and links with submission text
    buttons = soup.find_all(['button', 'a', 'input'], 
                          string=lambda text: text and any(pattern in text.lower() 
                                                         for pattern in submission_button_patterns))
    
    for i, button in enumerate(buttons):
        button_text = button.get_text().strip() if button.get_text() else button.get('value', '').strip()
    
    for button in buttons:
        # Get URL from href, onclick, or form action
        url = _extract_button_url(button, base_url)
        if url:
            return url
    
    return ''


def _extract_button_url(button, base_url: str) -> str:
    """Extract URL from button element."""
    # Check href attribute
    if button.get('href'):
        href = button.get('href')
        if href.startswith('http'):
            return href
        elif href.startswith('/'):
            from urllib.parse import urljoin
            return urljoin(base_url, href)
    
    # Check onclick attribute
    onclick = button.get('onclick', '')
    if onclick:
        import re
        url_match = re.search(r'["\']([^"\']+)["\']', onclick)
        if url_match:
            url = url_match.group(1)
            if url.startswith('http'):
                return url
            elif url.startswith('/'):
                from urllib.parse import urljoin
                return urljoin(base_url, url)
    
    # Check if button is inside a form
    form = button.find_parent('form')
    if form:
        return _build_form_url(form.get('action', ''), base_url)
    
    return ''


def _build_form_url(form_action: str, base_url: str) -> str:
    """Build complete form URL from action attribute."""
    if not form_action:
        return base_url
    
    if form_action.startswith('http'):
        return form_action
    elif form_action.startswith('/'):
        from urllib.parse import urljoin
        return urljoin(base_url, form_action)
    else:
        from urllib.parse import urljoin
        return urljoin(base_url, form_action)


def _scrape_form_from_url(url: str) -> Dict:
    """Scrape form fields from a given URL."""
    try:
        import requests
        from bs4 import BeautifulSoup
        
        response = requests.get(url, timeout=15)
        if response.status_code == 200:
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Debug: Check what's on the submission page
            soup.find_all('form')
            
            # Check for any elements containing form-related text
            soup.find_all(string=lambda text: text and any(word in text.lower() for word in ['form', 'submit', 'application', 'proposal']))
            
            return _find_speaker_submission_form(soup, url)
    except Exception as e:
        logger.error(f"CONTENT_EXTRACTOR: Error scraping {url}: {e}")
    
    return {}

def is_speaker_relevant_field(field_name, input_field) -> bool:
    """
    Determine if a form field is relevant for speaker submissions.
    Very inclusive approach to capture all form fields except security/tracking.
    """
    # Define patterns to exclude (security, tracking, etc.)
    exclude_patterns = [
        'csrf', 'token', 'authenticity', 'nonce', 'security',
        'captcha', 'recaptcha', 'honeypot', 'spam',
        'submit', 'button', 'action', 'method',
        'digest', 'hash', 'signature', 'checksum',
        'subscribe', 'newsletter', 'marketing', 'promotional',
        'group[', 'option[', 'choice[', 'radio[', 'checkbox[',
        'hidden', 'internal', 'system', 'admin',
        'b_', 'utm_', 'gclid', 'fbclid', 'ref',
        'session', 'cookie', 'tracking', 'analytics',
        'password', 'confirm_password', 'repeat_password', 'terms', 'privacy',
        'id', 'user_id', 'form_id', 'record_id', 'entry_id',  # Database/system IDs
        'search', 'q'  # site-wide search fields
    ]
    
    # Skip if matches exclude patterns
    for exclude_pattern in exclude_patterns:
        if exclude_pattern in field_name:
            return False
    
    # Include all other fields with meaningful names
    if field_name and len(field_name) > 1 and not field_name.isdigit():
        return True
    
    return False


def get_field_label(input_field):
    """Get meaningful label for input field by looking at associated HTML elements.
    Priority: 1. name attribute, 2. placeholder, 3. label text
    """
    import re
    
    def clean_text(text):
        """Clean text to field name."""
        if not text:
            return None
        
        # Remove extra whitespace, tabs, newlines
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove special chars except spaces, convert to lowercase
        cleaned = re.sub(r'[^\w\s]', '', text.lower())
        
        # Replace multiple spaces with single underscore
        cleaned = re.sub(r'\s+', '_', cleaned)
        
        # Remove leading/trailing underscores
        cleaned = cleaned.strip('_')
        
        # Limit length to reasonable size (max 50 chars)
        if len(cleaned) > 50:
            cleaned = cleaned[:50].rstrip('_')
        
        return cleaned if cleaned and len(cleaned) > 1 else None
    
    # 1. PRIORITY: Check name attribute first
    field_name = input_field.get('name')
    if field_name:
        name_cleaned = clean_text(field_name)
        if name_cleaned:
            return name_cleaned
    
    # 2. PRIORITY: Check placeholder second
    placeholder = input_field.get('placeholder')
    if placeholder:
        placeholder_text = clean_text(placeholder)
        if placeholder_text:
            return placeholder_text
    
    # 3. PRIORITY: Check label with 'for' attribute third
    field_id = input_field.get('id')
    if field_id:
        form = input_field.find_parent('form')
        if form:
            label = form.find('label', {'for': field_id})
            if label:
                label_text = clean_text(label.get_text())
                if label_text:
                    return label_text
    
    # 4. Check parent label
    parent_label = input_field.find_parent('label')
    if parent_label:
        label_text = clean_text(parent_label.get_text())
        if label_text:
            return label_text
    
    # 5. Check previous sibling text
    prev_sibling = input_field.find_previous_sibling()
    if prev_sibling and prev_sibling.get_text().strip():
        sibling_text = clean_text(prev_sibling.get_text())
        if sibling_text:
            return sibling_text
    
    # 6. Check parent text content (if not too long)
    parent = input_field.parent
    if parent:
        parent_text = parent.get_text().strip()
        if parent_text and len(parent_text) < 50:  # Not too long
            parent_cleaned = clean_text(parent_text)
            if parent_cleaned:
                return parent_cleaned
    
    # 7. Check table header (th) if in table
    table_row = input_field.find_parent('tr')
    if table_row:
        th = table_row.find('th')
        if th:
            th_text = clean_text(th.get_text())
            if th_text:
                return th_text
    
    # 8. Check fieldset legend
    fieldset = input_field.find_parent('fieldset')
    if fieldset:
        legend = fieldset.find('legend')
        if legend:
            legend_text = clean_text(legend.get_text())
            if legend_text:
                return legend_text
    
    return None


def clean_field_name(field_name):
    """Clean field name from nested structures."""
    if '[' in field_name and ']' in field_name:
        start_bracket = field_name.find('[')
        end_bracket = field_name.find(']')
        if start_bracket != -1 and end_bracket != -1 and end_bracket > start_bracket:
            return field_name[start_bracket + 1:end_bracket]
        else:
            return field_name.split('[')[0]
    else:
        return field_name.split('[')[0]


def extract_speaker_fields_from_form(form) -> list:
    """
    Extract and normalize speaker-relevant fields from a form.
    """
    field_names = []
    inputs = form.find_all(['input', 'textarea', 'select'])
    
    for input_field in inputs:
        field_name = input_field.get('name', '').lower()
        field_id = input_field.get('id', '').lower()
        field_type = input_field.get('type', '').lower()
        
        # Skip if no name/id or is hidden/button type
        if (not field_name and not field_id) or field_type in ['hidden', 'submit', 'button', 'reset']:
            continue
        
        # Use name first, then id as fallback
        primary_field_name = field_name if field_name else field_id
        
        # Check if it's a speaker-relevant field
        if is_speaker_relevant_field(primary_field_name, input_field):
            # Try to get meaningful name from label/placeholder/etc.
            meaningful_name = get_field_label(input_field)
            
            if meaningful_name:
                # Use meaningful name from label
                clean_name = meaningful_name
            else:
                # Fallback to cleaning the field name
                clean_name = clean_field_name(primary_field_name)
            
            # Only add if it's a meaningful field name and not already added
            if (clean_name and 
                clean_name not in field_names and 
                len(clean_name) > 1 and  # Avoid single character fields
                not clean_name.isdigit()):  # Avoid numeric-only fields
                field_names.append(clean_name)
    
    return field_names

