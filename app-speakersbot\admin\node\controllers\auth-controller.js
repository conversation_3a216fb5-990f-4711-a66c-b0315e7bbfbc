const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const ApiResponse = require("../helpers/api-response");
const { encodeToBase64, decodeFromBase64 } = require("../helpers/app-hepler");
const CONFIG = require("../config/config");
const emailService = require("../services/email-service");
const jwtHelper = require("../helpers/jwt-helper");

const authService = require("../services/auth-service");

/**
 * Authenticate user and generate JWT token
 */
exports.login = async (req, res, next) => {
    try {

        const result = await authService.login(req);

        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).send({ status: true, message: message, data: data });
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error during login:", error);
        next(error);
    }
};

/**
 * Send forgot password email
 */
exports.forgotPassword = async (req, res, next) => {
    try {
        const { email } = req.body
        if (!email) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email is required");
        }
        const { id, name, userType, roleId } = await authService.forgotPassword(email.trim());

        res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message: "Reset password email sent successfully" }));
        // Generate JWT reset token with 6 hours expiration
        const resetToken = jwtHelper.generateToken(
            {
                userId: id,
                roleId: roleId,
                role: userType,
                type: 'password_reset'
            },
            '6h' // 6 hours expiration
        );

        // Send password reset email
        const baseUrl = CONFIG.ADMIN_FRONTEND_BASE_URL;
        emailService.sendPasswordResetEmail(
            email,
            name,
            encodeToBase64(resetToken),
            baseUrl
        ).catch(error => {
            console.error('Failed to send password reset email:', error);
        });

    } catch (error) {
        console.error("Error in forgotPassword:", error);
        next(error);
    }
}

/**
 * Reset password using token
 */
exports.resetPassword = async (req, res, next) => {
    try {
        const { token, newPassword } = req.body;
        if (!token || !newPassword) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Token and new password are required");
        }
        const isReset = await authService.resetPassword(decodeFromBase64(token), newPassword);

        if (isReset) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message: "Password reset successfully" }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Failed to reset password");

    } catch (error) {
        console.error("Error in resetPassword:", error);
        next(error);
    }
}

/**
 * Change password for authenticated user
 */
exports.changePassword = async (req, res, next) => {
    try {
        const result = await authService.changePassword(req);
        const { status, message } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json({ status: true, message });
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Failed to change password");

    } catch (error) {
        console.error("Error in changePassword:", error);
        next(error);
    }
}