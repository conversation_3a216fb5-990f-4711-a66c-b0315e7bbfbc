import dayjs from "dayjs";

export type TrendRow = { date: string; status: "Accepted"|"Rejected"|"Interested"; value: number };

export function prepareOpportunityTrends(
  rawMatches: Array<{ date: string; status: "Accepted"|"Rejected"|"Interested" }>,
  startISO: string,
  endISO: string
): TrendRow[] {
  const start = dayjs(startISO).startOf("day");
  const end   = dayjs(endISO).startOf("day");
  const days: string[] = [];
  for (let d = start; d.isBefore(end) || d.isSame(end, "day"); d = d.add(1, "day")) {
    days.push(d.format("YYYY-MM-DD"));
  }

  // aggregate raw counts by day+status
  const map = new Map<string, number>();
  for (const m of rawMatches) {
    const key = `${dayjs(m.date).format("YYYY-MM-DD")}|${m.status}`;
    map.set(key, (map.get(key) ?? 0) + 1);
  }

  const statuses: TrendRow["status"][] = ["Accepted","Rejected","Interested"];
  const out: TrendRow[] = [];
  for (const d of days) {
    for (const s of statuses) {
      const key = `${d}|${s}`;
      out.push({ date: d, status: s, value: map.get(key) ?? 0 });
    }
  }
  return out;
}