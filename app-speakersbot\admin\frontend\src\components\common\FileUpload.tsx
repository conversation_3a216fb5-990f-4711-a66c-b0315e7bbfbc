// components/FileUpload.tsx
import { UploadOutlined } from "@ant-design/icons";
import React, { useRef, useState } from "react";

interface FileUploadProps {
  label?: string;
  accept?: string; // "image/*" | ".pdf" | etc.
  multiple?: boolean;
  onChange: (files: File[]) => void;
}

const FileUpload: React.FC<FileUploadProps> = ({
  label = "Upload File",
  accept = "image/*",
  multiple = false,
  onChange,
}) => {
  const inputRef = useRef<HTMLInputElement>(null);
  const [preview, setPreview] = useState<string[]>([]);

  const handleClick = () => {
    inputRef.current?.click();
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files) return;

    const files = Array.from(e.target.files);
    onChange(files);

    // Show preview for images
    const previews = files.map((file) =>
      file.type.startsWith("image") ? URL.createObjectURL(file) : ""
    );
    setPreview(previews);
  };

  return (
    <div
      className="flex flex-col items-center gap-3 border-2 border-dashed border-dashboard-light/40 rounded-lg p-4 cursor-pointer hover:bg-muted"
      onClick={handleClick}
    >
      <button
        type="button"
        className="px-4 py-2 text-muted-foreground rounded-lg cursor-pointer flex flex-col items-center gap-2 justify-center"
      >
        <UploadOutlined className="mr-2" />
        {label}
      </button>

      <input
        type="file"
        accept={accept}
        multiple={multiple}
        ref={inputRef}
        onChange={handleFileChange}
        className="hidden"
      />

      {/* Preview Images */}
      <div className="flex gap-2 flex-wrap">
        {preview.map(
          (src, i) =>
            src && (
              <img
                key={i}
                src={src}
                alt={`preview-${i}`}
                className="w-20 h-20 object-cover rounded-lg border"
              />
            )
        )}
      </div>
    </div>
  );
};

export default FileUpload;
