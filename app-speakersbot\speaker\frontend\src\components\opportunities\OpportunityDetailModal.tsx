import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { MapPin, Calendar, Clock, DollarSign, Users, ExternalLink, Heart, Share2, CheckCircle, XCircle } from 'lucide-react';
import { NotBookedModal } from '@/components/ui/not-booked-modal';

interface Opportunity {
  id: number;
  title: string;
  location: string;
  date: string;
  deadline: string;
  type: string;
  topics: string[];
  compensation: string;
  isNew: boolean;
  matchScore: number;
  description?: string;
  requirements?: string[];
  benefits?: string[];
  organizer?: string;
  expectedAttendees?: number;
  duration?: string;
  applicationUrl?: string;
}

interface OpportunityDetailModalProps {
  opportunity: Opportunity | null;
  isOpen: boolean;
  onClose: () => void;
  acceptedOpportunities: Set<number>;
  bookedOpportunities: Set<number>;
  notBookedOpportunities: Set<number>;
  interestedOpportunities: Set<number>;
  rejectedOpportunities: Set<number>;
  onAccept: (opportunityId: number) => void;
  onReject: (opportunityId: number) => void;
  onInterested: (opportunityId: number) => void;
  onBook: (opportunityId: number) => void;
  onNotBook: (opportunityId: number) => void;
}

export function OpportunityDetailModal({ 
  opportunity, 
  isOpen, 
  onClose,
  acceptedOpportunities,
  bookedOpportunities,
  notBookedOpportunities,
  interestedOpportunities,
  rejectedOpportunities,
  onAccept,
  onReject,
  onInterested,
  onBook,
  onNotBook
}: OpportunityDetailModalProps) {
  const [isNotBookedModalOpen, setIsNotBookedModalOpen] = useState(false);

  if (!opportunity) return null;

  const handleShare = () => {
    // Share opportunity
    console.log('Sharing opportunity:', opportunity.id);
  };

  const handleNotBookOpportunity = (oppId: number) => {
    setIsNotBookedModalOpen(true);
  };

  const handleNotBookedSubmit = (reason: string, note: string) => {
    if (opportunity) {
      onNotBook(opportunity.id);
      // Log the reason and note (in real app, send to backend)
      console.log(`Not booked reason for opportunity ${opportunity.id}:`, { reason, note });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto bg-surface border-border p-0">
        {/* Banner Section */}
        <div className="relative bg-gradient-to-r from-primary/20 via-primary/10 to-accent/20 p-8 border-b border-border">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-4">
                {/* Large Match Score */}
                <div className={`w-20 h-20 rounded-full border-4 flex items-center justify-center font-bold text-xl ${
                  opportunity.matchScore >= 90 ? 'border-success bg-success/10 text-success' :
                  opportunity.matchScore >= 80 ? 'border-primary bg-primary/10 text-primary' :
                  'border-warning bg-warning/10 text-warning'
                }`}>
                  {opportunity.matchScore}%
                </div>
                <div>
                  <DialogTitle className="text-2xl text-foreground mb-2">
                    {opportunity.title}
                  </DialogTitle>
                  <div className="flex items-center gap-4 text-foreground-muted">
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      {opportunity.location}
                    </div>
                    <Badge variant="secondary" className="text-sm">{opportunity.type}</Badge>
                    {opportunity.isNew && <Badge className="text-sm bg-primary">New Opportunity</Badge>}
                  </div>
                </div>
              </div>
              <div className="flex items-center gap-4">
                {/* Status Badges */}
                <div className="flex gap-2">
                  {acceptedOpportunities.has(opportunity.id) && (
                    <Badge variant="outline" className="border-green-600 text-green-600 bg-green-50">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Accepted
                    </Badge>
                  )}
                  {interestedOpportunities.has(opportunity.id) && (
                    <Badge variant="outline" className="border-yellow-600 text-yellow-600 bg-yellow-50">
                      <Heart className="h-3 w-3 mr-1" />
                      Interested
                    </Badge>
                  )}
                  {rejectedOpportunities.has(opportunity.id) && (
                    <Badge variant="outline" className="border-red-600 text-red-600 bg-red-50">
                      <XCircle className="h-3 w-3 mr-1" />
                      Rejected
                    </Badge>
                  )}
                  {bookedOpportunities.has(opportunity.id) && (
                    <Badge variant="outline" className="border-blue-600 text-blue-600 bg-blue-50">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Booked
                    </Badge>
                  )}
                  {notBookedOpportunities.has(opportunity.id) && (
                    <Badge variant="outline" className="border-gray-600 text-gray-600 bg-gray-50">
                      <XCircle className="h-3 w-3 mr-1" />
                      Not Booked
                    </Badge>
                  )}
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-success mb-1">{opportunity.compensation}</div>
                  <div className="text-sm text-foreground-muted">Compensation</div>
                </div>
              </div>
            </div>
          
          <DialogDescription className="text-foreground-muted text-base">
            {opportunity.description || 'Join this exciting speaking opportunity and share your expertise with a wider audience.'}
          </DialogDescription>
        </div>

        <div className="p-6">
          <div className="space-y-6">
          {/* Key Details */}
          <div className="grid grid-cols-2 gap-4">
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-primary" />
              <span className="text-foreground-muted">Event Date:</span>
              <span className="font-medium text-foreground">
                {new Date(opportunity.date).toLocaleDateString()}
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Clock className="h-4 w-4 text-warning" />
              <span className="text-foreground-muted">Deadline:</span>
              <span className="font-medium text-foreground">
                {new Date(opportunity.deadline).toLocaleDateString()}
              </span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <DollarSign className="h-4 w-4 text-success" />
              <span className="text-foreground-muted">Compensation:</span>
              <span className="font-medium text-success">{opportunity.compensation}</span>
            </div>
            <div className="flex items-center gap-2 text-sm">
              <Users className="h-4 w-4 text-primary" />
              <span className="text-foreground-muted">Expected Attendees:</span>
              <span className="font-medium text-foreground">
                {opportunity.expectedAttendees || '500+'}
              </span>
            </div>
          </div>

          <Separator />

          {/* Topics */}
          <div>
            <h4 className="font-medium text-foreground mb-3">Topics & Expertise Areas</h4>
            <div className="flex flex-wrap gap-2">
              {opportunity.topics.map((topic) => (
                <Badge key={topic} variant="secondary">
                  {topic}
                </Badge>
              ))}
            </div>
          </div>

          {/* Requirements */}
          <div>
            <h4 className="font-medium text-foreground mb-3">Requirements</h4>
            <ul className="space-y-2 text-sm text-foreground-muted">
              {(opportunity.requirements || [
                'Relevant industry experience (5+ years)',
                'Previous speaking experience preferred',
                'Ability to engage and educate audience',
                'Professional presentation materials'
              ]).map((req, index) => (
                <li key={index} className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-primary mt-2 flex-shrink-0" />
                  {req}
                </li>
              ))}
            </ul>
          </div>

          {/* Benefits */}
          <div>
            <h4 className="font-medium text-foreground mb-3">What You'll Get</h4>
            <ul className="space-y-2 text-sm text-foreground-muted">
              {(opportunity.benefits || [
                'Professional speaking fee',
                'Travel and accommodation covered',
                'Networking with industry leaders',
                'Conference recording and promotion',
                'Certificate of participation'
              ]).map((benefit, index) => (
                <li key={index} className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 rounded-full bg-success mt-2 flex-shrink-0" />
                  {benefit}
                </li>
              ))}
            </ul>
          </div>

          {/* Organizer Info */}
          <div className="bg-surface-elevated rounded-lg p-4">
            <h4 className="font-medium text-foreground mb-2">Event Organizer</h4>
            <p className="text-sm text-foreground-muted">
              <strong>{opportunity.organizer || 'TechEvents Global'}</strong> - 
              A leading technology conference organizer with 10+ years of experience 
              hosting world-class events for developers and tech leaders.
            </p>
          </div>

          <Separator />

          {/* Action Buttons */}
          <div className="flex items-center justify-between pt-2">
            <div className="flex items-center gap-3">
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleShare}
                className="flex items-center gap-2"
              >
                <Share2 className="h-4 w-4" />
                Share
              </Button>
              {opportunity.applicationUrl && (
                <Button 
                  variant="outline" 
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="h-4 w-4" />
                  Visit Website
                </Button>
              )}
            </div>
            
            <div className="flex items-center gap-3">
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
              
              {/* Show Accept, Interested, Reject buttons only when no action taken */}
              {!acceptedOpportunities.has(opportunity.id) && 
               !rejectedOpportunities.has(opportunity.id) && 
               !interestedOpportunities.has(opportunity.id) && 
               !bookedOpportunities.has(opportunity.id) && 
               !notBookedOpportunities.has(opportunity.id) && (
                <>
                  <Button
                    variant="outline"
                    className="border-green-600 text-green-600 hover:bg-green-50"
                    onClick={() => onAccept(opportunity.id)}
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Accept
                  </Button>
                  <Button
                    variant="outline"
                    className="border-yellow-600 text-yellow-600 hover:bg-yellow-50"
                    onClick={() => onInterested(opportunity.id)}
                  >
                    <Heart className="h-4 w-4 mr-1" />
                    Interested
                  </Button>
                  <Button
                    variant="outline"
                    className="border-red-600 text-red-600 hover:bg-red-50"
                    onClick={() => onReject(opportunity.id)}
                  >
                    <XCircle className="h-4 w-4 mr-1" />
                    Reject
                  </Button>
                </>
              )}
              
              {/* Show Book/Not Booked buttons only after accepting */}
              {acceptedOpportunities.has(opportunity.id) && 
               !bookedOpportunities.has(opportunity.id) && 
               !notBookedOpportunities.has(opportunity.id) && (
                <>
                  <Button
                    variant="outline"
                    className="border-blue-600 text-blue-600 hover:bg-blue-50"
                    onClick={() => onBook(opportunity.id)}
                  >
                    <CheckCircle className="h-4 w-4 mr-1" />
                    Book
                  </Button>
                  <Button
                    variant="outline"
                    className="border-gray-600 text-gray-600 hover:bg-gray-50"
                    onClick={() => handleNotBookOpportunity(opportunity.id)}
                  >
                    <XCircle className="h-4 w-4 mr-1" />
                    Not Booked
                  </Button>
                </>
              )}
            </div>
          </div>
          </div>
        </div>
      </DialogContent>
      
      {/* Not Booked Modal */}
      <NotBookedModal
        isOpen={isNotBookedModalOpen}
        onClose={() => setIsNotBookedModalOpen(false)}
        onSubmit={handleNotBookedSubmit}
        title="Why wasn't this opportunity booked?"
      />
    </Dialog>
  );
}