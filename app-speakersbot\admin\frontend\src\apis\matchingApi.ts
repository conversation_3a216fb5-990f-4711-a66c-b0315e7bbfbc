import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import type { RootState } from "../store";

// Define the base URL for your API
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:3000/api";

export const matchingApi = createApi({
  reducerPath: "matchingApi",
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Get the token from the state
      const token = (getState() as RootState).auth?.token;

      // If we have a token, set the authorization header
      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }

      // Set content type
      headers.set("content-type", "application/json");

      return headers;
    },
  }),
  tagTypes: ["Matching"],
  keepUnusedDataFor: 0,
  endpoints: (builder) => ({
    getMatches: builder.query({
      query: (params = {}) => ({
        url: "/matching-queue",
        params,
      }),
      providesTags: ["Matching"],
    }),

    getMatchById: builder.query({
      query: () => "/matching-queue/:id",
      providesTags: ["Matching"],
    }),

    exportMatchingQueue: builder.query<Blob, Record<string, any>>({
      query: (params = {}) => ({
        url: "/matching-queue/export",
        params,
        // Ensure we get a Blob back (CSV)
        responseHandler: async (response) => await response.blob(),
        headers: { Accept: "text/csv" },
      }),
    }),
  }),
});

export const { useGetMatchesQuery, useGetMatchByIdQuery, useExportMatchingQueueQuery, useLazyExportMatchingQueueQuery } = matchingApi;
