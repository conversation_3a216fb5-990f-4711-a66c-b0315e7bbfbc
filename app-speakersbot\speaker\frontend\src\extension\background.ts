// Chrome Extension Background Script (Service Worker)
// Handles extension lifecycle events, notifications, and communication

console.log('SpeakerBot Extension Background Script Loaded');

// Extension installation handler
chrome.runtime.onInstalled.addListener((details) => {
  console.log('Extension installed:', details.reason);
  
  // Set default settings on install
  if (details.reason === 'install') {
    chrome.storage.local.set({
      theme: 'dark',
      notifications: true,
      autoFill: true,
      lastSync: Date.now()
    });
  }
});

// Handle extension startup
chrome.runtime.onStartup.addListener(() => {
  console.log('Extension started');
});

// Message handler for communication between popup, content scripts, and background
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Background received message:', message);
  
  switch (message.type) {
    case 'GET_STORAGE':
      chrome.storage.local.get([message.key], (result) => {
        sendResponse({ data: result[message.key] });
      });
      return true; // Keep message channel open for async response
      
    case 'SET_STORAGE':
      chrome.storage.local.set({ [message.key]: message.value }, () => {
        sendResponse({ success: true });
      });
      return true;
      
    case 'SYNC_DATA':
      // Handle data synchronization with backend
      handleDataSync()
        .then(() => sendResponse({ success: true }))
        .catch((error) => sendResponse({ success: false, error: error.message }));
      return true;
      
    case 'SHOW_NOTIFICATION':
      // Show browser notification
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'favicon.ico',
        title: message.title || 'SpeakerBot',
        message: message.message
      });
      sendResponse({ success: true });
      break;
      
    default:
      console.log('Unknown message type:', message.type);
  }
});

// Handle notification clicks
chrome.notifications.onClicked.addListener((notificationId) => {
  console.log('Notification clicked:', notificationId);
  // Open extension popup or navigate to specific tab
});

// Periodic data sync (every 30 minutes)
chrome.alarms.create('dataSync', { periodInMinutes: 30 });
chrome.alarms.onAlarm.addListener((alarm) => {
  if (alarm.name === 'dataSync') {
    handleDataSync();
  }
});

// Data synchronization function
async function handleDataSync() {
  try {
    console.log('Starting data sync...');
    
    // Get stored data
    const result = await chrome.storage.local.get(['lastSync', 'userId']);
    
    // TODO: Implement actual API sync when backend is ready
    // For now, just update last sync time
    await chrome.storage.local.set({ lastSync: Date.now() });
    
    console.log('Data sync completed');
    
    // Send notification to popup if open
    chrome.runtime.sendMessage({
      type: 'SYNC_COMPLETE',
      timestamp: Date.now()
    }).catch(() => {
      // Popup might not be open, ignore error
    });
    
  } catch (error) {
    console.error('Data sync failed:', error);
  }
}

// Handle tab updates to detect opportunity pages
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    // Check if this is a potential opportunity or application page
    if (isOpportunityPage(tab.url)) {
      // Inject content script or send notification
      chrome.tabs.sendMessage(tabId, {
        type: 'OPPORTUNITY_PAGE_DETECTED',
        url: tab.url
      }).catch(() => {
        // Content script might not be ready, ignore error
      });
    }
  }
});

// Detect if URL is an opportunity or application page
function isOpportunityPage(url: string): boolean {
  const opportunityPatterns = [
    /application/i,
    /speaker/i,
    /call.*for.*papers/i,
    /cfp/i,
    /submit/i,
    /proposal/i
  ];
  
  return opportunityPatterns.some(pattern => pattern.test(url));
}

// Export for use in other parts of the extension
export { handleDataSync };