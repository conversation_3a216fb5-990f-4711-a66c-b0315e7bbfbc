"""
Query Builder Module for Speaker Opportunity Scraper

This module provides intelligent query construction for various search engines
and APIs. It builds optimized search queries with industry-specific terms,
time-based filtering, and appropriate exclusions to find relevant speaking
opportunities.

Key Features:
- Industry-specific keyword mapping
- Time-based query optimization
- Multiple query variations
- Domain filtering and exclusions
- Query length optimization

Author: Speaker Bot Team
Version: 2.0 (Optimized)
Last Updated: 2024
"""

from datetime import datetime
from typing import List, Tuple

from .constants import CALL_PHRASES, EVENT_WORDS, PAID_PHRASES, NOT_JOBS

# =============================================================================
# QUERY CONSTRUCTION
# =============================================================================

def build_queries(topic: str) -> List[Tuple[str, str]]:
    """
    Build comprehensive search queries for a given topic with multiple variations.
    
    This function creates multiple optimized search queries for different
    search engines, including industry-specific terms, time-based filtering,
    and appropriate exclusions to maximize relevant results.
    
    Args:
        topic (str): Base topic for query construction
        
    Returns:
        List[Tuple[str, str]]: List of (query_type, query_string) tuples
        
    Example:
        >>> queries = build_queries("AI Conference")
        >>> print(f"Generated {len(queries)} query variations")
        >>> for q_type, q_string in queries:
        ...     print(f"{q_type}: {q_string[:50]}...")
    """
    current_year = datetime.now().year
    current_month = datetime.now().month
    next_year = current_year + 1
    
    # Enhanced industry-specific terms for better targeting
    industry_terms = {
        "healthcare": "healthcare medical health pharmaceutical biotech clinical research",
        "technology": "tech technology software AI artificial intelligence machine learning data science",
        "finance": "finance financial banking fintech investment cryptocurrency blockchain",
        "education": "education academic learning training university college school",
        "manufacturing": "manufacturing industrial production automation robotics supply chain",
        "government": "government public sector policy regulatory compliance public administration",
        "data": "data science analytics big data business intelligence data engineering",
        "music": "music entertainment audio sound recording production music industry",
        "marketing": "marketing advertising digital marketing social media content marketing",
        "sales": "sales business development customer success revenue growth",
        "hr": "human resources talent management recruitment employee development",
        "legal": "legal law compliance regulatory legal technology lawtech",
        "retail": "retail ecommerce consumer goods supply chain logistics",
        "energy": "energy renewable sustainability clean energy oil gas utilities",
        "transportation": "transportation logistics mobility automotive aviation shipping",
        "real estate": "real estate property development construction architecture",
        "consulting": "consulting advisory management consulting business consulting",
        "nonprofit": "nonprofit charity social impact community development philanthropy"
    }
    
    # Get industry-specific terms for the topic
    industry_keywords = ""
    topic_lower = topic.lower()
    for industry, keywords in industry_terms.items():
        if industry in topic_lower:
            industry_keywords = keywords
            break
    
    # If no specific industry match, use the original topic
    if not industry_keywords:
        industry_keywords = topic
    
    # Build dynamic year filters based on current date
    # Include current year with future months + next year
    if current_month >= 6:  # If we're in second half of year, focus on next year
        year_filter = f'"{next_year}"'
    else:  # If we're in first half, include current year future months + next year
        year_filter = f'"{current_year}" OR "{next_year}"'
    
    # Simplified domain filtering
    domain_filter = "site:.org OR site:.edu OR site:.com"
    
    # Query 1: Comprehensive Event Discovery - Simplified and focused
    q_comprehensive = f'{topic} {industry_keywords} {domain_filter} {NOT_JOBS} AND ({EVENT_WORDS} OR {CALL_PHRASES}) AND {year_filter}'
    
    # Query 2: Premium Opportunity Targeting - More specific
    q_premium_opportunities = f'{topic} {industry_keywords} {domain_filter} {NOT_JOBS} AND ({CALL_PHRASES} OR "keynote speakers" OR "speaking opportunities") AND {year_filter}'
    
    return [
        ("comprehensive_event_discovery", q_comprehensive),
        ("premium_speaking_opportunities", q_premium_opportunities),
    ]
