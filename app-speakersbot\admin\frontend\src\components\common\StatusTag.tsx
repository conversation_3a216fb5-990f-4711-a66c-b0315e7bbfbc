import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { getStatusBadgeClass } from '@/utils/status-colors';
import React from 'react';

interface StatusTagProps {
  status: string;
  label?: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
}

const StatusTag: React.FC<StatusTagProps> = ({ status, label, className, style }) => {
  const display = label ?? (typeof status === 'string' ? status : String(status));
  const statusClasses = getStatusBadgeClass(String(status));
  return (
    <Badge className={cn(statusClasses, className)} style={style}>
      {display}
    </Badge>
  );
};

export default StatusTag;
