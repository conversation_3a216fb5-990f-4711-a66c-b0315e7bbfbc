import React, { useMemo, useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious
} from '@/components/ui/pagination';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Edit,
  Award,
  Star,
  Plus,
  Trash2,
  Check,
  X,
  Ban,
  Info,
  Eye,
  MapPin,
  Calendar,
  Phone,
  Mail,
  Globe,
  Building,
  User,
  Trophy,
  Target,
  Clock
} from 'lucide-react';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useAppState } from '../../state/AppStateProvider';
import { getPermissions } from '../../utils/permissions';
import { useGetSpeakerByIdQuery, useGetSpeakerOpportunitiesQuery, useGetSpeakerHistoryQuery, useUpdateSpeakerMutation } from '../../apis/speakersApi';
import dayjs from 'dayjs';
import type { Speaker, UserRole, Match, Opportunity } from '../../types';
import StatusTag from '@/components/common/StatusTag';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import LoadingSkeleton from '@/components/common/LoadingSkeleton';


const SpeakerDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();

  const navigate = useNavigate();
  const { speakers, matches, opportunities, dispatch, ui } = useAppState();

  // Fetch speaker data from API
  const { data: apiData, isLoading: isApiLoading, error: apiError } = useGetSpeakerByIdQuery(id!);

  // Mock user for permissions
  const user = { role: 'admin' as UserRole, id: 'admin-1' };
  const permissions = getPermissions(user?.role);

  // State management
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const [isRejectModalVisible, setIsRejectModalVisible] = useState(false);
  const [isNoteModalVisible, setIsNoteModalVisible] = useState(false);
  const [isCreditHistoryModalVisible, setIsCreditHistoryModalVisible] = useState(false);
  const [historyPage, setHistoryPage] = useState<number>(1);
  const [historyPageSize, setHistoryPageSize] = useState<number>(10);
  const [editingNote, setEditingNote] = useState<any | null>(null);
  const [rejectingMatch, setRejectingMatch] = useState<any | null>(null);
  const [activeTab, setActiveTab] = useState<string>('opportunities');
  // Form states for modals
  const [editFormData, setEditFormData] = useState({
    name: '',
    email: '',
    subscriptionPlan: '',
    activityStatus: ''
  });
  const [noteFormData, setNoteFormData] = useState({
    content: '',
    category: ''
  });
  const [rejectFormData, setRejectFormData] = useState({
    rejectionReason: '',
    notes: ''
  });

  // Update speaker mutation
  const [updateSpeaker, { isLoading: isUpdating }] = useUpdateSpeakerMutation();
  const creditData = apiData?.data?.creditHistory


  // Map API speaker data to Speaker type
  const mapApiSpeaker = (s: any): Speaker => {
    const preferredGeography: string[] = [];
    if (s.city) preferredGeography.push(s.city);
    if (s.state) preferredGeography.push(s.state);

    // Parse other expertise from field_13
    const otherExpertise: string[] = [];
    if (s.field_13) {
      otherExpertise.push(s.field_13);
    }

    const planNormalized = s.planType === 'not_activated'
      ? 'not subscribed'
      : (s.planType || 'basic');

    return {
      id: String(s.id),
      name: s.name || '',
      email: s.email || '',
      createdAt: s.created_at || new Date().toISOString(),
      updatedAt: s.updated_at || new Date().toISOString(),
      activityStatus: s.status === 'active' ? 'active' : (s.status === 'pending' ? 'pending' : 'inactive'),
      subscriptionPlan: planNormalized,
      gamificationPoints: 0, // Default value, can be updated from API if available
      intakeData: {
        preferredGeography,
        primaryExpertise: s.field_11 || '',
        otherExpertise,
        yearsOfExperience: (s.yearsExperienceSpeaker && !isNaN(Number(s.yearsExperienceSpeaker)))
          ? Number(s.yearsExperienceSpeaker)
          : (s.field_21 ? Number(s.field_21) : undefined),
        willingToTravel: typeof s.willingToTravel === 'string'
          ? (s.willingToTravel.trim().toLowerCase().startsWith('yes'))
          : (s.field_64 ? String(s.field_64).toLowerCase() === 'yes' : undefined),
        preferredTalkFormats: s.field_55 ? String(s.field_55).split(',').map((x) => x.trim()) : [],
        speakerWebsite: s.field_68 || undefined,
        company: s.company || undefined,
        title: s.title || undefined,
        phoneNumber: s.phone_number || undefined,
        bio: s.bio || undefined,
        linkedin: s.linkedin || undefined,
        city: s.city || undefined,
        state: s.state || undefined,
      },
      // Store raw API data for additional fields
      rawData: s,
    } as Speaker & { rawData: any };
  };

  // Use API data if available, otherwise fallback to local state
  const speaker = useMemo(() => {
    if (apiData?.data) {
      return mapApiSpeaker(apiData.data);
    }
    return speakers.speakers.find(s => s.id === id);
  }, [apiData, speakers.speakers, id]);

  // Fetch opportunities for this speaker from API
  const { data: oppData, isLoading: isOppLoading, isFetching: isOppFetching } = useGetSpeakerOpportunitiesQuery(
    {
      id: id!,
      page,
      limit: pageSize,
      filter: statusFilter && statusFilter !== 'all' ? { status: statusFilter } : undefined,
    } as any,
    { refetchOnMountOrArgChange: true }
  );

  const apiMatches = useMemo(() => {
    const list = oppData?.data || [];
    return list.map((item: any) => ({
      id: String(item.id),
      status: item.status || 'pending',
      matchingScore: `${Math.round((Number(item.overall_score ?? 0)))}`,
      updatedAt: item.updated_at || item.created_at,
      opportunity: {
        id: String(item.opportunity?.id || item.opportunity_id),
        title: item.opportunity?.title,
        organization: item.opportunity?.organization,
        category: item.opportunity?.event_type || item.opportunity?.industry,
        eventDate: item.opportunity?.start_date,
      }
    }));
  }, [oppData]);

  const paginationTotal = oppData?.pagination?.total || apiMatches.length;
  const totalPages = Math.ceil((paginationTotal || 0) / (pageSize || 1));

  // Matches list comes from API; already filtered by status on server
  const filteredMatches = apiMatches;

  // Fetch credit history when modal is open
  const { data: historyData, isLoading: isHistoryLoading, isFetching: isHistoryFetching } = useGetSpeakerHistoryQuery(
    { id: id as any, page: historyPage, limit: historyPageSize } as any,
    { skip: !isCreditHistoryModalVisible, refetchOnMountOrArgChange: true }
  );
  const creditHistory = useMemo(() => {
    const list = historyData?.data || [];
    return list.map((h: any) => ({
      id: String(h.id),
      date: h.created_at,
      change: Number(h.points) * (h.type === 'credit' ? 1 : -1),
      source: h.source || 'Activity',
      balance: h.balance || 0,
      type: h.type
    }));
  }, [historyData]);
  const historyTotal = historyData?.pagination?.total || creditHistory.length;

  // Handle match status changes with full system updates
  const handleMatchStatusChange = async (matchId: string, newStatus: string, reason?: string) => {
    if (!permissions.canEdit) {
      console.error('You do not have permission to change match status');
      return;
    }
    const match = (filteredMatches || []).find((m: any) => m.id === matchId);
    if (!match || !speaker) return;

    // Calculate points change
    let pointsChange = 0;
    switch (newStatus) {
      case 'accepted': pointsChange = 100; break;
      case 'rejected': pointsChange = -25; break;
      case 'interested': pointsChange = 25; break;
    }

    try {
      // Update speaker points
      const updatedSpeaker = {
        ...speaker,
        gamificationPoints: Math.max(0, speaker.gamificationPoints + pointsChange),
        updatedAt: dayjs().toISOString()
      };
      dispatch({ type: 'UPDATE_SPEAKER', payload: updatedSpeaker });

    } catch (error) {
      console.error('Failed to update match status');
    }
  };

  // Quick actions for header
  const handleQuickAction = (action: 'accept' | 'reject' | 'inactivate') => {
    if (!speaker || !permissions.canEdit) {
      console.error('You do not have permission to perform this action');
      return;
    }
    // Implementation for quick actions
  };

  const handleAddNote = () => {
    setEditingNote(null);
    setNoteFormData({ content: '', category: '' });
    setIsNoteModalVisible(true);
  };

  // Prefill edit form when opening modal
  useEffect(() => {
    if (isEditModalVisible && speaker) {
      setEditFormData({
        name: speaker.name || '',
        email: speaker.email || '',
        subscriptionPlan: speaker.subscriptionPlan || '',
        activityStatus: speaker.activityStatus || '',
      });
    }
  }, [isEditModalVisible, speaker]);

  const handleEditNote = (note: any) => {
    setEditingNote(note);
    setNoteFormData({ content: note.content, category: note.category || '' });
    setIsNoteModalVisible(true);
  };

  const handleSaveNote = async () => {
    try {
      setIsNoteModalVisible(false);
      setNoteFormData({ content: '', category: '' });
    } catch (error) {
      console.error('Failed to save note');
    }
  };

  const handleDeleteNote = (noteId: string) => {
  };


  // Loading state
  if (isApiLoading) {
    return (
      <div className="space-y-6 p-6">
        <Button variant="ghost" onClick={() => navigate('/admin/speakers')} className="px-0 h-auto">
          <ArrowLeftOutlined className="mr-2" />
          Back to Speakers
        </Button>

        {/* Header Skeleton */}
        <Card className="mb-6 bg-tertiary">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Skeleton className="h-16 w-16 rounded-full" />
                <div className="space-y-2">
                  <Skeleton className="h-6 w-40" />
                  <Skeleton className="h-4 w-52" />
                  <div className="mt-2 flex flex-wrap items-center gap-2">
                    <Skeleton className="h-5 w-16 rounded-md" />
                    <Skeleton className="h-5 w-14 rounded-md" />
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-3 w-28" />
                  </div>
                </div>
              </div>
              <Skeleton className="h-9 w-24" />
            </div>
          </CardContent>
        </Card>

        {/* Tabs Skeleton */}
        <div className="mb-6">
          <div className="bg-card rounded-t-xl border border-b-0 shadow-sm flex">
            <div className="px-7 py-4 flex-1 text-center">
              <Skeleton className="h-4 w-48 mx-auto" />
            </div>
            <div className="px-7 py-4 flex-1 text-center">
              <Skeleton className="h-4 w-32 mx-auto" />
            </div>
          </div>
          <div className="rounded-b-xl border border-t-0 shadow-sm p-8 bg-tertiary">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              {/* Left Panel Skeleton */}
              <div className="lg:col-span-1 space-y-6">
                <Card>
                  <CardHeader className='border-b'>
                    <Skeleton className="h-6 w-40" />
                  </CardHeader>
                  <CardContent className="space-y-4 p-3">
                    <div>
                      <Skeleton className="h-4 w-12 mb-2" />
                      <Skeleton className="h-9 w-full rounded-md" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between p-4 border-b">
                    <Skeleton className="h-4 w-28" />
                    <Skeleton className="h-4 w-12" />
                  </CardHeader>
                  <CardContent className='p-0'>
                    <div className="space-y-2 border m-4 rounded-md">
                      <div className="grid grid-cols-2 border-b">
                        <div className='p-2 px-4'>
                          <Skeleton className="h-4 w-8" />
                        </div>
                        <div className='p-2 text-left'>
                          <Skeleton className="h-4 w-12" />
                        </div>
                      </div>
                      {[...Array(3)].map((_, index) => (
                        <div key={index} className="grid grid-cols-2 text-xs p-2 px-4 pb-4 border-b last:border-b-0">
                          <div className="flex items-center">
                            <Skeleton className="h-3 w-16" />
                          </div>
                          <div className="flex items-center">
                            <Skeleton className="h-3 w-8" />
                          </div>
                        </div>
                      ))}
                      <div className="mt-3 pt-3 border-t p-4">
                        <Skeleton className="h-4 w-32" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Right Panel Skeleton */}
              <div className="lg:col-span-3">
                <Card>
                  <CardHeader className='border-b'>
                    <Skeleton className="h-6 w-48" />
                  </CardHeader>
                  <CardContent className='p-0'>
                    <div className="space-y-4 border m-4 rounded-md">
                      <div className="overflow-auto max-h-[450px]">
                        <Table>
                          <TableHeader>
                            <TableRow>
                              <TableHead><Skeleton className="h-4 w-20" /></TableHead>
                              <TableHead><Skeleton className="h-4 w-16" /></TableHead>
                              <TableHead><Skeleton className="h-4 w-12" /></TableHead>
                              <TableHead><Skeleton className="h-4 w-24" /></TableHead>
                              <TableHead><Skeleton className="h-4 w-16" /></TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {[...Array(5)].map((_, i) => (
                              <TableRow key={i}>
                                <TableCell>
                                  <div>
                                    <Skeleton className="h-4 w-32 mb-1" />
                                    <Skeleton className="h-3 w-24" />
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Skeleton className="h-6 w-16 rounded-full" />
                                </TableCell>
                                <TableCell>
                                  <Skeleton className="h-4 w-20" />
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    <Skeleton className="h-2 w-16 rounded-full" />
                                    <Skeleton className="h-4 w-8" />
                                  </div>
                                </TableCell>
                                <TableCell>
                                  <Skeleton className="h-6 w-16 rounded-full" />
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>

                      <div className="flex items-center justify-between p-3">
                        <Skeleton className="h-4 w-48" />
                        <div className="flex gap-2">
                          <Skeleton className="h-8 w-8 rounded-md" />
                          <Skeleton className="h-8 w-8 rounded-md" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (apiError) {
    return (
      <div className="space-y-6 p-6">
        <Button variant="ghost" onClick={() => navigate('/admin/speakers')} className="px-2 h-auto">
          <ArrowLeftOutlined className="mr-2" />
          Back to Speakers
        </Button>
        <div className="text-center py-12">
          <div className="text-lg text-muted-foreground">Failed to load speaker details</div>
        </div>
      </div>
    );
  }

  // Speaker not found
  if (!speaker) {
    return (
      <div className="space-y-6 p-6">
        <Button variant="ghost" onClick={() => navigate('/admin/speakers')} className="px-2 h-auto">
          <ArrowLeftOutlined className="mr-2" />
          Back to Speakers
        </Button>
        <div className="text-center py-12">
          <div className="text-lg text-muted-foreground">Speaker not found</div>
        </div>
      </div>
    );
  }

  // Counts are handled by server; we do not compute client-side for now
  return (
    <div className="space-y-6">
      {/* Header / Breadcrumb */}
      <div className="mb-4 flex items-center gap-3">
        <Button variant="ghost" onClick={() => navigate('/admin/speakers')} className="px-3 h-auto">
          <ArrowLeftOutlined className="mr-2" />
          Back to Speakers
        </Button>
        {/* <span className="text-muted-foreground">/</span>
        <span className="text-sm text-foreground">{speaker.name}</span> */}
      </div>

      {/* Header with Speaker Info */}
      <Card className="mb-6 bg-tertiary">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            {/* Left section */}
            <div className="flex items-center gap-4">
              {/* Avatar */}
              {isApiLoading ? (
                <Skeleton className="h-16 w-16 rounded-full" />
              ) : (
                <div className="h-16 w-16 rounded-full bg-blue-600 text-white flex items-center justify-center text-xl font-semibold">
                  {speaker.name.charAt(0)?.toUpperCase()}
                </div>
              )}

              {/* Text details */}
              <div>
                {isApiLoading ? (
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-40" />
                    <Skeleton className="h-4 w-52" />
                    <div className="mt-2 flex flex-wrap items-center gap-2">
                      <Skeleton className="h-5 w-16 rounded-md" />
                      <Skeleton className="h-5 w-14 rounded-md" />
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-3 w-28" />
                    </div>
                  </div>
                ) : (
                  <>
                    <h2 className="text-2xl font-semibold text-foreground m-0 capitalize">
                      {speaker?.name}
                    </h2>
                    <p className="text-sm text-muted-foreground">{speaker.email}</p>
                    <div className="mt-2 flex flex-wrap items-center gap-2">
                      <StatusTag label={speaker.subscriptionPlan} status="active" className="capitalize"/>
                      <Badge
                        variant={
                          speaker.activityStatus === "active"
                            ? "outline"
                            : "secondary"
                        }
                        className="capitalize border-0"
                      >
                        <span
                          className={`h-2 w-2 rounded-full mr-2 capitalize ${speaker.activityStatus === "active"
                            ? "bg-green-500"
                            : "bg-gray-400"
                            }`}
                        />
                        {speaker.activityStatus}
                      </Badge>

                      <span className="text-sm text-muted-foreground flex items-center gap-1">
                        <Trophy className="h-3 w-3 text-[#FAAD14]" />
                        {speaker.gamificationPoints} pts
                      </span>
                      <span className="text-xs text-muted-foreground">
                        Updated {dayjs(speaker.updatedAt).fromNow()}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>

            {/* Right section */}
            <div className="flex items-center gap-1">
              {isApiLoading ? (
                <Button variant="outline" disabled>
                  <Skeleton className="h-4 w-20" />
                </Button>
              ) : (
                permissions.canEdit && (
                  <Button
                    variant="outline"
                    disabled
                    onClick={() => handleQuickAction("inactivate")}
                  >
                    <Ban className="mr-2 h-4 w-4" />
                    Inactivate
                  </Button>
                )
              )}
            </div>
          </div>
        </CardContent>
      </Card>


      {/* Custom Styled Tabs */}
      <div className="mb-6">
        {/* Custom Tab Header */}
        <div className="bg-card rounded-t-xl border border-b-0 shadow-sm flex">
          <div
            onClick={() => setActiveTab('opportunities')}
            className={`px-7 py-4 cursor-pointer flex-1 text-center transition-all duration-300 rounded-tl-xl ${activeTab === 'opportunities'
              ? 'text-primary bg-tertiary border-b-[1px] border-b-[#427AD0] font-semibold'
              : 'text-muted-foreground bg-muted border-b-3 border-b-transparent font-normal'
              }`}
          >
            Opportunity & Performance
          </div>
          <div
            onClick={() => setActiveTab('details')}
            className={`px-7 py-4 cursor-pointer flex-1 text-center transition-all duration-300 rounded-tr-xl ${activeTab === 'details'
              ? 'text-primary bg-tertiary border-b-[1px] border-b-[#427AD0] font-semibold'
              : 'text-muted-foreground bg-muted border-b-3 border-b-transparent font-normal'
              }`}
          >
            Speaker Details
          </div>
        </div>

        {/* Tab Content */}
        <div className="rounded-b-xl border border-t-0 shadow-sm">
          {activeTab === 'opportunities' && (
            isApiLoading ? (
              <div className="p-4 py-6 bg-tertiary">
                {/* Main Content with Side Filter Skeleton */}
                <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
                  {/* Left Panel - Filter Skeleton */}
                  <div className="lg:col-span-1 space-y-6">
                    <Card>
                      <CardHeader className='border-b'>
                        <Skeleton className="h-6 w-20" />
                      </CardHeader>
                      <CardContent className="space-y-4 p-3">
                        <div>
                          <Skeleton className="h-4 w-12 mb-2" />
                          <Skeleton className="h-9 w-full rounded-md" />
                        </div>
                      </CardContent>
                    </Card>

                    {/* Credit History Card Skeleton */}
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between p-4 border-b">
                        <Skeleton className="h-4 w-28" />
                        <Skeleton className="h-4 w-12" />
                      </CardHeader>
                      <CardContent className='p-0'>
                        <div className="space-y-2 border m-4 rounded-md">
                          <div className="grid grid-cols-2 border-b">
                            <div className='p-2 px-4'>
                              <Skeleton className="h-4 w-8" />
                            </div>
                            <div className='p-2 text-left'>
                              <Skeleton className="h-4 w-12" />
                            </div>
                          </div>
                          {[...Array(3)].map((_, index) => (
                            <div key={index} className="grid grid-cols-2 text-xs p-2 px-4 pb-4 border-b last:border-b-0">
                              <div className="flex items-center">
                                <Skeleton className="h-3 w-16" />
                              </div>
                              <div className="flex items-center">
                                <Skeleton className="h-3 w-8" />
                              </div>
                            </div>
                          ))}
                          <div className="mt-3 pt-3 border-t p-4">
                            <Skeleton className="h-4 w-32" />
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Right Panel - Opportunities List Skeleton */}
                  <div className="lg:col-span-3">
                    <Card>
                      <CardHeader className='border-b'>
                        <Skeleton className="h-6 w-48" />
                      </CardHeader>
                      <CardContent className='p-0'>
                        <div className="space-y-4 border m-4 rounded-md">
                          {/* Table Skeleton */}
                          <div className="overflow-auto max-h-[450px]">
                            <Table>
                              <TableHeader>
                                <TableRow>
                                  <TableHead><Skeleton className="h-4 w-20" /></TableHead>
                                  <TableHead><Skeleton className="h-4 w-16" /></TableHead>
                                  <TableHead><Skeleton className="h-4 w-12" /></TableHead>
                                  <TableHead><Skeleton className="h-4 w-24" /></TableHead>
                                  <TableHead><Skeleton className="h-4 w-16" /></TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {[...Array(5)].map((_, i) => (
                                  <TableRow key={i}>
                                    <TableCell>
                                      <div>
                                        <Skeleton className="h-4 w-32 mb-1" />
                                        <Skeleton className="h-3 w-24" />
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                      <Skeleton className="h-6 w-16 rounded-full" />
                                    </TableCell>
                                    <TableCell>
                                      <Skeleton className="h-4 w-20" />
                                    </TableCell>
                                    <TableCell>
                                      <div className="flex items-center gap-2">
                                        <Skeleton className="h-2 w-16 rounded-full" />
                                        <Skeleton className="h-4 w-8" />
                                      </div>
                                    </TableCell>
                                    <TableCell>
                                      <Skeleton className="h-6 w-16 rounded-full" />
                                    </TableCell>
                                  </TableRow>
                                ))}
                              </TableBody>
                            </Table>
                          </div>

                          {/* Pagination Skeleton */}
                          <div className="flex items-center justify-between p-3">
                            <Skeleton className="h-4 w-48" />
                            <div className="flex gap-2">
                              <Skeleton className="h-8 w-8 rounded-md" />
                              <Skeleton className="h-8 w-8 rounded-md" />
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </div>
            ) : (
              <div className="p-4 bg-tertiary">
                {/* Main Content with Side Filter */}
                <div className="grid grid-cols-4 lg:grid-cols-4 gap-6">
                  {/* Left Panel - Filter */}
                  <div className="col-span-4 xl:col-span-1 space-y-6">
                    <Card className="">
                      <CardHeader className='border-b px-4 py-4'>
                        <CardTitle className="text-lg text-md">Filter Opportunities</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4 p-0 p-3 ">
                        <div >
                          <Label className="text-sm font-medium">Status</Label>
                          <Select

                            value={statusFilter}
                            onValueChange={(v) => { setStatusFilter(v); setPage(1); }}
                          >
                            <SelectTrigger className="mt-2 bg-card border border-[#9CD2E766]-300">
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                            <SelectContent className='border'>
                              <SelectItem value="all">All</SelectItem>
                              <SelectItem value="pending">Pending</SelectItem>
                              <SelectItem value="interested">Interested</SelectItem>
                              <SelectItem value="accepted">Accepted</SelectItem>
                              <SelectItem value="rejected">Rejected</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Credit History Card */}
                    <Card>
                      <CardHeader className="flex flex-row items-center justify-between p-4 py- border-b">
                        <CardTitle className="text-sm font-medium text-sm">Credit History</CardTitle>
                        {!isApiLoading && (
                          <Button
                            variant="link"
                            size="sm"
                            onClick={() => setIsCreditHistoryModalVisible(true)}
                            className="h-auto p-0 text-primary text-sm"
                          >
                            View All
                          </Button>
                        )}
                      </CardHeader>
                      <CardContent className='p-0'>
                        {isApiLoading || !creditData ? (
                          // Skeleton loading for credit history
                          <div className="space-y-2 border m-4 rounded-md">
                            <div className="grid grid-cols-2 border-b">
                              <div className='p-2 px-4'>
                                <Skeleton className="h-4 w-8" />
                              </div>
                              <div className='p-2 text-left'>
                                <Skeleton className="h-4 w-12" />
                              </div>
                            </div>
                            {[...Array(7)].map((_, index) => (
                              <div key={index} className="grid grid-cols-2 text-xs p-2 px-4 pb-4 border-b last:border-b-0">
                                <div className="flex items-center">
                                  <Skeleton className="h-3 w-16" />
                                </div>
                                <div className="flex items-center">
                                  <Skeleton className="h-3 w-8" />
                                </div>
                              </div>
                            ))}
                            <div className="mt-3 pt-3 border-t p-4">
                              <Skeleton className="h-4 w-32" />
                            </div>
                          </div>
                        ) : (
                          <div className="space-y-2 border m-4 rounded-md ">
                            <div className="grid grid-cols-2 border-b text-sm px-4">
                              <div className='p-2 '>
                                Date
                              </div>
                              <div className='p-2 text-right'>
                                Change
                              </div>
                            </div>
                            {creditData && creditData.length > 0 ?
                              creditData?.map((item, index) => (
                                <div key={index} className="grid grid-cols-2 text-xs p-2 px-4 pb-4 border-b last:border-b-0">
                                  <span className="text-muted-foreground">
                                    {dayjs(item.date).format('MMM DD')}
                                  </span>

                                  <span className={`font-medium text-right px-2 ${item.change > 0 ? 'text-green-600' : 'text-red-600'
                                    }`}>
                                    {item.change > 0 ? '+' : ''}{item.change}
                                  </span>
                                </div>
                              )) : <div className="text-sm text-muted-foreground p-4 text-center">
                                No credit data available
                              </div>
                            }
                            {
                              creditData && creditData.length > 0 &&
                              <div className="mt-3 pt-3 border-t p-4">
                                <div className="text-sm font-semibold">
                                  Current Balance: {apiData?.data?.currentBalance} pts
                                </div>
                              </div>
                            }


                          </div>
                        )}
                      </CardContent>
                    </Card>
                  </div>

                  {/* Right Panel - Opportunities List */}
                  <div className="col-span-4 xl:col-span-3">
                    <Card>
                      <CardHeader className='border-b p-4'>
                        <CardTitle className='flex justify-between items-center'>
                          <p className="text-lg">Opportunities ({paginationTotal})</p>
                          <div className="flex items-center gap-3">
                            <span className="text-sm text-muted-foreground hidden sm:block">Rows per page</span>
                            <Select value={String(pageSize)} onValueChange={(v) => { setPageSize(Number(v)); setPage(1); }}>
                              <SelectTrigger className="w-[60px]">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent className="p-0">
                                <SelectItem value="10">10</SelectItem>
                                <SelectItem value="25">25</SelectItem>
                                <SelectItem value="50">50</SelectItem>
                                <SelectItem value="100">100</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </CardTitle>
                      </CardHeader>
                      <CardContent className='p-0'>
                        <div className="space-y-4 border m-4 rounded-md">
                          {isOppLoading || isOppFetching || isApiLoading ? (
                            <div className="overflow-auto max-h-[450px]">
                              <Table>
                                <TableHeader>
                                  <TableRow>
                                    <TableHead><Skeleton className="h-4 w-20" /></TableHead>
                                    <TableHead><Skeleton className="h-4 w-16" /></TableHead>
                                    <TableHead><Skeleton className="h-4 w-12" /></TableHead>
                                    <TableHead><Skeleton className="h-4 w-24" /></TableHead>
                                    <TableHead><Skeleton className="h-4 w-16" /></TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {[...Array(5)].map((_, i) => (
                                    <TableRow key={i}>
                                      <TableCell>
                                        <div>
                                          <Skeleton className="h-4 w-32 mb-1" />
                                          <Skeleton className="h-3 w-24" />
                                        </div>
                                      </TableCell>
                                      <TableCell>
                                        <Skeleton className="h-6 w-16 rounded-full" />
                                      </TableCell>
                                      <TableCell>
                                        <Skeleton className="h-4 w-20" />
                                      </TableCell>
                                      <TableCell>
                                        <div className="flex items-center gap-2">
                                          <Skeleton className="h-2 w-16 rounded-full" />
                                          <Skeleton className="h-4 w-8" />
                                        </div>
                                      </TableCell>
                                      <TableCell>
                                        <Skeleton className="h-6 w-16 rounded-full" />
                                      </TableCell>
                                    </TableRow>
                                  ))}
                                </TableBody>
                              </Table>
                            </div>
                          ) : filteredMatches.length === 0 ? (
                            <div className="text-center py-8">
                              <div className="text-muted-foreground">
                                No {statusFilter === 'all' ? '' : statusFilter} opportunities found
                              </div>
                            </div>
                          ) : (
                            <>
                              <div className="grid w-full overflow-auto ">
                                <Table>
                                  <TableHeader className='*:whitespace-nowrap sticky top-0'>
                                    <TableRow>
                                      <TableHead className="font-semibold min-w-[230px]">Opportunity</TableHead>
                                      <TableHead className="font-semibold w-[200px]">Category</TableHead>
                                      <TableHead className="font-semibold min-w-[130px]">Date</TableHead>
                                      <TableHead className="font-semibold w-[200px]">Matching Score</TableHead>
                                      <TableHead className="font-semibold w-[200px]">Status</TableHead>
                                      {/* {statusFilter === 'interested' && <TableHead>Actions</TableHead>} */}
                                    </TableRow>
                                  </TableHeader>
                                  <TableBody>
                                    {filteredMatches.map((match) => (
                                      <TableRow key={match.id}>
                                        <TableCell className="min-w-[200px]">
                                          <div>
                                            <div className="font-medium">{match.opportunity?.title || 'Unknown'}</div>
                                            <div className="text-sm text-[#666666]">{match.opportunity?.organization || ""}</div>
                                          </div>
                                        </TableCell>
                                        <TableCell>
                                          {
                                            match.opportunity?.category && <StatusTag status={match.status} label={match.opportunity?.category || ''} className='capitalize' />
                                          }

                                        </TableCell>
                                        <TableCell>
                                          {match.opportunity?.eventDate ? dayjs(match.opportunity.eventDate).format('MMM DD, YYYY') : 'No Date'}
                                        </TableCell>
                                        <TableCell>
                                          <div className="flex items-center space-x-2  p- rounded-md w-fit">
                                            {/* Progress wrapper */}
                                            <div className="flex items-center space-x-2 bg-[#0A1424]  rounded-md w-fit ">
                                              {/* Progress wrapper */}
                                              <div className="w-24 h-2 rounded-full overflow-hidden bg-gradient-to-r from-[#52C41A] to-[#FAAD14] ">
                                                {/* Progress fill */}
                                                <div
                                                  className="h-full rounded-full bg-gradient-to-r from-[#52C41A] to-[#52C41A]"
                                                  style={{ width: `${match.matchingScore}%` }}
                                                />
                                              </div>

                                              {/* Percentage text */}
                                              <span className="text-green-500 font-medium">{match.matchingScore}%</span>
                                            </div>
                                          </div>

                                        </TableCell>
                                        <TableCell>
                                          <StatusTag status={match.status} label={match.status?.toUpperCase()} className={`status-tag ${match.status === "accepted" ? "border border-[#88E788] text-white " : ""}`} />
                                        </TableCell>
                                        {/* {statusFilter !== 'interested' && (
                                        <TableCell>
                                          <div className="flex items-center gap-1">
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => handleMatchStatusChange(match.id, 'accepted')}
                                              disabled={!permissions.canEdit}
                                              className="h-8 w-8 p-0"
                                            >
                                              <Check className="h-4 w-4 text-green-600" />
                                            </Button>
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => {
                                                setRejectingMatch(match);
                                                setIsRejectModalVisible(true);
                                              }}
                                              disabled={!permissions.canEdit}
                                              className="h-8 w-8 p-0"
                                            >
                                              <X className="h-4 w-4 text-red-600" />
                                            </Button>
                                            <Button
                                              variant="ghost"
                                              size="sm"
                                              onClick={() => navigate(`/opportunities/${match.opportunity?.id}`)}
                                              className="h-8 w-8 p-0"
                                            >
                                              <Eye className="h-4 w-4" />
                                            </Button>
                                          </div>
                                        </TableCell>
                                      )} */}
                                      </TableRow>
                                    ))}
                                  </TableBody>
                                </Table>
                              </div>

                              {/* Pagination */}
                              {paginationTotal > pageSize && (
                                <div className="flex items-center justify-between p-3">
                                  <div className="text-sm text-muted-foreground whitespace-nowrap">
                                    Showing {Math.min((page - 1) * pageSize + 1, paginationTotal)} to {Math.min(page * pageSize, paginationTotal)} of {paginationTotal} items

                                  </div>
                                  <Pagination>
                                    <PaginationContent>
                                      <PaginationItem>
                                        <PaginationPrevious
                                          onClick={() => setPage(Math.max(1, page - 1))}
                                          className={page === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                                        />
                                      </PaginationItem>

                                      {/* Show page numbers */}
                                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                                        const start = Math.max(1, Math.min(totalPages - 4, page - 2));
                                        const pageNumber = start + i;
                                        if (pageNumber <= totalPages) {
                                          return (
                                            <PaginationItem key={pageNumber}>
                                              <PaginationLink
                                                onClick={() => setPage(pageNumber)}
                                                isActive={page === pageNumber}
                                                className="cursor-pointer"
                                              >
                                                {pageNumber}
                                              </PaginationLink>
                                            </PaginationItem>
                                          );
                                        }
                                        return null;
                                      })}

                                      {/* Last page link if not included */}
                                      {(() => {
                                        const start = Math.max(1, Math.min(totalPages - 4, page - 2));
                                        const end = Math.min(totalPages, start + 4);
                                        return end < totalPages ? (
                                          <PaginationItem key={`last-${totalPages}`}>
                                            <PaginationLink onClick={() => setPage(totalPages)} className="cursor-pointer" isActive={page === totalPages}>
                                              {totalPages}
                                            </PaginationLink>
                                          </PaginationItem>
                                        ) : null;
                                      })()}

                                      <PaginationItem>
                                        <PaginationNext
                                          onClick={() => setPage(Math.min(totalPages, page + 1))}
                                          className={page >= totalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                                        />
                                      </PaginationItem>
                                    </PaginationContent>
                                  </Pagination>

                                </div>
                              )}
                            </>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>

                {/* Notes Section */}
                {/* <Card className="mt-6">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0">
                    <CardTitle className="text-lg">Admin Notes</CardTitle>
                    {permissions.canEdit && (
                      <Button onClick={handleAddNote}>
                        <Plus className="mr-2 h-4 w-4" />
                    Add Note
                  </Button>
                    )}
                  </CardHeader>
                  <CardContent className="space-y-4"> */}
                {/* Mock notes */}
                {/* {[{
                      id: '1',
                      content: 'Excellent speaker with great engagement',
                      author: 'Admin User',
                      createdAt: dayjs().subtract(2, 'days').toISOString()
                    }, {
                      id: '2',
                      content: 'Follow up needed for Q2 opportunities',
                      author: 'Manager',
                      createdAt: dayjs().subtract(5, 'days').toISOString()
                    }].map(note => (
                      <Card key={note.id} className="bg-muted/50">
                        <CardContent className="p-4">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="mb-2">{note.content}</div>
                              <div className="text-xs text-muted-foreground">
                              By {note.author} • {dayjs(note.createdAt).fromNow()}
                              </div>
                            </div>
                          {permissions.canEdit && (
                              <div className="flex items-center gap-1">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEditNote(note)}
                                  className="h-8 w-8 p-0"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteNote(note.id)}
                                  disabled={!permissions.canDelete}
                                  className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </CardContent>
                </Card> */}
              </div>
            )
          )}

          {activeTab === 'details' && (
            isApiLoading || !apiData?.data?.["form-data"] ? (
              <div className="p-4 bg-tertiary">
                <Card>
                  <CardHeader className='border-b'>
                    <Skeleton className="h-6 w-64" />
                  </CardHeader>
                  <CardContent>
                    <div className="overflow-x-auto">
                      {[...Array(2)].map((_, index) => (
                        <div
                          key={index}
                          className="border rounded-md my-4 w-full space-y-2"
                        >
                          {/* Accordion Header Skeleton */}
                          <div className="border-none rounded-md px-4 py-3">
                            <div className="flex items-center justify-between">
                              <Skeleton className="h-5 w-48" />
                              <Skeleton className="h-4 w-4 rounded" />
                            </div>
                          </div>

                          {/* Accordion Content Skeleton */}
                          <div className="px-4 pb-4">
                            <div className="grid grid-cols-1 lg:grid-cols-2 rounded-sm border">
                              {[...Array(6)].map((_, newIndex) => (
                                <div
                                  key={newIndex}
                                  className="grid grid-cols-2 border-r"
                                >
                                  <div className="p-4 bg-muted/100">
                                    <Skeleton className="h-4 w-32" />
                                  </div>
                                  <div className="p-4 border-b">
                                    <Skeleton className="h-4 w-24" />
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            ) : (
              <div className="p-4 bg-tertiary">
                <Card>
                  <CardHeader className='border-b'>
                    <CardTitle className="text-lg">Complete Speaker Information</CardTitle>
                  </CardHeader>

                  <CardContent>
                    <div className="overflow-x-auto">
                      {apiData?.data?.["form-data"]?.map((item, index) => (
                        <Accordion
                          type="single"
                          collapsible
                          key={index}
                          className="border rounded-md my-4 w-full space-y-2"
                        >
                          <AccordionItem
                            value={`item-${index}`}
                            className="border-none rounded-md px-4"
                          >
                            <AccordionTrigger className="hover:no-underline flex-row-reverse">
                              {item?.form_type_name}
                            </AccordionTrigger>
                            <AccordionContent>
                              <div className="grid grid-cols- md:grid-cols-1  lg:grid-cols-2 rounded-sm border ">
                                {item.questions.map((newItem, newIndex) => (
                                  <div
                                    key={newIndex}
                                    className={`grid grid-cols-2 border-r`}
                                  >
                                    <span className={`text-sm text-gray-400 p-4 w-full border-r ${ui.theme == "dark" ? "bg-[#00152A]" : "bg-white"}`}>{newItem?.question}</span>
                                    <span className="text-sm font-medium p-4 w-full border-b">{newItem?.selected_value}</span>
                                  </div>
                                ))}
                              </div>

                            </AccordionContent>
                          </AccordionItem>
                        </Accordion>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )
          )}
        </div>
      </div>

      {/* Credit History Modal */}
      <Dialog open={isCreditHistoryModalVisible} onOpenChange={(open) => { setIsCreditHistoryModalVisible(open); if (!open) setHistoryPage(1); }}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Complete Credit History</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {isHistoryLoading || isHistoryFetching ? (
              <LoadingSkeleton type="table" rows={4} />
            ) : (
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[120px]">Date</TableHead>
                    <TableHead className="w-[100px]">Type</TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="w-[120px] whitespace-nowrap">Points Change</TableHead>
                    <TableHead className="w-[100px]">Balance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {creditHistory && creditHistory.length > 0 ? (
                    creditHistory.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell className="text-sm">
                          {dayjs(item.date).format('MMM DD, YYYY')}
                        </TableCell>
                        <TableCell>
                          <Badge
                            variant={item.type === 'credit' ? 'default' : 'secondary'}
                          >
                            {item.type?.toUpperCase()}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-sm">{item.source}</TableCell>
                        <TableCell
                          className={`text-sm font-medium ${item.change > 0 ? 'text-green-600' : 'text-red-600'
                            }`}
                        >
                          {item.change > 0 ? '+' : ''}
                          {item.change}
                        </TableCell>
                        <TableCell className="text-sm font-semibold">
                          {item.balance}
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    // 3. Fallback only when not loading and no data
                    <TableRow>
                      <TableCell
                        colSpan={5}
                        className="h-[300px] text-center text-sm text-muted-foreground"
                      >
                        No credit data available
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>

              </Table>
            )}
            {/* History Pagination */}
            {historyTotal > historyPageSize && (
              <div className="flex items-center justify-between">
                <div className="text-sm text-muted-foreground whitespace-nowrap">
                  Showing {Math.min((historyPage - 1) * historyPageSize + 1, historyTotal)} to {Math.min(historyPage * historyPageSize, historyTotal)} of {historyTotal} items
                </div>
                
                <div className="flex items-center gap-4">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => setHistoryPage(Math.max(1, historyPage - 1))}
                          className={historyPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                        />
                      </PaginationItem>
                      
                      {/* Show page numbers */}
                      {Array.from({ length: Math.min(5, Math.ceil(historyTotal / historyPageSize)) }, (_, i) => {
                        const start = Math.max(1, Math.min(Math.ceil(historyTotal / historyPageSize) - 4, historyPage - 2));
                        const pageNumber = start + i;
                        if (pageNumber <= Math.ceil(historyTotal / historyPageSize)) {
                          return (
                            <PaginationItem key={pageNumber}>
                              <PaginationLink
                                onClick={() => setHistoryPage(pageNumber)}
                                isActive={historyPage === pageNumber}
                                className="cursor-pointer"
                              >
                                {pageNumber}
                              </PaginationLink>
                            </PaginationItem>
                          );
                        }
                        return null;
                      })}

                      {/* Last page link if not included */}
                      {(() => {
                        const totalPages = Math.ceil(historyTotal / historyPageSize);
                        const start = Math.max(1, Math.min(totalPages - 4, historyPage - 2));
                        const end = Math.min(totalPages, start + 4);
                        return end < totalPages ? (
                          <PaginationItem key={`last-${totalPages}`}>
                            <PaginationLink 
                              onClick={() => setHistoryPage(totalPages)} 
                              className="cursor-pointer" 
                              isActive={historyPage === totalPages}
                            >
                              {totalPages}
                            </PaginationLink>
                          </PaginationItem>
                        ) : null;
                      })()}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => setHistoryPage(Math.min(Math.ceil(historyTotal / historyPageSize), historyPage + 1))}
                          className={historyPage >= Math.ceil(historyTotal / historyPageSize) ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                  
                  <div className="flex items-center gap-3">
                    <span className="text-sm text-muted-foreground hidden sm:block">Rows per page</span>
                    <Select value={String(historyPageSize)} onValueChange={(v) => { 
                      setHistoryPageSize(Number(v)); 
                      setHistoryPage(1); 
                    }}>
                      <SelectTrigger className="w-[60px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent className="p-0">
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="25">25</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                        <SelectItem value="100">100</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {/* Edit Speaker Modal */}
      <Dialog open={isEditModalVisible} onOpenChange={setIsEditModalVisible}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Speaker</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="name">Name</Label>
              <Input
                id="name"
                placeholder="Speaker name"
                value={editFormData.name}
                onChange={(e) => setEditFormData({ ...editFormData, name: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                placeholder="Speaker email"
                value={editFormData.email}
                onChange={(e) => setEditFormData({ ...editFormData, email: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="subscriptionPlan">Subscription Plan</Label>
              <Select
                value={editFormData.subscriptionPlan}
                onValueChange={(value) => setEditFormData({ ...editFormData, subscriptionPlan: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select plan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="basic">Basic</SelectItem>
                  <SelectItem value="premium">Premium</SelectItem>
                  <SelectItem value="enterprise">Enterprise</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="activityStatus">Activity Status</Label>
              <Select
                value={editFormData.activityStatus}
                onValueChange={(value) => setEditFormData({ ...editFormData, activityStatus: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setIsEditModalVisible(false)}>
                Cancel
              </Button>
              <Button
                disabled={isUpdating}
                onClick={async () => {
                  if (!id) return;
                  try {
                    await updateSpeaker({
                      id,
                      name: editFormData.name || speaker.name,
                      email: editFormData.email || speaker.email,
                      subscriptionPlan: editFormData.subscriptionPlan || speaker.subscriptionPlan,
                      activityStatus: editFormData.activityStatus || speaker.activityStatus,
                    }).unwrap();
                    setIsEditModalVisible(false);
                  } catch (e) {
                    console.error('Failed to update speaker', e);
                  }
                }}
              >
                {isUpdating ? 'Saving...' : 'Save Changes'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Reject Match Modal */}
      <Dialog open={isRejectModalVisible} onOpenChange={setIsRejectModalVisible}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Reject Match</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="rejectionReason">Rejection Reason *</Label>
              <Select
                value={rejectFormData.rejectionReason}
                onValueChange={(value) => setRejectFormData({ ...rejectFormData, rejectionReason: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select rejection reason" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="expertise">Speaker expertise does not match event requirements</SelectItem>
                  <SelectItem value="availability">Speaker availability conflicts with event dates</SelectItem>
                  <SelectItem value="budget">Budget constraints - speaker fee too high</SelectItem>
                  <SelectItem value="location">Speaker location too far from event venue</SelectItem>
                  <SelectItem value="experience">Insufficient speaking experience for this level of event</SelectItem>
                  <SelectItem value="declined">Speaker declined due to scheduling conflicts</SelectItem>
                  <SelectItem value="other">Other reason</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="notes">Additional Notes</Label>
              <Textarea
                id="notes"
                rows={3}
                placeholder="Additional details..."
                value={rejectFormData.notes}
                onChange={(e) => setRejectFormData({ ...rejectFormData, notes: e.target.value })}
              />
            </div>
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setIsRejectModalVisible(false)}>
                Cancel
              </Button>
              <Button
                variant="destructive"
                onClick={() => {
                  if (rejectingMatch) {
                    handleMatchStatusChange(rejectingMatch.id, 'rejected');
                    setIsRejectModalVisible(false);
                    setRejectingMatch(null);
                  }
                }}
              >
                Confirm Rejection
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Add Note Modal */}
      <Dialog open={isNoteModalVisible} onOpenChange={setIsNoteModalVisible}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{editingNote ? "Edit Note" : "Add Note"}</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="content">Note Content *</Label>
              <Textarea
                id="content"
                rows={4}
                placeholder="Enter your note..."
                value={noteFormData.content}
                onChange={(e) => setNoteFormData({ ...noteFormData, content: e.target.value })}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="category">Category</Label>
              <Select
                value={noteFormData.category}
                onValueChange={(value) => setNoteFormData({ ...noteFormData, category: value })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general">General</SelectItem>
                  <SelectItem value="performance">Performance</SelectItem>
                  <SelectItem value="communication">Communication</SelectItem>
                  <SelectItem value="technical">Technical</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex justify-end space-x-2 pt-4">
              <Button variant="outline" onClick={() => setIsNoteModalVisible(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveNote}>
                {editingNote ? 'Update Note' : 'Add Note'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default SpeakerDetail;