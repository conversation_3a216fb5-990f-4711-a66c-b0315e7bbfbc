
const authController = require("../controllers/auth-controller");
const router = require("express").Router();

module.exports = (app) => {

    // Public routes (no authentication required)
    router.post("/login", authController.login);
    router.post("/signup", authController.signup);
    
    // forgot password and reset password routes
    router.post("/forgot-password", authController.forgotPassword);
    router.post("/reset-password", authController.resetPassword);

    // change password routes
    // router.post("/change-password", authController.changePassword);

    app.use("/speaker/auth/v1", router);

};