const CustomError  = require("../helpers/custome-error");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const dashboardService = require("../services/dashboard-service");
const ApiResponse = require("../helpers/api-response");




exports.getApplicationStats = async (req, res, next) => {
    try{ 
        const {status,message,result,pagination} = await dashboardService.getApplicationStats(req);
        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data: result, pagination }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    }catch(error){
        next(error);
    }
}