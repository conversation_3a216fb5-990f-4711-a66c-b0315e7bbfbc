import * as React from "react"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { AlertCircle, CheckCircle, Info, AlertTriangle } from "lucide-react"

const themeAlertVariants = cva(
  "relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4",
  {
    variants: {
      variant: {
        info: "bg-blue-50 border-blue-200 text-blue-800 dark:bg-blue-950/50 dark:border-blue-800/50 dark:text-blue-200 [&>svg]:text-blue-600 dark:[&>svg]:text-blue-400",
        success: "bg-green-50 border-green-200 text-green-800 dark:bg-green-950/50 dark:border-green-800/50 dark:text-green-200 [&>svg]:text-green-600 dark:[&>svg]:text-green-400",
        warning: "bg-yellow-50 border-yellow-200 text-yellow-800 dark:bg-yellow-950/50 dark:border-yellow-800/50 dark:text-yellow-200 [&>svg]:text-yellow-600 dark:[&>svg]:text-yellow-400",
        error: "bg-red-50 border-red-200 text-red-800 dark:bg-red-950/50 dark:border-red-800/50 dark:text-red-200 [&>svg]:text-red-600 dark:[&>svg]:text-red-400",
      },
    },
    defaultVariants: {
      variant: "info",
    },
  }
)

export interface ThemeAlertProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof themeAlertVariants> {
  title?: string
  description?: string
  icon?: React.ReactNode
  showIcon?: boolean
}

const ThemeAlert = React.forwardRef<HTMLDivElement, ThemeAlertProps>(
  ({ className, variant, title, description, icon, showIcon = true, children, ...props }, ref) => {
    const defaultIcons = {
      info: <Info className="h-4 w-4" />,
      success: <CheckCircle className="h-4 w-4" />,
      warning: <AlertTriangle className="h-4 w-4" />,
      error: <AlertCircle className="h-4 w-4" />,
    }

    return (
      <div
        ref={ref}
        role="alert"
        className={cn(themeAlertVariants({ variant }), className)}
        {...props}
      >
        {showIcon && (icon || defaultIcons[variant || "info"])}
        <div className="ml-0">
          {title && (
            <h5 className="mb-1 font-medium leading-none tracking-tight">
              {title}
            </h5>
          )}
          {description && (
            <div className="text-sm [&_p]:leading-relaxed">
              {description}
            </div>
          )}
          {children}
        </div>
      </div>
    )
  }
)
ThemeAlert.displayName = "ThemeAlert"

export { ThemeAlert }
