"""
LLM-based extractor for event fields.
 
Runs after load_page to extract structured fields using a constrained JSON prompt.
Falls back gracefully if no API key configured.
"""
import j<PERSON>
from typing import Dict, Optional
from datetime import datetime, date
from openai import OpenAI
from app.config.config import config
from app.config.logger import get_logger
logger = get_logger(__name__, file_name="scraper.log")
 
# Initialize OpenAI client once
openai_client = OpenAI(api_key=config.OPENAI_API_KEY)
 
def _build_prompt(text: str) -> str:
    return (
        "You are an advanced event data extraction system.\n\n"
        "Your task: From the given unstructured text about an event, extract accurate structured event details.\n\n"
        "Return ONLY valid JSON with the following keys:\n"
        "[\"title\", \"event_type\", \"description\", \"venue\", \"city\", \"state\", \"country\", \"start_date\", \"end_date\", \"is_virtual\", \"industry\", \"contact_email\", \"is_call_for_speaker\", \"is_closed_for_speaker\", \"applied_by\", \"has_multiple_events\"]\n\n"
        "### Extraction Guidelines:\n\n"
        "1. **Title**\n"
        "   - Extract the official name of the event (short and precise).\n"
        "   - Avoid marketing phrases like \"Join us for\" or \"Don't miss\".\n"
        "   - If multiple names appear, choose the one representing the full event.\n\n"
        "2. **Event Type**\n"
        "   - Classify the type of event based on content and context.\n"
        "   - Common types: conference, workshop, webinar, seminar, summit, meetup, training, course, hackathon, competition, festival, exhibition, trade show, networking event, panel discussion, keynote, presentation, demo, launch, announcement, RFP, proposal, tender, call for papers, call for speakers, etc.\n"
        "   - Choose the most specific and accurate type.\n"
        "   - If unclear, use a general type like 'event' or 'meeting'.\n\n"
        "3. **Description**\n"
        "   - Write a short summary (around 250 words) of what the event is about.\n"
        "   - Use neutral, factual language.\n\n"
        "4. **Dates (start_date, end_date)**\n"
        "   - Identify all date mentions in the text.\n"
        "   - Choose the main event duration, not registration or submission deadlines.\n"
        "   - Normalize both dates to YYYY-MM-DD format.\n"
        "   - If only one valid event date exists, use it for both start and end.\n"
        "   - If no valid event dates are found, set both to null.\n\n"
        "5. **Location (venue, city, state, country)**\n"
        "   - Extract venue name if mentioned.\n"
        "   - Detect city, state, and country related to that venue.\n"
        "   - **IMPORTANT**: Always use FULL NAMES, never abbreviations:\n"
        "     * **States**: 'NY' → 'New York', 'CA' → 'California', 'TX' → 'Texas', 'FL' → 'Florida'\n"
        "     * **Countries**: 'USA' → 'United States', 'UK' → 'United Kingdom', 'UAE' → 'United Arab Emirates'\n"
        "     * **Cities**: 'NYC' → 'New York City', 'LA' → 'Los Angeles', 'SF' → 'San Francisco'\n"
        "   - **Smart Location Inference Rules**:\n"
        "     * If ONLY city is found → Use your knowledge to infer the most likely state and country\n"
        "     * If ONLY state is found → Use your knowledge to infer the country (do NOT guess city)\n"
        "     * If ONLY country is found → Use only the country (do NOT reverse-engineer city/state)\n"
        "     * If multiple locations mentioned → Choose the primary event location\n"
        "   - If any field cannot be reasonably inferred, set it to null.\n"
        "   - Do not fabricate unrelated data.\n\n"
        "6. **Virtual Event (is_virtual)**\n"
        "   - Set to true if the event is explicitly virtual, online, or remote.\n"
        "   - Set to false if the event is in-person or hybrid.\n"
        "   - Set to null if unclear from the text.\n\n"
        "7. **Industry**\n"
        "   - Identify the primary industry or sector this event serves.\n"
        "   - Common industries: technology, healthcare, finance, education, manufacturing, government, music, data science, etc.\n"
        "   - If unclear, set to null.\n\n"
        "8. **Contact Email**\n"
        "   - Extract any contact email mentioned for submissions, inquiries, or registration.\n"
        "   - Look for patterns like 'contact@', 'info@', 'submissions@', etc.\n"
        "   - If not exact email found, set to null.\n\n"
        "9. **Call for Speakers (is_call_for_speaker)**\n"
        "   - **STRICT RULE**: Set to true ONLY if the event is EXPLICITLY and ACTIVELY seeking speakers, presenters, or paper submissions.\n"
        "   - **REQUIRED EVIDENCE**: Must find clear, explicit phrases indicating speaker recruitment such as:\n"
        "     * 'call for speakers', 'call for papers', 'call for proposals', 'call for abstracts'\n"
        "     * 'speaker submissions', 'speaker applications', 'speaker proposals', 'speaker nominations'\n"
        "     * 'presentation proposals', 'abstract submissions', 'paper submissions', 'talk submissions'\n"
        "     * 'we are seeking speakers', 'looking for speakers', 'inviting speakers', 'speaker recruitment'\n"
        "     * 'submit your proposal', 'submit your abstract', 'submit your talk', 'apply to speak'\n"
        "     * 'speaker registration', 'speaker sign-up', 'become a speaker', 'speak at our event'\n"
        "   - **STRICT EXCLUSIONS** - Set to false if you only see:\n"
        "     * General event announcements, event descriptions, or event information pages\n"
        "     * Registration pages, ticket sales, or attendee sign-up pages\n"
        "     * Event schedules, agendas, or program listings\n"
        "     * Speaker bios, speaker profiles, or 'meet our speakers' pages\n"
        "     * General event information without explicit speaker recruitment language\n"
        "     * Pages that mention 'speakers' only in context of 'our speakers' or 'featured speakers' (past/present speakers, not seeking new ones)\n"
        "     * Event listings or directories without speaker recruitment\n"
        "   - **CRITICAL**: If the text mentions speakers but does NOT explicitly ask for submissions/applications/proposals, set to false.\n"
        "   - Always return true or false, never null\n\n"
        "10. **Closed for Speakers (is_closed_for_speaker)**\n"
        "   - **CRITICAL**: Set to true if ANY of the following conditions are met:\n"
        "     * Speaker submissions are explicitly closed, ended, or no longer accepting applications.\n"
        "     * The application deadline (applied_by) date has PASSED (compare with today's date).\n"
        "     * Look for phrases like 'submissions closed', 'call for speakers closed', 'deadline passed', 'no longer accepting', 'submissions ended', 'speaker applications closed', 'abstract submission closed', etc.\n"
        "   - **IMPORTANT**: If you extracted an 'applied_by' deadline date, you MUST check if that date is in the past. If the deadline date is before today's date, then is_closed_for_speaker MUST be true.\n"
        "   - Set to false ONLY if submissions are still open, ongoing, AND the deadline (if any) has NOT passed.\n"
        "   - Always give true or false, never null\n\n"
        "11. **Application Deadline (applied_by)**\n"
        "   - Extract the deadline date for speaker applications, submissions, or proposals.\n"
        "   - Look for phrases like 'deadline', 'due date', 'submission deadline', 'application deadline', 'call for speakers closes', 'abstract submission due', 'proposal deadline', etc.\n"
        "   - Normalize the date to YYYY-MM-DD format.\n"
        "   - If multiple deadlines exist, choose the main speaker application deadline.\n"
        "   - If no application deadline is mentioned, set to null.\n"
        "   - Focus on speaker-related deadlines, not general event registration deadlines.\n\n"
        "12. **Multiple Events Detection (has_multiple_events)**\n"
        "   - **CRITICAL**: Analyze the entire text to determine if it contains information about MULTIPLE distinct events.\n"
        "   - **IMPORTANT RULE**: If the page contains multiple events BUT one of them has PROPER/COMPLETE data (has start_date, city, state, title, description, etc.), then:\n"
        "     * Extract that ONE main event with proper data\n"
        "     * Set has_multiple_events to FALSE (treat it as a single event)\n"
        "     * Fill in all fields for that one main event (title, start_date, end_date, city, state, country, description, etc.)\n"
        "   - Set to true ONLY if:\n"
        "     * The page is a pure listing/catalog page with multiple events but NONE have complete/proper data\n"
        "     * The page is an events directory, archive, or calendar with no single focused event\n"
        "     * Multiple events are mentioned but all are incomplete (missing dates, locations, etc.)\n"
        "   - Set to false if:\n"
        "     * A single, specific event with complete data (title, dates, location, etc.)\n"
        "     * Multiple events mentioned but ONE has proper/complete data - extract that one\n"
        "     * One call-for-speakers opportunity for one event with proper data\n"
        "   - **Key Criteria for 'Proper Data'**: An event has proper data if it has at least:\n"
        "     * Title (clear event name)\n"
        "     * Start date OR end date (at least one date)\n"
        "     * Location information (city OR state OR country - at least one)\n"
        "     * Description or event details\n"
        "   - Always return true or false, never null\n\n"
        "13. **General Rules**\n"
        "   - Ignore unrelated or administrative dates (like deadlines or early-bird offers).\n"
        "   - Always output clean JSON — no comments, markdown, or text outside JSON.\n"
        "   - Missing or unclear fields → infer reasonably using general knowledge, if possible.\n"
        "   - Prioritize clarity and factual correctness.\n\n"
        "Now extract and return only JSON for the following text:\n\n"
        "\"\"\"\n"
        f"{text}\n"
        "\"\"\"\n"
    )

 
 
def _first_json_block(s: str) -> Optional[str]:
    try:
        start = s.find("{")
        end = s.rfind("}")
        if start != -1 and end != -1 and end > start:
            return s[start:end+1]
    except Exception:
        pass
    return None
 
 
async def extract_with_llm(url: str, title: str, text: str, soup) -> Optional[Dict]:
    """
    LLM-based extraction using OpenAI Chat API (GPT-4/3.5).
    Returns structured event data as dict or None.
    """
    if not config.OPENAI_API_KEY:
        logger.warning("OPENAI_API_KEY not configured — skipping LLM extraction.")
        return None
 
    prompt_core = _build_prompt(text)  # Reuse your existing prompt builder
 
    try:
        # OpenAI Chat Completions API (new SDK syntax)
        response = openai_client.chat.completions.create(
            model=config.OPENAI_MODEL,
            messages=[{"role": "user", "content": prompt_core}],
            temperature=config.OPENAI_TEMPERATURE,
        )
        content = response.choices[0].message.content if response and response.choices else ""
        logger.info(f"OpenAI API call successful, length: {len(content)} characters")
    except Exception as e:
        logger.warning(f"OpenAI API call failed: {e}")
        return None
 
    if not content:
        return None
 
    # Extract JSON block safely
    block = _first_json_block(content) or content
    try:
        parsed = json.loads(block)
       
        # Post-processing: Strict validation for is_closed_for_speaker
        # If applied_by deadline has passed, automatically set is_closed_for_speaker to True
        applied_by = parsed.get('applied_by')
        if applied_by and isinstance(applied_by, str):
            try:
                deadline_date = datetime.strptime(applied_by, '%Y-%m-%d').date()
                today = date.today()
                if deadline_date < today:
                    # Deadline has passed, so it's closed
                    if parsed.get('is_call_for_speaker') is True:
                        parsed['is_closed_for_speaker'] = True
                        logger.info(f"Post-processing: Deadline {applied_by} has passed. Setting is_closed_for_speaker=True")
            except (ValueError, TypeError) as e:
                logger.warning(f"Could not parse applied_by date '{applied_by}': {e}")
       
        logger.info(
            "LLM(OpenAI) extracted: title=%r, event_type=%r, description=%r, venue=%r, city=%r, state=%r, country=%r, start_date=%r, end_date=%r, is_virtual=%r, industry=%r, contact_email=%r, is_call_for_speaker=%r, is_closed_for_speaker=%r, applied_by=%r, has_multiple_events=%r, button_name=%r",
            parsed.get('title'), parsed.get('event_type'), parsed.get('description'), parsed.get('venue'),            
            parsed.get('city'), parsed.get('state'), parsed.get('country'),
            parsed.get('start_date'), parsed.get('end_date'), parsed.get('is_virtual'), parsed.get('industry'),
            parsed.get('contact_email'), parsed.get('is_call_for_speaker'), parsed.get('is_closed_for_speaker'), parsed.get('applied_by'),
            parsed.get('has_multiple_events'), parsed.get('button_name'),
        )
        return parsed
    except Exception as e:
        logger.warning(f"OpenAI JSON parse failed: {e}")
        return None
 