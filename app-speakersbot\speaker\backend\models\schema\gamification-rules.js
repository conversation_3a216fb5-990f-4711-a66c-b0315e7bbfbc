const connection = require('../connection');
const { DataTypes } = require('sequelize');

const GamificationRules = connection.define('GamificationRules', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    comment: 'Primary key for the setting',
  },
  key:{
    type: DataTypes.STRING,
    allowNull: false,
    comment: 'Unique key for the gamification point setting',
  },
  points: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  limit_count:{
    type: DataTypes.INTEGER,
    allowNull: true,
  },
  limit_period:{
    type: DataTypes.STRING,
    allowNull: true,
  },
  is_active:{
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: 1,
  },
  type:{
    type: DataTypes.STRING,
    allowNull: false,
  },
  description:{
    type: DataTypes.TEXT,
    allowNull: true,
    comment: 'Description of the gamification rule',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Record creation timestamp',
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Record last update timestamp',
  }
}, {
  tableName: 'gamification_rules',
  timestamps: false
});
module.exports = GamificationRules;