import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Trophy, Star, Target, Zap, Award, TrendingUp, Calendar, Users } from 'lucide-react';

export function GamificationTab() {
  const achievements = [
    { id: 1, name: 'First Speaker', description: 'Completed your first speaking engagement', icon: Trophy, earned: true, points: 100 },
    { id: 2, name: 'Profile Master', description: 'Completed 100% of your speaker profile', icon: Star, earned: true, points: 50 },
    { id: 3, name: '<PERSON> Bird', description: 'Applied to 5 opportunities within 24 hours', icon: Zap, earned: false, points: 75 },
    { id: 4, name: 'Networking Pro', description: 'Connected with 20+ fellow speakers', icon: Users, earned: false, points: 125 }
  ];

  const challenges = [
    { id: 1, name: 'Weekly Applier', description: 'Apply to 3 opportunities this week', progress: 2, target: 3, reward: 150 },
    { id: 2, name: 'Profile Perfectionist', description: 'Update all sections of your profile', progress: 7, target: 10, reward: 100 },
    { id: 3, name: 'Social Butterfly', description: 'Share 5 speaking tips on social media', progress: 1, target: 5, reward: 75 }
  ];

  const leaderboard = [
    { rank: 1, name: '<PERSON> Chen', points: 2450, talks: 12, badge: 'Diamond Speaker' },
    { rank: 2, name: 'Mike Rodriguez', points: 2180, talks: 9, badge: 'Gold Speaker' },
    { rank: 3, name: 'Alex Johnson', points: 1890, talks: 7, badge: 'Silver Speaker' },
    { rank: 4, name: 'You', points: 1650, talks: 5, badge: 'Bronze Speaker' },
    { rank: 5, name: 'Emma Wilson', points: 1420, talks: 4, badge: 'Bronze Speaker' }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Speaker Gamification</h2>
          <p className="text-foreground-muted mt-1">Level up your speaking career with achievements and challenges</p>
        </div>
        <Badge className="bg-gradient-primary text-primary-foreground px-4 py-2 text-lg">
          Level 5 Speaker
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Current Stats */}
        <div className="lg:col-span-2 space-y-6">
          {/* XP Progress */}
          <Card className="bg-gradient-surface border-border-subtle">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-primary" />
                Experience Progress
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-foreground-muted">Current Level: 5</span>
                <span className="text-sm text-foreground-muted">Next Level: 6</span>
              </div>
              <Progress value={75} className="h-3" />
              <div className="flex items-center justify-between text-sm">
                <span className="text-foreground">1,650 XP</span>
                <span className="text-foreground-muted">2,000 XP needed</span>
              </div>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 pt-4">
                <div className="text-center p-3 bg-surface-elevated rounded-lg">
                  <div className="text-lg font-bold text-primary">1,650</div>
                  <div className="text-xs text-foreground-muted">Total XP</div>
                </div>
                <div className="text-center p-3 bg-surface-elevated rounded-lg">
                  <div className="text-lg font-bold text-success">5</div>
                  <div className="text-xs text-foreground-muted">Talks Given</div>
                </div>
                <div className="text-center p-3 bg-surface-elevated rounded-lg">
                  <div className="text-lg font-bold text-warning">12</div>
                  <div className="text-xs text-foreground-muted">Applications</div>
                </div>
                <div className="text-center p-3 bg-surface-elevated rounded-lg">
                  <div className="text-lg font-bold text-primary">8</div>
                  <div className="text-xs text-foreground-muted">Achievements</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Active Challenges */}
          <Card className="bg-surface border-border-subtle">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Target className="h-5 w-5 text-primary" />
                Active Challenges
              </CardTitle>
              <CardDescription>Complete challenges to earn bonus XP</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {challenges.map((challenge) => (
                <div key={challenge.id} className="p-4 bg-surface-elevated rounded-lg border border-border-subtle">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-semibold text-foreground">{challenge.name}</h4>
                    <Badge variant="outline" className="text-xs">+{challenge.reward} XP</Badge>
                  </div>
                  <p className="text-sm text-foreground-muted mb-3">{challenge.description}</p>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-foreground-muted">Progress</span>
                      <span className="text-foreground">{challenge.progress}/{challenge.target}</span>
                    </div>
                    <Progress value={(challenge.progress / challenge.target) * 100} className="h-2" />
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Achievements */}
          <Card className="bg-surface border-border-subtle">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Award className="h-5 w-5 text-primary" />
                Achievements
              </CardTitle>
              <CardDescription>Unlock badges by reaching milestones</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {achievements.map((achievement) => {
                  const IconComponent = achievement.icon;
                  return (
                    <div 
                      key={achievement.id} 
                      className={`p-4 rounded-lg border transition-colors ${
                        achievement.earned 
                          ? 'bg-primary/10 border-primary/20' 
                          : 'bg-surface-elevated border-border-subtle opacity-60'
                      }`}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`p-2 rounded-lg ${
                          achievement.earned ? 'bg-primary text-primary-foreground' : 'bg-muted text-foreground-muted'
                        }`}>
                          <IconComponent className="h-4 w-4" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-foreground">{achievement.name}</h4>
                          <p className="text-xs text-foreground-muted">{achievement.description}</p>
                          <div className="flex items-center gap-2 mt-1">
                            <Badge variant={achievement.earned ? "default" : "outline"} className="text-xs">
                              +{achievement.points} XP
                            </Badge>
                            {achievement.earned && (
                              <Badge className="text-xs bg-success text-white">Earned</Badge>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Speaker Rank */}
          <Card className="bg-gradient-glow border-primary/20">
            <CardHeader className="pb-4 text-center">
              <div className="mx-auto w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mb-2">
                <Trophy className="h-8 w-8 text-primary-foreground" />
              </div>
              <CardTitle className="text-lg">Bronze Speaker</CardTitle>
              <CardDescription>Rank #4 in community</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3 pt-0">
              <Button className="w-full bg-primary hover:bg-primary-hover">
                View Full Profile
              </Button>
              <div className="text-center text-sm text-foreground-muted">
                230 XP until Silver Speaker
              </div>
            </CardContent>
          </Card>

          {/* Leaderboard */}
          <Card className="bg-surface border-border-subtle">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg">Community Leaderboard</CardTitle>
              <CardDescription>Top speakers this month</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              {leaderboard.map((user) => (
                <div 
                  key={user.rank} 
                  className={`flex items-center gap-3 p-2 rounded-lg ${
                    user.name === 'You' ? 'bg-primary/10 border border-primary/20' : 'bg-surface-elevated'
                  }`}
                >
                  <div className="w-6 h-6 rounded-full bg-primary text-primary-foreground text-xs flex items-center justify-center font-bold">
                    {user.rank}
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-semibold text-foreground">{user.name}</div>
                    <div className="text-xs text-foreground-muted">{user.points} XP • {user.talks} talks</div>
                  </div>
                  <Badge variant="outline" className="text-xs">{user.badge}</Badge>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Upcoming Events */}
          <Card className="bg-surface border-border-subtle">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Calendar className="h-5 w-5 text-primary" />
                Upcoming Events
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="p-3 bg-surface-elevated rounded-lg">
                <div className="text-sm font-semibold text-foreground">Monthly Challenge</div>
                <div className="text-xs text-foreground-muted">Ends in 12 days</div>
                <div className="text-xs text-primary">2x XP for applications</div>
              </div>
              <div className="p-3 bg-surface-elevated rounded-lg">
                <div className="text-sm font-semibold text-foreground">Speaker Spotlight</div>
                <div className="text-xs text-foreground-muted">March 15, 2024</div>
                <div className="text-xs text-primary">Featured speaker rewards</div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}