// Common API response types
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// User types
export interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  role: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthResponse {
  user: User;
  token: string;
  refreshToken: string;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  name: string;
  email: string;
  password: string;
}

// Opportunity types
export interface Opportunity {
  id: string;
  title: string;
  description: string;
  company: string;
  location: string;
  type: 'remote' | 'onsite' | 'hybrid';
  compensation: number;
  requirements: string[];
  benefits: string[];
  applicationDeadline: string;
  createdAt: string;
  updatedAt: string;
}

export interface OpportunityFilters {
  search?: string;
  type?: string;
  location?: string;
  compensation?: {
    min?: number;
    max?: number;
  };
  dateRange?: {
    start: string;
    end: string;
  };
}

// Profile types
export interface Profile {
  id: string;
  userId: string;
  bio: string;
  expertise: string[];
  experience: number;
  languages: string[];
  availability: string;
  socialLinks: {
    linkedin?: string;
    twitter?: string;
    website?: string;
  };
  createdAt: string;
  updatedAt: string;
}

// Application types
export interface Application {
  id: string;
  userId: string;
  opportunityId: string;
  status: 'pending' | 'reviewed' | 'accepted' | 'rejected';
  appliedAt: string;
  updatedAt: string;
  opportunity?: Opportunity;
}

// Dashboard types
export interface DashboardStats {
  totalOpportunities: number;
  appliedOpportunities: number;
  upcomingTalks: number;
  xpPoints: number;
}

export interface UpcomingTalk {
  id: string;
  title: string;
  date: string;
  time: string;
  location: string;
  type: 'conference' | 'workshop' | 'meetup' | 'webinar';
  status: 'confirmed' | 'pending' | 'cancelled';
}

// Notification types
export interface Notification {
  id: string;
  userId: string;
  type: 'opportunity' | 'application' | 'system' | 'reminder';
  title: string;
  message: string;
  read: boolean;
  createdAt: string;
}

// Form types
export interface IntakeForm {
  id: string;
  userId: string;
  step: number;
  totalSteps: number;
  data: Record<string, any>;
  completed: boolean;
  submittedAt?: string;
}

// Gamification types
export interface XPActivity {
  id: string;
  userId: string;
  type: 'application' | 'profile_complete' | 'talk_completed' | 'referral';
  points: number;
  description: string;
  createdAt: string;
}

export interface UserLevel {
  level: number;
  xp: number;
  nextLevelXp: number;
  progress: number;
}

// Search types
export interface SearchResult<T = any> {
  item: T;
  score: number;
  highlights?: Record<string, string[]>;
}

export interface SearchOptions {
  query: string;
  filters?: Record<string, any>;
  limit?: number;
  offset?: number;
}

// Subscription types
export interface Subscription {
  id: string;
  userId: string;
  planId: string;
  status: 'active' | 'inactive' | 'cancelled' | 'expired';
  startDate: string;
  endDate?: string;
  autoRenew: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  description: string;
  price: number;
  currency: string;
  interval: 'monthly' | 'yearly';
  features: string[];
  maxOpportunities?: number;
  maxApplications?: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}