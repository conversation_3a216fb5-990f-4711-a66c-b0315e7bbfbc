import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '../ui/card';
import { ResponsiveContainer, BarChart, Bar, LineChart, Line, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, Legend } from 'recharts';
import { CheckCircleOutlined, ClockCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { Badge } from '../ui/badge';
import { ScrollArea } from '../ui/scroll-area';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Star, MessageSquare, ThumbsUp, ThumbsDown } from 'lucide-react';

const FormAutofillAnalytics: React.FC = () => {
  const autofillTrendData = [
    { date: '12-01', attempts: 45, successes: 38, failures: 7 },
    { date: '12-02', attempts: 52, successes: 44, failures: 8 },
    { date: '12-03', attempts: 48, successes: 41, failures: 7 },
    { date: '12-04', attempts: 61, successes: 52, failures: 9 },
    { date: '12-05', attempts: 47, successes: 39, failures: 8 },
    { date: '12-06', attempts: 55, successes: 47, failures: 8 },
    { date: '12-07', attempts: 58, successes: 49, failures: 9 }
  ];

  const errorDistribution = [
    { name: 'Captcha Challenge', value: 28, color: 'hsl(var(--dashboard-dark-blue))' },
    { name: 'JS-Heavy Forms', value: 24, color: 'hsl(var(--dashboard-medium-blue))' },
    { name: 'Network Timeout', value: 18, color: 'hsl(var(--dashboard-light-blue))' },
    { name: '404 Not Found', value: 15, color: 'hsl(var(--muted))' },
    { name: 'Form Validation', value: 10, color: 'hsl(var(--muted-foreground))' },
    { name: 'Rate Limiting', value: 5, color: 'hsl(var(--accent))' }
  ];

  const submissionLogs = [
    { 
      id: 1, 
      timestamp: '2024-12-07 14:32:15', 
      opportunityTitle: 'Tech Conference 2024', 
      status: 'Success', 
      timeToSubmit: '12.4s', 
      errorType: null,
      browser: 'Chrome 119',
      speakerName: 'Sarah Chen'
    },
    { 
      id: 2, 
      timestamp: '2024-12-07 14:28:42', 
      opportunityTitle: 'Marketing Summit', 
      status: 'Failed', 
      timeToSubmit: '8.1s', 
      errorType: 'Captcha Challenge',
      browser: 'Chrome 119',
      speakerName: 'Mike Rodriguez'
    },
    { 
      id: 3, 
      timestamp: '2024-12-07 14:25:33', 
      opportunityTitle: 'Design Workshop Series', 
      status: 'Success', 
      timeToSubmit: '15.7s', 
      errorType: null,
      browser: 'Chrome 118',
      speakerName: 'Emily Johnson'
    },
    { 
      id: 4, 
      timestamp: '2024-12-07 14:22:18', 
      opportunityTitle: 'AI Innovation Conference', 
      status: 'Failed', 
      timeToSubmit: '22.3s', 
      errorType: 'JS-Heavy Forms',
      browser: 'Chrome 119',
      speakerName: 'David Park'
    },
    { 
      id: 5, 
      timestamp: '2024-12-07 14:19:45', 
      opportunityTitle: 'Startup Pitch Event', 
      status: 'Success', 
      timeToSubmit: '9.8s', 
      errorType: null,
      browser: 'Chrome 119',
      speakerName: 'Lisa Wang'
    },
    { 
      id: 6, 
      timestamp: '2024-12-07 14:16:29', 
      opportunityTitle: 'Corporate Training Session', 
      status: 'Failed', 
      timeToSubmit: '5.2s', 
      errorType: '404 Not Found',
      browser: 'Chrome 118',
      speakerName: 'John Smith'
    },
    { 
      id: 7, 
      timestamp: '2024-12-07 14:13:12', 
      opportunityTitle: 'Industry Panel Discussion', 
      status: 'Success', 
      timeToSubmit: '18.1s', 
      errorType: null,
      browser: 'Chrome 119',
      speakerName: 'Ana Garcia'
    },
    { 
      id: 8, 
      timestamp: '2024-12-07 14:10:07', 
      opportunityTitle: 'Webinar Series Launch', 
      status: 'Failed', 
      timeToSubmit: '13.6s', 
      errorType: 'Network Timeout',
      browser: 'Chrome 118',
      speakerName: 'Robert Taylor'
    }
  ];

  const speakerFeedback = [
    {
      rating: 5,
      feedback: "The autofill works flawlessly! Saves me so much time applying to opportunities.",
      speakerName: "Sarah Chen",
      date: "2024-12-07"
    },
    {
      rating: 4,
      feedback: "Really helpful extension, though sometimes struggles with complex forms.",
      speakerName: "Mike Rodriguez", 
      date: "2024-12-06"
    },
    {
      rating: 5,
      feedback: "Game changer for my speaker applications. Love the one-click apply feature!",
      speakerName: "Emily Johnson",
      date: "2024-12-06"
    },
    {
      rating: 3,
      feedback: "Works well most of the time, but had issues with a few JavaScript-heavy forms.",
      speakerName: "David Park",
      date: "2024-12-05"
    },
    {
      rating: 4,
      feedback: "Great tool overall. Would love to see better captcha handling.",
      speakerName: "Lisa Wang",
      date: "2024-12-05"
    }
  ];

  const submissionBreakdownData = [
    {
      speakerName: "Mike Rodriguez",
      opportunityTitle: "Marketing Summit 2024",
      date: "2024-12-07",
      breakdownStep: "Captcha Verification",
      issueCategory: "Anti-bot Protection",
      description: "reCAPTCHA prevented form submission after 3 attempts",
      severity: "High",
      resolved: false,
      userFeedback: "Very frustrating - had to switch to manual submission"
    },
    {
      speakerName: "David Park", 
      opportunityTitle: "AI Innovation Conference",
      date: "2024-12-07",
      breakdownStep: "Dynamic Form Loading",
      issueCategory: "JavaScript Complexity",
      description: "Fields loaded asynchronously, extension couldn't detect them",
      severity: "Medium",
      resolved: true,
      userFeedback: "Form didn't recognize my info initially, but worked on retry"
    },
    {
      speakerName: "Robert Taylor",
      opportunityTitle: "Webinar Series Launch", 
      date: "2024-12-07",
      breakdownStep: "Network Request",
      issueCategory: "Connection Timeout",
      description: "Submission timed out after 30 seconds",
      severity: "Medium", 
      resolved: false,
      userFeedback: "Extension hung on submit button, had to refresh and try again"
    },
    {
      speakerName: "John Smith",
      opportunityTitle: "Corporate Training Session",
      date: "2024-12-07", 
      breakdownStep: "Form URL",
      issueCategory: "Page Not Found",
      description: "Opportunity form URL returned 404 error",
      severity: "High",
      resolved: true,
      userFeedback: "Link was broken, but got redirected to updated form"
    },
    {
      speakerName: "Alex Chen",
      opportunityTitle: "Tech Startup Pitch",
      date: "2024-12-06",
      breakdownStep: "File Upload",
      issueCategory: "Form Validation",
      description: "Resume upload failed validation - wrong file type",
      severity: "Low",
      resolved: true,
      userFeedback: "Had to convert PDF to DOC format manually"
    },
    {
      speakerName: "Maria Santos",
      opportunityTitle: "Design Conference 2024",
      date: "2024-12-06",
      breakdownStep: "Multi-step Form",
      issueCategory: "Navigation",
      description: "Extension lost data when moving between form pages",
      severity: "High",
      resolved: false,
      userFeedback: "Lost all my info on step 2, very annoying"
    }
  ];

  const sourcePerformance = [
    { source: 'Extension', attempts: 158, successRate: 82.3, avgTime: 12.4 },
    { source: 'Bot Automation', attempts: 124, successRate: 76.8, avgTime: 8.7 },
    { source: 'Manual Entry', attempts: 89, successRate: 95.5, avgTime: 45.2 },
    { source: 'API Integration', attempts: 67, successRate: 91.0, avgTime: 3.2 }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-foreground mb-2">Form Autofill/Submission Tracker</h3>
        <p className="text-muted-foreground text-sm mb-6">Monitor automated form submission performance and error tracking</p>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-card border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold text-foreground">84.2%</p>
                <p className="text-xs text-green-500 mt-1">+2.1% from last week</p>
              </div>
              <CheckCircleOutlined className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Submit Time</p>
                <p className="text-2xl font-bold text-foreground">15.7s</p>
                <p className="text-xs text-blue-500 mt-1">-1.3s improvement</p>
              </div>
              <ClockCircleOutlined className="h-8 w-8 text-dashboard-medium-blue" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Attempts</p>
                <p className="text-2xl font-bold text-foreground">366</p>
                <p className="text-xs text-muted-foreground mt-1">Last 7 days</p>
              </div>
              <ExclamationCircleOutlined className="h-8 w-8 text-dashboard-light-blue" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-foreground">Daily Autofill Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={autofillTrendData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="attempts" fill="hsl(var(--dashboard-light-blue))" name="Attempts" />
                <Bar dataKey="successes" fill="hsl(var(--dashboard-dark-blue))" name="Successes" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-foreground">Error Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={errorDistribution}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="value"
                  label={({ name, value }) => `${name}: ${value}`}
                >
                  {errorDistribution.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="bg-card border-border lg:col-span-2">
          <CardHeader>
            <CardTitle className="text-foreground">Performance by Source</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={sourcePerformance}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="source" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="successRate" fill="hsl(var(--dashboard-dark-blue))" name="Success Rate %" />
                <Bar dataKey="avgTime" fill="hsl(var(--dashboard-medium-blue))" name="Avg Time (s)" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Submission Logs Table */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="text-foreground">Recent Form Submissions</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Timestamp</TableHead>
                  <TableHead>Speaker</TableHead>
                  <TableHead>Opportunity</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Time to Submit</TableHead>
                  <TableHead>Error Type</TableHead>
                  <TableHead>Browser</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {submissionLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell className="font-mono text-sm">{log.timestamp}</TableCell>
                    <TableCell className="font-medium">{log.speakerName}</TableCell>
                    <TableCell className="max-w-[200px] truncate">{log.opportunityTitle}</TableCell>
                    <TableCell>
                      <Badge 
                        variant={log.status === 'Success' ? 'default' : 'destructive'}
                        className={log.status === 'Success' 
                          ? 'bg-green-100 text-green-800 hover:bg-green-100' 
                          : 'bg-red-100 text-red-800 hover:bg-red-100'
                        }
                      >
                        {log.status}
                      </Badge>
                    </TableCell>
                    <TableCell className="font-mono text-sm">{log.timeToSubmit}</TableCell>
                    <TableCell>
                      {log.errorType ? (
                        <Badge variant="outline" className="text-muted-foreground">
                          {log.errorType}
                        </Badge>
                      ) : (
                        <span className="text-muted-foreground">-</span>
                      )}
                    </TableCell>
                    <TableCell className="text-muted-foreground text-sm">{log.browser}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Speaker Feedback Section */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="text-foreground flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Speaker Feedback on Submission UX
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Feedback Stats */}
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 bg-muted/20 rounded-lg">
                <div className="flex items-center gap-2">
                  <ThumbsUp className="h-5 w-5 text-green-500" />
                  <span className="font-medium">Positive Feedback</span>
                </div>
                <span className="text-2xl font-bold text-foreground">87%</span>
              </div>
              <div className="flex items-center justify-between p-4 bg-muted/20 rounded-lg">
                <div className="flex items-center gap-2">
                  <ThumbsDown className="h-5 w-5 text-red-500" />
                  <span className="font-medium">Issues Reported</span>
                </div>
                <span className="text-2xl font-bold text-foreground">13%</span>
              </div>
              <div className="flex items-center justify-between p-4 bg-muted/20 rounded-lg">
                <div className="flex items-center gap-2">
                  <Star className="h-5 w-5 text-yellow-500" />
                  <span className="font-medium">Average Rating</span>
                </div>
                <span className="text-2xl font-bold text-foreground">4.2/5</span>
              </div>
            </div>

            {/* Recent Feedback */}
            <div className="space-y-4">
              <h4 className="font-semibold text-foreground">Recent Feedback</h4>
              <ScrollArea className="h-[280px]">
                <div className="space-y-4">
                  {speakerFeedback.map((feedback, index) => (
                    <div key={index} className="p-4 border border-border rounded-lg bg-muted/10">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <span className="font-medium text-sm">{feedback.speakerName}</span>
                          <div className="flex">
                            {Array.from({ length: feedback.rating }).map((_, i) => (
                              <Star key={i} className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                            ))}
                            {Array.from({ length: 5 - feedback.rating }).map((_, i) => (
                              <Star key={i + feedback.rating} className="h-3 w-3 text-muted-foreground" />
                            ))}
                          </div>
                        </div>
                        <span className="text-xs text-muted-foreground">{feedback.date}</span>
                      </div>
                      <p className="text-sm text-muted-foreground">{feedback.feedback}</p>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submission Breakdown Analysis */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="text-foreground flex items-center gap-2">
            <ExclamationCircleOutlined className="h-5 w-5 text-red-500" />
            Submission Experience Breakdown Points
          </CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            Detailed analysis of where speakers encountered issues during submission
          </p>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[500px]">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Speaker</TableHead>
                  <TableHead>Opportunity</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Breakdown Step</TableHead>
                  <TableHead>Issue Category</TableHead>
                  <TableHead>Severity</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>User Feedback</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {submissionBreakdownData.map((breakdown, index) => (
                  <TableRow key={index}>
                    <TableCell className="font-medium">{breakdown.speakerName}</TableCell>
                    <TableCell className="max-w-[180px] truncate" title={breakdown.opportunityTitle}>
                      {breakdown.opportunityTitle}
                    </TableCell>
                    <TableCell className="text-sm text-muted-foreground">{breakdown.date}</TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-xs">
                        {breakdown.breakdownStep}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant="secondary"
                        className={
                          breakdown.issueCategory === 'Anti-bot Protection' ? 'bg-red-100 text-red-800' :
                          breakdown.issueCategory === 'JavaScript Complexity' ? 'bg-yellow-100 text-yellow-800' :
                          breakdown.issueCategory === 'Connection Timeout' ? 'bg-blue-100 text-blue-800' :
                          breakdown.issueCategory === 'Page Not Found' ? 'bg-purple-100 text-purple-800' :
                          'bg-gray-100 text-gray-800'
                        }
                      >
                        {breakdown.issueCategory}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={
                          breakdown.severity === 'High' ? 'destructive' :
                          breakdown.severity === 'Medium' ? 'secondary' : 'outline'
                        }
                      >
                        {breakdown.severity}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant={breakdown.resolved ? 'default' : 'destructive'}
                        className={breakdown.resolved 
                          ? 'bg-green-100 text-green-800 hover:bg-green-100' 
                          : 'bg-red-100 text-red-800 hover:bg-red-100'
                        }
                      >
                        {breakdown.resolved ? 'Resolved' : 'Open'}
                      </Badge>
                    </TableCell>
                    <TableCell className="max-w-[250px]">
                      <div className="text-sm text-muted-foreground" title={breakdown.userFeedback}>
                        {breakdown.userFeedback.length > 60 
                          ? `${breakdown.userFeedback.substring(0, 60)}...` 
                          : breakdown.userFeedback
                        }
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};

export default FormAutofillAnalytics;