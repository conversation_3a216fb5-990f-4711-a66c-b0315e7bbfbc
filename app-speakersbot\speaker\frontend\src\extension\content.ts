// Chrome Extension Content Script
// Handles page interaction, autofill functionality, and opportunity detection

console.log('SpeakerBot Extension Content Script Loaded');

// Initialize content script
let isExtensionActive = false;
let speakerProfile: any = null;

// Load speaker profile data
async function loadSpeakerProfile() {
  try {
    const response = await chrome.runtime.sendMessage({
      type: 'GET_STORAGE',
      key: 'speakerProfile'
    });
    speakerProfile = response?.data || null;
    console.log('Speaker profile loaded:', !!speakerProfile);
  } catch (error) {
    console.error('Failed to load speaker profile:', error);
  }
}

// Auto-fill form fields based on speaker profile
function autoFillForm() {
  if (!speakerProfile) {
    console.log('No speaker profile available for autofill');
    return;
  }

  console.log('Starting autofill process...');
  
  // Common form field selectors and their corresponding profile fields
  const fieldMappings = [
    // Name fields
    { selectors: ['input[name*="name"]', 'input[id*="name"]', '#name', '.name'], value: speakerProfile.name },
    { selectors: ['input[name*="first"]', 'input[id*="first"]', '#firstname'], value: speakerProfile.firstName },
    { selectors: ['input[name*="last"]', 'input[id*="last"]', '#lastname'], value: speakerProfile.lastName },
    
    // Email fields
    { selectors: ['input[type="email"]', 'input[name*="email"]', '#email'], value: speakerProfile.email },
    
    // Bio/Description fields
    { selectors: ['textarea[name*="bio"]', 'textarea[name*="description"]', '#bio', '#description'], value: speakerProfile.bio },
    
    // Location fields
    { selectors: ['input[name*="city"]', '#city'], value: speakerProfile.city },
    { selectors: ['input[name*="country"]', '#country'], value: speakerProfile.country },
    
    // Professional fields
    { selectors: ['input[name*="company"]', '#company'], value: speakerProfile.company },
    { selectors: ['input[name*="title"]', 'input[name*="jobtitle"]', '#title'], value: speakerProfile.jobTitle },
    
    // Social media
    { selectors: ['input[name*="linkedin"]', '#linkedin'], value: speakerProfile.linkedin },
    { selectors: ['input[name*="twitter"]', '#twitter'], value: speakerProfile.twitter },
    
    // Phone
    { selectors: ['input[type="tel"]', 'input[name*="phone"]', '#phone'], value: speakerProfile.phone },
    
    // State/Province  
    { selectors: ['input[name*="state"]', 'input[name*="province"]', '#state', '#province'], value: speakerProfile.state },
    
    // Website
    { selectors: ['input[name*="website"]', 'input[name*="url"]', '#website'], value: speakerProfile.website },
  ];

  let filledFields = 0;

  fieldMappings.forEach(mapping => {
    if (!mapping.value) return;

    mapping.selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach((element: any) => {
        if (element && !element.value && element.offsetParent !== null) {
          // Check if element is visible and not already filled
          element.value = mapping.value;
          
          // Trigger change events
          element.dispatchEvent(new Event('input', { bubbles: true }));
          element.dispatchEvent(new Event('change', { bubbles: true }));
          
          // Add visual indicator
          element.style.backgroundColor = 'rgba(32, 196, 244, 0.1)';
          element.style.border = '1px solid rgba(32, 196, 244, 0.3)';
          
          filledFields++;
          console.log(`Filled field: ${selector} with: ${mapping.value}`);
        }
      });
    });
  });

  if (filledFields > 0) {
    showAutofillNotification(filledFields);
  }
}

// Show autofill notification
function showAutofillNotification(fieldsCount: number) {
  // Create notification element
  const notification = document.createElement('div');
  notification.innerHTML = `
    <div style="
      position: fixed;
      top: 20px;
      right: 20px;
      background: #06214B;
      color: #FFFFFF;
      padding: 12px 16px;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      z-index: 10000;
      font-family: system-ui, sans-serif;
      font-size: 14px;
      border: 1px solid #39C9E3;
    ">
      <div style="display: flex; align-items: center; gap: 8px;">
        <div style="width: 20px; height: 20px; background: #39C9E3; border-radius: 4px; display: flex; align-items: center; justify-content: center;">
          <span style="color: #06214B; font-weight: bold; font-size: 12px;">SB</span>
        </div>
        <span>Auto-filled ${fieldsCount} fields from your profile</span>
      </div>
    </div>
  `;
  
  document.body.appendChild(notification);
  
  // Remove notification after 3 seconds
  setTimeout(() => {
    if (notification.parentNode) {
      notification.parentNode.removeChild(notification);
    }
  }, 3000);
}

// Detect opportunity pages and show suggestion
function detectOpportunityPage() {
  const url = window.location.href;
  const title = document.title;
  const bodyText = document.body.textContent?.toLowerCase() || '';
  
  const opportunityKeywords = [
    'call for speakers',
    'call for papers',
    'cfp',
    'speaker application',
    'speaker submission',
    'conference submission',
    'abstract submission',
    'proposal submission'
  ];
  
  const isOpportunityPage = opportunityKeywords.some(keyword => 
    title.toLowerCase().includes(keyword) || 
    bodyText.includes(keyword) ||
    url.toLowerCase().includes(keyword.replace(/\s+/g, ''))
  );
  
  if (isOpportunityPage) {
    console.log('Opportunity page detected');
    showAutofillSuggestion();
  }
}

// Show autofill suggestion button
function showAutofillSuggestion() {
  // Check if button already exists
  if (document.getElementById('speakerbot-autofill-btn')) return;
  
  const button = document.createElement('div');
  button.id = 'speakerbot-autofill-btn';
  button.innerHTML = `
    <button style="
      position: fixed;
      bottom: 20px;
      right: 20px;
      background: linear-gradient(135deg, #39C9E3, #20C4F4);
      color: #06214B;
      padding: 12px 20px;
      border: none;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(57, 201, 227, 0.3);
      z-index: 10000;
      font-family: system-ui, sans-serif;
      font-size: 14px;
      font-weight: 600;
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: all 0.2s ease;
    " onmouseover="this.style.transform='scale(1.05)'" onmouseout="this.style.transform='scale(1)'">
      <div style="width: 20px; height: 20px; background: rgba(6, 33, 75, 0.2); border-radius: 4px; display: flex; align-items: center; justify-content: center;">
        <span style="color: #06214B; font-weight: bold; font-size: 10px;">SB</span>
      </div>
      <span>Auto-fill with SpeakerBot</span>
    </button>
  `;
  
  const buttonElement = button.querySelector('button');
  if (buttonElement) {
    buttonElement.addEventListener('click', () => {
      autoFillForm();
      // Hide button after use
      button.style.display = 'none';
    });
  }
  
  document.body.appendChild(button);
}

// Message handler
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('Content script received message:', message);
  
  switch (message.type) {
    case 'AUTOFILL_FORM':
      autoFillForm();
      sendResponse({ success: true });
      break;
      
    case 'OPPORTUNITY_PAGE_DETECTED':
      detectOpportunityPage();
      sendResponse({ success: true });
      break;
      
    case 'GET_PAGE_INFO':
      sendResponse({
        url: window.location.href,
        title: document.title,
        hasForm: document.forms.length > 0
      });
      break;
      
    default:
      console.log('Unknown message type:', message.type);
  }
});

// Initialize content script
async function init() {
  await loadSpeakerProfile();
  
  // Detect opportunity pages on load
  setTimeout(detectOpportunityPage, 1000);
  
  // Watch for form changes
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      if (mutation.type === 'childList') {
        // Check if new forms were added
        const addedNodes = Array.from(mutation.addedNodes);
        const hasNewForm = addedNodes.some(node => 
          node.nodeType === 1 && 
          (node as Element).querySelector('form')
        );
        
        if (hasNewForm) {
          setTimeout(detectOpportunityPage, 500);
        }
      }
    });
  });
  
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
  
  console.log('SpeakerBot content script initialized');
}

// Start initialization when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', init);
} else {
  init();
}