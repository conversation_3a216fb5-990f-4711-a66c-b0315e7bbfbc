"""
Centralized Database Connection Manager

This module provides a singleton database connection manager to prevent
connection pool exhaustion and ensure proper resource management across
all services and workers.

Key Features:
- Singleton pattern to ensure single engine instance
- Proper connection pooling configuration
- Automatic connection health checks
- Graceful connection cleanup
- Production-ready error handling

Author: Speaker Bot Team
Version: 1.0 (Production Fix)
Last Updated: 2024
"""

import logging
from typing import Optional
from contextlib import contextmanager
from sqlalchemy import create_engine, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from sqlalchemy.exc import DisconnectionError, OperationalError

from app.config.config import config

logger = logging.getLogger(__name__)

class DatabaseManager:
    """
    Singleton database connection manager for the entire application.
    
    This class ensures that only one database engine is created and shared
    across all services and workers, preventing connection pool exhaustion.
    """
    
    _instance: Optional['DatabaseManager'] = None
    _engine = None
    _SessionLocal = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(DatabaseManager, cls).__new__(cls)
            cls._instance._initialize()
        return cls._instance
    
    def _initialize(self):
        """Initialize the database engine with optimized pooling settings."""
        if self._engine is None:
            try:
                # Production-optimized connection pooling for RDS
                self._engine = create_engine(
                    config.get_database_url(),
                    # Conservative pool settings for RDS (reduced to prevent 439 error)
                    pool_size=5,            # Base pool size (reduced from 10)
                    max_overflow=10,        # Additional connections (reduced from 20)
                    pool_pre_ping=True,     # Verify connections before use
                    pool_recycle=1800,      # Recycle connections every 30 minutes
                    pool_timeout=30,        # Timeout for getting connection from pool
                    pool_reset_on_return='commit',  # Reset connections on return
                    echo=False,             # Set to True for SQL debugging
                    # Additional MySQL-specific optimizations
                    connect_args={
                        'charset': 'utf8mb4',
                        'autocommit': False,
                        'connect_timeout': 10,
                        'read_timeout': 30,
                        'write_timeout': 30,
                    }
                )
                
                # Create session factory
                self._SessionLocal = sessionmaker(
                    bind=self._engine,
                    autocommit=False,
                    autoflush=False,
                    expire_on_commit=False
                )
                
                # Add connection health check event listener
                @event.listens_for(self._engine, "checkout")
                def receive_checkout(dbapi_connection, connection_record, connection_proxy):
                    """Log connection checkout for monitoring."""
                    logger.debug("Database connection checked out")
                
                @event.listens_for(self._engine, "checkin")
                def receive_checkin(dbapi_connection, connection_record):
                    """Log connection checkin for monitoring."""
                    logger.debug("Database connection checked in")
                
                logger.info("DatabaseManager initialized with RDS-optimized connection pooling")
                logger.info(f"Pool size: 5, Max overflow: 10, Recycle: 1800s (Total max: 15 connections)")
                
            except Exception as e:
                logger.error(f"Failed to initialize database engine: {e}")
                raise
    
    @property
    def engine(self):
        """Get the database engine."""
        if self._engine is None:
            self._initialize()
        return self._engine
    
    @property
    def SessionLocal(self):
        """Get the session factory."""
        if self._SessionLocal is None:
            self._initialize()
        return self._SessionLocal
    
    @contextmanager
    def get_session(self):
        """
        Context manager for database sessions with automatic cleanup.
        
        Usage:
            with db_manager.get_session() as session:
                # Use session here
                pass
        """
        session = self.SessionLocal()
        try:
            yield session
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def get_session_direct(self) -> Session:
        """
        Get a database session directly (caller must close it).
        
        Returns:
            SQLAlchemy Session object
            
        Note:
            Caller is responsible for calling session.close()
        """
        return self.SessionLocal()
    
    def test_connection(self) -> bool:
        """
        Test database connectivity.
        
        Returns:
            True if connection is healthy, False otherwise
        """
        try:
            with self.get_session() as session:
                from sqlalchemy import text
                session.execute(text("SELECT 1"))
                logger.debug("Database connection test successful")
                return True
        except Exception as e:
            logger.error(f"Database connection test failed: {e}")
            return False
    
    def get_pool_status(self) -> dict:
        """
        Get current connection pool status.
        
        Returns:
            Dictionary with pool statistics
        """
        try:
            pool = self._engine.pool
            return {
                "pool_size": pool.size(),
                "checked_in": pool.checkedin(),
                "checked_out": pool.checkedout(),
                "overflow": pool.overflow(),
                "total_connections": pool.size() + pool.overflow()
            }
        except Exception as e:
            logger.error(f"Failed to get pool status: {e}")
            return {"error": str(e)}
    
    def dispose(self):
        """Dispose of the database engine and close all connections."""
        try:
            if self._engine:
                self._engine.dispose()
                logger.info("Database engine disposed and all connections closed")
        except Exception as e:
            logger.error(f"Error disposing database engine: {e}")

# Global singleton instance
db_manager = DatabaseManager()

# Convenience functions for backward compatibility
def get_db_session():
    """Get a database session context manager."""
    return db_manager.get_session()