import React from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useAuth } from "../state/AuthContext";
import { usePermissions } from "../hooks/usePermissions";
import { Permission } from "../store/slices/rbacSlice";
import { Alert, AlertDescription } from "./ui/alert";
import { AlertTriangle, Lock } from "lucide-react";
import AppLayout from "./Layout/AppLayout";
import SplashScreen from "./common/SplashScreen";

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: Permission[];
  requireAll?: boolean; // If true, user must have ALL permissions. If false, user needs ANY permission
  fallbackPath?: string;
  showAccessDenied?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requiredPermissions = [],
  requireAll = false,
  fallbackPath = "/admin/dashboard",
  showAccessDenied = true,
}) => {
  const { isAuthenticated } = useAuth();
  const { hasPermission, hasAnyPermission, hasAllPermissions, isLoading } =
    usePermissions();
  const location = useLocation();

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/admin/login" state={{ from: location }} replace />;
  }

  // If permissions may still be loading (e.g., after refresh/login), show splash
  if (isLoading) {
    return (
      <AppLayout>
        <SplashScreen />
      </AppLayout>
    );
  }

  // Check permissions if required
  if (requiredPermissions.length > 0) {
    const hasRequiredPermissions = requireAll
      ? hasAllPermissions(requiredPermissions)
      : hasAnyPermission(requiredPermissions);

    if (!hasRequiredPermissions) {
      // Always mount AppLayout so it can fetch auth user/permissions
      return (
        <AppLayout>
          {showAccessDenied ? (
            <div className="flex items-center justify-center min-h-[calc(100dvh-165px)] p-4">
              <div className="max-w-md w-full">
                <Alert className="border-red-200 bg-red-50">
                  <Lock className="h-4 w-4 text-red-600" />
                  <AlertDescription className="text-red-800">
                    <div className="font-semibold mb-2">Access Denied</div>
                    <div className="text-sm">
                      You don't have the required permissions to access this
                      page.
                    </div>
                  </AlertDescription>
                </Alert>
              </div>
            </div>
          ) : (
            <Navigate to={fallbackPath} replace />
          )}
        </AppLayout>
      );
    }
  }

  // Success: mount the layout and the page
  return <AppLayout>{children}</AppLayout>;
};

export default ProtectedRoute;
