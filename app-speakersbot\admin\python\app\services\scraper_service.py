"""
Speaker Opportunity Scraper Service - Main Orchestrator

This is the main entry point for the speaker opportunity scraper service.
It orchestrates all the modular components to find, extract, and process
speaking opportunities from various sources.

Key Features:
- Multi-provider search (Tavily, Brave, SerpAPI, Exa, Firecrawl)
- Advanced content extraction and validation
- Database integration with error handling
- Graceful signal handling for clean shutdowns
- Concurrent processing with rate limiting

Architecture:
- Modular design with specialized components
- Async processing for better performance
- Comprehensive error handling and logging
- Configurable search parameters

Author: Speaker Bot Team
Version: 2.0 (Optimized)
Last Updated: 2024
"""

import sys
import signal
import uuid
from datetime import datetime, date
from typing import Dict, Optional

# =============================================================================
# MODULE IMPORTS
# =============================================================================

# Core scraper modules
from app.helpers.page_loader import load_page
from app.services.llm_extractor import extract_with_llm

# Utility functions
from app.helpers.utils import (
    normalize_text, org_from_domain, 
    is_job_related, is_event_related
)

# Data extraction modules
from app.helpers.content_extractor import (
    extract_form_fields
)

# =============================================================================
# LOGGING SETUP
# =============================================================================

# Setup logging with fallback for different import paths
try:
    from app.config.logger import get_logger
    logger = get_logger(__name__, file_name="scraper.log")
except Exception:
    from config.logger import get_logger
    logger = get_logger(__name__, file_name="scraper.log")

# =============================================================================
# GLOBAL STATE MANAGEMENT
# =============================================================================

# Global variables for signal handling and state tracking
current_session = None  # Database session for signal handling
running_topics = set()  # Set of currently running topics


# =============================================================================
# SIGNAL HANDLING
# =============================================================================

def signal_handler(signum, frame):
    """
    Handle interrupt signals (Ctrl+C, SIGTERM) to gracefully update database status.
    
    This function ensures that when the scraper is interrupted, all running
    topics are marked as ERROR in the database rather than remaining in RUNNING state.
    
    Args:
        signum (int): Signal number received
        frame: Current stack frame (unused)
    """
    logger.warning(f"Received signal {signum}. Updating running topics to ERROR status...")
    
    if current_session and running_topics:
        try:
            from app.models.scraping import ScrapingLogging, ScrapingStatus
            from sqlalchemy import update
            
            for topic in running_topics:
                # Update running status to ERROR
                stmt = update(ScrapingLogging).where(
                    ScrapingLogging.topic == topic,
                    ScrapingLogging.status == ScrapingStatus.RUNNING
                ).values(
                    status=ScrapingStatus.ERROR,
                    reason=f"Process interrupted by signal {signum}",
                    ended_at=datetime.now(),
                    updated_at=datetime.now()
                )
                current_session.execute(stmt)
                logger.info(f"Updated topic '{topic}' status to ERROR due to interruption")
            
            current_session.commit()
            logger.info("Successfully updated all running topics to ERROR status")
        except Exception as e:
            logger.error(f"Error updating status during interruption: {e}")
        finally:
            if current_session:
                current_session.close()
    
    logger.info("Exiting due to interruption...")
    sys.exit(130)  # Standard exit code for SIGINT (Ctrl+C)

def extract_source_url(hit: Dict, search_query: str = None) -> str:
    """
    Extract or generate a source URL from search hit data.
    
    This function attempts to determine the source URL where the event was listed.
    For search results, it tries to construct a reasonable source URL based on
    the search query and hit metadata.
    
    Args:
        hit (Dict): Search result hit data
        search_query (str, optional): The search query that generated this hit
        
    Returns:
        str: Source URL or fallback to event URL
    """
    # Try to get source URL from hit metadata
    source_url = hit.get("source_url") or hit.get("referrer") or hit.get("source")
    
    if source_url:
        return source_url
    
    # For search results, try to construct a source URL based on search query
    if search_query:
        # Extract domain from search query if it contains site: operator
        if "site:" in search_query:
            site_part = search_query.split("site:")[-1].split()[0]
            if "." in site_part:
                return f"https://{site_part}"
        
        # For conference listing sites, try to construct a reasonable source URL
        event_url = hit.get("url", "")
        if event_url:
            from urllib.parse import urlparse
            parsed = urlparse(event_url)
            domain = parsed.netloc
            
            # Common conference listing patterns
            if "eventbrite" in domain:
                return f"https://{domain}/events"
            elif "meetup" in domain:
                return f"https://{domain}/events"
            elif "conference" in domain or "events" in domain:
                return f"https://{domain}"
            elif "callforpapers" in domain or "cfp" in domain:
                return f"https://{domain}"
    
    # Fallback to event URL if no source URL can be determined
    return hit.get("url", "")

async def build_row(topic: str, hit: Dict, source_url: str = None, html: str = None, soup=None, page_text: str = None, opportunity_url_id: int = None) -> Optional[Dict]:
    """
    Build a structured opportunity row from search hit data using LLM extraction.
    
    This function processes a search result hit and extracts all relevant information
    using LLM-based extraction for comprehensive data extraction.
    
    Args:
        topic (str): Search topic that generated this hit
        hit (Dict): Search result data containing url, title, content, etc.
        source_url (str, optional): The URL of the page where this event was listed/found
        html (str, optional): Pre-loaded HTML content
        soup (optional): Pre-loaded BeautifulSoup object
        page_text (str, optional): Pre-loaded page text content
        
    Returns:
        Optional[Dict]: Structured opportunity data or None if filtered out
    """
    # Extract and normalize basic data from search hit
    title = normalize_text(hit.get("title"))
    url = normalize_text(hit.get("url"))
    id_val = str(uuid.uuid4())
    
    # Extract source URL
    if source_url is None:
        source_url = extract_source_url(hit, topic)
    
    # Early filtering based on title and snippet
    if is_job_related(title, page_text):
        return None
    
    if not is_event_related(title, page_text):
        return None
    
    # Use pre-loaded page data if available, otherwise load it
    if html is None or soup is None or page_text is None:
        html, soup, page_text, is_pdf = await load_page(url, timeout=8.0, retry_count=0)
    
    # Final filtering after loading full page content
    if is_job_related(title, page_text):
        return None
    
    if not is_event_related(title, page_text):
        return None
    
    # Extract title from page if not available from search hit
    page_title = title
    if soup:
        try:
            t = soup.find('title')
            if t:
                page_title = t.get_text(" ", strip=True)
        except Exception:
            pass
    
    # Use LLM extraction for all fields
    logger.info("Starting LLM extraction...")
    llm_result = await extract_with_llm(url, page_title, page_text[:60000], soup)
    logger.info("LLM extraction completed")
    
    # Initialize default values
    event_start = None
    event_end = None
    applied_by = None
    city = state = country = venue = ""
    is_virtual = None
    industry = ""
    description = ""
    
    # Process LLM results
    if llm_result:
        from dateutil import parser as date_parser
        try:
            # Extract dates
            if llm_result.get('start_date'):
                event_start = date_parser.parse(llm_result['start_date']).date()
            if llm_result.get('end_date'):
                event_end = date_parser.parse(llm_result['end_date']).date()
            
            # Extract application deadline
            applied_by = None
            if llm_result.get('applied_by'):
                applied_by = date_parser.parse(llm_result['applied_by']).date()
            
            # Extract other fields
            city = llm_result.get('city', '') or ""
            state = llm_result.get('state', '') or ""
            country = llm_result.get('country', '') or ""
            venue = llm_result.get('venue', '') or ""
            is_virtual = llm_result.get('is_virtual')
            industry = llm_result.get('industry', '') or ""
            description = llm_result.get('description', '') or ""
            contact_email = llm_result.get('contact_email', '') or ""
            event_type = llm_result.get('event_type', '') or ""
            
            # Use LLM title if available and better than search title
            if llm_result.get('title') and len(llm_result['title'].strip()) > len(title.strip()):
                title = llm_result['title']
                
        except Exception as e:
            logger.warning(f'LLM result processing failed: {e}')
    
    # Check if this is a call for speakers
    is_call_for_speaker = None
    is_closed_for_speaker = None
    if llm_result:
        is_call_for_speaker = llm_result.get('is_call_for_speaker')
        is_closed_for_speaker = llm_result.get('is_closed_for_speaker')
        logger.info(f"LLM determined is_call_for_speaker: {is_call_for_speaker}, is_closed_for_speaker: {is_closed_for_speaker} for URL: {url}")
    
    # If is_call_for_speaker is explicitly False, don't process this opportunity
    if is_call_for_speaker is False:
        logger.info(f"Skipping URL {url} - not a call for speakers (is_call_for_speaker=False)")
        return {"skip_reason": "not_call_for_speaker", "is_call_for_speaker": False}
    
    # If is_closed_for_speaker is explicitly True, don't process this opportunity
    if is_closed_for_speaker is True:
        logger.info(f"Skipping URL {url} - speaker submissions are closed (is_closed_for_speaker=True)")
        return {"skip_reason": "closed_for_speaker", "is_closed_for_speaker": True}
    
    # Check for multiple events - if detected, skip and update status
    has_multiple_events = False
    if llm_result:
        has_multiple_events = llm_result.get('has_multiple_events', False)
        if has_multiple_events:
            logger.info(f"Skipping URL {url} - page contains multiple events (listing page, not a single event)")
            return {"skip_reason": "multiple_events", "has_multiple_events": True}
    
    # Extract form fields and determine submission type
    form_data = None
    submission_type = None
    
    if soup:
        form_data = extract_form_fields(soup, url)
        if form_data and form_data.get('fields'):
            # Form found - set submission type to "form"
            submission_type = "form"
            logger.info(f"Found form with {len(form_data['fields'])} fields for URL: {url}")
        else:
            # No form found - set submission type to "email" if contact_email exists
            if contact_email:
                submission_type = "email"
                logger.info(f"No form found, using email submission: {contact_email} for URL: {url}")
            else:
                submission_type = None
                logger.info(f"No form or email found for URL: {url}")
    
    
    # Determine tag based on completeness
    tag = None
    if not event_start and not event_end:
        tag = "incomplete"
    
    # Filtering logic
    from datetime import date
    today = date.today()
    
    should_include = False
    if event_start and event_start > today:
        should_include = True
    elif event_end and event_end > today:
        should_include = True
    elif not event_start and not event_end:
        should_include = True
    
    if not should_include:
        return None
    
    from datetime import datetime
    
    row = {
        "id": id_val,
        "opportunity_url_id": opportunity_url_id,
        "title": title,
        "organization": org_from_domain(url),
        "event_type": event_type,
        "start_date": event_start,
        "end_date": event_end,
        "applied_by": applied_by,
        "event_url": url,
        "source_url": source_url or url,
        "city": city,
        "state": state,
        "country": country,
        "venue": venue,
        "is_virtual": is_virtual,
        "description": description,
        "industry": industry,
        "subcategory": topic,
        "search_query": topic,
        "source": hit.get("provider", "unknown"),
        "tag": tag,
        "is_active": True,
        "created_at": datetime.utcnow(),
        "updated_at": datetime.utcnow(),
        "deleted_at": None,
        "form_data": form_data,
        "contact_email": contact_email,
        "submission_type": submission_type
    }
    return row
async def process_single_url(url_record, hit):
    """
    Process a single URL and extract opportunity data.
    
    Args:
        url_record: OpportunitiesUrl database record
        hit: Mock hit object for build_row function
        
    Returns:
        Optional[Dict]: Extracted opportunity data or None
    """
    try:
        logger.info(f"Processing URL {url_record.id}: {url_record.url}")
        
        # Load page content
        html, soup, page_text, is_pdf = await load_page(url_record.url, timeout=8.0, retry_count=url_record.retry_count)
        
        # Extract title from page if not available
        if soup:
            title_tag = soup.find('title')
            if title_tag:
                hit['title'] = title_tag.get_text().strip()
            else:
                # Try to get title from h1 or other heading tags
                h1_tag = soup.find('h1')
                if h1_tag:
                    hit['title'] = h1_tag.get_text().strip()
        
        # Extract content/snippet from page
        if page_text:
            hit['content'] = page_text[:300000]  # First 500 chars as snippet
        
        # Use the build_row function with pre-loaded page data
        opportunity_data = await build_row(url_record.topic or 'general', hit, url_record.url, html, soup, page_text, url_record.id)
        
        if opportunity_data:
            logger.info(f"Successfully extracted data for URL {url_record.id}")
            return opportunity_data
        else:
            logger.warning(f"No opportunity data extracted for URL {url_record.id}")
            return None
            
    except Exception as e:
        logger.error(f"Error processing URL {url_record.id}: {e}")
        return None
