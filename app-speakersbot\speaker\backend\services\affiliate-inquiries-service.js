const { where } = require("sequelize");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const { AffiliateUsersDetails, AffiliateInquiries, Users, AffiliateInquiriesHistory, GamificationHistory, Speakers, sequelize, GamificationRules } = require("../models");
const { parsePagination, getPagingData } = require("../helpers/app-hepler");
const emailService = require("./email-service");
const CONFIG = require("../config/config");
const affiliateInquiriesService = {};

// ------------------------- affiliate-inquiries-service -------------------------



/**
 * Create a new affiliate inquiry.
 * Links a speaker with an affiliate for potential collaboration.
 * 
 * @param {Object} req - Request object
 * @param {number} req.userId - Speaker ID from authenticated user
 * @param {Object} req.body - Request body
 * @param {number} req.body.affiliate_id - Affiliate ID to inquire about
 * @param {string} req.body.speaker_note - Speaker's note/message
 * @returns {Promise<Object>} Success message
 * @throws {CustomError} When affiliate not found or creation fails
 */
affiliateInquiriesService.createAffiliateInquiry = async (req) => {
    try {
        const id = req.userId;

        const speakerXp = await Speakers.findOne({ where: { id: id }, attributes: ['xp_points'], raw: true });

        const requireXp = await GamificationRules.findOne({
            where: { key: "affiliate_meeting" },
            raw: true
        });
        console.log(requireXp)
        console.log(speakerXp.xp_points < requireXp.points)

        if (speakerXp.xp_points < Math.abs(requireXp.points)) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, `You need at least ${Math.abs(requireXp.points)} XP points to create an affiliate inquiry.`);
        }

        // await Speakers.update({ xp_points: sequelize.literal(speakerXp.xp_points - Math.abs(requireXp.points)) }, { where: { id: id } });


        await Speakers.decrement('xp_points', {
            by: Math.abs(requireXp.points),
            where: { id }
        });



        const { affiliate_id, note, inquiry_date, inquiry_time } = req.body;

        // Helper function to convert 12-hour to 24-hour format
        const convertTo24Hour = (timeStr) => {
            if (!timeStr) return null;

            // If already in 24-hour format (no AM/PM), return as is
            if (!timeStr.toLowerCase().includes('am') && !timeStr.toLowerCase().includes('pm')) {
                return timeStr;
            }

            // Handle 12-hour format
            const [time, period] = timeStr.split(/\s+/);
            let [hours, minutes] = time.split(':');
            hours = parseInt(hours);

            if (period.toLowerCase() === 'pm' && hours !== 12) {
                hours += 12;
            } else if (period.toLowerCase() === 'am' && hours === 12) {
                hours = 0;
            }

            return `${hours.toString().padStart(2, '0')}:${minutes || '00'}`;
        };

        // Convert inquiry_time to 24-hour format
        const formattedInquiryTime = convertTo24Hour(inquiry_time);

        // find affiliate by affiliate_id
        const affiliate = await AffiliateUsersDetails.findOne({ where: { affiliate_id: affiliate_id } });

        if (!affiliate) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Affiliate not found");
        }

        // create affiliate inquiry
        const affiliateInquiry = await AffiliateInquiries.create({
            speaker_id: id,
            affiliate_id: affiliate_id,
            amount: requireXp.points,

        });

        const affiliateInquiryHistory = await AffiliateInquiriesHistory.create({
            inquiry_id: affiliateInquiry.id,
            status: 'pending',
            note,
            inquiry_date: inquiry_date,
            inquiry_time: formattedInquiryTime,
            actor: 'speaker'

        });

        // send email to affiliate
        const user = await Users.findOne({ where: { id: affiliate.affiliate_id } });
        const speaker = await Speakers.findOne({ where: { id: id }, attributes: ['name'] });
        if (user && user?.email) {
            await emailService.sendMeetingScheduleEmail(user.email, user.name, speaker.name, {
                date: inquiry_date,
                time: formattedInquiryTime,
                topic: note,
            }, `${CONFIG.FRONTEND_BASE_URL}/affiliate-inquiries/accept/${affiliateInquiry.id}`, `${CONFIG.FRONTEND_BASE_URL}/affiliate-inquiries/reject/${affiliateInquiry.id}`);
        }

        return { status: true, message: "Affiliate inquiry created successfully" };
    }
    catch (error) {
        console.error("Error creating affiliate inquiry:", error);
        throw error;
    }
}

/**
 * Update an existing affiliate inquiry.
 * Allows updating affiliate notes and reason for inquiry.
 * 
 * @param {Object} req - Request object
 * @param {number} req.params.id - Inquiry ID to update
 * @param {Object} req.body - Update data
 * @param {string} [req.body.affiliate_note] - Updated affiliate note
 * @param {string} [req.body.reason] - Reason for inquiry
 * @returns {Promise<Object>} Success message
 * @throws {CustomError} When inquiry not found or update fails
 */
affiliateInquiriesService.getAffiliateInquiriesHistory = async (req) => {
    try {
        const { page, limit, offset } = parsePagination(req.query);
        const id = req.userId || req.user?.id;

        const affiliateInquiriesHistory = await AffiliateInquiries.findAll({
            where: { speaker_id: id },
            include: [
                {
                    model: AffiliateInquiriesHistory,
                    as: 'affiliateInquiriesHistory',
                    attributes: ['id', 'status', 'note', 'inquiry_date', 'inquiry_time', 'actor', 'created_at', 'updated_at'],
                    order: [['created_at', 'DESC']],
                },
                {
                    model: AffiliateUsersDetails,
                    as: 'affiliate',
                    attributes: ['id'], // Include at least ID to maintain relationship
                    include: [{
                        model: Users,
                        as: 'user',
                        attributes: ['name'] // Only select the name from Users
                    }]
                }
            ],
            limit,
            offset,
        });

        // Process each inquiry to show affiliate entry if exists, otherwise latest speaker entry
        const processedData = affiliateInquiriesHistory.map(inquiry => {
            const histories = inquiry.affiliateInquiriesHistory || [];

            // Find oldest affiliate entry (actor = 'affiliate') - reverse to get oldest first
            const affiliateEntry = [...histories].reverse().find(h =>
                (h.actor || '').toLowerCase() === 'affiliate'
            );

            // Find oldest speaker entry (actor = 'speaker') - reverse to get oldest first
            const oldestSpeakerEntry = [...histories].reverse().find(h =>
                (h.actor || '').toLowerCase() === 'speaker'
            );

            // Priority: Affiliate entry > Oldest speaker entry
            const displayEntry = affiliateEntry || oldestSpeakerEntry;

            return {
                id: inquiry.id,
                affiliate: {
                    user: {
                        name: inquiry.affiliate?.user?.name || 'Unknown Affiliate'
                    }
                },
                inquiry_date: displayEntry?.inquiry_date || null,
                inquiry_time: displayEntry?.inquiry_time || null,
                status: displayEntry?.status || 'pending',
                note: displayEntry?.note || '',
                actor: displayEntry?.actor || null,
                created_at: inquiry.created_at,
                updated_at: inquiry.updated_at
            };
        });

        const totalAffiliateInquiriesHistory = await AffiliateInquiries.count({ where: { speaker_id: id } });
        const pageData = getPagingData(totalAffiliateInquiriesHistory, limit, page);

        return {
            status: true,
            message: "Affiliate inquiries history fetched successfully",
            data: processedData,
            pageData: pageData
        };

    }
    catch (error) {
        console.error("Error getting affiliate inquiries history:", error);
        throw error;
    }
}



module.exports = affiliateInquiriesService;