import dayjs from "dayjs";

export const RoleId_Mapper = {
  1: "Super Admin",
  2: "Affiliate",
  3: "System Admin",
};

export function formatDateYMD(date) {
  return date ? dayjs(date).format("YYYY-MM-DD hh:mm A") : "";
}

export function getStatusClasses(status) {
  return status === "success"
    ? "border-green-500 text-green-500 bg-green-50/10"
    : status === "error"
    ? "border-red-500 text-red-500 bg-red-50/10"
    : status === "running"
    ? "border-blue-500 text-blue-500 bg-blue-50/10"
    : status === "paused"
    ? "border-yellow-500 text-yellow-500 bg-yellow-50/10"
    : "border-gray-300 bg-gray-50/10";
}

export function formatCurrency(amount, currency) {
  if(amount){
  const formatted = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency,
  }).format(amount);
  return formatted;
}
return null;
}
