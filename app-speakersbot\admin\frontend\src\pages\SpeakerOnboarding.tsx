import React, { useMemo } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '../components/ui/card';
import { Progress } from '../components/ui/progress';
import { 
  BarChart, Bar, LineChart, Line, PieChart, Pie, Cell, 
  XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
  AreaChart, Area
} from 'recharts';
import { UserAddOutlined, CheckCircleOutlined, FileTextOutlined, StarOutlined } from '@ant-design/icons';

const SpeakerOnboarding: React.FC = () => {
  // Demo data for speaker onboarding metrics
  const weeklySignupsData = useMemo(() => [
    { day: 'Mon', affiliates: 12, ads: 8, referrals: 5 },
    { day: 'Tue', affiliates: 15, ads: 10, referrals: 7 },
    { day: 'Wed', affiliates: 9, ads: 6, referrals: 4 },
    { day: 'Thu', affiliates: 18, ads: 12, referrals: 8 },
    { day: 'Fri', affiliates: 14, ads: 9, referrals: 6 },
    { day: 'Sat', affiliates: 7, ads: 4, referrals: 3 },
    { day: 'Sun', affiliates: 10, ads: 6, referrals: 4 }
  ], []);

  const profileCompletionData = useMemo(() => [
    { field: 'Contact', percentage: 95 },
    { field: 'Bio', percentage: 85 },
    { field: 'Photos', percentage: 70 },
    { field: 'Expertise', percentage: 60 },
    { field: 'Talk Titles', percentage: 40 },
    { field: 'Sizzle Reel', percentage: 25 }
  ], []);

  const intakeCompletionData = useMemo(() => [
    { name: 'Completed', value: 60, color: '#009BC9' },
    { name: 'Partially Completed', value: 25, color: '#39C9E3' },
    { name: 'Abandoned', value: 15, color: '#A3DCED' }
  ], []);

  const topIncompleteFields = useMemo(() => [
    { field: 'Sizzle Reel Video', missing: 124, percentage: 38 },
    { field: 'Speaker Photos', missing: 89, percentage: 27 },
    { field: 'Biography', missing: 56, percentage: 17 },
    { field: 'Talk Abstracts', missing: 43, percentage: 13 },
    { field: 'Social Media Links', missing: 38, percentage: 12 }
  ], []);

  const signupSourceData = useMemo(() => [
    { source: 'Affiliates', count: 85, percentage: 52 },
    { source: 'Direct Ads', count: 54, percentage: 33 },
    { source: 'Referrals', count: 24, percentage: 15 }
  ], []);

  // Calculated metrics
  const totalNewSignupsWeek = weeklySignupsData.reduce((sum, day) => 
    sum + day.affiliates + day.ads + day.referrals, 0);
  const averageProfileQuality = 7.8; // Demo score out of 10
  const intakeCompletionRate = 60; // Demo percentage

  return (
    <div className="p-6 space-y-6 bg-background min-h-screen">
      <h1 className="text-2xl font-bold text-foreground mb-8">Speaker Onboarding Dashboard</h1>

      {/* KPI Summary Bar */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Total New Signups This Week */}
        <Card className="bg-card border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium" style={{ color: 'hsl(var(--muted-foreground))' }}>New Signups This Week</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold text-dashboard-dark">{totalNewSignupsWeek}</div>
              <UserAddOutlined className="h-5 w-5 text-dashboard-medium" />
            </div>
            <div className="mt-3 h-16">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={weeklySignupsData}>
                  <defs>
                    <linearGradient id="signupGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="hsl(var(--dashboard-light-blue))" />
                      <stop offset="100%" stopColor="hsl(var(--dashboard-dark-blue))" />
                    </linearGradient>
                  </defs>
                  <Bar dataKey="affiliates" stackId="signups" fill="url(#signupGradient)" radius={[0, 0, 4, 4]} />
                  <Bar dataKey="ads" stackId="signups" fill="hsl(var(--dashboard-medium-blue))" />
                  <Bar dataKey="referrals" stackId="signups" fill="hsl(var(--dashboard-light-blue))" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Average Profile Quality Score */}
        <Card className="bg-card border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium" style={{ color: 'hsl(var(--muted-foreground))' }}>Avg Profile Quality</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold text-dashboard-dark">{averageProfileQuality}/10</div>
              <StarOutlined className="h-5 w-5 text-dashboard-medium" />
            </div>
            <div className="mt-3">
              <div className="w-full bg-muted rounded-full h-3">
                <div
                  className="h-3 rounded-full transition-all duration-500 ease-out"
                  style={{
                    width: `${(averageProfileQuality / 10) * 100}%`,
                    background: 'linear-gradient(90deg, hsl(var(--dashboard-dark-blue)), hsl(var(--dashboard-medium-blue)))'
                  }}
                />
              </div>
              <div className="text-xs text-muted-foreground mt-1">Excellent Quality</div>
            </div>
          </CardContent>
        </Card>

        {/* Intake Completion Rate */}
        <Card className="bg-card border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium" style={{ color: 'hsl(var(--muted-foreground))' }}>STAiGENT Completion Rate</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="text-2xl font-bold text-dashboard-dark">{intakeCompletionRate}%</div>
              <CheckCircleOutlined className="h-5 w-5 text-dashboard-medium" />
            </div>
            <div className="mt-3">
              <div className="relative w-16 h-16 mx-auto">
                <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 64 64">
                  <circle
                    cx="32"
                    cy="32"
                    r="28"
                    stroke="hsl(var(--dashboard-light-blue) / 0.3)"
                    strokeWidth="4"
                    fill="none"
                  />
                  <circle
                    cx="32"
                    cy="32"
                    r="28"
                    stroke="url(#completionGradient)"
                    strokeWidth="4"
                    fill="none"
                    strokeDasharray={`${(intakeCompletionRate / 100) * 175.9} 175.9`}
                    strokeLinecap="round"
                  />
                  <defs>
                    <linearGradient id="completionGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" stopColor="hsl(var(--dashboard-dark-blue))" />
                      <stop offset="100%" stopColor="hsl(var(--dashboard-medium-blue))" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>
              <div className="text-xs text-muted-foreground mt-1 text-center">Target: 80%</div>
            </div>
          </CardContent>
        </Card>

        {/* Profile Quality Distribution */}
        <Card className="bg-card border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium" style={{ color: 'hsl(var(--muted-foreground))' }}>Quality Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-xs text-muted-foreground">High Quality</span>
                <span className="text-xs font-medium text-dashboard-dark">62%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div 
                  className="h-2 rounded-full bg-dashboard-dark" 
                  style={{ width: '62%' }}
                />
              </div>
              <div className="flex justify-between items-center mt-2">
                <span className="text-xs text-muted-foreground">Medium Quality</span>
                <span className="text-xs font-medium text-dashboard-medium">28%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div 
                  className="h-2 rounded-full bg-dashboard-medium" 
                  style={{ width: '28%' }}
                />
              </div>
              <div className="flex justify-between items-center mt-2">
                <span className="text-xs text-muted-foreground">Low Quality</span>
                <span className="text-xs font-medium text-dashboard-light">10%</span>
              </div>
              <div className="w-full bg-muted rounded-full h-2">
                <div 
                  className="h-2 rounded-full bg-dashboard-light" 
                  style={{ width: '10%' }}
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* New Signups by Source */}
        <Card className="bg-card border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardHeader>
            <CardTitle className="text-lg font-semibold" style={{ color: 'hsl(var(--foreground))' }}>New Signups by Source</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={weeklySignupsData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                  <defs>
                    <linearGradient id="affiliatesGrad" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="#009BC9" />
                      <stop offset="100%" stopColor="#009BC9" opacity="0.8" />
                    </linearGradient>
                    <linearGradient id="adsGrad" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="#39C9E3" />
                      <stop offset="100%" stopColor="#39C9E3" opacity="0.8" />
                    </linearGradient>
                    <linearGradient id="referralsGrad" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="#A3DCED" />
                      <stop offset="100%" stopColor="#A3DCED" opacity="0.8" />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                  <XAxis dataKey="day" stroke="hsl(var(--muted-foreground))" />
                  <YAxis stroke="hsl(var(--muted-foreground))" />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: 'hsl(var(--card))', 
                      border: '1px solid hsl(var(--border))',
                      borderRadius: '8px',
                      color: 'hsl(var(--foreground))'
                    }} 
                  />
                  <Legend />
                  <Bar dataKey="affiliates" stackId="source" fill="url(#affiliatesGrad)" name="Affiliates" radius={[0, 0, 4, 4]} />
                  <Bar dataKey="ads" stackId="source" fill="url(#adsGrad)" name="Ads" />
                  <Bar dataKey="referrals" stackId="source" fill="url(#referralsGrad)" name="Referrals" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        {/* Profile Completion Heatmap */}
        <Card className="bg-card border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardHeader>
            <CardTitle className="text-lg font-semibold" style={{ color: 'hsl(var(--foreground))' }}>Profile Completion Progress</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={profileCompletionData} margin={{ top: 20, right: 30, left: 20, bottom: 40 }}>
                  <defs>
                    <linearGradient id="profileCompletionGrad" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="0%" stopColor="#009BC9" />
                      <stop offset="100%" stopColor="#39C9E3" />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                  <XAxis 
                    dataKey="field" 
                    stroke="hsl(var(--muted-foreground))" 
                    height={40}
                    interval={0}
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis 
                    stroke="hsl(var(--muted-foreground))" 
                    domain={[0, 100]}
                    tickFormatter={(value) => `${value}%`}
                  />
                  <Tooltip 
                    contentStyle={{ 
                      backgroundColor: 'hsl(var(--card))', 
                      border: '1px solid hsl(var(--border))',
                      borderRadius: '8px',
                      color: 'hsl(var(--foreground))'
                    }}
                    formatter={(value) => [`${value}%`, 'Completion']}
                  />
                  <Bar 
                    dataKey="percentage" 
                    fill="url(#profileCompletionGrad)" 
                    name="Completion %" 
                    radius={[4, 4, 0, 0]} 
                  />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bottom Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Incomplete Fields */}
        <Card className="bg-card border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardHeader>
            <CardTitle className="text-lg font-semibold" style={{ color: 'hsl(var(--foreground))' }}>Top Incomplete Fields</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topIncompleteFields.map((item, index) => (
                <div key={item.field} className="flex items-center justify-between p-3 rounded-lg bg-muted/50">
                  <div className="flex items-center space-x-3">
                    <div 
                      className="w-8 h-8 rounded-full flex items-center justify-center font-bold text-sm"
                      style={{ 
                        backgroundColor: index === 0 ? '#009BC9' : index === 1 ? '#39C9E3' : '#A3DCED',
                        color: '#FFFFFF'
                      }}
                    >
                      {index + 1}
                    </div>
                    <div>
                      <div className="font-medium text-foreground">{item.field}</div>
                      <div className="text-xs text-muted-foreground">{item.missing} speakers missing</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-dashboard-dark">{item.percentage}%</div>
                    <div className="w-16 bg-muted rounded-full h-2 mt-1">
                      <div 
                        className="h-2 rounded-full bg-dashboard-medium" 
                        style={{ width: `${item.percentage}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Intake Form Completion Insights */}
        <Card className="bg-card border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
          <CardHeader>
            <CardTitle className="text-lg font-semibold" style={{ color: 'hsl(var(--foreground))' }}>STAiGENT Form Completion</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-80 flex flex-col items-center justify-center">
              <div className="relative">
                <ResponsiveContainer width={280} height={280}>
                  <PieChart>
                    <Pie
                      data={intakeCompletionData}
                      cx="50%"
                      cy="50%"
                      outerRadius={120}
                      innerRadius={80}
                      dataKey="value"
                      startAngle={90}
                      endAngle={450}
                      paddingAngle={2}
                      label={({ value }) => `${value}%`}
                      labelLine={false}
                    >
                      {intakeCompletionData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{ 
                        backgroundColor: 'hsl(var(--card))', 
                        border: '1px solid hsl(var(--border))',
                        borderRadius: '8px',
                        color: 'hsl(var(--foreground))'
                      }} 
                    />
                  </PieChart>
                </ResponsiveContainer>
                {/* Center text */}
                <div className="absolute inset-0 flex flex-col items-center justify-center">
                  <div className="text-xl font-bold text-foreground">1,247</div>
                  <div className="text-xs text-muted-foreground">Total Forms</div>
                </div>
              </div>
              {/* Legend below chart */}
              <div className="flex justify-center space-x-4 mt-4">
                {intakeCompletionData.map((item, index) => (
                  <div key={item.name} className="flex items-center space-x-2">
                    <div 
                      className="w-3 h-3 rounded-full" 
                      style={{ backgroundColor: item.color }}
                    />
                    <div className="text-xs">
                      <span className="font-medium text-foreground">{item.name}</span>
                      <span className="text-muted-foreground ml-1">({item.value}%)</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SpeakerOnboarding;