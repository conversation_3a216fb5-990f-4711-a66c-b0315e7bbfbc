import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useForgotPasswordMutation } from "@/apis/authApi";
import { Link } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import logoLight from "/logo-light.png";
import { Separator } from "@/components/ui/separator";

export default function ForgotPassword() {
  const [email, setEmail] = useState("");
  const { toast } = useToast();
  const [forgotPassword, { isLoading }] = useForgotPasswordMutation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email) {
      toast({
        title: "Email required",
        description: "Please enter your email.",
        variant: "destructive",
      });
      return;
    }
    try {
      const res = await forgotPassword({ email }).unwrap();
      toast({
        title: "Email sent",
        description:
          res?.message || "If that email exists, a reset link was sent.",
      });
    } catch (error: any) {
      toast({
        title: "Request failed",
        description: error?.data?.message || "Unable to send reset email.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-surface flex flex-col items-center justify-center p-6 relative">
      {/* Decorative Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-primary opacity-10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-accent-purple opacity-10 rounded-full blur-3xl"></div>
      </div>
      <Card className="w-full max-w-md backdrop-blur-xl bg-card/80 border-border shadow-card relative z-10">
        <CardHeader className="space-y-2 pb-8">
          <div className="text-center">
            <img
              src={logoLight}
              alt="SpeakerBot"
              className="h-[80px] w-full object-contain"
            />
          </div>
          <div className="flex items-center">
            <Separator className="relative w-full bg-border my-3" />
          </div>
          <div className="">
            <CardTitle className="text-2xl text-center text-foreground">
              Forgot Password
            </CardTitle>
            <p className="text-foreground-muted text-sm text-center mt-2">
              Enter your email address and we'll send you a link to reset your password
            </p>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-foreground">
                Email
              </Label>
              <Input
                id="email"
                type="email"
                value={email}
                disabled={isLoading}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="bg-surface-elevated border-border-subtle focus:border-primary focus:ring-primary/20"
              />
            </div>
            <div className="mt-5">
              <Button
                type="submit"
                className="w-full"
                loading={isLoading}
                disabled={isLoading}
              >
                Send reset link
              </Button>
            </div>
          </form>
          <div className="text-center mt-4">
            <Link
              to="/speaker"
              className="inline-flex items-center text-sm text-primary-foreground hover:text-primary transition-colors"
            >
              <ArrowLeft className="mr-1" />
              Back to Login
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
