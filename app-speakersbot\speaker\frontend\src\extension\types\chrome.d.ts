// Chrome Extension Types
declare namespace chrome {
  namespace storage {
    interface LocalStorageArea {
      get(keys?: string | string[] | { [key: string]: any } | null): Promise<{ [key: string]: any }>;
      get(keys: string | string[] | { [key: string]: any } | null, callback: (items: { [key: string]: any }) => void): void;
      set(items: { [key: string]: any }): Promise<void>;
      set(items: { [key: string]: any }, callback?: () => void): void;
    }
    
    const local: LocalStorageArea;
  }
  
  namespace runtime {
    interface Port {
      name: string;
      disconnect(): void;
      onDisconnect: {
        addListener(callback: (port: Port) => void): void;
      };
      onMessage: {
        addListener(callback: (message: any, port: Port) => void): void;
      };
      postMessage(message: any): void;
    }
    
    function sendMessage(message: any): Promise<any>;
    function sendMessage(message: any, callback: (response: any) => void): void;
    
    const onInstalled: {
      addListener(callback: (details: { reason: string; previousVersion?: string }) => void): void;
    };
    
    const onStartup: {
      addListener(callback: () => void): void;
    };
    
    const onMessage: {
      addListener(callback: (message: any, sender: any, sendResponse: (response: any) => void) => void | boolean): void;
    };
  }
  
  namespace tabs {
    interface Tab {
      id?: number;
      url?: string;
      title?: string;
    }
    
    function sendMessage(tabId: number, message: any): Promise<any>;
    function sendMessage(tabId: number, message: any, callback: (response: any) => void): void;
    
    const onUpdated: {
      addListener(callback: (tabId: number, changeInfo: any, tab: Tab) => void): void;
    };
  }
  
  namespace notifications {
    interface NotificationOptions {
      type: 'basic' | 'image' | 'list' | 'progress';
      iconUrl: string;
      title: string;
      message: string;
      contextMessage?: string;
      priority?: number;
    }
    
    function create(notificationId: string, options: NotificationOptions): void;
    function create(options: NotificationOptions): void;
    
    const onClicked: {
      addListener(callback: (notificationId: string) => void): void;
    };
  }
  
  namespace alarms {
    interface Alarm {
      name: string;
      scheduledTime: number;
      periodInMinutes?: number;
    }
    
    function create(name: string, alarmInfo: { delayInMinutes?: number; periodInMinutes?: number; when?: number }): void;
    
    const onAlarm: {
      addListener(callback: (alarm: Alarm) => void): void;
    };
  }
}