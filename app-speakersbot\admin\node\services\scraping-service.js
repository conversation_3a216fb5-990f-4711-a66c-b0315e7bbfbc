const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const { sequelize, ScrapingLogging, Category, Subcategory, Settings } = require("../models");
const { Op } = require('sequelize');

const scrapingService = {};

// ------------------------- scraping-service -------------------------

/**
 * Get all scraping logs with filters, sorting, and pagination.
 * Comprehensive scraping management with advanced filtering and data export.
 * Supports filtering by status, topic, date range, and error messages.
 * 
 * @param {Object} [filters={}] - Filter options
 * @param {string} [filters.search] - Search term for topics/status/errors
 * @param {string} [filters.status] - Filter by scraping status
 * @param {string} [filters.topic] - Filter by specific topic
 * @param {string} [filters.startDate] - Filter by start date (YYYY-MM-DD)
 * @param {string} [filters.endDate] - Filter by end date (YYYY-MM-DD)
 * @param {string} [filters.error] - Search in error messages
 * @param {Object} [pagination={}] - Pagination options
 * @param {number} [pagination.page=1] - Page number
 * @param {number} [pagination.limit=10] - Items per page
 * @param {Object} [sorting={}] - Sorting options
 * @param {string} [sorting.sortBy] - Sort field (started_at, items_collected)
 * @param {string} [sorting.sortOrder] - Sort order (ASC, DESC)
 * @returns {Promise<Object>} Paginated scraping logs data
 * @throws {Error} When database operation fails
 */
scrapingService.getScrapingLogs = async (filters = {}, pagination = {}, sorting = {}) => {
  try {
    const {
      search,
      status,
      topic,
      startDate,
      endDate,
      error
    } = filters;

    const {
      page = 1,
      limit = 10
    } = pagination;

    const {
      sortBy = 'started_at',
      sortOrder = 'DESC'
    } = sorting;

    // Build where clause
    const whereClause = {};

    // Search filter - search in topic, reason, and status
    if (search) {
      whereClause[Op.or] = [
        { topic: { [Op.like]: `%${search}%` } },
        { reason: { [Op.like]: `%${search}%` } },
        { status: { [Op.like]: `%${search.toLowerCase()}%` } }
      ].filter(Boolean);
    }

    // Status filter (success/error/running/paused)
    if (status !== undefined && status !== null) {
      whereClause.status = status;
    }

    // Topic filter
    if (topic) {
      whereClause.topic = { [Op.like]: `%${topic}%` };
    }

    // Error filter (search in reason field)
    if (error) {
      whereClause.reason = { [Op.like]: `%${error}%` };
    }

    // Date range filter
    if (startDate || endDate) {
      whereClause.started_at = {};
      if (startDate) {
        whereClause.started_at[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereClause.started_at[Op.lte] = new Date(endDate);
      }
    }

    // Calculate offset
    const offset = (page - 1) * limit;

    // Build order clause
    let orderClause = [];
    if (sortBy === 'started_at') {
      orderClause = [['started_at', sortOrder.toUpperCase()]];
    } else if (sortBy === 'items_collected') {
      orderClause = [['item_count', sortOrder.toUpperCase()]];
    } else {
      orderClause = [['started_at', 'DESC']]; // Default sorting
    }

    // Get total count for pagination
    const totalCount = await ScrapingLogging.count({
      where: whereClause
    });

    // Get paginated results
    const logs = await ScrapingLogging.findAll({
      where: whereClause,
      order: orderClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      attributes: [
        'id',
        'topic',
        'item_count',
        'status',
        'reason',
        'started_at',
        'ended_at', 
        // 'created_at',
        // 'updated_at'
      ]
    });

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount || 0 / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return {
      data: logs,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        limit: parseInt(limit),
        hasNextPage,
        hasPrevPage
      }
    };
  } catch (error) {
    throw new CustomError(
      RESPONSE_CODES.SERVER_ERROR,
      `Error fetching scraping logs: ${error.message}`
    );
  }
};

/**
 * Get scraping log by ID
 * @param {number} id - Log ID
 */
scrapingService.getScrapingLogById = async (id) => {
  try {
    const log = await ScrapingLogging.findByPk(id, {
      attributes: [
        'id',
        'topic',
        'item_count',
        'status',
        'reason',
        'started_at',
        'ended_at',
        'created_at',
        'updated_at'
      ]
    });

    if (!log) {
      throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Scraping log not found");
    }

    return log;
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    throw new CustomError(
      RESPONSE_CODES.SERVER_ERROR,
      `Error fetching scraping log: ${error.message}`
    );
  }
};

/**
 * Get all available topics for filtering
 */
scrapingService.getAvailableTopics = async () => {
  try {
    const topics = await ScrapingLogging.findAll({
      attributes: ['topic'],
      group: ['topic'],
      order: [['topic', 'ASC']]
    });

    return topics.map(topic => topic.topic);
  } catch (error) {
    throw new CustomError(
      RESPONSE_CODES.SERVER_ERROR,
      `Error fetching topics: ${error.message}`
    );
  }
};

/**
 * Get scraping statistics
 */
scrapingService.getScrapingStats = async () => {
  try {
    const stats = await ScrapingLogging.findAll({
      attributes: [
        [sequelize.fn('COUNT', sequelize.col('id')), 'totalLogs'],
        [sequelize.fn('COUNT', sequelize.literal('CASE WHEN status = "success" THEN 1 END')), 'successfulLogs'],
        [sequelize.fn('COUNT', sequelize.literal('CASE WHEN status = "error" THEN 1 END')), 'failedLogs'],
        [sequelize.fn('SUM', sequelize.col('item_count')), 'totalItemsCollected'],
        [sequelize.fn('AVG', sequelize.col('item_count')), 'avgItemsPerLog']
      ],
      raw: true
    });

    return stats[0] || {
      totalLogs: 0,
      successfulLogs: 0,
      failedLogs: 0,
      totalItemsCollected: 0,
      avgItemsPerLog: 0
    };
  } catch (error) {
    throw new CustomError(
      RESPONSE_CODES.SERVER_ERROR,
      `Error fetching scraping statistics: ${error.message}`
    );
  }
};

/**
 * Get all subcategories with filters, sorting, and pagination
 * @param {Object} filters - Filter options
 * @param {Object} pagination - Pagination options
 * @param {Object} sorting - Sorting options
 */
scrapingService.getSubcategories = async (filters = {}, pagination = {}, sorting = {}) => {
  try {
    const {
      search,
      is_active
    } = filters;

    const {
      page = 1,
      limit = 10
    } = pagination;

    const {
      sortBy = 'name',
      sortOrder = 'ASC'
    } = sorting;

    // Build where clause
    const whereClause = {};

    // Search filter - search in subcategory name and category name
    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.like]: `%${search}%` } },
        { '$category.name$': { [Op.like]: `%${search}%` } }
      ];
    }

    // Active status filter
    if (is_active !== undefined && is_active !== null && is_active !== '') {
      whereClause.is_active = is_active;
    }

    // Calculate offset
    const offset = (page - 1) * limit;

    // Build order clause
    let orderClause = [];
    if (sortBy === 'name') {
      orderClause = [['name', sortOrder.toUpperCase()]];
    } else if (sortBy === 'category_name') {
      orderClause = [[Category, 'name', sortOrder.toUpperCase()]];
    } else if (sortBy === 'is_active') {
      orderClause = [['is_active', sortOrder.toUpperCase()]];
    } else {
      orderClause = [['name', 'ASC']]; // Default sorting
    }

    // Get total count for pagination
    const totalCount = await Subcategory.count({
      where: whereClause,
      include: [{
        model: Category,
        as: 'category',
        attributes: []
      }]
    });

    // Get paginated results
    const subcategories = await Subcategory.findAll({
      where: whereClause,
      include: [{
        model: Category,
        as: 'category',
        attributes: ['id', 'name']
      }],
      order: orderClause,
      limit: parseInt(limit),
      offset: parseInt(offset),
      attributes: [
        'id',
        'name',
        'category_id',
        'is_active'
      ]
    });

    // Transform data to include category name
    const transformedData = subcategories.map(subcategory => ({
      id: subcategory.id,
      name: subcategory.name,
      category_id: subcategory.category_id,
      category_name: subcategory.category ? subcategory.category.name : null,
      is_active: subcategory.is_active
    }));

    // Calculate pagination info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return {
      data: transformedData,
      pagination: {
        currentPage: parseInt(page),
        totalPages,
        totalCount,
        limit: parseInt(limit),
        hasNextPage,
        hasPrevPage
      }
    };
  } catch (error) {
    throw new CustomError(
      RESPONSE_CODES.SERVER_ERROR,
      `Error fetching subcategories: ${error.message}`
    );
  }
};

/**
 * Get all categories
 */
scrapingService.getAllCategories = async () => {
  try {
    const categories = await Category.findAll({
      attributes: [
        'id',
        'name'
      ],
      order: [['name', 'ASC']]
    });

    return categories;
  } catch (error) {
    throw new CustomError(
      RESPONSE_CODES.SERVER_ERROR,
      `Error fetching categories: ${error.message}`
    );
  }
};

/**
 * Toggle subcategory active status
 * @param {number} subcategoryId - Subcategory ID
 */
scrapingService.toggleSubcategoryActive = async (subcategoryId) => {
  try {
    const subcategory = await Subcategory.findByPk(subcategoryId);
    
    if (!subcategory) {
      throw new CustomError(RESPONSE_CODES.NOT_FOUND, 'Subcategory not found');
    }

    // Toggle the active status
    const newStatus = subcategory.is_active === '1' ? '0' : '1';
    await subcategory.update({ is_active: newStatus });

    return {
      id: subcategory.id,
      name: subcategory.name,
      category_id: subcategory.category_id,
      is_active: newStatus
    };
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    throw new CustomError(
      RESPONSE_CODES.SERVER_ERROR,
      `Error toggling subcategory status: ${error.message}`
    );
  }
};

/**
 * Create a new subcategory
 * @param {Object} subcategoryData - Subcategory data
 */
scrapingService.createSubcategory = async (subcategoryData) => {
  try {
    const { category_id, name } = subcategoryData;

    // Validate required fields
    if (!category_id || isNaN(parseInt(category_id))) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        'Valid category ID is required'
      );
    }

    if (!name || name.trim() === '') {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        'Subcategory name is required'
      );
    }

    // Check if category exists
    const category = await Category.findByPk(category_id);
    if (!category) {
      throw new CustomError(
        RESPONSE_CODES.NOT_FOUND,
        'Category not found'
      );
    }

    // Check if subcategory with same name already exists in the same category
    const existingSubcategory = await Subcategory.findOne({
      where: {
        name: name.trim(),
        category_id: category_id
      }
    });

    if (existingSubcategory) {
      throw new CustomError(
        RESPONSE_CODES.CONFLICT,
        'Subcategory with this name already exists in the selected category'
      );
    }

    // Create new subcategory
    const newSubcategory = await Subcategory.create({
      name: name.trim(),
      category_id: category_id,
      is_active: '1'
    });

    // Return subcategory with category name
    const subcategoryWithCategory = await Subcategory.findByPk(newSubcategory.id, {
      include: [{
        model: Category,
        as: 'category',
        attributes: ['id', 'name']
      }],
      attributes: [
        'id',
        'name',
        'category_id',
        'is_active'
      ]
    });

    return {
      id: subcategoryWithCategory.id,
      name: subcategoryWithCategory.name,
      category_id: subcategoryWithCategory.category_id,
      category_name: subcategoryWithCategory.category ? subcategoryWithCategory.category.name : null,
      is_active: subcategoryWithCategory.is_active
    };
  } catch (error) {
    if (error instanceof CustomError) {
      throw error;
    }
    throw new CustomError(
      RESPONSE_CODES.SERVER_ERROR,
      `Error creating subcategory: ${error.message}`
    );
  }
};


/**
 * Export scraping logs as CSV with filters
 * @param {Object} filters - Filter options
 * @param {Object} sorting - Sorting options
 */
scrapingService.exportScrapingLogsCSV = async (filters = {}, sorting = {}) => {
  try {
    const {
      search,
      status,
      topic,
      startDate,
      endDate,
      error
    } = filters;

    const {
      sortBy = 'started_at',
      sortOrder = 'DESC'
    } = sorting;

    // Build where clause (same as getScrapingLogs)
    const whereClause = {};

    // Search filter - search in topic, reason, and status
    if (search) {
      whereClause[Op.or] = [
        { topic: { [Op.like]: `%${search}%` } },
        { reason: { [Op.like]: `%${search}%` } },
        { status: { [Op.like]: `%${search.toLowerCase()}%` } }
      ].filter(Boolean);
    }

    // Status filter (success/failed)
    if (status !== undefined && status !== null) {
      whereClause.status = status;
    }

    // Topic filter
    if (topic) {
      whereClause.topic = { [Op.like]: `%${topic}%` };
    }

    // Error filter (search in reason field)
    if (error) {
      whereClause.reason = { [Op.like]: `%${error}%` };
    }

    // Date range filter
    if (startDate || endDate) {
      whereClause.started_at = {};
      if (startDate) {
        whereClause.started_at[Op.gte] = new Date(startDate);
      }
      if (endDate) {
        whereClause.started_at[Op.lte] = new Date(endDate);
      }
    }

    // Build order clause
    let orderClause = [];
    if (sortBy === 'started_at') {
      orderClause = [['started_at', sortOrder.toUpperCase()]];
    } else if (sortBy === 'items_collected') {
      orderClause = [['item_count', sortOrder.toUpperCase()]];
    } else {
      orderClause = [['started_at', 'DESC']]; // Default sorting
    }

    // Get all logs (no pagination for export)
    const logs = await ScrapingLogging.findAll({
      where: whereClause,
      order: orderClause,
      attributes: [
        'id',
        'topic',
        'item_count',
        'status',
        'reason',
        'started_at',
        'ended_at',
        'created_at',
        'updated_at'
      ]
    });

    // Transform data for CSV export
    const csvData = logs.map(log => ({
      'ID': log.id,
      'Topic': log.topic,
      'Items Collected': log.item_count,
      'Status': log.status.charAt(0).toUpperCase() + log.status.slice(1), // Capitalize first letter
      'Reason/Error': log.reason || '',
      'Started At': log.started_at ? new Date(log.started_at).toISOString() : '',
      'Ended At': log.ended_at ? new Date(log.ended_at).toISOString() : '',
      'Duration (minutes)': log.started_at && log.ended_at ? 
        Math.round((new Date(log.ended_at) - new Date(log.started_at)) / (1000 * 60)) : '',
      'Created At': log.created_at ? new Date(log.created_at).toISOString() : '',
      'Updated At': log.updated_at ? new Date(log.updated_at).toISOString() : ''
    }));

    return csvData;
  } catch (error) {
    throw new CustomError(
      RESPONSE_CODES.SERVER_ERROR,
      `Error exporting scraping logs: ${error.message}`
    );
  }
};

/**
 * Start or stop scraping by updating settings
 * @param {boolean} isRunning - Whether to start (true) or stop (false) scraping
 */
scrapingService.controlScraping = async (isRunning) => {
  try {
    const key = 'run_scraping';
    const value = isRunning ? 'true' : 'false';

    // Check if setting exists
    const existingSetting = await Settings.findOne({
      where: { key: key }
    });

    if (existingSetting) {
      // Update existing setting
      await existingSetting.update({ 
        value: value,
        updated_at: new Date()
      });
    } else {
      // Create new setting
      await Settings.create({
        key: key,
        value: value
      });
    }

    return {
      // key: key,
      // value: value,
      isRunning: isRunning,
      message: isRunning ? 'Scraping started successfully' : 'Scraping stopped successfully'
    };
  } catch (error) {
    throw new CustomError(
      RESPONSE_CODES.SERVER_ERROR,
      `Error controlling scraping: ${error.message}`
    );
  }
};

/**
 * Get current scraping status from settings
 */
scrapingService.getScrapingStatus = async () => {
  try {
    const setting = await Settings.findOne({
      where: { key: 'run_scraping' }
    });

    const isRunning = setting ? setting.value === 'true' : false;

    return {
      // key: 'run_scraping',
      // value: setting ? setting.value : 'false',
      isRunning: isRunning,
      message: isRunning ? 'Scraping is currently running' : 'Scraping is currently stopped'
    };
  } catch (error) {
    throw new CustomError(
      RESPONSE_CODES.SERVER_ERROR,
      `Error getting scraping status: ${error.message}`
    );
  }
};

module.exports = scrapingService;
