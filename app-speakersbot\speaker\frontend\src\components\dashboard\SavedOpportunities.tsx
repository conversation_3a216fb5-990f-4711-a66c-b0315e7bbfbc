import { Bookmark, Calendar, DollarSign, Users } from "lucide-react";
import { Card } from "@/components/ui/card";

const savedOpportunities = [
  {
    id: 1,
    title: "Senior React Developer Speaker",
    organization: "React Conference 2024",
    deadline: "2024-03-10",
    compensation: "$2,500",
    audience: "500+",
    type: "paid",
    category: "Technology"
  },
  {
    id: 2,
    title: "Startup Pitch Competition Judge",
    organization: "Startup Week",
    deadline: "2024-03-12",
    compensation: "Travel + Hotel",
    audience: "200",
    type: "expenses",
    category: "Business"
  },
  {
    id: 3,
    title: "Women in Tech Panel Discussion",
    organization: "Tech Diversity Summit",
    deadline: "2024-03-15",
    compensation: "Pro Bono",
    audience: "300",
    type: "volunteer",
    category: "Diversity"
  }
];

export const SavedOpportunities = () => {
  return (
    <Card className="bg-card border-border-subtle p-4 space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-semibold text-foreground flex items-center gap-2">
          <Bookmark className="h-4 w-4 text-silver" />
          Saved Opportunities
        </h3>
        <span className="text-xs text-foreground-muted bg-accent px-2 py-1 rounded-md">
          {savedOpportunities.length}
        </span>
      </div>
      
      <div className="space-y-3">
        {savedOpportunities.map((opportunity) => (
          <div
            key={opportunity.id}
            className="bg-surface-elevated border border-border-subtle rounded-lg p-3 hover:bg-surface transition-colors cursor-pointer group"
          >
            <div className="space-y-2">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-foreground text-sm line-clamp-2">
                    {opportunity.title}
                  </h4>
                  <p className="text-xs text-foreground-muted">{opportunity.organization}</p>
                </div>
                
                <span className={`text-xs px-2 py-1 rounded-full ml-2 ${
                  opportunity.type === 'paid' 
                    ? 'bg-success/20 text-success' 
                    : opportunity.type === 'expenses'
                    ? 'bg-warning/20 text-warning'
                    : 'bg-silver/20 text-silver'
                }`}>
                  {opportunity.type}
                </span>
              </div>
              
              <div className="flex items-center justify-between text-xs text-foreground-subtle">
                <div className="flex items-center gap-3">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>Due {new Date(opportunity.deadline).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Users className="h-3 w-3" />
                    <span>{opportunity.audience}</span>
                  </div>
                </div>
                
                <div className="flex items-center gap-1 text-foreground-muted">
                  <DollarSign className="h-3 w-3" />
                  <span>{opportunity.compensation}</span>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-xs text-foreground-subtle bg-muted px-2 py-1 rounded-md">
                  {opportunity.category}
                </span>
                <button className="text-xs text-primary hover:text-primary-hover opacity-0 group-hover:opacity-100 transition-all">
                  Apply Now
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <button className="w-full text-center text-xs text-foreground-muted hover:text-foreground transition-colors py-2 border-t border-border-subtle">
        View All Saved
      </button>
    </Card>
  );
};