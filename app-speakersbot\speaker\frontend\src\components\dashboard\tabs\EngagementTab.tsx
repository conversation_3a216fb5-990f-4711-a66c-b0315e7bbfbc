import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Trophy, 
  TrendingUp, 
  Calendar, 
  Target,
  Award,
  Star,
  ArrowUp,
  Plus,
  BarChart3,
  Zap
} from 'lucide-react';

export function EngagementTab() {
  const [currentXP] = useState(2800);
  const [nextTierXP] = useState(5000);
  const [currentTier] = useState('Speaker');
  const [nextTier] = useState('TEDx Coach');

  const xpProgress = (currentXP / nextTierXP) * 100;
  const xpNeeded = nextTierXP - currentXP;

  const affiliateRewards = [
    { tier: 'Novice', xpRequired: 0, benefits: ['Basic matching', 'Email support'], current: false },
    { tier: 'Speaker', xpRequired: 1000, benefits: ['Priority matching', 'Profile optimization', 'Monthly insights'], current: true },
    { tier: 'Expert', xpRequired: 2500, benefits: ['Advanced analytics', 'Custom outreach templates', 'Phone support'], current: false },
    { tier: 'TEDx Coach', xpRequired: 5000, benefits: ['1-hour coaching call', 'Speaking opportunity leads', 'Event introductions'], current: false },
    { tier: 'Keynote Elite', xpRequired: 10000, benefits: ['Personal account manager', 'VIP event access', 'Revenue optimization'], current: false }
  ];

  const quickXPActions = [
    { action: 'Complete profile fields', xp: 50, icon: Target },
    { action: 'Upload speaker reel', xp: 100, icon: Plus },
    { action: 'Write detailed bio', xp: 75, icon: Plus },
    { action: 'Add 3 new topics', xp: 60, icon: Plus },
    { action: 'Set availability calendar', xp: 40, icon: Calendar }
  ];

  const monthlyMetrics = {
    applicationsSubmitted: 12,
    applicationsLastMonth: 8,
    responseRate: 65,
    responseRateLastMonth: 58,
    avgFee: '$4,200',
    avgFeeLastMonth: '$3,800'
  };

  const getChangeIndicator = (current: number, previous: number) => {
    const change = current - previous;
    const percentChange = ((change / previous) * 100).toFixed(1);
    const isPositive = change > 0;
    
    return (
      <div className={`flex items-center gap-1 text-sm ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
        <ArrowUp className={`h-3 w-3 ${isPositive ? '' : 'rotate-180'}`} />
        {Math.abs(change)} ({percentChange}%)
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* XP Points & Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trophy className="h-5 w-5 text-yellow-500" />
            Your XP Journey
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Current XP Display */}
          <div className="text-center space-y-2">
            <div className="text-4xl font-bold text-primary">{currentXP.toLocaleString()} XP</div>
            <Badge variant="default" className="text-sm px-3 py-1">
              Current Tier: {currentTier}
            </Badge>
          </div>

          {/* Progress to Next Tier */}
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>Progress to {nextTier}</span>
              <span className="font-medium">{xpNeeded.toLocaleString()} XP needed</span>
            </div>
            <Progress value={xpProgress} className="h-3" />
            <p className="text-center text-sm text-muted-foreground">
              Reach {nextTierXP.toLocaleString()} XP to unlock {nextTier} benefits
            </p>
          </div>

          {/* Quick XP Actions */}
          <div className="space-y-3">
            <h3 className="font-semibold flex items-center gap-2">
              <Zap className="h-4 w-4 text-yellow-500" />
              Earn XP Now
            </h3>
            <div className="grid gap-2">
              {quickXPActions.map((action, index) => (
                <div key={index} className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-surface transition-colors">
                  <div className="flex items-center gap-3">
                    <action.icon className="h-4 w-4 text-primary" />
                    <span className="text-sm">{action.action}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className="text-xs">+{action.xp} XP</Badge>
                    <Button size="sm" variant="outline">
                      Do Now
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Affiliate Rewards Tiers */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5 text-purple-500" />
            Reward Tiers
          </CardTitle>
          <CardDescription>
            Unlock exclusive benefits as you gain more XP
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {affiliateRewards.map((reward, index) => (
              <div 
                key={index} 
                className={`p-4 rounded-lg border transition-colors ${
                  reward.current 
                    ? 'border-primary bg-primary/5' 
                    : currentXP >= reward.xpRequired 
                      ? 'border-green-500 bg-green-50' 
                      : 'border-border'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <Star className={`h-4 w-4 ${
                      reward.current ? 'text-primary' : 
                      currentXP >= reward.xpRequired ? 'text-green-500' : 'text-muted-foreground'
                    }`} />
                    <h3 className="font-semibold">{reward.tier}</h3>
                    {reward.current && <Badge variant="default">Current</Badge>}
                    {currentXP >= reward.xpRequired && !reward.current && <Badge variant="outline" className="border-green-500 text-green-600">Unlocked</Badge>}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {reward.xpRequired.toLocaleString()} XP
                  </div>
                </div>
                <div className="flex flex-wrap gap-1">
                  {reward.benefits.map((benefit, benefitIndex) => (
                    <Badge key={benefitIndex} variant="outline" className="text-xs">
                      {benefit}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Metrics Dashboard */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-blue-500" />
            Monthly Performance
          </CardTitle>
          <CardDescription>
            Track your speaking opportunity metrics and progress
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-3">
            <div className="p-4 border border-border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm font-medium text-muted-foreground">Applications Submitted</p>
                {getChangeIndicator(monthlyMetrics.applicationsSubmitted, monthlyMetrics.applicationsLastMonth)}
              </div>
              <p className="text-2xl font-bold">{monthlyMetrics.applicationsSubmitted}</p>
              <p className="text-xs text-muted-foreground">vs {monthlyMetrics.applicationsLastMonth} last month</p>
            </div>

            <div className="p-4 border border-border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm font-medium text-muted-foreground">Response Rate</p>
                {getChangeIndicator(monthlyMetrics.responseRate, monthlyMetrics.responseRateLastMonth)}
              </div>
              <p className="text-2xl font-bold">{monthlyMetrics.responseRate}%</p>
              <p className="text-xs text-muted-foreground">vs {monthlyMetrics.responseRateLastMonth}% last month</p>
            </div>

            <div className="p-4 border border-border rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <p className="text-sm font-medium text-muted-foreground">Average Fee</p>
                {getChangeIndicator(4200, 3800)}
              </div>
              <p className="text-2xl font-bold">{monthlyMetrics.avgFee}</p>
              <p className="text-xs text-muted-foreground">vs {monthlyMetrics.avgFeeLastMonth} last month</p>
            </div>
          </div>

          <Separator className="my-4" />

          {/* Insights */}
          <div className="space-y-2">
            <h3 className="font-semibold text-sm">This Month's Insights</h3>
            <div className="space-y-2 text-sm text-muted-foreground">
              <p>• Your application rate increased 50% - great job staying active!</p>
              <p>• Response rates are up 7% - your profile optimizations are working.</p>
              <p>• Consider targeting more tech conferences to increase your average fee.</p>
            </div>
          </div>

          <Separator className="my-4" />

          {/* Weekly Digest Option */}
          <div className="flex items-center justify-between p-3 bg-surface rounded-lg">
            <div>
              <p className="text-sm font-medium">Weekly Digest Email</p>
              <p className="text-xs text-muted-foreground">Get your performance summary every Monday</p>
            </div>
            <Button variant="outline" size="sm">
              Subscribe
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}