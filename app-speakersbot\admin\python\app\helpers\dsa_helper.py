"""
DSA Helper Functions
Contains data science and analytics related helper functions for speaker matching and opportunity analysis.
"""

import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional
from dateutil.relativedelta import relativedelta

from app.helpers.database_manager import db_manager

from app.config.config import config
from app.models.opportunities import SpeakerOpportunity as Opportunity

# Logging setup
logger = logging.getLogger(__name__)

# Use centralized database manager
SessionLocal = db_manager.SessionLocal


class DSAHelper:
    """Helper class for DSA-related operations."""

    def __init__(self) -> None:
        # Use centralized database manager instead of creating new session
        self.SessionLocal = db_manager.SessionLocal

    def get_similar_2026_opportunities_mysql(self, current_opportunity_id: int) -> List[Dict[str, Any]]:
        """Get 10 similar 2026 opportunities from MySQL with same industry."""
        try:
            with db_manager.get_session() as session:
                # Get current opportunity to find its industry
                current_opp = session.query(Opportunity).filter_by(id=current_opportunity_id).first()
                if not current_opp:
                    logger.error(f"Opportunity {current_opportunity_id} not found in MySQL database")
                    return []
                
                if not current_opp.industry:
                    logger.warning(f"Opportunity {current_opportunity_id} has no industry field")
                    return []
                
                current_industry = current_opp.industry
                logger.info(f"Looking for 2026 opportunities in industry: {current_industry}")
                
                # Query MySQL for 2026 opportunities with same industry
                # First try exact 2026 filter
                similar_opportunities = session.query(Opportunity)\
                .filter(Opportunity.start_date.like('2026%'))\
                .filter(Opportunity.industry.like(f'%{current_industry}%'))\
                .filter(Opportunity.id != current_opportunity_id)\
                .limit(10)\
                .all()
                
                # If no 2026 opportunities found, try any opportunities with same industry
                if not similar_opportunities:
                    logger.info("No 2026 opportunities found, trying any opportunities with same industry")
                    similar_opportunities = session.query(Opportunity)\
                        .filter(Opportunity.industry.like(f'%{current_industry}%'))\
                        .filter(Opportunity.id != current_opportunity_id)\
                        .limit(10)\
                        .all()
                
                logger.info(f"Found {len(similar_opportunities)} similar 2026 opportunities from MySQL")
                
                # Debug: Log the first opportunity to see what data we have
                if similar_opportunities:
                    first_opp = similar_opportunities[0]
                    logger.info(f"Sample opportunity data: title='{first_opp.title}', url='{first_opp.event_url}', date='{first_opp.start_date}', city='{first_opp.city}', country='{first_opp.country}', industry='{first_opp.industry}'")
                
                # Format the results for PDF
                formatted_opportunities = []
                for opp in similar_opportunities:
                    # Format date and location
                    date_location = "TBD"
                    if opp.start_date:
                        if opp.city and opp.country:
                            date_location = f"{opp.start_date.strftime('%B %d, %Y')} | {opp.city}, {opp.country}"
                        else:
                            date_location = f"{opp.start_date.strftime('%B %d, %Y')}"
                    
                    formatted_opp = {
                        "title": opp.title or "Unknown Event",
                        "event_url": opp.event_url or opp.source_url or f"https://example.com/{opp.id}",
                        "date": date_location,
                        "relevance_message": f"This {current_industry} industry event is highly relevant for the speaker's expertise."
                    }
                    formatted_opportunities.append(formatted_opp)
                    logger.info(f"Formatted opportunity: {formatted_opp}")
                
                return formatted_opportunities
            
        except Exception as e:
            logger.error(f"Error getting similar 2026 opportunities from MySQL: {e}")
            return []

    def calculate_email_timing(self, event_start_date: Any) -> tuple[str, str]:
        """Calculate email send dates based on event start date."""
        # Default values
        current_dt = datetime.utcnow()
        event_date_minus_5_5_months = f"{current_dt.strftime('%B %d, %Y')} (immediate - event date not available)"
        event_date_minus_4_months = f"{current_dt.strftime('%B %d, %Y')} (immediate - event date not available)"
        
        if event_start_date:
            try:
                # Parse the event start date
                if isinstance(event_start_date, str):
                    # Handle different date formats
                    if 'T' in event_start_date:
                        event_dt = datetime.fromisoformat(event_start_date.replace('Z', '+00:00'))
                    else:
                        # Try parsing as date only
                        event_dt = datetime.strptime(event_start_date, '%Y-%m-%d')
                else:
                    event_dt = event_start_date
                
                # Calculate time remaining until event
                time_diff = event_dt - current_dt
                days_remaining = time_diff.days
                months_remaining = int(days_remaining / 30.44)  # Approximate months
                
                # Calculate email send dates based on time remaining
                if days_remaining > 180:  # More than 6 months
                    # Send first email in 1-2 months
                    email1_date = current_dt + relativedelta(months=1)
                    email2_date = current_dt + relativedelta(months=2)
                    event_date_minus_5_5_months = f"{email1_date.strftime('%B %d, %Y')} (1 month from now - {months_remaining} months until event)"
                    event_date_minus_4_months = f"{email2_date.strftime('%B %d, %Y')} (2 months from now - {months_remaining} months until event)"
                elif days_remaining > 90:  # 3-6 months
                    # Send emails closer together
                    email1_date = current_dt + relativedelta(weeks=2)
                    email2_date = current_dt + relativedelta(weeks=4)
                    event_date_minus_5_5_months = f"{email1_date.strftime('%B %d, %Y')} (2 weeks from now - {months_remaining} months until event)"
                    event_date_minus_4_months = f"{email2_date.strftime('%B %d, %Y')} (1 month from now - {months_remaining} months until event)"
                elif days_remaining > 30:  # 1-3 months
                    # Send emails very soon
                    email1_date = current_dt + relativedelta(days=3)
                    email2_date = current_dt + relativedelta(weeks=1)
                    event_date_minus_5_5_months = f"{email1_date.strftime('%B %d, %Y')} (3 days from now - {days_remaining} days until event)"
                    event_date_minus_4_months = f"{email2_date.strftime('%B %d, %Y')} (1 week from now - {days_remaining} days until event)"
                else:  # Less than 1 month
                    # Send immediately
                    event_date_minus_5_5_months = f"{current_dt.strftime('%B %d, %Y')} (immediate - {days_remaining} days until event)"
                    event_date_minus_4_months = f"{current_dt.strftime('%B %d, %Y')} (immediate - {days_remaining} days until event)"
                    
            except Exception as e:
                logger.warning(f"Could not parse event start date {event_start_date}: {e}")
                # Keep default values which are already set above

        return event_date_minus_5_5_months, event_date_minus_4_months

    def sanitize_json_strings(self, text: str) -> str:
        """Sanitize illegal newlines inside JSON strings."""
        result_chars = []
        in_string = False
        escape = False
        for i, ch in enumerate(text):
            if in_string:
                if escape:
                    result_chars.append(ch)
                    escape = False
                else:
                    if ch == '\\':
                        result_chars.append(ch)
                        escape = True
                    elif ch == '"':
                        result_chars.append(ch)
                        in_string = False
                    elif ch in ('\n', '\r'):
                        # replace raw newline inside string with space
                        result_chars.append(' ')
                    else:
                        result_chars.append(ch)
            else:
                result_chars.append(ch)
                if ch == '"':
                    in_string = True
                    escape = False
        return ''.join(result_chars)

    def fix_incomplete_strings(self, text: str) -> str:
        """Fix incomplete strings in JSON."""
        # Find incomplete strings (strings that don't have closing quotes)
        lines = text.split('\n')
        fixed_lines = []
        in_incomplete_string = False
        
        for line in lines:
            if in_incomplete_string:
                # If we're in an incomplete string, try to close it
                if '"' in line:
                    # Find the first quote and close the string
                    quote_pos = line.find('"')
                    fixed_line = line[:quote_pos] + '"' + line[quote_pos:]
                    in_incomplete_string = False
                else:
                    # No quote found, add a closing quote at the end
                    fixed_line = line + '"'
                fixed_lines.append(fixed_line)
            else:
                # Check if this line starts an incomplete string
                if '": "' in line and not line.strip().endswith('"'):
                    in_incomplete_string = True
                    # Try to close the string on the same line
                    if '"' in line:
                        quote_pos = line.rfind('"')
                        if quote_pos > line.find('": "'):
                            in_incomplete_string = False
                fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)

    def fix_truncated_json(self, text: str) -> str:
        """Fix truncated JSON by adding missing closing braces and brackets."""
        text = text.strip()
        
        # Count opening and closing braces/brackets
        open_braces = text.count('{')
        close_braces = text.count('}')
        open_brackets = text.count('[')
        close_brackets = text.count(']')
        
        # Add missing closing braces
        missing_braces = open_braces - close_braces
        if missing_braces > 0:
            text += '}' * missing_braces
            logger.warning(f"Added {missing_braces} missing closing braces to JSON")
        
        # Add missing closing brackets
        missing_brackets = open_brackets - close_brackets
        if missing_brackets > 0:
            text += ']' * missing_brackets
            logger.warning(f"Added {missing_brackets} missing closing brackets to JSON")
        
        return text
    
    def fix_extra_data_error(self, text: str) -> str:
        """Fix 'Extra data' JSON error by trimming to last complete object."""
        # Find the last complete JSON object by finding the last closing brace
        last_brace = text.rfind('}')
        if last_brace != -1:
            # Check if there's content after the last brace
            after_brace = text[last_brace + 1:].strip()
            if after_brace:
                logger.warning(f"Found extra data after JSON: '{after_brace[:100]}...'")
                # Trim to the last complete closing brace
                text = text[:last_brace + 1]
                logger.info("Trimmed JSON to remove extra data")
        
        return text

    def nullify_empty_values(self, obj: Any) -> Any:
        """Recursively nullify empty strings and empty arrays."""
        if obj is None:
            return None
        if isinstance(obj, str):
            s = obj.strip()
            return None if s == "" or s.lower() in {"tbd", "n/a", "na", "none"} else obj
        if isinstance(obj, list):
            if len(obj) == 0:
                return None
            return [self.nullify_empty_values(x) for x in obj]
        if isinstance(obj, dict):
            if not obj:
                return None
            return {k: self.nullify_empty_values(v) for k, v in obj.items()}
        return obj

    def format_similar_opportunities_for_prompt(self, similar_opportunities: List[Dict[str, Any]]) -> str:
        """Format similar opportunities for the LLM prompt."""
        if not similar_opportunities:
            return "[]"
        
        # Format opportunities for the LLM prompt
        formatted_opportunities = []
        for opp in similar_opportunities:
            formatted_opp = {
                "title": opp.get("title", "Unknown Event"),
                "event_url": opp.get("event_url", "https://..."),
                "date": opp.get("date", "..."),
                "relevance_message": opp.get("relevance_message", "...")
            }
            formatted_opportunities.append(formatted_opp)
        
        similar_opportunities_json = json.dumps(formatted_opportunities, indent=2, default=str)
        logger.info(f"Formatted {len(formatted_opportunities)} similar opportunities for LLM prompt")
        return similar_opportunities_json

    def create_fallback_data_structure(self, speaker_id: int, opportunity_id: int) -> Dict[str, Any]:
        """Create fallback data structure when JSON parsing fails."""
        return {
            "Top 3 Attendee Challenges": {
                "Challenge #1": {"title": "Industry Challenge 1", "description": "Generic challenge description"},
                "Challenge #2": {"title": "Industry Challenge 2", "description": "Generic challenge description"},
                "Challenge #3": {"title": "Industry Challenge 3", "description": "Generic challenge description"}
            },
            "SWOT Analysis for [ORGANIZATION_NAME]": {
                "Strengths": ["Established reputation", "Strong network"],
                "Weaknesses": ["Limited reach", "High competition"],
                "Opportunities": ["Growing demand", "New partnerships"],
                "Threats": ["Economic uncertainty", "Market changes"]
            },
            "SMART Analysis": {
                "Specific": "Generic specific goal",
                "Measurable": "Generic measurable outcome",
                "Attainable": "Generic attainable target",
                "Relevant": "Generic relevant objective",
                "Timely": "Generic timely completion"
            },
            "Previous Two Annual [ORGANIZATION_NAME] Conferences – Keynote & Breakout Speaker Research": {
                "2025 Keynote Speakers": [{"name": "Sample Speaker", "topic": "Sample Topic", "estimated_fee_range": "$5,000 - $10,000"}],
                "2025 Breakouts": [{"name": "Sample Speaker", "estimated_fee_range": "$2,000 - $5,000"}],
                "2024 Keynote Speakers": [{"name": "Sample Speaker", "topic": "Sample Topic", "estimated_fee_range": "$5,000 - $10,000"}],
                "2024 Breakouts": [{"name": "Sample Speaker", "estimated_fee_range": "$2,000 - $5,000"}]
            },
            "Introduction Email": {
                "Subject Line": "Clear + benefit-driven (8–12 words)",
                "Body": "Generic email body content"
            },
            "Follow-Up Email Sequence": {
                "Email 1: Intro": {
                    "Send": "Immediately after gig acceptance",
                    "Subject Line": "Clear + benefit-driven (8–12 words)",
                    "Body": "Generic intro email body content with speaker phone number"
                },
                "Email 2: Value Reminder": {
                    "Send": "~1 week after Email 1",
                    "Subject Line": "[Speaker Name] + benefit statement (≤12 words)",
                    "Body": "Generic value reminder email body content"
                },
                "Email 3: Program Fit Check-In": {
                    "Send": "~60 days before event",
                    "Subject Line": "Content alignment for [Event Name]",
                    "Body": "Generic program fit check-in email body content"
                },
                "Email 4: Last Call": {
                    "Send": "~30 days after Program Fit Check-In if no response",
                    "Subject Line": "Last call to finalize [Speaker Name] for [Event Name]",
                    "Body": "Generic last call email body content"
                }
            },
            "20–30 Second Voicemail Script": "Generic voicemail script content",
            "Customized RFP": {
                "Proposed Keynote Title": "Generic Title",
                "Short Description": "Generic short description",
                "Detailed Description": "Generic detailed description",
                "Learning Objectives": ["Objective 1", "Objective 2", "Objective 3", "Objective 4", "Objective 5"],
                "Attendee Benefits": ["Benefit 1", "Benefit 2", "Benefit 3", "Benefit 4", "Benefit 5"],
                "Key Challenges": ["Challenge 1", "Challenge 2", "Challenge 3", "Challenge 4", "Challenge 5"],
                "Speaker Bio (Tailored)": "Generic speaker bio",
                "Speaker Bio (Long)": "Generic long speaker bio"
            },
            "500-Word New York Times-Style Article": {
                "Title": "Generic Article Title",
                "Byline": "[SPEAKER_NAME]",
                "Body": "Generic article body content"
            },
            "_metadata": {
                "speaker_id": speaker_id,
                "opportunity_id": opportunity_id,
                "error": "invalid_json_from_llm",
                "generated_at": datetime.utcnow().isoformat(),
            },
        }
