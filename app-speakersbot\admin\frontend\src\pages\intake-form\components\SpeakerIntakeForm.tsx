import ButtonLoader from "@/components/common/ButtonLoader";
import FileUpload from "@/components/common/FileUpload";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Card } from "@/components/ui/card";
import { Combobox } from "@/components/ui/combobox";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { toast } from "@/components/ui/use-toast";
import {
  DeleteOutlined,
  EditOutlined,
  PlusOutlined,
  SaveOutlined,
  SettingOutlined,
  StarOutlined,
  TrophyOutlined,
  UploadOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { format } from "date-fns";
import dayjs from "dayjs";
import { CalendarIcon } from "lucide-react";
import React, { useState } from "react";
import { DndProvider } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { useForm } from "react-hook-form";
import {
  useCreateFormMutation,
  useDeleteFormFieldMutation,
  useDeleteFormMutation,
  useGetFormFieldsQuery,
  useGetFormsWithPrioritiesQuery,
  useUpdateFormMutation,
  useUpdateFormPriorityMutation,
} from "../../../apis/formApi";
import { useAppState } from "../../../state/AppStateProvider";
import { useAuth } from "../../../state/AuthContext";
import type { SpeakerIntakeData, UserRole } from "../../../types";
import { getPermissions } from "../../../utils/permissions";
import DraggableFormSection from "./DraggableFormSection";
import { Label } from "@/components/ui/label";
import {
  useFetchScrapingCategoriesQuery,
  useGetScrapingSubCategoriesQuery,
} from "../../../apis/scrapingApi";

// Skeleton component for form steps
const FormStepsSkeleton: React.FC = () => {
  return (
    <div className="speaker-intake-form">
      <Card className="bg-tertiary rounded-lg p-6">
        <div className="flex justify-between items-start mb-6">
          <div className="text-center flex-1">
            <Skeleton className="h-8 w-64 mx-auto mb-2" />
            <Skeleton className="h-4 w-96 mx-auto" />
          </div>
          <div className="ml-4">
            <Skeleton className="h-10 w-24" />
          </div>
        </div>

        <div className="form-sections mb-6">
          {/* Skeleton for 6 form sections */}
          {Array.from({ length: 6 }).map((_, index) => (
            <div key={index} className="mb-4">
              <Card className="bg-tertiary/20 border-dashboard-light/20">
                <div className="p-4">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-4 w-4" />
                      <Skeleton className="h-4 w-4" />
                      <Skeleton className="h-6 w-6" />
                      <Skeleton className="h-6 w-32" />
                    </div>
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-8 w-8" />
                      <Skeleton className="h-8 w-8" />
                      <Skeleton className="h-8 w-20" />
                    </div>
                  </div>
                </div>
              </Card>
            </div>
          ))}
        </div>

        <div className="text-center mt-6 mb-0">
          <Skeleton className="h-10 w-32 mx-auto" />
        </div>
      </Card>
    </div>
  );
};

// Fetch and render fields for a given form id
const RemoteFormFields: React.FC<{
  formId: number;
  renderItem: (
    name: string,
    label: string,
    children: React.ReactNode,
    colSpan?: number,
    required?: boolean,
    field?: any
  ) => React.ReactNode;
  renderInput: (field: any) => React.ReactNode;
  onFieldUpdate?: () => void;
}> = React.memo(({ formId, renderItem, renderInput, onFieldUpdate }) => {
  const {
    data,
    isLoading: isLoadingFormFields,
    error,
    refetch: refetchFormFields,
  } = useGetFormFieldsQuery(formId, {
    refetchOnMountOrArgChange: false,
    refetchOnFocus: false,
    refetchOnReconnect: false,
  } as any);

  // Listen for field update events and refetch
  React.useEffect(() => {
    const handleFieldUpdate = () => {
      refetchFormFields();
      if (onFieldUpdate) {
        onFieldUpdate();
      }
    };

    window.addEventListener("fieldUpdated", handleFieldUpdate);
    return () => {
      window.removeEventListener("fieldUpdated", handleFieldUpdate);
    };
  }, [refetchFormFields, onFieldUpdate]);

  if (isLoadingFormFields) {
    return (
      <div className="py-4 text-sm text-muted-foreground grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
        <Skeleton className="w-full h-10 rounded-lg" />
        <Skeleton className="w-full h-10 rounded-lg" />
        <Skeleton className="w-full h-10 rounded-lg" />
        <Skeleton className="w-full h-10 rounded-lg" />
      </div>
    );
  }
  if (error) {
    return (
      <div className="py-4 text-sm text-red-500">Failed to load fields.</div>
    );
  }
  // API returns: { data: { id, title, priority, questions: [...] } }
  const questions = (data as any)?.data?.questions || [];
  if (!Array.isArray(questions) || questions.length === 0) {
    return null;
  }

  // Normalize API question into local field model expected by renderInput/renderItem
  const normalizedFields = questions.map((q: any) => {
    // Keep the original field_type from API for form compatibility
    const originalType = q.field_type || "";
    let localType = originalType;

    // Only normalize for internal rendering, but keep original for form editing
    const normalizedType = String(originalType).toLowerCase();

    // map known API types to local input types for rendering
    if (normalizedType === "text") localType = "text";
    if (normalizedType === "textarea") localType = "textarea";
    if (normalizedType === "number") localType = "number";
    if (normalizedType === "select") localType = "select";
    if (normalizedType === "multi-select" || normalizedType === "multiselect")
      localType = "multiselect";
    if (normalizedType === "multi-text" || normalizedType === "multitext")
      localType = "multi-text";
    if (normalizedType === "radio") localType = "radio";
    if (normalizedType === "switch" || normalizedType === "boolean")
      localType = "switch";
    if (
      normalizedType === "file-upload" ||
      normalizedType === "file" ||
      normalizedType === "file_upload"
    )
      localType = "upload";
    if (normalizedType === "date") localType = "date";

    // options might be array or comma-separated; normalize to comma string used by renderInput
    let options: string | undefined = undefined;
    const rawOptions = q.options;
    if (Array.isArray(rawOptions)) {
      options = rawOptions.join(" | ");
    } else if (typeof rawOptions === "string") {
      options = rawOptions;
    }

    return {
      id: q.id,
      name: q.field_id || `field_${q.id}`,
      label: q.question || `Field ${q.id}`,
      type: localType,
      originalType: originalType, // Keep original API field_type for form editing
      placeholder: q.placeholder || undefined,
      required: Boolean(q.is_required),
      options,
      is_deletable: q.is_deletable, // Include is_deletable from API
      original: q,
    };
  });

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
      {normalizedFields.map((field: any) => {
        const colSpan = field.type === "textarea" ? 24 : 12;
        return (
          <React.Fragment key={field.id || field.name}>
            {renderItem(
              field.name || `field_${field.id}`,
              field.label || "Field",
              renderInput(field),
              colSpan,
              Boolean(field.required),
              field
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
});

interface FormSection {
  id: string;
  title: string;
  icon: React.ReactNode;
  order: number;
  is_deletable?: string;
  originalForm?: any;
}

interface SpeakerIntakeFormProps {
  onSubmit?: (
    data: SpeakerIntakeData & { name: string; email: string }
  ) => void;
  initialValues?: Partial<SpeakerIntakeData & { name: string; email: string }>;
  isEdit?: boolean;
  onAddField?: (section?: { id: string; title: string }) => void;
  onEditField?: (field: any) => void;
  onDeleteField?: (fieldId: string) => void; // Optional callback for external delete handling
  onFieldUpdate?: () => void; // Callback to trigger refetch after field operations
  isDeletingField?: boolean; // Loading state for delete operation (external)
}

const SpeakerIntakeForm: React.FC<SpeakerIntakeFormProps> = ({
  onSubmit,
  initialValues,
  isEdit = false,
  onAddField,
  onEditField,
  onDeleteField,
  onFieldUpdate,
  isDeletingField = false,
}) => {
  const form = useForm<any>({ defaultValues: initialValues || {} });
  const stepFormRHF = useForm<any>({ defaultValues: {} });
  const [loading, setLoading] = useState(false);
  const [activeKeys, setActiveKeys] = useState<string[]>(["personal"]);
  const [isStepModalVisible, setIsStepModalVisible] = useState(false);
  const [isEditStepModalVisible, setIsEditStepModalVisible] = useState(false);
  const [editingStep, setEditingStep] = useState<any>(null);
  const [openDeleteFieldId, setOpenDeleteFieldId] = useState<string | null>(
    null
  );
  const { dispatch, intakeForm } = useAppState();
  const { user } = useAuth();
  const permissions = getPermissions(user?.roleId as unknown as UserRole);

  // Fetch scraping categories for Primary Category combobox
  const { data: scrapingCategoriesData } = useFetchScrapingCategoriesQuery(
    undefined as any,
    {
      refetchOnMountOrArgChange: false,
      refetchOnFocus: false,
      refetchOnReconnect: false,
    } as any
  );

  // State for subcategory infinite scroll
  const [subcategoryPage, setSubcategoryPage] = useState(1);
  const [subcategoryOptions, setSubcategoryOptions] = useState<any[]>([]);
  const [hasMoreSubcategories, setHasMoreSubcategories] = useState(true);
  const [isLoadingMoreSubcategories, setIsLoadingMoreSubcategories] =
    useState(false);

  // Fetch subcategories with pagination
  const { data: subcategoriesData, isLoading: subcategoriesLoading } =
    useGetScrapingSubCategoriesQuery(
      {
        page: subcategoryPage,
        limit: 50, // Load 50 items per page
      },
      {
        refetchOnMountOrArgChange: false,
        refetchOnFocus: false,
        refetchOnReconnect: false,
      }
    );

  // Handle subcategory data updates
  React.useEffect(() => {
    if (subcategoriesData?.data) {
      const newSubcategories = subcategoriesData.data;
      if (subcategoryPage === 1) {
        setSubcategoryOptions(newSubcategories);
      } else {
        setSubcategoryOptions((prev) => [...prev, ...newSubcategories]);
      }

      // Check if there are more pages
      const totalPages = Math.ceil(
        (subcategoriesData.pagination?.total || 0) / 50
      );
      setHasMoreSubcategories(subcategoryPage < totalPages);
      setIsLoadingMoreSubcategories(false);
    }
  }, [subcategoriesData, subcategoryPage]);

  // Load more subcategories function
  const loadMoreSubcategories = React.useCallback(() => {
    if (hasMoreSubcategories && !isLoadingMoreSubcategories) {
      setIsLoadingMoreSubcategories(true);
      setSubcategoryPage((prev) => prev + 1);
    }
  }, [hasMoreSubcategories, isLoadingMoreSubcategories]);

  // Fetch forms with priorities from API
  const {
    data: formsData,
    isLoading: formsLoading,
    error: formsError,
    refetch: refetchForms,
  } = useGetFormsWithPrioritiesQuery(undefined, {
    refetchOnMountOrArgChange: true,
  });

  // Map API response to form sections
  const getIconForForm = (title: string) => {
    const titleLower = title.toLowerCase();
    if (titleLower.includes("core") || titleLower.includes("phase")) {
      return <UserOutlined className="mr-2" />;
    }
    if (titleLower.includes("psychometric")) {
      return <TrophyOutlined className="mr-2" />;
    }
    if (titleLower.includes("media") || titleLower.includes("upload")) {
      return <UploadOutlined className="mr-2" />;
    }
    if (titleLower.includes("evergreen") || titleLower.includes("vault")) {
      return <StarOutlined className="mr-2" />;
    }
    return <SettingOutlined className="mr-2" />;
  };

  // Convert API data to form sections (memoized to avoid new array each render)
  const apiFormSections: FormSection[] = React.useMemo(() => {
    return (
      formsData?.data?.map((form: any) => ({
        id: `form_${form.id}`,
        title: form.title,
        icon: getIconForForm(form.title),
        order: form.priority,
        is_deletable: form.is_deletable,
        originalForm: form, // Keep reference to original form data
      })) || []
    );
  }, [formsData]);

  // Form sections with their order - use API data or fallback to default
  const [formSections, setFormSections] = useState<FormSection[]>([]);

  // Initialize formSections once when API data arrives (avoid infinite loop)
  React.useEffect(() => {
    if (
      !formsLoading &&
      !formsError &&
      formSections.length === 0 &&
      apiFormSections.length > 0
    ) {
      setFormSections(apiFormSections);
    }
  }, [formsLoading, formsError, apiFormSections, formSections.length]);

  // Trigger field update callback when forms data changes
  React.useEffect(() => {
    if (onFieldUpdate && formsData) {
      onFieldUpdate();
    }
  }, [formsData, onFieldUpdate]);

  // Listen for field update events and trigger refetch
  React.useEffect(() => {
    const handleFieldUpdate = () => {
      refetchForms();
    };

    window.addEventListener("fieldUpdated", handleFieldUpdate);
    return () => {
      window.removeEventListener("fieldUpdated", handleFieldUpdate);
    };
  }, [refetchForms]);

  const [updateFormPriority] = useUpdateFormPriorityMutation();
  const [createForm, { isLoading: isCreatingForm }] = useCreateFormMutation();
  const [updateForm, { isLoading: isUpdatingForm }] = useUpdateFormMutation();
  const [deleteForm, { isLoading: isDeletingForm }] = useDeleteFormMutation();
  const [deleteFormField, { isLoading: isDeletingFieldMutation }] =
    useDeleteFormFieldMutation();

  const moveSection = (dragIndex: number, hoverIndex: number) => {
    const draggedSection = formSections[dragIndex];
    const newSections = [...formSections];
    newSections.splice(dragIndex, 1);
    newSections.splice(hoverIndex, 0, draggedSection);

    // Update order values
    const updatedSections = newSections.map((section, index) => ({
      ...section,
      order: index + 1,
    }));

    setFormSections(updatedSections);
  };

  const commitSectionOrder = async () => {
    try {
      const priorities = formSections
        .filter((s) => String(s.id).startsWith("form_"))
        .map((s) => ({
          id: Number(String(s.id).replace("form_", "")),
          priority: s.order,
        }));
      if (priorities.length > 0) {
        await updateFormPriority({ priorities } as any).unwrap();
        // Refetch forms to get updated data
        await refetchForms();
        toast({ description: "Order saved successfully!" });
      }
    } catch (e) {
      console.error("Failed to update priorities", e);
      toast({ description: "Failed to save order", variant: "destructive" });
    }
  };

  const handleCollapseChange = (keys: string | string[]) => {
    setActiveKeys(Array.isArray(keys) ? keys : [keys]);
  };

  const handleStepSubmit = async (values: any) => {
    try {
      // Create form on backend
      const response = await createForm({
        title: values.stepName,
        priority: formSections.length + 1,
      }).unwrap();

      // Refetch forms to get updated data from API
      const updatedFormsData = await refetchForms();
      if (updatedFormsData.data) {
        const newFormSections =
          updatedFormsData.data.data?.map((form: any) => ({
            id: `form_${form.id}`,
            title: form.title,
            icon: getIconForForm(form.title),
            order: form.priority,
            is_deletable: form.is_deletable,
            originalForm: form,
          })) || [];
        setFormSections(newFormSections);
      }

      // Trigger field update event for other components
      window.dispatchEvent(new CustomEvent("fieldUpdated"));

      toast({ description: `Step added successfully` });
      setIsStepModalVisible(false);
      stepFormRHF.reset();
    } catch (error) {
      console.error("Failed to create step:", error);
      toast({
        description: "Failed to create step. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteField = async (fieldId: string) => {
    try {
      // For API fields, call the delete mutation
      await deleteFormField(fieldId).unwrap();

      // Refetch forms to get updated data
      await refetchForms();

      // Trigger field update event for other components
      window.dispatchEvent(new CustomEvent("fieldUpdated"));

      toast({ description: "Field deleted successfully" });
    } catch (error) {
      console.error("Failed to delete field:", error);
      toast({
        description: "Failed to delete field. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleEditStep = (step: any) => {
    setEditingStep(step);
    stepFormRHF.setValue("stepName", step.title);
    setIsEditStepModalVisible(true);
  };

  const handleUpdateStep = async (values: any) => {
    if (!editingStep) return;

    try {
      await updateForm({
        id: editingStep.id,
        title: values.stepName,
      }).unwrap();

      // Refetch forms to get updated data
      const updatedFormsData = await refetchForms();
      if (updatedFormsData.data) {
        const newFormSections =
          updatedFormsData.data.data?.map((form: any) => ({
            id: `form_${form.id}`,
            title: form.title,
            icon: getIconForForm(form.title),
            order: form.priority,
            is_deletable: form.is_deletable,
            originalForm: form,
          })) || [];
        setFormSections(newFormSections);
      }

      // Trigger field update event for other components
      window.dispatchEvent(new CustomEvent("fieldUpdated"));

      toast({ description: `Step updated successfully` });
      setIsEditStepModalVisible(false);
      setEditingStep(null);
      stepFormRHF.reset();
    } catch (error) {
      console.error("Failed to update step:", error);
      toast({
        description: "Failed to update step. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDeleteStep = async (step: any) => {
    try {
      await deleteForm(step.id).unwrap();

      // Refetch forms to get updated data
      const updatedFormsData = await refetchForms();
      if (updatedFormsData.data) {
        const newFormSections =
          updatedFormsData.data.data?.map((form: any) => ({
            id: `form_${form.id}`,
            title: form.title,
            icon: getIconForForm(form.title),
            order: form.priority,
            is_deletable: form.is_deletable,
            originalForm: form,
          })) || [];
        setFormSections(newFormSections);
      }

      // Trigger field update event for other components
      window.dispatchEvent(new CustomEvent("fieldUpdated"));

      toast({ description: `Step "${step.title}" deleted successfully` });
    } catch (error) {
      console.error("Failed to delete step:", error);
      toast({
        description: "Failed to delete step. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleSubmit = async (values: any) => {
    if (!values.name || !values.email) {
      toast({
        description: "Please fill in the required fields",
        variant: "destructive",
      });
      return;
    }

    setLoading(true);
    try {
      const submissionData = {
        ...values,
        id: Date.now().toString(),
        submittedAt: dayjs().toISOString(),
        status: "pending",
        type: "speaker-intake",
      };

      // Save to state
      dispatch({
        type: "ADD_ACTIVITY_LOG",
        payload: {
          id: Date.now().toString(),
          userId: "system",
          action: "Speaker Application Submitted",
          details: `New speaker application from ${values.name}`,
          timestamp: dayjs().toISOString(),
        },
      });

      if (onSubmit) {
        onSubmit(submissionData);
      } else {
        toast({ description: "Application submitted successfully!" });
      }
    } catch (error) {
      console.error("Submission error:", error);
      toast({
        description: "Failed to submit application. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  // Function to render form item with admin controls
  function renderFormItemWithControls(
    name: string,
    label: string,
    children: React.ReactNode,
    colSpan: number = 12,
    required: boolean = false,
    field?: any
  ) {
    const canShowControls = permissions.canEdit;

    return (
      <div className="col-span-1 md:col-span-24" key={name}>
        <div className="relative group">
          <FormField
            control={form.control}
            name={name}
            rules={{
              required: required ? `${label} is required` : false,
              pattern:
                name === "email"
                  ? {
                      value:
                        /^(?:[a-zA-Z0-9_'^&+\-])+(?:\.(?:[a-zA-Z0-9_'^&+\-])+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/,
                      message: "Please enter a valid email",
                    }
                  : undefined,
            }}
            render={({ field: rhfField }) => (
              <FormItem>
                <FormLabel>
                  <div className="flex justify-between items-center w-full">
                    <p className="text-sm leading-relaxed flex items-start gap-1">
                      {required && <span className="text-red-500 mr-1">*</span>}
                      <span>{label}</span>
                    </p>
                    {canShowControls && (
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <div className="flex items-center gap-2">
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    if (onEditField) {
                                      if (field) {
                                        onEditField(field);
                                      } else {
                                        const defaultField = {
                                          id: `default_${name}`,
                                          name: name,
                                          label: label,
                                          type: "text",
                                          required: required,
                                          isDefault: true,
                                        };
                                        onEditField(defaultField);
                                      }
                                    }
                                  }}
                                  className="text-blue-500 hover:text-blue-700"
                                >
                                  <EditOutlined />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>Edit field</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          {(!field || field.is_deletable !== "0") && (
                            <AlertDialog
                              open={
                                openDeleteFieldId ===
                                (field ? String(field.id) : `default_${name}`)
                              }
                              onOpenChange={(open) => {
                                const id = field
                                  ? String(field.id)
                                  : `default_${name}`;
                                setOpenDeleteFieldId(open ? id : null);
                              }}
                            >
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-red-500 hover:text-red-700"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    const id = field
                                      ? String(field.id)
                                      : `default_${name}`;
                                    setOpenDeleteFieldId(id);
                                  }}
                                >
                                  <DeleteOutlined />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>
                                    Delete field
                                  </AlertDialogTitle>
                                  <AlertDialogDescription>
                                    {`Are you sure you want to delete "${label}"?`}
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      setOpenDeleteFieldId(null);
                                    }}
                                  >
                                    Cancel
                                  </AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const id = field
                                        ? String(field.id)
                                        : `default_${name}`;
                                      // Close immediately, then perform delete
                                      setOpenDeleteFieldId(null);
                                      handleDeleteField(id);
                                    }}
                                    disabled={
                                      isDeletingField || isDeletingFieldMutation
                                    }
                                  >
                                    {isDeletingField || isDeletingFieldMutation
                                      ? "Deleting..."
                                      : "Delete"}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </FormLabel>
                <FormControl>
                  {React.isValidElement(children)
                    ? React.cloneElement(children as any, {
                        value: rhfField.value,
                        onChange: rhfField.onChange,
                        onValueChange: rhfField.onChange,
                        onBlur: rhfField.onBlur,
                        name: rhfField.name,
                      })
                    : children}
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    );
  }

  // Helper function to render different field types
  function renderFieldInput(field: any) {
    switch (field.type) {
      case "text":
      case "email":
      case "url":
        return (
          <Input
            className="bg-input-secondary border-dashboard-light/40"
            placeholder={field.placeholder || ""}
            type={field.type}
          />
        );

      case "textarea":
        return (
          <Textarea
            className="bg-input-secondary border-dashboard-light/40"
            rows={4}
            placeholder={field.placeholder || ""}
          />
        );

      case "number":
        return (
          <Input
            type="number"
            className="w-full bg-input-secondary border-dashboard-light/40 "
            placeholder={field.placeholder || ""}
          />
        );

      case "select":
        let selectOptions = field.options
          ? field.options.split(" | ").map((opt: string) => opt.trim())
          : [];

        if (field.label === "Primary Category") {
          const apiCategories = (scrapingCategoriesData as any)?.data || [];
          if (Array.isArray(apiCategories) && apiCategories.length > 0) {
            selectOptions = apiCategories.map((c: any) => c.name);
          }
        } else if (field.label === "Subcategory") {
          // Use subcategory options from API with infinite scroll
          selectOptions = subcategoryOptions.map((sub: any) => sub.name);
        }

        const comboboxOptions = selectOptions.map((option: string) => ({
          value: option,
          label: option,
        }));

        return (
          <Combobox
            options={comboboxOptions}
            placeholder={
              field.placeholder || `Select ${field.label.toLowerCase()}`
            }
            searchPlaceholder="Search options..."
            emptyText="No options found."
            className="bg-input-secondary border-dashboard-light/40 "
            {...(field.label === "Subcategory" &&
              ({
                onScrollToBottom: loadMoreSubcategories,
                hasMore: hasMoreSubcategories,
                isLoadingMore: isLoadingMoreSubcategories,
              } as any))}
          />
        );

      case "multiselect":
        const multiOptions = field.options
          ? field.options.split(" | ").map((opt: string) => opt.trim())
          : [];
        return (
          <div className="flex flex-wrap gap-x-3 gap-y-4 !mt-3 bg-muted rounded-lg p-3.5">
            {multiOptions.map((opt: string) => (
              <div key={opt} className="flex items-center space-x-2">
                <Checkbox
                  id={`${field.name}-${opt}`}
                  value={opt}
                  checked={field.value?.includes(opt) || false}
                  {...(field.onChange && { onCheckedChange: field.onChange })}
                />
                <Label
                  htmlFor={`${field.name}-${opt}`}
                  className="text-sm font-medium leading-none cursor-pointer"
                >
                  {opt}
                </Label>
              </div>
            ))}
          </div>
        );

      case "multi-text":
        if (field.original?.options?.length > 0) {
          return (
            <div className="flex flex-wrap gap-4 border-dashboard-light/40 ">
              {field.original?.options.map((opt: string, index: number) => (
                <Input
                  key={index}
                  className="bg-input-secondary border-dashboard-light/40 "
                  placeholder={field.placeholder || ""}
                  type="text"
                />
              ))}
            </div>
          );
        }
        // Fallback to radio buttons if no options are available
        const multiTextOptions = field.options
          ? field.options.split(" | ").map((opt: string) => opt.trim())
          : [];
        return (
          <RadioGroup className="flex flex-wrap gap-x-3 gap-y-4 !mt-3 bg-muted rounded-lg p-3.5">
            {multiTextOptions.map((option, index) => (
              <div key={index} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={`${field.name}-${index}`} />
                <Label
                  htmlFor={`${field.name}-${index}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {option}
                </Label>
              </div>
            ))}
          </RadioGroup>
        );
      case "radio":
        const radioOptions = field.options
          ? field.options.split(" | ").map((opt: string) => opt.trim())
          : [];
        return (
          <RadioGroup
            className="flex flex-wrap gap-x-3 gap-y-4 !mt-3 bg-muted rounded-lg p-3.5"
            {...(field.onChange && { onValueChange: field.onChange })}
          >
            {radioOptions.map((opt: string) => (
              <div key={opt} className="flex items-center space-x-2">
                <RadioGroupItem
                  value={opt}
                  id={`${field.name}-${opt}`}
                  className="cursor-pointer"
                />
                <Label
                  htmlFor={`${field.name}-${opt}`}
                  className="text-sm font-medium leading-none cursor-pointer"
                >
                  {opt}
                </Label>
              </div>
            ))}
          </RadioGroup>
        );

      case "switch":
        return <Switch />;

      case "upload":
        return (
          <FileUpload
            onChange={() => {}}
            accept="image/*"
            label="Upload File"
          />
        );

      case "date":
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                data-empty={!field.value}
                className="data-[empty=true]:text-dashboard-light justify-start text-left font-normal w-full bg-input-secondary border-dashboard-light/40 hover:!text-accent-foreground"
              >
                <CalendarIcon />
                {field.value ? (
                  format(field.value, "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={field.value}
                captionLayout="dropdown"
                onSelect={field.onChange}
              />
            </PopoverContent>
          </Popover>
        );

      default:
        return (
          <Input
            className="bg-input-secondary border-dashboard-light/40 "
            placeholder={
              field.placeholder || `Enter ${field.label.toLowerCase()}`
            }
          />
        );
    }
  }

  // Show loading state while fetching forms
  if (formsLoading) {
    return <FormStepsSkeleton />;
  }

  // Show error state if API fails
  if (formsError) {
    return (
      <div className="speaker-intake-form">
        <Card className="bg-tertiary rounded-lg p-6">
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <p className="text-red-500 mb-4">Failed to load form sections</p>
              <p className="text-muted-foreground text-sm">
                Please try refreshing the page
              </p>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  // Show message when no form sections are available
  if (!formsLoading && !formsError && formSections.length === 0) {
    return (
      <div className="speaker-intake-form">
        <Card className="bg-tertiary rounded-lg p-6">
          <div className="flex justify-center items-center h-64">
            <div className="text-center">
              <p className="text-muted-foreground mb-4">
                No form sections available
              </p>
              <p className="text-muted-foreground text-sm">
                Please contact your administrator to set up form sections
              </p>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="speaker-intake-form ">
        <Card className="bg-tertiary rounded-lg p-6">
          <div className="flex justify-between items-start mb-6">
            <div className="text-center flex-1">
              <h2 className="mb-2 text-2xl font-semibold">
                Speaker STAiGENT Identity Profile
              </h2>
              <p className="mt-3 block text-sm text-muted-foreground">
                Drag sections to reorder them according to your preference
              </p>
            </div>
            <div className="ml-4">
              <Button onClick={() => setIsStepModalVisible(true)}>
                <PlusOutlined className="mr-2" />
                Add Step
              </Button>
            </div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)}>
              <div className="form-sections mb-6">
                {formSections
                  .sort((a, b) => a.order - b.order)
                  .map((section, index) => (
                    <DraggableFormSection
                      key={section.id}
                      id={section.id}
                      header={
                        <div className="flex justify-between items-center w-full">
                          <p className="text-lg font-semibold flex items-center gap-2">
                            {section.icon}
                            <span className="text-lg font-semibold truncate xl:max-w-[400px] max-w-[250px]" title={section.title}>
                              {section.title}
                            </span>
                          </p>
                          <div className="flex items-center gap-2">
                            {/* Edit and Delete controls for API forms */}
                            {String(section.id).startsWith("form_") &&
                              permissions.canEdit && (
                                <>
                                  <TooltipProvider>
                                    <Tooltip>
                                      <TooltipTrigger asChild>
                                        <Button
                                          size="sm"
                                          variant="ghost"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            handleEditStep(
                                              section.originalForm
                                            );
                                          }}
                                          className="text-blue-500 hover:text-blue-700"
                                        >
                                          <EditOutlined />
                                        </Button>
                                      </TooltipTrigger>
                                      <TooltipContent>Edit step</TooltipContent>
                                    </Tooltip>
                                  </TooltipProvider>
                                  {section.is_deletable === "1" && (
                                    <AlertDialog>
                                      <AlertDialogTrigger
                                        asChild
                                        onClick={(e) => {
                                          e.stopPropagation();
                                        }}
                                      >
                                        <Button
                                          size="sm"
                                          variant="ghost"
                                          className="text-red-500 hover:text-red-700"
                                        >
                                          <DeleteOutlined />
                                        </Button>
                                      </AlertDialogTrigger>
                                      <AlertDialogContent>
                                        <AlertDialogHeader>
                                          <AlertDialogTitle>
                                            Delete step
                                          </AlertDialogTitle>
                                          <AlertDialogDescription>
                                            {`Are you sure you want to delete "${section.title}"? This action cannot be undone.`}
                                          </AlertDialogDescription>
                                        </AlertDialogHeader>
                                        <AlertDialogFooter>
                                          <AlertDialogCancel
                                            onClick={(e) => {
                                              e.stopPropagation();
                                            }}
                                          >
                                            Cancel
                                          </AlertDialogCancel>
                                          <AlertDialogAction
                                            onClick={(e) => {
                                              e.stopPropagation();
                                              handleDeleteStep(
                                                section.originalForm
                                              );
                                            }}
                                            disabled={isDeletingForm}
                                          >
                                            {isDeletingForm
                                              ? "Deleting..."
                                              : "Delete"}
                                          </AlertDialogAction>
                                        </AlertDialogFooter>
                                      </AlertDialogContent>
                                    </AlertDialog>
                                  )}
                                </>
                              )}
                            <Button
                              size="sm"
                              onClick={(e) => {
                                e.stopPropagation();
                                if (onAddField) {
                                  onAddField({
                                    id: section?.originalForm?.id,
                                    title: section.title,
                                  });
                                } else {
                                  onAddField({
                                    id: section?.originalForm?.id,
                                    title: section.title,
                                  });
                                  toast({
                                    description:
                                      "Please use the Add Field button in the header",
                                  });
                                }
                              }}
                            >
                              <PlusOutlined className="mr-1" />
                              Add Field
                            </Button>
                          </div>
                        </div>
                      }
                      index={index}
                      moveSection={moveSection}
                      activeKey={activeKeys}
                      onChange={handleCollapseChange}
                      onDrop={commitSectionOrder}
                    >
                      {/* Only render API-driven fields for API sections */}
                      {String(section.id).startsWith("form_") && (
                        <>
                          <RemoteFormFields
                            formId={Number(
                              String(section.id).replace("form_", "")
                            )}
                            renderItem={renderFormItemWithControls}
                            renderInput={renderFieldInput}
                            onFieldUpdate={onFieldUpdate}
                          />
                        </>
                      )}
                    </DraggableFormSection>
                  ))}
              </div>

              <Separator />

              {/* <div className="text-center mt-6 mb-0">
                <Button type="submit" disabled={loading} className="min-w-32">
                  <SaveOutlined className="mr-2" />
                  Preview
                </Button>
              </div> */}
            </form>
          </Form>
        </Card>

        {/* Add Step Modal */}
        <Dialog open={isStepModalVisible} onOpenChange={setIsStepModalVisible}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="text-base font-semibold">
                Add New Step
              </DialogTitle>
            </DialogHeader>
            <Form {...stepFormRHF}>
              <form onSubmit={stepFormRHF.handleSubmit(handleStepSubmit)}>
                <FormField
                  control={stepFormRHF.control}
                  name="stepName"
                  rules={{ required: "Please enter step name" }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Step Name</FormLabel>
                      <FormControl>
                        <Input
                          className="bg-input-secondary  border-dashboard-light/40"
                          placeholder="Enter step name (e.g., Contact Information)"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="w-full flex justify-end gap-2 mt-6">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => setIsStepModalVisible(false)}
                    disabled={isCreatingForm}
                  >
                    Cancel
                  </Button>
                  <ButtonLoader
                    loading={isCreatingForm}
                    type="submit"
                    disabled={isCreatingForm}
                  >
                    Confirm
                  </ButtonLoader>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>

        {/* Edit Step Modal */}
        <Dialog
          open={isEditStepModalVisible}
          onOpenChange={setIsEditStepModalVisible}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="text-base font-semibold">
                Edit Step
              </DialogTitle>
            </DialogHeader>
            <Form {...stepFormRHF}>
              <form onSubmit={stepFormRHF.handleSubmit(handleUpdateStep)}>
                <FormField
                  control={stepFormRHF.control}
                  name="stepName"
                  rules={{ required: "Please enter step name" }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Step Name</FormLabel>
                      <FormControl>
                        <Input
                          className="bg-input-secondary  border-dashboard-light/40"
                          placeholder="Enter step name (e.g., Contact Information)"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="w-full flex justify-end gap-2 mt-6">
                  <Button
                    variant="outline"
                    type="button"
                    onClick={() => {
                      setIsEditStepModalVisible(false);
                      setEditingStep(null);
                      stepFormRHF.reset();
                    }}
                    disabled={isUpdatingForm}
                  >
                    Cancel
                  </Button>
                  <ButtonLoader
                    loading={isUpdatingForm}
                    type="submit"
                    disabled={isUpdatingForm}
                  >
                    Update
                  </ButtonLoader>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
    </DndProvider>
  );
};

export default SpeakerIntakeForm;
