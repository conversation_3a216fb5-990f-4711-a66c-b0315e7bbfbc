subscriptionService = {};
const {  PricingPlan, Subscriptions, sequelize } = require("../models");
const { parsePagination, getPagingData } = require("../helpers/app-hepler");
const { QueryTypes } = require('sequelize');

// ------------------------- subscription-service -------------------------

/**
 * Get all pricing plans with speaker subscription status.
 * Shows all available pricing plans and indicates which ones the speaker has subscribed to.
 * 
 * @param {Object} [filters={}] - Filter options
 * @param {number} [filters.speaker_id] - Speaker ID to check subscriptions for
 * @param {number} [filters.page] - Page number for pagination
 * @param {number} [filters.limit] - Items per page
 * @returns {Promise<Object>} All pricing plans with subscription status
 * @throws {Error} When database operation fails
 */
subscriptionService.getSubscriptions = async (filters = {}, speakerId) => {
  try {
    const { page = 1, limit = 10, offset } = parsePagination(filters);
    console.log("speaker_id:", speakerId);

    if (!speakerId) {
      throw new Error("Speaker ID is required");
    }

    // Single optimized raw query with LEFT JOIN to get all pricing plans 
    // and their subscription status in one database call
    const rawQuery = `
      SELECT 
        pp.id,
        pp.name,
        pp.amount,
        pp.currency,
        pp.billing_interval,
        pp.description,
        pp.created_at,
        pp.updated_at,
        CASE 
          WHEN s.plan_id IS NOT NULL THEN true 
          ELSE false 
        END as is_subscribed
      FROM pricing_plans pp
      LEFT JOIN subscriptions s ON pp.id = s.plan_id 
        AND s.speaker_id = :speakerId 
        AND s.status = 'active'
      WHERE pp.is_active = true
      ORDER BY pp.created_at ASC
      LIMIT :limit OFFSET :offset
    `;

    // Get count for pagination
    const countQuery = `
      SELECT COUNT(*) as total
      FROM pricing_plans pp
      WHERE pp.is_active = true
    `;

    // Execute both queries in parallel for better performance
    const [pricingPlans, countResult] = await Promise.all([
      sequelize.query(rawQuery, {
        replacements: { speakerId, limit, offset },
        type: QueryTypes.SELECT
      }),
      sequelize.query(countQuery, {
        type: QueryTypes.SELECT
      })
    ]);

    const totalCount = parseInt(countResult[0].total);
    const subscribedCount = pricingPlans.filter(plan => plan.is_subscribed).length;
    const pageData = getPagingData(totalCount, limit, page);

    return {
      status: true,
      message: `Found ${totalCount} pricing plans for speaker`,
      data: {
        speaker_id: parseInt(speakerId),
        pricing_plans: pricingPlans,
        total_plans: totalCount,
        subscribed_plans: subscribedCount,
        pageData
      },
    };
  } catch (error) {
    console.log("Error fetching pricing plans with subscription status", error);
    throw error;
  }
};


subscriptionService.getSubscriptionHistory = async (filters = {}, speakerId) => {
    try {
      const { page = 1, limit = 10, offset } = parsePagination(filters);
      console.log("speaker_id:", speakerId);
      if (!speakerId) {
        throw new Error("Speaker ID is required");
      }
      // Fetch subscription history with associated pricing plan details
      const { rows: subscriptions, count } = await Subscriptions.findAndCountAll({
        where: { speaker_id: speakerId },
        include: [{
          model: PricingPlan,
          as: 'plan',
          attributes: ['id', 'name', 'amount', 'currency', 'billing_interval', 'description']
        }],
        order: [['created_at', 'DESC']],
        limit,
        offset
      });
      return {
        status: true,
        message: `Found ${count} subscription(s) for speaker`,
        data: {
          speaker_id: parseInt(speakerId),
          subscriptions
        }
      };
    } catch (error) {
      console.log("Error fetching subscription history", error);
      throw error;
    }
  };

module.exports = subscriptionService;