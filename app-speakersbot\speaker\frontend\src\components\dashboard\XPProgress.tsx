import { Zap, Trophy, TrendingUp } from "lucide-react";
import { Card } from "@/components/ui/card";

export const XPProgress = () => {
  const currentXP = 2850;
  const nextLevelXP = 3500;
  const currentLevel = 12;
  const progress = (currentXP / nextLevelXP) * 100;
  
  const recentActions = [
    { action: "Profile completed", xp: 50, time: "2h ago" },
    { action: "Applied to TechConf", xp: 25, time: "1d ago" },
    { action: "Referred a speaker", xp: 100, time: "3d ago" }
  ];

  return (
    <Card className="bg-card border-border-subtle p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-semibold text-foreground flex items-center gap-2">
          <Trophy className="h-4 w-4 text-silver" />
          Level Progress
        </h3>
        <div className="flex items-center gap-1 bg-gradient-primary px-2 py-1 rounded-md text-xs font-medium text-primary-foreground">
          <Zap className="h-3 w-3" />
          Level {currentLevel}
        </div>
      </div>
      
      {/* XP Progress Bar */}
      <div className="space-y-2">
        <div className="flex justify-between items-center text-xs">
          <span className="text-foreground-muted">{currentXP.toLocaleString()} XP</span>
          <span className="text-foreground-subtle">{nextLevelXP.toLocaleString()} XP</span>
        </div>
        
        <div className="w-full bg-surface-elevated rounded-full h-2 overflow-hidden">
          <div 
            className="h-full bg-gradient-primary rounded-full transition-all duration-500 shadow-glow"
            style={{ width: `${progress}%` }}
          />
        </div>
        
        <div className="text-center text-xs text-foreground-muted">
          {nextLevelXP - currentXP} XP to Level {currentLevel + 1}
        </div>
      </div>
      
      {/* Recent XP Actions */}
      <div className="space-y-2">
        <h4 className="text-xs font-medium text-foreground-muted flex items-center gap-1">
          <TrendingUp className="h-3 w-3" />
          Recent Activity
        </h4>
        
        <div className="space-y-2">
          {recentActions.map((action, index) => (
            <div key={index} className="flex items-center justify-between text-xs">
              <span className="text-foreground-subtle">{action.action}</span>
              <div className="flex items-center gap-2">
                <span className="text-foreground-muted">{action.time}</span>
                <span className="text-success font-medium">+{action.xp} XP</span>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <button className="w-full text-center text-xs text-foreground-muted hover:text-foreground transition-colors py-2 border-t border-border-subtle">
        View Full Progress
      </button>
    </Card>
  );
};