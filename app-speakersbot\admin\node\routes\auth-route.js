
const authController = require("../controllers/auth-controller");
const router = require("express").Router();

module.exports = (app) => {

    router.post("/login", authController.login);

    // forgot password and reset password routes
    router.post("/forgot-password", authController.forgotPassword);
    router.post("/reset-password", authController.resetPassword);

    // change password routes
    router.post("/change-password", authController.changePassword);

    app.use("/admin/auth/v1", router);

};