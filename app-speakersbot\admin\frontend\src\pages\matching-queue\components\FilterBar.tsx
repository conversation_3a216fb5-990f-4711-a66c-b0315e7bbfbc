import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Skeleton } from "@/components/ui/skeleton";
import { RotateCcw, Search } from "lucide-react";
import DatePicker from "react-multi-date-picker";

interface FilterBarProps {
  isLoading: boolean;
  filters: {
    eventIdFilter: string;
    eventTypeFilter: string;
    statusFilter: string[];
  };
  eventTypes: string[];
  dateRange: any;
  isEventTypeOpen: boolean;
  isStatusOpen: boolean;
  setIsEventTypeOpen: (v: boolean) => void;
  setIsStatusOpen: (v: boolean) => void;
  searchText: string;
  setSearchText: (v: string) => void;
  setSearchDebounced: (v: string) => void;
  updateFilter: (key: string, value: any) => void;
  setDateRange: (v: any) => void;
}

const FilterBar: React.FC<FilterBarProps> = ({
  isLoading,
  filters,
  eventTypes,
  dateRange,
  isEventTypeOpen,
  isStatusOpen,
  setIsEventTypeOpen,
  setIsStatusOpen,
  searchText,
  setSearchText,
  setSearchDebounced,
  updateFilter,
  setDateRange,
}) => {
  return (
    <Card className="bg-tertiary border-border rounded-xl shadow-sm">
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-foreground">
          Filters
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {isLoading ? (
          <>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <Skeleton className="h-10 w-full" />
              </div>
              <Skeleton className="h-10 w-28" />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
              <Skeleton className="h-10 w-full" />
            </div>
          </>
        ) : (
          <>
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Filter by Event ID"
                    value={searchText}
                    onChange={(e) => {
                      const val = e.target.value;
                      setSearchText(val);
                      setSearchDebounced(val);
                    }}
                    className="pl-10"
                  />
                </div>
              </div>

              <Button
                variant="outline"
                className="w-full md:w-auto"
                onClick={() => {
                  setSearchText("");
                  updateFilter("eventIdFilter", "");
                  updateFilter("eventTypeFilter", "all");
                  updateFilter("statusFilter", ["all"]);
                  setDateRange(null);
                }}
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset
              </Button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Popover open={isEventTypeOpen} onOpenChange={setIsEventTypeOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    className="w-full justify-between capitalize"
                  >
                    {filters.eventTypeFilter &&
                    filters.eventTypeFilter !== "all"
                      ? filters.eventTypeFilter
                      : "Filter by Event Type"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="p-0 w-[--radix-popover-trigger-width] max-h-[300px] overflow-y-auto">
                  <Command>
                    <CommandInput placeholder="Search event types..." />
                    <CommandEmpty>No event types found.</CommandEmpty>
                    <CommandGroup>
                      <CommandItem
                        value="all"
                        onSelect={() => {
                          updateFilter("eventTypeFilter", "all");
                          setIsEventTypeOpen(false);
                        }}
                      >
                        All
                      </CommandItem>
                      {eventTypes.map((et) => (
                        <CommandItem
                          key={et}
                          value={et}
                          onSelect={() => {
                            updateFilter("eventTypeFilter", et);
                            setIsEventTypeOpen(false);
                          }}
                        >
                          {et}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>

              <Popover open={isStatusOpen} onOpenChange={setIsStatusOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    className="w-full justify-between capitalize"
                  >
                    {filters.statusFilter[0] &&
                    filters.statusFilter[0] !== "all"
                      ? filters.statusFilter[0]
                      : "Filter by Status"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="p-0 w-[--radix-popover-trigger-width]">
                  <Command>
                    <CommandInput placeholder="Search status..." />
                    <CommandEmpty>No status found.</CommandEmpty>
                    <CommandGroup>
                      {[
                        { label: "All", value: "all" },
                        { label: "Pending", value: "pending" },
                        { label: "Interested", value: "interested" },
                        { label: "Accepted", value: "accepted" },
                        { label: "Rejected", value: "rejected" },
                      ].map((opt) => (
                        <CommandItem
                          key={opt.value}
                          value={opt.value}
                          onSelect={() => {
                            updateFilter("statusFilter", [opt.value]);
                            setIsStatusOpen(false);
                          }}
                        >
                          {opt.label}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>

              <DatePicker
                value={dateRange}
                onChange={setDateRange}
                range
                numberOfMonths={2}
                arrow={false}
                className="custom-calendar"
                placeholder="Select Date Range"
                inputClass="h-10 rounded-md border border-input bg-background w-full px-2 text-sm placeholder:text-foreground"
              />
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default FilterBar;
