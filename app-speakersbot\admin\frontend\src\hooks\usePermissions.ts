import { useAppSelector } from '../store/hooks';
import { Permission } from '../store/slices/rbacSlice';
import { useAuth } from '../state/AuthContext';

/**
 * Hook to check if user has specific permissions
 */
export const usePermissions = () => {
  const { user, isAuthenticated } = useAuth() as any;
  // Prefer permissions coming from auth slice (populated from Users API)
  const authPermissions = useAppSelector((state) => state.auth.permissions) as string[] | undefined;
  const authUser = useAppSelector((state) => state.auth.user);
  const { userPermissions: rbacPermissions, isLoading: rbacLoading, error } = useAppSelector((state) => state.rbac);
  const effectivePermissions: Permission[] = (authPermissions?.length ? authPermissions : rbacPermissions) as Permission[];

  // Consider loading while authenticated but user profile hasn't been populated yet.
  // permissions may be an empty array for affiliates, so rely on user presence.
  const isLoading = !!isAuthenticated && !authUser;

  /**
   * Check if user has a specific permission
   */
  const hasPermission = (permission: Permission): boolean => {
    return effectivePermissions.includes(permission);
  };

  /**
   * Check if user has any of the specified permissions
   */
  const hasAnyPermission = (permissions: Permission[]): boolean => {
    return permissions.some(permission => effectivePermissions.includes(permission));
  };

  /**
   * Check if user has all of the specified permissions
   */
  const hasAllPermissions = (permissions: Permission[]): boolean => {
    return permissions.every(permission => effectivePermissions.includes(permission));
  };

  /**
   * Check if user can read a specific module
   */
  const canRead = (module: string): boolean => {
    const readPermission = `read_${module}` as Permission;
    return hasPermission(readPermission);
  };

  /**
   * Check if user can write (create, update, delete) a specific module
   */
  const canWrite = (module: string): boolean => {
    const writePermission = `write_${module}` as Permission;
    return hasPermission(writePermission);
  };

  /**
   * Check if user can perform any action on a specific module
   */
  const canAccess = (module: string): boolean => {
    return canRead(module) || canWrite(module);
  };

  /**
   * Get all permissions for a specific module
   */
  const getModulePermissions = (module: string): Permission[] => {
    return effectivePermissions.filter(permission => 
      permission.startsWith(`read_${module}`) || permission.startsWith(`write_${module}`)
    );
  };

  /**
   * Check if user is admin (has all permissions)
   */
  const isAdmin = (): boolean => {
    const adminPermissions: Permission[] = [
      'read_dashboard',
      'read_opportunities',
      'write_opportunities',
      'read_speakers',
      'write_speakers',
      'read_matching_queue',
      'write_matching_queue',
      'read_users',
      'write_users',
      'read_form_types',
      'write_form_types',
      'read_scraping_logging',
      'write_scraping_topics',
      'write_gamification_data',
      'write_settings',
      'read_pricing_plans',
      'read_affiliates',
      'write_affiliates',
      'read_affiliate_users_details',
    ];
    return hasAllPermissions(adminPermissions);
  };

  /**
   * Check if user is affiliate (limited permissions)
   */
  const isAffiliate = (): boolean => {
    return user?.role?.name === 'Affiliate';
  };

  return {
    userPermissions: effectivePermissions,
    isLoading,
    error,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canRead,
    canWrite,
    canAccess,
    getModulePermissions,
    isAdmin,
    isAffiliate,
  };
};

/**
 * Hook for checking specific permission
 */
export const usePermission = (permission: Permission): boolean => {
  const { hasPermission } = usePermissions();
  return hasPermission(permission);
};

/**
 * Hook for checking module access
 */
export const useModuleAccess = (module: string) => {
  const { canRead, canWrite, canAccess } = usePermissions();
  
  return {
    canRead: canRead(module),
    canWrite: canWrite(module),
    canAccess: canAccess(module),
  };
};
