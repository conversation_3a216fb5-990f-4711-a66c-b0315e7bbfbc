import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import dayjs from 'dayjs';
import { storage } from '../../utils/storage';
import {
  generateDemoSpeakers, generateDemoOpportunities, generateDemoMatches,
  generateDemoIntakeForm, generateDemoAffiliates,
  generateDemoSettings, generateDemoPricingPlans, generateDemoUsers
} from '../../utils/demo-data';
import {
  generateDemoSubmissionLogs, generateDemoAffiliateMetrics, generateDemoAffiliateCommissions,
  generateDemoSubscriptionEvents, generateDemoChurnData, generateDemoFlaggedItems,
  generateDemoUserFeedback, generateDemoFeatureRequests, generateDemoBotStatus,
  generateDemoScrapingAlerts, generateDemoPaymentFailures
} from '../../utils/analytics-seed-data';
import type {
  Speaker, Opportunity, Match, IntakeFormVersion, ScrapingLog, ScrapingTopic,
  Affiliate, Invite, AppSettings, PricingPlan, ActivityLogEntry, User, Note,
  SpeakersState, OpportunitiesState, MatchesState, IntakeFormState,
  ScrapingState, AffiliatesState, SettingsState, UIState, MatchStatus,
  UsersState, IntakeFormField, OpportunityStatus, SubmissionLog, AffiliateMetrics,
  AffiliateCommission, SubscriptionEvent, ChurnData, FlaggedItem, ModerationAction,
  UserFeedback, FeatureRequest, SessionData, OtherFieldAnalysis, CategorySuggestion,
  BotStatus, ScrapingAlert, PaymentFailure
} from '../../types';

interface AppState {
  speakers: SpeakersState;
  opportunities: OpportunitiesState;
  matches: MatchesState;
  users: UsersState;
  intakeForm: IntakeFormState;
  // Scraping data is now fetched from API directly in components
  affiliates: AffiliatesState;
  settings: SettingsState;
  ui: UIState;
  activityLog: ActivityLogEntry[];
  pricingPlans: PricingPlan[];
  submissions: SubmissionLog[];
  affiliateMetrics: AffiliateMetrics[];
  affiliateCommissions: AffiliateCommission[];
  subscriptionEvents: SubscriptionEvent[];
  churnData: ChurnData[];
  flaggedItems: FlaggedItem[];
  moderationActions: ModerationAction[];
  userFeedback: UserFeedback[];
  featureRequests: FeatureRequest[];
  sessionData: SessionData[];
  categoryAnalysis: OtherFieldAnalysis[];
  categorySuggestions: CategorySuggestion[];
  botStatus: BotStatus[];
  scrapingAlerts: ScrapingAlert[];
  paymentFailures: PaymentFailure[];
}

const recalculateOpportunityCounts = (opportunities: Opportunity[], matches: Match[]): Opportunity[] => {
  return opportunities.map(opp => {
    const oppMatches = matches.filter(m => m.opportunityId === opp.id);
    return {
      ...opp,
      matchedCount: oppMatches.length,
      interestedCount: oppMatches.filter(m => m.status === 'interested').length,
      acceptedCount: oppMatches.filter(m => m.status === 'accepted').length,
      rejectedCount: oppMatches.filter(m => m.status === 'rejected').length,
    };
  });
};

const initialState: AppState = {
  speakers: { speakers: [], loading: true },
  opportunities: { opportunities: [], loading: true },
  matches: { matches: [], loading: true },
  users: { users: [], loading: true },
  intakeForm: { versions: [], activeVersion: null, loading: true },
  // Scraping data is now fetched from API directly in components
  affiliates: { affiliates: [], loading: true },
  settings: {
    settings: {
      payment: { gateway: 'stripe', publicKey: '', webhookUrl: '' },
      scrapingEnabled: false
    },
    invites: [],
    loading: true
  },
  ui: { sidebarCollapsed: false, dateRange: null, theme: 'dark' },
  activityLog: [],
  pricingPlans: [],
  submissions: [],
  affiliateMetrics: [],
  affiliateCommissions: [],
  subscriptionEvents: [],
  churnData: [],
  flaggedItems: [],
  moderationActions: [],
  userFeedback: [],
  featureRequests: [],
  sessionData: [],
  categoryAnalysis: [],
  categorySuggestions: [],
  botStatus: [],
  scrapingAlerts: [],
  paymentFailures: [],
};

export const appSlice = createSlice({
  name: 'app',
  initialState,
  reducers: {
    initData: (state) => {
      const speakers = generateDemoSpeakers();
      const opportunities = generateDemoOpportunities();
      const matches = generateDemoMatches();
      const affiliates = generateDemoAffiliates();
      const users = generateDemoUsers();
      const intakeForm = storage.get<IntakeFormVersion[]>('INTAKE_FORM') || [generateDemoIntakeForm()];
      // Scraping data is now fetched from API directly in components
      const settings = storage.get<AppSettings>('SETTINGS') || generateDemoSettings();
      const ui = storage.get<UIState>('UI') || { sidebarCollapsed: false, dateRange: null, theme: 'dark' };
      const activityLog = storage.get<ActivityLogEntry[]>('ACTIVITY_LOG') || [];
      const pricingPlans = generateDemoPricingPlans();

      const submissions = generateDemoSubmissionLogs();
      const affiliateMetrics = generateDemoAffiliateMetrics();
      const affiliateCommissions = generateDemoAffiliateCommissions();
      const subscriptionEvents = generateDemoSubscriptionEvents();
      const churnData = generateDemoChurnData();
      const flaggedItems = generateDemoFlaggedItems();
      const userFeedback = generateDemoUserFeedback();
      const featureRequests = generateDemoFeatureRequests();
      const botStatus = generateDemoBotStatus();
      const scrapingAlerts = generateDemoScrapingAlerts();
      const paymentFailures = generateDemoPaymentFailures();

      storage.set('SPEAKERS', speakers);
      storage.set('OPPORTUNITIES', opportunities);
      storage.set('MATCHES', matches);
      storage.set('USERS', users);
      storage.set('INTAKE_FORM', intakeForm);
      // Scraping data is now fetched from API directly in components
      storage.set('AFFILIATES', affiliates);
      storage.set('SETTINGS', settings);
      storage.set('UI', ui);
      storage.set('ACTIVITY_LOG', activityLog);

      state.speakers = { speakers, loading: false };
      state.opportunities = { opportunities: recalculateOpportunityCounts(opportunities, matches), loading: false };
      state.matches = { matches, loading: false };
      state.users = { users, loading: false };
      state.intakeForm = { versions: intakeForm, activeVersion: intakeForm.find(v => v.isActive) || intakeForm[0] || null, loading: false };
      // Scraping data is now fetched from API directly in components
      state.affiliates = { affiliates, loading: false };
      state.settings = { settings, invites: [], loading: false };
      state.ui = ui;
      state.activityLog = activityLog;
      state.pricingPlans = pricingPlans;
      state.submissions = submissions;
      state.affiliateMetrics = affiliateMetrics;
      state.affiliateCommissions = affiliateCommissions;
      state.subscriptionEvents = subscriptionEvents;
      state.churnData = churnData;
      state.flaggedItems = flaggedItems;
      state.moderationActions = [];
      state.userFeedback = userFeedback;
      state.featureRequests = featureRequests;
      state.sessionData = [];
      state.categoryAnalysis = [];
      state.categorySuggestions = [];
      state.botStatus = botStatus;
      state.scrapingAlerts = scrapingAlerts;
      state.paymentFailures = paymentFailures;
    },

    addSpeaker: (state, action: PayloadAction<Speaker>) => {
      state.speakers.speakers.push(action.payload);
      storage.set('SPEAKERS', state.speakers.speakers);
    },
    updateSpeaker: (state, action: PayloadAction<Speaker>) => {
      state.speakers.speakers = state.speakers.speakers.map(s => s.id === action.payload.id ? action.payload : s);
      storage.set('SPEAKERS', state.speakers.speakers);
    },

    addOpportunity: (state, action: PayloadAction<Opportunity>) => {
      state.opportunities.opportunities.push(action.payload);
      storage.set('OPPORTUNITIES', state.opportunities.opportunities);
    },
    updateOpportunity: (state, action: PayloadAction<Opportunity>) => {
      state.opportunities.opportunities = state.opportunities.opportunities.map(o => o.id === action.payload.id ? action.payload : o);
      storage.set('OPPORTUNITIES', state.opportunities.opportunities);
    },

    updateMatchStatus: (state, action: PayloadAction<{ matchId: string; status: MatchStatus; reason?: string }>) => {
      const { matchId, status, reason } = action.payload;
      state.matches.matches = state.matches.matches.map(m => m.id === matchId ? { ...m, status, reason, updatedAt: dayjs().toISOString() } : m);
      const updatedOpportunities = recalculateOpportunityCounts(state.opportunities.opportunities, state.matches.matches);
      state.opportunities.opportunities = updatedOpportunities;
      storage.set('MATCHES', state.matches.matches);
      storage.set('OPPORTUNITIES', updatedOpportunities);
    },
    addMatch: (state, action: PayloadAction<Match>) => {
      state.matches.matches.push(action.payload);
      const updatedOpportunities = recalculateOpportunityCounts(state.opportunities.opportunities, state.matches.matches);
      state.opportunities.opportunities = updatedOpportunities;
      storage.set('MATCHES', state.matches.matches);
      storage.set('OPPORTUNITIES', updatedOpportunities);
    },

    // Scraping actions removed - now handled via API directly in components

    addAffiliate: (state, action: PayloadAction<Affiliate>) => {
      state.affiliates.affiliates.push(action.payload);
      storage.set('AFFILIATES', state.affiliates.affiliates);
    },

    updateSettings: (state, action: PayloadAction<AppSettings>) => {
      state.settings.settings = action.payload as any;
      storage.set('SETTINGS', state.settings.settings);
    },

    setDateRange: (state, action: PayloadAction<[string, string] | null>) => {
      state.ui = { ...state.ui, dateRange: action.payload };
      storage.set('UI', state.ui);
    },
    toggleSidebar: (state) => {
      state.ui = { ...state.ui, sidebarCollapsed: !state.ui.sidebarCollapsed };
      storage.set('UI', state.ui);
    },
    toggleTheme: (state) => {
      const newTheme: 'light' | 'dark' = state.ui.theme === 'dark' ? 'light' : 'dark';
      state.ui = { ...state.ui, theme: newTheme };
      storage.set('UI', state.ui);
    },

    addActivityLog: (state, action: PayloadAction<ActivityLogEntry>) => {
      state.activityLog = [action.payload, ...state.activityLog].slice(0, 100);
      storage.set('ACTIVITY_LOG', state.activityLog);
    },

    addUser: (state, action: PayloadAction<User>) => {
      state.users.users.push(action.payload);
      storage.set('USERS', state.users.users);
    },
    updateUserLocal: (state, action: PayloadAction<User>) => {
      state.users.users = state.users.users.map(u => u.id === action.payload.id ? action.payload : u);
      storage.set('USERS', state.users.users);
    },
    deleteUserLocal: (state, action: PayloadAction<string>) => {
      state.users.users = state.users.users.filter(u => u.id !== action.payload);
      storage.set('USERS', state.users.users);
    },

    addIntakeField: (state, action: PayloadAction<IntakeFormField>) => {
      if (!state.intakeForm.activeVersion) return;
      const updatedVersion = { ...state.intakeForm.activeVersion, fields: [...state.intakeForm.activeVersion.fields, action.payload] };
      state.intakeForm.versions = state.intakeForm.versions.map(v => v.isActive ? updatedVersion : v);
      state.intakeForm.activeVersion = updatedVersion;
      storage.set('INTAKE_FORM', state.intakeForm.versions);
    },
    updateIntakeField: (state, action: PayloadAction<IntakeFormField>) => {
      if (!state.intakeForm.activeVersion) return;
      const updatedFields = state.intakeForm.activeVersion.fields.map(f => f.id === action.payload.id ? action.payload : f);
      const updatedVersion = { ...state.intakeForm.activeVersion, fields: updatedFields };
      state.intakeForm.versions = state.intakeForm.versions.map(v => v.isActive ? updatedVersion : v);
      state.intakeForm.activeVersion = updatedVersion;
      storage.set('INTAKE_FORM', state.intakeForm.versions);
    },
    setIntakeFormVersion: (state, action: PayloadAction<IntakeFormVersion>) => {
      const newVersion = action.payload;
      state.intakeForm.versions = [newVersion];
      state.intakeForm.activeVersion = newVersion;
      storage.set('INTAKE_FORM', state.intakeForm.versions);
    },
    deleteIntakeField: (state, action: PayloadAction<string>) => {
      if (!state.intakeForm.activeVersion) return;
      const updatedFields = state.intakeForm.activeVersion.fields.filter(f => f.id !== action.payload);
      const updatedVersion = { ...state.intakeForm.activeVersion, fields: updatedFields };
      state.intakeForm.versions = state.intakeForm.versions.map(v => v.isActive ? updatedVersion : v);
      state.intakeForm.activeVersion = updatedVersion;
      storage.set('INTAKE_FORM', state.intakeForm.versions);
    },
    saveIntakeVersion: (state) => {
      if (!state.intakeForm.activeVersion) return;
      const newVersion: IntakeFormVersion = {
        ...state.intakeForm.activeVersion,
        id: Date.now().toString(),
        version: Math.max(0, ...state.intakeForm.versions.map(v => v.version)) + 1,
        isActive: true,
        createdAt: new Date().toISOString(),
      };
      state.intakeForm.versions = [...state.intakeForm.versions.map(v => ({ ...v, isActive: false })), newVersion];
      state.intakeForm.activeVersion = newVersion;
      storage.set('INTAKE_FORM', state.intakeForm.versions);
    },
    rollbackIntakeVersion: (state, action: PayloadAction<string>) => {
      const targetVersion = state.intakeForm.versions.find(v => v.id === action.payload);
      if (!targetVersion) return;
      state.intakeForm.versions = state.intakeForm.versions.map(v => ({ ...v, isActive: v.id === action.payload }));
      state.intakeForm.activeVersion = targetVersion;
      storage.set('INTAKE_FORM', state.intakeForm.versions);
    },

    // Scraping topic actions removed - now handled via API directly in components

    updatePaymentSettings: (state, action: PayloadAction<any>) => {
      state.settings.settings = { ...state.settings.settings, payment: action.payload } as any;
      storage.set('SETTINGS', state.settings.settings);
    },
    // Scraping setting actions removed - now handled via API directly in components

    addInvite: (state, action: PayloadAction<Invite>) => {
      const currentSettings = storage.get<any>('SETTINGS') || { invites: [] };
      state.settings.invites = [...state.settings.invites, action.payload];
      storage.set('SETTINGS', { ...currentSettings, invites: state.settings.invites });
    },
    deleteInvite: (state, action: PayloadAction<string>) => {
      const currentSettings = storage.get<any>('SETTINGS') || { invites: [] };
      state.settings.invites = state.settings.invites.filter(i => i.id !== action.payload);
      storage.set('SETTINGS', { ...currentSettings, invites: state.settings.invites });
    },

    addOpportunityNote: (state, action: PayloadAction<{ opportunityId: string; note: Note }>) => {
      const { opportunityId, note } = action.payload;
      state.opportunities.opportunities = state.opportunities.opportunities.map(o => o.id === opportunityId ? { ...o, notes: [...o.notes, note] } : o);
      storage.set('OPPORTUNITIES', state.opportunities.opportunities);
    },
    updateOpportunityNote: (state, action: PayloadAction<{ opportunityId: string; noteId: string; content: string }>) => {
      const { opportunityId, noteId, content } = action.payload;
      state.opportunities.opportunities = state.opportunities.opportunities.map(o => o.id === opportunityId ? { ...o, notes: o.notes.map(n => n.id === noteId ? { ...n, content } : n) } : o);
      storage.set('OPPORTUNITIES', state.opportunities.opportunities);
    },
    deleteOpportunityNote: (state, action: PayloadAction<{ opportunityId: string; noteId: string }>) => {
      const { opportunityId, noteId } = action.payload;
      state.opportunities.opportunities = state.opportunities.opportunities.map(o => o.id === opportunityId ? { ...o, notes: o.notes.filter(n => n.id !== noteId) } : o);
      storage.set('OPPORTUNITIES', state.opportunities.opportunities);
    },

    duplicateOpportunity: (state, action: PayloadAction<Opportunity>) => {
      const duplicatedOpportunity: Opportunity = {
        ...action.payload,
        id: `op_${Date.now()}`,
        title: `${action.payload.title} (Copy)`,
        createdAt: dayjs().toISOString(),
        updatedAt: dayjs().toISOString(),
        matchedCount: 0,
        interestedCount: 0,
        acceptedCount: 0,
        rejectedCount: 0,
        notes: [],
      };
      state.opportunities.opportunities.push(duplicatedOpportunity);
      storage.set('OPPORTUNITIES', state.opportunities.opportunities);
    },
    deleteOpportunityWithMatches: (state, action: PayloadAction<{ opportunityId: string; deleteMatches: boolean }>) => {
      const { opportunityId, deleteMatches } = action.payload;
      state.opportunities.opportunities = state.opportunities.opportunities.filter(o => o.id !== opportunityId);
      if (deleteMatches) {
        state.matches.matches = state.matches.matches.filter(m => m.opportunityId !== opportunityId);
        storage.set('MATCHES', state.matches.matches);
      }
      storage.set('OPPORTUNITIES', state.opportunities.opportunities);
    },
    bulkDeleteOpportunities: (state, action: PayloadAction<string[]>) => {
      state.opportunities.opportunities = state.opportunities.opportunities.filter(o => !action.payload.includes(o.id));
      storage.set('OPPORTUNITIES', state.opportunities.opportunities);
    },
    bulkUpdateOpportunityStatus: (state, action: PayloadAction<{ ids: string[]; status: OpportunityStatus }>) => {
      const { ids, status } = action.payload;
      state.opportunities.opportunities = state.opportunities.opportunities.map(o => ids.includes(o.id) ? { ...o, status, updatedAt: dayjs().toISOString() } : o);
      storage.set('OPPORTUNITIES', state.opportunities.opportunities);
    },
    bulkUpdateMatchStatus: (state, action: PayloadAction<{ matchIds: string[]; status: MatchStatus; reason?: string }>) => {
      const { matchIds, status, reason } = action.payload;
      state.matches.matches = state.matches.matches.map(m => matchIds.includes(m.id) ? { ...m, status, reason, updatedAt: dayjs().toISOString() } : m);
      const updatedOpportunities = recalculateOpportunityCounts(state.opportunities.opportunities, state.matches.matches);
      state.opportunities.opportunities = updatedOpportunities;
      storage.set('MATCHES', state.matches.matches);
      storage.set('OPPORTUNITIES', updatedOpportunities);
    },
  }
});

export const {
  initData,
  addSpeaker,
  updateSpeaker,
  addOpportunity,
  updateOpportunity,
  updateMatchStatus,
  addMatch,
  // Scraping actions removed - now handled via API directly in components
  addAffiliate,
  updateSettings,
  setDateRange,
  toggleSidebar,
  toggleTheme,
  addActivityLog,
  addUser,
  updateUserLocal,
  deleteUserLocal,
  addIntakeField,
  updateIntakeField,
  deleteIntakeField,
  setIntakeFormVersion,
  saveIntakeVersion,
  rollbackIntakeVersion,
  updatePaymentSettings,
  // Scraping actions removed - now handled via API directly in components
  addInvite,
  deleteInvite,
  addOpportunityNote,
  updateOpportunityNote,
  deleteOpportunityNote,
  duplicateOpportunity,
  deleteOpportunityWithMatches,
  bulkDeleteOpportunities,
  bulkUpdateOpportunityStatus,
  bulkUpdateMatchStatus,
} = appSlice.actions;

export default appSlice.reducer;


