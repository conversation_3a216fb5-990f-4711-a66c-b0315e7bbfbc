
const opportunityController = require("../controllers/opportunity-controller");
const router = require("express").Router();

module.exports = (app) => {

    // ------------------------- Speaker Opportunity Routes -------------------------

    /**
     * GET /speaker/api/v1/opportunity/dashboard
     * Retrieves all opportunities associated with the authenticated speaker
     * Requires: Valid JWT token (handled by global middleware)
     * Returns: List of opportunities with metadata
     */
    router.get("/", opportunityController.getAllOpportunities);
    router.get("/due-in-72-hours", opportunityController.getAllOpportunitiesDueIn72Hours);
    router.get("/today-opportunities", opportunityController.getAllTodayOpportunities);
    router.put("/update/:id", opportunityController.updateOpportunity);
    router.get("/pipeline", opportunityController.getOpportunitiesPipeline);
    router.get("/lost-opportunities-tracker", opportunityController.getLostOpportunitiesTracker);
    router.get("/topics", opportunityController.getAllOpportunityTopics);
    router.get("/locations", opportunityController.getAllOpportunityLocations);

    app.use("/speaker/api/v1/opportunity", router);
};