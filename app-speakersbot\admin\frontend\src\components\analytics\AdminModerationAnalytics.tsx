import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from '../ui/card';
import { ResponsiveContainer, <PERSON><PERSON>hart, Bar, LineChart, Line, PieChart, Pie, Cell, XAxis, YAxis, CartesianGrid, <PERSON>ltip, Legend } from 'recharts';
import { FlagOutlined, EditOutlined, EyeOutlined, WarningOutlined, UserOutlined, CheckCircleOutlined, CloseCircleOutlined } from '@ant-design/icons';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Textarea } from '../ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../ui/dialog';
import { Separator } from '../ui/separator';

const AdminModerationAnalytics: React.FC = () => {
  const [selectedAction, setSelectedAction] = useState<string>('');
  const [adminNote, setAdminNote] = useState<string>('');

  const flaggedItemsData = [
    { category: 'Spam Accounts', count: 28, resolved: 22, pending: 6 },
    { category: 'Duplicate Gigs', count: 19, resolved: 15, pending: 4 },
    { category: 'Inappropriate Content', count: 15, resolved: 12, pending: 3 },
    { category: 'Fake Profiles', count: 12, resolved: 9, pending: 3 },
    { category: 'Policy Violations', count: 8, resolved: 6, pending: 2 }
  ];

  const flaggedAccounts = [
    {
      id: 1,
      type: 'Speaker',
      name: 'John Suspicious',
      email: '<EMAIL>',
      reason: 'Multiple duplicate profiles',
      flaggedDate: '2024-12-06',
      status: 'pending',
      severity: 'high'
    },
    {
      id: 2,
      type: 'Opportunity',
      name: 'Fake Tech Conference',
      company: 'Suspicious Corp',
      reason: 'Spam opportunity posting',
      flaggedDate: '2024-12-05',
      status: 'pending',
      severity: 'medium'
    },
    {
      id: 3,
      type: 'Speaker',
      name: 'Jane Spammer',
      email: '<EMAIL>',
      reason: 'Automated bot behavior',
      flaggedDate: '2024-12-04',
      status: 'under_review',
      severity: 'high'
    }
  ];

  const adminActionLogs = [
    {
      id: 1,
      admin: 'Admin Sarah',
      action: 'Banned Account',
      target: 'Speaker: Mike Fake',
      reason: 'Repeated policy violations',
      timestamp: '2024-12-07 14:30',
      notes: 'Multiple fake credentials verified'
    },
    {
      id: 2,
      admin: 'Admin Tom',
      action: 'Approved Override',
      target: 'Assignment: Speaker Jane → TechCorp Event',
      reason: 'Manual quality match',
      timestamp: '2024-12-07 12:15',
      notes: 'Perfect fit despite algorithm mismatch'
    },
    {
      id: 3,
      admin: 'Admin Lisa',
      action: 'Removed Content',
      target: 'Opportunity: Crypto Scam Event',
      reason: 'Fraudulent posting',
      timestamp: '2024-12-07 09:45',
      notes: 'Reported by multiple speakers'
    }
  ];

  const speakers = [
    { id: 1, name: 'Alice Johnson', expertise: 'AI/ML', rating: 4.8 },
    { id: 2, name: 'Bob Chen', expertise: 'Blockchain', rating: 4.6 },
    { id: 3, name: 'Carol Davis', expertise: 'DevOps', rating: 4.9 }
  ];

  const opportunities = [
    { id: 1, title: 'AI Summit 2024', company: 'TechCorp', budget: '$5000' },
    { id: 2, title: 'DevOps Conference', company: 'StartupInc', budget: '$3000' },
    { id: 3, title: 'Blockchain Expo', company: 'CryptoLtd', budget: '$4500' }
  ];

  const moderationActions = [
    { action: 'Content Removed', count: 45, color: 'hsl(var(--dashboard-dark-blue))' },
    { action: 'Account Suspended', count: 23, color: '#ef4444' },
    { action: 'Manual Override', count: 18, color: 'hsl(var(--dashboard-medium-blue))' },
    { action: 'Under Review', count: 15, color: '#f59e0b' }
  ];

  const dailyModerationData = [
    { date: '12-01', flagged: 12, reviewed: 15, resolved: 11, overrides: 3 },
    { date: '12-02', flagged: 8, reviewed: 12, resolved: 9, overrides: 2 },
    { date: '12-03', flagged: 18, reviewed: 8, resolved: 14, overrides: 4 },
    { date: '12-04', flagged: 15, reviewed: 18, resolved: 12, overrides: 5 },
    { date: '12-05', flagged: 22, reviewed: 15, resolved: 18, overrides: 3 },
    { date: '12-06', flagged: 11, reviewed: 22, resolved: 8, overrides: 6 },
    { date: '12-07', flagged: 14, reviewed: 11, resolved: 16, overrides: 2 }
  ];

  const handleApprove = (id: number) => {
    console.log(`Approved item ${id}`);
    // In real app, this would update the backend
  };

  const handleReject = (id: number) => {
    console.log(`Rejected item ${id}`);
    // In real app, this would update the backend
  };

  const handleManualAssignment = () => {
    console.log('Manual assignment created');
    // In real app, this would create the assignment
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'high': return 'text-red-500 bg-red-50';
      case 'medium': return 'text-orange-500 bg-orange-50';
      case 'low': return 'text-yellow-500 bg-yellow-50';
      default: return 'text-gray-500 bg-gray-50';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-orange-100 text-orange-800';     // Orange for pending
      case 'under_review': return 'bg-orange-100 text-orange-800'; // Orange for under review (pending state)
      case 'resolved': return 'bg-green-100 text-green-800';      // Green for resolved (active/success)
      case 'rejected': return 'bg-red-100 text-red-800';          // Red for rejected
      case 'premium': return 'bg-yellow-100 text-yellow-800';     // Golden for premium
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <h3 className="text-2xl font-semibold text-foreground mb-2">Admin Actions + Moderation</h3>
        <p className="text-muted-foreground text-sm mb-8">Manage flagged content, manual overrides, and platform integrity</p>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-card border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Pending Reviews</p>
                <p className="text-2xl font-bold text-foreground">18</p>
                <Badge variant="outline" className="mt-1">Urgent attention</Badge>
              </div>
              <FlagOutlined className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Manual Overrides</p>
                <p className="text-2xl font-bold text-foreground">25</p>
                <Badge variant="secondary" className="mt-1">This week</Badge>
              </div>
              <EditOutlined className="h-8 w-8 text-dashboard-medium-blue" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Resolution Rate</p>
                <p className="text-2xl font-bold text-foreground">91.4%</p>
                <Badge variant="secondary" className="mt-1">Last 30 days</Badge>
              </div>
              <CheckCircleOutlined className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-card border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Response Time</p>
                <p className="text-2xl font-bold text-foreground">8.3h</p>
                <Badge variant="outline" className="mt-1">Within SLA</Badge>
              </div>
              <WarningOutlined className="h-8 w-8 text-dashboard-light-blue" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Flagged Items Table */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="text-foreground">Flagged Accounts & Content</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Type</TableHead>
                  <TableHead>Name/Title</TableHead>
                  <TableHead>Reason</TableHead>
                  <TableHead>Severity</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {flaggedAccounts.map((item) => (
                  <TableRow key={item.id}>
                    <TableCell>
                      <Badge variant="outline">{item.type}</Badge>
                    </TableCell>
                    <TableCell className="font-medium">
                      <div>
                        <p>{item.name}</p>
                        {item.email && <p className="text-xs text-muted-foreground">{item.email}</p>}
                        {item.company && <p className="text-xs text-muted-foreground">{item.company}</p>}
                      </div>
                    </TableCell>
                    <TableCell>{item.reason}</TableCell>
                    <TableCell>
                      <Badge className={getSeverityColor(item.severity)}>
                        {item.severity}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusColor(item.status)}>
                        {item.status.replace('_', ' ')}
                      </Badge>
                    </TableCell>
                    <TableCell>{item.flaggedDate}</TableCell>
                    <TableCell>
                      <div className="flex gap-2">
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleApprove(item.id)}
                        >
                          <CheckCircleOutlined className="h-4 w-4 mr-1" />
                          Approve
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => handleReject(item.id)}
                        >
                          <CloseCircleOutlined className="h-4 w-4 mr-1" />
                          Reject
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Manual Override Section */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="text-foreground">Manual Override: Assign Speaker to Opportunity</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="text-sm font-medium text-foreground mb-2 block">Select Speaker</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Choose speaker..." />
                </SelectTrigger>
                <SelectContent>
                  {speakers.map((speaker) => (
                    <SelectItem key={speaker.id} value={speaker.id.toString()}>
                      {speaker.name} - {speaker.expertise} (★{speaker.rating})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <label className="text-sm font-medium text-foreground mb-2 block">Select Opportunity</label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Choose opportunity..." />
                </SelectTrigger>
                <SelectContent>
                  {opportunities.map((opp) => (
                    <SelectItem key={opp.id} value={opp.id.toString()}>
                      {opp.title} - {opp.company} ({opp.budget})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-end">
              <Button onClick={handleManualAssignment} className="w-full">
                Create Manual Assignment
              </Button>
            </div>
          </div>
          <div>
            <label className="text-sm font-medium text-foreground mb-2 block">Override Reason</label>
            <Textarea 
              placeholder="Explain why this manual assignment is necessary..."
              value={adminNote}
              onChange={(e) => setAdminNote(e.target.value)}
            />
          </div>
        </CardContent>
      </Card>

      {/* Admin Action Logs */}
      <Card className="bg-card border-border">
        <CardHeader>
          <CardTitle className="text-foreground">Admin Action Logs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {adminActionLogs.map((log) => (
              <div key={log.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-start mb-2">
                  <div>
                    <Badge variant="secondary">{log.action}</Badge>
                    <span className="ml-2 font-medium text-foreground">{log.admin}</span>
                  </div>
                  <span className="text-xs text-muted-foreground">{log.timestamp}</span>
                </div>
                <p className="text-sm text-foreground mb-1">
                  <strong>Target:</strong> {log.target}
                </p>
                <p className="text-sm text-muted-foreground mb-2">
                  <strong>Reason:</strong> {log.reason}
                </p>
                {log.notes && (
                  <div className="bg-muted/30 rounded p-2 text-xs">
                    <strong>Admin Notes:</strong> {log.notes}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-foreground">Flagged Content by Category</CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <ResponsiveContainer width="100%" height={350}>
              <BarChart data={flaggedItemsData}>
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                <XAxis 
                  dataKey="category" 
                  angle={-45} 
                  textAnchor="end" 
                  height={100}
                  tick={{ fill: 'hsl(var(--foreground))' }}
                  axisLine={{ stroke: 'hsl(var(--border))' }}
                />
                <YAxis 
                  tick={{ fill: 'hsl(var(--foreground))' }}
                  axisLine={{ stroke: 'hsl(var(--border))' }}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--popover))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                    color: 'hsl(var(--popover-foreground))'
                  }}
                />
                <Legend />
                <Bar dataKey="resolved" stackId="a" fill="hsl(var(--dashboard-dark-blue))" name="Resolved" />
                <Bar dataKey="pending" stackId="a" fill="#f59e0b" name="Pending" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-foreground">Moderation Actions</CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <ResponsiveContainer width="100%" height={350}>
              <PieChart>
                <Pie
                  data={moderationActions}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="count"
                  label={({ action, count }) => `${action}: ${count}`}
                >
                  {moderationActions.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--popover))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                    color: 'hsl(var(--popover-foreground))'
                  }}
                />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-foreground">Daily Admin Activity</CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <ResponsiveContainer width="100%" height={350}>
              <LineChart data={dailyModerationData}>
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                <XAxis 
                  dataKey="date"
                  tick={{ fill: 'hsl(var(--foreground))' }}
                  axisLine={{ stroke: 'hsl(var(--border))' }}
                />
                <YAxis 
                  tick={{ fill: 'hsl(var(--foreground))' }}
                  axisLine={{ stroke: 'hsl(var(--border))' }}
                />
                <Tooltip 
                  contentStyle={{
                    backgroundColor: 'hsl(var(--popover))',
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '8px',
                    color: 'hsl(var(--popover-foreground))'
                  }}
                />
                <Legend />
                <Line type="monotone" dataKey="flagged" stroke="#ef4444" name="Flagged" strokeWidth={2} />
                <Line type="monotone" dataKey="reviewed" stroke="hsl(var(--dashboard-medium-blue))" name="Reviewed" strokeWidth={2} />
                <Line type="monotone" dataKey="resolved" stroke="hsl(var(--dashboard-dark-blue))" name="Resolved" strokeWidth={2} />
                <Line type="monotone" dataKey="overrides" stroke="#8b5cf6" name="Manual Overrides" strokeWidth={2} />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="bg-card border-border">
          <CardHeader>
            <CardTitle className="text-foreground">Admin Notes & Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">Add Admin Note</label>
                <Textarea 
                  placeholder="Add notes about users, opportunities, or decisions..."
                  rows={3}
                />
              </div>
              <div className="flex gap-2">
                <Button size="sm" variant="outline">
                  <UserOutlined className="h-4 w-4 mr-1" />
                  Flag User
                </Button>
                <Button size="sm" variant="outline">
                  <FlagOutlined className="h-4 w-4 mr-1" />
                  Flag Content
                </Button>
                <Button size="sm" variant="outline">
                  <EditOutlined className="h-4 w-4 mr-1" />
                  Override Match
                </Button>
              </div>
              <Separator />
              <div className="text-sm text-muted-foreground">
                <p className="font-medium mb-2">Recent Platform Stats:</p>
                <ul className="space-y-1">
                  <li>• 18 pending reviews require attention</li>
                  <li>• 25 manual overrides completed this week</li>
                  <li>• 91.4% resolution rate (target: 90%)</li>
                  <li>• 8.3h average response time (SLA: 12h)</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminModerationAnalytics;