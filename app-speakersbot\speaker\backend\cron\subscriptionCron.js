const cron = require("node-cron");
const db = require("../models");;
const speakerHistoryService = require("../services/gamification-service");
const { Sequelize } = db;

async function checkSubscriptionsAndAwardPoints() {
    const results = await db.Subscriptions.findAll({
        attributes: [
            "speaker_id",
            "stripe_subscription_id",
            [Sequelize.fn("COUNT", Sequelize.col("id")), "subscriptionCount"]
        ],
        group: ["speaker_id", "stripe_subscription_id"],
        raw: true
    });

    for (const row of results) {
        const { speaker_id, subscriptionCount } = row;

        if (parseInt(subscriptionCount, 10) === 6) {
            const existing = await db.GamificationHistory.findOne({
                where: { speaker_id, key: 'customer_retension' }
            });
            if (!existing) {
                await speakerHistoryService.callEventPoints(speaker_id, 'customer_retension');
            } else {
                continue;
            }
        }
    }
}


// schedule to run daily at midnight
cron.schedule("0 0 * * *", () => {
    checkSubscriptionsAndAwardPoints().catch(err =>
        console.error("❌ Cron failed:", err)
    );
});

