import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { 
  HelpCircle, 
  MessageCircle, 
  Mail
} from 'lucide-react';

export function SupportTab() {
  return (
    <div className="space-y-6">
      {/* Contact Support */}
      <Card className="bg-surface">
        <CardHeader>
          <CardTitle>Contact Support</CardTitle>
          <CardDescription>
            Get personalized help from our support team
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Button variant="outline" className="flex flex-col items-center gap-2 h-auto p-4 bg-card border-subtle">
              <HelpCircle className="h-6 w-6 text-blue-500" />
              <div className="text-center">
                <div className="font-medium">Help Center</div>
                <div className="text-sm text-muted-foreground">Browse FAQs & guides</div>
              </div>
            </Button>
            
            <Button variant="outline" className="flex flex-col items-center gap-2 h-auto p-4 bg-card border-subtle">
              <MessageCircle className="h-6 w-6 text-green-500" />
              <div className="text-center">
                <div className="font-medium">Live Chat</div>
                <div className="text-sm text-muted-foreground">Chat with support</div>
              </div>
            </Button>
            
            <Button variant="outline" className="flex flex-col items-center gap-2 h-auto p-4 bg-card border-subtle">
              <Mail className="h-6 w-6 text-purple-500" />
              <div className="text-center">
                <div className="font-medium">Email Support</div>
                <div className="text-sm text-muted-foreground">Send detailed inquiry</div>
              </div>
            </Button>
          </div>

          <Separator className='border-t-subtle'/>

          <div className="space-y-3">
            <Label htmlFor="support-message">Send us a message</Label>
            <Textarea 
              id="support-message"
              placeholder="Describe your issue or question..."
              className="min-h-24 bg-card border-subtle"
            />
            <div className="flex gap-2">
              <Button>Send Message</Button>
              <Button variant="outline" className="border-subtle">Attach File</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}