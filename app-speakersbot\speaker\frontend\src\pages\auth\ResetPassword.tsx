import { useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useResetPasswordMutation } from "@/apis/authApi";
import logoLight from "/logo-light.png";
import { Separator } from "@/components/ui/separator";
import { Eye, EyeOff } from "lucide-react";

export default function ResetPassword() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const token = searchParams.get("token") || "";
  const [password, setPassword] = useState("");
  const [confirm, setConfirm] = useState("");
  const { toast } = useToast();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [resetPassword, { isLoading }] = useResetPasswordMutation();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!token) {
      toast({
        title: "Invalid link",
        description: "Reset token is missing.",
        variant: "destructive",
      });
      return;
    }
    if (!password || password.length < 8) {
      toast({
        title: "Weak password",
        description: "Use at least 8 characters.",
        variant: "destructive",
      });
      return;
    }
    if (password !== confirm) {
      toast({
        title: "Passwords do not match",
        description: "Please re-enter.",
        variant: "destructive",
      });
      return;
    }
    try {
      const res = await resetPassword({
        token,
        newPassword: password,
      }).unwrap();
      toast({
        title: "Password reset",
        description: res?.message || "You can now log in.",
      });
      navigate("/speaker");
    } catch (error: any) {
      toast({
        title: "Reset failed",
        description: error?.data?.message || "Unable to reset password.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-gradient-surface flex flex-col items-center justify-center p-6 relative">
      {/* Decorative Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-primary opacity-10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-accent-purple opacity-10 rounded-full blur-3xl"></div>
      </div>
      <Card className="w-full max-w-md backdrop-blur-xl bg-card/80 border-border shadow-card relative z-10">
        <CardHeader className="space-y-2 pb-8">
          <div className="text-center">
            <img
              src={logoLight}
              alt="SpeakerBot"
              className="h-[80px] w-full object-contain"
            />
          </div>
          <div className="flex items-center">
            <Separator className="relative w-full bg-border my-3" />
          </div>
          <div className="">
            <CardTitle className="text-2xl text-center text-foreground">
              Reset Password
            </CardTitle>
            <p className="text-foreground-muted text-sm text-center mt-2">
              Enter your new password below
            </p>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password" className="text-foreground">
                New Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="bg-surface-elevated border-border-subtle focus:border-primary focus:ring-primary/20 pr-12"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-foreground-muted" />
                  ) : (
                    <Eye className="h-5 w-5 text-foreground-muted" />
                  )}
                </Button>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirm" className="text-foreground">
                Confirm Password
              </Label>
              <div className="relative">
                <Input
                  id="confirm"
                  type={showConfirmPassword ? "text" : "password"}
                  value={confirm}
                  onChange={(e) => setConfirm(e.target.value)}
                  required
                  className="bg-surface-elevated border-border-subtle focus:border-primary focus:ring-primary/20"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-5 w-5 text-foreground-muted" />
                  ) : (
                    <Eye className="h-5 w-5 text-foreground-muted" />
                  )}
                </Button>
              </div>
            </div>
            <Button
              type="submit"
              className="w-full"
              disabled={isLoading}
              loading={isLoading}
            >
              Reset password
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
