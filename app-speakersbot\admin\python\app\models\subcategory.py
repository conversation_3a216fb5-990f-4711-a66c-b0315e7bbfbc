from sqlalchemy import Column, Integer, String, Enum, DateTime, Boolean, Index
from sqlalchemy.ext.declarative import declarative_base
from app.config.config import config
from sqlalchemy import create_engine

Base = declarative_base()

class SubCategory(Base):
    __tablename__ = 'subcategories'
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment='Primary key for the subcategory')
    name = Column(String(100), nullable=False, comment='Name of the subcategory')
    category_id = Column(Integer, nullable=False, comment='Foreign key referencing the parent category')
    is_active = Column(Enum('0', '1'), default='1', comment='Whether the subcategory is active')

class TopicDistribution(Base):
    """Optimized engine-specific topic distribution tracking"""
    __tablename__ = "topic_distribution"
    
    id = Column(Integer, primary_key=True, autoincrement=True, comment="Primary key")
    search_engine = Column(String(50), nullable=False, comment="Search engine name")
    topic_id = Column(Integer, nullable=False, comment="Topic ID from subcategories table")
    topic_name = Column(String(255), nullable=False, comment="Topic name for reference")
    distribution_round = Column(Integer, nullable=False, comment="Distribution round number")
    is_rotating_topic = Column(Boolean, default=False, comment="Whether this is a rotating topic")
    used_at = Column(DateTime, default=None, comment="When this topic was used")
    is_active = Column(Boolean, default=True, comment="Whether this distribution is active")
    
    # Optimized indexes for better performance
    __table_args__ = (
        Index('idx_round', 'distribution_round'),
        Index('idx_engine_round', 'search_engine', 'distribution_round'),
        Index('idx_topic_round', 'topic_id', 'distribution_round'),
        Index('idx_rotating_topics', 'is_rotating_topic'),
        {'mysql_engine': 'InnoDB'},
    )

def create_subcategory_table():
    """Create the subcategories table in the database"""
    db_url = config.get_database_url()
    engine = create_engine(db_url)
    Base.metadata.create_all(engine)

def create_topic_distribution_table():
    """Create the topic_distribution table in the database"""
    db_url = config.get_database_url()
    engine = create_engine(db_url)
    Base.metadata.create_all(engine)
    print("Topic distribution table created successfully")

if __name__ == "__main__":
    create_subcategory_table()
    create_topic_distribution_table()
    print("Tables 'subcategories' and 'topic_distribution' created in MySQL database")
