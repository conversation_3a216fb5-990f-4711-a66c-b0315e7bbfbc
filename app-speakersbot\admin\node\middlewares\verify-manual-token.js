const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const Setting = require("../models/schema/setting");

const verifyManualToken = async (req, res, next) => {
    try {
        const token = req.headers['authorization'];
        if (!token) throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, 'No token provided');

        const setting = await Setting.findOne({ where: { key: 'manual_token' } });
        if (!setting) throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, 'Token not found');
        if (token !== (setting.value)) throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, 'Invalid token');
        next();
    }
    catch (err) {
        next(err);
    }
}

module.exports = verifyManualToken;