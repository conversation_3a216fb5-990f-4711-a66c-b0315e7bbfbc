import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  MapPin, 
  Calendar as CalendarIcon, 
  DollarSign, 
  Star, 
  TrendingUp, 
  TrendingDown,
  Users,
  Clock,
  Target,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Heart
} from 'lucide-react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { NotBookedModal } from '@/components/ui/not-booked-modal';
import { OpportunityDetailModal } from '@/components/opportunities/OpportunityDetailModal';
import { useGetOpportunitiesQuery } from '@/apis/dashboardApi';

export function OpportunityTab() {
  const [draggedItem, setDraggedItem] = useState<any>(null);
  const [acceptedOpportunities, setAcceptedOpportunities] = useState<Set<number>>(new Set());
  const [rejectedOpportunities, setRejectedOpportunities] = useState<Set<number>>(new Set());
  const [interestedOpportunities, setInterestedOpportunities] = useState<Set<number>>(new Set());
  const [bookedOpportunities, setBookedOpportunities] = useState<Set<number>>(new Set());
  const [notBookedOpportunities, setNotBookedOpportunities] = useState<Set<number>>(new Set());
  
  // RTK Query hook for opportunities
  const { 
    data: opportunitiesResponse, 
    isLoading: loading, 
    error: queryError 
  } = useGetOpportunitiesQuery({});
  
  const opportunities = opportunitiesResponse?.data?.opportunities || [];
  const error = queryError ? 'Failed to fetch opportunities' : null;
  
  // Modal states
  const [isNotBookedModalOpen, setIsNotBookedModalOpen] = useState(false);
  const [selectedOpportunityId, setSelectedOpportunityId] = useState<number | null>(null);
  const [isOpportunityDetailModalOpen, setIsOpportunityDetailModalOpen] = useState(false);
  const [selectedOpportunity, setSelectedOpportunity] = useState<any>(null);
  console.log(opportunitiesResponse)

  // Transform API data for discover feed
  const discoverOpportunities = opportunities.map((opp) => ({
    id: opp.id,
    title: opp.opportunity.title,
    organizer: opp.opportunity.organization,
    reason: `Your expertise in ${opp.opportunity.industry} perfectly aligns with their ${opp.opportunity.event_type} requirements.`,
    matchScore: Math.round(opp.overall_score),
    compensation: '$2,500', // Default compensation since not provided in API
    location: opp.opportunity.location,
    eventDate: opp.opportunity.start_date,
    lastApplyDate: opp.opportunity.end_date,
    type: opp.opportunity.event_type,
    topics: [opp.opportunity.industry, opp.opportunity.event_type],
    description: opp.opportunity.description,
    status: opp.status
  }));

  const handleAcceptOpportunity = (oppId: number) => {
    setAcceptedOpportunities(prev => new Set([...prev, oppId]));
    setRejectedOpportunities(prev => {
      const newSet = new Set(prev);
      newSet.delete(oppId);
      return newSet;
    });
    setInterestedOpportunities(prev => {
      const newSet = new Set(prev);
      newSet.delete(oppId);
      return newSet;
    });
  };

  const handleRejectOpportunity = (oppId: number) => {
    setRejectedOpportunities(prev => new Set([...prev, oppId]));
    setAcceptedOpportunities(prev => {
      const newSet = new Set(prev);
      newSet.delete(oppId);
      return newSet;
    });
    setInterestedOpportunities(prev => {
      const newSet = new Set(prev);
      newSet.delete(oppId);
      return newSet;
    });
  };

  const handleInterestedOpportunity = (oppId: number) => {
    setInterestedOpportunities(prev => new Set([...prev, oppId]));
    setAcceptedOpportunities(prev => {
      const newSet = new Set(prev);
      newSet.delete(oppId);
      return newSet;
    });
    setRejectedOpportunities(prev => {
      const newSet = new Set(prev);
      newSet.delete(oppId);
      return newSet;
    });
  };

  const handleBookOpportunity = (oppId: number) => {
    setBookedOpportunities(prev => new Set([...prev, oppId]));
    setNotBookedOpportunities(prev => {
      const newSet = new Set(prev);
      newSet.delete(oppId);
      return newSet;
    });
  };

  const handleNotBookOpportunity = (oppId: number) => {
    setSelectedOpportunityId(oppId);
    setIsNotBookedModalOpen(true);
  };

  const handleNotBookedSubmit = (reason: string, note: string) => {
    if (selectedOpportunityId) {
      setNotBookedOpportunities(prev => new Set([...prev, selectedOpportunityId]));
      setBookedOpportunities(prev => {
        const newSet = new Set(prev);
        newSet.delete(selectedOpportunityId);
        return newSet;
      });
      
      // Log the reason and note (in real app, send to backend)
      console.log(`Not booked reason for opportunity ${selectedOpportunityId}:`, { reason, note });
    }
    setSelectedOpportunityId(null);
  };

  const handleOpportunityClick = (opportunity: any) => {
    setSelectedOpportunity(opportunity);
    setIsOpportunityDetailModalOpen(true);
  };

  // Mock data for pipeline
  const [pipelineData, setPipelineData] = useState({
    pending: [
      { id: 4, title: 'React Conference', organizer: 'Frontend Guild', fee: '$2,800', deadline: '2024-02-25', matchScore: 89 },
      { id: 11, title: 'Vue.js Summit', organizer: 'Vue Community', fee: '$3,100', deadline: '2024-03-05', matchScore: 86 },
      { id: 12, title: 'GraphQL Conference', organizer: 'GraphQL Foundation', fee: '$2,500', deadline: '2024-02-28', matchScore: 91 }
    ],
    accepted: [
      { id: 3, title: 'Web3 Summit', organizer: 'Crypto Corp', fee: '$4,000', deadline: '2024-03-01', matchScore: 92 },
      { id: 13, title: 'Blockchain Expo', organizer: 'Crypto Institute', fee: '$4,500', deadline: '2024-03-10', matchScore: 88 }
    ],
    rejected: [
      { id: 14, title: 'Old Tech Conference', organizer: 'Legacy Corp', fee: '$1,500', deadline: '2024-02-20', matchScore: 65 },
      { id: 15, title: 'Outdated Summit', organizer: 'Past Tech', fee: '$1,200', deadline: '2024-02-18', matchScore: 58 }
    ],
    interested: [
      { id: 16, title: 'Future Tech Expo', organizer: 'Innovation Labs', fee: '$5,200', deadline: '2024-03-20', matchScore: 94 },
      { id: 17, title: 'AI/ML Conference', organizer: 'Machine Learning Society', fee: '$4,800', deadline: '2024-03-25', matchScore: 96 }
    ],
    applied: [
      { id: 5, title: 'Data Science Expo', organizer: 'Analytics Inc', fee: '$3,500', deadline: '2024-03-10', matchScore: 91 },
      { id: 18, title: 'Big Data Summit', organizer: 'Data Insights', fee: '$3,800', deadline: '2024-03-15', matchScore: 89 },
      { id: 19, title: 'Python Conference', organizer: 'Python Foundation', fee: '$3,200', deadline: '2024-03-12', matchScore: 87 }
    ],
    interviewing: [
      { id: 6, title: 'Mobile Dev Conference', organizer: 'App Masters', fee: '$3,200', deadline: '2024-03-15', matchScore: 87 },
      { id: 20, title: 'iOS Development Summit', organizer: 'Apple Developers', fee: '$4,100', deadline: '2024-03-18', matchScore: 92 }
    ],
    booked: [
      { id: 8, title: 'Startup Pitch Day', organizer: 'Venture Capital', fee: '$2,000', deadline: '2024-04-01', matchScore: 85 },
      { id: 21, title: 'Tech Leadership Forum', organizer: 'Leadership Institute', fee: '$5,500', deadline: '2024-04-05', matchScore: 93 },
      { id: 22, title: 'Cloud Computing Workshop', organizer: 'AWS Community', fee: '$4,200', deadline: '2024-04-10', matchScore: 90 }
    ]
  });

  // Mock data for lost opportunities
  const lostOpportunities = [
    { id: 9, title: 'Blockchain Summit', fee: '$5,000', matchScore: 96, reason: 'Missed deadline' },
    { id: 10, title: 'ML Conference', fee: '$3,800', matchScore: 89, reason: 'Declined by organizer' }
  ];

  const totalLostValue = lostOpportunities.reduce((sum, opp) => sum + parseInt(opp.fee.replace('$', '').replace(',', '')), 0);

  const moveOpportunity = (opportunity: any, fromStage: string, toStage: string) => {
    setPipelineData(prev => ({
      ...prev,
      [fromStage]: prev[fromStage].filter(item => item.id !== opportunity.id),
      [toStage]: [...prev[toStage], opportunity]
    }));
  };

  const handleDragStart = (e: any, opportunity: any, stage: string) => {
    setDraggedItem({ opportunity, fromStage: stage });
  };

  const handleDragOver = (e: any) => {
    e.preventDefault();
  };

  const handleDrop = (e: any, toStage: string) => {
    e.preventDefault();
    if (draggedItem) {
      moveOpportunity(draggedItem.opportunity, draggedItem.fromStage, toStage);
      setDraggedItem(null);
    }
  };

  const PipelineColumn = ({ title, stage, opportunities }: any) => (
    <div 
      className="bg-surface rounded-lg p-4 min-h-[300px] w-64"
      onDragOver={handleDragOver}
      onDrop={(e) => handleDrop(e, stage)}
    >
      <h3 className="font-semibold text-foreground mb-3 text-center">{title}</h3>
      <div className="space-y-3">
        {opportunities.map((opp: any) => (
          <div
            key={opp.id}
            draggable
            onDragStart={(e) => handleDragStart(e, opp, stage)}
            className="bg-card p-3 rounded-md border border-border cursor-move hover:shadow-md transition-shadow"
          >
            <h4 className="font-medium text-sm text-foreground mb-1">{opp.title}</h4>
            <p className="text-xs text-muted-foreground mb-2">{opp.organizer}</p>
            <div className="flex justify-between items-center text-xs">
              <span className="text-primary font-medium">{opp.fee}</span>
              <Badge variant="secondary" className="text-xs">
                {opp.matchScore}% match
              </Badge>
            </div>
            <p className="text-xs text-muted-foreground mt-1">Due: {opp.deadline}</p>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <Tabs defaultValue="discover" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="discover" className="data-[state=active]:bg-gradient-tertiary">Discover Feed</TabsTrigger>
          <TabsTrigger value="pipeline" className="data-[state=active]:bg-gradient-tertiary">Pipeline</TabsTrigger>
        </TabsList>

        {/* Discover Feed */}
        <TabsContent value="discover" className="space-y-4">
          <Card className='bg-surface'>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                You Might Also Fit
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {loading && (
                <div className="flex items-center justify-center py-8">
                  <div className="text-muted-foreground">Loading opportunities...</div>
                </div>
              )}
              
              {error && (
                <div className="flex items-center justify-center py-8">
                  <div className="text-destructive">{error}</div>
                </div>
              )}
              
              {!loading && !error && discoverOpportunities.length === 0 && (
                <div className="flex items-center justify-center py-8">
                  <div className="text-muted-foreground">No opportunities found</div>
                </div>
              )}
              
              {!loading && !error && discoverOpportunities.map((opp) => (
                <Card 
                  key={opp.id} 
                  className="bg-card border-border shadow-sm cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => handleOpportunityClick(opp)}
                >
                  <CardContent className="p-0">
                    <div className="flex">
                      {/* Match Score Circle - Left Side */}
                      <div className="flex flex-col items-center justify-center p-6 bg-surface-elevated border-r border-border-subtle rounded-md">
                        <div className="relative w-16 h-16 mb-2">
                          <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                            <path
                              d="M18 2.0845
                                a 15.9155 15.9155 0 0 1 0 31.831
                                a 15.9155 15.9155 0 0 1 0 -31.831"
                              fill="none"
                              stroke="hsl(var(--muted))"
                              strokeWidth="2"
                            />
                            <path
                              d="M18 2.0845
                                a 15.9155 15.9155 0 0 1 0 31.831
                                a 15.9155 15.9155 0 0 1 0 -31.831"
                              fill="none"
                              stroke="hsl(var(--primary))"
                              strokeWidth="2.5"
                              strokeDasharray={`${opp.matchScore}, 100`}
                              className="drop-shadow-sm"
                            />
                          </svg>
                          <div className="absolute inset-0 flex items-center justify-center">
                            <span className="text-lg font-bold text-primary">{opp.matchScore}%</span>
                          </div>
                        </div>
                        <span className="text-xs font-medium text-primary uppercase tracking-wide text-center">Match Score</span>
                      </div>

                      {/* Main Content Area */}
                      <div className="flex-1 p-5">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <h3 className="text-lg font-semibold text-foreground mb-1">{opp.title}</h3>
                            <p className="text-sm text-foreground-muted">Organized by {opp.organizer}</p>
                          </div>
                          
                           {/* Status Indicator - Top Right */}
                           <div className="flex gap-1 ml-4">
                             {opp.status === 'interested' && (
                               <Badge variant="outline" className="border-yellow-600 text-yellow-600 bg-yellow-50">
                                 <Heart className="h-3 w-3 mr-1" />
                                 Interested
                               </Badge>
                             )}
                             {opp.status === 'accepted' && (
                               <Badge variant="outline" className="border-green-600 text-green-600 bg-green-50">
                                 <CheckCircle className="h-3 w-3 mr-1" />
                                 Accepted
                               </Badge>
                             )}
                             {opp.status === 'rejected' && (
                               <Badge variant="outline" className="border-red-600 text-red-600 bg-red-50">
                                 <XCircle className="h-3 w-3 mr-1" />
                                 Rejected
                               </Badge>
                             )}
                             {opp.status === 'booked' && (
                               <Badge variant="outline" className="border-blue-600 text-blue-600 bg-blue-50">
                                 <CheckCircle className="h-3 w-3 mr-1" />
                                 Booked
                               </Badge>
                             )}
                             {opp.status === 'not_booked' && (
                               <Badge variant="outline" className="border-gray-600 text-gray-600 bg-gray-50">
                                 <XCircle className="h-3 w-3 mr-1" />
                                 Not Booked
                               </Badge>
                             )}
                             {acceptedOpportunities.has(opp.id) && (
                               <Badge variant="outline" className="border-green-600 text-green-600 bg-green-50">
                                 <CheckCircle className="h-3 w-3 mr-1" />
                                 Accepted
                               </Badge>
                             )}
                             {interestedOpportunities.has(opp.id) && (
                               <Badge variant="outline" className="border-yellow-600 text-yellow-600 bg-yellow-50">
                                 <Heart className="h-3 w-3 mr-1" />
                                 Interested
                               </Badge>
                             )}
                             {rejectedOpportunities.has(opp.id) && (
                               <Badge variant="outline" className="border-red-600 text-red-600 bg-red-50">
                                 <XCircle className="h-3 w-3 mr-1" />
                                 Rejected
                               </Badge>
                             )}
                             {bookedOpportunities.has(opp.id) && (
                               <Badge variant="outline" className="border-blue-600 text-blue-600 bg-blue-50">
                                 <CheckCircle className="h-3 w-3 mr-1" />
                                 Booked
                               </Badge>
                             )}
                             {notBookedOpportunities.has(opp.id) && (
                               <Badge variant="outline" className="border-gray-600 text-gray-600 bg-gray-50">
                                 <XCircle className="h-3 w-3 mr-1" />
                                 Not Booked
                               </Badge>
                             )}
                           </div>
                        </div>
                        
                        {/* Match Reason */}
                        <p className="text-sm mb-4 italic leading-relaxed text-primary">
                          {opp.reason}
                        </p>
                        
                        {/* Meta Information */}
                        <div className="flex items-center gap-6 text-sm text-foreground-muted mb-4 flex-wrap">
                          <div className="flex items-center gap-2">
                            <MapPin className="h-4 w-4  flex-shrink-0 text-primary" />
                            <span>{opp.location}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <CalendarIcon className="h-4 w-4  flex-shrink-0 text-primary" />
                            <span>Event: {new Date(opp.eventDate).toLocaleDateString()}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Clock className="h-4 w-4 flex-shrink-0 text-primary" />
                            <span>Apply by: {new Date(opp.lastApplyDate).toLocaleDateString()}</span>
                          </div>
                        </div>

                        {/* Description */}
                        <p className="text-sm text-foreground-muted mb-4 leading-relaxed">
                          {opp.description}
                        </p>

                        {/* Tags Row */}
                        <div className="flex flex-wrap gap-2 mb-4">
                          {opp.topics.map((topic, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary border border-primary/20"
                            >
                              {topic}
                            </span>
                          ))}
                        </div>

                        {/* Bottom Row */}
                        <div className="flex items-center justify-between pt-3 border-border-light border-t-[1px]">
                          <div className="flex items-center gap-4">
                            <div className="text-lg font-bold text-foreground">
                              {opp.compensation}
                            </div>
                            <div className="text-sm text-foreground-muted">
                              compensation
                            </div>
                          </div>
                          
                           <div className="flex items-center gap-2">
                             {/* Show Accept, Interested, Reject buttons only when no action taken */}
                             {opp.status === 'pending' && 
                              !acceptedOpportunities.has(opp.id) && 
                              !rejectedOpportunities.has(opp.id) && 
                              !interestedOpportunities.has(opp.id) && 
                              !bookedOpportunities.has(opp.id) && 
                              !notBookedOpportunities.has(opp.id) && (
                               <>
                                 <Button
                                   size="sm"
                                   variant="outline"
                                   className="border-green-600 text-green-600 hover:bg-green-50"
                                   onClick={(e) => {
                                     e.stopPropagation();
                                     handleAcceptOpportunity(opp.id);
                                   }}
                                 >
                                   <CheckCircle className="h-4 w-4 mr-1" />
                                   Accept
                                 </Button>
                                 <Button
                                   size="sm"
                                   variant="outline"
                                   className="border-yellow-600 text-yellow-600 hover:bg-yellow-50"
                                   onClick={(e) => {
                                     e.stopPropagation();
                                     handleInterestedOpportunity(opp.id);
                                   }}
                                 >
                                   <Heart className="h-4 w-4 mr-1" />
                                   Interested
                                 </Button>
                                 <Button
                                   size="sm"
                                   variant="outline"
                                   className="border-red-600 text-red-600 hover:bg-red-50"
                                   onClick={(e) => {
                                     e.stopPropagation();
                                     handleRejectOpportunity(opp.id);
                                   }}
                                 >
                                   <XCircle className="h-4 w-4 mr-1" />
                                   Reject
                                 </Button>
                               </>
                             )}
                             
                             {/* Show Booked/Not Booked buttons only after accepting */}
                             {(opp.status === 'accepted' || acceptedOpportunities.has(opp.id)) && 
                              !bookedOpportunities.has(opp.id) && 
                              !notBookedOpportunities.has(opp.id) && (
                               <>
                                 <Button
                                   size="sm"
                                   variant="outline"
                                   className="border-blue-600 text-blue-600 hover:bg-blue-50"
                                   onClick={(e) => {
                                     e.stopPropagation();
                                     handleBookOpportunity(opp.id);
                                   }}
                                 >
                                   <CheckCircle className="h-4 w-4 mr-1" />
                                   Booked
                                 </Button>
                                 <Button
                                   size="sm"
                                   variant="outline"
                                   className="border-gray-600 text-gray-600 hover:bg-gray-50"
                                   onClick={(e) => {
                                     e.stopPropagation();
                                     handleNotBookOpportunity(opp.id);
                                   }}
                                 >
                                   <XCircle className="h-4 w-4 mr-1" />
                                   Not Booked
                                 </Button>
                               </>
                             )}
                           </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5" />
                Lost Opportunities Tracker
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Lost Opportunities</p>
                        <p className="text-2xl font-bold text-destructive">{lostOpportunities.length}</p>
                      </div>
                      <AlertTriangle className="h-8 w-8 text-destructive" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Money Left on Table</p>
                        <p className="text-2xl font-bold text-destructive">${totalLostValue.toLocaleString()}</p>
                      </div>
                      <DollarSign className="h-8 w-8 text-destructive" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-muted-foreground">Performance Score</p>
                        <p className="text-2xl font-bold text-primary">73%</p>
                        <Progress value={73} className="mt-2" />
                      </div>
                      <TrendingDown className="h-8 w-8 text-primary" />
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Missed Opportunity</TableHead>
                    <TableHead>Reason</TableHead>
                    <TableHead>Match %</TableHead>
                    <TableHead>Lost Fee</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {lostOpportunities.map((opp) => (
                    <TableRow key={opp.id}>
                      <TableCell className="font-medium">{opp.title}</TableCell>
                      <TableCell>{opp.reason}</TableCell>
                      <TableCell>
                        <Badge variant="outline">{opp.matchScore}%</Badge>
                      </TableCell>
                      <TableCell className="font-semibold text-destructive">{opp.fee}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Pipeline */}
        <TabsContent value="pipeline">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Opportunity Pipeline
              </CardTitle>
            </CardHeader>
            <CardContent className="p-0">
              <div className="p-6 overflow-x-auto">
                <div className="flex gap-4 min-w-max">
                  <PipelineColumn title="Pending" stage="pending" opportunities={pipelineData.pending} />
                  <PipelineColumn title="Accepted" stage="accepted" opportunities={pipelineData.accepted} />
                  <PipelineColumn title="Rejected" stage="rejected" opportunities={pipelineData.rejected} />
                  <PipelineColumn title="Interested" stage="interested" opportunities={pipelineData.interested} />
                  <PipelineColumn title="Applied" stage="applied" opportunities={pipelineData.applied} />
                  <PipelineColumn title="Interviewing" stage="interviewing" opportunities={pipelineData.interviewing} />
                  <PipelineColumn title="Booked" stage="booked" opportunities={pipelineData.booked} />
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

      </Tabs>

      {/* Not Booked Modal */}
      <NotBookedModal
        isOpen={isNotBookedModalOpen}
        onClose={() => {
          setIsNotBookedModalOpen(false);
          setSelectedOpportunityId(null);
        }}
        onSubmit={handleNotBookedSubmit}
        title="Why wasn't this opportunity booked?"
      />

      {/* Opportunity Detail Modal */}
      <OpportunityDetailModal
        opportunity={selectedOpportunity}
        isOpen={isOpportunityDetailModalOpen}
        onClose={() => {
          setIsOpportunityDetailModalOpen(false);
          setSelectedOpportunity(null);
        }}
        acceptedOpportunities={acceptedOpportunities}
        bookedOpportunities={bookedOpportunities}
        notBookedOpportunities={notBookedOpportunities}
        interestedOpportunities={interestedOpportunities}
        rejectedOpportunities={rejectedOpportunities}
        onAccept={handleAcceptOpportunity}
        onReject={handleRejectOpportunity}
        onInterested={handleInterestedOpportunity}
        onBook={handleBookOpportunity}
        onNotBook={handleNotBookOpportunity}
      />
    </div>
  );
}