const { RESPONSE_CODES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");
const { Op } = require("sequelize");
const { Speakers, SpeakerDetails, Opportunities, Subscriptions, PricingPlan, SpeakerOpportunity, SpeakerHistory, FormType, FormQuestion, GamificationData } = require("../models");
const connection = require("../models/connection");
const { parsePagination, getPagingData, buildSearchWhereClause, parseJSONSafely, buildFilterWhereClause, encodeToBase64 } = require("../helpers/app-hepler");
const bcrypt = require("bcrypt");
const emailService = require("./email-service");
const CONFIG = require("../config/config");
const jwtHelper = require("../helpers/jwt-helper");
const jwt = require("jsonwebtoken");
const FormQuestions = require("../models");;
const speakerHistoryService = require("./gamification-service");
const uploadFileToS3 = require("../helpers/upload-to-s3");

const speakerService = {};

// ------------------------- speaker-service -------------------------

/**
 * Fetch all speakers with optional search and pagination.
 * Comprehensive speaker management with advanced filtering, sorting, and data export.
 * Includes subscription status, pricing plan details, and speaker history.
 * 
 * @param {Object} getReq - The request object containing query params
 * @param {Object} getReq.query - Query parameters
 * @param {string} [getReq.query.search] - Search term for name/email
 * @param {string} [getReq.query.filter] - JSON filter object
 * @param {string} [getReq.query.sort] - JSON sort object
 * @param {number} [getReq.query.page] - Page number
 * @param {number} [getReq.query.limit] - Items per page
 * @param {boolean} [getReq.query.export] - Export data as CSV
 * @returns {Promise<Object>} List of speakers with pagination data or CSV export
 * @throws {Error} When database operation fails
 */


speakerService.getAllSpeakers = async (getReq) => {
    try {
        const { page, limit, offset } = parsePagination(getReq.query);

        let where = {};
        let subscriptionWhere = {};
        const filter = parseJSONSafely(getReq.query.filter, "Invalid JSON filter");
        const dateRange = parseJSONSafely(getReq.query.dateRange, "Invalid JSON date range");
        const search = getReq.query.search;


        let planFilter = null;
        if (filter?.plan !== undefined) {
            planFilter = filter.plan;
            delete filter.plan;
        }


        if (filter) {
            const filterWhere = buildFilterWhereClause(filter);
            where = { ...where, ...filterWhere };
        }
        console.log("Where:", filter);


        if (dateRange && (dateRange.start_date || dateRange.end_date)) {
            where.created_at = { [Op.between]: [dateRange.start_date, dateRange.end_date] };
        }


        const searchFields = [
            'name', 'email', 'phone_number', 'city', 'company', 'title',
            'state', 'linkedin', 'headshot', 'bio', 'source', 'status'
        ];
        if (search) {
            const searchWhere = buildSearchWhereClause(search, searchFields);
            where = { ...where, ...searchWhere };
        }

        if (planFilter) {
            if (planFilter === "premium") {
                subscriptionWhere.plan_id = 1;
                subscriptionWhere.status = "active";
            } else if (planFilter === "basic") {
                subscriptionWhere.plan_id = 2;
                subscriptionWhere.status = "active";
            } else if (planFilter === "expire") {
                subscriptionWhere.status = "expired";
                // or check end_date < now
                // subscriptionWhere.end_date = { [Op.lt]: new Date() };
            } else if (planFilter === "not_activated") {
                subscriptionWhere = null; // no subscription
            }
        }


        // Build sort order from query.sort if provided
        const sort = parseJSONSafely(getReq.query.sort, "Invalid JSON sort");
        let sortOrder = [['id', 'DESC']];
        if (sort && typeof sort === 'object' && Object.keys(sort).length > 0) {
            sortOrder = Object.entries(sort).map(([field, direction]) => {
                const dir = String(direction || 'DESC').toUpperCase();
                return [field, dir === 'ASC' ? 'ASC' : 'DESC'];
            });
        }

        const speakers = await Speakers.findAll({
            where,
            include: [
                { model: SpeakerDetails, as: 'details' },
                {
                    model: Subscriptions,
                    as: 'subscription',
                    required: planFilter === "not_activated" ? false : !!planFilter,
                    where: subscriptionWhere || undefined,
                    include: [{ model: PricingPlan, as: 'plan' }]
                }
            ],
            limit,
            offset,
            order: sortOrder
        });

        const speakerIds = speakers.map(s => s.id);
        let countsMap = {};
        if (speakerIds.length > 0) {
            const opportunityCounts = await SpeakerOpportunity.findAll({
                where: { speaker_id: speakerIds },
                attributes: [
                    'speaker_id',
                    'status',
                    [connection.fn('COUNT', connection.col('status')), 'count']
                ],
                group: ['speaker_id', 'status'],
                raw: true
            });

            opportunityCounts.forEach(({ speaker_id, status, count }) => {
                if (!countsMap[speaker_id]) {
                    countsMap[speaker_id] = { Matched: 0, Accepted: 0, Interested: 0, Rejected: 0, Pending: 0 };
                }
                const s = (status || '').toLowerCase();
                if (['pending', 'accepted', 'interested', 'rejected'].includes(s)) {
                    countsMap[speaker_id].Matched += Number(count);
                    countsMap[speaker_id][s.charAt(0).toUpperCase() + s.slice(1)] += Number(count);
                }
            });
        }


        const formattedSpeakers = speakers.map((speaker) => {
            const { details = [], subscription, ...baseData } = speaker.toJSON();

            // Details into key-value without overwriting base fields
            const baseFieldNames = new Set(Object.keys(baseData));
            details.forEach(({ key, value }) => {
                if (!baseFieldNames.has(key)) {
                    baseData[key] = value;
                }
            });

            // Subscription info
            if (subscription?.status === 'active') {
                baseData.planType = subscription.plan_id === 1 ? 'premium' : 'basic';
                baseData.subscriptionDetail = subscription;
            } else if (subscription?.status === 'expired') {
                baseData.planType = 'expire';
                baseData.subscriptionDetail = subscription;
            } else {
                baseData.planType = 'not_activated';
                baseData.subscriptionDetail = null;
            }

            // Opportunity counts
            baseData.opportunityCounts = countsMap[speaker.id] || { Matched: 0, Accepted: 0, Interested: 0, Rejected: 0, Pending: 0 };

            return baseData;
        });


        const alltotalSpeaker = await Speakers.count();
        const activeSpeaker = await Speakers.count({ where: { status: 'active' } });
        const premiumSpeakers = await Subscriptions.count({ where: { status: 'active', plan_id: 1 } });
        const basicSpeakers = await Subscriptions.count({ where: { status: 'active', plan_id: 2 } });
        const notActivatedSpeakers = alltotalSpeaker - (premiumSpeakers + basicSpeakers);
        const summary = { alltotalSpeaker, activeSpeaker, premiumSpeakers, basicSpeakers, notActivatedSpeakers };


        const totalSpeakers = await Speakers.count({
            where,
            include: [
                {
                    model: Subscriptions,
                    as: 'subscription',
                    required: planFilter === "not_activated" ? false : !!planFilter,
                    where: subscriptionWhere || undefined
                }
            ]
        });
        const pageData = getPagingData(totalSpeakers, limit, page);

        
        return {
            status: true,
            message: "Speakers fetched successfully",
            data: { speakers: formattedSpeakers, pageData, summary }
        };

    } catch (error) {
        console.error("Error fetching speakers:", error);
        throw error;
    }
};

/**
 * Fetch a single speaker by ID.
 * @param {Object} getReq - The request object containing params and query.
 * @param {string} getReq.params.id - The speaker ID.
 * @param {string} [getReq.query.status] - Optional status filter for opportunities ('pending', 'accepted', 'rejected', 'interested').
 * @returns {Promise<Object>} The speaker data with filtered opportunities if status is provided.
 */
speakerService.getSpeakerById = async (getReq) => {
    try {
        const speakerId = getReq.params.id;
        const status = getReq.query.status; // Get status filter from query parameters

        if (!speakerId) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Speaker ID is required");
        }

        // Build include options for opportunities
        const opportunityInclude = {
            model: Opportunities,
            as: 'opportunities'
        };

        // Add status filter if provided
        if (status) {
            opportunityInclude.through = {
                where: { status: status }
            };
        }

        const speaker = await Speakers.findOne({
            where: {
                id: speakerId,
            },
            include: [
                { model: SpeakerDetails, as: 'details' },
                {
                    model: Subscriptions,
                    as: 'subscription',
                    include: [{ model: PricingPlan, as: 'plan' }]
                },
                {
                    model: GamificationData,
                    as: 'gamificationData',
                    limit: 10,
                    order: [['created_at', 'DESC']]
                }
            ],
        });

        // Calculate total points sum
        const totalPointsResult = await GamificationData.findOne({
            where: { speaker_id: speakerId },
            attributes: [
                [connection.fn('SUM', connection.col('points')), 'currentBalance']
            ],
            raw: true
        });

        const currentBalance = totalPointsResult?.currentBalance || 0;

        // Get top 5 recent gamification history with formatted data
        const creditHistory = await GamificationData.findAll({
            where: { speaker_id: speakerId },
            attributes: ['points', 'created_at'],
            order: [['created_at', 'DESC']],
            limit: 5,
            raw: true
        });

        const formattedHistory = creditHistory.map(item => {
            const date = new Date(item.created_at);
            const formattedDate = date.toLocaleDateString('en-GB', {
                day: 'numeric',
                month: 'short'
            }).replace(' ', '');

            const pointsText = item.points > 0 ? `+${item.points}` : `${item.points}`;

            return {
                date: formattedDate,
                change: item.points,
                displayText: `${formattedDate} ${pointsText}points`,
                source: item.source,
                type: item.type,
                created_at: item.created_at
            };
        });

        if (!speaker) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Speaker not found");
        }

        // Get form data with questions and speaker answers using Sequelize
        const formTypes = await FormType.findAll({
            attributes: ['id', 'priority', 'title', 'created_at', 'updated_at'],
            include: [{
                model: FormQuestion,
                as: 'questions',
                attributes: ['id', 'question', 'form_type_id', 'field_type', 'field_id', 'is_required', 'options_json', 'created_at', 'updated_at'],
                include: [{
                    model: SpeakerDetails,
                    as: 'speakerDetails',
                    where: { speaker_id: speakerId },
                    required: false,
                    attributes: ['value']
                }],
                required: false
            }],
            order: [
                ['priority', 'ASC'],
                [{ model: FormQuestion, as: 'questions' }, 'id', 'ASC']
            ]
        });

        // Transform form data to the required structure
        const formData = formTypes.map(formType => ({
            form_type_id: formType.id,
            form_type_name: formType.title,
            questions: formType.questions.map(question => {
                console.log(`Question ${question.field_id}:`, {
                    speakerDetails: question.speakerDetails,
                    hasDetails: question.speakerDetails && question.speakerDetails.length > 0
                });

                return {
                    field_id: question.field_id,
                    question: question.question,
                    type: question.field_type,
                    is_required: question.is_required ? "1" : "0",
                    value: question.options_json,
                    selected_value: question.speakerDetails && question.speakerDetails.length > 0
                        ? question.speakerDetails[0].value
                        : null
                };
            })
        }));

        const transformedSpeaker = { ...speaker.toJSON() };

        // Remove details from base object and add form-data
        delete transformedSpeaker.details;
        transformedSpeaker['form-data'] = formData;

        // Add subscription details
        if (speaker.subscription) {
            transformedSpeaker.subscription = speaker.subscription;
            if (speaker.subscription.status === 'active') {
                transformedSpeaker.planType = speaker.subscription.plan_id === 1 ? 'premium' : 'basic';
            } else {
                transformedSpeaker.planType = 'not_activated';
            }
        } else {
            transformedSpeaker.subscription = null;
            transformedSpeaker.planType = 'not_activated';
        }

        transformedSpeaker.currentBalance = parseInt(currentBalance) || 0;
        transformedSpeaker.creditHistory = formattedHistory;

        return {
            status: true,
            message: "Speaker fetched successfully",
            data: transformedSpeaker
        };
    } catch (error) {
        console.error("Error fetching speaker by ID:", error);
        throw error;
    }
};

/**
 * Update a speaker and their details.
 * @param {Object} updateReq - The request object containing params and body.
 * @returns {Promise<Object>} Result of update operation.
 */
speakerService.updateSpeaker = async (updateReq) => {
    try {
        const speakerId = updateReq.params.id;
        const updateData = { ...updateReq.body };

        const speaker = await Speakers.findOne({ where: { id: speakerId } });

        if (!speaker) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Speaker not found");
        }

        if (updateData.email) {
            const emailExists = await Speakers.findOne({ where: { email: updateData.email } });
            if (emailExists && emailExists.id !== speaker.id) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email already exists");
            }
        }

        const speakerFields = {};
        const detailFields = {};
        Object.entries(updateData).forEach(([key, value]) => {
            if (key.startsWith("field_")) detailFields[key] = value;
            else speakerFields[key] = value;
        });

        // Update main speaker fields
        if (Object.keys(speakerFields).length > 0) {
            await Speakers.update(speakerFields, { where: { id: speakerId } });
        }

        const existingDetails = await SpeakerDetails.findAll({ where: { speaker_id: speakerId } });
        const existingMap = Object.fromEntries(existingDetails.map(d => [d.field_id, d]));

        const detailUpdates = Object.entries(detailFields).map(async ([fieldId, value]) => {
            if (existingMap[fieldId]) {
                await SpeakerDetails.update({ value }, { where: { id: existingMap[fieldId].id } });
            }
        });
        await Promise.all(detailUpdates);

        const gamificationTasks = [];

        const specialFieldHandlers = {
            "field_14": "headshot_update",
            "field_13": "bio_update",
            "field_37": "video_upload",
            "field_45": "add_testimonial",
            "topic": "topic_update"
        };
        for (const [field, actionKey] of Object.entries(specialFieldHandlers)) {
            if ((field.startsWith("field_") && detailFields[field]) || (field === "topic" && updateData.topic)) {
                gamificationTasks.push(speakerHistoryService.callEventPoints(speakerId, actionKey));
            }
        }

        // Optional questions (is_required = 0)
        const optionalQuestions = await FormQuestions.findAll({ where: { is_required: 0 } });
        for (const question of optionalQuestions) {
            const fieldId = question.field_id;
            if (updateData[fieldId] || detailFields[fieldId]) {
                gamificationTasks.push(speakerHistoryService.callEventPoints(speakerId, "optional_question"));
            }
        }

        // Run gamification tasks in parallel
        await Promise.all(gamificationTasks);

        return { status: true, message: "Speaker updated successfully" };

    } catch (error) {
        console.error("Error updating speaker:", error);
        throw error;
    }
};

/** 
 * Export speakers.
 * @param {Object} exportReq - The request object containing params.
 * @returns {Promise<Object>} Result of export operation.
 */
speakerService.exportSpeakers = async (req, res) => {
    try {
        const { ids } = req.body || {};

        let where = {};

        if (ids && ids.length > 0) {
            where.id = ids;
        }
        const speakers = await Speakers.findAll({
            where,
            raw: true,
        });

        if (!speakers || speakers.length === 0) {
            new CustomError(RESPONSE_CODES.NOT_FOUND, "No speaker found")
        }
        // Convert JSON -> CSV
        return exportAsCSV(res, speakers, "speakers.csv");

    } catch (error) {
        console.error("Error exporting speakers:", error);
        res.status(500).json({ message: "Error exporting speakers" });
    }
};


speakerService.intakeSpeakers = async (intakeReq) => {
    try {
        const id = intakeReq.userId;    

        if (!id) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Unauthorized");
        }

        const files = intakeReq.files;
        const uploadedFiles = {};

        if (files && files.length > 0) {
            for (const file of files) {
                const fileUrl = await uploadFileToS3(file, 'speakers');
                console.log("Uploaded file URL:", fileUrl);
                console.log(file.fieldname)
                uploadedFiles[file.fieldname] = fileUrl;
            }
        }   

        const details = intakeReq.body;

        // Extract speaker main table fields from the details
        const speakerData = {};

        if (details && typeof details === 'object') {
            // Map field IDs to speaker table columns
            const fieldMapping = {
                'field-12': 'title',
                'field-5': 'email',
                'field-6': 'phone_number',
                'field-7': 'city',
                'field-8': 'state',
                'field-11': 'linkedin',
                'field-13': 'bio',
                'field-1': 'primary_category',
                'field-2': 'subcategory',
                'field-3': 'topic',
                'field-9': 'country',
                'field-21': 'differentiators',
                'field-20': 'top_keywords',
                'field-14': 'headshot',
                'field-10': 'speaker_website',
                'field-15': 'learning_objectives',
                'field-16': 'takeaways',
                'field-18': 'preferred_speaker_geography',
                'field-19': 'speaker_credentials',
                'field-17': 'challenges'
            };

            // Extract mapped fields for speaker table  
            for (const [fieldId, value] of Object.entries(details)) {
                if (fieldMapping[fieldId]) {
                    speakerData[fieldMapping[fieldId]] = value;
                }
            }
        }

        // Update speaker main record if we have any mapped data
        if (Object.keys(speakerData).length > 0) {
            await Speakers.update(
                speakerData,
                {
                    where: {
                        id: id
                    }
                }
            );
        }

        // Get all field IDs that we need to process (both files and form fields)
        const allFieldIds = new Set([
            ...Object.keys(uploadedFiles),
            ...(details ? Object.keys(details) : [])
        ]);

        // Fetch questions for all field IDs to get the actual question text
        const questionsMap = {};
        if (allFieldIds.size > 0) {
            const questions = await FormQuestion.findAll({
                where: {
                    field_id: Array.from(allFieldIds)
                },
                attributes: ['field_id', 'question'],
                raw: true
            });

            questions.forEach(q => {
                questionsMap[q.field_id] = q.question;
            });
        }

        // Handle file uploads in SpeakerDetails
        if (Object.keys(uploadedFiles).length > 0) {
            for (const [fieldname, fileUrl] of Object.entries(uploadedFiles)) {
                const questionText = questionsMap[fieldname] || fieldname; // fallback to fieldname if question not found

                const existingDetail = await SpeakerDetails.findOne({
                    where: {
                        speaker_id: id,
                        field_id: fieldname
                    }
                });

                if (existingDetail) {
                    await SpeakerDetails.update(
                        {
                            value: fileUrl,
                            key: questionText
                        },
                        {
                            where: {
                                speaker_id: id,
                                field_id: fieldname
                            }
                        }
                    );
                } else {
                    await SpeakerDetails.create({
                        speaker_id: id,
                        field_id: fieldname,
                        key: questionText,
                        value: fileUrl
                    });
                }
            }
        }

        // Handle all form fields in SpeakerDetails (including mapped and unmapped fields)
        if (details && typeof details === 'object') {
            for (const [fieldId, value] of Object.entries(details)) {
                const questionText = questionsMap[fieldId] || fieldId; // fallback to fieldId if question not found

                const existingDetail = await SpeakerDetails.findOne({
                    where: {
                        speaker_id: id,
                        field_id: fieldId
                    }
                });

                if (existingDetail) {
                    await SpeakerDetails.update(
                        {
                            value: value,
                            key: questionText
                        },
                        {
                            where: {
                                speaker_id: id,
                                field_id: fieldId
                            }
                        }
                    );
                } else {
                    await SpeakerDetails.create({
                        speaker_id: id,
                        field_id: fieldId,
                        key: questionText,
                        value: value
                    });
                }
            }
        }

        return {
            status: true,
            message: "Speakers intaked successfully",
        };
    } catch (error) {
        console.error("Error intaking speakers:", error);
 
        throw error;
    }
}


speakerService.getSpeakerOpportunities = async (getReq) => {
    try {

        const { page, limit, offset } = parsePagination(getReq.query);

        const speakerId = getReq.params.id;

        let where = {};

        const filter = parseJSONSafely(getReq.query.filter, "Invalid JSON filter");

        console.log("Raw filter:", filter);

        if (filter) {
            const filterWhere = buildFilterWhereClause(filter);
            console.log("Filter where clause:", filterWhere);
            where = { ...where, ...filterWhere };
        }



        const finalWhere = { speaker_id: speakerId, ...where };

        // Check what status values exist for this speaker
        const statusCheck = await SpeakerOpportunity.findAll({
            where: { speaker_id: speakerId },
            attributes: ['status'],
            group: ['status'],
            raw: true
        });


        const opportunities = await SpeakerOpportunity.findAll({
            where: finalWhere,
            include: [{ model: Opportunities, as: 'opportunity' }],
            limit,
            offset,
        });


        const totalOpportunities = await SpeakerOpportunity.count({ where: finalWhere });
        const pageData = getPagingData(totalOpportunities, limit, page);
        return {
            status: true,
            message: "Speaker opportunities fetched successfully",
            data: opportunities,
            pageData
        };

    } catch (error) {
        console.error("Error fetching speaker opportunities:", error);
        throw error;
    }
}


/**
 * Verify speaker email with token
 * @param {string} token - Email verification token
 */
speakerService.verifyEmail = async (token) => {
    try {
        let decoded;
        try {
            decoded = jwt.verify(token, CONFIG.JWT_SECRET);
            if (!decoded) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid or expired verification token");
            }
        } catch (error) {
            if (error instanceof jwt.TokenExpiredError) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Token expired");
            }
            if (error instanceof jwt.JsonWebTokenError) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid verification token");
            }
            throw error;
        }
        // Find speaker with valid token
        const speaker = await Speakers.findOne({
            where: {
                id: decoded.id,
                email: decoded.email
            }
        });

        if (!speaker) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid or expired verification token");
        }

        // Update speaker status
        await speaker.update({
            email_verified: '1',
        });

        return {
            id: speaker.id,
            name: speaker.name,
            email: speaker.email,
            email_verified: speaker.email_verified
        }

    } catch (error) {
        console.error('Error verifying email:', error);
        throw error;
    }
};

/**
 * Resend verification email
 * @param {string} email - Speaker's email address
 */
speakerService.resendVerificationEmail = async (email) => {
    try {
        const speaker = await Speakers.findOne({
            where: { email: email.trim().toLowerCase() }
        });

        if (!speaker) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Speaker not found");
        }

        if (speaker.email_verified === '1') {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email already verified");
        }

        // Generate new verification token
        const verificationToken = jwtHelper.generateToken({ id: speaker.id, email: speaker.email }, '1d');

        // console.log("Verification Token:", verificationToken);

        // Send verification email
        const baseUrl = CONFIG.EXTENSION_BASE_URL;
        await emailService.sendVerificationEmail(
            speaker.email,
            speaker.name,
            encodeToBase64(verificationToken),
            baseUrl
        );

    } catch (error) {
        console.error('Error resending verification email:', error);
        throw error;
    }
};

/**
 * Check if speaker email is verified
 * @param {string} speakerId - Speaker ID
 */
speakerService.checkEmailVerified = async (speakerId) => {
    try {
        const speaker = await Speakers.findOne({ where: { id: speakerId } });
        if (!speaker) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Speaker not found");
        }
        return speaker.email_verified === '1';

    } catch (error) {
        console.error('Error checking email verified:', error);
        throw error;
    }
}

speakerService.getSpeakerHistory = async (getReq) => {
    try {
        const { page, limit, offset } = parsePagination(getReq.query);
        const speakerId = getReq.params.id;

        const history = await GamificationData.findAll({
            where: { speaker_id: speakerId },
            limit,
            offset,
        });
        const totalHistory = await GamificationData.count({ where: { speaker_id: speakerId } });
        const pageData = getPagingData(totalHistory, limit, page);
        return {
            status: true,
            message: "Speaker gamification data fetched successfully",
            data: history,
            pageData
        };
    } catch (error) {
        console.error("Error fetching speaker history:", error);
        throw error;
    }
}


speakerService.updateSpeakerOpportunity = async (updateReq) => {
    try {

        const { speaker_id, opportunity_id } = updateReq.body;
        const updateData = updateReq.body;
        await SpeakerOpportunity.update(updateData, { where: { speaker_id, opportunity_id } });
        return {
            status: true,
            message: "Speaker opportunity updated successfully"
        }; ``
    } catch (error) {
        console.error("Error updating speaker opportunity:", error);
        throw error;
    }
}



speakerService.getIntakeForm = async (req) => {
    try {
        const speakerId = req.userId;
        // Get form data with questions using Sequelize
        // Get form data with questions and speaker answers using Sequelize
        const formTypes = await FormType.findAll({
            attributes: ['id', 'priority', 'title', 'created_at', 'updated_at'],
            include: [{
                model: FormQuestion,
                as: 'questions',
                attributes: ['id', 'question', 'form_type_id', 'field_type', 'field_id', 'is_required', 'options_json', 'created_at', 'updated_at'],
                required: false
            }],
            order: [
                ['priority', 'ASC'],
                [{ model: FormQuestion, as: 'questions' }, 'id', 'ASC']
            ]
        });

        // Transform form data to the required structure
        const formData = formTypes.map(formType => ({
            form_type_id: formType.id,
            form_type_name: formType.title,
            questions: formType.questions.map(question => {
                console.log(`Question ${question.field_id}:`, {
                    speakerDetails: question.speakerDetails,
                    hasDetails: question.speakerDetails && question.speakerDetails.length > 0
                });

                return {
                    field_id: question.field_id,
                    question: question.question,
                    type: question.field_type,
                    is_required: question.is_required ? "1" : "0",
                    value: question.options_json,
                    selected_value: question.speakerDetails && question.speakerDetails.length > 0
                        ? question.speakerDetails[0].value
                        : null
                };
            })
        }));

        return {
            status: true,
            message: "Intake form fetched successfully",
            data: formData
        };
    } catch (error) {
        console.error("Error fetching intake form:", error);
        throw error;
    }
}




/**
 * Get Intake form progress for speaker
 */


speakerService.getIntakeFormProgress = async(getReq)=>{ 
    try{ 
        const speakerId = getReq.userId;
        
        // Get total number of form questions
        const totalIntakeFields = await FormQuestion.count();
        console.log('Total intake fields:', totalIntakeFields);
        
        // Get count of completed fields (where speaker has provided values)
        const completedFields = await SpeakerDetails.count({
            where: {
                speaker_id: speakerId,
                value: {
                    [Op.ne]: null,
                    [Op.ne]: ''
                }
            }
        });
       
        // Get critical missing fields (required fields that are not completed)
        const criticalMissingFields = await connection.query(`
            SELECT fq.field_id, fq.question, fq.field_type
            FROM form_questions fq
            LEFT JOIN speaker_details sd ON fq.field_id = sd.field_id AND sd.speaker_id = :speakerId
            WHERE fq.is_required = 1 
            AND (sd.value IS NULL OR sd.value = '')
        `, {
            replacements: { speakerId },
            type: connection.QueryTypes.SELECT
        });
        
        
        // Get missing psychometric assessments (form_type_id 3)
        const missingPsychometricFields = await connection.query(`
            SELECT fq.field_id, fq.question, fq.field_type, fq.is_required
            FROM form_questions fq
            LEFT JOIN speaker_details sd ON fq.field_id = sd.field_id AND sd.speaker_id = :speakerId
            WHERE fq.form_type_id = 3 
            AND (sd.value IS NULL OR sd.value = '')
        `, {
            replacements: { speakerId },
            type: connection.QueryTypes.SELECT
        });
        
        // Calculate completion percentage
        const completionPercentage = totalIntakeFields > 0 ? Math.round((completedFields / totalIntakeFields) * 100) : 0;
        
        return {
            status: true,
            message: "Intake form progress fetched successfully",
            data: {
                totalFields: totalIntakeFields,
                completedFields: completedFields,
                completionPercentage: completionPercentage,
                remainingFields: totalIntakeFields - completedFields,
                criticalMissingFields: criticalMissingFields,
                missingPsychometricFields: missingPsychometricFields,
            }
        };
          
    }catch(error){
        console.error(error);
        throw error;
     }
}


module.exports = speakerService;
