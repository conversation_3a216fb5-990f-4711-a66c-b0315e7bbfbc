const Stripe = require('stripe');
const settingService = require('../services/setting-service');
const { decryptValue } = require('./app-hepler');

let stripeClientInstance = null;

const getStripe = async (keyType = 'secret_key') => {
  // If requesting secret_key and we already have a client instance, return it
  if (keyType === 'secret_key' && stripeClientInstance) return stripeClientInstance;
  
  try {
    const keysToTry = ['stripe'];
    let requestedValue;

    for (const key of keysToTry) {
      try {
        const result = await settingService.getSettingByKey(key);
        
        if (result && result.status && result.data && result.data.value) {
          let cfg = result.data.value;
          
          // If cfg is a string, try to parse it as JSON first
          if (typeof cfg === 'string') {
            try {
              cfg = JSON.parse(cfg);
            } catch (parseError) {
              console.log(`Config is not JSON, treating as string:`, cfg);
              // If parsing fails and we're looking for secret_key, treat the string as the key
              if (keyType === 'secret_key') {
                requestedValue = decryptValue(cfg);
                break;
              }
            }
          }
          
          if (typeof cfg === 'object') {
            if (keyType === 'secret_key') {
              requestedValue = cfg.secret_key;
              // Decrypt the secret key if it's encrypted
              if (requestedValue) {
                requestedValue = decryptValue(requestedValue);
              }
            } else if (keyType === 'webhook_secret') {
              requestedValue = cfg.webhook_secret;
              // Decrypt the webhook secret if it's encrypted
              if (requestedValue) {
                requestedValue = decryptValue(requestedValue);
              }
            }
            if (requestedValue) break;
          }
        }
      } catch (error) {
        // ignore and try next key
        console.warn(`helpers/stripe.getStripe: failed to read setting '${key}':`, error.message || error);
      }
    }

    if (!requestedValue) {
      throw new Error(`Stripe ${keyType} not found in settings (stripe)`);
    }

    // For secret_key, return the Stripe client instance
    if (keyType === 'secret_key') {
      stripeClientInstance = Stripe(requestedValue);
      return stripeClientInstance;
    }
    
    // For webhook_secret, return the raw decrypted value
    return requestedValue;
  } catch (err) {
    console.error('helpers/stripe.getStripe error:', err);
    throw err;
  }
};

// helpers/stripeCheckoutHelpers.js
const buildMetadata = (speaker, plan_id, referral_code, extra = {}) => ({
    speaker_id: speaker.id,
    plan_id,
    referral_code: referral_code || "",
    customer_email: speaker.email,
    speaker_name: speaker.name,
    ...extra,
});

const determineMetadata = (plan, speaker, referral_code, registrationFee) => {
  console.log("Determining metadata for plan:", plan, "speaker:", speaker, "referral_code:", referral_code, "registrationFee:", registrationFee);
    const metadata = buildMetadata(speaker, plan.id, referral_code);

    // Determine mode
    const mode = plan.billing_interval === 'one-time' ? "payment" : "subscription";

    // Build line items
    const line_items = [{ price: plan.stripe_price_id, quantity: 1 }];
    if (registrationFee) line_items.push({ price: registrationFee.stripe_price_id, quantity: 1 });

    // Build metadata
    console.log("Determined metadata:", { mode, line_items, metadata });
    return { mode, line_items, metadata };
};


const buildSessionConfig = (speaker, stripeCustomerId, success_url, cancel_url, mode, line_items, metadata) => {
    const config = {
        payment_method_types: ["card"],
        billing_address_collection: "required",
        success_url,
        cancel_url,
        line_items,
        mode,
        metadata  // Add metadata directly to session
    };

    if (stripeCustomerId) config.customer = stripeCustomerId;
    else config.customer_email = speaker.email;

    // Also add to payment_intent_data or subscription_data for webhook consistency
    if (mode === "payment") config.payment_intent_data = { metadata };
    else if (mode === "subscription") config.subscription_data = { metadata };

    return config;
};

module.exports = { buildMetadata, determineMetadata, buildSessionConfig, getStripe };
