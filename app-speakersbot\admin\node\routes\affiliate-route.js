const affiliatesController = require("../controllers/affiliate-controller");
const verifyToken = require("../middlewares/verify-token");
const router = require("express").Router();

module.exports = (app) => {

    // ------------------------- affiliate -------------------------

    // get all affiliates
    router.get("/affiliates", affiliatesController.getAffiliates);

    // get affiliate by id
    router.get("/affiliate/:id",  affiliatesController.getAffiliateById);

    // get affiliate names only
    router.get("/affiliates/names",  affiliatesController.getAffiliateNames);

    // export affiliates to csv
    router.get("/affiliates/export",  affiliatesController.exportAffiliates);

    // get speakers for specific affiliate
    router.get("/affiliate/speakers/:id",  affiliatesController.getAffiliateSpeakers);

    // get affiliate landing page
    router.get("/affiliate/landing-page/:id",  affiliatesController.getLandingPageData);

    // update affiliate landing page
    router.put("/affiliate/landing-page/:id",  affiliatesController.updateLandingPageData);
    
    app.use("/admin/api/v1", router);

}
