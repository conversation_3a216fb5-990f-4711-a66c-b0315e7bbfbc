// models/SubCategory.js
const { DataTypes } = require("sequelize");
const connection = require("../connection");

const SubCategory = connection.define("SubCategory", {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    comment: 'Primary key for the subcategory',
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Name of the subcategory',
  },
  category_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Foreign key referencing the parent category',
  },
  is_active: {
    type: DataTypes.ENUM('0', '1'), // 0: inactive, 1: active
    allowNull: true,
    defaultValue: '1',
    comment: 'Whether the subcategory is active',
  },
}, {
  timestamps: false,
  tableName: 'subcategories'
});

module.exports = SubCategory;
