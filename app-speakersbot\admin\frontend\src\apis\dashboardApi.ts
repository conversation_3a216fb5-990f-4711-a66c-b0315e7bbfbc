import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import type { RootState } from "../store";

// Define the base URL for your API
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:8000";

export const dashboardApi = createApi({
  reducerPath: "dashboardApi",
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      const token = (getState() as RootState).auth?.token;

      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }

      headers.set("content-type", "application/json");
      return headers;
    },
  }),
  tagTypes: ["Dashboard"],
  keepUnusedDataFor: 0, // Disable caching - always fetch fresh data
  endpoints: (builder) => ({
    // Categories
    getDashboardCategories: builder.query({
      query: () => "/dashboard/categories",
      providesTags: ["Dashboard"],
    }),
    promoteToCategory: builder.mutation({
      query: (data) => ({
        url: "/dashboard/add/category",
        method: "POST",
        body: data,
      }),
    }),

    // Onboarding
    getDashboardOnboarding: builder.query({
      query: () => "/dashboard/onboarding",
      providesTags: ["Dashboard"],
    }),

    // Health
    getDashboardHealth: builder.query({
      query: () => "/dashboard/health",
      providesTags: ["Dashboard"],
    }),

    // Feedback
    getDashboardFeedback: builder.query({
      query: () => "/dashboard/feedback",
      providesTags: ["Dashboard"],
    }),


    getRevenue: builder.query({
      query: () => ({
        url: "/dashboard/revenue/monthly",
      }),
      providesTags: ["Dashboard"],
    }),

    getPipeline: builder.query({
      query: () => ({
        url: "/dashboard/pipeline",
      }),
      providesTags: ["Dashboard"],
    }),
    getMatching: builder.query({
      query: () => ({
        url: "/dashboard/matching",
      }),
      providesTags: ["Dashboard"],
    }),
    getAffiliate: builder.query({
      query: () => ({
        url: "/dashboard/affiliate",
      }),
      providesTags: ["Dashboard"],
    }),
  }),
});

export const {
  useGetDashboardCategoriesQuery,
  usePromoteToCategoryMutation,
  useGetDashboardOnboardingQuery,
  useGetDashboardHealthQuery,
  useGetDashboardFeedbackQuery,
  useGetRevenueQuery,
  useGetPipelineQuery,
  useGetMatchingQuery,
  useGetAffiliateQuery,
} = dashboardApi;
