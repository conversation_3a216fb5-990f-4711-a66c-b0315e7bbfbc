Admin Python Services (SpeakersBot)

Overview
- FastAPI application with comprehensive speaker opportunity discovery and management system
- Features include: web scraping with multiple search providers, background workers, scheduled discovery, ChromaDB integration, speaker matching, affiliate scraping, and digital form generation
- Architecture: FastAPI + SQLAlchemy (MySQL) + Background Workers + APScheduler + ChromaDB
- Search providers: Tavily, Brave, SerpAPI, Exa, Firecrawl with intelligent fallback and rate limiting
- Background processing: Discovery worker for URL collection, scrape worker for processing, opportunity worker for ChromaDB operations
- Centralized configuration via environment variables (app/config/config.py) and rotating file logging (app/config/logger.py)

Quick start
1) Python version: 3.10+
2) Install dependencies:
   - pip install -r requirements.txt
3) Create a .env file in app-speakersbot/admin/python with at least:
   **Required:**
   - DATABASE_URL=mysql://user:pass@host:3306/dbname
   - OPENAI_API_KEY=sk-...
   
   **Search Providers (at least one recommended):**
   - TAVILY_API_KEY=...
   - BRAVE_API_KEY=...
   - SERPAPI_API_KEY=...
   - EXA_API_KEY=...
   - FIRECRAWL_API_KEY=...
   
   **Optional Configuration:**
   - BRAVE_ENDPOINT=https://api.search.brave.com/res/v1/web/search
   - FIRECRAWL_ENDPOINT=https://api.firecrawl.dev/v1/search
   - CHROMA_PERSIST_DIR=./chroma_db
   - CHROMA_HOST=localhost
   - CHROMA_PORT=8000
   - OPENAI_MODEL=gpt-4
   - OPENAI_EMBEDDING_MODEL=text-embedding-3-small
   - OPENAI_TEMPERATURE=0.3
   
   **Worker Configuration:**
   - WORKER_ENABLED=true
   - WORKER_POLL_INTERVAL_SECONDS=9.0
   - WORKER_BATCH_SIZE=1
   - WORKER_MAX_RETRIES=2
   - WORKER_RETRY_DELAY_SECONDS=60
   
   **Scheduler Configuration:**
   - DISCOVERY_INTERVAL_MINUTES=120
   - SCHEDULER_INTERVAL_MINUTES=30
   
   **Concurrency Settings:**
   - MAX_CONCURRENT_TOPICS=1
   - MAX_CONCURRENT_URLS=1
   
   **Proxy Configuration (Optional):**
   - SCRAPEOPS_API_KEY=...
   - SCRAPEOPS_PROXY_ENABLED=false
   - SCRAPEOPS_COUNTRY=US
   - SCRAPEOPS_RENDER_JS=false
4) Run the API:
   - uvicorn app.main:app --reload --port 8001
5) Health/root endpoint:
   - GET http://localhost:8001/

API Endpoints

Core Services:
- **Scraper Service** (`/api/v1/scraper/`):
  - POST `/opportunity_scraper` - Run opportunity scraper for all topics
  - Background processing with worker-based architecture

- **Speaker Matching** (`/speaker_matching/`):
  - POST `/speaker_matching/{speaker_id}` - Process speaker with LLM matching
  - GET `/health` - Health check endpoint

- **Affiliate Scraper** (`/affiliate/`):
  - POST `/scraper?url={url}&affiliate_id={id}` - Scrape affiliate data from URL

- **Digital Form Generation** (`/digital-form/`):
  - POST `/generate-details?speaker_id={id}&opportunity_id={id}` - Generate PDF details

- **Form Prefill** (`/fill-form/`):
  - POST `/fill-form/{speaker_id}/{event_id}` - Prefill forms for speaker/event

- **Opportunity Management** (`/opportunity/`):
  - POST `/add-opportunity/{opportunity_id}` - Add single opportunity to ChromaDB
  - POST `/add-all-opportunities` - Add all opportunities to ChromaDB
  - POST `/add-new-opportunities` - Add only new opportunities
  - GET `/opportunity/{opportunity_id}` - Get opportunity details
  - GET `/search` - Search opportunities in ChromaDB
  - GET `/stats` - Get collection statistics
  - DELETE `/cleanup` - Cleanup old opportunities
  - POST `/sync-after-scrape` - Sync after scraping operations

Configuration
- See app/config/config.py for all supported environment variables and defaults.
- Call config.validate_required_keys() to check for mandatory values (DATABASE_URL, OPENAI_API_KEY).

Architecture Overview

**Background Workers:**
- **Discovery Worker**: Orchestrates URL discovery using dynamic topic distribution across search engines
- **Scrape Worker**: Processes pending URLs from the database with row-level locking
- **Opportunity Worker**: Manages ChromaDB operations for opportunity storage and retrieval
- **Prefill Worker**: Handles form prefill operations for speaker/event combinations

**Scheduler System:**
- APScheduler-based scheduling for reliable job execution
- Discovery jobs run every 2 hours (configurable)
- Scraper jobs run every 30 minutes (configurable)
- Dynamic topic distribution prevents search engine rate limiting
- Comprehensive error handling and job status tracking

**ChromaDB Integration:**
- Vector database for semantic search of opportunities
- Automatic embedding generation using OpenAI
- Collection management with cleanup and sync operations
- Search capabilities with similarity scoring

**Search Provider Architecture:**
- Multiple search engines with intelligent fallback
- Rate limiting and backoff strategies
- Query optimization and length management
- Proxy integration support via ScrapeOps

Logging
- Uses app/config/logger.py; logs under app/logs/<file>.log with rotation.
- Many modules request get_logger(__name__, file_name="scraper.log").

Code Review Summary
Strengths
- Modular scraping architecture. Search providers, query builders, page loader, and extraction utilities are cleanly separated (app/services/scraper/*).
- Async-first design for I/O heavy operations, with concurrency limits and backoff for rate-limited providers.
- Database logging for scraping runs (ScrapingLogging/ScrapingStatus) gives traceability and basic observability of topics and outcomes.
- Robust provider integrations: graceful fallbacks when API keys are missing, query length guards, and proxy integration hooks.
- Centralized config and logger reduce duplication and make environments easier to manage.

Critical issues (fix these first)
1) Undefined variable used in scraper_service.build_row
   - File: app/services/scraper_service.py
   - In build_row, additional date extraction tries to read description before it is defined. Examples:
     - desc_dates = parse_dates_from_text(description)
     - all_text = f"{title} {description} {snippet}".lower()
   - description is assigned later via extract_comprehensive_description(...).
   - Impact: NameError or reference to undefined variable at runtime, causing URL processing to fail.
   - Fix:
     - Move description = extract_comprehensive_description(...) up before any references; or
     - For date fallbacks, use page_text/snippet/title only, then extract description afterwards; or
     - Initialize description = snippet or "" near the top and update after.

2) Duplicate import name collision in app/main.py
   - File: app/main.py
   - create_speaker_table is imported from two different modules:
     - from models.speaker_opportunities import create_speaker_table
     - from models.speakers import create_speaker_table
   - The second import overwrites the first symbol. In lifespan you call create_speaker_table() only once, so the speaker_opportunities one is ignored.
   - Fix: Remove the first import (if unused) or alias imports to distinct names, e.g.,
     - from models.speakers import create_speaker_table as create_speakers_table
     - from models.speaker_opportunities import create_speaker_table as create_speaker_opportunities_table

3) pause_scraping_for_topic ignores provided reason
   - File: app/services/scraper/database.py
   - In pause_scraping_for_topic, the update sets reason=None instead of the provided reason argument.
   - Fix: Set reason=reason so the pause reason is recorded in ScrapingLogging.

4) Signal handlers in a web server context
   - File: app/services/scraper_service.py (global signal.signal calls)
   - Registering SIGINT/SIGTERM handlers at import time can be problematic under uvicorn/gunicorn or on Windows with multiple workers.
   - Recommendation: Register signals only when running as a standalone script (if __name__ == "__main__"), or convert to FastAPI startup/shutdown event handlers that update statuses and close sessions.

5) DB engine/session management duplicated
   - Many functions construct engines and sessions ad hoc (e.g., get_seen_urls_from_db, save_to_db, read_topics, status/pause handlers).
   - This is workable, but centralizing a SessionLocal factory and using dependency injection in FastAPI routes and services will reduce overhead and improve testability.

High-impact improvements
- Enforce uniqueness at DB level for event_url
  - Add a unique constraint or index on SpeakerOpportunity.event_url to prevent duplicates at insert time. Then you can rely less on preloading all URLs into memory and instead handle IntegrityError on insert.
- Reduce memory usage of dedup step
  - get_seen_urls_from_db loads all URLs; for very large tables this can grow. Combine DB-side uniqueness and incremental dedup per run (or a bloom filter) to keep memory bounded.
- Consistent HTTP clients reuse
  - Where possible, reuse a single httpx.AsyncClient per provider to avoid connection setup cost. Provide clients via DI or a provider-level singleton with proper lifecycle.
- Provider validation cost
  - tclient() validates Tavily key by firing a test search. Consider lazy validation or skip the test to reduce startup latency and external calls.
- Config validation on startup
  - At FastAPI startup, call config.validate_required_keys() and log warnings or raise early if critical keys are missing.
- Authentication/authorization for admin endpoints
  - If these endpoints run in production, protect /api/v1/scraper/* and /affiliate/* with auth (token/key) to avoid misuse.
- Observability
  - Add request metrics (duration, failures, per-provider counts) via Prometheus/OpenTelemetry. You already log per-provider counts; exporting metrics would help operations.
- Type hints and minor cleanups
  - Type soup as bs4.BeautifulSoup where applicable.
  - Remove unused imports (e.g., unused BeautifulSoup import in extract_structured_fields).
  - Prefer pathlib over os.path consistently.
- Testing/tooling
  - Add unit tests for utils (date/location extraction, is_job_related/is_event_related), and integration tests for providers (with mocked HTTP).
  - Add linting/formatting: ruff + black + mypy config.

Operational notes
- Uvicorn
  - Example: uvicorn app.main:app --host 0.0.0.0 --port 8001 --reload
- Database
  - Ensure MySQL user has privileges to create tables. If models share a common Base, ensure all models import use the same Base to create all tables.
- Proxy (optional)
  - The code integrates with ScrapeOps proxy when available. Set SCRAPEOPS_API_KEY and SCRAPEOPS_PROXY_ENABLED=true to enable.

Example Requests

**Core Scraping Operations:**
```bash
# Run opportunity scraper (background processing)
curl -X POST http://localhost:8001/api/v1/scraper/opportunity_scraper

# Health check
curl http://localhost:8001/
```

**Speaker Matching:**
```bash
# Process speaker with LLM matching
curl -X POST http://localhost:8001/speaker_matching/speaker_matching/123

# Health check for speaker matching service
curl http://localhost:8001/speaker_matching/health
```

**Affiliate Scraping:**
```bash
# Scrape affiliate data from URL
curl -X POST "http://localhost:8001/affiliate/scraper?url=https://example.com&affiliate_id=ABC123"
```

**Digital Form Generation:**
```bash
# Generate PDF details for speaker/opportunity pair
curl -X POST "http://localhost:8001/digital-form/generate-details?speaker_id=123&opportunity_id=456"
```

**Form Prefill:**
```bash
# Prefill forms for speaker/event combination
curl -X POST http://localhost:8001/fill-form/123/event456
```

**ChromaDB Opportunity Management:**
```bash
# Add single opportunity to ChromaDB
curl -X POST http://localhost:8001/opportunity/add-opportunity/123

# Add all opportunities to ChromaDB
curl -X POST http://localhost:8001/opportunity/add-all-opportunities

# Search opportunities in ChromaDB
curl -X GET "http://localhost:8001/opportunity/search?query=data%20science&limit=10"

# Get collection statistics
curl -X GET http://localhost:8001/opportunity/stats

# Cleanup old opportunities
curl -X DELETE "http://localhost:8001/opportunity/cleanup?days_old=30"

# Sync after scraping operations
curl -X POST http://localhost:8001/opportunity/sync-after-scrape
```

Troubleshooting

**Common Issues:**

- **RuntimeError: "asyncio.run() cannot be called from a running event loop"**
  - Occurs if you call run_scraper_logic() from an async context. The FastAPI route is sync, so it runs in a threadpool and is safe. If you expose an async route, run main() via asyncio.create_task or await main() directly instead of asyncio.run.

- **Missing API Keys**
  - Providers will log and skip if keys are missing. Verify your .env values.
  - Check config.validate_required_keys() output for missing required keys.

- **Search Provider Rate Limits (422/429)**
  - Query shortening and backoff are implemented. Ensure API keys are valid and keep queries concise.
  - Workers implement retry logic with exponential backoff.

- **Database Connection Issues**
  - The code auto-rewrites mysql:// to mysql+pymysql:// for SQLAlchemy. Ensure pymysql is installed (requirements includes it).
  - Ensure MySQL user has privileges to create tables and perform operations.

- **ChromaDB Issues**
  - Ensure CHROMA_PERSIST_DIR is writable and accessible.
  - Check ChromaDB service is running if using remote instance.
  - Verify OpenAI API key for embedding generation.

- **Worker Not Processing**
  - Check WORKER_ENABLED=true in environment variables.
  - Verify database connection and table structure.
  - Check logs for worker status and error messages.

- **Scheduler Not Starting**
  - Verify APScheduler dependencies are installed.
  - Check for port conflicts if running multiple instances.
  - Review scheduler logs for initialization errors.

**Performance Optimization:**
- Adjust WORKER_BATCH_SIZE and WORKER_POLL_INTERVAL_SECONDS for your workload
- Configure MAX_CONCURRENT_TOPICS and MAX_CONCURRENT_URLS based on API limits
- Use proxy configuration (ScrapeOps) for high-volume scraping
- Monitor ChromaDB collection size and cleanup old data regularly

Recent Updates & Current Status

**Architecture Improvements:**
- ✅ Implemented background worker architecture for scalable processing
- ✅ Added APScheduler for reliable job scheduling
- ✅ Integrated ChromaDB for semantic search capabilities
- ✅ Enhanced search provider architecture with intelligent fallback
- ✅ Added comprehensive configuration management

**New Features:**
- ✅ Digital form generation with PDF output
- ✅ Speaker matching with LLM integration
- ✅ Affiliate data scraping capabilities
- ✅ Form prefill automation
- ✅ Comprehensive opportunity management in ChromaDB

**Operational Enhancements:**
- ✅ Row-level locking for worker concurrency
- ✅ Comprehensive error handling and retry logic
- ✅ Dynamic topic distribution for search optimization
- ✅ Proxy integration support
- ✅ Detailed logging and monitoring

**Dependencies:**
- FastAPI, SQLAlchemy, ChromaDB, APScheduler
- Multiple search providers (Tavily, Brave, SerpAPI, Exa, Firecrawl)
- OpenAI integration for embeddings and LLM processing
- ReportLab for PDF generation
