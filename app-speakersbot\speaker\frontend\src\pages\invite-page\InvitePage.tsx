import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { Copy, Gift, Share2, TrendingUp, Trophy, Users } from 'lucide-react';

export const InvitePage = () => {
  const { toast } = useToast();

  const referralStats = {
    totalInvites: 12,
    acceptedInvites: 8,
    pendingInvites: 4,
    totalRewards: 400,
    conversionRate: 67
  };

  const referralHistory = [
    { id: 1, name: '<PERSON>', email: '<EMAIL>', status: 'accepted', date: '2024-01-15', reward: 50 },
    { id: 2, name: '<PERSON>', email: '<EMAIL>', status: 'accepted', date: '2024-01-12', reward: 50 },
    { id: 3, name: '<PERSON>', email: '<EMAIL>', status: 'pending', date: '2024-01-10', reward: 0 },
    { id: 4, name: '<PERSON>', email: '<EMAIL>', status: 'accepted', date: '2024-01-08', reward: 50 }
  ];

  const rewards = [
    { milestone: 1, reward: '50 XP Credit', description: 'First successful referral', achieved: true },
    { milestone: 5, reward: '250 XP Credit', description: '5 successful referrals', achieved: true },
    { milestone: 10, reward: '500 XP Credit + Premium Badge', description: '10 successful referrals', achieved: false },
    { milestone: 25, reward: '1,000 XP Credit + VIP Status', description: '25 successful referrals', achieved: false }
  ];

  const referralLink = 'https://speakerbot.com/join?ref=alex-johnson-2024';

  const copyReferralLink = () => {
    navigator.clipboard.writeText(referralLink);
    toast({
      title: "Link Copied!",
      description: "Your referral link has been copied to clipboard."
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Invite Friends & Earn Rewards</h2>
          <p className="text-foreground-muted mt-1">Share SpeakerBot with fellow speakers and earn credits for each successful referral</p>
        </div>
        <Badge className="bg-gradient-primary text-primary-foreground px-4 py-2 text-lg">
          Referral Ambassador
        </Badge>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Referral Section */}
        <div className="lg:col-span-2 space-y-6">
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card className="bg-surface border-border-subtle">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-primary">{referralStats.totalInvites}</div>
                <div className="text-sm text-foreground-muted">Total Invites</div>
              </CardContent>
            </Card>
            <Card className="bg-surface border-border-subtle">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-success">{referralStats.acceptedInvites}</div>
                <div className="text-sm text-foreground-muted">Successful</div>
              </CardContent>
            </Card>
            <Card className="bg-surface border-border-subtle">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-warning">{referralStats.totalRewards} XP</div>
                <div className="text-sm text-foreground-muted">Total Earned</div>
              </CardContent>
            </Card>
            <Card className="bg-surface border-border-subtle">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-primary">{referralStats.conversionRate}%</div>
                <div className="text-sm text-foreground-muted">Success Rate</div>
              </CardContent>
            </Card>
          </div>

          {/* Share Your Referral Link */}
          <Card className="bg-gradient-surface border-border-subtle">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Share2 className="h-5 w-5 text-primary" />
                Share Your Referral Link
              </CardTitle>
              <CardDescription>
                Earn 50 XP for each friend who joins and completes their first application
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex gap-2">
                <Input 
                  value={referralLink} 
                  readOnly 
                  className="bg-surface-elevated border-border font-mono text-sm"
                />
                <Button onClick={copyReferralLink} className="flex items-center gap-2">
                  <Copy className="h-4 w-4" />
                  Copy
                </Button>
              </div>
              
            </CardContent>
          </Card>

          {/* Referral History */}
          <Card className="bg-surface border-border-subtle">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Users className="h-5 w-5 text-primary" />
                Referral History
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {referralHistory.map((referral) => (
                  <div key={referral.id} className="flex items-center justify-between p-3 bg-surface-elevated rounded-lg border border-border-subtle">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center">
                        <span className="text-primary-foreground font-semibold text-sm">
                          {referral.name.charAt(0)}
                        </span>
                      </div>
                      <div>
                        <div className="text-sm font-semibold text-foreground">{referral.name}</div>
                        <div className="text-xs text-foreground-muted">{referral.email}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <Badge 
                        variant={referral.status === 'accepted' ? 'default' : 'outline'}
                        className="mb-1"
                      >
                        {referral.status === 'accepted' ? 'Joined' : 'Pending'}
                      </Badge>
                      <div className="text-xs text-foreground-muted">{new Date(referral.date).toLocaleDateString()}</div>
                      {referral.reward > 0 && (
                        <div className="text-xs font-semibold text-success">{referral.reward} XP earned</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Current Progress */}
          <Card className="bg-gradient-glow border-primary/20">
            <CardHeader className="pb-4 text-center">
              <div className="mx-auto w-16 h-16 bg-gradient-primary rounded-full flex items-center justify-center mb-2">
                <Gift className="h-8 w-8 text-primary-foreground" />
              </div>
              <CardTitle className="text-lg">Next Reward</CardTitle>
              <CardDescription>2 more referrals needed</CardDescription>
            </CardHeader>
            <CardContent className="space-y-3 pt-0">
              <div className="text-center">
                <div className="text-2xl font-bold text-primary">500 XP Credit</div>
                <div className="text-sm text-foreground-muted">+ Premium Badge</div>
              </div>
              <div className="w-full bg-surface-elevated rounded-full h-2">
                <div className="bg-gradient-primary h-2 rounded-full" style={{width: '80%'}}></div>
              </div>
              <div className="text-center text-sm text-foreground-muted">
                8 of 10 referrals complete
              </div>
            </CardContent>
          </Card>

          {/* Reward Milestones */}
          <Card className="bg-surface border-border-subtle">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Trophy className="h-5 w-5 text-primary" />
                Reward Milestones
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {rewards.map((reward) => (
                <div 
                  key={reward.milestone} 
                  className={`p-3 rounded-lg border ${
                    reward.achieved 
                      ? 'bg-primary/10 border-primary/20' 
                      : 'bg-surface-elevated border-border-subtle'
                  }`}
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm font-semibold text-foreground">
                      {reward.milestone} Referrals
                    </span>
                    {reward.achieved && (
                      <Badge className="text-xs bg-success text-white">Earned</Badge>
                    )}
                  </div>
                  <div className="text-sm text-primary font-semibold">{reward.reward}</div>
                  <div className="text-xs text-foreground-muted">{reward.description}</div>
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Referral Tips */}
          <Card className="bg-surface border-border-subtle">
            <CardHeader className="pb-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-primary" />
                Referral Tips
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div className="p-2 bg-surface-elevated rounded">
                <strong className="text-foreground">Personal touch:</strong>
                <p className="text-foreground-muted">Add a personal message when sharing your link</p>
              </div>
              <div className="p-2 bg-surface-elevated rounded">
                <strong className="text-foreground">Target speakers:</strong>
                <p className="text-foreground-muted">Focus on friends who are already speakers or want to become ones</p>
              </div>
              <div className="p-2 bg-surface-elevated rounded">
                <strong className="text-foreground">Share benefits:</strong>
                <p className="text-foreground-muted">Explain how SpeakerBot helps find speaking opportunities</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}