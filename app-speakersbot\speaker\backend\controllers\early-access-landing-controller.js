const e = require("express");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const { Category, Subcategory } = require("../models");
const ApiResponse = require("../helpers/api-response");
const earlyAccessLandingService = require('../services/early-access-landing-services')

/**
 * Add speaker from landing page - simple registration without password
 */
exports.landingPageSpeaker = async (req, res, next) => {
    try {
        const { email, first_name, last_name, phone_number, city, state, country, primary_category ,sub_category} = req.body;

        if (!email || !first_name || !last_name || !phone_number || !city || !state || !country || !primary_category || !sub_category ) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "All fields are required");
        }

        const result = await earlyAccessLandingService.createLandingPageSpeaker(req.body);
        const { status, message } = result;

        if (status) {
            res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({
                message: "Speaker information submitted successfully"
            }));
        } else {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, message);
        }

    } catch (error) {
        next(error);
    }
}

exports.getPrimaryCategories = async (req, res, next) => {
    try {
        const categories = await Category.findAll({
            attributes: ['id', 'name']
        });
        res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({
            data: categories
        }));
    } catch (error) {
        next(error);
    }
}

exports.getSubCategories = async (req, res, next) => {
    try {
        const { primaryCategoryId } = req.params;

        if (!primaryCategoryId) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Primary Category ID is required");
        }

        const subCategories = await Subcategory.findAll({
            attributes: ['id', 'name'],
            where: { category_id: primaryCategoryId }
        });
        res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({
            data: subCategories
        }));
    } catch (error) {
        next(error);
    }
}
