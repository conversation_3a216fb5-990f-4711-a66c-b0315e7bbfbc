from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean
from app.models.base import Base
import datetime as dt


class Settings(Base):
    __tablename__ = "settings"
    id = Column(Integer, primary_key=True, autoincrement=True, comment="Primary key for the setting")
    key = Column(String(100), nullable=False, unique=False, comment="Unique key for the setting")
    value = Column(Text, nullable=True, comment="Value associated with the setting key")
    is_active = Column(Boolean, default=True, comment="Indicates if the setting is active")
    created_at = Column(DateTime, default=dt.datetime.utcnow, nullable=False, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=dt.datetime.utcnow, nullable=False, comment="Record last update timestamp")
    deleted_at = Column(DateTime, nullable=True, comment="Record deletion timestamp(soft delete)")


def create_settings_table():
    """Utility to create the settings table in the DB (for ad-hoc use)."""
    from app.config.config import config
    from sqlalchemy import create_engine
    engine = create_engine(config.get_database_url())
    Base.metadata.create_all(engine)


if __name__ == "__main__":
    create_settings_table()
    print("Table 'settings' ensured in DB")
