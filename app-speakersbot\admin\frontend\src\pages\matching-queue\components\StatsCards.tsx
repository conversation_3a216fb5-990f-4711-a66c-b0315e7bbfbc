import React from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { Card } from "@/components/ui/card";

type StatusCounts = {
  total: number;
  pending: number;
  interested: number;
  accepted: number;
  rejected: number;
};

type FollowThroughStats = {
  appliedCount: number;
  bookedCount: number;
  bookingRate: number;
};

interface StatsCardsProps {
  isLoading: boolean;
  statusCounts: StatusCounts;
  followThroughStats: FollowThroughStats;
}

const StatsCards: React.FC<StatsCardsProps> = ({ isLoading, statusCounts, followThroughStats }) => {
  return (
    <div
      className="grid grid-cols-1 min-[1600px]:grid-cols-8 xl:grid-cols-5 md:grid-cols-4 gap-4"
      style={{ marginBottom: 24 }}
    >
      {isLoading ? (
        Array.from({ length: 8 }).map((_, i) => (
          <Skeleton className="h-[110px] w-full rounded-lg" key={i} />
        ))
      ) : (
        <>
          <div className="w-full">
            <Card className="border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 bg-tertiary p-[25px] h-full">
              <div className="text-sm font-medium text-muted-foreground mb-2">Total Matches</div>
              <div className="text-2xl font-bold" style={{ color: "hsl(var(--dashboard-dark-blue))" }}>
                {statusCounts.total}
              </div>
            </Card>
          </div>
          <div className="w-full">
            <Card className="border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 bg-tertiary p-[25px] h-full">
              <div className="text-sm font-medium text-muted-foreground mb-2">Pending</div>
              <div className="text-2xl font-bold" style={{ color: "hsl(var(--muted-foreground))" }}>
                {statusCounts.pending}
              </div>
            </Card>
          </div>
          <div className="w-full">
            <Card className="border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 bg-tertiary p-[25px] h-full">
              <div className="text-sm font-medium text-muted-foreground mb-2">Interested</div>
              <div className="text-2xl font-bold" style={{ color: "#1890ff" }}>
                {statusCounts.interested}
              </div>
            </Card>
          </div>
          <div className="w-full">
            <Card className="border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 bg-tertiary p-[25px] h-full">
              <div className="text-sm font-medium text-muted-foreground mb-2">Accepted</div>
              <div className="text-2xl font-bold" style={{ color: "#52c41a" }}>
                {statusCounts.accepted}
              </div>
            </Card>
          </div>
          <div className="w-full">
            <Card className="border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 bg-tertiary p-[25px] h-full">
              <div className="text-sm font-medium text-muted-foreground mb-2">Rejected</div>
              <div className="text-2xl font-bold" style={{ color: "#ff4d4f" }}>
                {statusCounts.rejected}
              </div>
            </Card>
          </div>

          <div className="w-full">
            <Card className="border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 bg-tertiary p-[25px] h-full">
              <div className="text-sm font-medium text-muted-foreground mb-2">Applied</div>
              <div className="text-2xl font-bold" style={{ color: "#722ed1" }}>
                {followThroughStats.appliedCount}
              </div>
            </Card>
          </div>
          <div className="w-full">
            <Card className="border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 bg-tertiary p-[25px] h-full">
              <div className="text-sm font-medium text-muted-foreground mb-2">Booked</div>
              <div className="text-2xl font-bold" style={{ color: "#13c2c2" }}>
                {followThroughStats.bookedCount}
              </div>
            </Card>
          </div>
          <div className="w-full">
            <Card className="border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 bg-tertiary p-[25px] h-full">
              <div className="text-sm font-medium text-muted-foreground mb-2">Conversion</div>
              <div className="text-2xl font-bold" style={{ color: "#eb2f96" }}>
                {followThroughStats.bookingRate}
              </div>
            </Card>
          </div>
        </>
      )}
    </div>
  );
};

export default StatsCards;


