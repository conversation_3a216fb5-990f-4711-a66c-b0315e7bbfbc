const { Op } = require("sequelize");
const { parsePagination, getPagingData, buildSearchWhereClause, parseJSONSafely, buildFilterWhereClause } = require("../helpers/app-hepler");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const { SpeakerOpportunity, Speakers, Opportunities } = require("../models");
const { exportAsCSV } = require("../helpers/csv-helper");

const matchingQueueService = {};

// ------------------------- matching-queue-service -------------------------

/**
 * Get matching queue with filters and pagination.
 * Retrieves speaker-opportunity matches with advanced filtering options.
 * 
 * @param {Object} getReq - Request object with query parameters
 * @param {Object} getReq.query - Query parameters
 * @param {string} [getReq.query.search] - Search term
 * @param {string} [getReq.query.filter] - JSON filter object
 * @param {number} [getReq.query.page] - Page number
 * @param {number} [getReq.query.limit] - Items per page
 * @returns {Promise<Object>} Paginated matching queue data
 * @throws {Error} When database operation fails
 */
matchingQueueService.getMatchingQueue = async (getReq) => {
    try {
        const { page, limit, offset } = parsePagination(getReq.query);

        const search = getReq.query.search;
        const filter = parseJSONSafely(getReq.query.filter, "Invalid JSON filter");
        const sort = parseJSONSafely(getReq.query.sort, "Invalid JSON sort");
        const dateRange = parseJSONSafely(getReq.query.dateRange, "Invalid JSON date range");
         

        let where = {};
        let speakerWhere = {};
        let opportunityWhere = {};
        let includeArray = [
            {
                model: Speakers,
                as: 'speaker'
            },
            {
                model: Opportunities,
                as: 'opportunity'
            }
        ];

        // Sorting builder
        const buildOrderArray = (sortObj) => {
            // Default sort
            const orderArray = [["overall_score", "DESC"]];
            if (!sortObj || typeof sortObj !== 'object') return orderArray;

            const pushOrder = (field, dir) => {
                const direction = String(dir || '').toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
                switch (field) {
                    case 'speakerName':
                    case 'speaker_name':
                    case 'speaker':
                        orderArray.push([{ model: Speakers, as: 'speaker' }, 'name', direction]);
                        break;
                    case 'opportunityTitle':
                    case 'opportunity_title':
                    case 'title':
                        orderArray.push([{ model: Opportunities, as: 'opportunity' }, 'title', direction]);
                        break;
                    case 'overallScore':
                    case 'overall_score':
                    case 'score':
                        orderArray.push(['overall_score', direction]);
                        break;
                    case 'startDate':
                    case 'start_date':
                        orderArray.push([{ model: Opportunities, as: 'opportunity' }, 'start_date', direction]);
                        break;
                    case 'endDate':
                    case 'end_date':
                        orderArray.push([{ model: Opportunities, as: 'opportunity' }, 'end_date', direction]);
                        break;
                    default:
                        break;
                }
            };

            // Allow single key or multiple keys
            Object.entries(sortObj).forEach(([key, value]) => pushOrder(key, value));
            return orderArray;
        };

        const order = buildOrderArray(sort);

        // AND-based filters
        if (filter && typeof filter === 'object') {
            // speaker filters (name/email) - exact match
            if (filter.speakerName) {
                speakerWhere.name = filter.speakerName;
            }
            if (filter.speakerEmail) {
                speakerWhere.email = filter.speakerEmail;
            }

            // opportunity filters (event_type exact, start_date/end_date range)
            if (filter.event_type) {
                // support single value or array of values for event_type, applied on Opportunities include
                opportunityWhere.event_type = Array.isArray(filter.event_type)
                    ? { [Op.in]: filter.event_type }
                    : filter.event_type;
            }
            if (filter.start_date || filter.end_date) {
                // If both bounds are provided, constrain start_date and end_date window
                if (filter.start_date && filter.end_date) {
                    opportunityWhere.start_date = { ...(opportunityWhere.start_date || {}), [Op.gte]: filter.start_date };
                    opportunityWhere.end_date = { ...(opportunityWhere.end_date || {}), [Op.lte]: filter.end_date };
                } else if (filter.start_date) {
                    opportunityWhere.start_date = { ...(opportunityWhere.start_date || {}), [Op.gte]: filter.start_date };
                } else if (filter.end_date) {
                    opportunityWhere.end_date = { ...(opportunityWhere.end_date || {}), [Op.lte]: filter.end_date };
                }
            }

            // SpeakerOpportunity filters (overall_score =, >=, <=, between)
            if (filter.exactScore !== undefined && filter.exactScore !== null && filter.exactScore !== '') {
                where.overall_score = Number(filter.exactScore);
            }
            if (filter.minScore !== undefined) {
                where.overall_score = { ...(where.overall_score || {}), [Op.gte]: Number(filter.minScore) };
            }
            if (filter.maxScore !== undefined) {
                where.overall_score = { ...(where.overall_score || {}), [Op.lte]: Number(filter.maxScore) };
            }
            if (filter.scoreBetween && typeof filter.scoreBetween === 'object') {
                const { min, max } = filter.scoreBetween;
                if (min !== undefined && max !== undefined) {
                    where.overall_score = { [Op.between]: [Number(min), Number(max)] };
                }
            }

            // Status filter for SpeakerOpportunity
            if (filter.status) {
                where.status = filter.status;
            }
        }

        // Apply top-level dateRange (same logic as filter), if provided
        if (dateRange) {
            const normalizeDateRangeTop = (dr) => {
                if (!dr) return { start: undefined, end: undefined };
                if (Array.isArray(dr)) {
                    const [start, end] = dr; return { start, end };
                }
                if (typeof dr === 'object') {
                    const start = dr.start || dr.from || dr.startDate || dr.start_date;
                    const end = dr.end || dr.to || dr.endDate || dr.end_date;
                    return { start, end };
                }
                return { start: undefined, end: undefined };
            };
            const { start, end } = normalizeDateRangeTop(dateRange);
            if (start) {
                opportunityWhere.start_date = { ...(opportunityWhere.start_date || {}), [Op.gte]: start };
            }
            if (end) {
                opportunityWhere.end_date = { ...(opportunityWhere.end_date || {}), [Op.lte]: end };
            }
        }

        // Apply conditional includes based on filters
        if (Object.keys(speakerWhere).length > 0) {
            includeArray[0].where = speakerWhere;
            includeArray[0].required = true;
        }
        if (Object.keys(opportunityWhere).length > 0) {
            includeArray[1].where = opportunityWhere;
            includeArray[1].required = true;
        }

        // Search functionality
        if (search) {
            // Search across speaker and opportunity fields using Sequelize column references
            const searchConditions = [];

            // Speaker fields
            const speakerFields = ['name', 'email', 'company', 'city', 'state', 'phone_number', 'linkedin', 'bio', 'source'];
            speakerFields.forEach(field => {
                searchConditions.push({
                    [`$speaker.${field}$`]: { [Op.like]: `%${search}%` }
                });
            });

            // Opportunity fields
            const opportunityFields = ['title', 'organization', 'event_type', 'city', 'country', 'venue', 'industry', 'source_url', 'event_url'];
            opportunityFields.forEach(field => {
                searchConditions.push({
                    [`$opportunity.${field}$`]: { [Op.like]: `%${search}%` }
                });
            });

            // Also search by numeric IDs
            const numericSearch = Number(search);
            if (!isNaN(numericSearch)) {
                searchConditions.push(
                    { speaker_id: numericSearch },
                    { opportunity_id: numericSearch }
                );
            }

            where[Op.or] = searchConditions;
        }
 const [
            totalMatches,
            pendingMatches,
            acceptedMatches,
            rejectedMatches,
            interestedMatches,
            appliedMatches,
            bookedMatches,
            conversionMatches
        ] = await Promise.all([
            SpeakerOpportunity.count(),
            SpeakerOpportunity.count({ where: { status: 'pending' } }),
            SpeakerOpportunity.count({ where: { status: 'accepted' } }),
            SpeakerOpportunity.count({ where: { status: 'rejected' } }),
            SpeakerOpportunity.count({ where: { status: 'interested' } }),
            SpeakerOpportunity.count({ where: { status: 'applied' } }),
            SpeakerOpportunity.count({ where: { status: 'booked' } }),
            SpeakerOpportunity.count({ where: { status: 'conversion' } })
        ]);


        const summary ={
            totalMatches,
            pendingMatches,
            acceptedMatches,
            rejectedMatches,
            interestedMatches,
            appliedMatches,
            bookedMatches,
            conversionMatches
        }

        const matchingQueue = await SpeakerOpportunity.findAll({
            where,
            include: includeArray,
            limit,
            offset,
            order
        });

        const totalMatchingQueue = await SpeakerOpportunity.count({
            where,
            include: includeArray
        });

        const pageData = getPagingData(totalMatchingQueue, limit, page);

        return {
            status: true,
            message: "Matching queue retrieved successfully",
            data: { matchingQueue, summary },
            pageData
        };

    } catch (error) {
        console.error("Error in getMatchingQueue:", error);
        throw error;
    }
}


/**
 * Get a specific matching queue item by ID.
 * Includes speaker and opportunity details.
 * 
 * @param {Object} getReq - Request object
 * @param {number} getReq.params.id - Matching queue item ID
 * @returns {Promise<Object>} Matching queue item with related data
 * @throws {Error} When item not found or database operation fails
 */
matchingQueueService.getMatchingQueueById = async (getReq) => {
    try {
        const { id } = getReq.params;
        const matchingQueue = await SpeakerOpportunity.findByPk(id, {
            include: [
                {
                    model: Speakers,
                    as: 'speaker'
                },
                {
                    model: Opportunities,
                    as: 'opportunity'
                }
            ]
        });

        if (!matchingQueue) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Matching queue not found");
        }
        return {
            status: true,
            message: "Matching queue retrieved successfully",
            data: { matchingQueue }
        };
    } catch (error) {
        console.error("Error in getMatchingQueueById:", error);
        throw error;
    }
}

/**
 * Export matching queue to csv
 */
matchingQueueService.exportMatchingQueue = async (req,res) => { 
    try {  
        // Mirror filters/search from getMatchingQueue
        const search = req.query.search;
        const filter = parseJSONSafely(req.query.filter, "Invalid JSON filter");
        const sort = parseJSONSafely(req.query.sort, "Invalid JSON sort");
        const dateRange = parseJSONSafely(req.query.dateRange, "Invalid JSON date range");

        let where = {};
        let speakerWhere = {};
        let opportunityWhere = {};
        const includeArray = [
            { model: Speakers, as: 'speaker' },
            { model: Opportunities, as: 'opportunity' }
        ];

        const buildOrderArray = (sortObj) => {
            const orderArray = [["overall_score", "DESC"]];
            if (!sortObj || typeof sortObj !== 'object') return orderArray;
            const pushOrder = (field, dir) => {
                const direction = String(dir || '').toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
                switch (field) {
                    case 'speakerName':
                    case 'speaker_name':
                    case 'speaker':
                        orderArray.push([{ model: Speakers, as: 'speaker' }, 'name', direction]);
                        break;
                    case 'opportunityTitle':
                    case 'opportunity_title':
                    case 'title':
                        orderArray.push([{ model: Opportunities, as: 'opportunity' }, 'title', direction]);
                        break;
                    case 'overallScore':
                    case 'overall_score':
                    case 'score':
                        orderArray.push(['overall_score', direction]);
                        break;
                    case 'startDate':
                    case 'start_date':
                        orderArray.push([{ model: Opportunities, as: 'opportunity' }, 'start_date', direction]);
                        break;
                    case 'endDate':
                    case 'end_date':
                        orderArray.push([{ model: Opportunities, as: 'opportunity' }, 'end_date', direction]);
                        break;
                    default:
                        break;
                }
            };
            Object.entries(sortObj).forEach(([key, value]) => pushOrder(key, value));
            return orderArray;
        };
        const order = buildOrderArray(sort);

        if (filter && typeof filter === 'object') {
            if (filter.speakerName) {
                speakerWhere.name = filter.speakerName;
            }
            if (filter.speakerEmail) {
                speakerWhere.email = filter.speakerEmail;
            }

            if (filter.event_type) {
                opportunityWhere.event_type = Array.isArray(filter.event_type)
                    ? { [Op.in]: filter.event_type }
                    : filter.event_type;
            }
            if (filter.start_date || filter.end_date) {
                if (filter.start_date && filter.end_date) {
                    opportunityWhere.start_date = { ...(opportunityWhere.start_date || {}), [Op.gte]: filter.start_date };
                    opportunityWhere.end_date = { ...(opportunityWhere.end_date || {}), [Op.lte]: filter.end_date };
                } else if (filter.start_date) {
                    opportunityWhere.start_date = { ...(opportunityWhere.start_date || {}), [Op.gte]: filter.start_date };
                } else if (filter.end_date) {
                    opportunityWhere.end_date = { ...(opportunityWhere.end_date || {}), [Op.lte]: filter.end_date };
                }
            }

            if (filter.exactScore !== undefined && filter.exactScore !== null && filter.exactScore !== '') {
                where.overall_score = Number(filter.exactScore);
            }
            if (filter.minScore !== undefined) {
                where.overall_score = { ...(where.overall_score || {}), [Op.gte]: Number(filter.minScore) };
            }
            if (filter.maxScore !== undefined) {
                where.overall_score = { ...(where.overall_score || {}), [Op.lte]: Number(filter.maxScore) };
            }
            if (filter.scoreBetween && typeof filter.scoreBetween === 'object') {
                const { min, max } = filter.scoreBetween;
                if (min !== undefined && max !== undefined) {
                    where.overall_score = { [Op.between]: [Number(min), Number(max)] };
                }
            }
            if (filter.status) {
                where.status = filter.status;
            }
        }

        // Apply top-level dateRange for export as well
        if (dateRange) {
            const normalizeDateRangeTop = (dr) => {
                if (!dr) return { start: undefined, end: undefined };
                if (Array.isArray(dr)) { const [start, end] = dr; return { start, end }; }
                if (typeof dr === 'object') {
                    const start = dr.start || dr.from || dr.startDate || dr.start_date;
                    const end = dr.end || dr.to || dr.endDate || dr.end_date;
                    return { start, end };
                }
                return { start: undefined, end: undefined };
            };
            const { start, end } = normalizeDateRangeTop(dateRange);
            if (start) {
                opportunityWhere.start_date = { ...(opportunityWhere.start_date || {}), [Op.gte]: start };
            }
            if (end) {
                opportunityWhere.end_date = { ...(opportunityWhere.end_date || {}), [Op.lte]: end };
            }
        }

        if (Object.keys(speakerWhere).length > 0) {
            includeArray[0].where = speakerWhere;
            includeArray[0].required = true;
        }
        if (Object.keys(opportunityWhere).length > 0) {
            includeArray[1].where = opportunityWhere;
            includeArray[1].required = true;
        }

        if (search) {
            const searchConditions = [];
            const speakerFields = ['name', 'email', 'company', 'city', 'state', 'phone_number', 'linkedin', 'bio', 'source'];
            speakerFields.forEach(field => {
                searchConditions.push({ [`$speaker.${field}$`]: { [Op.like]: `%${search}%` } });
            });
            const opportunityFields = ['title', 'organization', 'event_type', 'city', 'country', 'venue', 'industry', 'source_url', 'event_url'];
            opportunityFields.forEach(field => {
                searchConditions.push({ [`$opportunity.${field}$`]: { [Op.like]: `%${search}%` } });
            });
            const numericSearch = Number(search);
            if (!isNaN(numericSearch)) {
                searchConditions.push({ speaker_id: numericSearch }, { opportunity_id: numericSearch });
            }
            where[Op.or] = searchConditions;
        }

        const matchingQueue = await SpeakerOpportunity.findAll({
            where,
            include: includeArray,
            raw: true,
            nest: true,
            order
        });

        if (!matchingQueue || matchingQueue.length === 0) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Matching queue not found");
        }

        // return exportAsCSV(res, matchingQueue, "matching-queue.csv");
        return matchingQueue;

    } catch (error) { 
        console.error("Error in exportMatchingQueue:", error);
        throw error;
    }
}

module.exports = matchingQueueService;