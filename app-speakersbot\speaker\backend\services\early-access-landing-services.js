
const CustomError = require('../helpers/custome-error');
const earlyAccessLandingService ={};
const  {Speakers, Subcategory} = require('../models');
const emailService = require('./email-service');


/**
 * Create speaker from landing page with basic info
 */
earlyAccessLandingService.createLandingPageSpeaker = async (speakerData) => {
    try {
        const { email, first_name, last_name, phone_number, city, state, country,primary_category ,sub_category } = speakerData;
        
        const name = [first_name, last_name].filter(Boolean).join(' ');

        // Check if email already exists
        const existingSpeaker = await Speakers.findOne({ where: { email } });
        
        if (existingSpeaker) {
            return {
                status: false,
                message: "Email already exists"
            };
        }

        // Create speaker with basic info
        const newSpeaker = await Speakers.create({
            name: name || null,
            email: email.trim().toLowerCase(),
            phone_number: phone_number || null,
            city: city || null,
            state: state || null,
            country: country || null,
            primary_category,
            subcategory:sub_category
        
        });

    await emailService.sendEarlyAccessEmail(email, name).catch((error) => { console.error('Error sending email:', error); });

        return {
            status: true,
            message: "Speaker created successfully"
        };

    } catch (error) {
        console.error('Error creating landing page speaker:', error);
        throw error;
    }
}

module.exports = earlyAccessLandingService;