const scrapingController = require("../controllers/scraping-controller");
const verifyToken = require("../middlewares/verify-token");
const router = require("express").Router();

module.exports = (app) => {

    // ------------------------- scraping logs -------------------------

    // Get all scraping logs with filters, sorting, and pagination
    // Query parameters:
    // - search: search in topics, status, errors
    // - status: filter by status (success/error/running/paused)
    // - topic: filter by specific topic
    // - startDate: filter by start date (YYYY-MM-DD)
    // - endDate: filter by end date (YYYY-MM-DD)
    // - error: search in error messages
    // - page: page number (default: 1)
    // - limit: items per page (default: 10, max: 100)
    // - sortBy: sort field (started_at, items_collected)
    // - sortOrder: sort order (ASC, DESC)
    router.get("/logs", scrapingController.getScrapingLogs);

    // Get available topics for filtering (from logs)
    router.get("/logs/topics", scrapingController.getAvailableTopics);

    // Get scraping log by ID
    router.get("/logs/:id", scrapingController.getScrapingLogById);

    // Get scraping statistics
    router.get("/stats", scrapingController.getScrapingStats);

    // Export scraping logs as CSV
    // Query parameters: same as getScrapingLogs but without pagination
    // - search: search in topics, status, errors
    // - status: filter by status (success/error/running/paused)
    // - topic: filter by specific topic
    // - startDate: filter by start date (YYYY-MM-DD)
    // - endDate: filter by end date (YYYY-MM-DD)
    // - error: search in error messages
    // - sortBy: sort field (started_at, items_collected)
    // - sortOrder: sort order (ASC, DESC)
    router.get("/logs/csv/export", scrapingController.exportScrapingLogsCSV);

    // Control scraping (start/stop)
    // Body: { isRunning: boolean }
    router.post("/control", scrapingController.controlScraping);

    // Get current scraping status
    router.get("/status", scrapingController.getScrapingStatus);

    // ------------------------- scraping subcategories -------------------------

    // Get all subcategories with filters, sorting, and pagination
    // Query parameters:
    // - search: search in category and subcategory names
    // - is_active: filter by active status ('1' for active, '0' for inactive)
    // - page: page number (default: 1)
    // - limit: items per page (default: 10, max: 100)
    // - sortBy: sort field (name, category_name, is_active)
    // - sortOrder: sort order (ASC, DESC)
    router.get("/subcategories", scrapingController.getSubcategories);

    // Get all categories for subcategory creation
    router.get("/categories", scrapingController.getAllCategories);

    // Toggle subcategory active status
    // Body: { subcategory_id: number }
    router.put("/subcategories/toggle-active", scrapingController.toggleSubcategoryActive);

    // Create a new subcategory
    // Body: { category_id: number, name: string }
    router.post("/subcategories", scrapingController.createSubcategory);

    app.use("/admin/api/v1/scraping", router);
};
