const affiliateInquiriesController = require("../controllers/affiliate-inquiries-controller");
const verifyToken = require("../middlewares/verify-token");
const router = require("express").Router();

module.exports = (app) => {

    // ------------------------- affiliate-inquiries -------------------------
    // update affiliate inquiry by id
    router.put("/affiliate-inquiries/:id",  affiliateInquiriesController.updateAffiliateInquiry);

    // create new affiliate inquiry
    router.post("/affiliate-inquiries",  affiliateInquiriesController.createAffiliateInquiry);


    // get affiliate inquiry by id
    router.get("/affiliate-inquiries/:id",  affiliateInquiriesController.getAffiliateInquiries);
    
    router.get("/affiliate-inquiries-request/:id",  affiliateInquiriesController.getAffiliateInquiryRequest);

    router.get("/affiliate-inquiries/history/:id",  affiliateInquiriesController.getAffiliateInquiriesHistory);
    
    // router.get("/affiliate-inquiries",  affiliateInquiriesController.getAffiliateInquiries);

    app.use("/admin/api/v1", router);
}