import React from 'react';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { Card, CardContent } from '@/components/ui/card';

interface FilledFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  fileName: string;
}

export function FilledFormModal({ isOpen, onClose, fileName }: FilledFormModalProps) {
  // Mock form data - in real app this would be fetched based on fileName
  const formData = {
    'Speaker_Bio.pdf': {
      title: 'Speaker Biography Form',
      fields: [
        { label: 'Full Name', value: '<PERSON>' },
        { label: 'Professional Title', value: 'Senior Software Engineer & Tech Speaker' },
        { label: 'Company', value: 'Tech Innovations Inc.' },
        { label: 'Bio (100 words)', value: '<PERSON> is a passionate software engineer with over 8 years of experience in full-stack development and cloud architecture. He specializes in React, Node.js, and AWS services. <PERSON> has spoken at over 25 conferences worldwide and is known for his engaging presentations on modern web development practices and DevOps culture.' },
        { label: 'Speaking Topics', value: 'React, JavaScript, Cloud Computing, DevOps' },
        { label: 'Years of Experience', value: '8 years' },
        { label: 'Previous Speaking Engagements', value: 'ReactConf 2023, DevOps Summit 2022, JSConf EU 2023' }
      ]
    },
    'Technical_Resume.pdf': {
      title: 'Technical Speaker Resume',
      fields: [
        { label: 'Name', value: 'John Doe' },
        { label: 'Email', value: '<EMAIL>' },
        { label: 'Phone', value: '+****************' },
        { label: 'Technical Skills', value: 'JavaScript, TypeScript, React, Node.js, Python, AWS, Docker, Kubernetes' },
        { label: 'Speaking Experience', value: '3+ years, 25+ conferences' },
        { label: 'Key Achievements', value: '• Led development of platform serving 1M+ users\n• Open source contributor with 500+ GitHub stars\n• AWS Certified Solutions Architect' }
      ]
    },
    'Ethics_Presentation.pdf': {
      title: 'AI Ethics Presentation Outline',
      fields: [
        { label: 'Presentation Title', value: 'Building Responsible AI Systems: A Developer\'s Guide' },
        { label: 'Duration', value: '45 minutes + Q&A' },
        { label: 'Target Audience', value: 'Software developers, AI practitioners, tech leads' },
        { label: 'Key Points', value: '• Bias detection and mitigation\n• Explainable AI principles\n• Privacy-preserving techniques\n• Ethical decision-making frameworks' },
        { label: 'Interactive Elements', value: 'Live coding demo, audience poll, case study discussion' }
      ]
    }
  };

  const currentForm = formData[fileName as keyof typeof formData] || {
    title: 'Document Preview',
    fields: [
      { label: 'File Name', value: fileName },
      { label: 'Status', value: 'Submitted to organizer' },
      { label: 'Submission Date', value: new Date().toLocaleDateString() }
    ]
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{currentForm.title}</DialogTitle>
          <DialogDescription>
            This is the filled form that was submitted to the event organizers. All fields are read-only.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 mt-4">
          {currentForm.fields.map((field, index) => (
            <div key={index}>
              <Card>
                <CardContent className="pt-4">
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-foreground">
                      {field.label}
                    </label>
                    <div className="min-h-[2rem] p-3 bg-muted rounded-md border border-border">
                      <p className="text-sm text-muted-foreground whitespace-pre-line">
                        {field.value}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
              {index < currentForm.fields.length - 1 && <Separator className="my-2" />}
            </div>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  );
}