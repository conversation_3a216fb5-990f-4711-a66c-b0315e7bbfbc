const { DataTypes } = require("sequelize");
const connection = require("../connection");

const AffiliateInquiriesHistory = connection.define("AffiliateInquiriesHistory", {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: 'Primary key for the affiliate inquiries history',
    },
    inquiry_id: {
        type: DataTypes.INTEGER,
         references:{ model: 'affiliate_inquiries', key: 'id' },
        comment: 'Foreign key referencing the inquiry',
    },
    actor: {
        type: DataTypes.TEXT,
        comment: 'Action taken on the inquiry',
    },
    status: {
        type: DataTypes.ENUM('pending', 'accepted', 'rejected', 'completed','rescheduled'),
        allowNull: false,
        defaultValue: 'pending',
    },
    note: {
        type: DataTypes.TEXT,
        allowNull: true,
    },
    inquiry_date: {
        type: DataTypes.DATE,
        allowNull: true,

    },
    inquiry_time: {
        type: DataTypes.TIME,
        allowNull: true,
    },
   
    created_at: {
        type: DataTypes.DATE,
    
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
        comment: 'Record creation timestamp',
    },
    updated_at: {
        type: DataTypes.DATE,
       
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
        comment: 'Record last update timestamp',
    }
    
}, {
    tableName: 'affiliate_inquiries_history',
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
});

module.exports = AffiliateInquiriesHistory;