import React, { useState } from 'react';
import { Di<PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface BookingStatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (reason: string, note: string) => void;
  opportunityTitle: string;
}

export function BookingStatusModal({ isOpen, onClose, onConfirm, opportunityTitle }: BookingStatusModalProps) {
  const [selectedReason, setSelectedReason] = useState('');
  const [note, setNote] = useState('');

  const handleConfirm = () => {
    if (selectedReason) {
      onConfirm(selectedReason, note);
      setSelectedReason('');
      setNote('');
      onClose();
    }
  };

  const handleCancel = () => {
    setSelectedReason('');
    setNote('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleCancel}>
      <DialogContent className="bg-surface border-border max-w-md">
        <DialogHeader>
          <DialogTitle className="text-foreground">Update Booking Status</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <p className="text-sm text-foreground-muted">
            Please select the reason for marking <strong>{opportunityTitle}</strong> as not booked:
          </p>
          
          <RadioGroup value={selectedReason} onValueChange={setSelectedReason}>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="declined-by-organizer" id="declined" />
              <Label htmlFor="declined" className="text-foreground">Declined by Organizer</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="no-response" id="no-response" />
              <Label htmlFor="no-response" className="text-foreground">No Response</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="withdrawn" id="withdrawn" />
              <Label htmlFor="withdrawn" className="text-foreground">Withdrawn</Label>
            </div>
          </RadioGroup>
          
          <div className="space-y-2">
            <Label htmlFor="note" className="text-foreground">Note (Optional)</Label>
            <Textarea
              id="note"
              placeholder="Add any additional notes..."
              value={note}
              onChange={(e) => setNote(e.target.value)}
              className="bg-surface border-border text-foreground"
              rows={3}
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel}>
            Cancel
          </Button>
          <Button 
            onClick={handleConfirm} 
            disabled={!selectedReason}
            className="bg-primary text-primary-foreground hover:bg-primary/90"
          >
            Confirm
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}