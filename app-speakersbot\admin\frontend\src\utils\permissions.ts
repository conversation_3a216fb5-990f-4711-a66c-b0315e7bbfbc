import { UserRole } from '../types';

export interface PermissionConfig {
  canView: boolean;
  canCreate: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canBulkActions: boolean;
  canImportExport: boolean;
}

export const getPermissions = (role: UserRole | undefined): PermissionConfig => {
  switch (role) {
    case 'super_admin':
      return {
        canView: true,
        canCreate: true,
        canEdit: true,
        canDelete: true,
        canBulkActions: true,
        canImportExport: true,
      };
    case 'system_admin':
      return {
        canView: true,
        canCreate: true,
        canEdit: true,
        canDelete: true,
        canBulkActions: true,
        canImportExport: true,
      };
    case 'affiliate':
      return {
        canView: true,
        canCreate: true,
        canEdit: true,
        canDelete: false,
        canBulkActions: false,
        canImportExport: false,
      };
    default:
      return {
        canView: true,
        canCreate: true,
        canEdit: true,
        canDelete: true,
        canBulkActions: true,
        canImportExport: true,
      };
  }
};

export const createActivityLog = (
  action: string,
  details: string,
  actorRole: UserRole,
  meta?: any
) => ({
  id: Date.now().toString(),
  action,
  details,
  timestamp: new Date().toISOString(),
  userId: 'current-user-id', // In real app, get from auth context
});