import { Middleware, AnyAction } from '@reduxjs/toolkit';
import { initializePermissions, clearPermissions } from '../store/slices/rbacSlice';

/**
 * Middleware to automatically initialize permissions when user logs in
 */
export const rbacMiddleware: Middleware = (store) => (next) => (action: AnyAction) => {
  const result = next(action);

  // Initialize permissions when user credentials are set
  if (action.type === 'auth/setCredentials') {
    const { user } = action.payload || {};
    if (user?.role?.name) {
      store.dispatch(initializePermissions({ roleName: user.role.name, customPermissions: user.role.permissions }));
    }
  }

  // Clear permissions when user logs out
  if (action.type === 'auth/logout') {
    store.dispatch(clearPermissions());
  }

  return result;
};
