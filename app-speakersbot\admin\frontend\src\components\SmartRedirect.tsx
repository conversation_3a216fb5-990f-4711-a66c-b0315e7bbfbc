import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAppSelector } from '../store/hooks';

const SmartRedirect: React.FC = () => {
  const user = useAppSelector((state) => state.auth.user);
  const isAuthenticated = useAppSelector((state) => state.auth.isAuthenticated);
  
  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    return <Navigate to="/admin/login" replace />;
  }
  
  // If user data is not loaded yet, redirect to dashboard (will be handled by AppLayout)
  if (!user) {
    return <Navigate to="/admin/dashboard" replace />;
  }
  
  // If user is affiliate, redirect to affiliates page
  if (user.role?.name === 'Affiliate') {
    return <Navigate to="/admin/affiliates" replace />;
  }
  
  // Default redirect to dashboard for all other users
  return <Navigate to="/admin/dashboard" replace />;
};

export default SmartRedirect;
