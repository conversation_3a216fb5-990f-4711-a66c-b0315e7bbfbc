/**
 * Manage Users page
 *
 * Responsibilities:
 * - Fetch users with server-driven pagination, filtering, and sorting
 * - Create, edit, deactivate, and delete users (permission-gated)
 * - Provide local, debounced search and persisted filter state
 *
 * Tech:
 * - UI: Shadcn components
 * - State/forms: react-hook-form
 * - Data: RTK Query hooks from `apis/usersApi`
 * - Auth/RBAC: `useAuth`, `getPermissions`
 */
import ButtonLoader from "@/components/common/ButtonLoader";
import StatusTag from "@/components/common/StatusTag";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Skeleton } from "@/components/ui/skeleton";
import {
  DeleteOutlined,
  EditOutlined,
  EyeOutlined,
  PlusOutlined,
  StopOutlined,
  UserOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import { Eye, EyeOff, RotateCcw, Search } from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import {
  useDeleteUserMutation,
  useGetPermissionsQuery,
  useLazyGetUserByIdQuery,
  useGetUsersQuery,
  useUpdateUserMutation
} from "../../apis/usersApi";
import { InvalidTokenHandler } from "../../components/common/InvalidTokenHandler";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "../../components/ui/alert-dialog";
import { Badge } from "../../components/ui/badge";
import { Button } from "../../components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import { Checkbox } from "../../components/ui/checkbox";
import { Combobox } from "../../components/ui/combobox";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog";
import { Input } from "../../components/ui/input";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "../../components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../../components/ui/tooltip";
import { toast } from "../../components/ui/use-toast";
import { useAuth } from "../../state/AuthContext";
import type { User, UserRole } from "../../types";
import { useDebouncedCallback } from "../../utils/debounce";
import { formatDateYMD, RoleId_Mapper } from "../../utils/helper";
import { getPermissions } from "../../utils/permissions";
import UserSkeletion from "./UserSkeletion";

const Users: React.FC = () => {
  const { user: currentUser } = useAuth();
  const navigate = useNavigate();

  // API hooks for users data and mutations

  const [
    updateUser,
    { error: errorUpdateUser, isLoading: isLoadingUpdateUser },
  ] = useUpdateUserMutation();
  const [
    deleteUser,
    { error: errorDeleteUser, isLoading: isLoadingDeleteUser },
  ] = useDeleteUserMutation();

  // Permissions
  const permissions = getPermissions(String(currentUser?.role) as UserRole);

  const [filters, setFilters] = useState(() => {
    return {
      searchText: "",
      statusFilter: null,
      roleFilter: null as number | null,
    };
  });

  // Local input state for responsive typing; filters update is debounced
  const [searchInput, setSearchInput] = useState<string>(() =>
    typeof filters.searchText === "string" ? filters.searchText : ""
  );

  useEffect(() => {
    resetFilters();
  }, []);

  useEffect(() => {
    // keep input in sync if filters are changed externally
    if (filters.searchText !== searchInput) {
      setSearchInput(filters.searchText);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filters.searchText]);

  // Debounced updater for search filter
  const setSearchDebounced = useDebouncedCallback<[string]>((value) => {
    updateFilter("searchText", value);
  }, 400);

  // Primary user create/edit dialog visibility
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDeactivateModalVisible, setIsDeactivateModalVisible] =
    useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [deactivatingUser, setDeactivatingUser] = useState<User | null>(null);
  // Selected role drives conditional affiliate fields in the dialog
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const [commissionType, setCommissionType] = useState<
    "fixed" | "percentage" | null
  >(null);
  const [commissionValue, setCommissionValue] = useState<number>(0);
  const [showPassword, setShowPassword] = useState(false);
  const [selectedPermissions, setSelectedPermissions] = useState<number[]>([]);

  // Helper function to handle permission dependencies
  const handlePermissionChange = (permissionId: number, checked: boolean) => {
    setSelectedPermissions((prev) => {
      let newPermissions = [...prev];

      if (checked) {
        // Add the selected permission
        if (!newPermissions.includes(permissionId)) {
          newPermissions.push(permissionId);
        }

        // Auto-select corresponding read permission for write permissions
        const permission = permissionsData?.data?.find(
          (p: any) => p.permission_id === permissionId
        );
        if (permission && permission.permission_name.startsWith("write_")) {
          const readPermissionName = permission.permission_name.replace(
            "write_",
            "read_"
          );
          const readPermission = permissionsData?.data?.find(
            (p: any) => p.permission_name === readPermissionName
          );
          if (
            readPermission &&
            !newPermissions.includes(readPermission.permission_id)
          ) {
            newPermissions.push(readPermission.permission_id);
          }
        }
      } else {
        // Remove the deselected permission
        newPermissions = newPermissions.filter((id) => id !== permissionId);

        // Auto-deselect corresponding write permission for read permissions
        const permission = permissionsData?.data?.find(
          (p: any) => p.permission_id === permissionId
        );
        if (permission && permission.permission_name.startsWith("read_")) {
          const writePermissionName = permission.permission_name.replace(
            "read_",
            "write_"
          );
          const writePermission = permissionsData?.data?.find(
            (p: any) => p.permission_name === writePermissionName
          );
          if (
            writePermission &&
            newPermissions.includes(writePermission.permission_id)
          ) {
            newPermissions = newPermissions.filter(
              (id) => id !== writePermission.permission_id
            );
          }
        }
      }

      return newPermissions;
    });
  };

  // User form values
  type UserFormValues = {
    name: string;
    email: string;
    roleId: number | null;
    status: boolean; // true = active, false = inactive
    password?: string;
    phone?: string;
    legalBusinessName?: string;
    website?: string;
    commissionType?: "fixed" | "percentage";
    commissionValue?: number;
  };

  // User form
  const form = useForm<UserFormValues>({
    defaultValues: {
      name: "",
      email: "",
      roleId: null,
      status: true,
      password: "",
      phone: "",
      legalBusinessName: "",
      website: "",
      commissionType: null,
      commissionValue: 0,
    },
  });

  // Role options
  const ROLE_OPTIONS: { id: number; label: string; value: UserRole }[] = [
    { id: 1, label: "Super Admin", value: "super_admin" },
    { id: 2, label: "Affiliate", value: "affiliate" },
    { id: 3, label: "System Admin", value: "system_admin" },
  ];

  // Limit which roles can be created based on the current user's role
  const createRoleIds = useMemo<number[]>(() => {
    // If current user has roleId 3 (system admin), they can only create affiliate users
    if (currentUser?.roleId == 3) {
      return [2];
    }
    // For other roles (like super admin), they can create all roles
    return [1, 2, 3];
  }, [currentUser?.roleId]);
  const CREATE_ROLE_OPTIONS = useMemo(
    () => ROLE_OPTIONS.filter((r) => createRoleIds.includes(r.id)),
    [createRoleIds]
  );

  // Client-side sorting and pagination for the Shadcn table
  type SortField = "name" | "email" | "createdAt" | null;
  type SortOrder = "asc" | "desc";

  const [sortField, setSortField] = useState<SortField>(null);
  const [sortOrder, setSortOrder] = useState<SortOrder>("asc");
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  const [triggerGetUserById, { data: userByIdData, isFetching: isFetchingUserById } ] = useLazyGetUserByIdQuery();

  // Trigger fetch-by-id on edit click, then populate when data arrives
  const onEditClick = async (userId: string) => {
    if (!permissions.canEdit) {
      toast({ description: "You do not have permission to edit users" });
      return;
    }
    // set minimal editing user to kick off the query with id
    setEditingUser({ id: userId } as any);
    setIsModalVisible(true);
    try {
      await triggerGetUserById(userId, true);
    } catch (e) {
      // ignore; errors are handled by useEffect toast below if needed
    }
  };

  useEffect(() => {
    if (
      isModalVisible &&
      editingUser?.id &&
      userByIdData?.data &&
      !isFetchingUserById
    ) {
      handleEditUser(userByIdData.data as any);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isModalVisible, editingUser?.id, userByIdData?.data, isFetchingUserById]);

  useEffect(() => {
    setPage(1);
  }, [filters]);

  // Small helper to update a single filter key
  const updateFilter = (key: string, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const resetFilters = () => {
    setFilters({
      searchText: "",
      statusFilter: null,
      roleFilter: null,
    });
    setSearchInput("");
  };

  // Normalize API users → view model used by the table
  type ListUser = {
    id: string;
    name: string;
    email: string;
    role: string;
    roleId: number | null;
    phone: string;
    legalBusinessName: string;
    website: string;
    status: "active" | "inactive";
    createdAt: string;
  };

  // Build backend filter/sort params
  const sortFieldMap: Record<string, string> = {
    name: "name",
    email: "email",
    createdAt: "created_at",
  };
  const sortFieldApi = sortField ? sortFieldMap[sortField] : undefined;

  // Build filter for API
  const apiFilter = useMemo(() => {
    const filter: Record<string, any> = {};
    if (filters.statusFilter === "active") filter.is_active = true;
    if (filters.statusFilter === "inactive") filter.is_active = false;
    if (typeof filters.roleFilter === "number")
      filter.role_id = filters.roleFilter;
    return filter;
  }, [filters.statusFilter, filters.roleFilter]);

  // Build sort for API
  const apiSort = useMemo(() => {
    if (!sortField) return {} as Record<string, "ASC" | "DESC">;
    const sortColumnMap: Record<string, string> = {
      name: "name",
      email: "email",
      createdAt: "created_at",
    };
    const column = sortColumnMap[sortField];
    if (!column) return {} as Record<string, "ASC" | "DESC">;
    return { [column]: (sortOrder || "asc").toUpperCase() as "ASC" | "DESC" };
  }, [sortField, sortOrder]);

  const {
    data: usersData,
    isLoading: isLoadingUsers,
    error: errorUsers,
    isFetching: isFetchingUsers,
    refetch,
  } = useGetUsersQuery(
    {
      search: filters.searchText,
      page,
      limit: pageSize,
      filter: apiFilter,
      sort: apiSort,
    },
    {
      refetchOnMountOrArgChange: true,
      refetchOnReconnect: true,
    }
  );

  const { data: permissionsData } = useGetPermissionsQuery({
    refetchOnMountOrArgChange: true,
    refetchOnReconnect: true,
  });
  const apiUsers: any[] = usersData?.data?.users || [];
  const normalizedUsers: ListUser[] = apiUsers.map((u) => ({
    id: String(u.id),
    name: u.name,
    email: u.email,
    phone: u.phone,
    legalBusinessName: u.legal_business_name,
    website: u.website,
    commissionType: u.commission_type,
    commissionValue: u.commission,
    role: String(u.role?.name || ""),
    roleId: typeof u.role_id === "number" ? u.role_id : null,
    status: u.is_active ? "active" : "inactive",
    permission_ids: u.permission_ids,
    createdAt: u.created_at,
    updatedAt: u.updated_at,
  }));

  // Navigate to Affiliates page preselecting the user
  const handleViewAffiliate = (userId: string) => {
    navigate(`/admin/affiliates`, { state: { selectedUserId: userId } });
  };

  const handleAddUser = () => {
    if (!permissions.canCreate) {
      toast({ description: "You do not have permission to add users" });
      return;
    }
    setEditingUser(null);
    setSelectedRole(null);
    setCommissionType(null);
    setCommissionValue(0);
    setShowPassword(false);
    setSelectedPermissions([]);
    form.reset({
      name: "",
      email: "",
      roleId: null,
      status: true,
      password: "",
      phone: "",
      legalBusinessName: "",
      website: "",
      commissionType: null,
      commissionValue: 0,
    });
    setIsModalVisible(true);
  };

  const handleEditUser = (user: any) => {
    if (!permissions.canEdit) {
      toast({ description: "You do not have permission to edit users" });
      return;
    }
    const normalizedUsers: any = {
      id: String(user.id),
      name: user.name,
      email: user.email,
      phone: user.phone,
      legalBusinessName: user.legal_business_name,
      website: user.website,
      commissionType: user.commission_type,
      commissionValue: user.commission,
      role: String(user.role?.name || ""),
      roleId: typeof user.role_id == "number" ? user .role_id : null,
      status: user.is_active ? "active" : "inactive",
      permission_ids: user.permission_ids,
    };
    setEditingUser(user as any);
    const incomingRoleId =
      typeof (user as any).role_id === "number"
        ? (user as any).role_id
        : typeof (user as any).roleId === "number"
        ? (user as any).roleId
        : null;
    const roleFromId =
      ROLE_OPTIONS.find((r) => r.id == incomingRoleId)?.value ?? null;

    setSelectedRole(roleFromId);
    setCommissionType(user.commission_type);
    setCommissionValue(user.commission);

    // Handle permissions based on role for editing
    if (incomingRoleId === 1) {
      // Super Admin: Don't set selectedPermissions as they get all permissions automatically
      setSelectedPermissions([]);
    } else if (incomingRoleId === 2) {
      // Affiliate: Don't set selectedPermissions as they get permission 19 automatically
      setSelectedPermissions([]);
    } else if (incomingRoleId === 3) {
      // System Admin: Set existing permissions for checkbox selection
      const existingPermissions = (user as any).permission_ids
        ? JSON.parse((user as any).permission_ids)
        : [];
      setSelectedPermissions(existingPermissions);
    }

    form.reset({
      name: normalizedUsers.name,
      email: normalizedUsers.email,
      roleId: incomingRoleId ?? null,
      status:
        (normalizedUsers as any).status === "active" || (normalizedUsers as any).is_active === true,
      phone: normalizedUsers.phone,
      legalBusinessName: normalizedUsers.legalBusinessName,
      website: normalizedUsers.website,
      commissionType: normalizedUsers.commissionType,
      commissionValue: normalizedUsers.commissionValue,
    });
    setIsModalVisible(true);
  };

  const handleDeactivateUser = (user: User) => {
    setDeactivatingUser(user);
    setIsDeactivateModalVisible(true);
  };

  const handleDeactivateSubmit = async (action: "deactivate" | "delete") => {
    if (!deactivatingUser) return;

    try {
      if (action === "deactivate") {
        await updateUser({
          id: deactivatingUser.id,
          is_active: false,
          updated_at: dayjs().toISOString(),
        }).unwrap();
        toast({ description: "User deactivated successfully" });
        refetch();
      } else {
        await deleteUser(deactivatingUser.id).unwrap();
        toast({ description: "User deleted permanently" });
        refetch();
      }
    } catch (error) {
      toast({
        description: error?.data?.error?.message || `Failed to ${action} user`,
        variant: "destructive",
      });
      return;
    }

    setIsDeactivateModalVisible(false);
    setDeactivatingUser(null);
  };

  const handleDeleteUser = async (userId: string) => {
    try {
      await deleteUser(userId).unwrap();
      toast({ description: "User deleted successfully" });
      refetch();
    } catch (error) {
      toast({
        description: error?.data?.error?.message || "Failed to delete user",
        variant: "destructive",
      });
    }
  };

  const onSubmit = async (values: UserFormValues) => {
    try {
      // Validate permissions for system admin users
      if (values.roleId === 3 && selectedPermissions.length === 0) {
        toast({
          description:
            "Please select at least one permission for system admin users",
          variant: "destructive",
        });
        return;
      }

      const payload: any = {
        name: values.name,
        email: values.email,
        is_active: values.status,
      };

      // Handle permissions based on role
      if (values.roleId === 1) {
        // Super Admin: Get all permission IDs
        const allPermissionIds =
          permissionsData?.data?.map((p: any) => p.permission_id) || [];
        payload.permission_ids = allPermissionIds;
      } else if (values.roleId === 2) {
        // Affiliate: Get only permission ID 19
        payload.permission_ids = [19];
      } else if (values.roleId === 3) {
        // System Admin: Use selected permissions
        payload.permission_ids = selectedPermissions;
      }

      if (editingUser) {
        payload.id = editingUser.id;
        if (selectedRole == "affiliate") {
          if (values.phone) payload.phone = values.phone;
          if (values.legalBusinessName)
            payload.legal_business_name = values.legalBusinessName;
          if (values.website) payload.website = values.website;
          if (commissionType) payload.commission_type = commissionType;
          if (commissionValue) payload.commission = commissionValue;
        }
        await updateUser(payload).unwrap();
        toast({ description: "User updated successfully" });
        refetch();
      } else {
        // Add password for new users
        payload.role_id = values.roleId;
        if (values.password) {
          payload.password = values.password;
        }
        if (selectedRole == "affiliate") {
          if (values.phone) payload.phone = values.phone;
          if (values.legalBusinessName)
            payload.legal_business_name = values.legalBusinessName;
          if (values.website) payload.website = values.website;
          if (commissionType) payload.commission_type = commissionType;
          if (commissionValue) payload.commission = commissionValue;
        }
        await updateUser(payload).unwrap();
        toast({
          description: "User added successfully",
        });
        refetch();
      }
      setIsModalVisible(false);
      form.reset({
        name: "",
        email: "",
        roleId: null,
        status: true,
        password: "",
        phone: "",
        legalBusinessName: "",
        website: "",
        commissionType: null,
        commissionValue: 0,
      });
      setSelectedPermissions([]);
    } catch (error) {
      toast({
        description: error?.data?.error?.message || "Failed to save user",
        variant: "destructive",
      });
    }
  };

  // Authorization checks for delete/deactivate
  const canDeleteUser = (user: User) => {
    if (!permissions.canDelete) return false;
    if (String(user.id) === String(currentUser?.id)) return false;
    if (currentUser?.roleId == 3 && user?.roleId == 1) return false;
    return true;
  };

  const canDeactivateUser = (user: User) => {
    if (!permissions.canDelete) return false;
    if (String(user.id) === String(currentUser?.id)) return false;
    if (currentUser?.roleId == 3 && user?.roleId == 1) return false;
    if (user.status === "inactive") return false;
    return true;
  };

  // Authorization checks for edit
  const canEditUser = (user: User) => {
    if (!permissions.canEdit) return false;
    if (currentUser?.roleId == 3 && user.roleId == 1) return false;
    return true;
  };

  // Server-driven pagination: use API metadata; list is already paged
  const total = usersData?.pagination?.total ?? normalizedUsers.length;
  const totalPages = Math.max(
    1,
    usersData?.pagination?.totalPage ??
      usersData?.pagination?.totalPages ??
      Math.ceil(total / pageSize)
  );
  const currentPage = usersData?.pagination?.page ?? page;
  const pagedUsers = normalizedUsers;

  const pageItems = React.useMemo<(number | "ellipsis")[]>(() => {
    const totalP = totalPages;
    const currentP = currentPage;
    if (totalP <= 7) {
      return Array.from({ length: totalP }, (_, i) => i + 1);
    }
    const pages: number[] = [];
    pages.push(1);
    const start = Math.max(2, currentP - 1);
    const end = Math.min(totalP - 1, currentP + 1);
    for (let i = start; i <= end; i++) pages.push(i);
    pages.push(totalP);

    // Build with ellipses where gaps exist
    const output: (number | "ellipsis")[] = [];
    let last = 0;
    for (const p of pages) {
      if (last && p - last > 1) output.push("ellipsis");
      output.push(p);
      last = p;
    }
    return output;
  }, [currentPage, totalPages]);

  // Toggle sort order and let API handle ordering
  const toggleSort = (field: SortField) => {
    if (sortField !== field) {
      setSortField(field);
      setSortOrder("asc");
    } else {
      setSortOrder((prev) => (prev === "asc" ? "desc" : "asc"));
    }
  };

  return (
    <>
      <InvalidTokenHandler
        error={errorUsers || errorUpdateUser || errorDeleteUser}
      />
      <div>
        {/* Header */}
        <div className="flex justify-between mb-6 items-center">
          <h1 className="m-0 text-2xl font-bold text-foreground">
            Manage Users
          </h1>

          <Tooltip>
            <TooltipTrigger asChild>
              <Button onClick={handleAddUser} disabled={!permissions.canCreate}>
                <PlusOutlined />
                Add User
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              {!permissions.canCreate
                ? "No permission to add users"
                : "Add new user"}
            </TooltipContent>
          </Tooltip>
        </div>

        <Card className="bg-tertiary border-border rounded-lg shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-foreground">
              Filters
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {isLoadingUsers ? (
              <>
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <Skeleton className="h-10 w-28" />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </>
            ) : (
              <>
                {/* Search and Reset - First Row */}
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground" />
                      <Input
                        placeholder="Search by name or email..."
                        value={searchInput}
                        onChange={(e) => {
                          const val = e.target.value;
                          setSearchInput(val);
                          setSearchDebounced(val);
                        }}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    className="w-full md:w-auto"
                    onClick={resetFilters}
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset
                  </Button>
                </div>

                {/* Main Filters - Second Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Combobox
                    options={[
                      { value: "clear", label: "All Status" },
                      { value: "active", label: "Active" },
                      { value: "inactive", label: "Inactive" },
                    ]}
                    value={filters.statusFilter || "clear"}
                    onValueChange={(value) =>
                      updateFilter(
                        "statusFilter",
                        value === "clear" ? null : value
                      )
                    }
                    placeholder="Filter by Status"
                  />

                  <Select
                    value={
                      typeof filters.roleFilter == "number"
                        ? String(filters.roleFilter)
                        : "clear"
                    }
                    onValueChange={(value) =>
                      updateFilter(
                        "roleFilter",
                        value === "clear" ? null : Number(value)
                      )
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Filter by Role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="clear">All Roles</SelectItem>
                      {currentUser?.roleId == 1 && (
                        <SelectItem value="1">Super Admin</SelectItem>
                      )}
                      <SelectItem value="3">System Admin</SelectItem>
                      <SelectItem value="2">Affiliate</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </>
            )}
          </CardContent>
        </Card>
        {/* Table */}
        <div className="mt-6">
          <div className="flex items-center justify-between mb-4">
            <span className="text-muted-foreground text-sm">
              {isLoadingUsers ? (
                <Skeleton className="h-[20px] w-[200px] rounded-lg" />
              ) : (
                `Showing ${normalizedUsers.length} of ${
                  usersData?.pagination?.total || normalizedUsers.length
                } users`
              )}
            </span>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground text-sm">
                Rows per page
              </span>
              <Select
                value={String(pageSize)}
                onValueChange={(v) => {
                  setPageSize(Number(v));
                  setPage(1);
                }}
              >
                <SelectTrigger className="w-[80px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {isLoadingUsers || isFetchingUsers ? (
            <UserSkeletion />
          ) : normalizedUsers.length === 0 ? (
            <div className="p-12 text-center border rounded-lg">
              <div className="mb-3 text-base">No users found</div>
              {permissions.canCreate && (
                <Button onClick={handleAddUser}>
                  <PlusOutlined /> Add First User
                </Button>
              )}
            </div>
          ) : (
            <div className="bg-tertiary border rounded-lg">
              <Table className="whitespace-nowrap">
                <TableHeader>
                  <TableRow>
                    <TableHead
                      onClick={() => toggleSort("name")}
                      className="cursor-pointer select-none"
                    >
                      Name{" "}
                      {sortField === "name"
                        ? sortOrder === "asc"
                          ? "↑"
                          : "↓"
                        : ""}
                    </TableHead>
                    <TableHead
                      onClick={() => toggleSort("email")}
                      className="cursor-pointer select-none"
                    >
                      Email{" "}
                      {sortField === "email"
                        ? sortOrder === "asc"
                          ? "↑"
                          : "↓"
                        : ""}
                    </TableHead>
                    <TableHead>Role</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead
                      onClick={() => toggleSort("createdAt")}
                      className="cursor-pointer select-none"
                    >
                      Created{" "}
                      {sortField === "createdAt"
                        ? sortOrder === "asc"
                          ? "↑"
                          : "↓"
                        : ""}
                    </TableHead>
                    <TableHead className="w-[140px]">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pagedUsers.map((record: any) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        {record.roleId == 2 ? (
                          <Button
                            variant="link"
                            onClick={() => handleViewAffiliate(record.id)}
                            className="p-0 h-auto text-primary"
                          >
                            {record.name}
                          </Button>
                        ) : (
                          record.name
                        )}
                      </TableCell>
                      <TableCell>{record.email}</TableCell>
                      <TableCell>
                        <Badge
                          className={`uppercase ${
                            record.roleId == 2
                              ? "bg-dashboard-medium/20 text-dashboard-light border border-dashboard-medium/50 rounded-md hover:bg-dashboard-medium/20 hover:text-dashboard-light"
                              : record.roleId == 3
                              ? "bg-dashboard-dark/20 text-dashboard-dark border border-dashboard-dark/50 rounded-md hover:bg-dashboard-dark/20 hover:text-dashboard-dark"
                              : "bg-destructive-superLight text-destructive border border-destructive rounded-md hover:bg-destructive-superLight hover:text-destructive"
                          }`}
                        >
                          {RoleId_Mapper[record.roleId]}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <StatusTag
                          status={record.status}
                          label={record.status.toUpperCase()}
                        />
                      </TableCell>
                      <TableCell>{formatDateYMD(record.createdAt)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1.5">
                          {record.roleId == 2 && (
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() =>
                                      handleViewAffiliate(record.id)
                                    }
                                    className="action-btn"
                                  >
                                    <EyeOutlined />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  View affiliate details
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          )}
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                onClick={() => onEditClick(record.id)}
                                  disabled={
                                    !canEditUser(record) ||
                                    (currentUser?.roleId == record.roleId &&
                                      currentUser?.roleId == 3) ||
                                    (currentUser?.roleId == 3 &&
                                      record.roleId == 1) ||
                                    currentUser?.roleId == record.roleId
                                  }
                                  className="action-btn"
                                >
                                  <EditOutlined />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                {!canEditUser(record) ||
                                (currentUser?.roleId == record.roleId &&
                                  currentUser?.roleId == 3) ||
                                (currentUser?.roleId == 3 &&
                                  record.roleId == 1) ||
                                currentUser?.roleId == record.roleId
                                  ? "No permission to edit this user"
                                  : "Edit user"}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeactivateUser(record)}
                                  disabled={
                                    !canDeactivateUser(record) ||
                                    (currentUser?.roleId == record.roleId &&
                                      currentUser?.roleId == 3) ||
                                    (currentUser?.roleId == 3 &&
                                      record.roleId == 1) ||
                                    currentUser?.roleId == record.roleId
                                  }
                                  className="action-btn text-orange-500 hover:text-orange-600"
                                >
                                  <StopOutlined />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>
                                {!canDeactivateUser(record) ||
                                (currentUser?.roleId == record.roleId &&
                                  currentUser?.roleId == 3) ||
                                (currentUser?.roleId == 3 &&
                                  record.roleId == 1) ||
                                currentUser?.roleId == record.roleId
                                  ? "No permission to deactivate this user"
                                  : "Deactivate user"}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <AlertDialog>
                                  <AlertDialogTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="sm"
                                      disabled={
                                        !canDeleteUser(record) ||
                                        (currentUser?.roleId == record.roleId &&
                                          currentUser?.roleId == 3) ||
                                        (currentUser?.roleId == 3 &&
                                          record.roleId == 1) ||
                                        currentUser?.roleId == record.roleId
                                      }
                                      className="action-btn text-red-500 hover:text-red-600"
                                    >
                                      <DeleteOutlined />
                                    </Button>
                                  </AlertDialogTrigger>
                                  <AlertDialogContent>
                                    <AlertDialogHeader>
                                      <AlertDialogTitle>
                                        Delete user permanently
                                      </AlertDialogTitle>
                                      <AlertDialogDescription>
                                        This action cannot be undone. Are you
                                        sure?
                                      </AlertDialogDescription>
                                    </AlertDialogHeader>
                                    <AlertDialogFooter>
                                      <AlertDialogCancel>
                                        Cancel
                                      </AlertDialogCancel>
                                      <AlertDialogAction
                                        onClick={() =>
                                          handleDeleteUser(record.id)
                                        }
                                      >
                                        Delete
                                      </AlertDialogAction>
                                    </AlertDialogFooter>
                                  </AlertDialogContent>
                                </AlertDialog>
                              </TooltipTrigger>
                              <TooltipContent>
                                {!canDeleteUser(record) ||
                                (currentUser?.roleId == record.roleId &&
                                  currentUser?.roleId == 3) ||
                                (currentUser?.roleId == 3 &&
                                  record.roleId == 1) ||
                                currentUser?.roleId == record.roleId
                                  ? "No permission to delete this user"
                                  : "Delete user permanently"}
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              <div className="flex items-center justify-center p-4 gap-5">
                <Pagination style={{ margin: 0, width: "auto" }}>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentPage > 1) setPage(currentPage - 1);
                        }}
                        className={
                          currentPage === 1
                            ? "pointer-events-none opacity-50"
                            : ""
                        }
                      />
                    </PaginationItem>
                    {pageItems.map((item, idx) => (
                      <PaginationItem key={`${item}-${idx}`}>
                        {item === "ellipsis" ? (
                          <PaginationEllipsis />
                        ) : (
                          <PaginationLink
                            href="#"
                            isActive={item === currentPage}
                            onClick={(e) => {
                              e.preventDefault();
                              setPage(item as number);
                            }}
                          >
                            {item}
                          </PaginationLink>
                        )}
                      </PaginationItem>
                    ))}
                    <PaginationItem>
                      <PaginationNext
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          if (currentPage < totalPages)
                            setPage(currentPage + 1);
                        }}
                        className={
                          currentPage === totalPages
                            ? "pointer-events-none opacity-50"
                            : ""
                        }
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          )}
        </div>

        <Dialog open={isModalVisible} onOpenChange={(open) => {
          setIsModalVisible(open);
          if (!open) {
            setEditingUser(null);
          }
        }}>
          <DialogContent className="user-modal max-h-[90vh] flex flex-col overflow-y-auto">
            <DialogHeader>
              <DialogTitle>
                {editingUser ? "Edit User" : "Add User"}
              </DialogTitle>
            </DialogHeader>
            {editingUser && isFetchingUserById ? (
              <div className="space-y-4">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                {selectedRole === "system_admin" && (
                  <>
                    <Skeleton className="h-6 w-40" />
                    <div className="space-y-2">
                      <Skeleton className="h-8 w-full" />
                      <Skeleton className="h-8 w-full" />
                      <Skeleton className="h-8 w-full" />
                    </div>
                  </>
                )}
                {selectedRole == "affiliate" && (
                  <>
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-10 w-full" />
                    <Skeleton className="h-10 w-full" />
                  </>
                )}
                <div className="flex items-center justify-end gap-2 w-full">
                  <Button variant="outline" disabled>
                    Cancel
                  </Button>
                  <Button disabled>
                    {editingUser ? "Update User" : "Add User"}
                  </Button>
                </div>
              </div>
            ) : (
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="name"
                  rules={{
                    required: "Name is required",
                  }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Name{" "}
                        <span className="text-destructive" aria-hidden>
                          *
                        </span>
                      </FormLabel>
                      <FormControl>
                        <div className="flex items-center gap-2">
                          <Input
                            startIcon={
                              <UserOutlined className="text-muted-foreground" />
                            }
                            placeholder="Enter name"
                            {...field}
                            className="bg-tertiary-foreground"
                            aria-required
                          />
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="email"
                  disabled={!!editingUser}
                  rules={{
                    required: "Email is required",
                    pattern: {
                      value:
                        /^(?:[a-zA-Z0-9_'^&+\-])+(?:\.(?:[a-zA-Z0-9_'^&+\-])+)*@(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}$/,
                      message: "Enter a valid email address",
                    },
                  }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Email{" "}
                        <span className="text-destructive" aria-hidden>
                          *
                        </span>
                      </FormLabel>
                      <FormControl>
                        <Input
                          type="email"
                          placeholder="Enter email"
                          {...field}
                          className="bg-tertiary-foreground"
                          aria-required
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {!editingUser && (
                  <FormField
                    control={form.control}
                    name="password"
                    rules={{
                      required: "Password is required",
                      minLength: {
                        value: 4,
                        message: "Password must be at least 4 characters",
                      },
                    }}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>
                          Password{" "}
                          <span className="text-destructive" aria-hidden>
                            *
                          </span>
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type={showPassword ? "text" : "password"}
                              placeholder="Enter password"
                              className="pr-10"
                              {...field}
                              aria-required
                            />
                            <button
                              type="button"
                              className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground"
                              onClick={() => setShowPassword((p) => !p)}
                              aria-label={
                                showPassword ? "Hide password" : "Show password"
                              }
                            >
                              {showPassword ? (
                                <EyeOff className="h-4 w-4" />
                              ) : (
                                <Eye className="h-4 w-4" />
                              )}
                            </button>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                )}

                <FormField
                  control={form.control}
                  name="roleId"
                  rules={{
                    required: "User type is required",
                  }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        User Type{" "}
                        <span className="text-destructive" aria-hidden>
                          *
                        </span>
                      </FormLabel>
                      <FormControl>
                        <Select
                          value={
                            field.value != null
                              ? String(field.value)
                              : undefined
                          }
                          disabled={!!editingUser}
                          onValueChange={(value) => {
                            const idNum = Number(value);
                            field.onChange(idNum);
                            const found = ROLE_OPTIONS.find(
                              (r) => r.id === idNum
                            );
                            setSelectedRole(found?.value ?? null);

                            // Handle permissions based on role
                            if (idNum === 1 || idNum === 2) {
                              // Super Admin and Affiliate: Reset permissions as they get automatic permissions
                              setSelectedPermissions([]);
                            } else if (idNum === 3) {
                              // System Admin: Reset permissions for manual selection
                              setSelectedPermissions([]);
                            }
                          }}
                          aria-required
                        >
                          <SelectTrigger className="bg-tertiary-foreground">
                            <SelectValue placeholder="Select user type" />
                          </SelectTrigger>
                          <SelectContent className="bg-tertiary-foreground">
                            {CREATE_ROLE_OPTIONS.map((r) => (
                              <SelectItem key={r.id} value={String(r.id)}>
                                {r.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {selectedRole === "system_admin" && (
                  <div className="space-y-3">
                    <label className="text-sm font-medium">
                      Permissions{" "}
                      <span className="text-destructive" aria-hidden>
                        *
                      </span>
                    </label>
                    <div className="grid grid-cols-1 gap-3 max-h-60 overflow-y-auto border rounded-md p-3 bg-tertiary-foreground">
                      {permissionsData?.data?.map((permission: any) => (
                        <div
                          key={permission.permission_id}
                          className="flex items-center space-x-2"
                        >
                          <Checkbox
                            id={`permission-${permission.permission_id}`}
                            checked={selectedPermissions.includes(
                              permission.permission_id
                            )}
                            onCheckedChange={(checked) => {
                              handlePermissionChange(
                                permission.permission_id,
                                checked as boolean
                              );
                            }}
                          />
                          <label
                            htmlFor={`permission-${permission.permission_id}`}
                            className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 cursor-pointer"
                          >
                            {permission.permission_name
                              .replace(/_/g, " ")
                              .replace(/\b\w/g, (l) => l.toUpperCase())}
                          </label>
                        </div>
                      ))}
                    </div>
                    {selectedPermissions.length === 0 && (
                      <p className="text-sm text-destructive">
                        Please select at least one permission for system admin
                        users.
                      </p>
                    )}
                  </div>
                )}

                {selectedRole == "affiliate" && (
                  <>
                    {/* Affiliate Contact & Business Info */}
                    <FormField
                      control={form.control}
                      name="phone"
                      rules={{
                        required: "Phone is required",
                        pattern: {
                          value: /^[0-9+()\-\s]{10,20}$/,
                          message: "Enter a valid phone number",
                        },
                      }}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Phone{" "}
                            <span className="text-destructive" aria-hidden>
                              *
                            </span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              type="tel"
                              placeholder="Enter phone number"
                              {...field}
                              className="bg-tertiary-foreground"
                              aria-required
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="legalBusinessName"
                      rules={{ required: "Legal Business Name is required" }}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>
                            Legal Business Name{" "}
                            <span className="text-destructive" aria-hidden>
                              *
                            </span>
                          </FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter legal business name"
                              {...field}
                              className="bg-tertiary-foreground"
                              aria-required
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="website"
                      rules={{
                        pattern: {
                          // simple URL validation
                          value:
                            /^(https?:\/\/)?([\w-]+\.)+[\w-]+(\/[\w-./?%&=]*)?$/i,
                          message: "Enter a valid URL",
                        },
                      }}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Website (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              type="url"
                              placeholder="https://your-business.com"
                              {...field}
                              className="bg-tertiary-foreground"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        Commission Type
                      </label>
                      <Select
                        value={commissionType || undefined}
                        onValueChange={(value) =>
                          setCommissionType(value as "fixed" | "percentage")
                        }
                      >
                        <SelectTrigger className="bg-tertiary-foreground">
                          <SelectValue placeholder="Select commission type" />
                        </SelectTrigger>
                        <SelectContent className="bg-tertiary-foreground">
                          <SelectItem value="fixed">Fixed Amount</SelectItem>
                          <SelectItem value="percentage">Percentage</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <label className="text-sm font-medium">
                        {commissionType == "percentage"
                          ? "Commission Percentage (%)"
                          : "Commission Amount ($)"}
                      </label>
                      <Input
                        type="number"
                        placeholder={
                          commissionType == "percentage"
                            ? "Enter percentage (e.g., 10)"
                            : "Enter amount (e.g., 50)"
                        }
                        value={commissionValue}
                        onChange={(e) =>
                          setCommissionValue(Number(e.target.value))
                        }
                        className="bg-tertiary-foreground"
                      />
                    </div>
                  </>
                )}

                <FormField
                  control={form.control}
                  name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>
                        Status{" "}
                        <span className="text-destructive" aria-hidden>
                          *
                        </span>
                      </FormLabel>
                      <FormControl>
                        <Select
                          value={field.value ? "active" : "inactive"}
                          onValueChange={(value) =>
                            field.onChange(value === "active")
                          }
                          aria-required
                        >
                          <SelectTrigger className="bg-tertiary-foreground">
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent className="bg-tertiary-foreground">
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="inactive">Inactive</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <DialogFooter>
                  <div className="flex items-center justify-end gap-2 w-full">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsModalVisible(false)}
                      disabled={isLoadingUpdateUser}
                    >
                      Cancel
                    </Button>
                    <ButtonLoader
                      type="submit"
                      disabled={isLoadingUpdateUser}
                      loading={isLoadingUpdateUser}
                    >
                      {editingUser ? "Update User" : "Add User"}
                    </ButtonLoader>
                  </div>
                </DialogFooter>
              </form>
            </Form>
            )}
          </DialogContent>
        </Dialog>

        {/* Deactivate/Delete Modal */}
        <Dialog
          open={isDeactivateModalVisible}
          onOpenChange={setIsDeactivateModalVisible}
        >
          <DialogContent className="deactivate-modal">
            <DialogHeader>
              <DialogTitle>Remove User</DialogTitle>
            </DialogHeader>
            <div className="space-y-4">
              <p>
                What would you like to do with{" "}
                <strong>{deactivatingUser?.name}</strong>?
              </p>
              <div className="w-full space-y-2">
                <Button
                  className="w-full justify-start"
                  size="lg"
                  variant="outline"
                  onClick={() => handleDeactivateSubmit("deactivate")}
                >
                  <StopOutlined className="mr-2" />
                  <div className="text-left">
                    <div className="font-semibold">Deactivate User</div>
                    <div className="text-xs text-muted- hover:text-muted">
                      User will be set to inactive status but data is preserved
                    </div>
                  </div>
                </Button>
                <Button
                  className="w-full justify-start"
                  size="lg"
                  variant="destructive"
                  onClick={() => handleDeactivateSubmit("delete")}
                >
                  <DeleteOutlined className="mr-2" />
                  <div className="text-left">
                    <div className="font-semibold">Delete User Permanently</div>
                    <div className="text-xs text-muted-foreground">
                      User and all associated data will be removed forever
                    </div>
                  </div>
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
};

export default Users;
