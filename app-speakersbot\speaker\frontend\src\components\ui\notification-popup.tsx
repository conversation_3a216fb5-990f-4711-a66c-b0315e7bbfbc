import React, { useEffect, useState } from 'react';
import { X, Bell, AlertCircle, CheckCircle, Clock, Users } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface NotificationPopupProps {
  title: string;
  message: string;
  type: 'new_opportunity' | 'deadline' | 'referral' | 'meeting' | 'success' | 'info';
  onClose: () => void;
  duration?: number;
}

const iconMap = {
  new_opportunity: Bell,
  deadline: AlertCircle,
  referral: CheckCircle,
  meeting: Users,
  success: CheckCircle,
  info: Bell,
};

const colorMap = {
  new_opportunity: 'border-primary bg-primary/5',
  deadline: 'border-warning bg-warning/5',
  referral: 'border-success bg-success/5',
  meeting: 'border-info bg-info/5',
  success: 'border-success bg-success/5',
  info: 'border-primary bg-primary/5',
};

export function NotificationPopup({ title, message, type, onClose, duration = 5000 }: NotificationPopupProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [isExiting, setIsExiting] = useState(false);
  
  const Icon = iconMap[type];

  useEffect(() => {
    // Trigger entrance animation
    const timer = setTimeout(() => setIsVisible(true), 100);
    
    // Auto-close after duration
    const autoCloseTimer = setTimeout(() => {
      handleClose();
    }, duration);

    return () => {
      clearTimeout(timer);
      clearTimeout(autoCloseTimer);
    };
  }, [duration]);

  const handleClose = () => {
    setIsExiting(true);
    setTimeout(() => {
      onClose();
    }, 300);
  };

  return (
    <div className="fixed bottom-4 right-4 z-50">
      <Card 
        className={cn(
          'w-80 shadow-lg transition-all duration-300 ease-out',
          colorMap[type],
          isVisible && !isExiting ? 'translate-x-0 opacity-100' : 'translate-x-full opacity-0'
        )}
      >
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Icon className={cn(
              'h-5 w-5 mt-0.5 flex-shrink-0',
              type === 'deadline' ? 'text-warning' :
              type === 'success' || type === 'referral' ? 'text-success' :
              type === 'meeting' ? 'text-info' : 'text-primary'
            )} />
            <div className="flex-1 min-w-0">
              <h4 className="text-sm font-semibold text-foreground mb-1">{title}</h4>
              <p className="text-sm text-foreground-muted">{message}</p>
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 hover:bg-background/50 flex-shrink-0"
              onClick={handleClose}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}