"""
Optimized Matching Helper Functions
=================================
 
This module provides optimized helper functions for speaker matching operations,
including scoring algorithms, analysis generation, and content processing.
 
Key Features:
- Optimized scoring algorithms with caching
- Efficient text processing and similarity calculation
- Robust error handling and validation
- Performance monitoring and logging
- Clean separation of concerns
 
Author: Speaker Bot Team
Version: 2.0 (Optimized)
Last Updated: 2024
"""
 
import logging
import re
import time
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
import numpy as np
# Try to import sentence transformers for semantic similarity
from sentence_transformers import SentenceTransformer
 
# Configure structured logging
logger = logging.getLogger(__name__)
 
@dataclass
class ScoringConfig:
    """Configuration constants for scoring operations."""
    MIN_SCORE_THRESHOLD: float = 0.1
    MAX_SCORE_VALUE: float = 1.0
    LOCATION_PERFECT_SCORE: float = 1.0
    LOCATION_DEFAULT_SCORE: float = 0.2
    VIRTUAL_EVENT_SCORE: float = 1.0
   
    # Enhanced scoring weights
    PRIMARY_CATEGORY_WEIGHT: float = 0.4
    SUBCATEGORY_WEIGHT: float = 0.3
    GEO_LOCATION_WEIGHT: float = 0.2
    TOPIC_WEIGHT: float = 0.1
   
    # Location component weights within the 20% location weight
    LOCATION_COUNTRY_WEIGHT: float = 0.30    # 30% of location weight
    LOCATION_STATE_WEIGHT: float = 0.30      # 30% of location weight
    LOCATION_CITY_WEIGHT: float = 0.40       # 40% of location weight
   
    # Semantic similarity settings
    SEMANTIC_MODEL_NAME: str = "all-MiniLM-L6-v2"
 
class SemanticProcessor:
    """Semantic similarity processing using sentence transformers."""
   
    def __init__(self, config: ScoringConfig):
        self.config = config
        self.model = None
        self.embedding_cache = {}
        self.model = SentenceTransformer(config.SEMANTIC_MODEL_NAME)
       
    def _compute_cosine_similarity(self, vec_a: np.ndarray, vec_b: np.ndarray) -> float:
        """Compute cosine similarity manually using NumPy operations."""
        if vec_a is None or vec_b is None:
            return 0.0
       
        # Dot product
        dot_product = np.sum(vec_a * vec_b)
       
        # Norms (magnitudes)
        norm_a = np.sqrt(np.sum(vec_a ** 2))
        norm_b = np.sqrt(np.sum(vec_b ** 2))
       
        if norm_a == 0.0 or norm_b == 0.0:
            return 0.0
       
        # Cosine similarity
        return float(dot_product / (norm_a * norm_b))
       
    def calculate_semantic_similarity(self, text1: str, text2: str) -> float:
        """Calculate semantic similarity between two texts."""
        if not self.model or not text1.strip() or not text2.strip():
            return 0.0
       
        try:
            # Get embeddings
            embedding1 = self._get_embedding(text1)
            embedding2 = self._get_embedding(text2)
           
            if embedding1 is None or embedding2 is None:
                return 0.0
           
            # Calculate cosine similarity manually
            similarity = self._compute_cosine_similarity(embedding1, embedding2)
            return similarity
           
        except Exception as e:
            logger.warning(f"Error calculating semantic similarity: {e}")
            return 0.0
   
    def _get_embedding(self, text: str) -> Optional[np.ndarray]:
        """Get embedding for text with caching."""
        if not self.model:
            return None
       
        # Check cache
        cache_key = hash(text)
        if cache_key in self.embedding_cache:
            return self.embedding_cache[cache_key]
       
        try:
            embedding = self.model.encode(text)
            self.embedding_cache[cache_key] = embedding
            return embedding
        except Exception as e:
            logger.warning(f"Error generating embedding: {e}")
            return None
 
class AdvancedTextProcessor:
    """Advanced text processing with Sentence Transformers only."""
   
    def __init__(self, config: ScoringConfig):
        self.config = config
        self.semantic_processor = SemanticProcessor(config)
   
    def calculate_advanced_similarity(self, text1: str, text2: str) -> float:
        """Calculate semantic similarity using Sentence Transformers only."""
        if not text1.strip() or not text2.strip():
            return 0.0
       
        # Return only semantic similarity (Sentence Transformers)
        return self.semantic_processor.calculate_semantic_similarity(text1, text2)
 
# TextProcessor class removed - using only Sentence Transformers now
 
class LocationMatcher:
    """Optimized location matching with API caching."""
   
    def __init__(self):
        """Initialize the LocationMatcher with configuration."""
        self.config = ScoringConfig()
   
    def _detect_location_type(self, preferred_geo: str) -> str:
        """
        Detect the location type from preferred geography string.
        Handles multiple preferences like "national, state" by checking each keyword.
       
        Args:
            preferred_geo: Speaker's preferred geography string (e.g., "national, state")
           
        Returns:
            Location type: 'international', 'national', 'state', 'regional', 'local'
        """
        if not preferred_geo:
            return 'local'  # Default to local if no preference specified
       
        preferred_lower = preferred_geo.lower().strip()
       
        # Check for exact keywords in order of priority
        if 'international' in preferred_lower:
            return 'international'
        elif 'national' in preferred_lower:
            return 'national'
        elif 'state' in preferred_lower:
            return 'state'
        elif 'regional' in preferred_lower:
            return 'regional'
        elif 'local' in preferred_lower:
            return 'local'
        else:
            return 'local'  # Default fallback
           
    def _is_global_preference(self, speaker_loc: str) -> bool:
        """Check if speaker prefers global/international opportunities."""
        return self._detect_location_type(speaker_loc) == 'international'
   
    def _parse_multiple_preferences(self, preferred_geo: str) -> List[str]:
        """
        Parse multiple preferred locations from a single string.
       
        Args:
            preferred_geo: String containing multiple preferences (e.g., "national, state")
           
        Returns:
            List of individual preference strings
        """
        if not preferred_geo:
            return ['local']  # Default to local
       
        # Split by common separators
        separators = [',', ';', '|', ' and ', ' or ']
        preferences = [preferred_geo.strip()]
       
        for sep in separators:
            new_preferences = []
            for pref in preferences:
                new_preferences.extend([p.strip() for p in pref.split(sep) if p.strip()])
            preferences = new_preferences
       
        return preferences if preferences else ['local']
   
    def calculate_location_score(self, speaker_preferred_geo: str, speaker_city: str,
                                        speaker_state: str, speaker_country: str,
                                        opportunity_location: Dict[str, str]) -> float:
        """
        Calculate location compatibility score with proper priority:
        1. First check: is_virtual → return 1.0
        2. Second check: speaker_preferred_geo is global → return 1.0
        3. Then: Calculate individual country/state/city scores
       
        Args:
            speaker_preferred_geo: Speaker's preferred geography (e.g., "ASIA", "GLOBAL")
            speaker_city: Speaker's city
            speaker_state: Speaker's state
            speaker_country: Speaker's country
            opportunity_location: Opportunity location details
           
        Returns:
            Enhanced location compatibility score (0-1)
        """
        # FIRST CHECK: If opportunity is virtual (perfect score for all speakers)
        if opportunity_location.get('is_virtual', False):
            return 1.0
       
        # SECOND CHECK: If speaker prefers global/international opportunities
        speaker_preferred_norm = speaker_preferred_geo.lower().strip() if speaker_preferred_geo else ""
        if speaker_preferred_norm and self._is_global_preference(speaker_preferred_norm):
            return 1.0
       
        # Extract opportunity location components
        opp_city = opportunity_location.get('city', '').lower().strip()
        opp_state = opportunity_location.get('state', '').lower().strip()
        opp_country = opportunity_location.get('country', '').lower().strip()
       
        # Normalize speaker location components
        speaker_city_norm = speaker_city.lower().strip()
        speaker_state_norm = speaker_state.lower().strip()
        speaker_country_norm = speaker_country.lower().strip()
       
        # Parse multiple preferences and check each one
        preferences = self._parse_multiple_preferences(speaker_preferred_geo)
       
        # Check each preference and return the best score
        for preference in preferences:
            location_type = self._detect_location_type(preference)
            if location_type == 'international':
                # International preference: always full score
                return 1.0
           
            elif location_type == 'national':
                # National preference: check country match
                if speaker_country_norm not in ["null", "None", ""] and speaker_country_norm == opp_country:
                    return 1.0
           
            elif location_type == 'state':
                # State preference: check state match
                if speaker_state_norm not in ["null", "None", ""] and speaker_state_norm == opp_state:
                    return 1.0
           
            elif location_type in ['regional', 'local']:
                # Regional/Local preference: check city match
                if speaker_city_norm not in ["null", "None", ""] and speaker_city_norm == opp_city:
                    return 1.0
        # If no specific preference matches, use weighted calculation as fallback
        country_score = self._calculate_country_score(speaker_country_norm, opp_country)
        state_score = self._calculate_state_score(speaker_state_norm, opp_state)
        city_score = self._calculate_city_score(speaker_city_norm, opp_city)
       
        # Apply proper weighting within the 20% location weight:
        weighted_location_score = (
            self.config.LOCATION_COUNTRY_WEIGHT * country_score +    # 30% of location weight
            self.config.LOCATION_STATE_WEIGHT * state_score +          # 30% of location weight
            self.config.LOCATION_CITY_WEIGHT * city_score              # 40% of location weight
        )
        return round(weighted_location_score, 2)
   
    def _calculate_country_score(self, speaker_country: str, opp_country: str) -> float:
        """Calculate country matching score."""
        # Check country match and return full score
        if speaker_country not in ["null", "None", ""] and speaker_country == opp_country:
            return 1.0
        return 0.0
   
    def _calculate_state_score(self, speaker_state: str, opp_state: str) -> float:
        """Calculate state matching score."""
        # Check state match and return full score
        if speaker_state not in ["null", "None", ""] and speaker_state == opp_state:
            return 1.0
        return 0.0
   
    def _calculate_city_score(self, speaker_city: str, opp_city: str) -> float:
        """Calculate city matching score."""
        # Check city match and return full score
        if speaker_city not in ["null", "None", ""] and speaker_city == opp_city:
            return 1.0
        return 0.0
 
class MatchAnalyzer:
    """Generate comprehensive match analysis."""
   
    def __init__(self):
        self.config = ScoringConfig()
   
    def generate_match_reasons(self, scores: Dict[str, float]) -> str:
        """
        Generate detailed match reasons based on scoring analysis.
       
        Args:
            scores: Dictionary containing component scores
           
        Returns:
            Detailed reason string
        """
        reasons = []
       
        # Primary Category (40% weight)
        primary_score = scores.get('primary_category_score', 0.0)
        if primary_score >= 0.7:
            reasons.append("Excellent primary expertise alignment")
        elif primary_score >= 0.4:
            reasons.append("Good primary expertise match")
        elif primary_score >= 0.2:
            reasons.append("Moderate primary expertise alignment")
        else:
            reasons.append("Limited primary expertise overlap")
       
        # Subcategory (30% weight)
        subcategory_score = scores.get('subcategory_score', 0.0)
        if subcategory_score >= 0.7:
            reasons.append("Strong specialized area compatibility")
        elif subcategory_score >= 0.4:
            reasons.append("Good specialized area alignment")
        elif subcategory_score >= 0.2:
            reasons.append("Some specialized area overlap")
        else:
            reasons.append("Limited specialized area match")
       
        # Location (20% weight)
        geo_score = scores.get('geo_location_score', 0.0)
        if geo_score >= 0.9:
            reasons.append("Location highly compatible")
        elif geo_score >= 0.4:
            reasons.append("Location considerations manageable")
        else:
            reasons.append("Location may present challenges")
       
        # Topic (10% weight)
        topic_score = scores.get('topic_score', 0.0)
        if topic_score >= 0.7:
            reasons.append("Strong topic relevance")
        elif topic_score >= 0.4:
            reasons.append("Good topic alignment")
        elif topic_score >= 0.2:
            reasons.append("Some topic overlap")
        else:
            reasons.append("Limited topic relevance")
       
        # Overall assessment
        overall_score = scores.get('overall_score', 0.0)
        if overall_score >= 0.8:
            reasons.append("Exceptional overall match quality")
        elif overall_score >= 0.6:
            reasons.append("Solid overall compatibility")
        elif overall_score >= 0.4:
            reasons.append("Moderate overall fit")
        else:
            reasons.append("Limited overall alignment")
       
        return "; ".join(reasons)
   
    def generate_match_strengths(self, scores: Dict[str, float]) -> str:
        """Generate match strengths based on high-scoring components."""
        strengths = []
       
        if scores.get('primary_category_score', 0.0) >= 0.7:
            strengths.append("Strong primary expertise alignment")
        if scores.get('subcategory_score', 0.0) >= 0.7:
            strengths.append("Excellent specialized area fit")
        if scores.get('geo_location_score', 0.0) >= 0.7:
            strengths.append("Location highly compatible")
        if scores.get('topic_score', 0.0) >= 0.7:
            strengths.append("Strong topic relevance")
       
        return "; ".join(strengths) if strengths else "Moderate compatibility across multiple factors"
   
    def generate_match_concerns(self, scores: Dict[str, float]) -> str:
        """Generate match concerns based on low-scoring components."""
        concerns = []
       
        if scores.get('primary_category_score', 0.0) < 0.4:
            concerns.append("Limited primary expertise alignment")
        if scores.get('subcategory_score', 0.0) < 0.4:
            concerns.append("Insufficient specialized area overlap")
        if scores.get('geo_location_score', 0.0) < 0.4:
            concerns.append("Potential location challenges")
        if scores.get('topic_score', 0.0) < 0.4:
            concerns.append("Limited topic relevance")
       
        return "; ".join(concerns) if concerns else "No major concerns identified"
   
    def generate_recommendation(self, overall_score: float, status: str) -> str:
        """Generate actionable recommendation based on match quality."""
        if status == "Matched":
            if overall_score >= 0.8:
                return "Highly recommended - excellent match with strong potential for success"
            elif overall_score >= 0.6:
                return "Recommended - good match with solid potential for engagement"
            elif overall_score >= 0.4:
                return "Consider with caution - moderate match that requires evaluation"
            else:
                return "Proceed cautiously - limited match quality may impact outcomes"
        else:
            return "Not recommended - insufficient match quality for optimal results"
   
    def generate_match_considerations(self, opportunity_data: Dict[str, Any]) -> str:
        """Generate logistical and timing considerations."""
        considerations = []
       
        # Event format
        if opportunity_data.get('is_virtual'):
            considerations.append("Virtual event - eliminates travel requirements")
        else:
            considerations.append("In-person event - requires travel planning")
       
        # Timing
        start_date = opportunity_data.get('start_date')
        if start_date:
            considerations.append(f"Scheduled for: {start_date}")
       
        # Organization
        organization = opportunity_data.get('organization')
        if organization:
            considerations.append(f"Hosted by: {organization}")
       
        # Location details
        location_parts = []
        for key in ['city', 'state', 'country']:
            value = opportunity_data.get(key)
            if value:
                location_parts.append(value)
       
        if location_parts:
            considerations.append(f"Event location: {', '.join(location_parts)}")
       
        return "; ".join(considerations)
 
class DatabaseRelationshipHelper:
    """Helper class for database relationship queries."""
   
    def __init__(self):
        from app.helpers.database_manager import db_manager
        self.SessionLocal = db_manager.SessionLocal
   
    def get_category_from_subcategory(self, subcategory_name: str) -> Optional[str]:
        """
        Get primary category name from subcategory name using database relationship.
       
        Args:
            subcategory_name: Name of the subcategory
           
        Returns:
            Primary category name or None if not found
        """
        try:
            from sqlalchemy import text
            with self.SessionLocal() as session:
                # Query to get category name from subcategory
                query = text("""
                    SELECT c.name as category_name
                    FROM subcategories sc
                    JOIN categories c ON sc.category_id = c.id
                    WHERE sc.name = :subcategory_name
                    AND sc.is_active = '1'
                    AND c.deleted_at IS NULL
                """)
               
                result = session.execute(query, {"subcategory_name": subcategory_name}).fetchone()
                if result:
                    return result[0]
                return None
               
        except Exception as e:
            logger.error(f"Error getting category from subcategory '{subcategory_name}': {e}")
            return None
 
# Create global instances
# Global instances - only using advanced_text_processor now
scoring_config = ScoringConfig()
advanced_text_processor = AdvancedTextProcessor(scoring_config)
location_matcher = LocationMatcher()
match_analyzer = MatchAnalyzer()
database_helper = DatabaseRelationshipHelper()
 
def calculate_primary_category_score(speaker_data: Dict[str, Any], opportunity_data: Dict[str, Any]) -> float:
    """
    Calculate primary category score (40% weight) using database relationships.
   
    NEW APPROACH:
    1. Get speaker's primary_category
    2. Get opportunity's subcategory
    3. Use database helper to fetch primary_category from subcategory
    4. Compare speaker primary_category with opportunity primary_category using Sentence Transformers
   
    Args:
        speaker_data: Speaker information including primary_category
        opportunity_data: Opportunity information including subcategory
       
    Returns:
        Score between 0 and 1 (will be scaled to 1-10)
    """
    start_time = time.time()
   
    try:
        # Get speaker's primary category
        speaker_primary_category = speaker_data.get("primary_category", "").lower().strip()
        if not speaker_primary_category:
            return scoring_config.MIN_SCORE_THRESHOLD
       
        # Get opportunity's subcategory
        opp_subcategory = opportunity_data.get("subcategory", "").strip()
        if not opp_subcategory:
            return scoring_config.MIN_SCORE_THRESHOLD
       
        # OPTIMIZATION: Check for exact match first (no sentence transformer needed)
        # First get the opportunity's primary category from subcategory
        opp_primary_category = database_helper.get_category_from_subcategory(opp_subcategory)
        if not opp_primary_category:
            return scoring_config.MIN_SCORE_THRESHOLD
       
        # Now compare primary categories
        if speaker_primary_category == opp_primary_category.lower():
            score = 1.0  # Perfect match
        else:
            # Only use sentence transformer for different primary categories
            score = advanced_text_processor.calculate_advanced_similarity(
                speaker_primary_category, opp_primary_category.lower()
            )
       
        # Apply minimum threshold and round to 2 decimal places
        final_score = max(scoring_config.MIN_SCORE_THRESHOLD, score)
        final_score = round(final_score, 2)
        return final_score
       
    except Exception as e:
        logger.error(f"Error calculating primary category score: {e}")
        return scoring_config.MIN_SCORE_THRESHOLD
 
def calculate_subcategory_score(speaker_data: Dict[str, Any], opportunity_data: Dict[str, Any]) -> float:
    """
    Calculate subcategory score (30% weight) using direct subcategory matching.
   
    NEW APPROACH:
    1. Get speaker's subcategory
    2. Get opportunity's subcategory
    3. Compare directly using Sentence Transformers
   
    Args:
        speaker_data: Speaker information including subcategory
        opportunity_data: Opportunity information including subcategory
       
    Returns:
        Score between 0 and 1 (will be scaled to 1-10)
    """
    start_time = time.time()
   
    try:
        # Get speaker's subcategory
        speaker_subcategory = speaker_data.get("subcategory", "").lower().strip()
        if not speaker_subcategory:
            return scoring_config.MIN_SCORE_THRESHOLD
       
        # Get opportunity's subcategory
        opp_subcategory = opportunity_data.get("subcategory", "").strip()
        if not opp_subcategory:
            return scoring_config.MIN_SCORE_THRESHOLD
       
        # OPTIMIZATION: Check for exact match first (no sentence transformer needed)
        if speaker_subcategory == opp_subcategory.lower():
            score = 1.0  # Perfect match
        else:
            # Only use sentence transformer for different subcategories
            score = advanced_text_processor.calculate_advanced_similarity(
                speaker_subcategory, opp_subcategory.lower()
            )
       
        # Apply minimum threshold and round to 2 decimal places
        final_score = max(scoring_config.MIN_SCORE_THRESHOLD, score)
        final_score = round(final_score, 2)
        return final_score
       
    except Exception as e:
        logger.error(f"Error calculating subcategory score: {e}")
        return scoring_config.MIN_SCORE_THRESHOLD
 
def calculate_geo_location_score(speaker_data: Dict[str, Any], opportunity_data: Dict[str, Any]) -> float:
    """
    Calculate geo location score (20% weight) with comprehensive location matching.
   
    Enhanced approach:
    1. Get speaker's preferred geography (preferred_speaker_geography)
    2. Get speaker's physical location (city, state, country)
    3. Get opportunity location details
    4. Calculate compatibility considering both preferred geography and physical proximity
   
    Args:
        speaker_data: Speaker information including preferred geography and physical location
        opportunity_data: Opportunity location information
       
    Returns:
        Score between 0 and 1 (will be scaled to 1-10)
    """
    start_time = time.time()
   
    try:
        # Get speaker location preferences and physical location
        speaker_preferred_geo = speaker_data.get("preferred_speaker_geography", "")
       
        # Handle nested location structure
        location_data = speaker_data.get("location", {})
        if isinstance(location_data, dict):
            speaker_city = location_data.get("city", "")
            speaker_state = location_data.get("state", "")
            speaker_country = location_data.get("country", "")
        else:
            # Fallback to direct keys if location is not a dict
            speaker_city = speaker_data.get("city", "")
            speaker_state = speaker_data.get("state", "")
            speaker_country = speaker_data.get("country", "")
       
        # If no location preference is specified, return default score
        if not speaker_preferred_geo and not any([speaker_city, speaker_state, speaker_country]):
            return scoring_config.LOCATION_DEFAULT_SCORE
       
        # Prepare opportunity location
        opportunity_location = {
            'city': opportunity_data.get("city", ""),
            'state': opportunity_data.get("state", ""),
            'country': opportunity_data.get("country", ""),
            'is_virtual': opportunity_data.get("is_virtual", False)
        }
       
        # Calculate location compatibility using enhanced matching
        location_score = location_matcher.calculate_location_score(
            speaker_preferred_geo=speaker_preferred_geo,
            speaker_city=speaker_city,
            speaker_state=speaker_state,
            speaker_country=speaker_country,
            opportunity_location=opportunity_location
        )
       
        # If 0 or None is returned, use default score instead
        if not location_score or location_score == 0:
            location_score = scoring_config.LOCATION_DEFAULT_SCORE
        # Round to 2 decimal places
        return round(location_score, 2)
       
    except Exception as e:
        logger.error(f"Error calculating geo location score: {e}")
        return scoring_config.LOCATION_DEFAULT_SCORE
 
def calculate_topic_score(speaker_data: Dict[str, Any], opportunity_data: Dict[str, Any]) -> float:
    """
    Calculate topic score (10% weight) using opportunity title+description with speaker topics.
   
    NEW APPROACH:
    1. Get speaker's topic
    2. Get opportunity's title + description
    3. Compare using Sentence Transformers
   
    Args:
        speaker_data: Speaker information including topic
        opportunity_data: Opportunity information including title and description
       
    Returns:
        Score between 0 and 1 (will be scaled to 1-10)
    """
    start_time = time.time()
   
    try:
        # Get speaker's topic
        speaker_topic = speaker_data.get("topic", "").lower().strip()
        if not speaker_topic:
            return scoring_config.MIN_SCORE_THRESHOLD
       
        # Get opportunity topic fields (title + description)
        opp_title = opportunity_data.get("title", "").lower().strip()
        opp_description = opportunity_data.get("description", "").lower().strip()
        opp_text = f"{opp_title} {opp_description}".strip()
       
        if not opp_text:
            return scoring_config.MIN_SCORE_THRESHOLD
       
        # Calculate topic similarity using Sentence Transformers
        score = advanced_text_processor.calculate_advanced_similarity(speaker_topic, opp_text)
       
        # Apply minimum threshold and round to 2 decimal places
        final_score = max(scoring_config.MIN_SCORE_THRESHOLD, score)
        final_score = round(final_score, 2)
        return final_score
       
    except Exception as e:
        logger.error(f"Error calculating topic score: {e}")
        return scoring_config.MIN_SCORE_THRESHOLD
 
def generate_match_reasons(topic_score: float, primary_category_score: float,
                         subcategory_score: float, geo_location_score: float,
                         overall_score: float) -> str:
    """Generate detailed match reasons using optimized analyzer."""
    scores = {
        'topic_score': topic_score,
        'primary_category_score': primary_category_score,
        'subcategory_score': subcategory_score,
        'geo_location_score': geo_location_score,
        'overall_score': overall_score
    }
    return match_analyzer.generate_match_reasons(scores)
 
def generate_match_strengths(topic_score: float, primary_category_score: float,
                            subcategory_score: float, geo_location_score: float) -> str:
    """Generate match strengths using optimized analyzer."""
    scores = {
        'topic_score': topic_score,
        'primary_category_score': primary_category_score,
        'subcategory_score': subcategory_score,
        'geo_location_score': geo_location_score
    }
    return match_analyzer.generate_match_strengths(scores)
 
def generate_match_concerns(topic_score: float, primary_category_score: float,
                          subcategory_score: float, geo_location_score: float) -> str:
    """Generate match concerns using optimized analyzer."""
    scores = {
        'topic_score': topic_score,
        'primary_category_score': primary_category_score,
        'subcategory_score': subcategory_score,
        'geo_location_score': geo_location_score
    }
    return match_analyzer.generate_match_concerns(scores)
 
def generate_recommendation(overall_score: float, status: str) -> str:
    """Generate recommendation using optimized analyzer."""
    return match_analyzer.generate_recommendation(overall_score, status)
 
def generate_match_considerations(opportunity_data: Dict[str, Any]) -> str:
    """Generate considerations using optimized analyzer."""
    return match_analyzer.generate_match_considerations(opportunity_data)