import { clearCredentials } from "@/store/slices/authSlice";
import { useEffect, useRef } from "react";
import { useDispatch } from "react-redux";
import { toast } from "sonner";

export function InvalidTokenHandler({ error }: { error: any }) {
  const shown = useRef(false);
  const dispatch = useDispatch();
  useEffect(() => {
    if (
      error &&
      (error?.data?.error?.message === "Invalid or expired token" ||
        error?.data?.error?.message === "Invalid token" ||
        error?.data?.error?.message === "token is required for authentication") &&
      !shown.current
    ) {
      toast.error("Token expired! Please log in again.");
      shown.current = true;
      dispatch(clearCredentials());
    }
  }, [error, dispatch]);

  if (error && error?.data?.error?.message === "Invalid Token") {
    return null;
  }
  return null;
}
