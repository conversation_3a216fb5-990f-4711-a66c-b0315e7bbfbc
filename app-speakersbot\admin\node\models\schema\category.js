// models/Category.js
const { DataTypes } = require("sequelize");
const connection = require("../connection");

const Category = connection.define("Category", {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    comment: 'Primary key for the category',
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Name of the category (must be unique)',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Record creation timestamp',
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Record last update timestamp',
  },
  deleted_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Record deletion timestamp(soft delete)',
  } 
}, {
  tableName: 'categories',
  timestamps: true,
  createdAt: "created_at",
  updatedAt: "updated_at",
  paranoid: true,
  deletedAt: "deleted_at", 

});

module.exports = Category;
