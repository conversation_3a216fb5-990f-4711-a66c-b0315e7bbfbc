
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");
const { parsePagination, getPagingData, parseJSONSafely, buildSearchWhereClause, buildFilterWhereClause } = require("../helpers/app-hepler");
const { Opportunities, Speakers, SpeakerOpportunity, sequelize,OpportunityForm } = require("../models");
const { Op } = require("sequelize");
const { Parser } = require("json2csv");
const { exportAsCSV } = require("../helpers/csv-helper");
const csv = require('fast-csv');

const opportunityService = {};

// ------------------------- opportunity-service -------------------------

/**
 * Get all opportunities with optional search, date range, and pagination.
 * Supports advanced filtering by event type, date range, and search terms.
 * Includes speaker matching data and export functionality.
 * 
 * @param {Object} getReq - The request object containing query params
 * @param {Object} getReq.query - Query parameters
 * @param {string} [getReq.query.search] - Search term for title/description
 * @param {string} [getReq.query.dateRange] - JSON date range filter
 * @param {string} [getReq.query.filter] - JSON filter object
 * @param {number} [getReq.query.page] - Page number
 * @param {number} [getReq.query.limit] - Items per page
 * @returns {Promise<Object>} List of opportunities with pagination data
 * @throws {Error} When database operation fails
 */
opportunityService.getAllOpportunities = async (getReq) => {
    try {
        const { page, limit, offset } = parsePagination(getReq.query);
        const search = getReq.query.search;
        const dateRange = parseJSONSafely(getReq.query.dateRange, "Invalid JSON date range");

        let where = {};
        let order = [['id', 'DESC']];

        const filter = parseJSONSafely(getReq.query.filter, "Invalid JSON filter");

        const searchFields = [
            'title', 'organization', 'event_type', 'city',
            'state', 'country', 'venue', 'industry', 'source_url', 'event_url', 'search_query'
        ];
        

        const sort = parseJSONSafely(getReq.query.sort, "Invalid JSON sort");
        // Validate and build safe order array
        if (sort && typeof sort === 'object') {
            let column = null;
            let rawDir = null;
 
            // Support { column: 'title', value: 'DESC' }
            if (sort.column) {
                column = String(sort.column);
                rawDir = sort.value || sort.order || sort.direction || 'DESC';
            } else {
                // Support { title: 'DESC' }
                const entries = Object.entries(sort);
                if (entries.length === 1) {
                    column = String(entries[0][0]);
                    rawDir = entries[0][1] || 'DESC';
                }
            }

            if (column) {
                const columnLc = column.toLowerCase();
                const dir = ['ASC', 'DESC'].includes(String(rawDir).toUpperCase()) ? String(rawDir).toUpperCase() : 'DESC';
                const allowed = ['title', 'organization', 'event_type','city', 'is_active', 'start_date', 'end_date', 'id'];

                if (allowed.includes(columnLc)) {
                    if (columnLc === 'start_date') {
                        order = [['start_date', dir], ['id', 'DESC']];
                    } else if (columnLc === 'end_date') {
                        order = [['end_date', dir], ['id', 'DESC']];
                    } else if (columnLc === 'id') {
                        order = [['id', dir]];
                    } else {
                        order = [[columnLc, dir], ['id', 'DESC']];
                    }
                }
            }
        }

        if (filter) {
            const filterWhere = buildFilterWhereClause(filter);
            where = { ...where, ...filterWhere };

        }

        if (search) {
            const searchWhere = buildSearchWhereClause(search, searchFields);
            where = { ...where, ...searchWhere };
        }
        if (dateRange && (dateRange.start_date || dateRange.end_date)) {
            const dateConditions = [];

            if (dateRange.start_date && dateRange.end_date) {
                // Both dates provided - show opportunities that are completely within the range
                dateConditions.push({
                    [Op.and]: [
                        { start_date: { [Op.gte]: dateRange.start_date } },
                        { start_date: { [Op.lte]: dateRange.end_date } },
                        { end_date: { [Op.lte]: dateRange.end_date } },
                        { end_date: { [Op.gte]: dateRange.start_date } }
                    ]
                });
            } else if (dateRange.start_date) {
                // Only start date provided - show opportunities that start from this date onwards
                dateConditions.push({ start_date: { [Op.gte]: dateRange.start_date } });
            } else if (dateRange.end_date) {
                // Only end date provided - show opportunities that end before or on this date
                dateConditions.push({ end_date: { [Op.lte]: dateRange.end_date } });
            }

            if (dateConditions.length > 0) {
                where[Op.and] = where[Op.and] ? [...where[Op.and], ...dateConditions] : dateConditions;
            }
        }
        const opportunities = await Opportunities.findAll({
            where,
            include: [{ model: Speakers, as: 'speakers' }, { model: OpportunityForm, as: 'forms-data' }],
            limit,
            offset,
            order,
        });

        const totalOpportunities = await Opportunities.count();

        const activeOpportunities = await Opportunities.count({
            where: { is_active: true }
        });

        const totalMatches = await SpeakerOpportunity.count();
        const acceptedMatches = await SpeakerOpportunity.count({ where: { status: 'accepted' } });

        const totalCount = await Opportunities.count({ where });
        const pageData = getPagingData(totalCount, limit, page);

        // Calculate summary statistics

        const summary = {
            totalOpportunities,
            active: activeOpportunities,
            totalMatches,
            acceptedMatches
        };

        return {
            status: true,
            message: RESPONSE_MESSAGES.SUCCESS,
            data: { opportunities, pageData, summary }
        };
    } catch (error) {
        console.error("Error in getAllOpportunities:", error);
        throw error;
    }
};

/**
 * Get an opportunity by ID.
 * @param {Object} getReq - The request object containing params.
 * @returns {Promise<Object>} The opportunity data.
 */

opportunityService.getOpportunityById = async (getReq) => {
    try {
        const id = getReq.params.id;
        const opportunity = await Opportunities.findByPk(id, {
            include: [{ model: OpportunityForm, as: 'forms-data' }],
            
        });
        if (!opportunity) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Opportunity not found");
        }
        return {
            status: true,
            message: RESPONSE_MESSAGES.SUCCESS,
            data: { opportunity }
        }
    } catch (error) {
        console.error("Error in getOpportunityById:", error);
        throw error;
    }
};

/**
 * Update an opportunity by ID.
 * @param {Object} updateReq - The request object containing params and body.
 * @returns {Promise<Object>} Result of update operation.
 */

opportunityService.updateOpportunity = async (updateReq) => {
    try {
        const id = updateReq.params.id;
        const existingOpportunity = await Opportunities.findByPk(id);
        if (!existingOpportunity) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Opportunity not found");
        }
        const updateData = updateReq.body;
        await Opportunities.update(updateData, { where: { id } });
        return {
            status: true,
            message: "Opportunity updated successfully"
        };
    } catch (error) {
        console.error("Error in updateOpportunity:", error);
        throw error;
    }
};

/**
 * Create a new opportunity.
 * @param {Object} createReq - The request object containing body.
 * @returns {Promise<Object>} Result of creation operation.
 */
opportunityService.createOpportunity = async (createReq) => {
    try {
        const newOpportunityData = createReq.body;
        const newOpportunity = await Opportunities.create(newOpportunityData);
        return {
            status: true,
            message: "Opportunity created successfully",
            data: newOpportunity
        };
    } catch (error) {
        console.error("Error in createOpportunity:", error);
        throw error;
    }
};


/**
 * Export opportunities.
 * @param {Object} exportReq - The request object containing params.
 * @returns {Promise<Object>} Result of export operation.
 */

opportunityService.exportOpportunities = async (req, res) => {
    try {
        let { ids } = req.body || {}; // selected IDs (optional)
        
        const search = req.query.search;
        const dateRange = parseJSONSafely(req.query.dateRange, "Invalid JSON date range");
        const filter = parseJSONSafely(req.query.filter, "Invalid JSON filter");

        let where = {};

        // filters (same helper used in list)
        if (filter) {
            const filterWhere = buildFilterWhereClause(filter);
            where = { ...where, ...filterWhere };
        }

        // search (same fields as list)
        if (search) {
            const searchFields = [
                'title', 'organization', 'event_type', 'city',
                'state', 'country', 'venue', 'industry', 'source_url', 'event_url', 'search_query'
            ];
            const searchWhere = buildSearchWhereClause(search, searchFields);
            where = { ...where, ...searchWhere };
        }

        // date range (same semantics as list)
        if (dateRange && (dateRange.start_date || dateRange.end_date)) {
            const dateConditions = [];
            if (dateRange.start_date && dateRange.end_date) {
                dateConditions.push({
                    [Op.and]: [
                        { start_date: { [Op.gte]: dateRange.start_date } },
                        { start_date: { [Op.lte]: dateRange.end_date } },
                        { end_date: { [Op.lte]: dateRange.end_date } },
                        { end_date: { [Op.gte]: dateRange.start_date } }
                    ]
                });
            } else if (dateRange.start_date) {
                dateConditions.push({ start_date: { [Op.gte]: dateRange.start_date } });
            } else if (dateRange.end_date) {
                dateConditions.push({ end_date: { [Op.lte]: dateRange.end_date } });
            }
            if (dateConditions.length > 0) {
                where[Op.and] = where[Op.and] ? [...where[Op.and], ...dateConditions] : dateConditions;
            }
        }

        // Optional intersect with selected IDs
        if (ids && Array.isArray(ids) && ids.length > 0) {
            where.id = where.id ? { [Op.and]: [where.id, { [Op.in]: ids }] } : { [Op.in]: ids };
        }

        const opportunities = await Opportunities.findAll({
            include: [{ model: OpportunityForm, as: 'forms-data' }],
            where,
            raw: true,
        });
       

        if (!opportunities || opportunities.length === 0) {
            return res.status(404).json({ message: "No opportunities found for current filters" });
        }

        return {
            status: true,
            message: "Opportunities fetched successfully",
            data: opportunities,
            fileName: "opportunities.csv"
        }

        // return exportAsCSV(res, opportunities, "opportunities.csv");

    } catch (error) {
        console.error("Error exporting opportunities:", error);
        res.status(500).json({ message: "Error exporting opportunities" });
    }
};


opportunityService.importOpportunities = async (req, res) => {
    return new Promise((resolve, reject) => {
        try {
            if (!req.file) {
                return resolve({ status: false, message: "CSV file is required" });
            }

            const opportunities = [];
            const stream = csv
                .parseString(req.file.buffer.toString(), { headers: true, trim: true })
                .on('error', (error) => {
                    console.error("CSV Parse Error:", error);
                    reject(error);
                })
                .on('data', (row) => {
                    opportunities.push({
                        title: row.title,
                        organization: row.organization,
                        event_type: row.event_type,
                        // add other fields mapping as per CSV & DB schema
                    });
                })
                .on('end', async () => {
                    try {
                        if (opportunities.length === 0) {
                            return resolve({ status: false, message: "No data found in CSV" });
                        }

                        // Bulk insert into DB
                        await Opportunities.bulkCreate(opportunities, {
                            ignoreDuplicates: true, // optional
                        });

                        resolve({ status: true, message: "CSV imported successfully" });
                    } catch (dbError) {
                        console.error("DB Insert Error:", dbError);
                        reject(dbError);
                    }
                });
        } catch (error) {
            reject(error);
        }
    });
};


opportunityService.getOpportunitySpeakers = async (getReq) => {
    try {
        const { page, limit, offset } = parsePagination(getReq.query);
        const opportunityId = getReq.params.id;
        const sort = parseJSONSafely(getReq.query.sort, "Invalid JSON sort");
        let sortOrder = [['speaker', 'created_at', 'DESC']];
        if (sort && sort.column) {
            const column = String(sort.column).toLowerCase();
            const rawDir = String(sort.value || 'DESC');
            const dir = ['ASC', 'DESC'].includes(rawDir.toUpperCase()) ? rawDir.toUpperCase() : 'DESC';
            // If sorting by speaker fields, use the included model
            if (['name', 'email', 'created_at', 'updated_at', 'overall_score', 'topic_score', 'expertise_score', 'audience_score', 'format_score', 'geographic_score'].includes(column)) {
                sortOrder = [['speaker', column, dir]];
            } else {
                sortOrder = [[column, dir]];
            }
        }

        let where = {};
        const filter = parseJSONSafely(getReq.query.filter, "Invalid JSON filter");
        if (filter) {
            const filterWhere = buildFilterWhereClause(filter);
            where = { ...where, ...filterWhere };
        }
        const speakerOpportunities = await SpeakerOpportunity.findAll({
            include: [{ model: Speakers, as: 'speaker' }],
            where: { opportunity_id: opportunityId, ...where },
            order: sortOrder,
            limit,
            offset,
        });

        // Extract only the speaker data from the results
        // const speakers = speakerOpportunities.map(so => so.speaker).filter(speaker => speaker !== null);

        const totalSpeakers = await SpeakerOpportunity.count({ where: { opportunity_id: opportunityId } });
        const pageData = getPagingData(totalSpeakers, limit, page);
        return {
            status: true,
            message: "Opportunity speakers fetched successfully",
            data: speakerOpportunities,
            pageData    
        };
    } catch (error) {
        console.error("Error fetching opportunity speakers:", error);
        throw error;
    }
}

opportunityService.deleteOpportunity = async (deleteReq) => {
    const transaction = await sequelize.transaction();
    try {
        const id = deleteReq.params.id;

        await Opportunities.destroy({ where: { id }, transaction });

        await SpeakerOpportunity.destroy({ where: { opportunity_id: id }, transaction });

        await transaction.commit();

        return {
            status: true,
            message: "Opportunity deleted successfully"
        };
    }
    catch (error) {
        await transaction.rollback();
        console.error("Error deleting opportunity:", error);
        throw error;
    }
}
opportunityService.getDistinctEventTypes = async (getReq) => {
    try {
        const eventTypes = await Opportunities.findAll({
            attributes: [
                [sequelize.fn('DISTINCT', sequelize.col('event_type')), 'event_type']
            ],
            raw: true
        });

        const eventTypesArray = eventTypes.map(eventType => eventType.event_type);

        return {
            status: true,
            message: "Distinct event types fetched successfully",
            data: eventTypesArray
        };
    }
    catch (error) {
        console.error("Error fetching distinct event types:", error);
        throw error;
    }
};

opportunityService.getDistinctCities = async (getReq) => {
    try {
        const cities = await Opportunities.findAll({
            attributes: [
                [sequelize.fn('DISTINCT', sequelize.col('city')), 'city']
            ],
            raw: true
        });

        const citiesArray = cities.map(city => city.city);

        return {
            status: true,
            message: "Distinct cities fetched successfully",
            data: citiesArray
        };
    }
    catch (error) {
        console.error("Error fetching distinct cities:", error);
        throw error;
    }
};

opportunityService.generateSampleCSV = async (getReq) => {
    try {
        // Read model attributes dynamically
        const rawAttrs = Opportunities && Opportunities.rawAttributes ? Opportunities.rawAttributes : null;
        if (!rawAttrs) {
            return {
                status: false,
                message: "Opportunities model attributes not available"
            };
        }

        // Map attribute entries to actual column names when defined (attr.field), otherwise use the attribute key
        const allColumns = Object.keys(rawAttrs).map(key => {
            const attr = rawAttrs[key] || {};
            return (attr.field && String(attr.field).trim()) || (attr.fieldName && String(attr.fieldName).trim()) || key;
        });

        // Exclude timestamp fields (both snake_case and camelCase)
        const exclude = new Set(['created_at', 'updated_at', 'deleted_at', 'createdAt', 'updatedAt', 'deletedAt']);

        const columns = allColumns.filter(col => !exclude.has(col));

        if (!columns || columns.length === 0) {
            return {
                status: false,
                message: "No columns found for opportunities table"
            };
        }

        return {
            status: true,
            data: columns,
            fileName: 'sample_opportunities.csv'
        };
    } catch (error) {
        console.error("Error generating sample CSV:", error);
        throw error;
    }
}


module.exports = opportunityService; 