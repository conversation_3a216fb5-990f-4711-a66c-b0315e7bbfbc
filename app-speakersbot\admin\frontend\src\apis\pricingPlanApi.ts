import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../store';
import type { PricingPlan, SubscriptionEvent } from '../types';

// Define the base URL for your API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

export const pricingPlanApi = createApi({
  reducerPath: 'pricingPlanApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Get the token from the state
      const token = (getState() as RootState).auth?.token;
      
      // If we have a token, set the authorization header
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }

      // Default to JSON for all endpoints
      if (!headers.has('Accept')) headers.set('Accept', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['PricingPlan', 'Subscription'],
  keepUnusedDataFor: 0,
  endpoints: (builder) => ({
    // ==================== PRICING PLAN ENDPOINTS ====================
    
    // Get all pricing plans
    getPlans: builder.query<{ message: string; data: PricingPlan[] }, void>({
      query: () => ({
        url: '/plans',
        headers: {
          Accept: 'application/json',
        },
      }),
      providesTags: ['PricingPlan'],
    }),
    // ==================== SUBSCRIPTION ENDPOINTS ====================
    
    // Get all subscriptions with filtering and pagination
    getSubscriptions: builder.query({
      query: (params) => {
        const queryParams = params as { 
          search?: string; 
          status?: string; 
          startDate?: string; 
          endDate?: string;
          page?: number;
          limit?: number;
        } | undefined;
        return {
          url: '/subscriptions',
          params: {
            ...(queryParams?.search && { search: queryParams.search }),
            ...(queryParams?.status && { status: queryParams.status }),
            ...(queryParams?.startDate && { startDate: queryParams.startDate }),
            ...(queryParams?.endDate && { endDate: queryParams.endDate }),
            ...(queryParams?.page && { page: queryParams.page }),
            ...(queryParams?.limit && { limit: queryParams.limit }),
          },
          headers: {
            Accept: 'application/json',
          },
        };
      },
      providesTags: ['Subscription'],
    }),

    exportSubscriptions: builder.query({
      query: (params) => ({
        url: '/subscriptions/export',
        params: params,
        headers: {
          Accept: 'text/csv',
          'Content-Type': 'application/json',
        },
        // Force blob response for CSV download
        responseHandler: async (response) => await response.blob(),
      }),
    }),
  }),
});

export const {
  // Pricing Plan hooks
  useGetPlansQuery,
  useGetSubscriptionsQuery,
  useLazyExportSubscriptionsQuery,
} = pricingPlanApi;
