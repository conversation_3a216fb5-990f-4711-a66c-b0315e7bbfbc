from app.config.config import config
from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime
from app.models.base import Base
import datetime as dt

class OpportunitiesUrl(Base):
    __tablename__ = "opportunities_url"
    id = Column(Integer, primary_key=True, autoincrement=True, comment="Primary key for the opportunities URL record")
    url = Column(Text, nullable=False, comment="URL of the opportunity")
    topic = Column(String(255), nullable=True, comment="Topic of the opportunity")
    browser = Column(String(100), nullable=True, comment="Browser used for scraping")
    status = Column(String(50), default="pending", comment="Status of the URL processing")
    retry_count = Column(Integer, default=0, nullable=False, comment="Number of retry attempts for this URL")
    created_at = Column(DateTime, default=dt.datetime.utcnow, nullable=False, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=dt.datetime.utcnow, nullable=False, comment="Record last update timestamp")

def create_opportunities_url_table():
    db_url = config.get_database_url()
    engine = create_engine(db_url)
    Base.metadata.create_all(engine)

if __name__ == "__main__":
    create_opportunities_url_table()
    print("Table 'opportunities_url' created in MySQL database")
