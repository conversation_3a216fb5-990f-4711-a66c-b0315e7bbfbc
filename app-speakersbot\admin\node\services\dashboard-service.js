const { Op } = require("sequelize");
const { Subscriptions, PricingPlan, Speakers, Category, SubCategory, Opportunities, SpeakerDetails, SpeakerOpportunity, FormType, FormQuestion, Users, AffiliateUsersDetails, Settings, Feedback, Sequelize } = require("../models");
const sequelize = require("../models/connection");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");
const { getCurrentWeekMondayToSundayRangeUTC } = require("../helpers/app-hepler");

const dashboardService = {};

// using helper getCurrentWeekMondayToSundayRange()

/**
 * Build a 7-item Mon->Sun series counting opportunities created each day this week
 */
async function getDailyScrapingVolumeThisWeek() {
    const { start: weekStart } = getCurrentWeekMondayToSundayRangeUTC();
    const labels = ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"];
    const data = [];

    for (let i = 0; i < 7; i++) {
        const dayStart = new Date(weekStart);
        dayStart.setUTCDate(weekStart.getUTCDate() + i);
        dayStart.setUTCHours(0, 0, 0, 0);

        const dayEnd = new Date(dayStart);
        dayEnd.setUTCHours(23, 59, 59, 999);

        const count = await Opportunities.count({
            where: {
                created_at: {
                    [Op.between]: [dayStart, dayEnd]
                }
            },

        });

        data.push({ label: labels[i], value: count });
    }

    return data;
}



async function getDashboardSummary() {
    // Date ranges for last 2 weeks (UTC, week starts Sunday)
    const now = new Date();
    const startOfThisWeek = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate() - now.getUTCDay()));
    const startOfLastWeek = new Date(startOfThisWeek);
    startOfLastWeek.setUTCDate(startOfThisWeek.getUTCDate() - 7);
    const startOf2WeeksAgo = new Date(startOfLastWeek);
    startOf2WeeksAgo.setUTCDate(startOfLastWeek.getUTCDate() - 7);
    const endOfLastWeek = new Date(startOfThisWeek);
    endOfLastWeek.setUTCDate(startOfThisWeek.getUTCDate() - 1);
    endOfLastWeek.setUTCHours(23, 59, 59, 999);
    const endOf2WeeksAgo = new Date(startOfLastWeek);
    endOf2WeeksAgo.setUTCDate(startOfLastWeek.getUTCDate() - 1);
    endOf2WeeksAgo.setUTCHours(23, 59, 59, 999);

    // Avg Match Rate and Applied After Match (batch queries)
    const [
        totalOpportunitiesLast, matchedOpportunitiesLast, appliedCountLast, matchCountLast,
        totalOpportunitiesPrev, matchedOpportunitiesPrev, appliedCountPrev, matchCountPrev
    ] = await Promise.all([
        Opportunities.count({ where: { created_at: { [Op.between]: [startOfLastWeek, endOfLastWeek] } } }),
        SpeakerOpportunity.count({ where: { created_at: { [Op.between]: [startOfLastWeek, endOfLastWeek] } }, distinct: true, col: 'opportunity_id' }),
        SpeakerOpportunity.count({ where: { created_at: { [Op.between]: [startOfLastWeek, endOfLastWeek] }, status: 'applied' } }),
        SpeakerOpportunity.count({ where: { created_at: { [Op.between]: [startOfLastWeek, endOfLastWeek] } } }),
        Opportunities.count({ where: { created_at: { [Op.between]: [startOf2WeeksAgo, endOf2WeeksAgo] } } }),
        SpeakerOpportunity.count({ where: { created_at: { [Op.between]: [startOf2WeeksAgo, endOf2WeeksAgo] } }, distinct: true, col: 'opportunity_id' }),
        SpeakerOpportunity.count({ where: { created_at: { [Op.between]: [startOf2WeeksAgo, endOf2WeeksAgo] }, status: 'applied' } }),
        SpeakerOpportunity.count({ where: { created_at: { [Op.between]: [startOf2WeeksAgo, endOf2WeeksAgo] } } })
    ]);
    const avgMatchRateLast = totalOpportunitiesLast > 0 ? (matchedOpportunitiesLast / totalOpportunitiesLast) * 100 : 0;
    const avgMatchRatePrev = totalOpportunitiesPrev > 0 ? (matchedOpportunitiesPrev / totalOpportunitiesPrev) * 100 : 0;
    const avgMatchRateTrend = avgMatchRatePrev !== 0 ? (avgMatchRateLast - avgMatchRatePrev) : 0;
    const appliedAfterMatchLast = matchCountLast > 0 ? (appliedCountLast / matchCountLast) * 100 : 0;
    const appliedAfterMatchPrev = matchCountPrev > 0 ? (appliedCountPrev / matchCountPrev) * 100 : 0;
    const appliedAfterMatchTrend = appliedAfterMatchPrev !== 0 ? (appliedAfterMatchLast - appliedAfterMatchPrev) : 0;

    // Revenue/Match
    const matchedSpeakerIds = await SpeakerOpportunity.findAll({
        where: { created_at: { [Op.between]: [startOfLastWeek, endOfLastWeek] } },
        attributes: [[sequelize.fn('DISTINCT', sequelize.col('speaker_id')), 'speaker_id']],
        raw: true
    });
    const speakerIds = matchedSpeakerIds.map(s => s.speaker_id);
    let avgRevenuePerMatch = 0;
    if (speakerIds.length) {
        const subs = await Subscriptions.findAll({
            where: { speaker_id: { [Op.in]: speakerIds } },
            include: [{ model: PricingPlan, as: 'plan', attributes: [] }],
            attributes: [[sequelize.fn('SUM', sequelize.col('plan.amount')), 'revenue']],
            raw: true
        });
        const totalRevenue = Number(subs[0]?.revenue || 0);
        avgRevenuePerMatch = matchCountLast > 0 ? (totalRevenue / matchCountLast) : 0;
    }

    // Low Match Opps
    const allOppsLast = await Opportunities.findAll({
        where: { created_at: { [Op.between]: [startOfLastWeek, endOfLastWeek] } },
        attributes: ['id'],
        raw: true
    });
    const oppIds = allOppsLast.map(o => o.id);
    let lowMatchOpps = 0;
    if (oppIds.length) {
        const [matchCounts, totalSpeakers] = await Promise.all([
            SpeakerOpportunity.findAll({
                where: { opportunity_id: { [Op.in]: oppIds } },
                attributes: ['opportunity_id', [sequelize.fn('COUNT', sequelize.col('speaker_id')), 'matchCount']],
                group: ['opportunity_id'],
                raw: true
            }),
            Speakers.count()
        ]);
        lowMatchOpps = matchCounts.filter(mc => totalSpeakers > 0 && (Number(mc.matchCount) / totalSpeakers) < 0.3).length;
    }

    // Format summary array
    return [
        {
            label: 'Avg Match Rate',
            value: Number(avgMatchRateLast ? avgMatchRateLast.toFixed(2) : 0),
            trend: Number(avgMatchRateTrend ? avgMatchRateTrend.toFixed(1) : 0)
        },
        {
            label: 'Applied After Match',
            value: Number(appliedAfterMatchLast ? appliedAfterMatchLast.toFixed(2) : 0),
            trend: Number(appliedAfterMatchTrend ? appliedAfterMatchTrend.toFixed(2) : 0)
        },
        {
            label: 'Revenue/Match',
            value: Math.round(avgRevenuePerMatch),
            subtext: 'Average value'
        },
        {
            label: 'Low Match Opps',
            value: lowMatchOpps,
            subtext: 'Need attention'
        }
    ];
}

/**
 * Monthly scraping trends split into fixed 4 buckets (W1..W4) for current UTC month
 * W1: 1-7, W2: 8-14, W3: 15-21, W4: 22-end
 */
async function getMonthlyScrapingTrendsFourWeeks() {
    const now = new Date();
    const year = now.getUTCFullYear();
    const month = now.getUTCMonth();

    const monthStart = new Date(Date.UTC(year, month, 1, 0, 0, 0, 0));
    const monthEnd = new Date(Date.UTC(year, month + 1, 0, 23, 59, 59, 999));

    const ranges = [
        { startDay: 1, endDay: 7 },
        { startDay: 8, endDay: 14 },
        { startDay: 15, endDay: 21 },
        { startDay: 22, endDay: monthEnd.getUTCDate() }
    ];

    const labels = ["W1", "W2", "W3", "W4"];
    const points = [];

    for (const r of ranges) {
        const rangeStart = new Date(Date.UTC(year, month, r.startDay, 0, 0, 0, 0));
        const rangeEnd = new Date(Date.UTC(year, month, r.endDay, 23, 59, 59, 999));

        // Clamp to month bounds just in case
        const start = rangeStart < monthStart ? monthStart : rangeStart;
        const end = rangeEnd > monthEnd ? monthEnd : rangeEnd;

        const count = await Opportunities.count({
            where: {
                created_at: { [Op.between]: [start, end] }
            }
        });
        points.push({ label: labels[points.length], value: count });
    }

    return points;
}

/**
 * Get breakdown of opportunities by source
 */
async function getSourceBreakdown() {
    const rows = await Opportunities.findAll({
        attributes: [
            'source',
            [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        where: {
            source: { [Op.ne]: null }
        },
        group: ['source'],
        order: [[sequelize.literal('count'), 'DESC']]
    });

    return rows.map(r => ({
        source: r.get('source') || 'Unknown',
        count: Number(r.get('count')) || 0
    }));
}

/**
 * Compute average profile completion across all speakers
 */
async function getAverageSpeakerProfileCompletion() {
    const importantFields = [
        'name',
        'email',
        'phone_number',
        'city',
        'state',
        'linkedin',
        'bio',
        'headshot',
        'company',
        'primary_category',
        'subcategory',
        'topic',
        'country',
        'speaker_website',
        'learning_objectives',
        'takeaways',
        'challenges',
        'preferred_speaker_geography',
        'speaker_credentials',
        'top_keywords',
        'differentiators'
    ];

    const totalSpeakers = await Speakers.count();
    if (totalSpeakers === 0) return 0;

    let filledAcrossAll = 0;
    for (const field of importantFields) {
        const countFilled = await Speakers.count({
            where: {
                [Op.and]: [
                    { [field]: { [Op.ne]: null } },
                    sequelize.where(sequelize.fn('TRIM', sequelize.col(field)), '!=', '')
                ]
            }
        });
        filledAcrossAll += countFilled;
    }

    const totalPossible = importantFields.length * totalSpeakers;
    const percentage = (filledAcrossAll / totalPossible) * 100;

    return Number(percentage.toFixed(1));
}

/**
 * weekly onboarding trends for last 4 weeks
*/


async function getWeeklyOnboardingTrends() {
    const now = new Date();
    const year = now.getUTCFullYear();
    const month = now.getUTCMonth();
    const monthEnd = new Date(Date.UTC(year, month + 1, 0, 23, 59, 59, 999));
    const ranges = [
        { startDay: 1, endDay: 7 },
        { startDay: 8, endDay: 14 },
        { startDay: 15, endDay: 21 },
        { startDay: 22, endDay: monthEnd.getUTCDate() }
    ];
    const labels = ["W1", "W2", "W3", "W4"];
    const allQuestions = await FormQuestion.findAll({ raw: true });
    const allFieldIds = allQuestions.map(q => q.field_id);
    const result = [];

    for (let i = 0; i < ranges.length; i++) {
        const rangeStart = new Date(Date.UTC(year, month, ranges[i].startDay, 0, 0, 0, 0));
        const rangeEnd = new Date(Date.UTC(year, month, ranges[i].endDay, 23, 59, 59, 999));

        // New signups in this week
        const newSpeakers = await Speakers.findAll({
            attributes: ['id'],
            where: {
                created_at: { [Op.between]: [rangeStart, rangeEnd] }
            },
            raw: true
        });
        const newSignupIds = newSpeakers.map(s => s.id);

        // Completed onboarding: speakers who filled all intake fields
        let completed = 0;
        if (newSignupIds.length > 0 && allFieldIds.length > 0) {
            const speakerDetails = await SpeakerDetails.findAll({
                attributes: ['speaker_id', [sequelize.fn('COUNT', sequelize.col('field_id')), 'filledCount']],
                where: {
                    speaker_id: { [Op.in]: newSignupIds },
                    field_id: { [Op.in]: allFieldIds },
                    value: { [Op.ne]: null }
                },
                group: ['speaker_id'],
                raw: true
            });
            completed = speakerDetails.filter(sd => sd.filledCount == allFieldIds.length).length;
        }

        const newSignups = newSignupIds.length;
        const droppedOff = newSignups - completed;

        result.push({
            week: labels[i],
            newSignups,
            completed,
            droppedOff
        });
    }

    return result;
}



/**
 * Get intakeform complation flow 
 */
async function getIntakeFormCompletionFlow() {
    // Get all form types
    const formTypes = await FormType.findAll({ raw: true });
    const totalSpeakers = await Speakers.count();
    if (totalSpeakers === 0) return [];

    const result = [];
    for (const formType of formTypes) {
        // Get all questions for this form type
        const questions = await FormQuestion.findAll({
            where: { form_type_id: formType.id },
            raw: true
        });
        let totalCompletion = 0;
        for (const q of questions) {
            // Count how many speakers answered this question (non-null value)
            const filled = await SpeakerDetails.count({
                where: {
                    field_id: q.field_id,
                    value: { [Op.ne]: null }
                }
            });
            const percentage = totalSpeakers ? Math.round((filled / totalSpeakers) * 100) : 0;
            totalCompletion += percentage;
        }
        const avgCompletion = questions.length > 0 ? Math.round(totalCompletion / questions.length) : 0;
        result.push({
            step: formType.title,
            completionRate: avgCompletion,
            dropoff: 100 - avgCompletion
        });
    }
    return result;
}


/**
 * Compute profile completion heatmap across all speakers
 */

async function getProfileCompletionHeatmap() {
    const totalSpeakers = await Speakers.count();
    if (totalSpeakers === 0) return [];

    // Exclude reference/non-profile columns
    const excludeFields = [
        'id', 'speaker_id', 'affiliate_id', 'stripe_customer_id', 'email_verified', 'status', 'created_at', 'updated_at', 'deleted_at', 'password'
    ];
    const speakerFields = Object.keys(Speakers.rawAttributes).filter(f => !excludeFields.includes(f));
    const heatmap = [];
    for (const field of speakerFields) {
        const filled = await Speakers.count({ where: { [field]: { [Op.ne]: null } } });
        const percentage = totalSpeakers ? Math.round((filled / totalSpeakers) * 100) : 0;
        heatmap.push({ field, percentage: percentage });
    }

    // Check all keys in SpeakerDetails
    const detailKeysRaw = await SpeakerDetails.findAll({ attributes: ['key'], group: ['key'], raw: true });
    const detailKeys = detailKeysRaw.map(d => d.key);
    for (const key of detailKeys) {
        const filled = await SpeakerDetails.count({ where: { key, value: { [Op.ne]: null } } });
        const percentage = totalSpeakers ? Math.round((filled / totalSpeakers) * 100) : 0;
        heatmap.push({ field: key, percentage: percentage });
    }

    // Sort by completion percentage (descending) and return top 10
    return heatmap.sort((a, b) => b.percentage - a.percentage).slice(0, 10);
}

/**
 * Get top 5 incompleted profile fields across all speakers
 */

async function getTopIncompleteProfileFields() {

    const totalSpeakers = await Speakers.count();
    if (totalSpeakers === 0) return [];

    // 1. Check all columns in Speakers table
    // Exclude reference/non-profile columns
    const excludeFields = [
        'id', 'speaker_id', 'affiliate_id', 'referral_speaker_id','stripe_customer_id', 'password_changed_at','source','company','email_verified', 'status', 'created_at', 'updated_at', 'deleted_at', 'password', 'stripe_subscription_id'
    ];
    const speakerFields = Object.keys(Speakers.rawAttributes).filter(f => !excludeFields.includes(f));
    const incompleteCounts = [];
    for (const field of speakerFields) {
        const filled = await Speakers.count({ where: { [field]: { [Op.ne]: null } } });
        const missing = totalSpeakers - filled;
        if (missing > 0) {
            const percentComplete = totalSpeakers > 0 ? Math.round((filled / totalSpeakers) * 100) : 0;
            const percentMissing = 100 - percentComplete;
            incompleteCounts.push({
                field,
                missingCount: missing,
                percentComplete,
                percentMissing
            });
        }
    }

    // 2. Check all keys in SpeakerDetails
    const detailKeysRaw = await SpeakerDetails.findAll({ attributes: ['key'], group: ['key'], raw: true });
    const detailKeys = detailKeysRaw.map(d => d.key);
    for (const key of detailKeys) {
        const filled = await SpeakerDetails.count({ where: { key, value: { [Op.ne]: null } } });
        const missing = totalSpeakers - filled;
        if (missing > 0) {
            const percentComplete = totalSpeakers > 0 ? Math.round((filled / totalSpeakers) * 100) : 0;
            const percentMissing = 100 - percentComplete;
            incompleteCounts.push({
                field: key,
                missingCount: missing,
                percentComplete,
                percentMissing
            });
        }
    }

    // Sort and return top 5
    return incompleteCounts.sort((a, b) => b.missingCount - a.missingCount).slice(0, 5);
}

/**
 * speaker signup source
 */
async function getSpeakerSignupSource() {
    // Count speakers signed up via Affiliate
    const affiliateCount = await Speakers.count({
        where: {
            affiliate_id: { [Op.ne]: null }
        }
    });

    // Count speakers signed up via Speaker Referral
    const referralCount = await Speakers.count({
        where: {
            referral_speaker_id: { [Op.ne]: null }
        }
    });

    // Count speakers signed up directly (neither affiliate nor referral)
    const directCount = await Speakers.count({
        where: {
            affiliate_id: null,
            referral_speaker_id: null
        }
    });

    return {
        affiliate: affiliateCount,
        referral: referralCount,
        direct: directCount
    }
}

/**
 * Get top 10 low match opportunities
 */
async function getTopLowMatchOpportunities() {
    // Get total number of speakers
    const totalSpeakers = await Speakers.count();
    if (totalSpeakers === 0) return [];

    // Get match counts for each opportunity, joined with opportunity details
    const results = await SpeakerOpportunity.findAll({
        attributes: [
            'opportunity_id',
            [sequelize.fn('COUNT', sequelize.col('speaker_id')), 'matchCount']
        ],
        group: ['opportunity_id'],
        include: [{
            model: Opportunities,
            as: 'opportunity',
            attributes: ['title', 'start_date', 'event_type']
        }],
        raw: true
    });

    // Calculate match rate, filter for <30%, sort ascending, and take top 10
    const lowMatch = results
        .map(data => ({
            opportunityName: data['opportunity.title'],
            date: data['opportunity.start_date'] ? new Date(data['opportunity.start_date']).toLocaleDateString('en-US') : '',
            type: data['opportunity.event_type'],
            matchRate: totalSpeakers > 0 ? (Number(data.matchCount) / totalSpeakers) * 100 : 0
        }))
        .filter(o => o.matchRate < 30)
        .sort((a, b) => a.matchRate - b.matchRate)
        .slice(0, 10)
        .map(o => ({
            opportunityName: o.opportunityName,
            date: o.date,
            type: o.type,
            matchRate: Number(o.matchRate.toFixed(2))
        }));

    return lowMatch;
}

/**
 * Get AI vs Manual Match
 */
async function getAIVsManualMatch() {
    // Count total matches
    const total = await SpeakerOpportunity.count();
    if (total === 0) {
        return [
            { ai: 0 },
            { manual: 0 }
        ];
    }
    const aiCount = await SpeakerOpportunity.count({ where: { ai_match: 1 } });
    const manualCount = await SpeakerOpportunity.count({ where: { ai_match: 0 } });
    let aiPercent = (aiCount / total) * 100;
    let manualPercent = 100 - aiPercent;
    // Format to 2 decimal places if not integer
    aiPercent = Number.isInteger(aiPercent) ? aiPercent : Number(aiPercent.toFixed(2));
    manualPercent = Number.isInteger(manualPercent) ? manualPercent : Number(manualPercent.toFixed(2));
    return [
        { ai: aiPercent },
        { manual: manualPercent }
    ];
}



/**
 * Get Match Rate Per Opportunity Type
 */
async function getMatchRateByOpportunityType() {
    // Get total speakers count
    const totalSpeakers = await Speakers.count();
    if (totalSpeakers === 0) return [];

    // Get all distinct event types
    const eventTypes = await Opportunities.findAll({
        attributes: [
            [sequelize.fn('DISTINCT', sequelize.col('event_type')), 'event_type']
        ],
        raw: true
    });
    const typeList = eventTypes.map(t => t.event_type);

    // For each event type, calculate match rate %
    const results = [];
    for (const eventType of typeList) {
        // Get all opportunity IDs for this event type
        const opps = await Opportunities.findAll({
            attributes: ['id'],
            where: { event_type: eventType },
            raw: true
        });
        const oppIds = opps.map(o => o.id);

        // Count total matches for these opportunities
        const matchCount = await SpeakerOpportunity.count({
            where: { opportunity_id: { [Op.in]: oppIds } }
        });

        // Calculate match rate as percentage
        const totalPossibleMatches = oppIds.length * totalSpeakers;
        const matchRate = totalPossibleMatches > 0 ? (matchCount / totalPossibleMatches) * 100 : 0;

        results.push({
            label: eventType,
            value: Number(matchRate.toFixed(2))
        });
    }

    return results;
}

/**
 * Get revenue by matched opportunities
 */

async function getRevenueByMatchedOpportunities() {
    // Get all unique speaker IDs matched to opportunities
    const speakerIds = await SpeakerOpportunity.findAll({
        attributes: [[sequelize.fn('DISTINCT', sequelize.col('speaker_id')), 'speaker_id']],
        raw: true
    });
    const ids = speakerIds.map(s => s.speaker_id);

    // Get all subscriptions for these speakers, including their plan
    const subscriptions = await Subscriptions.findAll({
        where: { speaker_id: { [Op.in]: ids } },
        include: [{ model: PricingPlan, as: 'plan' }],
        raw: true
    });

    // Sum all plan amounts
    const totalRevenue = subscriptions.reduce((sum, sub) => {
        return sum + (Number(sub['plan.amount']) || 0);
    }, 0);


    return totalRevenue;
}

/** 
 * Get low match opportunities
*/
async function getLowMatchOpportunitiesCount() {
    // Get total speakers count
    const totalSpeakers = await Speakers.count();
    if (totalSpeakers === 0) return 0;

    // Get match counts for each opportunity
    const matchCounts = await SpeakerOpportunity.findAll({
        attributes: [
            'opportunity_id',
            [sequelize.fn('COUNT', sequelize.col('speaker_id')), 'matchCount']
        ],
        group: ['opportunity_id'],
        raw: true
    });

    // Count opportunities with match percentage < 30%
    const lowMatchCount = matchCounts
        .filter(mc => (Number(mc.matchCount) / totalSpeakers) < 0.3)
        .length;

    return lowMatchCount;
}

/**
 * Affiliate Summary and Performance
 */
async function getAffiliateDashboardSummary() {
    // Date ranges for this month and last month (UTC)
    const now = new Date();
    const startOfMonth = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), 1));
    const endOfMonth = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth() + 1, 0, 23, 59, 59, 999));
    const startOfPrevMonth = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth() - 1, 1));
    const endOfPrevMonth = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth(), 0, 23, 59, 59, 999));

    // Total Revenue (affiliates only)
    const [currentRevenue, prevRevenue] = await Promise.all([
        Subscriptions.findAll({
            where: { created_at: { [Op.between]: [startOfMonth, endOfMonth] } },
            include: [
                { model: Speakers, as: 'speaker', where: { affiliate_id: { [Op.ne]: null } }, attributes: [] },
                { model: PricingPlan, as: 'plan', attributes: [] }
            ],
            attributes: [[sequelize.fn('SUM', sequelize.col('plan.amount')), 'revenue']],
            raw: true
        }),
        Subscriptions.findAll({
            where: { created_at: { [Op.between]: [startOfPrevMonth, endOfPrevMonth] } },
            include: [
                { model: Speakers, as: 'speaker', where: { affiliate_id: { [Op.ne]: null } }, attributes: [] },
                { model: PricingPlan, as: 'plan', attributes: [] }
            ],
            attributes: [[sequelize.fn('SUM', sequelize.col('plan.amount')), 'revenue']],
            raw: true
        })
    ]);
    const revenueNow = Number(currentRevenue[0]?.revenue || 0);
    const revenuePrev = Number(prevRevenue[0]?.revenue || 0);
    const revenueTrend = revenuePrev > 0 ? ((revenueNow - revenuePrev) / revenuePrev) * 100 : 0;

    // Active Affiliates (role_id: 2, is_active: true)
    const [activeAffiliatesNow, activeAffiliatesPrev] = await Promise.all([
        Users.count({ where: { role_id: 2, is_active: true, created_at: { [Op.between]: [startOfMonth, endOfMonth] } } }),
        Users.count({ where: { role_id: 2, is_active: true, created_at: { [Op.between]: [startOfPrevMonth, endOfPrevMonth] } } })
    ]);
    const totalActiveAffiliates = await Users.count({ where: { role_id: 2, is_active: true } });
    const affiliatesTrend = activeAffiliatesNow - activeAffiliatesPrev;

    // Avg Conversion (signup_count / click_count, avg for all active affiliates, this month and last month)
    const affiliates = await AffiliateUsersDetails.findAll({
        where: { created_at: { [Op.between]: [startOfMonth, endOfMonth] } },
        attributes: ['click_count', 'signup_count'],
        raw: true
    });
    let avgConv = 0, avgConvPrev = 0;
    if (affiliates.length) {
        const sumConv = affiliates.reduce((sum, a) => sum + (a.click_count > 0 ? (a.signup_count / a.click_count) * 100 : 0), 0);
        avgConv = sumConv / affiliates.length;
    }
    const affiliatesPrev = await AffiliateUsersDetails.findAll({
        where: { created_at: { [Op.between]: [startOfPrevMonth, endOfPrevMonth] } },
        attributes: ['click_count', 'signup_count'],
        raw: true
    });
    if (affiliatesPrev.length) {
        const sumConvPrev = affiliatesPrev.reduce((sum, a) => sum + (a.click_count > 0 ? (a.signup_count / a.click_count) * 100 : 0), 0);
        avgConvPrev = sumConvPrev / affiliatesPrev.length;
    }
    const convTrend = avgConvPrev !== 0 ? (avgConv - avgConvPrev) : 0;

    // Total Clicks (this month)
    const totalClicks = await AffiliateUsersDetails.sum('click_count', { where: { created_at: { [Op.between]: [startOfMonth, endOfMonth] } } });

    // Total Commissions (sum commission_paid, pending = sum commission_pending)
    // const [commissionPaid, commissionPending] = await Promise.all([
    //     AffiliateUsersDetails.sum('commission_paid', { where: { created_at: { [Op.between]: [startOfMonth, endOfMonth] } } }),
    //     AffiliateUsersDetails.sum('commission_pending', { where: { created_at: { [Op.between]: [startOfMonth, endOfMonth] } } })
    // ]);

    // Total Commission Earned (all time, 2 decimals)
    const totalCommission = await Subscriptions.sum('affiliate_commission');

    return [
        {
            label: 'Total Revenue',
            value: Number(revenueNow ? revenueNow.toFixed(2) : 0),
            trend: Number(revenueTrend ? revenueTrend.toFixed(2) : 0)
        },
        {
            label: 'Active Affiliates',
            value: Number(totalActiveAffiliates ? totalActiveAffiliates.toFixed(2) : 0),
            trend: Number(affiliatesTrend ? affiliatesTrend.toFixed(2) : 0)
        },
        {
            label: 'Avg Conversion',
            value: `${avgConv.toFixed(1)}%`,
            trend: Number(convTrend ? convTrend.toFixed(2) : 0)
        },
        {
            label: 'Total Clicks',
            value: totalClicks || 0,
            subtext: 'This month.'
        },
        {
            label: 'Total Commissions',
            value: totalCommission !== null ? Number(Number(totalCommission).toFixed(2)) : 0,
            subtext: 'All time.'
        }
    ];
}

/**
 * Get Top Affiliates By Performance
 */
async function getTopAffiliatesByPerformance(limit = 10) {
    // Get all active affiliate users (role_id = 2, is_active = true) with their affiliate details
    const affiliates = await Users.findAll({
        where: { role_id: 2, is_active: true },
        include: [{
            model: AffiliateUsersDetails,
            as: 'affiliateUsersDetails',
            required: true
        }],
        attributes: ['id', 'name', 'created_at'],
        raw: true,
        nest: true
    });

    // Fetch all speakers with affiliate_id
    const allSpeakers = await Speakers.findAll({
        attributes: ['id', 'affiliate_id'],
        where: { affiliate_id: { [Op.ne]: null } },
        raw: true
    });

    // Map affiliate_id to speaker ids
    const affiliateToSpeakerIds = {};
    for (const s of allSpeakers) {
        if (!affiliateToSpeakerIds[s.affiliate_id]) affiliateToSpeakerIds[s.affiliate_id] = [];
        affiliateToSpeakerIds[s.affiliate_id].push(s.id);
    }

    // Fetch all subscriptions for all speakers, with plan
    const allSubscriptions = await Subscriptions.findAll({
        include: [{ model: PricingPlan, as: 'plan' }],
        attributes: ['id', 'speaker_id', 'affiliate_commission', 'affiliate_id'],
        raw: true
    });

    // Map speaker_id to subscriptions
    const speakerIdToSubscriptions = {};
    for (const sub of allSubscriptions) {
        if (!speakerIdToSubscriptions[sub.speaker_id]) speakerIdToSubscriptions[sub.speaker_id] = [];
        speakerIdToSubscriptions[sub.speaker_id].push(sub);
    }

    // Build stats for each affiliate
    const affiliateStats = affiliates.map(a => {
        const details = a.affiliateUsersDetails || {};
        const clicks = Number(details.click_count) || 0;
        const signups = Number(details.signup_count) || 0;

        // Get all speakers referred by this affiliate
        const speakerIds = affiliateToSpeakerIds[a.id] || [];

        // Sum revenue and commission for all subscriptions of these speakers
        let totalRevenue = 0;
        let totalCommission = 0;
        for (const speakerId of speakerIds) {
            const subs = speakerIdToSubscriptions[speakerId] || [];
            for (const sub of subs) {
                totalRevenue += Number(sub['plan.amount']) || 0;
                totalCommission += Number(sub.affiliate_commission) || 0;  // Remove the condition
            }
        }

        // Conversion rate
        const convRate = clicks > 0 ? ((signups / clicks) * 100).toFixed(1) : '0.0';

        // Use Users.created_at for joined
        const joined = a.created_at ? new Date(a.created_at).toISOString().slice(0, 10) : '';

        return {
            affiliateName: a.name,
            status: details.status || 'Active',
            tier: details.tier || '',
            clicks,
            signups,
            convRate: Number(convRate),
            revenue: Number(totalRevenue),
            commission: Number(totalCommission),
            joined
        };
    });

    // Sort by signups (from signup_count), take top N
    return affiliateStats
        .sort((a, b) => b.signups - a.signups)
        .slice(0, limit);
}

/**
 * Get Top Revenue by Affiliate
 */

async function getRevenueByAffiliate(limit = 10) {
    // Get top affiliates by revenue (sum of subscription plan amounts for speakers referred by each affiliate)
    // Avoid JS loops by using a single query with grouping and joins
    const results = await Subscriptions.findAll({
        include: [
            {
                model: Speakers,
                as: 'speaker',
                attributes: ['affiliate_id'],
                required: true,
                where: { affiliate_id: { [Op.ne]: null } }
            },
            {
                model: PricingPlan,
                as: 'plan',
                attributes: []
            }
        ],
        attributes: [
            [sequelize.col('speaker.affiliate_id'), 'affiliate_id'],
            [sequelize.fn('SUM', sequelize.col('plan.amount')), 'revenue'],
            [sequelize.fn('SUM', sequelize.col('affiliate_commission')), 'commission']
        ],
        group: ['speaker.affiliate_id'],
        order: [[sequelize.literal('revenue'), 'DESC']],
        raw: true,
        limit
    });

    if (!results.length) return [];

    // Get affiliate names in one query
    const affiliateIds = results.map(r => r.affiliate_id);
    const affiliates = await Users.findAll({
        where: { id: { [Op.in]: affiliateIds } },
        attributes: ['id', 'name'],
        raw: true
    });
    const affiliateIdToName = Object.fromEntries(affiliates.map(a => [String(a.id), a.name]));

    // Format result to include both revenue and commission
    return results.map(r => ({
        label: affiliateIdToName[String(r.affiliate_id)] || 'Unknown',
        revenue: Number(r.revenue) || 0,
        commission: Number(r.commission) || 0
    }));
}



/**
 * Get Top Rereferrers by Affiliate
 */


async function getTopReferrersByAffiliate(limit = 10) {
    // 1. Get all speakers with affiliate_id (referred by affiliate)
    const speakers = await Speakers.findAll({
        where: { affiliate_id: { [Op.ne]: null } },
        attributes: ['id', 'name', 'affiliate_id', 'created_at', 'status'],
        raw: true
    });

    if (!speakers.length) return [];

    // 2. Get all affiliate users (for name lookup)
    const affiliateIds = [...new Set(speakers.map(s => s.affiliate_id))];
    const affiliates = await Users.findAll({
        where: { id: { [Op.in]: affiliateIds } },
        attributes: ['id', 'name'],
        raw: true
    });
    const affiliateIdToName = Object.fromEntries(affiliates.map(a => [a.id, a.name]));

    // 3. Get all SpeakerOpportunity for these speakers, aggregate by speaker_id and status
    const speakerIds = speakers.map(s => s.id);
    const speakerOppsAgg = await SpeakerOpportunity.findAll({
        where: { speaker_id: { [Op.in]: speakerIds } },
        attributes: [
            'speaker_id',
            'status',
            [sequelize.fn('COUNT', sequelize.col('id')), 'count']
        ],
        group: ['speaker_id', 'status'],
        raw: true
    });

    // Build a map: { speaker_id: { applied: X, booked: Y } }
    const speakerOppsMap = {};
    for (const row of speakerOppsAgg) {
        if (!speakerOppsMap[row.speaker_id]) speakerOppsMap[row.speaker_id] = { applied: 0, booked: 0 };
        if (row.status === 'applied') speakerOppsMap[row.speaker_id].applied = Number(row.count);
        if (row.status === 'booked') speakerOppsMap[row.speaker_id].booked = Number(row.count);
    }

    // 4. Get all Subscriptions for these speakers (with plan), aggregate revenue and commission per speaker
    const subscriptionsAgg = await Subscriptions.findAll({
        where: { speaker_id: { [Op.in]: speakerIds } },
        include: [{ model: PricingPlan, as: 'plan', attributes: [] }],
        attributes: [
            'speaker_id',
            [sequelize.fn('SUM', sequelize.col('plan.amount')), 'revenue'],
            [sequelize.fn('SUM', sequelize.col('affiliate_commission')), 'commission']
        ],
        group: ['speaker_id'],
        raw: true
    });
    const speakerRevenueMap = Object.fromEntries(
        subscriptionsAgg.map(sub => [sub.speaker_id, Number(sub.revenue) || 0])
    );
    const speakerCommissionMap = Object.fromEntries(
        subscriptionsAgg.map(sub => [sub.speaker_id, Number(sub.commission) || 0])
    );

    // 5. Build result array (commission is per speaker, not total affiliate commission)
    const result = speakers.map(s => {
        const sid = s.id;
        const opps = speakerOppsMap[sid] || { applied: 0, booked: 0 };
        const revenue = speakerRevenueMap[sid] || 0;
        const commission = speakerCommissionMap[sid] || 0;
        return {
            id: sid,
            referrer: affiliateIdToName[s.affiliate_id] || 'Unknown',
            speaker: s.name,
            signupDate: s.created_at ? new Date(s.created_at).toISOString().slice(0, 10) : '',
            applied: opps.applied,
            booked: opps.booked,
            revenue: Number(revenue),
            commission: Number(commission),
            affiliate_id: s.affiliate_id,
            status: s.status // Assuming all referred speakers are active; adjust if needed
        };
    });

    // 6. Sort by applied count desc, then booked desc, then revenue desc
    const sorted = result
        .sort((a, b) => {
            if (b.applied !== a.applied) return b.applied - a.applied;
            if (b.booked !== a.booked) return b.booked - a.booked;
            return (b.revenue - a.revenue);
        })
        .slice(0, limit);

    return sorted;
}

/**
 * Get Total Revenue For Affiliate Analytics
 */

async function getAffiliateTotalRevenueAndTrend() {
    // Get current month range
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);

    // Get previous month range
    const startOfPrevMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const endOfPrevMonth = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59);

    // Helper to sum revenue for a date range
    async function sumRevenue(startDate, endDate) {
        const subs = await Subscriptions.findAll({
            where: {
                created_at: { [Op.between]: [startDate, endDate] }
            },
            include: [
                {
                    model: Speakers,
                    as: 'speaker',
                    where: { affiliate_id: { [Op.ne]: null } }
                },
                {
                    model: PricingPlan,
                    as: 'plan'
                }
            ],
            raw: true
        });
        return subs.reduce((sum, sub) => sum + (Number(sub['plan.amount']) || 0), 0);
    }

    // Calculate current and previous month revenue
    const currentRevenue = await sumRevenue(startOfMonth, endOfMonth);
    const prevRevenue = await sumRevenue(startOfPrevMonth, endOfPrevMonth);

    // Calculate trend
    let trend = '';
    if (prevRevenue > 0) {
        const percentChange = ((currentRevenue - prevRevenue) / prevRevenue) * 100;
        trend = `${percentChange >= 0 ? '+' : ''}${percentChange.toFixed(1)}% vs last month`;
    } else {
        trend = 'N/A';
    }

    return {
        label: 'Total Revenue',
        value: Number(currentRevenue ? (currentRevenue).toFixed(2) : 0),
        trend
    };
}

/**
 * Get dashboard pipeline
 */
dashboardService.getDashboardPipeline = async () => {
    try {

        const pipeline = {};

        // week scraped details (Monday -> Sunday window)
        const { start: weekStart, end: weekEnd } = getCurrentWeekMondayToSundayRangeUTC();

        console.log(weekStart, weekEnd)
        const totalScrapedOpportunities = await Opportunities.count({
            where: {
                created_at: {
                    [Op.between]: [weekStart, weekEnd]
                }
            }, logging: console.log
        });

        const matchedOpportunities = await SpeakerOpportunity.count({
            distinct: true,
            col: 'opportunity_id'
        });

        const reviewQueue = await SpeakerOpportunity.count({
            where: {
                status: 'pending'
            }
        });

        const totalOpportunities = await Opportunities.count();

        const matchPercentage = totalOpportunities > 0 ? (matchedOpportunities / totalOpportunities) * 100 : 0;

        const healthScore = await SpeakerOpportunity.findOne({
            attributes: [
                [sequelize.fn('AVG', sequelize.col("overall_score")), 'avgHealthScore']
            ]
        })

        const summary = {
            weekScraped: totalScrapedOpportunities,
            matchedToSpeaker: matchPercentage,
            healthScore: healthScore?.dataValues?.avgHealthScore ?? 0,
            reviewQueue: reviewQueue
        }

        pipeline.summary = summary;
        pipeline.dailyScrapingVolume = await getDailyScrapingVolumeThisWeek();
        pipeline.sourceBreakdown = await getSourceBreakdown();
        pipeline.monthlyScrapingTrends = await getMonthlyScrapingTrendsFourWeeks();



        return {
            status: true,
            message: "Dashboard pipeline retrieved successfully",
            data: pipeline
        };

    } catch (error) {
        console.error("Error in getDashboardPipeline:", error);
        throw error;
    }
}

/**
 * Get comprehensive revenue metrics
 */
dashboardService.getRevenueMetrics = async (req) => {
    try {
        const { startDate, endDate, period = 'month' } = req.query;

        // Set date ranges
        const now = new Date();
        let start, end;

        if (startDate && endDate) {
            start = new Date(startDate);
            end = new Date(endDate);
        } else {
            // Default to current month
            start = new Date(now.getFullYear(), now.getMonth(), 1);
            end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
        }

        // Calculate all revenue metrics
        const [
            mrrMetrics,
            paymentRecovery,
            subscriptionTrends,
            arrByPlan,
            paymentFailuresRecovery
        ] = await Promise.all([
            calculateMRRMetrics(start, end),
            getPaymentRecoverySuccessRate(req.query.year),
            getSubscriptionTrendsForRevenue(end),
            getARRByPlan(start, end),
            getPaymentFailuresRecovery(req.query.year)
        ]);

        return {
            status: true,
            message: "Revenue metrics retrieved successfully",
            data: {
                totalMRR: mrrMetrics.totalMRR,
                newMRR: mrrMetrics.newMRR,
                expansionMRR: mrrMetrics.expansionMRR,
                churnedMRR: mrrMetrics.churnedMRR,
                netMRRGrowth: mrrMetrics.netMRRGrowth,
                annualRunRate: mrrMetrics.annualRunRate,
                paymentRecovery: { successRate: paymentRecovery },
                subscriptionTrends: subscriptionTrends,
                arrByPlan: arrByPlan,
                paymentFailuresRecovery: paymentFailuresRecovery
            }
        };

    } catch (error) {
        console.error("Error in getRevenueMetrics:", error);
        throw error;
    }
};

/**
 * Get comprehensive category analytics
 */
dashboardService.getCategoryAnalytics = async (req) => {
    try {
        const { startDate, endDate, period = 'month' } = req.query;

        // Set date ranges
        const now = new Date();
        let start, end;

        if (startDate && endDate) {
            start = new Date(startDate);
            end = new Date(endDate);
        } else {
            // Default to current month
            start = new Date(now.getFullYear(), now.getMonth(), 1);
            end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
        }

        // Get previous period for comparison
        const prevStart = new Date(start);
        const prevEnd = new Date(end);

        if (period === 'quarter') {
            prevStart.setMonth(prevStart.getMonth() - 3);
            prevEnd.setMonth(prevEnd.getMonth() - 3);
        } else {
            prevStart.setMonth(prevStart.getMonth() - 1);
            prevEnd.setMonth(prevEnd.getMonth() - 1);
        }

        // Calculate all category metrics
        const [
            activeCategories,
            fastestGrowingCategory,
            biggestGapCategory,
            avgCategoryGrowth,
            categoryPerformance,
            emergingCategories,
            otherEntries
        ] = await Promise.all([
            getActiveCategories(start, end),
            getFastestGrowingCategory(start, end),
            getBiggestGapCategory(start, end),
            getAvgCategoryGrowth(start, end, prevStart, prevEnd),
            getCategoryPerformance(start, end),
            getEmergingCategories(start, end),
            getOtherEntries()
        ]);

        return {
            status: true,
            message: "Category analytics retrieved successfully",
            data: {
                activeCategories: {
                    totalActive: activeCategories.totalActive,
                    addedThisQuarter: activeCategories.addedThisQuarter
                },
                fastestGrowingCategory: {
                    categoryName: fastestGrowingCategory.categoryName,
                    growthPercentage: fastestGrowingCategory.growthPercentage
                },
                biggestGapCategory: {
                    categoryName: biggestGapCategory.categoryName,
                    speakerCount: biggestGapCategory.speakerCount
                },
                avgCategoryGrowth: {
                    percentage: avgCategoryGrowth.percentage,
                    fromLastQuarter: avgCategoryGrowth.fromLastQuarter
                },
                categoryPerformance: categoryPerformance,
                emergingCategories: emergingCategories,
                otherEntries: otherEntries
            }
        };

    } catch (error) {
        console.error("Error in getCategoryAnalytics:", error);
        throw error;
    }
};

/**
 * Get active categories count and added this quarter
 */
const getActiveCategories = async (startDate, endDate) => {
    try {
        const totalActiveCategories = await Category.count({
            where: {
                deleted_at: { [Op.is]: null }
            }
        });

        const categoriesAddedThisQuarter = await Category.count({
            where: {
                created_at: { [Op.between]: [startDate, endDate] },
                deleted_at: { [Op.is]: null }
            }
        });

        return {
            totalActive: totalActiveCategories,
            addedThisQuarter: categoriesAddedThisQuarter
        };

    } catch (error) {
        console.error("Error getting active categories:", error);
        return {
            totalActive: 0,
            addedThisQuarter: 0
        };
    }
};

/**
 * Get fastest growing category - category with highest percentage increase in opportunities
 * Example: Last quarter: 100 Education events, This quarter: 101 Education events
 * Growth = (101 - 100) / 100 * 100 = +1%
 */
const getFastestGrowingCategory = async (startDate, endDate) => {
    try {
        // Get previous period for comparison (last quarter)
        const prevStart = new Date(startDate);
        const prevEnd = new Date(endDate);
        prevStart.setMonth(prevStart.getMonth() - 3);
        prevEnd.setMonth(prevEnd.getMonth() - 3);

        const fastestGrowing = await sequelize.query(`
            SELECT 
                c.name as categoryName,
                COALESCE(current_period.opportunityCount, 0) as currentOpportunities,
                COALESCE(previous_period.opportunityCount, 0) as previousOpportunities,
                CASE 
                    WHEN COALESCE(previous_period.opportunityCount, 0) > 0 
                    THEN LEAST(ROUND(((COALESCE(current_period.opportunityCount, 0) - COALESCE(previous_period.opportunityCount, 0)) / previous_period.opportunityCount) * 100, 1), 100)
                    ELSE 0 
                END as growthPercentage
            FROM categories c
            LEFT JOIN subcategories sc ON sc.category_id = c.id AND sc.is_active = '1'
            LEFT JOIN (
                SELECT 
                    sc2.category_id,
                    COUNT(DISTINCT o2.id) as opportunityCount
                FROM opportunities o2
                LEFT JOIN subcategories sc2 ON sc2.name COLLATE utf8mb4_general_ci = o2.search_query COLLATE utf8mb4_general_ci AND sc2.is_active = '1'
                WHERE o2.created_at BETWEEN :startDate AND :endDate
                AND o2.deleted_at IS NULL
                AND o2.search_query IS NOT NULL
                AND o2.search_query != ''
                AND sc2.id IS NOT NULL
                GROUP BY sc2.category_id
            ) current_period ON current_period.category_id = c.id
            LEFT JOIN (
                SELECT 
                    sc3.category_id,
                    COUNT(DISTINCT o3.id) as opportunityCount
                FROM opportunities o3
                LEFT JOIN subcategories sc3 ON sc3.name COLLATE utf8mb4_general_ci = o3.search_query COLLATE utf8mb4_general_ci AND sc3.is_active = '1'
                WHERE o3.created_at BETWEEN :prevStart AND :prevEnd
                AND o3.deleted_at IS NULL
                AND o3.search_query IS NOT NULL
                AND o3.search_query != ''
                AND sc3.id IS NOT NULL
                GROUP BY sc3.category_id
            ) previous_period ON previous_period.category_id = c.id
            WHERE c.deleted_at IS NULL
            AND current_period.opportunityCount > 0
            AND previous_period.opportunityCount > 0
            ORDER BY growthPercentage DESC
            LIMIT 1
        `, {
            replacements: { startDate, endDate, prevStart, prevEnd },
            type: sequelize.QueryTypes.SELECT
        });

        const result = fastestGrowing[0] || {
            categoryName: "No data available",
            growthPercentage: 0
        };

        return {
            categoryName: result.categoryName,
            growthPercentage: parseFloat(result.growthPercentage || 0)
        };

    } catch (error) {
        console.error("Error getting fastest growing category:", error);
        return {
            categoryName: "No data available",
            growthPercentage: 0
        };
    }
};

/**
 * Get biggest gap category - category where demand vs supply has the largest gap
 * Example: If 10 events in "Healthcare" but 30 speakers are available, gap = 20
 * Returns category name with number of speakers needed
 */
const getBiggestGapCategory = async (startDate, endDate) => {
    try {
        // Find biggest gap: categories with most speakers but least opportunities
        const biggestGap = await sequelize.query(`
            SELECT 
                c.name as categoryName,
                COUNT(DISTINCT sd.speaker_id) as speakersWithExpertise,
                COUNT(DISTINCT o.id) as availableOpportunities,
                GREATEST(COUNT(DISTINCT o.id) - COUNT(DISTINCT sd.speaker_id), 0) as speakerCount
            FROM categories c
            LEFT JOIN subcategories sc ON sc.category_id = c.id AND sc.is_active = '1'
            LEFT JOIN opportunities o ON o.search_query COLLATE utf8mb4_general_ci = sc.name COLLATE utf8mb4_general_ci
                AND o.created_at BETWEEN :startDate AND :endDate
                AND o.deleted_at IS NULL
                AND o.search_query IS NOT NULL
                AND o.search_query != ''
            LEFT JOIN speaker_details sd ON sd.value COLLATE utf8mb4_general_ci = sc.name COLLATE utf8mb4_general_ci
                AND sd.key = 'Subcategory'
                AND sd.deleted_at IS NULL
            WHERE c.deleted_at IS NULL
            GROUP BY c.id, c.name
            HAVING speakersWithExpertise > 0
            ORDER BY speakerCount DESC
            LIMIT 1
        `, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        const result = biggestGap[0];

        // console.log('Biggest Gap Debug:', {
        //     rawResult: result,
        //     totalSpeakersInDB: '9 speakers confirmed by user',
        //     calculation: result ? {
        //         category: result.categoryName,
        //         opportunitiesInCategory: result.availableOpportunities,    // Total opportunities in this category
        //         speakersInCategory: result.speakersWithExpertise,          // Speakers with expertise in this category  
        //         gapCalculation: `${result.availableOpportunities} opportunities - ${result.speakersWithExpertise} speakers = ${result.speakerCount} gap`,
        //         interpretation: `${result.speakerCount} more speakers needed for this category`
        //     } : 'No result'
        // });

        if (!result || !result.categoryName) {
            return {
                categoryName: "No gap data available",
                speakerCount: 0
            };
        }

        return {
            categoryName: result.categoryName,
            speakerCount: parseInt(result.speakerCount || 0)
        };

    } catch (error) {
        console.error("Error getting biggest gap category:", error);
        return {
            categoryName: "No gap data available",
            speakerCount: 0
        };
    }
};

/**
 * Get average category growth - aggregate growth rate across all categories
 * Example: Education grew +1%, Health declined -1%, Tech grew +2%
 * Avg Growth = (1 + (-1) + 2) / 3 = +0.67%
 * Returns average growth percentage and % from last quarter
 */
const getAvgCategoryGrowth = async (startDate, endDate, prevStartDate, prevEndDate) => {
    try {
        // Step 1: Get count of each category for CURRENT month
        const currentMonthData = await sequelize.query(`
            SELECT 
                c.id as categoryId,
                c.name as categoryName,
                COUNT(DISTINCT o.id) as opportunityCount
            FROM categories c
            LEFT JOIN subcategories sc ON sc.category_id = c.id AND sc.is_active = '1'
            LEFT JOIN opportunities o ON o.search_query COLLATE utf8mb4_general_ci = sc.name COLLATE utf8mb4_general_ci
                AND o.created_at BETWEEN :startDate AND :endDate
                AND o.deleted_at IS NULL
                AND o.search_query IS NOT NULL
                AND o.search_query != ''
            WHERE c.deleted_at IS NULL
            GROUP BY c.id, c.name
            HAVING opportunityCount > 0
        `, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        // Step 2: Get count of each category for PREVIOUS month
        const previousMonthData = await sequelize.query(`
            SELECT 
                c.id as categoryId,
                c.name as categoryName,
                COUNT(DISTINCT o.id) as opportunityCount
            FROM categories c
            LEFT JOIN subcategories sc ON sc.category_id = c.id AND sc.is_active = '1'
            LEFT JOIN opportunities o ON o.search_query COLLATE utf8mb4_general_ci = sc.name COLLATE utf8mb4_general_ci
                AND o.created_at BETWEEN :prevStartDate AND :prevEndDate
                AND o.deleted_at IS NULL
                AND o.search_query IS NOT NULL
                AND o.search_query != ''
            WHERE c.deleted_at IS NULL
            GROUP BY c.id, c.name
            HAVING opportunityCount > 0
        `, {
            replacements: { prevStartDate, prevEndDate },
            type: sequelize.QueryTypes.SELECT
        });

        // Step 3: Calculate growth for each category and find average
        const categoryGrowthRates = [];
        
        // Create maps for easy lookup
        const currentMap = {};
        currentMonthData.forEach(cat => {
            currentMap[cat.categoryId] = cat.opportunityCount;
        });
        
        const previousMap = {};
        previousMonthData.forEach(cat => {
            previousMap[cat.categoryId] = cat.opportunityCount;
        });

        // Calculate growth for each category
        [...new Set([...currentMonthData.map(c => c.categoryId), ...previousMonthData.map(c => c.categoryId)])].forEach(categoryId => {
            const currentCount = currentMap[categoryId] || 0;
            const previousCount = previousMap[categoryId] || 0;
            
            if (previousCount > 0) {
                const growthPercentage = ((currentCount - previousCount) / previousCount) * 100;
                categoryGrowthRates.push({
                    categoryId,
                    currentCount,
                    previousCount,
                    growthPercentage: Math.min(growthPercentage, 100) // Cap at 100%
                });
            }
        });

        // Step 4: Calculate average of all category growths
        const avgGrowthPercentage = categoryGrowthRates.length > 0
            ? Math.min(categoryGrowthRates.reduce((sum, cat) => sum + cat.growthPercentage, 0) / categoryGrowthRates.length, 100)
            : 0;

        // console.log('Category Growth Calculation:', {
        //     totalCategories: categoryGrowthRates.length,
        //     currentMonthCategories: currentMonthData.length,
        //     previousMonthCategories: previousMonthData.length,
        //     growthRates: categoryGrowthRates.map(cat => ({
        //         id: cat.categoryId,
        //         previous: cat.previousCount,
        //         current: cat.currentCount,
        //         growth: cat.growthPercentage.toFixed(2) + '%'
        //     })),
        //     averageGrowth: avgGrowthPercentage.toFixed(2) + '%'
        // });

        return {
            percentage: parseFloat(avgGrowthPercentage.toFixed(2)),
            fromLastQuarter: parseFloat(avgGrowthPercentage.toFixed(2))
        };

    } catch (error) {
        console.error("Error getting average category growth:", error);
        return {
            percentage: 0,
            fromLastQuarter: 0
        };
    }
};

/**
 * Get category performance - top 5 categories with opportunity and speaker count
 */
const getCategoryPerformance = async (startDate, endDate) => {
    try {
        const categoryPerformance = await sequelize.query(`
            SELECT DISTINCT
                c.name as categoryName,
                COUNT(DISTINCT o.id) as opportunityCount,
                COUNT(DISTINCT sd.speaker_id) as speakerCount
            FROM categories c
            LEFT JOIN subcategories sc ON sc.category_id = c.id AND sc.is_active = '1'
            LEFT JOIN opportunities o ON o.search_query COLLATE utf8mb4_general_ci = sc.name COLLATE utf8mb4_general_ci
                AND o.created_at BETWEEN :startDate AND :endDate
                AND o.deleted_at IS NULL
                AND o.search_query IS NOT NULL
                AND o.search_query != ''
            LEFT JOIN speaker_details sd ON sd.value COLLATE utf8mb4_general_ci = sc.name COLLATE utf8mb4_general_ci
                AND sd.key = 'Subcategory'
                AND sd.deleted_at IS NULL
            WHERE c.deleted_at IS NULL
            GROUP BY c.id, c.name
            HAVING opportunityCount > 0
            ORDER BY opportunityCount DESC, speakerCount DESC
            LIMIT 5
        `, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });
        if (categoryPerformance.length !== 0) {
            return categoryPerformance.map(category => ({
            categoryName: category.categoryName,
            opportunityCount: parseInt(category.opportunityCount),
            speakerCount: parseInt(category.speakerCount)
        }));
        }
         // recent five opportunity count and speaker count
         const recentOpportunityCountAndSpeakerCount = await sequelize.query(`
             SELECT DISTINCT
                 COALESCE(c.name, o.search_query) as categoryName,
                 COUNT(DISTINCT o.id) as opportunityCount,
                 COUNT(DISTINCT sd.speaker_id) as speakerCount
             FROM opportunities o
             LEFT JOIN subcategories sc ON sc.name COLLATE utf8mb4_general_ci = o.search_query COLLATE utf8mb4_general_ci AND sc.is_active = '1'
             LEFT JOIN categories c ON c.id = sc.category_id AND c.deleted_at IS NULL
             LEFT JOIN speaker_details sd ON sd.value COLLATE utf8mb4_general_ci = o.search_query COLLATE utf8mb4_general_ci
                 AND sd.key = 'Subcategory'
                 AND sd.deleted_at IS NULL
             WHERE o.created_at BETWEEN :startDate AND :endDate
             AND o.deleted_at IS NULL
             AND o.search_query IS NOT NULL
             AND o.search_query != ''
             GROUP BY COALESCE(c.name, o.search_query)
             HAVING opportunityCount > 0
             ORDER BY opportunityCount DESC, speakerCount DESC
             LIMIT 5
         `, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });
        return recentOpportunityCountAndSpeakerCount.map(category => ({
            categoryName: category.categoryName,
            opportunityCount: parseInt(category.opportunityCount),
            speakerCount: parseInt(category.speakerCount)
        }));

    } catch (error) {
        console.error("Error getting category performance:", error);
        return [];
    }
};

/**
 * Get emerging categories - top 5 distinct emerging categories for pie chart
 */
const getEmergingCategories = async (startDate, endDate) => {
    try {
        // Step 1: Get count of each category for CURRENT month
        const categoryData = await sequelize.query(`
            SELECT 
                c.id as categoryId,
                c.name as categoryName,
                COUNT(DISTINCT o.id) as opportunityCount
            FROM categories c
            LEFT JOIN subcategories sc ON sc.category_id = c.id AND sc.is_active = '1'
            LEFT JOIN opportunities o ON o.search_query COLLATE utf8mb4_general_ci = sc.name COLLATE utf8mb4_general_ci
                AND o.created_at BETWEEN :startDate AND :endDate
                AND o.deleted_at IS NULL
                AND o.search_query IS NOT NULL
                AND o.search_query != ''
            WHERE c.deleted_at IS NULL
            GROUP BY c.id, c.name
            HAVING opportunityCount > 0
        `, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        // Step 2: Get TOTAL count of all opportunities for current month
        const totalOpportunitiesResult = await sequelize.query(`
            SELECT COUNT(DISTINCT o.id) as totalOpportunities
            FROM opportunities o
            WHERE o.created_at BETWEEN :startDate AND :endDate
            AND o.deleted_at IS NULL
            AND o.search_query IS NOT NULL
            AND o.search_query != ''
        `, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        const totalOpportunities = totalOpportunitiesResult[0].totalOpportunities || 0;

        // Step 3: Calculate percentage for each category and sort
        const emergingCategories = categoryData
            .map(category => ({
                categoryId: category.categoryId,
                categoryName: category.categoryName,
                opportunityCount: category.opportunityCount,
                percentage: totalOpportunities > 0 
                    ? parseFloat(((category.opportunityCount / totalOpportunities) * 100).toFixed(2))
                    : 0
            }))
            .sort((a, b) => b.percentage - a.percentage) // Sort by percentage DESC
            .slice(0, 5); // Take top 5

        // Step 4: Debug logging
        // console.log('Emerging Categories Calculation:', {
        //     totalOpportunities,
        //     totalCategories: categoryData.length,
        //     topCategories: emergingCategories.length,
        //     categoryBreakdown: emergingCategories.map(cat => ({
        //         name: cat.categoryName,
        //         count: cat.opportunityCount,
        //         percentage: cat.percentage.toFixed(2) + '%'
        //     })),
        //     totalPercentage: emergingCategories.reduce((sum, cat) => sum + cat.percentage, 0).toFixed(2) + '%',
        //     remainingPercentage: (100 - emergingCategories.reduce((sum, cat) => sum + cat.percentage, 0)).toFixed(2) + '%'
        // });

        // Option 2: Return top 5 + "Others" to sum to 100%
        const result = emergingCategories.map(category => ({
            categoryName: category.categoryName,
            percentage: category.percentage
        }));

        // Add "Others" category if there are remaining categories
        const otherCategories = categoryData.slice(5); // Get categories 6+
        if (otherCategories.length > 0) {
            const othersPercentage = otherCategories.reduce((sum, cat) => {
                const percentage = totalOpportunities > 0 
                    ? parseFloat(((cat.opportunityCount / totalOpportunities) * 100).toFixed(2))
                    : 0;
                return sum + percentage;
            }, 0);

            result.push({
                categoryName: "Others",
                percentage: parseFloat(othersPercentage.toFixed(2))
            });
        }

        return result;

    } catch (error) {
        console.error("Error getting emerging categories:", error);
        return [];
    }
};

// Revenue helper functions
const calculateMRRMetrics = async (startDate, endDate) => {
    try {
        const activeSubscriptions = await getActiveSubscriptions();
        const newSubscriptions = await getNewSubscriptions(startDate, endDate);
        const churnedSubscriptions = await getChurnedSubscriptions(startDate, endDate);
        const prevChurnedSubscriptions = await getChurnedSubscriptions(
            new Date(startDate.getTime() - 90 * 24 * 60 * 60 * 1000),
            new Date(startDate.getTime() - 1)
        );

        const totalMRR = parseFloat(calculateTotalMRR(activeSubscriptions).toFixed(2));
        const newMRR = parseFloat(calculateTotalMRR(newSubscriptions).toFixed(2));
        const churnedMRR = parseFloat(calculateTotalMRR(churnedSubscriptions).toFixed(2));
        const expansionMRR = parseFloat((await calculateExpansionMRR(startDate, endDate)).toFixed(2));
        const netMRRGrowth = parseFloat((newMRR + expansionMRR - churnedMRR).toFixed(2));
        const annualRunRate = parseFloat((totalMRR * 12).toFixed(2));

        // Calculate growth rates
        const totalMRRGrowth = await calculateGrowthRate(totalMRR, startDate);
        const churnedMRRGrowth = prevChurnedSubscriptions.length > 0
            ? ((churnedMRR - calculateTotalMRR(prevChurnedSubscriptions)) / calculateTotalMRR(prevChurnedSubscriptions)) * 100
            : 0;

        return {
            totalMRR: { amount: totalMRR, growth: totalMRRGrowth },
            newMRR: { amount: newMRR },
            expansionMRR: { amount: expansionMRR },
            churnedMRR: { amount: churnedMRR, growth: churnedMRRGrowth },
            netMRRGrowth: { amount: netMRRGrowth },
            annualRunRate: { amount: annualRunRate }
        };
    } catch (error) {
        console.error("Error calculating MRR metrics:", error);
        throw error;
    }
};

const getActiveSubscriptions = async () => {
    return await Subscriptions.findAll({
        where: { status: 'active' },
        include: [{
            model: PricingPlan, as: 'plan',
            where: {
                [Op.and]: [
                    sequelize.where(sequelize.fn('LOWER', sequelize.col('plan.name')), { [Op.ne]: 'registration_fee' }),
                    { billing_interval: { [Op.ne]: 'one-time' } }
                ]
            }
        }]
    });
};

const getNewSubscriptions = async (startDate, endDate) => {
    return await Subscriptions.findAll({
        where: {
            status: 'active',
            created_at: { [Op.between]: [startDate, endDate] }
        },
        include: [{
            model: PricingPlan, as: 'plan', where: {
                [Op.and]: [
                    sequelize.where(sequelize.fn('LOWER', sequelize.col('plan.name')), { [Op.ne]: 'registration_fee' }),
                    { billing_interval: { [Op.ne]: 'one-time' } }
                ]
            }
        }]
    });
};

const getChurnedSubscriptions = async (startDate, endDate) => {
    return await Subscriptions.findAll({
        where: {
            status: 'expired',
            updated_at: { [Op.between]: [startDate, endDate] }
        },
        include: [{
            model: PricingPlan, as: 'plan', where: {
                [Op.and]: [
                    sequelize.where(sequelize.fn('LOWER', sequelize.col('plan.name')), { [Op.ne]: 'registration_fee' }),
                    { billing_interval: { [Op.ne]: 'one-time' } }
                ]
            }
        }]
    });
};

// Compute total MRR as of a specific date (subs considered active at that date)
const getTotalMRRAsOf = async (asOfDate) => {
    const subs = await Subscriptions.findAll({
        where: {
            created_at: { [Op.lte]: asOfDate },
            [Op.or]: [
                { status: { [Op.ne]: 'expired' } },
                { updated_at: { [Op.gt]: asOfDate } }
            ]
        },
        include: [{
            model: PricingPlan, as: 'plan', where: {
                [Op.and]: [
                    sequelize.where(sequelize.fn('LOWER', sequelize.col('plan.name')), { [Op.ne]: 'registration_fee' }),
                    { billing_interval: { [Op.ne]: 'one-time' } }
                ]
            }
        }]
    });
    return calculateTotalMRR(subs);
};

const calculateTotalMRR = (subscriptions) => {
    return subscriptions.reduce((total, subscription) => {
        const monthlyAmount = calculateMonthlyAmount(subscription.plan);
        return total + Number(monthlyAmount || 0);  // force number
    }, 0);
};


const calculateExpansionMRR = async (startDate, endDate) => {
    try {
        // Try to compute upgrades using previous_plan_id if schema supports it
        const expansionSubscriptions = await sequelize.query(`
            SELECT s.id, pp.amount, pp.billing_interval
            FROM subscriptions s
            INNER JOIN pricing_plans pp ON s.plan_id = pp.id
            WHERE s.status = 'active'
            AND s.updated_at BETWEEN :startDate AND :endDate
            AND s.plan_id != s.previous_plan_id
            AND s.previous_plan_id IS NOT NULL
        `, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        if (!Array.isArray(expansionSubscriptions) || expansionSubscriptions.length === 0) {
            return 0;
        }

        return calculateTotalMRR(expansionSubscriptions.map(r => ({ plan: { amount: r.amount, billing_interval: r.billing_interval } })));
    } catch (error) {
        // Fallback when previous_plan_id does not exist: return 0 to avoid wrong numbers
        if (process && process.env && process.env.DEBUG_DASHBOARD === 'true') {
            console.warn('Expansion MRR fallback to 0 due to schema limitations:', error?.message || error);
        }
        return 0;
    }
};

const calculateMonthlyAmount = (plan) => {
    if (!plan) return 0;
    const amount = Number(plan.amount || 0);
    if (plan.billing_interval === 'year') return amount / 12;
    if (plan.billing_interval === 'quarter') return amount / 3;
    return amount; // default monthly
};


const calculateGrowthRate = async (currentAmount, startDate) => {
    try {
        const prevPeriodEnd = new Date(startDate.getTime() - 1);
        const prevAmount = await getTotalMRRAsOf(prevPeriodEnd);
        if (prevAmount === 0) return currentAmount > 0 ? 100 : 0;
        return ((currentAmount - prevAmount) / prevAmount) * 100;
    } catch (error) {
        console.error("Error calculating growth rate:", error);
        return 0;
    }
};

const getSubscriptionTrendsForRevenue = async (endDate) => {
    try {
        const trends = [];
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        for (let i = 2; i >= 0; i--) {
            const monthStart = new Date(endDate.getFullYear(), endDate.getMonth() - i, 1);
            const monthEnd = new Date(endDate.getFullYear(), endDate.getMonth() - i + 1, 0, 23, 59, 59);

            const [newSub, expansion, churned] = await Promise.all([
                // New subscriptions created in the month
                Subscriptions.count({
                    where: {
                        status: 'active',
                        created_at: { [Op.between]: [monthStart, monthEnd] }
                    }
                }),
                // Plan upgrades (subscriptions that changed plans) — best-effort; if schema unsupported, return 0
                (async () => {
                    
                    try {
                        const result = await sequelize.query(`
                            SELECT COUNT(*) as count
                            FROM subscriptions s
                            WHERE s.status = 'active'
                            AND s.updated_at BETWEEN :monthStart AND :monthEnd
                            AND s.plan_id != s.previous_plan_id
                            AND s.previous_plan_id IS NOT NULL
                        `, {
                            replacements: { monthStart, monthEnd },
                            type: sequelize.QueryTypes.SELECT
                        });
                        return Number(result[0]?.count || 0);
                    } catch {
                        return 0;
                    }
                })(),
                // Churned subscriptions (expired in the month)
                Subscriptions.count({
                    where: {
                        status: 'expired',
                        updated_at: { [Op.between]: [monthStart, monthEnd] }
                    }
                })
            ]);

            trends.push({
                month: monthNames[monthStart.getMonth()],
                newSub,
                expansion: typeof expansion === 'number' ? expansion : 0,
                churned
            });
        }

        return trends;
    } catch (error) {
        console.error("Error getting subscription trends:", error);
        return [];
    }
};

const getARRByPlan = async (startDate, endDate) => {
    try {
        const arrByPlan = await sequelize.query(`
            SELECT 
                pp.name as planName,
                pp.amount as planAmount,
                pp.billing_interval,
                COUNT(DISTINCT s.id) as subscriptionCount,
                CASE 
                    WHEN pp.billing_interval = 'year' THEN pp.amount * COUNT(DISTINCT s.id)
                    ELSE pp.amount * COUNT(DISTINCT s.id) * 12
                END as arr
            FROM pricing_plans pp
            LEFT JOIN subscriptions s ON s.plan_id = pp.id 
                AND s.status = 'active'
                AND s.created_at BETWEEN :startDate AND :endDate
            WHERE pp.deleted_at IS NULL
            AND pp.name <> 'Registration_fee'
            AND pp.name <> 'registration_fee'
            AND pp.billing_interval <> 'one-time'
            GROUP BY pp.id, pp.name, pp.amount, pp.billing_interval
            ORDER BY pp.name
        `, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        const totalARR = arrByPlan.reduce((sum, plan) => sum + parseFloat(plan.arr), 0);

        return {
            plans: arrByPlan.map(plan => ({
                planName: plan.planName,
                planAmount: parseFloat(plan.planAmount),
                billingInterval: plan.billing_interval,
                subscriptionCount: parseInt(plan.subscriptionCount),
                arr: parseFloat(plan.arr)
            })),
            totalARR: parseFloat(totalARR.toFixed(2))
        };
    } catch (error) {
        console.error("Error getting ARR by plan:", error);
        return { plans: [], totalARR: 0 };
    }
};

const getPaymentFailuresRecovery = async (yearParam) => {
    try {
        const year = yearParam || new Date().getFullYear();
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        const monthlyData = [];
        let yearlyTotals = { totalRecurringPayments: 0, recoveredPayments: 0, failedPayments: 0 };

        for (let month = 0; month < 12; month++) {
            const monthStart = new Date(year, month, 1);
            const monthEnd = new Date(year, month + 1, 0, 23, 59, 59);

            // Get recurring subscriptions that should have been paid this month
            const recurringSubscriptions = await sequelize.query(`
                SELECT DISTINCT s.id, s.speaker_id, s.plan_id, pp.billing_interval, s.status, s.created_at, s.updated_at
                FROM subscriptions s
                INNER JOIN speakers sp ON sp.id = s.speaker_id
                INNER JOIN pricing_plans pp ON pp.id = s.plan_id
                WHERE pp.billing_interval IN ('month', 'year')
                AND s.status = 'active'
                AND sp.stripe_customer_id IS NOT NULL
                AND (
                    -- Monthly subscriptions: check if this month should have a payment
                    (pp.billing_interval = 'month' AND MONTH(s.created_at) = :month + 1)
                    OR
                    -- Yearly subscriptions: check if this month is the anniversary month
                    (pp.billing_interval = 'year' AND MONTH(s.created_at) = :month + 1)
                    OR
                    -- Check if there was a payment attempt this month (updated_at indicates payment activity)
                    (s.updated_at BETWEEN :monthStart AND :monthEnd)
                )
                AND pp.deleted_at IS NULL
            `, {
                replacements: { 
                    month: month, 
                    monthStart: monthStart.toISOString(), 
                    monthEnd: monthEnd.toISOString() 
                },
                type: sequelize.QueryTypes.SELECT
            });

            // Count recovered vs failed payments
            let recoveredPayments = 0;
            let failedPayments = 0;

            for (const subscription of recurringSubscriptions) {
                // Check if subscription was updated this month (indicating payment activity)
                const subscriptionUpdated = new Date(subscription.updated_at);
                const isUpdatedThisMonth = subscriptionUpdated >= monthStart && subscriptionUpdated <= monthEnd;

                if (isUpdatedThisMonth) {
                    // If subscription is still active and was updated this month, payment was recovered
                    if (subscription.status === 'active') {
                        recoveredPayments++;
                    } else if (subscription.status === 'failed' || subscription.status === 'expired') {
                        failedPayments++;
                    }
                } else {
                    // If subscription wasn't updated this month but should have been paid, it's a failure
                    // This indicates the payment was due but not processed
                    failedPayments++;
                }
            }

            const totalRecurringPayments = recurringSubscriptions.length;

            monthlyData.push({
                month: monthNames[month],
                totalRecurringPayments,
                recoveredPayments,
                failedPayments,
                recoveryRate: totalRecurringPayments > 0 ? 
                    Math.round((recoveredPayments / totalRecurringPayments) * 100) : 0
            });

            yearlyTotals.totalRecurringPayments += totalRecurringPayments;
            yearlyTotals.recoveredPayments += recoveredPayments;
            yearlyTotals.failedPayments += failedPayments;
        }

        // Calculate yearly recovery rate
        const yearlyRecoveryRate = yearlyTotals.totalRecurringPayments > 0 ? 
            Math.round((yearlyTotals.recoveredPayments / yearlyTotals.totalRecurringPayments) * 100) : 0;

        return {
            year,
            monthlyData,
            yearlyTotals: {
                ...yearlyTotals,
                recoveryRate: yearlyRecoveryRate
            }
        };

    } catch (error) {
        console.error("Error getting payment failures recovery:", error);
        return {
            year: yearParam || new Date().getFullYear(),
            monthlyData: [],
            yearlyTotals: { totalRecurringPayments: 0, recoveredPayments: 0, failedPayments: 0, recoveryRate: 0 }
        };
    }
};
const getPaymentRecoverySuccessRate = async (yearParam) => {
    try {
        const year = yearParam || new Date().getFullYear();
        const yearStart = new Date(year, 0, 1);
        const yearEnd = new Date(year, 11, 31, 23, 59, 59);

        // Count actual failed payments in the year
        const failedPayments = await Subscriptions.count({
            where: {
                status: 'failed',
                created_at: { [Op.between]: [yearStart, yearEnd] }
            }
        });

        // Best-effort recovered payments: try via status history, fallback to 0 if table missing
        let recoveredCount = 0;
        try {
            const recoveredPayments = await sequelize.query(`
                SELECT COUNT(*) as count
                FROM subscriptions s
                WHERE s.status = 'active'
                AND s.updated_at BETWEEN :yearStart AND :yearEnd
                AND EXISTS (
                    SELECT 1 FROM pricing_plans pp WHERE pp.id = s.plan_id AND pp.name <> 'Registration_fee'
                )
                AND EXISTS (
                    SELECT 1 FROM subscription_status_history ssh
                    WHERE ssh.subscription_id = s.id
                    AND ssh.previous_status = 'failed'
                    AND ssh.new_status = 'active'
                    AND ssh.changed_at BETWEEN :yearStart AND :yearEnd
                )
            `, {
                replacements: { yearStart, yearEnd },
                type: sequelize.QueryTypes.SELECT
            });
            recoveredCount = Number(recoveredPayments[0]?.count || 0);
        } catch {
            recoveredCount = 0;
        }

        if (failedPayments === 0) return 0; // avoid misleading 100%

        return Math.round((recoveredCount / failedPayments) * 100);
    } catch (error) {
        console.error("Error getting payment recovery success rate:", error);
        return 0;
    }
};



/**
 * Get recent "Other" entries from speaker_details
 */
const getOtherEntries = async () => {
    try {
        const otherEntries = await sequelize.query(`
            SELECT
                'Other Category' AS questionField,
                oc.value AS otherEntry,
                COUNT(*) AS frequency,
                COUNT(DISTINCT oc.speaker_id) AS totalUsers,
                MAX(oc.created_at) AS latestEntry,
                'pending' AS status
            FROM speaker_details oc
            INNER JOIN speaker_details cat
                ON cat.speaker_id = oc.speaker_id
                AND cat.key = 'Primary Category'
                AND cat.value = 'Other'
                AND cat.deleted_at IS NULL
            WHERE oc.key = 'Other Category'
            AND oc.deleted_at IS NULL
            GROUP BY oc.value
            HAVING frequency > 1
            ORDER BY frequency DESC, latestEntry DESC
            LIMIT 7
        `, {
            type: sequelize.QueryTypes.SELECT
        });

        // For each entry, get the actual user emails and format them
        const formattedEntries = [];

        for (const entry of otherEntries) {
            // Get recent user emails for this specific entry
            const userEmails = await sequelize.query(`
                SELECT DISTINCT s.email
                FROM speaker_details oc
                INNER JOIN speaker_details cat
                    ON cat.speaker_id = oc.speaker_id
                    AND cat.key = 'Primary Category'
                    AND cat.value = 'Other'
                    AND cat.deleted_at IS NULL
                INNER JOIN speakers s ON s.id = oc.speaker_id
                WHERE oc.key = 'Other Category'
                AND oc.value = :otherEntry
                AND oc.deleted_at IS NULL
                ORDER BY oc.created_at DESC
                LIMIT 5
            `, {
                replacements: {
                    otherEntry: entry.otherEntry
                },
                type: sequelize.QueryTypes.SELECT
            });

            const totalUsers = parseInt(entry.totalUsers);
            const emails = userEmails.map(user => user.email);

            // Format recent users as "2 user emails and +3 others"
            let recentUsersFormatted = '';
            if (totalUsers === 0) {
                recentUsersFormatted = '0 users';
            } else if (totalUsers <= 2) {
                recentUsersFormatted = emails.slice(0, totalUsers).join(', ');
            } else {
                const firstTwoEmails = emails.slice(0, 2).join(', ');
                const remainingCount = totalUsers - 2;
                recentUsersFormatted = `${firstTwoEmails} and +${remainingCount} others`;
            }

            formattedEntries.push({
                questionField: entry.questionField,
                otherEntry: entry.otherEntry,
                frequency: parseInt(entry.frequency),
                recentUsers: recentUsersFormatted,
                status: entry.status
            });
        }

        return formattedEntries;

    } catch (error) {
        console.error("Error getting other entries:", error);
        return [];
    }
};




/**
 * Get dashboard onboarding
 */
dashboardService.getDashboardOnboarding = async () => {
    try {

        const onboarding = {};
        const summary = {};

        // new speakers last 30 days
        const newSpeakers = await Speakers.count({
            where: {
                created_at: {
                    [Op.between]: [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()]
                }
            }
        });

        // Intake completion percentage: % of speakers who filled all required intake questions
        const totalSpeakers = await Speakers.count();
        const requiredQuestions = await FormQuestion.findAll({
            where: { is_required: true },
            raw: true
        });
        const requiredFieldIds = requiredQuestions.map(q => q.field_id);
        const completedSpeakers = await SpeakerDetails.findAll({
            attributes: ['speaker_id'],
            where: {
                field_id: { [Op.in]: requiredFieldIds },
                value: { [Op.ne]: null }
            },
            group: ['speaker_id'],
            having: sequelize.literal(`COUNT(DISTINCT field_id) = ${requiredFieldIds.length}`)
        });
        const intakeCompletion = totalSpeakers > 0
            ? Math.round((completedSpeakers.length / totalSpeakers) * 100)
            : 0;
        const avgQualityScore = await SpeakerOpportunity.findOne({
            attributes: [[sequelize.fn('AVG', sequelize.col('overall_score')), 'avgQualityScore']]
        })


        summary.newSpeakers = Number(newSpeakers);
        summary.profileCompletion = await getAverageSpeakerProfileCompletion();
        summary.intakeCompletion = intakeCompletion;
        summary.avgQualityScore = avgQualityScore.dataValues.avgQualityScore !== null && avgQualityScore.dataValues.avgQualityScore !== undefined
            ? Math.round(Number(avgQualityScore.dataValues.avgQualityScore) * 100) / 100
            : null;

        const [
            signupSource,
            topIncompleteFields,
            profileCompletionHeatmap,
            intakeFromComplationFlow,
            weeklyOnboardingTrends,

        ] = await Promise.all([
            getSpeakerSignupSource(),
            getTopIncompleteProfileFields(),
            getProfileCompletionHeatmap(),
            getIntakeFormCompletionFlow(),
            getWeeklyOnboardingTrends()
        ]);

        onboarding.signupSource = signupSource;
        onboarding.topIncompleteFields = topIncompleteFields;
        onboarding.profileCompletionHeatmap = profileCompletionHeatmap;
        onboarding.intakeFromComplationFlow = intakeFromComplationFlow;
        onboarding.weeklyOnboardingTrends = weeklyOnboardingTrends;
        onboarding.summary = summary;

        return {
            status: true,
            message: "Dashboard onboarding retrieved successfully",
            data: onboarding
        };
    }

    catch (error) {
        console.error("Error in getDashboardOnboarding:", error);
        throw error;
    }
}

/** 
 * Get dashboard matching analytics
 */
dashboardService.getMatchingAnalytics = async () => {
    try {

        const matchingAnalytics = {};
        const summary = await getDashboardSummary();

        // const totalOpportunities = await Opportunities.count();
        // const totalMatching = await SpeakerOpportunity.count();

        // // Get number of unique opportunities that have at least one speaker matched
        // const matchedCount = await SpeakerOpportunity.count({
        //     distinct: true,
        //     col: 'opportunity_id'
        // });

        // Calculate average matching rate
        // const avgMatchingRate = totalOpportunities > 0
        //     ? ((matchedCount / totalOpportunities) * 100).toFixed(1)
        //     : '0';


        // const afterMatch = await SpeakerOpportunity.count({
        //     where: {
        //         status: 'applied'
        //     }
        // });

        // const appliedAfterMatch = afterMatch / totalMatching * 100;

        // const [revenueMatch, lowMatchOpportunitiesCount] = await Promise.all([
        //     getRevenueByMatchedOpportunities(),
        //     getLowMatchOpportunitiesCount()
        // ]);

        // summary.avgMatchingRate = Number(avgMatchingRate);
        // summary.totalMatching = Number(appliedAfterMatch);
        // summary.revenueMatch = revenueMatch !== null && revenueMatch !== undefined
        //     ? Math.round(Number(revenueMatch) * 100) / 100
        //     : 0;
        // summary.lowMatchOpportunitiesCount = lowMatchOpportunitiesCount;
        // summary.aiVsmanualMatch = [{'ai':'30'},{'manual':'70'}];   //pending real data


        // Match Rate Per Opportunity Type

        const matchRateByOpportunityType = await getMatchRateByOpportunityType();
        // Low Match Opportunities 
        const lowMatchOpportunities = await getTopLowMatchOpportunities();
        const aiVsManualMatch = await getAIVsManualMatch();
        matchingAnalytics.summary = summary;
        matchingAnalytics.aiVsManualMatch = aiVsManualMatch;
        matchingAnalytics.matchRateByOpportunityType = matchRateByOpportunityType;
        matchingAnalytics.lowMatchOpportunities = lowMatchOpportunities;

        return {
            status: true,
            message: "Dashboard matching analytics retrieved successfully",
            data: matchingAnalytics
        };


    } catch (error) {
        console.error("Error in getMatchingAnalytics:", error);
        throw error;
    }
}
/**
 * Compute system health from scraping logs (last 30 days)
 */
async function getSystemHealthFromScraping() {
    try {
        const now = new Date();
        const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);

        const rows = await sequelize.query(`
            SELECT 
                COUNT(*) AS totalRuns,
                SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) AS successRuns,
                SUM(CASE WHEN status = 'error' OR (reason IS NOT NULL AND (reason LIKE '%critical%' OR reason LIKE '%critical error%')) THEN 1 ELSE 0 END) AS criticalErrors,
                AVG(CASE WHEN ended_at IS NOT NULL AND started_at IS NOT NULL 
                        THEN TIMESTAMPDIFF(MICROSECOND, started_at, ended_at) 
                        ELSE NULL END) AS avgMicroseconds
            FROM scraping_logging
            WHERE deleted_at IS NULL
            AND started_at BETWEEN :startDate AND :endDate
        `, {
            replacements: { startDate: thirtyDaysAgo, endDate: now },
            type: sequelize.QueryTypes.SELECT
        });

        const r = rows[0] || {};
        const totalRuns = Number(r.totalRuns || 0);
        const successRuns = Number(r.successRuns || 0);
        const criticalErrors = Number(r.criticalErrors || 0);
        const avgMicro = Number(r.avgMicroseconds || 0);

        const uptimePct = totalRuns > 0 ? (successRuns / totalRuns) * 100 : 0;
        const avgHours = avgMicro > 0 ? parseFloat((avgMicro / 1000000 / 3600).toFixed(2)) : 0;

        let perfStatus = 'Good performance';
        if (avgHours > 1) perfStatus = 'Slow';
        else if (avgHours > 0.3) perfStatus = 'Moderate';

        return {
            systemUptime: {
                percentage: parseFloat(uptimePct.toFixed(1)),
                change: 'Last 30 days'
            },
            criticalAlerts: {
                count: criticalErrors,
                status: criticalErrors > 0 ? 'Requires attention' : 'All clear'
            },
            avgResponse: {
                timeHours: avgHours,
                status: perfStatus
            }
        };
    } catch (error) {
        console.error('Error computing system health from scraping logs:', error);
        return {
            systemUptime: { percentage: 0, change: 'Last 30 days' },
            criticalAlerts: { count: 0, status: 'All clear' },
            avgResponse: { timeHours: 0, status: 'Good performance' }
        };
    }
}

/**
 * Last five months scraping alerts timeline (success/error counts per month)
 */
async function getSystemAlertsTimeline() {
    try {
        const now = new Date();
        // Compute the first day of the month 4 months ago
        const startMonth = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth() - 4, 1, 0, 0, 0, 0));
        const endMonth = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth() + 1, 0, 23, 59, 59, 999));

        // Aggregate per month between range
        const rows = await sequelize.query(`
            SELECT 
                DATE_FORMAT(CONVERT_TZ(started_at, '+00:00', '+00:00'), '%Y-%m') AS ym,
                SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) AS errorCount,
                SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) AS successCount
            FROM scraping_logging
            WHERE deleted_at IS NULL
            AND started_at BETWEEN :startDate AND :endDate
            GROUP BY ym
            ORDER BY ym ASC
        `, {
            replacements: { startDate: startMonth, endDate: endMonth },
            type: sequelize.QueryTypes.SELECT
        });

        // Build a complete last-5-months series including zeros
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const series = [];
        for (let i = 4; i >= 0; i--) {
            const d = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth() - i, 1));
            const key = `${d.getUTCFullYear()}-${String(d.getUTCMonth() + 1).padStart(2, '0')}`;
            const found = rows.find(r => r.ym === key);
            series.push({
                month: monthNames[d.getUTCMonth()],
                error: Number(found?.errorCount || 0),
                success: Number(found?.successCount || 0)
            });
        }

        return series;
    } catch (error) {
        console.error('Error computing system alerts timeline:', error);
        return [];
    }
}

/**
 * Last five months performance metrics: average scraping duration per month (in hours)
 */
async function getPerformanceMetricsTimeline() {
    try {
        const now = new Date();
        const startMonth = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth() - 4, 1, 0, 0, 0, 0));
        const endMonth = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth() + 1, 0, 23, 59, 59, 999));

        const rows = await sequelize.query(`
            SELECT 
                DATE_FORMAT(CONVERT_TZ(started_at, '+00:00', '+00:00'), '%Y-%m') AS ym,
                AVG(CASE WHEN ended_at IS NOT NULL AND started_at IS NOT NULL 
                        THEN TIMESTAMPDIFF(MICROSECOND, started_at, ended_at) 
                        ELSE NULL END) AS avgMicro
            FROM scraping_logging
            WHERE deleted_at IS NULL
            AND started_at BETWEEN :startDate AND :endDate
            GROUP BY ym
            ORDER BY ym ASC
        `, {
            replacements: { startDate: startMonth, endDate: endMonth },
            type: sequelize.QueryTypes.SELECT
        });

        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        const series = [];
        for (let i = 4; i >= 0; i--) {
            const d = new Date(Date.UTC(now.getUTCFullYear(), now.getUTCMonth() - i, 1));
            const key = `${d.getUTCFullYear()}-${String(d.getUTCMonth() + 1).padStart(2, '0')}`;
            const found = rows.find(r => r.ym === key);
            const avgMicro = Number(found?.avgMicro || 0);
            const avgHours = avgMicro > 0 ? parseFloat((avgMicro / 1000000 / 3600).toFixed(2)) : 0;
            series.push({
                month: monthNames[d.getUTCMonth()],
                avgTimeHours: avgHours
            });
        }

        return series;
    } catch (error) {
        console.error('Error computing performance metrics timeline:', error);
        return [];
    }
}


/**
 * Get health analytics
 */
dashboardService.getHealthAnalytics = async () => {
    try {
        const health = await getSystemHealthFromScraping();
        const alertsTimeline = await getSystemAlertsTimeline();
        const performanceTimeline = await getPerformanceMetricsTimeline();
        return {
            status: true,
            message: "Health analytics retrieved successfully",
            data: {
                health: health,
                alertsTimeline: alertsTimeline,
                performanceTimeline: performanceTimeline
            }
        };
    }
    catch (error) {
        console.error("Error in getHealthAnalytics:", error);
        throw error;
    }
}

/**
 * Add category
 */
dashboardService.addCategory = async (category) => {
    try {
        const categoryExists = await Category.findOne({ where: { name: category.trim() } });
        if (categoryExists) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Category already exists");
        }
        const createdCategory = await Category.create({ name: category.trim() });
        return {
            status: true,
            message: "Category added successfully",
            data: createdCategory
        };
    }
    catch (error) {
        console.error("Error in addCategory:", error);
        throw error;
    }
}

/**
 * Get feedback analytics
 */
dashboardService.getFeedbackAnalytics = async (req) => {
    try {
        // Default to current month if not provided
        const currentDate = new Date();
        const targetMonth = req.query?.month || currentDate.getMonth() + 1; // 1-12
        const targetYear = req.query?.year || currentDate.getFullYear();
        
        // Calculate date ranges
        const currentMonthStart = new Date(targetYear, targetMonth - 1, 1);
        const currentMonthEnd = new Date(targetYear, targetMonth, 0, 23, 59, 59);
        
        const lastMonth = targetMonth === 1 ? 12 : targetMonth - 1;
        const lastMonthYear = targetMonth === 1 ? targetYear - 1 : targetYear;
        const lastMonthStart = new Date(lastMonthYear, lastMonth - 1, 1);
        const lastMonthEnd = new Date(lastMonthYear, lastMonth, 0, 23, 59, 59);
        
        // Get feedback tags configuration from settings
        const feedbackTagsSetting = await Settings.findOne({ where: { key: 'feedback_tags' } });
        let feedbackTags = [];
        if (feedbackTagsSetting?.value) {
            try {
                feedbackTags = JSON.parse(feedbackTagsSetting.value);
            } catch (parseError) {
                console.error('Error parsing feedback_tags setting:', parseError);
                // Default feedback tags with ratings
                feedbackTags = [
                    { status: "Fee too low", rating: 2 },
                    { status: "Not my topic", rating: 2 },
                    { status: "Poor event quality", rating: 1 },
                    { status: "Scheduling conflict", rating: 2 },
                    { status: "Location issues", rating: 2 },
                    { status: "Unclear requirements", rating: 2 },
                    { status: "Great match", rating: 5 },
                    { status: "Needs improvement", rating: 3 }
                ];
            }
        } else {
            // Default feedback tags with ratings
            feedbackTags = [
                { status: "Fee too low", rating: 2 },
                { status: "Not my topic", rating: 2 },
                { status: "Poor event quality", rating: 1 },
                { status: "Scheduling conflict", rating: 2 },
                { status: "Location issues", rating: 2 },
                { status: "Unclear requirements", rating: 2 },
                { status: "Great match", rating: 5 },
                { status: "Needs improvement", rating: 3 }
            ];
        }
        
        // Create rating lookup map
        const ratingMap = {};
        feedbackTags.forEach(tag => {
            ratingMap[tag.status] = tag.rating;
        });
        
        // Get current month feedback data
        const currentMonthFeedback = await Feedback.findAll({
            where: {
                created_at: {
                    [Sequelize.Op.between]: [currentMonthStart, currentMonthEnd]
                }
            },
            include: [{
                model: Speakers,
                as: 'speaker',
                attributes: ['id', 'name']
            }]
        });
        
        // Get last month feedback data
        const lastMonthFeedback = await Feedback.findAll({
            where: {
                created_at: {
                    [Sequelize.Op.between]: [lastMonthStart, lastMonthEnd]
                }
            }
        });
        
        // Calculate average satisfaction for current month
        let currentMonthSatisfaction = 0;
        let currentMonthSatisfactionCount = 0;
        
        currentMonthFeedback.forEach(feedback => {
            if (feedback.tags && Array.isArray(feedback.tags)) {
                feedback.tags.forEach(tag => {
                    if (ratingMap[tag]) {
                        currentMonthSatisfaction += ratingMap[tag];
                        currentMonthSatisfactionCount++;
                    }
                });
            }
        });
        
        const avgSatisfaction = currentMonthSatisfactionCount > 0 
            ? (currentMonthSatisfaction / currentMonthSatisfactionCount).toFixed(1)
            : 0;
        
        // Calculate average satisfaction for last month
        let lastMonthSatisfaction = 0;
        let lastMonthSatisfactionCount = 0;
        
        lastMonthFeedback.forEach(feedback => {
            if (feedback.tags && Array.isArray(feedback.tags)) {
                feedback.tags.forEach(tag => {
                    if (ratingMap[tag]) {
                        lastMonthSatisfaction += ratingMap[tag];
                        lastMonthSatisfactionCount++;
                    }
                });
            }
        });
        
        const lastMonthAvgSatisfaction = lastMonthSatisfactionCount > 0 
            ? (lastMonthSatisfaction / lastMonthSatisfactionCount).toFixed(1)
            : 0;
        
        // Calculate satisfaction change
        const satisfactionChange = lastMonthAvgSatisfaction > 0 
            ? ((avgSatisfaction - lastMonthAvgSatisfaction) / lastMonthAvgSatisfaction * 100).toFixed(1)
            : 0;
        
        const satisfactionChangeText = satisfactionChange >= 0 
            ? `+${satisfactionChange}% from last month`
            : `${satisfactionChange}% from last month`;
        
        // Calculate total feedback count
        const totalFeedbackCount = currentMonthFeedback.length;
        const lastMonthFeedbackCount = lastMonthFeedback.length;
        const feedbackChange = lastMonthFeedbackCount > 0 
            ? ((totalFeedbackCount - lastMonthFeedbackCount) / lastMonthFeedbackCount * 100).toFixed(1)
            : 0;
        const feedbackChangeText = feedbackChange >= 0 
            ? `+${feedbackChange}% from last month`
            : `${feedbackChange}% from last month`;
        
        // Calculate feature requests
        const currentMonthFeatures = currentMonthFeedback.filter(f => f.type === 'feature');
        const lastMonthFeatures = lastMonthFeedback.filter(f => f.type === 'feature');
        
        const featureRequestsCount = currentMonthFeatures.length;
        const lastMonthFeatureCount = lastMonthFeatures.length;
        const featureChange = lastMonthFeatureCount > 0 
            ? ((featureRequestsCount - lastMonthFeatureCount) / lastMonthFeatureCount * 100).toFixed(1)
            : 0;
        const featureChangeText = featureChange >= 0 
            ? `+${featureChange}% from last month`
            : `${featureChange}% from last month`;
        
        // Calculate feedback distribution
        const feedbackDistribution = {
            featureRequests: featureRequestsCount,
            feedback: totalFeedbackCount - featureRequestsCount
        };
        
        // Calculate satisfaction trend for last 4 months
        const satisfactionTrend = [];
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        
        for (let i = 3; i >= 0; i--) {
            const trendMonth = targetMonth - i;
            const trendYear = trendMonth <= 0 ? targetYear - 1 : targetYear;
            const actualMonth = trendMonth <= 0 ? trendMonth + 12 : trendMonth;
            
            const trendStart = new Date(trendYear, actualMonth - 1, 1);
            const trendEnd = new Date(trendYear, actualMonth, 0, 23, 59, 59);
            
                const trendFeedback = await Feedback.findAll({
                where: {
                    created_at: {
                        [Sequelize.Op.between]: [trendStart, trendEnd]
                    }
                }
            });
            
            let trendSatisfaction = 0;
            let trendSatisfactionCount = 0;
            
            trendFeedback.forEach(feedback => {
                if (feedback.tags && Array.isArray(feedback.tags)) {
                    feedback.tags.forEach(tag => {
                        if (ratingMap[tag]) {
                            trendSatisfaction += ratingMap[tag];
                            trendSatisfactionCount++;
                        }
                    });
                }
            });
            
            const trendAvgSatisfaction = trendSatisfactionCount > 0 
                ? (trendSatisfaction / trendSatisfactionCount).toFixed(1)
                : 0;
            
            satisfactionTrend.push({
                month: monthNames[actualMonth - 1],
                satisfaction: parseFloat(trendAvgSatisfaction),
                responses: trendFeedback.length
            });
        }
        
        // Get top feature requests for current month
        // const topFeatureRequests = currentMonthFeatures.map(feature => ({
        //     feature: feature.note || 'No description',
        //     count: 1
        // }));
        
        // // Group and count feature requests
        // const featureCounts = {};
        // topFeatureRequests.forEach(item => {
        //     const key = item.feature.substring(0, 50); // Limit to 50 chars
        //     featureCounts[key] = (featureCounts[key] || 0) + 1;
        // });
        
        // const topFeatureRequestsList = Object.entries(featureCounts)
        //     .map(([feature, count]) => ({ feature, count }))
        //     .sort((a, b) => b.count - a.count)
        //     .slice(0, 5);
        
        return {
            status: true,
            message: "Feedback analytics retrieved successfully",
            data: {
                "avgSatisfaction": {
                    "score": parseFloat(avgSatisfaction),
                    "outOf": 5,
                    "change": satisfactionChangeText
                },
                "totalFeedback": {
                    "count": totalFeedbackCount,
                    "period": "This Month",
                    "change": feedbackChangeText
                },
                "featureRequests": {
                    "count": featureRequestsCount,
                    "change": featureChangeText
                },
                "feedbackDistribution": feedbackDistribution,
                "satisfactionTrend": satisfactionTrend,
                // "topFeatureRequests": topFeatureRequestsList,
                "featureRequestsDetails": currentMonthFeatures.map(feature => ({
                    note: feature.note || 'No description',
                    speakerName: feature.speaker?.name || 'Unknown Speaker',
                    createdAt: feature.created_at
                }))
            }
        };
    }
    catch (error) {
        console.error("Error in getFeedbackAnalytics:", error);
        throw error;
    }
}


/**
 * Get Affiliate analytics
 */

dashboardService.getAffiliateAnalytics = async () => {
    try {

        const affiliateAnalytics = {};
        const summary = await getAffiliateDashboardSummary();

        // const activeAffilate = await Users.count({
        //     where:{is_active :true, role_id: 2}
        // })

        // const totalClick = await AffiliateUsersDetails.sum('click_count')

        // const totalCommission = 6798; //pending real data

        // const avgConversionRate = 2.4; //pending real data

        // const [totalRevenue] = await Promise.all([ 
        //      getAffiliateTotalRevenueAndTrend()
        // ]);

        const topAffiliateByPerformance = await getTopAffiliatesByPerformance();
        const topRefferersByAffilate = await getTopReferrersByAffiliate();
        const revenueByAffiliate = await getRevenueByAffiliate();

        // summary.activeAffilate = activeAffilate;
        // summary.totalClick = totalClick || 0;
        // summary.totalCommission =  totalCommission;
        // summary.avgConversionRate = avgConversionRate;
        // summary.totalRevenue = totalRevenue

        affiliateAnalytics.summary = summary;
        affiliateAnalytics.topAffiliateByPerformance = topAffiliateByPerformance;
        affiliateAnalytics.topRefferersByAffilate = topRefferersByAffilate;
        affiliateAnalytics.revenueByAffiliate = revenueByAffiliate;

        return {
            status: true,
            message: "Affiliate analytics retrieved successfully",
            data: affiliateAnalytics
        }

    }
    catch (error) {
        console.error("Error in getAffiliateAnalytics:", error);
        throw error;
    }
}

dashboardService.getMonthlyRevenueAnalytics = async (req) => {
    try {
        const { startDate, endDate, month } = req.query;

        // Set date ranges - default to current month
        const now = new Date();
        let start, end;

        if (month) {
            // Parse month parameter (YYYY-MM format)
            const [year, monthNum] = month.split('-');
            start = new Date(year, monthNum - 1, 1);
            end = new Date(year, monthNum, 0, 23, 59, 59);
        } else if (startDate && endDate) {
            start = new Date(startDate);
            end = new Date(endDate);
        } else {
            // Default to current month
            start = new Date(now.getFullYear(), now.getMonth(), 1);
            end = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
        }

        // Calculate all revenue metrics
        const [
            newMonthlySubscriptions,
            recurringMonthlySubscriptions,
            expansionMonthlySubscriptions,
            churnedMonthlySubscriptions,
            newYearlySubscriptions,
            recurringYearlySubscriptions,
            expansionYearlySubscriptions,
            churnedYearlySubscriptions,
            newLifetimeSubscriptions,
            totalRevenue,
            subscriptionTrends,
            arrByPlan,
            paymentFailuresRecovery
        ] = await Promise.all([
            getNewMonthlySubscriptions(start, end),
            getRecurringMonthlySubscriptions(start, end),
            getExpansionMonthlySubscriptions(start, end),
            getChurnedMonthlySubscriptions(start, end),
            getNewYearlySubscriptions(start, end),
            getRecurringYearlySubscriptions(start, end),
            getExpansionYearlySubscriptions(start, end),
            getChurnedYearlySubscriptions(start, end),
            getNewLifetimeSubscriptions(start, end),
            getTotalRevenue(start, end),
            getSubscriptionTrendsForRevenue(end),
            getARRByPlan(start, end),
            getPaymentFailuresRecovery(req.query.year)
        ]);

        return {
            status: true,
            message: "Monthly revenue analytics retrieved successfully",
            data: {
                period: {
                    startDate: start.toISOString().split('T')[0],
                    endDate: end.toISOString().split('T')[0],
                    month: `${start.getFullYear()}-${String(start.getMonth() + 1).padStart(2, '0')}`
                },
                newMonthlyPlanSubscriptions: {
                    count: newMonthlySubscriptions.count,
                    revenue: newMonthlySubscriptions.revenue
                },
                recurringMonthlyPlanSubscriptions: {
                    count: recurringMonthlySubscriptions.count,
                    revenue: recurringMonthlySubscriptions.revenue
                },
                expansionMonthlyPlanSubscriptions: {
                    count: expansionMonthlySubscriptions.count,
                    revenue: expansionMonthlySubscriptions.revenue
                },
                churnedMonthlyPlanSubscriptions: {
                    count: churnedMonthlySubscriptions.count,
                    lostRevenue: churnedMonthlySubscriptions.lostRevenue
                },
                newYearlyPlanSubscriptions: {
                    count: newYearlySubscriptions.count,
                    revenue: newYearlySubscriptions.revenue
                },
                recurringYearlyPlanSubscriptions: {
                    count: recurringYearlySubscriptions.count,
                    revenue: recurringYearlySubscriptions.revenue
                },
                expansionYearlyPlanSubscriptions: {
                    count: expansionYearlySubscriptions.count,
                    revenue: expansionYearlySubscriptions.revenue
                },
                churnedYearlyPlanSubscriptions: {
                    count: churnedYearlySubscriptions.count,
                    lostRevenue: churnedYearlySubscriptions.lostRevenue
                },
                newLifetimePlanSubscriptions: {
                    count: newLifetimeSubscriptions.count,
                    revenue: newLifetimeSubscriptions.revenue
                },
                totalRevenueThisMonth: {
                    amount: totalRevenue.amount,
                    currency: 'USD'
                },
                subscriptionTrends: subscriptionTrends,
                arrByPlan: arrByPlan,
                paymentFailuresRecovery: paymentFailuresRecovery
            }
        };

    } catch (error) {
        console.error("Error in getMonthlyRevenueAnalytics:", error);
        throw error;
    }
};

/**
 * 1. NEW MONTHLY PLAN SUBSCRIPTIONS
 * Count of users who purchased/subscribed to new monthly plans this month
 * Logic: First entry of speaker in subscription table with plan_id = 1 (Foundation Plan)
 */
const getNewMonthlySubscriptions = async (startDate, endDate) => {
    try {
        const query = `
            SELECT 
                COUNT(DISTINCT s.id) as count,
                COALESCE(SUM(s.amount), 0) as revenue
            FROM subscriptions s
            INNER JOIN pricing_plans p ON p.id = s.plan_id
            WHERE s.created_at BETWEEN :startDate AND :endDate
            AND s.plan_id = 1
            AND s.status = 'active'
            AND p.deleted_at IS NULL
            AND NOT EXISTS (
                SELECT 1 FROM subscriptions prev_s
                WHERE prev_s.speaker_id = s.speaker_id
                AND prev_s.created_at < s.created_at
            )
        `;

        const result = await sequelize.query(query, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        return {
            count: parseInt(result[0].count),
            revenue: parseFloat(result[0].revenue)
        };
    } catch (error) {
        console.error("Error getting new monthly subscriptions:", error);
        throw error;
    }
};

/**
 * 2. RECURRING MONTHLY PLAN SUBSCRIPTIONS
 * Users with monthly subscriptions this month who have stripe_subscription_id in speakers table
 * Logic: Check if speaker has stripe_subscription_id (not null) and subscription is monthly plan
 */
const getRecurringMonthlySubscriptions = async (startDate, endDate) => {
    try {
        const query = `
            SELECT 
                COUNT(DISTINCT s.id) as count,
                COALESCE(SUM(s.amount), 0) as revenue
            FROM subscriptions s
            INNER JOIN pricing_plans p ON p.id = s.plan_id
            INNER JOIN speakers sp ON sp.id = s.speaker_id
            WHERE s.created_at BETWEEN :startDate AND :endDate
            AND s.plan_id = 1
            AND s.status = 'active'
            AND sp.stripe_subscription_id IS NOT NULL
            AND p.deleted_at IS NULL
        `;

        const result = await sequelize.query(query, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        return {
            count: parseInt(result[0].count),
            revenue: parseFloat(result[0].revenue)
        };
    } catch (error) {
        console.error("Error getting recurring monthly subscriptions:", error);
        throw error;
    }
};

/**
 * 3. EXPANSION MONTHLY PLAN SUBSCRIPTIONS
 * Users who upgraded from monthly to yearly/lifetime plans this month
 * Logic: Speaker exists in subscription table and upgraded to yearly (plan_id = 2) or lifetime (plan_id = 3)
 */
const getExpansionMonthlySubscriptions = async (startDate, endDate) => {
    try {
        const query = `
            SELECT 
                COUNT(DISTINCT s.id) as count,
                COALESCE(SUM(s.amount), 0) as revenue
            FROM subscriptions s
            INNER JOIN pricing_plans p ON p.id = s.plan_id
            WHERE s.created_at BETWEEN :startDate AND :endDate
            AND s.plan_id IN (2, 3)
            AND s.status = 'active'
            AND p.deleted_at IS NULL
            AND EXISTS (
                SELECT 1 FROM subscriptions prev_s
                WHERE prev_s.speaker_id = s.speaker_id
                AND prev_s.created_at < s.created_at
                AND prev_s.plan_id = 1
            )
        `;

        const result = await sequelize.query(query, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        return {
            count: parseInt(result[0].count),
            revenue: parseFloat(result[0].revenue)
        };
    } catch (error) {
        console.error("Error getting expansion monthly subscriptions:", error);
        throw error;
    }
};

/**
 * 4. CHURNED MONTHLY PLAN SUBSCRIPTIONS
 * Users who canceled monthly plans this month
 * Logic: Monthly plan subscriptions with status = 'expired' or 'failed'
 */
const getChurnedMonthlySubscriptions = async (startDate, endDate) => {
    try {
        const query = `
            SELECT 
                COUNT(DISTINCT s.id) as count,
                COALESCE(SUM(s.amount), 0) as lostRevenue
            FROM subscriptions s
            INNER JOIN pricing_plans p ON p.id = s.plan_id
            WHERE s.created_at BETWEEN :startDate AND :endDate
            AND s.plan_id = 1
            AND s.status IN ('cancelled')
            AND p.deleted_at IS NULL
        `;

        const result = await sequelize.query(query, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        return {
            count: parseInt(result[0].count),
            lostRevenue: parseFloat(result[0].lostRevenue)
        };
    } catch (error) {
        console.error("Error getting churned monthly subscriptions:", error);
        throw error;
    }
};

/**
 * 5. NEW YEARLY PLAN SUBSCRIPTIONS
 * Count of users who purchased/subscribed to new yearly plans this month
 * Logic: First entry of speaker in subscription table with plan_id = 2 (Pro Plan)
 */
const getNewYearlySubscriptions = async (startDate, endDate) => {
    try {
        const query = `
            SELECT 
                COUNT(DISTINCT s.id) as count,
                COALESCE(SUM(s.amount), 0) as revenue
            FROM subscriptions s
            INNER JOIN pricing_plans p ON p.id = s.plan_id
            WHERE s.created_at BETWEEN :startDate AND :endDate
            AND s.plan_id = 2
            AND s.status = 'active'
            AND p.deleted_at IS NULL
            AND NOT EXISTS (
                SELECT 1 FROM subscriptions prev_s
                WHERE prev_s.speaker_id = s.speaker_id
                AND prev_s.created_at < s.created_at
            )
        `;

        const result = await sequelize.query(query, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        return {
            count: parseInt(result[0].count),
            revenue: parseFloat(result[0].revenue)
        };
    } catch (error) {
        console.error("Error getting new yearly subscriptions:", error);
        throw error;
    }
};

/**
 * 6. RECURRING YEARLY PLAN SUBSCRIPTIONS
 * Users with yearly subscriptions this month who have stripe_subscription_id in speakers table
 * Logic: Check if speaker has stripe_subscription_id (not null) and subscription is yearly plan
 */
const getRecurringYearlySubscriptions = async (startDate, endDate) => {
    try {
        const query = `
            SELECT 
                COUNT(DISTINCT s.id) as count,
                COALESCE(SUM(s.amount), 0) as revenue
            FROM subscriptions s
            INNER JOIN pricing_plans p ON p.id = s.plan_id
            INNER JOIN speakers sp ON sp.id = s.speaker_id
            WHERE s.created_at BETWEEN :startDate AND :endDate
            AND s.plan_id = 2
            AND s.status = 'active'
            AND sp.stripe_subscription_id IS NOT NULL
            AND p.deleted_at IS NULL
        `;

        const result = await sequelize.query(query, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        return {
            count: parseInt(result[0].count),
            revenue: parseFloat(result[0].revenue)
        };
    } catch (error) {
        console.error("Error getting recurring yearly subscriptions:", error);
        throw error;
    }
};

/**
 * 7. EXPANSION YEARLY PLAN SUBSCRIPTIONS
 * Users who upgraded from yearly to lifetime plans this month
 * Logic: Speaker upgraded to lifetime plan (plan_id = 3) from yearly plan (plan_id = 2)
 */
const getExpansionYearlySubscriptions = async (startDate, endDate) => {
    try {
        const query = `
            SELECT 
                COUNT(DISTINCT s.id) as count,
                COALESCE(SUM(s.amount), 0) as revenue
            FROM subscriptions s
            INNER JOIN pricing_plans p ON p.id = s.plan_id
            WHERE s.created_at BETWEEN :startDate AND :endDate
            AND s.plan_id = 3
            AND s.status = 'active'
            AND p.deleted_at IS NULL
            AND EXISTS (
                SELECT 1 FROM subscriptions prev_s
                WHERE prev_s.speaker_id = s.speaker_id
                AND prev_s.created_at < s.created_at
                AND prev_s.plan_id = 2
            )
        `;

        const result = await sequelize.query(query, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        return {
            count: parseInt(result[0].count),
            revenue: parseFloat(result[0].revenue)
        };
    } catch (error) {
        console.error("Error getting expansion yearly subscriptions:", error);
        throw error;
    }
};

/**
 * 8. CHURNED YEARLY PLAN SUBSCRIPTIONS
 * Users who degraded from yearly to monthly plans this month
 * Logic: Find monthly subscriptions this month where user had previous yearly subscription
 */
const getChurnedYearlySubscriptions = async (startDate, endDate) => {
    try {
        const query = `
            SELECT 
                COUNT(DISTINCT s.id) as count,
                COALESCE(SUM(s.amount), 0) as lostRevenue
            FROM subscriptions s
            INNER JOIN pricing_plans p ON p.id = s.plan_id
            WHERE s.created_at BETWEEN :startDate AND :endDate
            AND s.plan_id = 1
            AND s.status = 'active'
            AND p.deleted_at IS NULL
            AND EXISTS (
                SELECT 1 FROM subscriptions prev_s
                WHERE prev_s.speaker_id = s.speaker_id
                AND prev_s.created_at < s.created_at
                AND prev_s.plan_id = 2
            )
        `;

        const result = await sequelize.query(query, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        return {
            count: parseInt(result[0].count),
            lostRevenue: parseFloat(result[0].lostRevenue)
        };
    } catch (error) {
        console.error("Error getting churned yearly subscriptions:", error);
        throw error;
    }
};

/**
 * 9. NEW LIFETIME PLAN SUBSCRIPTIONS
 * Count of users who purchased/subscribed to new lifetime plans this month
 * Logic: First entry of speaker in subscription table with plan_id = 3 (Lifeme Plan)
 */
const getNewLifetimeSubscriptions = async (startDate, endDate) => {
    try {
        const query = `
            SELECT 
                COUNT(DISTINCT s.id) as count,
                COALESCE(SUM(s.amount), 0) as revenue
            FROM subscriptions s
            INNER JOIN pricing_plans p ON p.id = s.plan_id
            WHERE s.created_at BETWEEN :startDate AND :endDate
            AND s.plan_id = 3
            AND s.status = 'active'
            AND p.deleted_at IS NULL
            AND NOT EXISTS (
                SELECT 1 FROM subscriptions prev_s
                WHERE prev_s.speaker_id = s.speaker_id
                AND prev_s.created_at < s.created_at
            )
        `;

        const result = await sequelize.query(query, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        return {
            count: parseInt(result[0].count),
            revenue: parseFloat(result[0].revenue)
        };
    } catch (error) {
        console.error("Error getting new lifetime subscriptions:", error);
        throw error;
    }
};

/**
 * 10. TOTAL REVENUE THIS MONTH
 * Sum of all active subscription amounts created this month
 * Logic: Sum of amount column from subscriptions table with status = 'active'
 */
const getTotalRevenue = async (startDate, endDate) => {
    try {
        const query = `
            SELECT 
                COALESCE(SUM(s.amount), 0) as amount
            FROM subscriptions s
            INNER JOIN pricing_plans p ON p.id = s.plan_id
            WHERE s.created_at BETWEEN :startDate AND :endDate
            AND s.status = 'active'
            AND p.deleted_at IS NULL
        `;

        const result = await sequelize.query(query, {
            replacements: { startDate, endDate },
            type: sequelize.QueryTypes.SELECT
        });

        return {
            amount: parseFloat(result[0].amount)
        };
    } catch (error) {
        console.error("Error getting total revenue:", error);
        throw error;
    }
};

module.exports = dashboardService;