const { RESPONSE_CODES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");
const { parsePagination, getPagingData } = require("../helpers/app-hepler");
const sequelize = require("../models/connection");
const db = require("../models");
const speakerHistoryService = {};

// ------------------------- Speaker History Service -------------------------

/**
* Get points history for an authenticated speaker with filters and pagination
* Retrieves the complete points activity history including point changes and running balance
* 
* @param {Object} getReq - The request object containing query parameters and speaker info
* @param {Object} getReq.query - Query parameters for filtering and pagination
* @param {number} [getReq.query.page=1] - Page number for pagination
* @param {number} [getReq.query.limit=10] - Items per page for pagination
* @param {string} [getReq.query.type] - Filter by history type (opportunity, subscription, gamification)
* @param {string} [getReq.query.start_date] - Filter by start date (YYYY-MM-DD format)
* @param {string} [getReq.query.end_date] - Filter by end date (YYYY-MM-DD format)
* @param {string} [getReq.query.search] - Search term for description
* @param {number} getReq.speakerId - Authenticated speaker's ID
* @returns {Promise<Object>} Paginated points history with summary statistics
* @throws {CustomError} When speaker ID is missing or database operation fails
*/
speakerHistoryService.getSpeakerPointsHistory = async (getReq) => {
    try {
        // Extract speaker ID from authenticated session
        const speakerId = getReq.speakerId;

        if (!speakerId) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Speaker ID is required");
        }

        // Parse pagination parameters with defaults
        const query = getReq.query || {};
        const { page = 1, limit = 10, offset } = parsePagination({
            page: query.page || 1,
            limit: query.limit || 10
        });

        // Extract filter parameters
        const { type, start_date, end_date } = query;

        // Build the SQL query to join gamification_history with gamification_rules
        let whereClause = `gh.speaker_id = ? AND gh.deleted_at IS NULL`;
        const whereParams = [speakerId];

        // Add type filter if provided (credit or redemption)
        if (type && ['credit', 'redemption'].includes(type)) {
            whereClause += ` AND gh.type = ?`;
            whereParams.push(type);
        }

        // Add date range filters if provided
        if (start_date && end_date) {
            whereClause += ` AND DATE(gh.created_at) BETWEEN ? AND ?`;
            whereParams.push(start_date, end_date);
        } else if (start_date) {
            whereClause += ` AND DATE(gh.created_at) >= ?`;
            whereParams.push(start_date);
        } else if (end_date) {
            whereClause += ` AND DATE(gh.created_at) <= ?`;
            whereParams.push(end_date);
        }

        // Count query for pagination
        const countQuery = `
            SELECT COUNT(*) as total
            FROM gamification_history gh
            LEFT JOIN gamification_rules gr ON gh.gamification_id = gr.id
            WHERE ${whereClause}
        `;

        const countResult = await sequelize.query(countQuery, {
            replacements: whereParams,
            type: sequelize.QueryTypes.SELECT
        });

        const totalCount = countResult[0].total;

        // First, get all records for balance calculation (ordered by created_at ASC)
        const allRecordsQuery = `
            SELECT 
                gh.id,
                gh.points,
                gh.type,
                gh.created_at,
                COALESCE(gr.description, 'Unknown Activity') as activity_description
            FROM gamification_history gh
            LEFT JOIN gamification_rules gr ON gh.gamification_id = gr.id
            WHERE gh.speaker_id = ? AND gh.deleted_at IS NULL
            ORDER BY gh.created_at ASC
        `;

        const allRecords = await sequelize.query(allRecordsQuery, {
            replacements: [speakerId],
            type: sequelize.QueryTypes.SELECT
        });

        // Calculate running balance for all records
        let balance = 0;
        const recordsWithBalance = allRecords.map(record => {
            if (record.type === 'credit') {
                balance += record.points;
            } else if (record.type === 'redemption') {
                balance -= record.points;
            }

            return {
                ...record,
                running_balance: balance
            };
        });

        // Now get the paginated records with filters applied
        const historyQuery = `
            SELECT 
                gh.id,
                gh.points,
                gh.type,
                gh.created_at,
                COALESCE(gr.description, 'Unknown Activity') as activity_description
            FROM gamification_history gh
            LEFT JOIN gamification_rules gr ON gh.gamification_id = gr.id
            WHERE ${whereClause}
            ORDER BY gh.created_at DESC
            LIMIT ? OFFSET ?
        `;

        const paginatedParams = [...whereParams, parseInt(limit), parseInt(offset)];
        const paginatedRecords = await sequelize.query(historyQuery, {
            replacements: paginatedParams,
            type: sequelize.QueryTypes.SELECT
        });

        // Map the paginated records to include balance from our calculation
        const formattedHistory = paginatedRecords.map(record => {
            // Find the corresponding record with balance calculation
            const recordWithBalance = recordsWithBalance.find(r => r.id === record.id);

            return {
                id: record.id,
                date: record.created_at,
                activity: record.activity_description,
                type: record.type,
                // points: record.type === 'credit' ? `+${record.points}` : `-${record.points}`,
                points: record.type === 'credit' ? record.points : -record.points,
                balance: recordWithBalance ? recordWithBalance.running_balance : 0,
                is_positive: record.type === 'credit'
            };
        });

        // Generate pagination data
        const pageData = getPagingData(totalCount, parseInt(limit), parseInt(page));

        // Calculate summary statistics
        // const totalEarned = allRecords
        //     .filter(r => r.type === 'credit')
        //     .reduce((sum, r) => sum + r.points, 0);

        // const totalSpent = allRecords
        //     .filter(r => r.type === 'redemption')
        //     .reduce((sum, r) => sum + r.points, 0);

        const currentBalance = recordsWithBalance.length > 0 ?
            recordsWithBalance[recordsWithBalance.length - 1].running_balance : 0;

        return {
            status: true,
            message: `Found ${paginatedRecords.length} history records for speaker`,
            data: {
                speaker_id: parseInt(speakerId),
                history: formattedHistory,
                summary: {
                    current_balance: currentBalance,
                    // total_earned: totalEarned,
                    // total_spent: totalSpent,
                    // net_points: currentBalance,
                    // total_transactions: totalCount
                },
                pageData
            }
        };

    } catch (error) {
        console.error("Error in getSpeakerPointsHistory:", error);
        throw error;
    }
};

speakerHistoryService.callEventPoints = async (speaker_id, event) => {
    try {
        console.log(`Awarding points for event: ${event} to speaker ID: ${speaker_id}`);
        switch (event) {
            case 'daily_login':
                return await speakerHistoryService.handleDailyLogin(speaker_id);

            case 'bio_update':
            case 'add_testimonial':
            case 'video_upload':
            case 'topic_update':
            case 'upload_photo':
            case 'optional_questions':
            case 'complete_intake_form':
            case 'headshot_update':
            case 'affiliate_meeting':
            case 'applied_oppurtunity':
                
            case 'booked_opportunity':
                return await speakerHistoryService.handleFormUpdate(speaker_id, event);

            case 'referral_points':
                return await speakerHistoryService.referralPoints(speaker_id, event);

            case 'customer_retention':
                return await speakerHistoryService.customerRetention(speaker_id, event);

            default:
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid event type");
        }
    } catch (error) {
        console.error("Error awarding event points:", error);
        throw error;
    }
};


speakerHistoryService.handleDailyLogin = async (speaker_id) => {
    try {
        const { status, message, data } = await speakerHistoryService.updateStreak(speaker_id);
        if (status) {
            const streakMap = {
                5: '5_day_login',
                30: '30_day_login',
                365: 'annual_login',
            };
            const key = data ? (streakMap[data.streak_count] || 'daily_login') : 'daily_login';
            const eventRule = await db.GamificationRules.findOne({
                where: { key }
            });

            if (!eventRule) {
                throw new Error(`No rule found for event type: ${key}. Please add it to gamification_points table.`);
            }

            const rule = {
                points: eventRule.points,
                type: eventRule.type,
                gamification_id: eventRule.id
            };

            const result = await speakerHistoryService.awardPointsWithRule(speaker_id, key, rule);
            return result;
        }
        return { status: false, message: "Failed to update streak", data: null };
    } catch (error) {
        console.error("Error handling daily login:", error);
        throw error;
    }
};

speakerHistoryService.updateStreak = async (speaker_id) => {
    try {
        const today = new Date();
        const todayDateOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

        // Use existing streak if provided, otherwise fetch it
        let streak = await db.Streaks.findOne({ where: { speaker_id } });

        if (!streak) {
            // Create new streak record
            streak = await db.Streaks.create({
                speaker_id,
                count: 1,
                last_login: today
            });
            return { status: true, message: "New streak created", data: { streak_count: streak.count } };
        } else {
            // Check if user already logged in today
            const lastLoginDate = new Date(streak.last_login);
            const lastLoginDateOnly = new Date(lastLoginDate.getFullYear(), lastLoginDate.getMonth(), lastLoginDate.getDate());

            // If already logged in today, don't update streak
            if (lastLoginDateOnly.getTime() === todayDateOnly.getTime()) {
                return { status: false, message: "Already logged in today", data: { streak_count: streak.count } };
            }

            // Check if yesterday was the last login (consecutive day)
            const yesterday = new Date(todayDateOnly);
            yesterday.setDate(yesterday.getDate() - 1);

            if (lastLoginDateOnly.getTime() === yesterday.getTime()) {
                // Consecutive day - increment streak count by +1
                streak.count += 1;
            } else {
                // Not consecutive - reset streak to 1 (current day)
                streak.count = 1;
            }

            // Update last login date
            streak.last_login = today;
            await streak.save();
        }

        return { status: true, message: "Streak updated", data: { streak_count: streak.count } };

    } catch (error) {
        console.error("Error updating streak:", error);
        throw error;
    }
};


speakerHistoryService.awardPointsWithRule = async (speaker_id, key, rule) => {
    try {
        const points = rule.points;

        if (!points || points <= 0) {
            return {
                status: false,
                message: "Invalid points value",
                data: null
            };
        }

        // Create gamification data record
        const GamificationHistory = await db.GamificationHistory.create({
            speaker_id,
            key,
            points,
            gamification_id: rule.gamification_id || null
        });

        return {
            status: true,
            message: "Points awarded successfully",
            data: {
                id: GamificationHistory.id,
                speaker_id,
                key,
                points: GamificationHistory.points,
                created_at: GamificationHistory.created_at
            }
        };

    } catch (error) {
        console.error("Error awarding points with rule:", error);
        throw error;
    }
};


speakerHistoryService.checkEventLimit = async (speaker_id, event, limit_period, limit_count) => {
    try {
        console.log(`Checking event limit for event: ${event}, speaker ID: ${speaker_id}, period: ${limit_period}, count: ${limit_count}`);

        if(!limit_count){
            return true; // No limit set
        }
        const whereCondition = {
            speaker_id,
            key: event
        };

        const now = new Date();

        // Helper to calculate date ranges
        const getDateRange = (period) => {
            let startDate, endDate;

            switch (period) {
                case 7: {
                    // Current week: Monday → Sunday
                    const dayOfWeek = now.getDay();
                    startDate = new Date(now);
                    startDate.setDate(now.getDate() - ((dayOfWeek + 6) % 7));
                    startDate.setHours(0, 0, 0, 0);

                    endDate = new Date(startDate);
                    endDate.setDate(startDate.getDate() + 6);
                    endDate.setHours(23, 59, 59, 999);
                    break;
                }
                case 30: {
                    // Current month
                    startDate = new Date(now.getFullYear(), now.getMonth(), 1);
                    endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
                    break;
                }
                case 90: {
                    // Current quarter
                    const quarter = Math.floor(now.getMonth() / 3);
                    const startMonth = quarter * 3;
                    const endMonth = startMonth + 2;

                    startDate = new Date(now.getFullYear(), startMonth, 1);
                    endDate = new Date(now.getFullYear(), endMonth + 1, 0, 23, 59, 59, 999);
                    break;
                }
                default:
                    return null;
            }

            return { startDate, endDate };
        };

        const range = getDateRange(limit_period);
        if (range) {
            whereCondition.created_at = {
                [db.Sequelize.Op.gte]: range.startDate,
                [db.Sequelize.Op.lte]: range.endDate
            };
        }

        const count = await db.GamificationHistory.count({
            where: whereCondition
        });
        console.log(`Event "${event}" count for speaker ID ${speaker_id} in period ${limit_period} days: ${count}/${limit_count}`); 

        return count < limit_count;
    } catch (error) {
        console.error("Error checking event limit:", error);
        throw error;
    }
};


speakerHistoryService.handleFormUpdate = async (speaker_id, event) => {
    try {
        console.log(speaker_id, event, "handleform update")
        // Fetch gamification rule for update
        const rule = await db.GamificationRules.findOne({
            where: { key: event }
        });

        if (!rule) {
            throw new CustomError(`No rule found for event type: ${event}.`);
        }

        const limit = await speakerHistoryService.checkEventLimit(speaker_id, event, rule?.limit_period, rule.limit_count);
        if (!limit) {
            console.log(`Limit reached for event "${event}" for speaker ID ${speaker_id}`);
            return { status: false, message: "Update points limit reached for the period", data: null };
        }

        // Award points
        const award = await speakerHistoryService.awardPointsWithRule(speaker_id, event, {
            points: rule.points,
            gamification_id: rule.id,
            type: event === 'affiliate_meeting' ? 'redemption' : 'earn'
        });
        return { status: award.status, message: award.message, data: award.data };

    } catch (error) {
        console.error("Error handling form update:", error);
        throw error;
    }
};

speakerHistoryService.referralPoints = async (speaker_id, event) => {
    try {
        const count = await db.GamificationHistory.count({
            where: { speaker_id, key: event }
        });

        let key;
        if (count == 10) {
            key = '10_referral_points';
        } else if (count == 5) {
            key = '5_referral_points';
        } else {
            key = 'referral_points';
        }

        // Award points
        const award = await speakerHistoryService.awardPointsWithRule(speaker_id, key, {
            points: rule.points,
            gamification_id: rule.id,
            type: 'earn'
        });
        return { status: award.status, message: award.message, data: award.data };

    } catch (error) {
        console.error("Error handling referral points:", error);
        throw error;
    }
}


speakerHistoryService.customerRetention = async (speaker_id, event) => {
    try {
        const eventRule = await db.GamificationRules.findOne({ where: { key: event } });
        if (!eventRule) {
            throw new Error(`No rule found for event type: ${event}. Please add it to gamification_points table.`);
        }

        // Award points
        await speakerHistoryService.awardPointsWithRule(speaker_id, event, {
            points: eventRule.points,
            type: "credit",
            gamification_id: eventRule.id,
        });

        return { status: true, message: "Customer Retention points awarded." };

    } catch (error) {
        console.error("Error handling subscription event:", error);
        throw error;
    }
};



module.exports = speakerHistoryService;