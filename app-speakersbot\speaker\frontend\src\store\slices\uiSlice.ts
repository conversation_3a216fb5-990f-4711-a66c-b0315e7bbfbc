import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface UIState {
  sidebarOpen: boolean;
  mobileMenuOpen: boolean;
  notifications: {
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
    timestamp: number;
    read: boolean;
  }[];
  modals: {
    loginModal: boolean;
    profileModal: boolean;
    opportunityModal: boolean;
    settingsModal: boolean;
  };
  loading: {
    global: boolean;
    auth: boolean;
    opportunities: boolean;
    profile: boolean;
  };
  errors: {
    global: string | null;
    auth: string | null;
    opportunities: string | null;
    profile: string | null;
  };
}

const initialState: UIState = {
  sidebarOpen: true,
  mobileMenuOpen: false,
  notifications: [],
  modals: {
    loginModal: false,
    profileModal: false,
    opportunityModal: false,
    settingsModal: false,
  },
  loading: {
    global: false,
    auth: false,
    opportunities: false,
    profile: false,
  },
  errors: {
    global: null,
    auth: null,
    opportunities: null,
    profile: null,
  },
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Sidebar actions
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },
    
    // Mobile menu actions
    toggleMobileMenu: (state) => {
      state.mobileMenuOpen = !state.mobileMenuOpen;
    },
    
    setMobileMenuOpen: (state, action: PayloadAction<boolean>) => {
      state.mobileMenuOpen = action.payload;
    },
    
    // Notification actions
    addNotification: (state, action: PayloadAction<Omit<UIState['notifications'][0], 'id' | 'timestamp' | 'read'>>) => {
      const notification = {
        ...action.payload,
        id: Date.now().toString(),
        timestamp: Date.now(),
        read: false,
      };
      state.notifications.unshift(notification);
      
      // Keep only last 50 notifications
      if (state.notifications.length > 50) {
        state.notifications = state.notifications.slice(0, 50);
      }
    },
    
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    
    markNotificationAsRead: (state, action: PayloadAction<string>) => {
      const notification = state.notifications.find(n => n.id === action.payload);
      if (notification) {
        notification.read = true;
      }
    },
    
    markAllNotificationsAsRead: (state) => {
      state.notifications.forEach(notification => {
        notification.read = true;
      });
    },
    
    clearNotifications: (state) => {
      state.notifications = [];
    },
    
    // Modal actions
    setModalOpen: (state, action: PayloadAction<{ modal: keyof UIState['modals']; open: boolean }>) => {
      const { modal, open } = action.payload;
      state.modals[modal] = open;
    },
    
    closeAllModals: (state) => {
      Object.keys(state.modals).forEach(modal => {
        state.modals[modal as keyof UIState['modals']] = false;
      });
    },
    
    // Loading actions
    setLoading: (state, action: PayloadAction<{ key: keyof UIState['loading']; loading: boolean }>) => {
      const { key, loading } = action.payload;
      state.loading[key] = loading;
    },
    
    setGlobalLoading: (state, action: PayloadAction<boolean>) => {
      state.loading.global = action.payload;
    },
    
    // Error actions
    setError: (state, action: PayloadAction<{ key: keyof UIState['errors']; error: string | null }>) => {
      const { key, error } = action.payload;
      state.errors[key] = error;
    },
    
    clearError: (state, action: PayloadAction<keyof UIState['errors']>) => {
      state.errors[action.payload] = null;
    },
    
    clearAllErrors: (state) => {
      Object.keys(state.errors).forEach(key => {
        state.errors[key as keyof UIState['errors']] = null;
      });
    },
  },
});

export const {
  toggleSidebar,
  setSidebarOpen,
  toggleMobileMenu,
  setMobileMenuOpen,
  addNotification,
  removeNotification,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  clearNotifications,
  setModalOpen,
  closeAllModals,
  setLoading,
  setGlobalLoading,
  setError,
  clearError,
  clearAllErrors,
} = uiSlice.actions;

export default uiSlice.reducer;
