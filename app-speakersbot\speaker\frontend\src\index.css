@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* SpeakerBot Chrome Extension Design System - Dark Theme with Maroon/Plum Accents */

@layer base {
  :root {
    /* Dark Theme Only - Core Background Colors */
    --border-light: 220 14% 95%; 
    --background: 0 0% 9%; /* #171717 - Deep charcoal background */
    --surface: 240 3% 11%; /* #1C1C1E - Dark card surfaces */
    --surface-elevated: 0 0% 14%; /* #252525 - Elevated surfaces */
    --sidebar: 240 3% 11%; /* #1C1C1E - Dark card surfaces */

    /* Text Colors */
    --foreground: 0 0% 100%; /* #FFFFFF - White text */
    --foreground-muted: 215 16% 47%; /* #b3b3b3 - Muted gray text */
    --foreground-subtle: 0 0% 50%; /* #808080 - Subtle gray text */
    --primary-text: 220 3% 82%; /* #CFD0D2 */
    
    /* Primary Accent Colors */
    --primary: 250 70% 55%; /* Vibrant blue accent */
    --primary-hover: 250 70% 65%; /* Lighter blue on hover */
    --primary-active: 250 70% 65%; /* Darker blue when active */
    --primary-foreground: 0 0% 100%; /* White text on blue */
    
    /* Secondary Colors */
    --secondary: 0 0% 16%; /* Match surface color */
    --secondary-foreground: 0 0% 100%;

    /* Tertiary Colors */
    --tertiary-foreground: 0, 0%, 0%;

    /* Accent Colors */
    --accent-blue: 213 94% 68%; /* Vibrant blue accent */
    --accent-purple: 280 60% 60%; /* Purple accent option */
    
    /* Status Colors */
    --success: 142 76% 36%;
    --warning: 38 92% 50%;
    --destructive: 0 84% 60%;
    
    /* UI Elements */
    --border: 0 0% 25%; /* #404040 - Subtle borders */
    --border-subtle: 0 0% 20%; /* #333333 - Very subtle borders */
    --input: 0 0% 16%; /* Same as surface */
    --card: 0 0% 9%; /* Much darker card background for visibility */
    --muted: 0 0% 18%; /* #2e2e2e - Muted backgrounds */
    --accent: 0 0% 16%;
    --ring: 213 94% 68%; /* Blue focus rings */
    --input-background: 213 14% 15%;
    --input-border: 210 16% 20%;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, hsl(213 94% 68%), hsl(220 91% 65%));
    --gradient-brand: linear-gradient(135deg, hsl(213 94% 68%), hsl(207 87% 61%));
    --gradient-surface: linear-gradient(180deg, hsl(0 0% 20%), hsl(0 0% 16%));
    --gradient-glow: linear-gradient(135deg, hsl(213 94% 68% / 0.1), hsl(220 91% 65% / 0.1));
    --gradient-tertiary: linear-gradient(251.57deg, #7A7B7F 8.24%, #B9BABE 36.29%, #FCFCFE 73.15%, #808185 91.58%);
    --gradient-background: radial-gradient(50% 50% at 50% 50%, rgba(0, 0, 0, 0.33) 0%, rgba(0, 0, 0, 0.65) 100%);
    --gradient-background-overlay: linear-gradient(180deg, rgba(0, 0, 0, 0) 0%, #000000 77.18%);
    --button-gradient: linear-gradient(114.86deg, #954DDF 16.58%, #31147E 88.14%);
    --border-gradient: linear-gradient(114.84deg, rgba(142, 143, 147, 0.5) 0%, rgba(245, 245, 247, 0.5) 31.17%, rgba(199, 200, 204, 0.5) 65.27%, rgba(141, 142, 146, 0.5) 101.32%);

    /* Shadows */
    --shadow-glow: 0 0 30px hsl(213 94% 68% / 0.3);
    --shadow-card: 0 8px 40px hsl(0 0% 0% / 0.5);
    --shadow-elevated: 0 20px 60px hsl(0 0% 0% / 0.7);
    --price-shadow: -5px -2px 12.6px 0px hsl(0 0% 0% / 1);
    --card-secondary: -11px 19px 13px 0px hsla(0, 0%, 0%, 1);
    
    /* Animation */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
    
    --radius: 16px; /* More curved like the reference */
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  /* Metallic silver shine effect */
  .metallic-shine {
    background: linear-gradient(135deg, 
      hsl(var(--silver-dark)), 
      hsl(var(--silver)), 
      hsl(var(--silver-glow)), 
      hsl(var(--silver))
    );
    background-size: 200% 200%;
    animation: shimmer 3s ease-in-out infinite;
  }
  
  @keyframes shimmer {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }
  
  /* Extension popup styling */
  .popup-container {
    width: 380px;
    max-height: 600px;
    overflow-y: auto;
  }
  
  /* Custom scrollbar */
  .popup-container::-webkit-scrollbar {
    width: 4px;
  }
  
  .popup-container::-webkit-scrollbar-track {
    background: hsl(var(--surface));
  }
  
  .popup-container::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
    border-radius: 2px;
  }
  
  .popup-container::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--primary));
  }
}