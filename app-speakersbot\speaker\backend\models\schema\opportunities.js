const { DataTypes } = require("sequelize");
const connection = require("../connection");


const opportunities = connection.define("opportunities", {


    title: {
        type: DataTypes.TEXT,
        // allowNull: false,
        comment: 'Title of the opportunity/event'
    },
    organization: {
        type: DataTypes.STRING,
        comment: 'Name of the organizing entity'
    },
    event_type: {
        type: DataTypes.STRING,
        comment: 'Type of the event (e.g., conference, webinar)'
    },
    description: {
        type: DataTypes.TEXT,
        comment: 'Description of the opportunity/event'
    },
    start_date: {
        type: DataTypes.DATEONLY,
        comment: 'Event start date'
    },
    end_date: {
        type: DataTypes.DATEONLY,
        comment: 'Event end date'
    },

    event_url: {
        type: DataTypes.TEXT,
        comment: 'URL to the event page'
    },
    source_url: {
        type: DataTypes.TEXT,
        comment: 'Source URL where the opportunity was found'
    },

    city: {
        type: DataTypes.STRING,
        comment: 'City where the event is held'
    },
    state: {
        type: DataTypes.STRING,
        comment: 'State where the event is held'
    },
    country: {
        type: DataTypes.STRING,
        comment: 'Country where the event is held'
    },
    venue: {
        type: DataTypes.TEXT,
        comment: 'Venue of the event'
    },
    industry: {
        type: DataTypes.TEXT,
        comment: 'Industry related to the opportunity'
    },
    search_query: {
        type: DataTypes.TEXT,
        comment: 'Search query used to find the opportunity'
    },
    is_virtual: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'Indicates if the event is virtual'
    },
    speaker_earning: {
        type: DataTypes.DECIMAL(10, 2),
        comment: 'Earning potential or compensation details'
    },
    is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: false,
        comment: 'Indicates if the opportunity is active'
    },
    feed_back: {
        type: DataTypes.TEXT,
        comment: 'Feedback or notes about the opportunity'
    },
    source: {
        type: DataTypes.STRING,
        comment: 'Source of the opportunity'
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
        comment: 'Record creation timestamp',
    },
    tag: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'Tag or category for the opportunity',
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
        comment: 'Record last update timestamp',
    },
    deleted_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Record deletion timestamp(soft delete)',
    }

}, {

    tableName: 'opportunities',
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    paranoid: true,
    deletedAt: "deleted_at",

});

module.exports = opportunities;