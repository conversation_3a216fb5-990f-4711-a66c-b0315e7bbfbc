import React, { useState } from 'react';
import { Tabs, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Home, FileText, User, Calendar, Bell, Settings } from 'lucide-react';
import { HomeTab } from './tabs/HomeTab';
import { IntakeTab } from './tabs/IntakeTab';
import { ProfileTab } from './tabs/ProfileTab';
import { OpportunitiesTab } from './tabs/OpportunitiesTab';
import { NotificationsTab } from './tabs/NotificationsTab';
import { SettingsTab } from './tabs/SettingsTab';

export function ExtensionApp() {
  const [activeTab, setActiveTab] = useState('home');

  return (
    <div className="h-full flex flex-col bg-background text-foreground">
      {/* Header */}
      <header className="flex items-center justify-between p-4 border-b border-border bg-surface">
        <div className="flex items-center gap-2">
          <div className="w-8 h-8 rounded-lg bg-gradient-primary flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-sm">SB</span>
          </div>
          <h1 className="font-semibold text-foreground">SpeakerBot</h1>
        </div>
      </header>

      {/* Navigation Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
        <TabsList className="grid grid-cols-6 mx-4 mt-4 bg-surface-elevated">
          <TabsTrigger value="home" className="flex flex-col items-center gap-1 py-2">
            <Home className="h-4 w-4" />
            <span className="text-xs">Home</span>
          </TabsTrigger>
          <TabsTrigger value="intake" className="flex flex-col items-center gap-1 py-2">
            <FileText className="h-4 w-4" />
            <span className="text-xs">Intake</span>
          </TabsTrigger>
          <TabsTrigger value="profile" className="flex flex-col items-center gap-1 py-2">
            <User className="h-4 w-4" />
            <span className="text-xs">Profile</span>
          </TabsTrigger>
          <TabsTrigger value="opportunities" className="flex flex-col items-center gap-1 py-2">
            <Calendar className="h-4 w-4" />
            <span className="text-xs">Ops</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex flex-col items-center gap-1 py-2">
            <Bell className="h-4 w-4" />
            <span className="text-xs">Alerts</span>
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex flex-col items-center gap-1 py-2">
            <Settings className="h-4 w-4" />
            <span className="text-xs">Settings</span>
          </TabsTrigger>
        </TabsList>

        {/* Tab Content */}
        <div className="flex-1 overflow-y-auto">
          <TabsContent value="home" className="m-0 p-4">
            <HomeTab />
          </TabsContent>
          <TabsContent value="intake" className="m-0 p-4">
            <IntakeTab />
          </TabsContent>
          <TabsContent value="profile" className="m-0 p-4">
            <ProfileTab />
          </TabsContent>
          <TabsContent value="opportunities" className="m-0 p-4">
            <OpportunitiesTab />
          </TabsContent>
          <TabsContent value="notifications" className="m-0 p-4">
            <NotificationsTab />
          </TabsContent>
          <TabsContent value="settings" className="m-0 p-4">
            <SettingsTab />
          </TabsContent>
        </div>
      </Tabs>
    </div>
  );
}