const {
  parsePagination,
  getPagingData,
  buildSearchWhere<PERSON>lause,
  buildFilterWhereClause,
  parseJSONSafely,
} = require("../helpers/app-hepler");
const { Users, Roles } = require("../models");
const { Op } = require("sequelize");

const affiliateService = {};

// ------------------------- affiliate-service -------------------------

/**
 * Get affiliate names with optional pagination
 * Retrieves affiliate user names with pagination support
 *
 * @param {Object} getReq - The request object containing query parameters
 * @param {Object} getReq.query - Query parameters for pagination
 * @param {number} [getReq.query.page] - Page number (defaults to 1)
 * @param {number} [getReq.query.limit] - Items per page (defaults to 10)
 * @returns {Promise<Object>} Affiliate names with pagination info
 * @throws {CustomError} When database operation fails
 */
affiliateService.getAffiliateNames = async (getReq) => {
  try {
    // Check if pagination parameters are provided

    const pagination = parsePagination(getReq.query);
    page = pagination.page;
    limit = pagination.limit;
    offset = pagination.offset;
    // Find affiliate role (fixing typo: "Affileate" should be "Affiliate")
    const role = await Roles.findOne({ where: { id: { [Op.in]: [2, 4] } } });
    // If role not found, try the typo version for backward compatibility
    const roleId = role ? role.id : 2; // Fallback to role_id 2 if not found
    // Build where clause for affiliate users
   const whereClause = { role_id: { [Op.in]: [2, 4] }, is_active: true };

    // Get total count for pagination
    const totalCount = await Users.count({
      where: whereClause,
      distinct: true,
    });
    // Get affiliates with pagination
    const queryOptions = {
      where: whereClause,
      attributes: ["id", "name","role_id"],
      order: [["name", "ASC"]],
    };

    queryOptions.limit = limit;
    queryOptions.offset = offset;
    const paginationData = getPagingData(totalCount, limit, page);

    const affiliates = await Users.findAll(queryOptions);

    // Extract names for response
    const names = affiliates.map((affiliate) => ({
      id: affiliate.id,
      name: affiliate.name,
      role_id: affiliate.role_id,
      
    }));

    const response = {
      status: true,
      message: "Affiliate names fetched successfully",
      data: names,
    };
    // Add pagination info if pagination was requested
    response.pagination = paginationData;
    return response;
  } catch (error) {
    console.error("Error fetching affiliate names:", error);
    throw error;
  }
};

module.exports = affiliateService;
