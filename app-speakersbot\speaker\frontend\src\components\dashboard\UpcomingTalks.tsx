import { Calendar, Clock, MapPin } from "lucide-react";
import { Card } from "@/components/ui/card";

const upcomingTalks = [
  {
    id: 1,
    title: "React Performance Optimization",
    event: "TechConf 2024",
    date: "2024-03-15",
    time: "2:00 PM",
    location: "San Francisco, CA",
    type: "keynote",
    status: "confirmed"
  },
  {
    id: 2,
    title: "Building Scalable Web Applications",
    event: "DevSummit",
    date: "2024-03-20",
    time: "10:30 AM",
    location: "Virtual",
    type: "workshop",
    status: "pending"
  },
  {
    id: 3,
    title: "The Future of Frontend Development",
    event: "WebDev Meetup",
    date: "2024-03-25",
    time: "6:00 PM",
    location: "New York, NY",
    type: "talk",
    status: "confirmed"
  }
];

export const UpcomingTalks = () => {
  return (
    <Card className="bg-card border-border-subtle p-4 space-y-3">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-semibold text-foreground">Upcoming Talks</h3>
        <span className="text-xs text-foreground-muted bg-accent px-2 py-1 rounded-md">
          {upcomingTalks.length}
        </span>
      </div>
      
      <div className="space-y-3">
        {upcomingTalks.map((talk) => (
          <div
            key={talk.id}
            className="bg-surface-elevated border border-border-subtle rounded-lg p-3 hover:bg-surface transition-colors cursor-pointer"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1 min-w-0">
                <h4 className="font-medium text-foreground text-sm line-clamp-2 mb-1">
                  {talk.title}
                </h4>
                <p className="text-xs text-foreground-muted mb-2">{talk.event}</p>
                
                <div className="flex items-center gap-3 text-xs text-foreground-subtle">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>{new Date(talk.date).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-3 w-3" />
                    <span>{talk.time}</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    <span className="truncate">{talk.location}</span>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2 ml-2">
                <span className={`text-xs px-2 py-1 rounded-full ${
                  talk.status === 'confirmed' 
                    ? 'bg-success/20 text-success' 
                    : 'bg-warning/20 text-warning'
                }`}>
                  {talk.status}
                </span>
                <span className={`text-xs px-2 py-1 rounded-full bg-primary/20 text-primary capitalize`}>
                  {talk.type}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      <button className="w-full text-center text-xs text-foreground-muted hover:text-foreground transition-colors py-2 border-t border-border-subtle">
        View All Talks
      </button>
    </Card>
  );
};