const { DataTypes } = require("sequelize");
const connection = require("../connection");


const SpeakerHistory = connection.define("SpeakerHistory", {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    comment: 'Primary key for the speaker history record',
  },
  speaker_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Foreign key referencing the speaker',
  },
  type: {
    type: DataTypes.ENUM('opportunity', 'subscription', 'gamification'),
    allowNull: false,
    comment: 'Type of the speaker history',
  },
  description: {
    type: DataTypes.STRING,
    comment: "Description of the speaker history",
  },
  point_change: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Point change for the speaker history',
  },
  balance: {
    type: DataTypes.INTEGER,
    allowNull: false,
    comment: 'Point balance for the speaker history',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'Record creation timestamp',
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW,
    comment: 'Record last update timestamp',
  },
 

}, {
  tableName: 'speaker_history',
  timestamps: true,
  createdAt: "created_at",
  updatedAt: "updated_at",

});

module.exports = SpeakerHistory;