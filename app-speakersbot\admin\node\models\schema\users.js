const connection = require('../connection');
const { DataTypes } = require('sequelize');

const Users = connection.define("Users", {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: 'Primary key for the user',
    },
    name: {
        type: DataTypes.STRING(100),
        allowNull: false,
        comment: 'Full name of the user',
    },
    email: {
        type: DataTypes.STRING(150),
        allowNull: false,
        validate: {
            isEmail: true
        },
        comment: 'Email address of the user (must be unique)',
    },
    phone: {
        type: DataTypes.STRING(15),
        allowNull: true,
        comment: 'Phone number of the user',
    },
    password: {
        type: DataTypes.STRING,
        allowNull: false,
        validate: {
            len: [6, 100]
        },
        comment: 'Hashed password for the user',
    },
    role_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Foreign key referencing the user role',
    },
    permission_ids: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Foreign key referencing the user permission',
    },

    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    },
    password_changed_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Date and time when the user password was changed',
    },
    deleted_at: {
        type: DataTypes.DATE,
        allowNull: true,
    },
    is_active: {
        type: DataTypes.BOOLEAN,
        defaultValue: true,
        comment: 'Indicates if the user is active',
    },
}, {
    timestamps: true,
    tableName: 'users',
    createdAt: "created_at",
    updatedAt: "updated_at",
    paranoid: true,
    deletedAt: "deleted_at",

});

module.exports = Users;