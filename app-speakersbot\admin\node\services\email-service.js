const nodemailer = require('nodemailer');
const CustomError = require('../helpers/custome-error');
const { RESPONSE_CODES } = require('../helpers/response-codes');
const CONFIG = require('../config/config');
const settingService = require('./setting-service');

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransport({
      host: CONFIG.SMTP.HOST,
      port: CONFIG.SMTP.PORT,
      secure: true,
      auth: {
        user: CONFIG.SMTP.USER,
        pass: CONFIG.SMTP.PASS
      }
    });
  }

  /**
   * Send password reset email
   * @param {string} email - Speaker's email address
   * @param {string} name - Speaker's name
   * @param {string} resetToken - Password reset token
   * @param {string} resetUrl - Base URL for password reset
   */
  async sendPasswordResetEmail(email, name, resetToken, resetUrl) {
    try {
      if(!email || !resetToken || !resetUrl){
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email, resetToken and resetUrl are required");
      }
      // get template from settings table
      const templateData = await settingService.getEmailTemplateByKey('password_reset_template');

      const resetLink = `${resetUrl}/reset-password?token=${resetToken}`;
      
      const fromName = templateData?.from_name || 'Digital Speaker Agent';
      const subject = templateData?.subject || 'Reset Your Password - Digital Speaker Agent';
      const html = templateData?.html ? 
        templateData.html
          .replace(/\{\{UserName\}\}/g, name || 'User')
          .replace(/\{\{ResetLink\}\}/g, resetLink)
        :
        `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
              <h1 style="color: #333; margin: 0;">Password Reset Request</h1>
            </div>
            <div style="padding: 30px 20px;">
              <h2 style="color: #333;">Hi ${name || 'User'},</h2>
              <p style="color: #666; line-height: 1.6;">
                We received a request to reset your password for your Digital Speaker Agent account. 
                Click asdfghj the button below to reset your password.
              </p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${resetLink}" 
                   style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; 
                          border-radius: 5px; display: inline-block; font-weight: bold;">
                  Reset Password
                </a>
              </div>
              <p style="color: #666; line-height: 1.6; font-size: 14px;">
                If the button doesn't work, you can copy and paste this link into your browser:
                <br>
                <a href="${resetLink}" style="color: #dc3545; word-break: break-all;">${resetLink}</a>
              </p>
              <p style="color: #666; line-height: 1.6; font-size: 14px;">
                This reset link will expire in 6 hours for security reasons.
              </p>
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              <p style="color: #999; font-size: 12px; text-align: center;">
                If you didn't request a password reset, please ignore this email.
              </p>
            </div>
          </div>
        `;

      const mailOptions = {
        from: `"${fromName}" <${CONFIG.SMTP.USER}>`,
        to: email,
        subject: subject,
        html: html,
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`Password reset email sent to ${email}`, result.messageId, new Date().toISOString());
      return result;
    } catch (error) {
      console.error(`Error sending password reset email to ${email} ${new Date().toISOString()}`, error);
      throw new CustomError(RESPONSE_CODES.SERVER_ERROR, 'Failed to send password reset email');
    }
  }

  /**
   * Test email configuration
   */
  async testConnection() {
    try {
      await this.transporter.verify();
      console.log('Email service is ready to send emails');
      return true;
    } catch (error) {
      console.error('Email service connection failed:', error);
      return false;
    }
  }
}

module.exports = new EmailService();
