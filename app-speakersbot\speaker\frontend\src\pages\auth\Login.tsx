import { useLoginMutation } from "@/apis/authApi";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { setCredentials } from "@/store/slices/authSlice";
import { Eye, EyeOff } from "lucide-react";
import { useState } from "react";
import { useDispatch } from "react-redux";
import { Link, useNavigate } from "react-router-dom";
import logoLight from "/logo-light.png";
import { Separator } from "@/components/ui/separator";

interface LoginProps {
  onLogin: () => void;
}
export default function Login({ onLogin }: LoginProps) {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const { toast } = useToast();
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const [login, { isLoading }] = useLoginMutation();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      const res = await login({ email, password }).unwrap();
      const token = res?.data?.token;
      if (token) {
        localStorage.setItem("token", token);
        dispatch(
          setCredentials({ user: null as any, token, refreshToken: "" })
        );
        toast({
          title: "Login Successful",
          description: "Welcome to SpeakerBot!",
        });
        onLogin();
      } else {
        throw new Error("Invalid response");
      }
    } catch (error: any) {
      const message =
        error?.data?.error?.message || "Login failed. Check your credentials.";
      toast({
        title: "Login Failed",
        description: message,
        variant: "destructive",
      });
    }
  };
  return (
    <div className="min-h-screen bg-gradient-surface flex flex-col items-center justify-center p-6 relative">
      {/* Decorative Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-primary opacity-10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-1/3 right-1/4 w-80 h-80 bg-accent-purple opacity-10 rounded-full blur-3xl"></div>
      </div>

      {/* Login Card */}
      <Card className="w-full max-w-md backdrop-blur-xl bg-card/80 border-border shadow-card relative z-10">
        <CardHeader className="space-y-2 pb-8">
          <div className="text-center">
            <img
              src={logoLight}
              alt="SpeakerBot"
              className="h-[80px] w-full object-contain"
            />
          </div>
          <div className="flex items-center">
            <Separator className="relative w-full bg-border my-3" />
          </div>
          <div className="">
            <CardTitle className="text-2xl text-center text-foreground">
              Welcome back
            </CardTitle>
            <p className="text-foreground-muted text-sm text-center mt-2">
              Login to SpeakerBot
            </p>
          </div>
        </CardHeader>
        <CardContent className="space-y-6">
          <form onSubmit={handleLogin} className="space-y-5">
            <div className="space-y-3">
              <Label htmlFor="email" className="text-foreground">
                Email
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="bg-surface-elevated border-border-subtle focus:border-primary focus:ring-primary/20"
              />
            </div>

            <div className="space-y-3">
              <Label htmlFor="password" className="text-foreground">
                Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="••••••••"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                  className="bg-surface-elevated border-border-subtle focus:border-primary focus:ring-primary/20 pr-12"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? (
                    <EyeOff className="h-5 w-5 text-foreground-muted" />
                  ) : (
                    <Eye className="h-5 w-5 text-foreground-muted" />
                  )}
                </Button>
              </div>
            </div>

            <div className="text-right">
              <Link
                to="/speaker/forgot-password"
                className="text-sm text-primary hover:text-primary-hover transition-colors"
              >
                Forgot Password?
              </Link>
            </div>

            <Button
              type="submit"
              className="w-full mt-5"
              loading={isLoading}
              disabled={isLoading}
            >
              Login
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
