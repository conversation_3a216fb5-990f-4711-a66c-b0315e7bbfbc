import BackgroundTexture from "@/assets/images/background-texture.png";
import Header from "./components/Header";
import HeroSection from "./components/HeroSection";
import HowItWorksSection from "./components/HowItWorksSection";
import RegistrationForm from "./components/RegistrationForm";
import WhyJoinSection from "./components/WhyJoinSection";
import CTASection from "./components/CTASection";

export default function EarlyAccess() {
  return (
    <main className="min-h-[100dvh] bg-black text-white font-poppins overflow-x-hidden relative">
      {/* Header */}
      <Header />

      {/* Hero Section */}
      <HeroSection />

      {/* Why Join Waitlist Section */}
      <section
        className="relative lg:py-20 py-10 lg:px-10 px-5 mx-auto"
        style={{
          backgroundImage: "url(" + BackgroundTexture + ")",
          backgroundSize: "cover",
          backgroundPosition: "top center",
        }}
      >
        {/* Dark gradient overlay at top */}
        <div className="absolute inset-x-0 top-0 lg:h-[154px] h-[100px] z-[5] bg-gradient-background-overlay" />

        {/* Why Join Waitlist Section */}
        <WhyJoinSection />
        {/* How It Works Section */}
        <HowItWorksSection />
        {/* Registration Form Section */}
        <RegistrationForm />
      </section>

      {/* Bottom CTA Section */}
      <CTASection />
    </main>
  );
}
