"""
Affiliate Page Loader
Web page loading and parsing utilities for affiliate scraping
"""

import requests
import logging
from bs4 import BeautifulSoup
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
from typing import Optional, Tuple
from app.config.logger import get_logger
logger = get_logger(__name__, file_name="scraper.log")
from .constants import (
    DEFAULT_TIMEOUT, RETRY_TIMEOUT, MAX_RETRIES, BACKOFF_FACTOR,
    RETRY_STATUS_CODES, DEFAULT_HEADERS
)

class ScraperHttpError(Exception):
    """Represents an HTTP error encountered while fetching a webpage."""
    def __init__(self, status_code: int, message: str):
        super().__init__(message)
        self.status_code = status_code

class AffiliatePageLoader:
    """Web page loading and parsing utilities"""
    def __init__(self):
        """Initialize HTTP session with retry strategy"""
        self.session = self._create_session()
    def _create_session(self) -> requests.Session:
        """Create HTTP session with browser-like headers, retry strategy, and connection pooling"""
        session = requests.Session()
        session.headers.update(DEFAULT_HEADERS)
        # Configure session with retry strategy and connection pooling
        retry_strategy = Retry(
            total=MAX_RETRIES,
            backoff_factor=BACKOFF_FACTOR,
            status_forcelist=RETRY_STATUS_CODES,
        )
        # Add connection pooling for better performance
        adapter = HTTPAdapter(
            max_retries=retry_strategy,
            pool_connections=5,  # Number of connection pools
            pool_maxsize=10,  # Maximum number of connections in pool
            pool_block=False  # Don't block when pool is full
        )
        session.mount("http://", adapter)
        session.mount("https://", adapter)
        return session
    
    def load_page(self, url: str) -> Tuple[BeautifulSoup, str]:
        """
        Load and parse a webpage with robust error handling
        Args:
            url (str): The URL to load
        Returns:
            Tuple[BeautifulSoup, str]: Parsed HTML soup and the URL that was loaded
        Raises:
            ScraperHttpError: If HTTP error occurs
            Exception: If other errors occur during loading
        """
        try:
            response = self.session.get(url, timeout=DEFAULT_TIMEOUT)
            response.raise_for_status()
        except requests.exceptions.Timeout as e:
            try:
                response = self.session.get(url, timeout=RETRY_TIMEOUT)
                response.raise_for_status()
            except requests.exceptions.Timeout:
                logger.error(f"Both timeout attempts failed for {url}")
                raise Exception(f"Website timeout: Unable to load {url} within reasonable time")
                
        except requests.exceptions.HTTPError as e:
            status_code = e.response.status_code if e.response is not None else 0
            reason = e.response.reason if e.response is not None else "HTTP error"
            logger.error(f"HTTP error while fetching {url}: {status_code} {reason}")
            raise ScraperHttpError(status_code, f"Failed to fetch webpage: {status_code} {reason} for url: {url}")
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Request error for {url}: {e}")
            raise Exception(f"Failed to fetch webpage: {str(e)}")
        
        soup = BeautifulSoup(response.content, 'html.parser')
        return soup, url