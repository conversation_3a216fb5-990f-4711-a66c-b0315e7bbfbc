/**
 * Helper to create a structured API response.
 *
 * @param {Object} options - Options for building the response.
 * @param {any[]} [options.data] - Actual data to return.
 * @param {string} [options.message] - Optional message to return.
 * @param {Object} [options.error] - Error information (if any).
 * @param {number} [options.error.code] - HTTP status code for error.
 * @param {string} [options.error.message] - Error message.
 * @param {any} [options.error.error] - Original error/cause.
 * @param {Object} [options.pagination] - Pagination info.
 * @param {number} [options.pagination.total] - Total number of items.
 * @param {number} [options.pagination.limit] - Items per page.
 * @param {number} [options.pagination.page] - Current page number.
 * @returns {Object} Structured API response.
 *
 * @example
 * // Success with data only
 * ApiResponse({ data: [{ id: 1, name: "Test" }] });
 *
 * @example
 * // Success with data, message, and pagination
 * ApiResponse({
 *   data: [{ id: 1 }, { id: 2 }],
 *   message: "Fetched successfully",
 *   pagination: { total: 50, limit: 10, page: 1 }
 * });
 *
 * @example
 * // Message only
 * ApiResponse({ message: "Operation completed successfully" });
 *
 * @example
 * // Error response
 * ApiResponse({
 *   error: { code: 404, message: "Not found", error: "Product missing" }
 * });
 */

function ApiResponse({ data, message, error, pagination } = {}) {
    const response = {};

    if (error) {
        response.status = false;
        response.error = {
            code: error.code || 500,
            message: error.message || "Something went wrong",
            error: error.error || null,
        };
    } else {
        response.status = true;

        if (message) response.message = message;
        if (data !== undefined) response.data = data;

        if (pagination) {
            const { total, limit, page } = pagination;
            const totalPages = Math.ceil(total / limit);
            response.pagination = {
                total,
                limit,
                page,
                totalPages,
            };
        }
    }

    return response;
}

module.exports = ApiResponse
