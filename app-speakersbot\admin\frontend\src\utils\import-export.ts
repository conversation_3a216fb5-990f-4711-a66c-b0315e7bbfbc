import { Opportunity } from '../types';
import { toast } from "@/hooks/use-toast";

export interface ImportColumn {
  field: keyof Opportunity;
  label: string;
  required: boolean;
  validate?: (value: any) => boolean;
}

export const IMPORT_COLUMNS: ImportColumn[] = [
  { field: 'title', label: 'Title', required: true },
  { field: 'organization', label: 'Organization', required: true },
  { field: 'description', label: 'Description', required: true },
  { field: 'category', label: 'Category', required: true },
  { field: 'status', label: 'Status', required: false },
  { field: 'budget', label: 'Budget', required: false },
  { field: 'location', label: 'Location', required: false },
  { field: 'eventDate', label: 'Event Date', required: true },
  { field: 'deadline', label: 'Application Deadline', required: true },
  { field: 'requirements', label: 'Requirements', required: false },
  { field: 'tags', label: 'Tags', required: false },
  { field: 'externalUrl', label: 'External URL', required: false },
];

export const validateCsvRow = (row: any, columnMapping: Record<string, string>): string[] => {
  const errors: string[] = [];
  
  // Check required fields
  IMPORT_COLUMNS.forEach(col => {
    if (col.required) {
      const csvColumn = columnMapping[col.field];
      if (!csvColumn || !row[csvColumn]) {
        errors.push(`Missing required field: ${col.label}`);
      }
    }
  });

  // Validate dates
  const eventDateColumn = columnMapping['eventDate'];
  const deadlineColumn = columnMapping['deadline'];
  
  if (eventDateColumn && row[eventDateColumn] && !isValidDate(row[eventDateColumn])) {
    errors.push('Invalid event date format');
  }
  
  if (deadlineColumn && row[deadlineColumn] && !isValidDate(row[deadlineColumn])) {
    errors.push('Invalid deadline format');
  }

  // Validate deadline is before event date
  if (eventDateColumn && deadlineColumn && row[eventDateColumn] && row[deadlineColumn]) {
    const eventDate = new Date(row[eventDateColumn]);
    const deadline = new Date(row[deadlineColumn]);
    if (deadline >= eventDate) {
      errors.push('Deadline must be before event date');
    }
  }

  return errors;
};

export const isValidDate = (dateString: string): boolean => {
  const date = new Date(dateString);
  return !isNaN(date.getTime());
};

export const transformCsvRowToOpportunity = (
  row: any, 
  columnMapping: Record<string, string>
): Partial<Opportunity> => {
  const opportunity: any = {};
  
  Object.keys(columnMapping).forEach(field => {
    const csvColumn = columnMapping[field];
    if (csvColumn && row[csvColumn] !== undefined) {
      let value = row[csvColumn];
      
      // Transform specific fields
      switch (field) {
        case 'budget':
          value = parseFloat(value) || 0;
          break;
        case 'requirements':
          value = typeof value === 'string' ? value.split(',').map(r => r.trim()) : [];
          break;
        case 'tags':
          value = typeof value === 'string' ? value.split(',').map(t => t.trim()) : [];
          break;
        case 'eventDate':
        case 'deadline':
          value = new Date(value).toISOString();
          break;
      }
      
      opportunity[field] = value;
    }
  });

  return opportunity;
};

export const exportOpportunitiesToCsv = (
  opportunities: Opportunity[], 
  visibleColumns: string[]
): void => {
  if (opportunities.length === 0) {
    toast({
      title: 'No data to export',
      variant: 'destructive'
    });
    return;
  }

  const headers = visibleColumns.map(col => {
    const column = IMPORT_COLUMNS.find(c => c.field === col);
    return column?.label || col;
  });

  const csvRows = opportunities.map(opp => {
    return visibleColumns.map(col => {
      let value = opp[col as keyof Opportunity];
      
      // Handle arrays and objects
      if (Array.isArray(value)) {
        value = value.join(', ');
      } else if (typeof value === 'object' && value !== null) {
        value = JSON.stringify(value);
      } else if (typeof value === 'string' && value.includes(',')) {
        value = `"${value}"`;
      }
      
      return value || '';
    });
  });

  const csvContent = [headers, ...csvRows].map(row => row.join(',')).join('\n');
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', `opportunities_${new Date().toISOString().split('T')[0]}.csv`);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  toast({
    title: 'Opportunities exported successfully'
  });
};

export const exportToCSV = (data: any[], filename: string): void => {
  if (data.length === 0) {
    toast({
      title: 'No data to export',
      variant: 'destructive'
    });
    return;
  }

  // Get headers from the first object
  const headers = Object.keys(data[0]);

  const csvRows = data.map(row => {
    return headers.map(header => {
      let value = row[header];
      
      // Handle arrays and objects
      if (Array.isArray(value)) {
        value = value.join(', ');
      } else if (typeof value === 'object' && value !== null) {
        value = JSON.stringify(value);
      } else if (typeof value === 'string' && value.includes(',')) {
        value = `"${value}"`;
      }
      
      return value || '';
    });
  });

  const csvContent = [headers, ...csvRows].map(row => row.join(',')).join('\n');
  
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  toast({
    title: 'Data exported successfully'
  });
};

export const findDuplicateOpportunities = (
  newOpportunity: Partial<Opportunity>,
  existingOpportunities: Opportunity[]
): Opportunity[] => {
  return existingOpportunities.filter(existing => {
    return existing.title?.toLowerCase() === newOpportunity.title?.toLowerCase() &&
           existing.organization?.toLowerCase() === newOpportunity.organization?.toLowerCase() &&
           existing.deadline === newOpportunity.deadline;
  });
};