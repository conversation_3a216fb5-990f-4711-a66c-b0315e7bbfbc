import { User, Mic, LogOut } from "lucide-react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { SidebarProvider, SidebarTrigger, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/common/AppSidebar";
import { useState } from "react";
import { OpportunityTab } from "@/components/dashboard/tabs/OpportunityTab";
import { AffiliateBookingTab } from "@/components/dashboard/tabs/AffiliateBookingTab";
// import { ExtensionTab } from "@/components/dashboard/tabs/ExtensionTab"; // Hidden temporarily - can be restored when needed
import { DashboardTab } from "@/components/dashboard/tabs/DashboardTab";
import { Toaster } from "@/components/ui/toaster";
import { SubscriptionPage } from "./subscriptions/SubscriptionPage";
import IntakePage from "./intake-form/IntakePage";
import { InvitePage } from "./invite-page/InvitePage";

interface IndexProps {
  onLogout: () => void;
}

// Dashboard index page - main entry point
const Index = ({ onLogout }: IndexProps) => {
  const [activeSection, setActiveSection] = useState('today');
  const navigate = useNavigate();

  const renderContent = () => {
    switch (activeSection) {
      case 'today':
        return <DashboardTab />;
      case 'intake':
        return <IntakePage />;
      case 'opportunities':
        return <OpportunityTab />;
      case 'subscription':
        return <SubscriptionPage />;
      case 'gamification':
        return <AffiliateBookingTab />;
      case 'invite':
        return <InvitePage />;
      default:
        return <DashboardTab />;
    }
  };

  return (
    <SidebarProvider defaultOpen={true}>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar activeSection={activeSection} onSectionChange={setActiveSection} />
        
        <SidebarInset className="w-full">
          {/* Header */}
          <header className="bg-surface border-b border-border-subtle sticky top-0 z-50 flex h-16 shrink-0 items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            
            <div className="flex-1"></div>
            
            <div className="flex items-center gap-2">
              <Button 
                size="sm" 
                variant="ghost" 
                className="p-2 hover:bg-surface-elevated"
                onClick={() => navigate('/speaker/profile')}
                title="Profile"
              >
                <User className="h-4 w-4 text-foreground-muted" />
              </Button>
              <Button 
                size="sm" 
                variant="ghost" 
                className="p-2 hover:bg-surface-elevated"
                onClick={onLogout}
                title="Logout"
              >
                <LogOut className="h-4 w-4 text-foreground-muted" />
              </Button>
            </div>
          </header>

          {/* Main Content */}
          <main className="flex-1 overflow-auto p-6">
            {renderContent()}
          </main>
        </SidebarInset>
      </div>

      
      <Toaster />
    </SidebarProvider>
  );
};

export default Index;
