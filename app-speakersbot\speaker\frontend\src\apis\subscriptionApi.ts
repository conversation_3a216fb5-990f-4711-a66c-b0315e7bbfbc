import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import type { ApiResponse, SubscriptionPlan } from "./types";

// Define the base URL - you can adjust this based on your backend configuration
const BASE_URL =
  import.meta.env.VITE_API_BASE_URL;

export const subscriptionApi = createApi({
  reducerPath: "subscriptionApi",
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Add authentication token if available
      const token = localStorage.getItem("token");
      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }
      headers.set("content-type", "application/json");
      return headers;
    },
  }),
  tagTypes: ["Subscriptions"],
  endpoints: (builder) => ({
    // Get available subscription plans
    getSubscriptionPlans: builder.query<ApiResponse<SubscriptionPlan[]>, void>({
      query: () => "/subscriptions",
      providesTags: ["Subscriptions"],
    }),
  }),
});

// Export hooks for usage in functional components
export const { useGetSubscriptionPlansQuery } = subscriptionApi;
