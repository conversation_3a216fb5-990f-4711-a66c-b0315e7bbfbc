"""
Digital Form Service
Generates structured, LLM-produced key/value details for a given speaker and opportunity.

Workflow:
- Fetch speaker and opportunity context from MySQL database
- Construct a clear, constrained prompt for the LLM
- Parse and validate JSON response
- Return structured details keyed by specific sections
"""

from __future__ import annotations

import json
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional
from app.helpers.database_manager import db_manager
from openai import OpenAI
from app.config.config import config
from app.services.speaker_matching_service import SpeakerMatchingService
from app.models.speakers import Speaker
from app.models.speaker_details import SpeakerDetails
from app.models.opportunities import SpeakerOpportunity as Opportunity
from app.helpers.dsa_helper import DSAHelper

# Logging setup (inherits root handlers)
logger = logging.getLogger(__name__)
# OpenAI Client
openai_client = OpenAI(api_key=config.OPENAI_API_KEY)

class DigitalFormService:
    """Service to generate digital form details from LLM using stored profiles."""

    def __init__(self) -> None:
        # Use centralized database manager instead of creating new session
        self.SessionLocal = db_manager.SessionLocal
        self.simple_hybrid_service = SpeakerMatchingService()
        self.dsa_helper = DSAHelper()

    # -----------------------------
    # Data Access Helpers
    # -----------------------------
    
    def _get_speaker_from_mysql(self, speaker_id: int) -> Optional[Dict[str, Any]]:
        """Retrieve speaker data from MySQL database."""
        try:
            with self.SessionLocal() as session:
                speaker_detail = session.query(SpeakerDetails).filter(SpeakerDetails.speaker_id == speaker_id).all()
                speaker_dict = {}
                remove_speaker_dict = ['When you walk off stage, do you usually feel more energized by the crowd or drained and ready for quiet?',
                'Do you prefer sticking to a clear plan and checklist, or adapting in the moment as things unfold?',
                'What pushes you hardest when preparing a talk — making it flawless, inspiring people, solving problems, or connecting deeply?',
                'When an audience challenges you, do you win them with stories and enthusiasm, with facts and data, or by inviting collaboration?',
                'Do you naturally weave themes like gratitude, fairness, hope, or strategy into your message?',
                'If you weren\'t speaking, would you rather be hands-on building something, exploring ideas, leading a team, or helping people grow?',
                'Headshot',
                'Voice Clip Upload',
                'Video Clip Upload',
                'Logo',
                'Additional Headshots',
                'One-sheet PDF',
                'Sizzle Reel',
                'Past Talk Decks',
                'Press Coverage Links',
                'TED or TEDx Talk Link',
                'TEDx Transcript Upload',
                'Affiliate Program Participation',
                'Live Speaking Fee',
                'Refer New Affiliates',
                'Affiliate Notes'
                ]

                # Convert SQLAlchemy object to dictionary
                for field in speaker_detail:
                    if field.key and field.value:
                        if field.key not in remove_speaker_dict:
                            speaker_dict[field.key] = field.value
                return speaker_dict
        except Exception as e:
            logger.error(f"MySQL speaker fetch failed for {speaker_id}: {e}")
        return None

    def _get_opportunity_from_mysql(self, opportunity_id: int) -> Optional[Dict[str, Any]]:
        """Retrieve opportunity data from MySQL database."""
        try:
            with self.SessionLocal() as session:
                opportunity = session.query(Opportunity).filter(Opportunity.id == opportunity_id).first()
                if opportunity:
                    # Convert SQLAlchemy object to dictionary
                    opp_dict = {
                        "id": opportunity.id,
                        "title": opportunity.title,
                        "organization": opportunity.organization,
                        "event_type": opportunity.event_type,
                        "start_date": opportunity.start_date.isoformat() if opportunity.start_date else None,
                        "end_date": opportunity.end_date.isoformat() if opportunity.end_date else None,
                        "event_url": opportunity.event_url,
                        "source_url": opportunity.source_url,
                        "city": opportunity.city,
                        "state": opportunity.state,
                        "country": opportunity.country,
                        "venue": opportunity.venue,
                        "is_virtual": opportunity.is_virtual,
                        "is_active": opportunity.is_active,
                        "description": opportunity.description,
                        "industry": opportunity.industry,
                        "search_query": opportunity.search_query,
                        "feed_back": opportunity.feed_back,
                        "source": opportunity.source,
                        "tag": opportunity.tag,
                        "created_at": opportunity.created_at.isoformat() if opportunity.created_at else None,
                        "updated_at": opportunity.updated_at.isoformat() if opportunity.updated_at else None,
                        "deleted_at": opportunity.deleted_at.isoformat() if opportunity.deleted_at else None,
                    }
                    return opp_dict
        except Exception as e:
            logger.error(f"MySQL opportunity fetch failed for {opportunity_id}: {e}")
        return None

    def _get_speaker_voiceprint_metrics(self, speaker_id: int) -> Dict[str, Any]:
        """Retrieve voiceprint tone profile from Speaker model."""
        try:
            with self.SessionLocal() as session:
                speaker = session.query(Speaker).filter(Speaker.id == speaker_id).first()
                if speaker and speaker.voiceprint:
                    return speaker.voiceprint
        except Exception as e:
            logger.warning(f"Failed to fetch voiceprint for speaker {speaker_id}: {e}")
        return {}

    # -----------------------------
    # Prompting
    # -----------------------------
    def _build_prompt(self, speaker_ctx: Dict[str, Any], opp_ctx: Dict[str, Any], org_name: str = None, speaker_name: str = None, voiceprint_metrics: Dict[str, Any] = None) -> str:
        """Build strict JSON-only prompt with exact headings and nulls for missing data.
        Uses token replacement to avoid f-string brace nesting issues.
        """
        # Precompute dynamic values
        speaker_id_val = speaker_ctx.get('id') or speaker_ctx.get('speaker_id') or 0
        opportunity_id_val = opp_ctx.get('id') or opp_ctx.get('opportunity_id') or 0
        speaker_name_val = speaker_ctx.get('name') or speaker_ctx.get('Name') or ""
        speaker_email_val = speaker_ctx.get('email') or speaker_ctx.get('Email') or ""
        speaker_phone_val = speaker_ctx.get('phone_number') or speaker_ctx.get('Phone Number') or ""
        org_val = opp_ctx.get('organization') or ""
        title_val = opp_ctx.get('title') or ""
        
        # Log the extracted values for debugging
        logger.info(f"Extracted values - Speaker: {speaker_name_val}, Org: {org_val}, Title: {title_val}")
        now_iso = datetime.utcnow().isoformat()
        
        # Calculate time remaining until event and email send dates
        event_start_date = opp_ctx.get('start_date')
        
        # Debug logging
        logger.info(f"Event start date from context: {event_start_date}")
        logger.info(f"Opportunity context keys: {list(opp_ctx.keys())}")
        
        # Use DSA helper to calculate email timing
        event_date_minus_5_5_months, event_date_minus_4_months = self.dsa_helper.calculate_email_timing(event_start_date)

        # Get voiceprint tone profile if provided
        voiceprint_metrics_json = ""
        if voiceprint_metrics:
            voiceprint_metrics_json = json.dumps(voiceprint_metrics, indent=2, default=str)
        else:
            voiceprint_metrics_json = json.dumps({
                "speaker_id": "",
                "voiceprint_version": "v1",
                "tone_profile": {}
            }, indent=2)

        speaker_json = json.dumps(speaker_ctx, indent=2, default=str)
        opportunity_json = json.dumps(opp_ctx, indent=2, default=str)

        # JSON-serialize values for safe injection (keeps quotes/nulls correct)
        j_speaker_id = json.dumps(int(speaker_id_val))
        j_opportunity_id = json.dumps(int(opportunity_id_val))
        j_speaker_name = json.dumps(speaker_name_val if speaker_name_val else None)
        j_speaker_email = json.dumps(speaker_email_val if speaker_email_val else None)
        j_speaker_phone = json.dumps(speaker_phone_val if speaker_phone_val else None)
        j_org = json.dumps(org_val if org_val else None)
        j_title = json.dumps(title_val if title_val else None)
        j_now = json.dumps(now_iso)


        template = """
            You are a senior sales enablement strategist following DSA Developer Speaker Output Playbook v3. Generate a single JSON object with EXACT structure below. Use SPEAKER and OPPORTUNITY context to populate all fields meaningfully.

            ⚠️⚠️⚠️ CRITICAL: WORD COUNT COMPLIANCE IS MANDATORY ⚠️⚠️⚠️
            Every section below has a SPECIFIC word count requirement. You MUST count words for each section and ensure it matches the specified range. 
            Word counts are NOT suggestions - they are MANDATORY requirements. Your output will be rejected if word counts are not met.
            
            ⚠️⚠️⚠️⚠️⚠️ CRITICAL: Introduction Email vs Email 1: Intro MUST BE COMPLETELY DIFFERENT ⚠️⚠️⚠️⚠️⚠️
            BEFORE GENERATING: Remember that "Introduction Email" and "Email 1: Intro" are TWO SEPARATE emails.
            - They MUST have DIFFERENT subject lines (different words, different angles, NOT synonyms)
            - They MUST have DIFFERENT body content (different opening hooks, different examples, different phrasing, different closing)
            - NEVER copy any text between these two emails
            - After writing both, compare them side by side - if similar, REWRITE to make them unique
            - This is a CRITICAL requirement - duplicated content will cause rejection
            
            ⚠️⚠️⚠️ SPECIAL ATTENTION: Follow-Up Email Sequence - Email 2, 3, and 4 MUST be 220-280 words EXACTLY ⚠️⚠️⚠️
            These three emails frequently fail word count requirements. You MUST pay extra attention:
            - Email 2: Value Reminder = 220-280 words (count and verify)
            - Email 3: Program Fit Check-In = 220-280 words (count and verify)
            - Email 4: Last Call = 220-280 words (count and verify)
            
            Before outputting, verify word count for: emails (270-330 or 220-280), voicemail (90-120), descriptions (150-200, 500-600), bios (70-100, 300-400), article (450-550), and all other sections as specified.

            CRITICAL JSON FORMATTING:
            - ALL string values must be properly quoted and closed with double quotes
            - NO unescaped newlines inside string values
            - NO incomplete strings or missing closing quotes
            - EVERY string must end with a closing double quote before comma, bracket, or brace
            - Output ONLY valid JSON, no markdown blocks
            - Test your JSON before outputting - it must be parseable

            OVERARCHING DEVELOPER RULES:
            - Voice: Calibrated speaker voiceprint, plain English, conversational, warm, direct
            - Formatting: MANDATORY - Enforce section word counts exactly as defined, short paragraphs, one clear CTA per email
            - Dynamic Fields: Always fill [Event Name], [Contact Name], [Event Date], [Audience], [Keynote Title]
            - Consistency: All text editable, match filled-sample style, choose robust options
            - Special Rules: Plain English unless industry-specific terms required, commas and periods only
            - Knowledge Integration: Start from calibration, validate against Agent Score
            - Word Counts: All word counts are MANDATORY - output must match the defined word count ranges

            PSYCHOMETRIC PERSONALITY ALIGNMENT:
            - Incorporate MBTI, DiSC, Big Five, Enneagram, CliftonStrengths, and RIASEC profiles to shape personality-based nuances in writing. Reflect the speaker’s cognitive and emotional tendencies, motivation patterns, and interpersonal style throughout the content. Align sentence structure, pacing, and focus areas accordingly.

            CONTEXT_SPEAKER:
            <<SPEAKER_JSON>>

            CONTEXT_OPPORTUNITY:
            <<OPPORTUNITY_JSON>>

            ⚠️ MANDATORY VOICEPRINT TONE PROFILE CALIBRATION - CRITICAL REQUIREMENT ⚠️:
            
            THE VOICEPRINT TONE PROFILE PROVIDED BELOW CONTAINS ACTUAL CALIBRATED SCORES (1-5 SCALE) FOR THIS SPECIFIC SPEAKER.
            
            YOU MUST GENERATE ALL CONTENT AND CREATE THE PDF STRICTLY BASED ON THESE EXACT TONE PROFILE SCORES. DO NOT WRITE RANDOMLY OR IGNORE THESE VALUES.
            
            CRITICAL RULES:
            1. These tone profile scores are REAL measured values from LLM analysis of this speaker's actual audio, video, and text samples
            2. You MUST use these exact scores to calibrate every piece of content you generate
            3. The PDF/DSA you create MUST match these specific tone profile scores, not generic speaker voice
            4. Every sentence, paragraph, email, bio, article, and PDF content MUST reflect the tone profile scores shown below
            5. Use the EXACT scores provided in the tone profile JSON below - do not approximate or guess
            6. DO NOT write randomly or use generic voice - you MUST follow the tone profile scores exactly as provided
            7. The tone profile scores tell you EXACTLY how this speaker communicates - analyze the actual scores and match your writing accordingly
            8. Based on these scores, generate content and create a PDF that matches the speaker's communication style precisely
            
            ============================================
            VOICEPRINT TONE PROFILE - MANDATORY CALIBRATION DATA
            ============================================
            THE JSON BELOW CONTAINS REAL CALIBRATED TONE PROFILE SCORES (1-5 SCALE) FOR THIS SPECIFIC SPEAKER.
            YOU MUST USE THESE EXACT SCORES TO GENERATE ALL CONTENT AND CREATE THE PDF.
            DO NOT IGNORE OR APPROXIMATE - USE THE EXACT SCORES PROVIDED.
            BASED ON THESE SCORES, INTERPRET THE SPEAKER'S COMMUNICATION STYLE AND MATCH YOUR WRITING ACCORDINGLY.
            ============================================
            
            <<VOICEPRINT_METRICS_JSON>>
            
            ============================================
            TONE PROFILE STRUCTURE (for your reference):
            ============================================
            
            The tone profile JSON contains scores (1-5 scale) for 12 communication dimensions:
            - directness (1-5): How direct and straightforward is the communication? 1=very indirect, 5=very direct/blunt
            - formality (1-5): Level of formality in language and delivery. 1=very casual, 5=very formal
            - rhythm (1-5): Consistency and flow of speech patterns. 1=irregular/choppy, 5=very smooth/consistent
            - sentence_length (1-5): Average sentence complexity. 1=very short/simple, 5=very long/complex
            - warmth (1-5): Emotional warmth and friendliness. 1=cold/distant, 5=very warm/approachable
            - aggression (1-5): Assertiveness and intensity. 1=very passive, 5=very aggressive/forceful
            - emotional_weight (1-5): Emotional depth and intensity. 1=very light/superficial, 5=very deep/intense
            - complexity (1-5): Intellectual complexity and sophistication. 1=very simple, 5=very complex/sophisticated
            - rhetorical_devices (1-5): Use of rhetorical techniques, metaphors, storytelling. 1=minimal, 5=extensive
            - evidence_preference (1-5): Reliance on data, facts, and evidence. 1=anecdotal/emotional, 5=data-driven/analytical
            - jargon_tolerance (1-5): Use of technical terms and specialized language. 1=plain language, 5=highly technical
            - humor_intensity (1-5): Use of humor and lightheartedness. 1=serious/formal, 5=very humorous/playful
            
            Use your knowledge of how these scores relate to communication style and generate content that matches the EXACT scores provided.
            Analyze each score and ensure your writing reflects it accurately in the PDF and all content.
            
            ============================================
            MANDATORY CALIBRATION WORKFLOW FOR PDF GENERATION:
            ============================================
            
            FOR EVERY PIECE OF CONTENT YOU GENERATE (emails, bios, articles, descriptions, PDF content):
            
            1. Read the tone profile JSON above carefully
            2. Analyze each score (1-5) using your knowledge of communication styles
            3. Generate content that matches the EXACT scores provided in the tone profile
            4. Ensure directness level matches the directness score (1=indirect, 5=very direct)
            5. Ensure formality level matches the formality score (1=casual, 5=very formal)
            6. Ensure sentence complexity matches the sentence_length score (1=short/simple, 5=long/complex)
            7. Ensure warmth level matches the warmth score (1=cold, 5=very warm)
            8. Ensure assertiveness matches the aggression score (1=passive, 5=aggressive)
            9. Ensure emotional depth matches the emotional_weight score (1=light, 5=deep)
            10. Ensure intellectual level matches the complexity score (1=simple, 5=sophisticated)
            11. Ensure use of rhetorical devices matches the rhetorical_devices score (1=minimal, 5=extensive)
            12. Ensure data vs anecdotal balance matches the evidence_preference score (1=anecdotal, 5=data-driven)
            13. Ensure technical language matches the jargon_tolerance score (1=plain, 5=technical)
            14. Ensure humor level matches the humor_intensity score (1=serious, 5=humorous)
            15. After writing, verify each score is reflected in your content
            16. If any score is not reflected, rewrite to match it
            17. Create the PDF based on these tone profile scores to ensure it matches the speaker's communication style

            REQUIRED OUTPUT JSON STRUCTURE (9 SECTIONS):
            NOTE: For each field below, you MUST count words and ensure it matches the specified word count. Word counts are MANDATORY.
            {
            "Top 3 Attendee Challenges": {
                "Challenge #1": {"title": "...", "description": "..."}, // description MUST be 25-60 words
                "Challenge #2": {"title": "...", "description": "..."}, // description MUST be 25-60 words
                "Challenge #3": {"title": "...", "description": "..."} // description MUST be 25-60 words
            },

            "SWOT Analysis for [ORGANIZATION_NAME]": {
                "Strengths": ["...", "...", "..."],
                "Weaknesses": ["...", "...", "..."],
                "Opportunities": ["...", "...", "..."],
                "Threats": ["...", "...", "..."]
            },

            "SMART Analysis": {
                "Specific": "...", // All 5 sections combined MUST be 200-300 words total - count and verify
                "Measurable": "...", // All 5 sections combined MUST be 200-300 words total
                "Attainable": "...", // All 5 sections combined MUST be 200-300 words total
                "Relevant": "...", // All 5 sections combined MUST be 200-300 words total
                "Timely": "..." // All 5 sections combined MUST be 200-300 words total
            },

            "Previous Two Annual [ORGANIZATION_NAME] Conferences – Keynote & Breakout Speaker Research": {
                "2025 Keynote Speakers": [{"name": "...", "topic": "...", "estimated_fee_range": "..."}],
                "2025 Breakouts": [{"name": "...", "estimated_fee_range": "..."}],
                "2024 Keynote Speakers": [{"name": "...", "topic": "...", "estimated_fee_range": "..."}],
                "2024 Breakouts": [{"name": "...", "estimated_fee_range": "..."}]
            },

            "Introduction Email": {
                "Subject Line": "Clear + benefit-driven (8–12 words) - MUST BE UNIQUE, NOT SAME AS Email 1: Intro",
                "Body": "..." // Body MUST be 270-330 words - MUST BE COMPLETELY DIFFERENT from Email 1: Intro body (different hooks, examples, phrasing)
            },

            ⚠️⚠️⚠️ CRITICAL: Introduction Email vs Email 1: Intro - THEY MUST BE COMPLETELY DIFFERENT ⚠️⚠️⚠️
            - "Introduction Email" and "Email 1: Intro" are TWO SEPARATE emails - NO DUPLICATION ALLOWED
            - They MUST have COMPLETELY DIFFERENT subject lines (different words, different angles, not synonyms)
            - They MUST have COMPLETELY DIFFERENT body content (different hooks, different examples, different phrasing, different closing)
            - Introduction Email = first initial outreach; Email 1: Intro = follow-up to deepen relationship
            - Use different success stories, different credentials, different proof points in each email
            - NEVER copy any sentences, paragraphs, or phrases between these two emails

            ⚠️⚠️⚠️ FOLLOW-UP EMAIL SEQUENCE - WORD COUNT REQUIREMENTS ⚠️⚠️⚠️
            CRITICAL: Each email Body below has a SPECIFIC word count that MUST be followed exactly. 
            Word counts are MANDATORY - you MUST count words and verify before outputting.
            
            - Email 1: Intro Body = 270-330 words (MANDATORY - count and verify)
            
            ⚠️⚠️⚠️ PAY SPECIAL ATTENTION TO EMAILS 2, 3, AND 4 - THEY MUST BE 220-280 WORDS EXACTLY ⚠️⚠️⚠️
            - Email 2: Value Reminder Body = 220-280 words (MANDATORY - count and verify EXACTLY - not less, not more)
            - Email 3: Program Fit Check-In Body = 220-280 words (MANDATORY - count and verify EXACTLY - not less, not more)
            - Email 4: Last Call Body = 220-280 words (MANDATORY - count and verify EXACTLY - not less, not more)
            
            REMINDER: For Email 2, 3, and 4, the word count MUST be between 220-280 words. Count every single word. If it's less than 220, add more content. If it's more than 280, remove content. This is COMPULSORY.

            "Follow-Up Email Sequence": {
                "Email 1: Intro": {
                "Send": "Immediately after gig acceptance",
                "Subject Line": "Clear + benefit-driven (8–12 words) - MUST BE UNIQUE, NOT SAME AS Introduction Email",
                "Body": "..." // CRITICAL: Body MUST be EXACTLY 270-330 words AND MUST BE COMPLETELY DIFFERENT from Introduction Email body (different hooks, examples, phrasing, closing). This is MANDATORY.
                },
                "Email 2: Value Reminder": {
                "Send": "~1 week after Email 1",
                "Subject Line": "[Speaker Name] + benefit statement (≤12 words)",
                "Body": "..." // ⚠️⚠️⚠️ CRITICAL: Body MUST be EXACTLY 220-280 words (NOT less than 220, NOT more than 280). Count every word. If wrong, rewrite until correct. This is MANDATORY and will be rejected if not met.
                },
                "Email 3: Program Fit Check-In": {
                "Send": "~60 days before event",
                "Subject Line": "Content alignment for [Event Name]",
                "Body": "..." // ⚠️⚠️⚠️ CRITICAL: Body MUST be EXACTLY 220-280 words (NOT less than 220, NOT more than 280). Count every word. If wrong, rewrite until correct. This is MANDATORY and will be rejected if not met.
                },
                "Email 4: Last Call": {
                "Send": "~30 days after Program Fit Check-In if no response",
                "Subject Line": "Last call to finalize [Speaker Name] for [Event Name]",
                "Body": "..." // ⚠️⚠️⚠️ CRITICAL: Body MUST be EXACTLY 220-280 words (NOT less than 220, NOT more than 280). Count every word. If wrong, rewrite until correct. This is MANDATORY and will be rejected if not met.
                }
            },

            "20–30 Second Voicemail Script": "...", // MUST be 90-120 words - count and verify

            "Customized RFP": {
                "Proposed Keynote Title": "...", // MUST be 7-12 words - count and verify
                "Short Description": "...", // MUST be 150-200 words - count and verify
                "Detailed Description": "...", // MUST be 500-600 words - count and verify
                "Learning Objectives": ["...", "...", "...", "...", "..."], // MUST be ≤15 words each - count each one
                "Attendee Benefits": ["...", "...", "...", "...", "..."], // MUST be ≤15 words each - count each one
                "Key Challenges": ["...", "...", "...", "...", "..."], // MUST be ≤20 words each - count each one
                "Speaker Bio (Tailored)": "...", // MUST be 70-100 words - count and verify
                "Speaker Bio (Long)": "..." // MUST be 300-400 words - count and verify
            },

            "500-Word New York Times-Style Article": {
                "Title": "...",
                "Byline": "[SPEAKER_NAME]",
                "Body": "..." // Body MUST be 450-550 words - count and verify
            },

            "_metadata": {
                "speaker_id": <<SPEAKER_ID>>,
                "opportunity_id": <<OPPORTUNITY_ID>>,
                "speaker_name": <<SPEAKER_NAME>>,
                "speaker_email": <<SPEAKER_EMAIL>>,
                "speaker_phone": <<SPEAKER_PHONE>>,
                "organization": <<ORG>>,
                "event_title": <<TITLE>>,
                "generated_at": <<NOW_ISO>>
            }
            }

            DSA EMAIL TIMING REQUIREMENTS:
            - Email 1 (Intro): Sent immediately after gig acceptance
            - Introduction Email: Sent immediately after gig acceptance
            - Email 2 (Value Reminder): Sent ~1 week after Email 1
            - Email 3 (Program Fit Check-In): Sent ~60 days before event
            - Email 4 (Last Call): Sent ~30 days after Program Fit Check-In if no response
            
            ⚠️⚠️⚠️ CRITICAL: Introduction Email and Email 1: Intro MUST BE COMPLETELY DIFFERENT ⚠️⚠️⚠️
            - NO DUPLICATION: Different subject lines (different words/angles, not synonyms)
            - NO DUPLICATION: Different body content (different hooks, examples, phrasing, closing)
            - Different purpose: Introduction Email = first outreach; Email 1: Intro = follow-up relationship building
            - Use different success stories, credentials, and proof points in each

            DSA EMAIL STRUCTURE REQUIREMENTS:
            ⚠️ CRITICAL: ALL emails MUST start with "Dear Organiser Team" - never use "[Contact Name]" or any contact name placeholders
            - Email 1 (Intro): Start with "Dear Organiser Team,"; Subject ≤ 15 words; Hook paragraph (pain); Authority paragraph (proof); Benefits paragraph; CTA paragraph; Signature with phone and email
            - Introduction Email: Start with "Dear Organiser Team,"; Subject ≤ 15 words; Hook paragraph (pain); Authority paragraph (proof); Benefits paragraph; CTA paragraph; Signature with phone and email
            - Email 2 (Value Reminder): Start with "Dear Organiser Team,"; Subject ≤ 12 words; Opening line framing value; Value paragraph; 3 outcome bullets (outcomes, not features); CTA paragraph; Signature
            - Email 3 (Program Fit Check-In): Start with "Dear Organiser Team,"; Subject; Reaffirm-fit paragraph; Options paragraph with 2–3 customization options; Assets checklist line; Close with CTA
            - Email 4 (Last Call): Start with "Dear Organiser Team,"; Subject; Need/fit restatement; Benefits + assets line; Alternative support line (breakout/waitlist/virtual); CTA and signature

            DSA EMAIL CONTENT REQUIREMENTS:
            - Email 1 (Intro): Establish pain, credibility, and clear benefit; secure a call; Pain → Authority → Benefits → CTA; specific examples; single CTA line; signature with direct phone; word count: 270-330 words, 4-5 paragraphs; subject ≤ 15 words; Hook paragraph (pain); Authority paragraph (proof); Benefits paragraph; CTA paragraph; Signature with phone and email
            - Email 2: Three bullets are outcomes, not features; one-sentence CTA; subject meets limit
            - Email 3: List 2–3 customization options; list assets available; invite template handoff
            - Email 4: Gentle urgency; offer alternate support; 1 CTA; clear signature

            CRITICAL EMAIL PERSONALIZATION REQUIREMENTS:
            - NEVER use "[Your Name]" or "[Speaker Name]" placeholders in email content
            - ALWAYS use the actual speaker name from the SPEAKER context in all email content
            - ALWAYS use the actual speaker phone number from SPEAKER context in signatures
            - ALWAYS use the actual speaker email from SPEAKER context in signatures
            - Write emails as if the speaker is personally writing them with their real name and contact info
            - Example: Instead of "Best regards, [Your Name]", write "Best regards, John Smith"
            - Example: Instead of "(*************", use the actual speaker phone number
            - Example: Instead of "<EMAIL>", use the actual speaker email

            DSA EMAIL SUBJECT LINE REQUIREMENTS:
            - Email 1 (Intro): Clear + benefit-driven (8–12 words)
            - Introduction Email: Clear + benefit-driven (8–12 words)
            - Email 2: [Speaker Name] + benefit statement
            - Email 3: "Content alignment for [Event Name]"
            - Email 4: "Last call to finalize [Speaker Name] for [Event Name]"

            DSA WORD COUNT REQUIREMENTS (MANDATORY - FOLLOW EXACTLY):
            ⚠️⚠️⚠️ MANDATORY: All word counts defined below are COMPULSORY and NON-NEGOTIABLE. 
            You MUST count words for EVERY section and ensure it matches the specified range EXACTLY.
            Do NOT approximate - count accurately. If word count is wrong, rewrite until it matches.
            Word count compliance is REQUIRED - output will be rejected if not met.
            
            FOLLOW-UP EMAIL SEQUENCE WORD COUNTS (CRITICAL - COUNT AND VERIFY EACH):
            - Email 1: Intro (in Follow-Up Email Sequence): Body MUST be 270-330 words - count and verify EXACTLY
            
            ⚠️⚠️⚠️ SPECIAL ATTENTION REQUIRED FOR EMAILS 2, 3, AND 4 ⚠️⚠️⚠️
            These three emails MUST have word counts between 220-280 words. Pay extra attention to these:
            - Email 2: Value Reminder (in Follow-Up Email Sequence): Body MUST be 220-280 words EXACTLY - count every word, verify it's between 220-280 (inclusive). If less, add more. If more, remove content. This is MANDATORY.
            - Email 3: Program Fit Check-In (in Follow-Up Email Sequence): Body MUST be 220-280 words EXACTLY - count every word, verify it's between 220-280 (inclusive). If less, add more. If more, remove content. This is MANDATORY.
            - Email 4: Last Call (in Follow-Up Email Sequence): Body MUST be 220-280 words EXACTLY - count every word, verify it's between 220-280 (inclusive). If less, add more. If more, remove content. This is MANDATORY.
            
            - Introduction Email: 270-330 words, 4-5 paragraphs
            - Email 2 (Value Reminder): 220-280 words, 4 paragraphs
            - Email 3 (Program Fit Check-In): 220-280 words, 4 paragraphs
            - Email 4 (Last Call): 220-280 words, 4-5 paragraphs
            - Voicemail Script: 90-120 words, exactly 1 paragraph
            - Challenge descriptions: 25-60 words each
            - SWOT bullets: 3-6 items each, 6-16 words per bullet
            - SMART Analysis: 200-300 words total, 5 subsections (S, M, A, R, T)
            - Proposed Keynote Title: 7-12 words
            - Short Description: 150-200 words, 1 paragraph
            - Detailed Description: 500-600 words, 5-7 paragraphs
            - Learning Objectives: 4-6 items, ≤15 words each, MUST start with action verb
            - Attendee Benefits: 3-5 bullets, ≤15 words each
            - Key Challenges: 3-5 bullets, ≤20 words each
            - Tailored Bio: 70-100 words, 1 paragraph
            - Long Bio: 300-400 words, 4-5 paragraphs
            - NYT Article: 450-550 words, 5-7 paragraphs

            LEARNING OBJECTIVES REQUIREMENTS:
            - Each objective MUST start with an action verb (identify, apply, demonstrate, create, analyze, evaluate, etc.)
            - Use Bloom's Taxonomy verbs for appropriate cognitive level
            - Focus on what attendees will DO, not just what they'll know
            - Each objective should be measurable and observable
            - No compound objectives (one action per objective)
            - Examples: "Identify warning signs", "Apply practical tools", "Demonstrate techniques", "Create action plans"

            PROHIBITIONS:
            - No em dashes
            - No emojis
            - No buzzwords or corporate jargon
            - No clichés
            - No generic claims
            - No vague offers
            - No pressure language
            - No guilt language
            - No false scarcity
            - No links dump
            - No em dashes
            - No jargon
            - No clichés

            DYNAMIC FIELDS REQUIRED:
            - [Event Name], [Contact Name], [Event Date], [Audience], [Keynote Title]
            - [Speaker Name], [Speaker Role], [Event Theme], [Speaker Story]
            - [Primary Topic], [Contact Info], [Credentials], [Industry Context]
            - [Speaker Phone]

            SPEAKER CONTACT INFORMATION USAGE:
            - ALWAYS use the actual speaker phone number from SPEAKER context in email signatures
            - ALWAYS use the actual speaker email from SPEAKER context in email signatures
            - NEVER use placeholder phone numbers like "(*************"
            - NEVER use placeholder emails like "<EMAIL>"
            - NEVER use "[Your Name]" - always use the actual speaker name

            CONTACT PERSON GREETING REQUIREMENTS:
            ⚠️⚠️⚠️ CRITICAL: ALL emails MUST start with "Dear Organiser Team" - NEVER use "[Contact Name]" or any contact name placeholders ⚠️⚠️⚠️
            - ALWAYS use "Dear Organiser Team" as the greeting in ALL emails (Introduction Email, Email 1, Email 2, Email 3, Email 4)
            - NEVER use "Dear [Contact Name]" or any variation with contact names
            - NEVER use "Dear Event Organizer" or "Dear Team" - use "Dear Organiser Team" specifically
            - NEVER use placeholder names like "Dear Ms. Doe" or "Dear Sarah"
            - The greeting MUST be exactly "Dear Organiser Team" for consistency across all emails
            - Example: Start every email with "Dear Organiser Team," followed by the email content

            VOICE MATCHING INSTRUCTIONS BY SECTION (MUST USE METRICS):
            - Email 1 (Intro): Use metrics to match sentence length (average_sentence_length), tone (dominant_tone), pacing (speech_rate_wpm, pause_ratio), and expressiveness (pitch_variance). Write in speaker's exact measured style - match their greeting, tone, and closing based on metrics; establish pain, credibility, and clear benefit; secure a call; USE ACTUAL SPEAKER NAME AND CONTACT INFO, not placeholders
            - Introduction Email: Use metrics to match sentence length, tone, pacing, and expressiveness. Write in speaker's exact measured email style - match their greeting, tone, and closing based on metrics; USE ACTUAL SPEAKER NAME AND CONTACT INFO, not placeholders
            - Follow-Up Emails: Use metrics consistently across all emails - match sentence length, tone, pacing, punctuation density, and expressiveness from metrics. Maintain measured voice across all emails - same style, humor, warmth level from metrics; USE ACTUAL SPEAKER NAME AND CONTACT INFO in all content, not placeholders
            - Voicemail Script: Use audio_metrics and video_metrics to match natural rhythm, pacing (speech_rate_wpm), pauses (pause_ratio), and expressiveness. Write as speaker would actually speak based on their measured metrics - their natural rhythm and phrases from metrics
            - Customized RFP: Use text_metrics to match sentence structure, tone, and pacing. All descriptions must sound like speaker wrote them - match their measured expertise voice using metrics
            - Speaker Bios: Use text_metrics (average_sentence_length, dominant_tone, punctuation_density) to write in speaker's authentic measured voice - not generic bio tone, but their actual measured speaking style from metrics
            - NYT Article: Use text_metrics to match sentence structure, rhythm_score, punctuation_density, and tone. Match speaker's measured writing style for articles - their measured voice from metrics, not journalistic voice

            ============================================
            CRITICAL REMINDER:
            ============================================
            These metrics are REAL MEASURED VALUES for this specific speaker.
            Use your knowledge and understanding of communication patterns to interpret these values and generate content that matches them precisely.
            DO NOT write randomly or use generic voice patterns.
            The PDF/DSA you create MUST be calibrated to these specific metrics.
            Every sentence, word, and punctuation mark should reflect these measurements.
            ============================================
            
            CRITICAL VALIDATION:
            - MANDATORY: Enforce exact word counts per section - all word counts defined are COMPULSORY and must be followed
            - Validate paragraph counts per section
            - Ensure all dynamic fields populated
            - Reject outputs outside word ranges - word counts are MANDATORY
            - Require [Event Theme] references where specified
            - VALIDATE VOICE MATCHING: Every section must sound authentically like the speaker based on METRICS
            - VALIDATE METRICS USAGE: Check that sentence lengths match average_sentence_length, tone matches dominant_tone, pacing matches speech_rate_wpm and pause_ratio, punctuation matches punctuation_density
            - If metrics are provided but not reflected in content, the output is INCORRECT - rewrite to match metrics
            - Output MUST be valid JSON only
            - Email 1 (Intro) Developer Checks: Reject if word count out of range (270-330); enforce 1 CTA; verify greeting is "Dear Organiser Team" (not "[Contact Name]" or any placeholder); verify [Speaker Phone] present
            - FINAL CHECK: Before outputting, verify that:
              1. ALL word counts are met (all defined word counts are MANDATORY)
              2. ALL provided metrics are reflected in the generated content. If any metric is ignored, regenerate the content to match the metrics exactly.
              3. ALL emails start with "Dear Organiser Team" - NEVER "[Contact Name]" or any contact name placeholders
              4. All email greetings are exactly "Dear Organiser Team" - verify no placeholders are used
              5. MANDATORY: "Introduction Email" and "Email 1: Intro" COMPARISON CHECK:
                 a. Read both subject lines side by side - they MUST be completely different (different words, not synonyms)
                 b. Read both body paragraphs side by side - they MUST have different opening hooks, different examples, different phrasing
                 c. If ANY sentence, phrase, or structure is the same or similar, REWRITE one or both emails to be completely unique
                 d. If subject lines use similar words or angles, REWRITE to use completely different approaches
                 e. This is a CRITICAL validation - output will be rejected if these emails are duplicates or similar
            """
        prompt = (
            template
            .replace('<<SPEAKER_JSON>>', speaker_json)
            .replace('<<OPPORTUNITY_JSON>>', opportunity_json)
            .replace('<<VOICEPRINT_METRICS_JSON>>', voiceprint_metrics_json)
            .replace('<<SPEAKER_ID>>', j_speaker_id)
            .replace('<<OPPORTUNITY_ID>>', j_opportunity_id)
            .replace('<<SPEAKER_NAME>>', j_speaker_name)
            .replace('<<SPEAKER_EMAIL>>', j_speaker_email)
            .replace('<<SPEAKER_PHONE>>', j_speaker_phone)
            .replace('<<ORG>>', j_org)
            .replace('<<TITLE>>', j_title)
            .replace('<<NOW_ISO>>', j_now)
            .replace('[ORGANIZATION_NAME]', org_val or '[ORGANIZATION_NAME]')
            .replace('[SPEAKER_NAME]', speaker_name_val or '[SPEAKER_NAME]')
            .replace('[SPEAKER_PHONE]', speaker_phone_val or '[SPEAKER_PHONE]')
            .replace('[EVENT_START_DATE_MINUS_5_5_MONTHS]', event_date_minus_5_5_months or '[EVENT_START_DATE_MINUS_5_5_MONTHS]')
            .replace('[EVENT_START_DATE_MINUS_4_MONTHS]', event_date_minus_4_months or '[EVENT_START_DATE_MINUS_4_MONTHS]')
        )
        return prompt

    # -----------------------------
    # JSON Parsing Helpers
    # -----------------------------
    
    def _parse_llm_json_response(self, raw_response: str, speaker_id: int, opportunity_id: int) -> Dict[str, Any]:
        """Robustly parse LLM JSON response with multiple fallback strategies."""
        # Step 1: Basic cleaning
        cleaned = self._clean_json_response(raw_response)
        # Step 2: Try direct parsing
        try:
            data = json.loads(cleaned)
            return data
        except json.JSONDecodeError as e:
            logger.error(f"First JSON parse failed: {e}")

        # Step 3: Fix common JSON issues
        try:
            fixed = self._fix_common_json_issues(cleaned)
            data = json.loads(fixed)
            return data
        except json.JSONDecodeError as e2:
            logger.error(f"Second JSON parse failed: {e2}")
        
        # Step 4: Use DSA helper methods
        try:
            sanitized = self.dsa_helper.sanitize_json_strings(cleaned)
            sanitized = self.dsa_helper.fix_incomplete_strings(sanitized)
            sanitized = self.dsa_helper.fix_truncated_json(sanitized)
            sanitized = self.dsa_helper.fix_extra_data_error(sanitized)
            data = json.loads(sanitized)
            return data
        except json.JSONDecodeError as e3:
            logger.error(f"Third JSON parse failed: {e3}")
            logger.error(f"Sanitized (first 500 chars): {sanitized[:500]}")
        
        # Step 5: Ask LLM to repair the JSON
        try:
            repaired = self._repair_json_with_llm(raw_response)
            data = json.loads(repaired)
            return data
        except Exception as e4:
            logger.error(f"LLM repair failed: {e4}")
        
        # Step 6: Use fallback data structure
        logger.warning("All JSON parsing attempts failed, using fallback data structure")
        return self.dsa_helper.create_fallback_data_structure(speaker_id, opportunity_id)
    
    def _clean_json_response(self, raw_response: str) -> str:
        """Clean raw LLM response to extract JSON."""
        cleaned = raw_response.strip()
        
        # Remove markdown code fences
        if cleaned.startswith("```json"):
            cleaned = cleaned[7:]
        if cleaned.startswith("```"):
            cleaned = cleaned[3:]
        if cleaned.endswith("```"):
            cleaned = cleaned[:-3]
        cleaned = cleaned.replace("```json", "").replace("```", "").strip()
        
        # Try to isolate a single JSON object
        first_brace = cleaned.find("{")
        last_brace = cleaned.rfind("}")
        if first_brace != -1 and last_brace != -1 and last_brace > first_brace:
            cleaned = cleaned[first_brace:last_brace + 1]
        
        return cleaned
    
    def _fix_common_json_issues(self, json_str: str) -> str:
        """Fix common JSON issues like incomplete strings and extra data."""
        # First, try to fix extra data by trimming to last complete object
        json_str = self._trim_to_complete_json(json_str)
        
        lines = json_str.split('\n')
        fixed_lines = []
        
        for line in lines:
            line = line.strip()
            if line:
                # Fix incomplete strings that don't end with quotes
                if '"' in line and not line.endswith(('"', ',', ']', '}')):
                    quote_count = line.count('"')
                    if quote_count % 2 == 1:  # Odd number of quotes means incomplete
                        line = line + '"'
                
                # Fix common incomplete patterns
                incomplete_patterns = [
                    'speaker', 'volunteer', 'reach', 'marketing', 'engagement', 
                    'topics', 'content', 'audience', 'conference', 'event',
                    'speaking', 'presentation', 'workshop', 'session'
                ]
                
                for pattern in incomplete_patterns:
                    if line.endswith(pattern) and not line.endswith(f'{pattern}"'):
                        line = line + '"'
                        break
                
                fixed_lines.append(line)
            else:
                fixed_lines.append(line)
        
        return '\n'.join(fixed_lines)
    
    def _trim_to_complete_json(self, json_str: str) -> str:
        """Trim JSON string to the last complete closing brace."""
        last_brace_idx = json_str.rfind('}')
        if last_brace_idx != -1:
            return json_str[:last_brace_idx + 1]
        return json_str
    
    def _repair_json_with_llm(self, raw_response: str) -> str:
        """Ask LLM to repair broken JSON."""
        repair_prompt = (
            "Return ONLY a valid JSON object. Fix any broken JSON below without changing keys or order. "
            "CRITICAL: All string values must be properly quoted and closed. "
            "If a string value is incomplete or has unescaped newlines, complete it properly or set to null. "
            "Ensure all JSON syntax is correct with proper commas, braces, and quotes. "
            "No markdown, no commentary, just valid JSON.\n\n"
            "BROKEN_JSON:\n" + raw_response[:8000]
        )
        
        repair_resp = openai_client.chat.completions.create(
            model=config.OPENAI_MODEL,
            messages=[{"role": "user", "content": repair_prompt}],
            temperature=0
        )
        
        repaired = (repair_resp.choices[0].message.content or "").strip()
        
        # Clean code fences if present
        if repaired.startswith("```json"):
            repaired = repaired[7:]
        if repaired.startswith("```"):
            repaired = repaired[3:]
        if repaired.endswith("```"):
            repaired = repaired[:-3]
        repaired = repaired.replace("```json", "").replace("```", "").strip()
        
        # Isolate to object braces
        fb = repaired.find('{')
        lb = repaired.rfind('}')
        if fb != -1 and lb != -1 and lb > fb:
            repaired = repaired[fb:lb+1]
        
        return repaired

    # -----------------------------
    # Public API
    # -----------------------------
    def generate_details(self, speaker_id: int, opportunity_id: int) -> Dict[str, Any]:
        """Generate digital form details for a speaker/opportunity pair using LLM."""
        # 1) Fetch context from MySQL database
        speaker_ctx = None
        opp_ctx = None
        mysql_speaker = self._get_speaker_from_mysql(speaker_id)
        if mysql_speaker:
            speaker_ctx = mysql_speaker
        else:
            raise RuntimeError(f"Speaker {speaker_id} not found in MySQL database")
        mysql_opp = self._get_opportunity_from_mysql(opportunity_id)
        if mysql_opp:
            opp_ctx = mysql_opp
        else:
            raise RuntimeError(f"Opportunity {opportunity_id} not found in MySQL database")

        # 2) Fetch voiceprint metrics from Speaker model
        voiceprint_metrics = {}
        
        voiceprint_metrics = self._get_speaker_voiceprint_metrics(speaker_id)
        print(f"Voiceprint metrics: {voiceprint_metrics}")
        
        # 3) Extract organization and speaker names for key mapping
        org_name = (opp_ctx.get('organization') or "Unknown Organization")
        speaker_name = (speaker_ctx.get('name') or speaker_ctx.get('Name') or "Unknown Speaker")
        # 4) Build prompt with voiceprint metrics
        prompt = self._build_prompt(speaker_ctx, opp_ctx, org_name, speaker_name, voiceprint_metrics)
        # 5) Call LLM
        try:
            response = openai_client.chat.completions.create(
                model=config.OPENAI_MODEL,
                messages=[{"role": "user", "content": prompt}],
                temperature=config.OPENAI_TEMPERATURE,
            )
        except Exception as e:
            logger.error(f"OpenAI API error: {e}")
            raise RuntimeError(f"LLM call failed: {e}")
        raw = (response.choices[0].message.content or "").strip()
        # 6) Clean and parse JSON with robust error handling
        data = self._parse_llm_json_response(raw, speaker_id, opportunity_id)
        # Ensure presence of exact top-level keys; if missing, set to appropriate defaults
        required_top_keys = [
            "Top 3 Attendee Challenges",
            "SWOT Analysis for [ORGANIZATION_NAME]",
            "SMART Analysis",
            "Previous Two Annual [ORGANIZATION_NAME] Conferences – Keynote & Breakout Speaker Research",
            "Introduction Email",
            "Follow-Up Email Sequence",
            "20–30 Second Voicemail Script",
            "Customized RFP",
            "500-Word New York Times-Style Article",
            "_metadata",
        ]
        if not isinstance(data, dict):
            data = {}
        # Use passed organization and speaker names, or extract from context as fallback
        if not org_name:
            org_name = (opp_ctx.get('organization') or "Unknown Organization")
        if not speaker_name:
            speaker_name = (speaker_ctx.get('name') or "Unknown Speaker")
        
        # Create a mapping of placeholder keys to actual keys
        key_mapping = {
            "SWOT Analysis for [ORGANIZATION_NAME]": f"SWOT Analysis for {org_name}",
            "Previous Two Annual [ORGANIZATION_NAME] Conferences – Keynote & Breakout Speaker Research": f"Previous Two Annual {org_name} Conferences – Keynote & Breakout Speaker Research"
        }
        
        # Ensure all required keys exist with proper names
        for key in required_top_keys:
            actual_key = key_mapping.get(key, key)
            if actual_key not in data:
                data[actual_key] = None

        # Always enforce metadata IDs
        meta = data.get("_metadata") or {}
        if not isinstance(meta, dict):
            meta = {}
        meta["speaker_id"] = speaker_id
        meta["opportunity_id"] = opportunity_id
        meta.setdefault("generated_at", datetime.utcnow().isoformat())
        
        # Ensure speaker_name, event_title, and organization are set from context
        if not meta.get("speaker_name") or meta.get("speaker_name") == "Unknown Speaker":
            meta["speaker_name"] = speaker_ctx.get('name') or speaker_ctx.get('Name') or "Unknown Speaker"
        if not meta.get("event_title") or meta.get("event_title") == "Unknown Event":
            meta["event_title"] = opp_ctx.get('title') or "Unknown Event"
        if not meta.get("organization") or meta.get("organization") == "Unknown Organization":
            meta["organization"] = opp_ctx.get('organization') or "Unknown Organization"
        if not meta.get("speaker_email"):
            meta["speaker_email"] = speaker_ctx.get('email') or speaker_ctx.get('Email')
        if not meta.get("speaker_phone"):
            meta["speaker_phone"] = speaker_ctx.get('phone_number') or speaker_ctx.get('Phone Number')
        
        data["_metadata"] = meta

        # Nullify empty values recursively using DSA helper
        data = self.dsa_helper.nullify_empty_values(data)
        return data