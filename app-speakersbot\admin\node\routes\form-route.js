const formController = require("../controllers/form-controller");
const router = require("express").Router();

module.exports = (app) => {

    // ------------------------- form-type -------------------------

    // initial request to get avilable form types ordered by priorities
    router.get("/priority", formController.getFormswithPriorities);

    // create form with questions(fields)
    router.post("/create", formController.createForm);

    // update form priorities
    router.put("/update/priority", formController.updateFormPriority);

    // update form (only title only)
    router.put("/update/:id", formController.updateForm);

    // delete form type (soft delete)
    router.delete("/:id", formController.deleteForm);


    // ------------------------- form-question -------------------------

    // get all quetions(fields) for a form
    router.get("/:formId", formController.getFormFields);

    // create new question(field)
    router.post("/field/:formId", formController.createFormField);

    // update question(field) and there options(ex. add new options)
    router.put("/field/:id", formController.updateFormField);

    // delete question(field) (soft delete)
    router.delete("/field/:id", formController.deleteFormField);

    app.use("/admin/api/v1/form", router);

}