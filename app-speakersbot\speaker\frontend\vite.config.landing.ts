import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
import { componentTagger } from "lovable-tagger";

// Landing page specific configuration
export default defineConfig(({ mode }) => ({
  base: "./",
  server: {
    host: "::",
    port: 8081, // Different port for landing page
  },
  build: {
    outDir: "../backend/dist/landing",
    rollupOptions: {
      input: {
        main: path.resolve(__dirname, "index.html"),
      },
    },
  },
  plugins: [react(), mode === "development" && componentTagger()].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
