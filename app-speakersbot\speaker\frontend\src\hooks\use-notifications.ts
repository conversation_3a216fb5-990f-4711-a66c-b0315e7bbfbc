import { useState, useEffect } from 'react';
import { toast } from '@/hooks/use-toast';

export interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'new_opportunity' | 'deadline' | 'referral' | 'meeting' | 'success' | 'info';
  timestamp: Date;
  read: boolean;
}

export function useNotifications() {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [hasUnread, setHasUnread] = useState(false);

  // Simulate receiving notifications
  useEffect(() => {
    const simulateNotifications = () => {
      const sampleNotifications: Notification[] = [
        {
          id: '1',
          title: 'New Opportunity Available',
          message: 'AI Conference 2024 matches your expertise in Machine Learning',
          type: 'new_opportunity',
          timestamp: new Date(Date.now() - 2 * 60 * 1000), // 2 minutes ago
          read: false
        },
        {
          id: '2', 
          title: 'Application Deadline Passed',
          message: 'React Summit application deadline has passed',
          type: 'deadline',
          timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
          read: false
        },
        {
          id: '3',
          title: 'Referral Successful',
          message: 'Your referral for <PERSON> has been accepted',
          type: 'referral',
          timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
          read: false
        },
        {
          id: '4',
          title: 'Meeting Scheduled',
          message: 'Affiliate meeting with TechCorp scheduled for tomorrow at 2 PM',
          type: 'meeting',
          timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
          read: false
        }
      ];

      setNotifications(sampleNotifications);
      setHasUnread(true);
    };

    simulateNotifications();
  }, []);

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
      read: false
    };

    setNotifications(prev => [newNotification, ...prev]);
    setHasUnread(true);

    // Show toast notification
    toast({
      title: notification.title,
      description: notification.message,
      duration: 5000,
    });
  };

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
    
    // Check if there are any unread notifications left
    const updatedNotifications = notifications.map(notif => 
      notif.id === id ? { ...notif, read: true } : notif
    );
    setHasUnread(updatedNotifications.some(notif => !notif.read));
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, read: true }))
    );
    setHasUnread(false);
  };

  const clearNotification = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const unreadCount = notifications.filter(notif => !notif.read).length;

  return {
    notifications,
    hasUnread,
    unreadCount,
    addNotification,
    markAsRead,
    markAllAsRead,
    clearNotification
  };
}