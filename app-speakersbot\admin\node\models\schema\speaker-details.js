const { DataTypes } = require("sequelize");
const connection = require("../connection");


const SpeakerDetails = connection.define("SpeakerDetails", {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: 'Primary key for the speaker details record',
    },
    speaker_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Foreign key referencing the speaker',
    },
    field_id: {
        type: DataTypes.STRING,
        allowNull: false,
        references:{ 
            model:'form_questions',
            key:'field_id'
        },
        comment: 'Identifier for the form field',
    },
    key: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: 'Key or name of the detail',
    },
    value: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: 'Value of the detail',
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
        comment: 'Record creation timestamp',
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: false,   
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
        comment: 'Record last update timestamp',
    },
    deleted_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Record deletion timestamp(soft delete)',
    }
}, {
    tableName: 'speaker_details',
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    paranoid: true,
    deletedAt: "deleted_at", 

});

module.exports = SpeakerDetails;   