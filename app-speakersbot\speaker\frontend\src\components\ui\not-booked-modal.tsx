import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON>alogT<PERSON>le, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface NotBookedModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (reason: string, note: string) => void;
  title: string;
}

export function NotBookedModal({ 
  isOpen, 
  onClose, 
  onSubmit, 
  title
}: NotBookedModalProps) {
  const [selectedReason, setSelectedReason] = useState('');
  const [note, setNote] = useState('');

  const reasons = [
    { value: 'competing_priorities', label: 'Competing priorities or scheduling conflicts' },
    { value: 'compensation_mismatch', label: 'Compensation not aligned with expectations' },
    { value: 'event_fit', label: 'Event not a good fit for expertise/brand' }
  ];

  const handleSubmit = () => {
    if (selectedReason) {
      onSubmit(selectedReason, note);
      setSelectedReason('');
      setNote('');
      onClose();
    }
  };

  const handleClose = () => {
    setSelectedReason('');
    setNote('');
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
        </DialogHeader>
        <div className="space-y-6 py-4">
          {/* Radio Button Options */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">Why wasn't this opportunity booked?</Label>
            <RadioGroup 
              value={selectedReason} 
              onValueChange={setSelectedReason}
              className="space-y-3"
            >
              {reasons.map((reason) => (
                <div key={reason.value} className="flex items-start space-x-2">
                  <RadioGroupItem 
                    value={reason.value} 
                    id={reason.value}
                    className="mt-0.5" 
                  />
                  <Label 
                    htmlFor={reason.value} 
                    className="text-sm leading-relaxed cursor-pointer"
                  >
                    {reason.label}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>

          {/* Optional Note Field */}
          <div className="space-y-2">
            <Label htmlFor="note" className="text-sm font-medium">
              Additional notes (optional)
            </Label>
            <Textarea
              id="note"
              placeholder="Any additional context or details..."
              value={note}
              onChange={(e) => setNote(e.target.value)}
              className="min-h-[80px] resize-none"
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={handleClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={!selectedReason}>
            Submit
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}