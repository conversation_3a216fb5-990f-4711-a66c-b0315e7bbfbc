variables:
  NODE_VERSION: "22"

workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      changes:
        - admin/frontend/**/*
        - admin/node/**/*
    - if: '$CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"'
      changes:
        - admin/frontend/**/*
        - admin/node/**/*

# Build stage optimized for development
build:
  stage: build
  image: node:${NODE_VERSION}-alpine
  before_script:
    - apk add --no-cache zip python3 make g++
  script:
    - echo "Building admin frontend..."
    - cd admin/frontend
    - rm -rf node_modules .vite .vite-temp dist
    
    # Use development environment variables
    - echo "VITE_API_BASE_URL=\"$DEV_ADMIN_VITE_API_BASE_URL\"" > .env
    - echo "VITE_BASE_URL=\"$DEV_ADMIN_VITE_BASE_URL\"" >> .env
    - echo "VITE_ENVIRONMENT=\"development\"" >> .env
    - npm install
    - npm run build:dev
    
    - echo "Admin build completed"
    - cd ../node
    
    # Create development deployment package
    - echo "Creating admin backend package..."
    - zip -r ../../dev-admin-deploy.zip . -x "admin/frontend/node_modules/*" ".git/*" "node_modules/*" "*.log" "admin/frontend/dist/*"
  artifacts:
    paths:
      - dev-admin-deploy.zip
    expire_in: 1 hour

# Development deployments
deploy:
  stage: deploy
  image: amazonlinux:latest
  dependencies:
    - build
  before_script:
    - yum install -y unzip aws-cli
  script:
    # Configure AWS credentials
    - aws configure set aws_access_key_id $DEV_AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $DEV_AWS_SECRET_ACCESS_KEY
    - aws configure set default.region $DEV_AWS_DEFAULT_REGION

    # Upload to S3
    - echo "Uploading to S3..."
    - aws s3 cp dev-admin-deploy.zip s3://$DEV_S3_BUCKET/dev-admin-deploy-${CI_COMMIT_SHORT_SHA}.zip
    
    # Deploy to Elastic Beanstalk
    - echo "Deploying to Elastic Beanstalk..."
    - export VERSION_LABEL=admin-dev-${CI_COMMIT_REF_NAME}-$(date +%s)
    - aws elasticbeanstalk create-application-version --application-name $DEV_ADMIN_EB_APPLICATION_NAME --version-label $VERSION_LABEL --source-bundle S3Bucket=${DEV_S3_BUCKET},S3Key=dev-admin-deploy-${CI_COMMIT_SHORT_SHA}.zip
    - aws elasticbeanstalk update-environment --environment-name $DEV_ADMIN_EB_ENVIRONMENT_NAME --version-label $VERSION_LABEL

    # Health check
    - echo "Checking deployment health..."
    - sleep 15
    - echo "Admin deployment completed successfully"
