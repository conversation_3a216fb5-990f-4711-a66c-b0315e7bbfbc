import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Responsive<PERSON><PERSON><PERSON> } from "recharts";
const data = [{
  date: "Aug 18",
  accepted: 8,
  rejected: 5,
  interested: 4
}, {
  date: "Aug 19",
  accepted: 11,
  rejected: 7,
  interested: 6
}, {
  date: "Aug 20",
  accepted: 9,
  rejected: 4,
  interested: 8
}, {
  date: "Aug 21",
  accepted: 13,
  rejected: 6,
  interested: 5
}, {
  date: "Aug 22",
  accepted: 16,
  rejected: 9,
  interested: 7
}, {
  date: "Aug 23",
  accepted: 14,
  rejected: 8,
  interested: 6
}, {
  date: "Aug 24",
  accepted: 12,
  rejected: 8,
  interested: 6
}, {
  date: "Aug 25",
  accepted: 10,
  rejected: 6,
  interested: 8
}, {
  date: "Aug 26",
  accepted: 15,
  rejected: 12,
  interested: 5
}, {
  date: "Aug 27",
  accepted: 20,
  rejected: 10,
  interested: 12
}, {
  date: "Aug 28",
  accepted: 18,
  rejected: 15,
  interested: 7
}, {
  date: "Aug 29",
  accepted: 22,
  rejected: 8,
  interested: 9
}, {
  date: "Aug 30",
  accepted: 25,
  rejected: 10,
  interested: 6
}, {
  date: "Aug 31",
  accepted: 19,
  rejected: 12,
  interested: 8
}, {
  date: "Sep 1",
  accepted: 17,
  rejected: 9,
  interested: 11
}];
const OpportunityTrendChart: React.FC = () => {
  return <div 
    className="w-full h-[400px] p-4 rounded-2xl shadow"
    style={{ 
      background: 'hsl(var(--card))',
      color: 'hsl(var(--card-foreground))'
    }}
  >
      
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={data} barCategoryGap="20%">
          <defs>
            <linearGradient id="greenGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor="#4CAF50" />
              <stop offset="100%" stopColor="#81C784" />
            </linearGradient>
            <linearGradient id="redGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor="#E53935" />
              <stop offset="100%" stopColor="#EF9A9A" />
            </linearGradient>
            <linearGradient id="goldGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor="#FFC107" />
              <stop offset="100%" stopColor="#FFE082" />
            </linearGradient>
          </defs>

          <XAxis dataKey="date" stroke="#888" fontSize={12} />
          <YAxis stroke="#888" />
          <Tooltip />
          <Legend />
          <Bar dataKey="accepted" stackId="a" fill="url(#greenGradient)" name="Accepted" />
          <Bar dataKey="rejected" stackId="a" fill="url(#redGradient)" name="Rejected" />
          <Bar dataKey="interested" stackId="a" fill="url(#goldGradient)" name="Interested" />
        </BarChart>
      </ResponsiveContainer>
    </div>;
};
export default OpportunityTrendChart;