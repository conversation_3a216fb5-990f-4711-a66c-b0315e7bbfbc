import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../store';
import type { Affiliate } from '../types';

// Define the base URL for your API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api';

export const affiliatesApi = createApi({
  reducerPath: 'affiliatesApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Get the token from the state
      const token = (getState() as RootState).auth?.token;
      
      // If we have a token, set the authorization header
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      
      // Set content type
      headers.set('content-type', 'application/json');
      
      return headers;
    },
  }),
  tagTypes: ['Affiliate'],
  endpoints: (builder) => ({
    getAffiliates: builder.query<
      { affiliates: Affiliate[]; summary?: { totalClick: number; totalSignup: number; totalConversion: number; overallConversion: string }; pagination?: { total: number; limit: number; page: number; totalPages: number } },
      { search?: string; filter?: { is_active?: string }; page?: number; limit?: number } | void
    >({
      query: (params = {}) => {
        const queryParams: Record<string, any> = {};
        if (params && typeof params === 'object') {
          if ((params as any).search) queryParams.search = (params as any).search;
          if ((params as any).filter) queryParams.filter = JSON.stringify((params as any).filter);
          if ((params as any).page) queryParams.page = (params as any).page;
          if ((params as any).limit) queryParams.limit = (params as any).limit;
        }
        return {
          url: '/affiliates',
          params: queryParams,
        };
      },
      transformResponse: (response: any) => {
        // Expected backend shape:
        // { status, message, data: { affiliates: [...], summary: {...} } }
        const affiliatesRaw = response?.data?.affiliates || [];
        const mapped: Affiliate[] = affiliatesRaw.map((a: any) => ({
          id: String(a?.id ?? a?.affiliate_id ?? ''),
          name: a?.user?.name || '—',
          email: a?.user?.email || '',
          affiliateId: String(a?.affiliate_id ?? a?.referral_code ?? ''),
          link: `https://speakerbot.com/signup?ref=${a?.referral_code ?? a?.affiliate_id ?? ''}`,
          clicks: Number(a?.click_count ?? 0),
          signups: Number(a?.signup_count ?? 0),
          conversions: Number(a?.conversion_count ?? 0),
          createdAt: a?.created_at || new Date().toISOString(),
          status: a?.user?.is_active,
        }));
        return { affiliates: mapped, summary: response?.data?.summary, pagination: response?.pagination || response?.data?.pagination };
      },
      providesTags: ['Affiliate'],
    }),
    
    getAffiliateById: builder.query<
      { affiliate: Affiliate; summary?: any; breakdown?: any },
      string | number
    >({
      query: (id) => `/affiliate/${id}`,
      transformResponse: (response: any) => {
        const a = response?.data?.affiliate || response?.data || response;
        const mapped: Affiliate = {
          id: String(a?.id ?? a?.affiliate_id ?? ''),
          name: a?.user?.name || '—',
          email: a?.user?.email || '',
          affiliateId: String(a?.affiliate_id ?? a?.referral_code ?? ''),
          link: `https://speakerbot.com/signup?ref=${a?.referral_code ?? a?.affiliate_id ?? ''}`,
          clicks: Number(a?.click_count ?? 0),
          signups: Number(a?.signup_count ?? 0),
          conversions: Number(a?.conversion_count ?? 0),
          createdAt: a?.created_at || new Date().toISOString(),
          status: a?.user?.is_active,
        };
        return { affiliate: mapped, summary: response?.data?.summary, breakdown: response?.data?.breakdown };
      },
      providesTags: (result, error, id) => [{ type: 'Affiliate', id }],
    }),

   

    getAffiliateSpeakers: builder.query<
      { speakers: Array<{ id: number; name: string; email: string; joinDate: string; status: string; converted: boolean; commission: number }>;
        pagination?: { total: number; limit: number; page: number; totalPages: number } },
      { affiliateId: string | number; page?: number; limit?: number; search?: string; filter?: Record<string, any>; dateRange?: { start_date: string; end_date: string } }
    >({
      query: ({ affiliateId, page = 1, limit = 10, search, filter, dateRange }) => ({
        url: `/affiliate/speakers/${affiliateId}`,
        params: { 
          page, 
          limit,
          ...(search ? { search } : {}),
          ...(filter ? { filter: JSON.stringify(filter) } : {}),
          ...(dateRange ? { dateRange: JSON.stringify(dateRange) } : {}),
        },
      }),
      transformResponse: (response: any) => {
        const raw = response?.data?.speakers || [];
        const speakers = raw.map((s: any) => ({
          id: Number(s.id),
          name: s.name,
          email: s.email,
          joinDate: s.created_at,
          status: s.status || 'Active',
          converted: Boolean(s.signup_count || s.conversion_count),
          commission: 0, // server does not provide; keep 0 or compute if needed
        }));
        return { speakers, pagination: response?.pagination };
      },
    }),

    // Export affiliates CSV
    exportAffiliatesCsv: builder.query<string, { search?: string; filter?: Record<string, any> } | void>({
      query: (args) => ({
        // Absolute URL to bypass API_BASE_URL since export lives under /admin/api/v1
        url: '/affiliates/export',
        method: 'GET',
        headers: { accept: 'text/csv' },
        params: args ? {
          ...(args.search ? { search: args.search } : {}),
          ...(args.filter ? { filter: JSON.stringify(args.filter) } : {}),
        } : undefined,
        // Ensure we read the response as text (CSV)
        responseHandler: (response) => (response as Response).text(),
      }),
    }),

    // Fetch a single affiliate inquiry request details by inquiry id
    getAffiliateInquiryRequest: builder.query<
      { speakerName: string | null; speakerNote: string | null; inquiryDate: string | null; inquiryTime: string | null; status: string | null },
      number | string
    >({
      query: (inquiryId) => `/affiliate-inquiries-request/${inquiryId}`,
      transformResponse: (response: any) => {
        const d = response?.data || {};
        return {
          speakerName: d.speakerName ?? null,
          speakerNote: d.speakerNote ?? null,
          inquiryDate: d.inquiryDate ?? null,
          inquiryTime: d.inquiryTime ?? null,
          status: d.status ?? null,
        };
      },
    }),

    // Fetch affiliate inquiries list for a given affiliate id
    getAffiliateInquiries: builder.query<
      { data: Array<{ id: number; affiliateId: number; speakerId: number; speakerName: string; status: string | null; inquiryDate: string | null }>; pagination?: { total: number; limit: number; page: number; totalPages: number } },
      { affiliateId: number | string; page?: number; limit?: number; search?: string; filter?: Record<string, any>; dateRange?: { start_date: string; end_date: string } }
    >({
      query: ({ affiliateId, page = 1, limit = 10, search, filter, dateRange }) => ({
        url: `/affiliate-inquiries/${affiliateId}`,
        params: { 
          page, 
          limit,
          ...(search ? { search } : {}),
          ...(filter ? { filter: JSON.stringify(filter) } : {}),
          ...(dateRange ? { dateRange: JSON.stringify(dateRange) } : {}),
        },
      }),
      transformResponse: (response: any) => {
        const raw = Array.isArray(response?.data) ? response.data : [];
        const data = raw.map((i: any) => ({
          id: Number(i.id),
          affiliateId: Number(i.affiliateId),
          speakerId: Number(i.speakerId),
          speakerName: String(i.speakerName || '—'),
          status: i.status,
          inquiryDate: i.inquiryDate,
        }));
        return { data, pagination: response?.pagination };
      },
    }),

    // Update affiliate inquiry status
    updateAffiliateInquiry: builder.mutation<
      { success: boolean; message: string },
      { inquiryId: number | string; note: string; status: string; inquiry_date?: string; inquiry_time?: string }
    >({
      query: ({ inquiryId, note, status, inquiry_date, inquiry_time }) => ({
        url: `/affiliate-inquiries/${inquiryId}`,
        method: 'POST',
        body: {
          note,
          status,
          inquiry_date,
          inquiry_time,
        },
      }),
      transformResponse: (response: any) => {
        return {
          success: response?.status === 'success' || response?.success === true,
          message: response?.message || 'Inquiry updated successfully',
        };
      },
      invalidatesTags: ['Affiliate'],
    }),

    // Get landing page data for affiliate
    getLandingPageData: builder.query<
      {
        id: number;
        name: string;
        company_legal: string | null;
        contact: {
          email: string;
          phone: string;
          website: string;
        };
        affiliate_program: {
          affiliate_link_url: string;
          commission_percent: string;
        };
        branding: {
          images: string[];
          font_url: string;
          logo_url: string;
          font_stack: string;
          text_color: string;
          title_color: string;
          button_color: string;
          background_color: string;
          button_text_color: string;
        };
        free_offer: {
          title: string;
          options: string[];
          xp_value: number;
          disclaimer: string;
          is_placeholder: boolean;
        };
        value_to_speakers: {
          takeaways: string[];
          challenges: string[];
          core_benefit: string;
          differentiator: string;
          overarching_offer: string;
        };
        admin_and_compliance: {
          promo_channels: string[];
          reach_estimate: string;
          digital_signature: string;
          compliance_link_url: string;
          business_type_entity: string;
          accept_terms_and_conditions: boolean;
        };
      },
      string | number
    >({
      query: (affiliateId) => `/affiliate/landing-page/${affiliateId}`,
      transformResponse: (response: any) => {
        return response?.data || response;
      },
      providesTags: (result, error, affiliateId) => [{ type: 'Affiliate', id: `landing-page-${affiliateId}` }],
    }),

    // Update landing page data for affiliate
    updateLandingPageData: builder.mutation<
      { success: boolean; message: string },
      { affiliateId: string | number; data: any }
    >({
      query: ({ affiliateId, data }) => ({
        url: `/affiliate/landing-page/${affiliateId}`,
        method: 'PUT',
        body: data,
      }),
     
      transformResponse: (response: any) => {
        return {
          success: response?.status === true || response?.success === true,
          message: response?.message || 'Landing page updated successfully',
        };
      },
      invalidatesTags: (result, error, { affiliateId }) => [
        { type: 'Affiliate', id: `landing-page-${affiliateId}` },
        { type: 'Affiliate', id: affiliateId }
      ],
    }),
  }),
});

export const {
  useGetAffiliatesQuery,
  useGetAffiliateByIdQuery,
  useGetAffiliateSpeakersQuery,
  useExportAffiliatesCsvQuery,
  useLazyExportAffiliatesCsvQuery,
  useGetAffiliateInquiryRequestQuery,
  useLazyGetAffiliateInquiryRequestQuery,
  useGetAffiliateInquiriesQuery,
  useUpdateAffiliateInquiryMutation,
  useGetLandingPageDataQuery,
  useUpdateLandingPageDataMutation,
} = affiliatesApi;
