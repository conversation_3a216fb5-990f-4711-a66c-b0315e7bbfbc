
const ApiResponse = require("../helpers/api-response");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const uploadFileToS3 = require("../helpers/upload-to-s3");
const {AffiliateUsersDetails} = require("../models");


exports.uploadFile = async (req, res, next) => {
    try {
        if (!req.file) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "No file uploaded");
        }

        const { affiliateId, type } = req.body;

        if (!affiliateId || !type) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "affiliateId and type are required");
        }

        // Find the affiliate user details
        const affiliateMetadata = await AffiliateUsersDetails.findOne({
            where: { affiliate_id:affiliateId },
            attributes: ['metadata', 'id']
        });

        if (!affiliateMetadata) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Affiliate not found");
        }

        // Upload file to S3 and get URL
        const url = await uploadFileToS3(req.file);
        
        // Parse metadata JSON
        let metadata = affiliateMetadata.metadata;
        if (typeof metadata === 'string') {
            try {
                metadata = JSON.parse(metadata);
            } catch (e) {
                metadata = {};
            }
        }

        // Update the correct field based on type value
        if (type === 'logo_url' || type === 'logo') {
            if (!metadata.branding) metadata.branding = {};
            metadata.branding.logo_url = url;
        } else if (type === 'digital_signature' || type === 'signature') {
            if (!metadata.admin_and_compliance) metadata.admin_and_compliance = {};
            metadata.admin_and_compliance.digital_signature = url;
        } else {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, `Unsupported type: ${type}`);
        }

        // Save updated metadata (use correct key for where clause)
    
        await AffiliateUsersDetails.update(
            { metadata: JSON.stringify(metadata) },
            { where: { affiliate_id: affiliateId } }
        );

        res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message: "File uploaded and metadata updated successfully", data: url }));
    } catch (err) {
        next(err);
    }
};