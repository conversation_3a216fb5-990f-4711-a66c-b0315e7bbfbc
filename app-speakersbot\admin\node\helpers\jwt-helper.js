const jwt = require('jsonwebtoken');
const CustomError = require('./custome-error');
const { RESPONSE_CODES } = require('./response-codes');


const JWT_SECRET = process.env.JWT_SECRET;

if (!JWT_SECRET) {
    throw new CustomError(RESPONSE_CODES.BAD_REQUEST, 'JWT_SECRET is not defined in environment variables');
}

const jwtHelper = {
    generateToken: (payload, expiresIn = '6h') => {
        return jwt.sign(payload, JWT_SECRET, { expiresIn });
    },
    verifyToken: (token) => {
        try {

            return jwt.verify(token, JWT_SECRET);

        } catch (err) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, 'Invalid or expired token');
        }
    }
};

module.exports = jwtHelper;
