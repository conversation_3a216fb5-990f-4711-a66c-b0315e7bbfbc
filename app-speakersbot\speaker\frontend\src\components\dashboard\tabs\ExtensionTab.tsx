import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Chrome, Download, Settings, Zap, Shield, Globe, Code, ExternalLink, CheckCircle, AlertCircle } from 'lucide-react';

export function ExtensionTab() {
  const [extensionStatus, setExtensionStatus] = useState<'not-installed' | 'installed' | 'connected'>('not-installed');

  const features = [
    {
      icon: Zap,
      title: 'Auto-fill Forms',
      description: 'Automatically fill speaker application forms with your profile data',
      status: 'active'
    },
    {
      icon: Globe,
      title: 'Opportunity Detection',
      description: 'Detect speaking opportunities on any website',
      status: 'active'
    },
    {
      icon: Shield,
      title: 'Smart Suggestions',
      description: 'AI-powered suggestions for better applications',
      status: 'coming-soon'
    },
    {
      icon: Chrome,
      title: 'Quick Apply',
      description: 'Apply to opportunities directly from any webpage',
      status: 'active'
    }
  ];

  const extensionStats = {
    formsAutoFilled: 23,
    opportunitiesDetected: 45,
    applicationsSubmitted: 12,
    timesSaved: '4.2 hours'
  };

  const detectedOpportunities = [
    {
      id: 1,
      title: 'React Conference 2024 - Call for Speakers',
      url: 'reactconf2024.com/speakers',
      detected: '2 hours ago',
      status: 'new'
    },
    {
      id: 2,
      title: 'DevOps Summit - Speaker Applications',
      url: 'devopssummit.com/cfp',
      detected: '1 day ago',
      status: 'applied'
    },
    {
      id: 3,
      title: 'Tech Innovation Meetup - Speaking Slots',
      url: 'techmeetup.com/speakers',
      detected: '3 days ago',
      status: 'dismissed'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Chrome Extension</h2>
          <p className="text-foreground-muted mt-1">Manage speaking opportunities directly from your browser</p>
        </div>
        <div className="flex items-center gap-2">
          <Badge 
            variant={extensionStatus === 'connected' ? 'default' : 'outline'}
            className="flex items-center gap-1"
          >
            {extensionStatus === 'connected' ? (
              <CheckCircle className="h-3 w-3" />
            ) : (
              <AlertCircle className="h-3 w-3" />
            )}
            {extensionStatus === 'connected' ? 'Connected' : 
             extensionStatus === 'installed' ? 'Installed' : 'Not Installed'}
          </Badge>
        </div>
      </div>

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid grid-cols-4 w-full max-w-md bg-surface-elevated">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="install">Install</TabsTrigger>
          <TabsTrigger value="detected">Detected</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Extension Status */}
          <Card className={`border-2 ${
            extensionStatus === 'connected' 
              ? 'bg-success/10 border-success/20' 
              : 'bg-warning/10 border-warning/20'
          }`}>
            <CardContent className="flex items-center justify-between p-6">
              <div className="flex items-center gap-4">
                <div className={`p-3 rounded-full ${
                  extensionStatus === 'connected' ? 'bg-success text-white' : 'bg-warning text-white'
                }`}>
                  <Chrome className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-foreground">
                    {extensionStatus === 'connected' ? 'Extension Connected' : 'Extension Not Connected'}
                  </h3>
                  <p className="text-foreground-muted">
                    {extensionStatus === 'connected' 
                      ? 'Your extension is working and syncing data' 
                      : 'Install the extension to unlock browser features'}
                  </p>
                </div>
              </div>
              {extensionStatus !== 'connected' && (
                <Button className="bg-primary hover:bg-primary-hover">
                  <Download className="h-4 w-4 mr-2" />
                  Install Extension
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <Card className="bg-surface border-border-subtle">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-primary">{extensionStats.formsAutoFilled}</div>
                <div className="text-sm text-foreground-muted">Forms Auto-filled</div>
              </CardContent>
            </Card>
            <Card className="bg-surface border-border-subtle">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-success">{extensionStats.opportunitiesDetected}</div>
                <div className="text-sm text-foreground-muted">Opportunities Found</div>
              </CardContent>
            </Card>
            <Card className="bg-surface border-border-subtle">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-warning">{extensionStats.applicationsSubmitted}</div>
                <div className="text-sm text-foreground-muted">Applications Sent</div>
              </CardContent>
            </Card>
            <Card className="bg-surface border-border-subtle">
              <CardContent className="p-4 text-center">
                <div className="text-2xl font-bold text-primary">{extensionStats.timesSaved}</div>
                <div className="text-sm text-foreground-muted">Time Saved</div>
              </CardContent>
            </Card>
          </div>

          {/* Features */}
          <Card className="bg-surface border-border-subtle">
            <CardHeader>
              <CardTitle className="text-lg">Extension Features</CardTitle>
              <CardDescription>Powerful tools to streamline your speaking applications</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {features.map((feature) => {
                  const IconComponent = feature.icon;
                  return (
                    <div key={feature.title} className="flex items-start gap-3 p-3 bg-surface-elevated rounded-lg">
                      <div className={`p-2 rounded-lg ${
                        feature.status === 'active' ? 'bg-primary text-primary-foreground' : 'bg-muted text-foreground-muted'
                      }`}>
                        <IconComponent className="h-4 w-4" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold text-foreground">{feature.title}</h4>
                        <p className="text-sm text-foreground-muted">{feature.description}</p>
                        <Badge 
                          variant={feature.status === 'active' ? 'default' : 'outline'} 
                          className="mt-1 text-xs"
                        >
                          {feature.status === 'active' ? 'Active' : 'Coming Soon'}
                        </Badge>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Install Tab */}
        <TabsContent value="install" className="space-y-6">
          <Card className="bg-surface border-border-subtle">
            <CardHeader>
              <CardTitle className="text-lg">Install SpeakerBot Extension</CardTitle>
              <CardDescription>Follow these steps to install the Chrome extension</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-start gap-3 p-4 bg-surface-elevated rounded-lg">
                  <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">1</div>
                  <div>
                    <h4 className="font-semibold text-foreground">Download Extension</h4>
                    <p className="text-sm text-foreground-muted">Click the button below to go to Chrome Web Store</p>
                    <Button className="mt-2 bg-primary hover:bg-primary-hover">
                      <Chrome className="h-4 w-4 mr-2" />
                      Add to Chrome
                    </Button>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-4 bg-surface-elevated rounded-lg">
                  <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">2</div>
                  <div>
                    <h4 className="font-semibold text-foreground">Pin Extension</h4>
                    <p className="text-sm text-foreground-muted">Pin the SpeakerBot extension to your toolbar for easy access</p>
                  </div>
                </div>

                <div className="flex items-start gap-3 p-4 bg-surface-elevated rounded-lg">
                  <div className="w-6 h-6 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-sm font-bold">3</div>
                  <div>
                    <h4 className="font-semibold text-foreground">Sign In</h4>
                    <p className="text-sm text-foreground-muted">Click the extension icon and sign in with your SpeakerBot account</p>
                  </div>
                </div>
              </div>

              <div className="p-4 bg-primary/10 border border-primary/20 rounded-lg">
                <h4 className="font-semibold text-foreground mb-2">System Requirements</h4>
                <ul className="text-sm text-foreground-muted space-y-1">
                  <li>• Chrome 88+ or Chromium-based browser</li>
                  <li>• Active SpeakerBot account</li>
                  <li>• Internet connection for sync</li>
                </ul>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Detected Opportunities Tab */}
        <TabsContent value="detected" className="space-y-6">
          <Card className="bg-surface border-border-subtle">
            <CardHeader>
              <CardTitle className="text-lg">Recently Detected Opportunities</CardTitle>
              <CardDescription>Speaking opportunities found while browsing</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {detectedOpportunities.map((opportunity) => (
                <div key={opportunity.id} className="flex items-center justify-between p-4 bg-surface-elevated rounded-lg border border-border-subtle">
                  <div className="flex-1">
                    <h4 className="font-semibold text-foreground">{opportunity.title}</h4>
                    <p className="text-sm text-foreground-muted">{opportunity.url}</p>
                    <p className="text-xs text-foreground-muted mt-1">Detected {opportunity.detected}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge 
                      variant={
                        opportunity.status === 'new' ? 'default' :
                        opportunity.status === 'applied' ? 'outline' : 'secondary'
                      }
                      className="text-xs"
                    >
                      {opportunity.status === 'new' ? 'New' :
                       opportunity.status === 'applied' ? 'Applied' : 'Dismissed'}
                    </Badge>
                    <Button size="sm" variant="outline">
                      <ExternalLink className="h-3 w-3 mr-1" />
                      View
                    </Button>
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card className="bg-surface border-border-subtle">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Settings className="h-5 w-5 text-primary" />
                Extension Settings
              </CardTitle>
              <CardDescription>Configure how the extension works</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-surface-elevated rounded-lg">
                  <div>
                    <h4 className="font-semibold text-foreground">Auto-fill Forms</h4>
                    <p className="text-sm text-foreground-muted">Automatically fill detected speaker forms</p>
                  </div>
                  <Button variant="outline" size="sm">Enable</Button>
                </div>

                <div className="flex items-center justify-between p-4 bg-surface-elevated rounded-lg">
                  <div>
                    <h4 className="font-semibold text-foreground">Opportunity Notifications</h4>
                    <p className="text-sm text-foreground-muted">Get notified when opportunities are detected</p>
                  </div>
                  <Button variant="outline" size="sm">Enable</Button>
                </div>

                <div className="flex items-center justify-between p-4 bg-surface-elevated rounded-lg">
                  <div>
                    <h4 className="font-semibold text-foreground">Smart Suggestions</h4>
                    <p className="text-sm text-foreground-muted">AI-powered application improvements</p>
                  </div>
                  <Button variant="outline" size="sm" disabled>Coming Soon</Button>
                </div>

                <div className="flex items-center justify-between p-4 bg-surface-elevated rounded-lg">
                  <div>
                    <h4 className="font-semibold text-foreground">Data Sync</h4>
                    <p className="text-sm text-foreground-muted">Sync data between browser and dashboard</p>
                  </div>
                  <Button variant="outline" size="sm">Enable</Button>
                </div>
              </div>

              <div className="pt-4 border-t border-border-subtle">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold text-foreground">Reset Extension</h4>
                    <p className="text-sm text-foreground-muted">Clear all extension data and settings</p>
                  </div>
                  <Button variant="outline" size="sm" className="text-destructive hover:text-destructive">
                    Reset
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}