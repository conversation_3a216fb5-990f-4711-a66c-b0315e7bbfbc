

const ApiResponse = require("../helpers/api-response");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const roleService = require("../services/role-service");

/**
 * Create new role with permissions
 */
exports.createRole = async (req, res, next) => {

    try {

        const { status, message } = await roleService.createRole(req);

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error creating role:", error);
        next(error)
    }
}

/**
 * Update role and permissions
 */
exports.updateRole = async (req, res, next) => {
    try {
        const { status, message } = await roleService.updateRole(req);

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error updating role:", error);
        next(error)
    }
}

/**
 * Delete role by id
 */
exports.deleteRole = async (req, res, next) => {
    try {
        const { status, message } = await roleService.deleteRole(req);

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error deleting role:", error);
        next(error)
    }
}

/**
 * Get all roles with user assignments
 */
exports.getRoles = async (req, res, next) => {
    try {
        const { status, data, message } = await roleService.getRoles(req);

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ data, message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error fetching roles:", error);
        next(error)
    }
}


exports.getPermissions = async (req, res, next) => {
    try {
        const { status, data, message } = await roleService.getPermissions(req);

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ data, message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error fetching permissions:", error);
        next(error)
    }
}