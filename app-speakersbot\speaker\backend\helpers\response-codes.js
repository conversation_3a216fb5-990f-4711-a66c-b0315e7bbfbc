// ResponseCodes class containing standardized HTTP response codes, messages, and response objects
class ResponseCodes {

  // Static constants for HTTP response status codes used throughout the application
  static RESPONSE_CODES = {
    SUCCESS: 200,                    // Request successful
    ACCEPTED: 201,                   // Resource created successfully
    BAD_REQUEST: 400,               // Client error - invalid request
    UNAUTHORIZED: 401,              // Authentication required
    PAYMENT_REQUIRED: 402,          // Payment required for access
    ACCESS_NOT: 403,                // Forbidden - access denied
    NOT_FOUND: 404,                 // Resource not found
    NOT_ACCEPTABLE: 406,            // Request not acceptable
    TIMEOUT: 408,                   // Request timeout
    CONFLICT: 409,                  // Resource conflict
    LARGE_PAYLOAD: 413,             // Request payload too large
    UNPROCESSABLE_ENTITY: 422,      // Validation errors
    TOO_MANY_REQUESTS: 429,         // Rate limiting
    SERVER_ERROR: 500,              // Internal server error
    BAD_GATEWAY: 502                // Bad gateway error
  }

  // Static constants for standardized response messages used across the application
  static RESPONSE_MESSAGES = {
    SUCCESS: "Success",                                                                                    // General success message
    UPDATED: "Updated successfully",                                                                       // Resource update success
    CREATED: "Created successfully",                                                                       // Resource creation success
    DELETED: "Deleted successfully",                                                                       // Resource deletion success
    NOT_FOUND: "Not found.",                                                                              // Resource not found
    BAD_REQUEST: "Bad request.",                                                                          // Invalid request format
    PAYMENT_REQUIRED: "Payment required.",                                                               // Payment needed for access
    ACCESS_NOT: "Forbidden, access denied.",                                                             // Access permission denied
    BAD_GATEWAY: "Request method not allowed.",                                                          // HTTP method not allowed
    UNPROCESSABLE_ENTITY: "Unprocessable entity.",                                                       // Validation failed
    TIMEOUT: "Request timeout.",                                                                         // Request processing timeout
    LARGE_PAYLOAD: "Request payload too large.",                                                         // Request size exceeded
    SERVER_ERROR: "Internal server error.",                                                             // Server-side error
    MISSING_BODY: "Required value missing.",                                                             // Required fields missing
    FILE_ERROR: "Error to process file",                                                                 // File processing error
    NOT_ACCEPTABLE: "Not acceptable",                                                                    // Request format not acceptable
    EMAIL_ALREADY_REGISTERED: "Email already registered",                                               // Duplicate email registration
    TOO_MANY_REQUESTS: "Too many requests, please try again later.",                                    // Rate limit exceeded
    CARD_ALREADY_EXIST: "Card already exist",                                                           // Duplicate card registration
    ALREADY_EXIST: "Already exist",                                                                     // Resource already exists
    LINK_SENT: "A link has been successfully sent to your registered email address.",                  // Email link sent confirmation
    SOMETHING_WENT_WRONG: "Something went wrong.",                                                      // Generic error message
    PASSWORD: {                                                                                         // Password-related messages
      NOT_SAME: "Password and confirm password fields must match",                                      // Password mismatch
      INCORRECT: "Password is incorrect"                                                                // Invalid password
    },
    UNAUTHORIZED: "Failed to authenticate because of bad credentials or invalid authorization details." // Authentication failure
  }

  // Static response objects combining status codes and messages for consistent API responses
  static RESPONSES = {
    SUCCESS: {                                                    // Standard success response
      status: this.RESPONSE_CODES.SUCCESS,
      message: this.RESPONSE_MESSAGES.SUCCESS
    },
    CREATED: {                                                    // Resource creation success response
      status: this.RESPONSE_CODES.ACCEPTED,
      message: this.RESPONSE_MESSAGES.CREATED
    },
    UPDATED: {                                                    // Resource update success response
      status: this.RESPONSE_CODES.SUCCESS,
      message: this.RESPONSE_MESSAGES.UPDATED
    },
    NOT_FOUND: {                                                  // Resource not found error response
      status: this.RESPONSE_CODES.NOT_FOUND,
      message: this.RESPONSE_MESSAGES.NOT_FOUND
    },
    BAD_REQUEST: {                                                // Bad request error response
      status: this.RESPONSE_CODES.BAD_REQUEST,
      message: this.RESPONSE_MESSAGES.BAD_REQUEST
    },
    PAYMENT_REQUIRED: {                                           // Payment required error response
      status: this.RESPONSE_CODES.PAYMENT_REQUIRED,
      message: this.RESPONSE_MESSAGES.PAYMENT_REQUIRED
    },
    ACCESS_NOT: {                                                 // Access denied error response
      status: this.RESPONSE_CODES.ACCESS_NOT,
      message: this.RESPONSE_MESSAGES.ACCESS_NOT
    },
    BAD_GATEWAY: {                                                // Bad gateway error response
      status: this.RESPONSE_CODES.BAD_GATEWAY,
      message: this.RESPONSE_MESSAGES.BAD_GATEWAY
    },
    NOT_ACCEPTABLE: {                                             // Not acceptable error response
      status: this.RESPONSE_CODES.NOT_ACCEPTABLE,
      message: this.RESPONSE_MESSAGES.NOT_ACCEPTABLE
    },
    TIMEOUT: {                                                    // Request timeout error response
      status: this.RESPONSE_CODES.TIMEOUT,
      message: this.RESPONSE_MESSAGES.TIMEOUT
    },
    LARGE_PAYLOAD: {                                              // Large payload error response
      status: this.RESPONSE_CODES.LARGE_PAYLOAD,
      message: this.RESPONSE_MESSAGES.LARGE_PAYLOAD
    },
    SERVER_ERROR: {                                               // Internal server error response
      status: this.RESPONSE_CODES.SERVER_ERROR,
      message: this.RESPONSE_MESSAGES.SERVER_ERROR
    },
    FILE_ERROR: {                                                 // File processing error response
      status: this.RESPONSE_CODES.BAD_GATEWAY,
      message: this.RESPONSE_MESSAGES.FILE_ERROR
    },
    UNPROCESSABLE_ENTITY: {                                       // Validation error response
      status: this.RESPONSE_CODES.UNPROCESSABLE_ENTITY,
      message: this.RESPONSE_MESSAGES.MISSING_BODY
    },
    LINK_SENT: {                                                  // Email link sent success response
      status: this.RESPONSE_CODES.SUCCESS,
      message: this.RESPONSE_MESSAGES.LINK_SENT
    },
    ALREADY_EXIST: {                                              // Resource conflict error response
      status: this.RESPONSE_CODES.CONFLICT,
      message: this.RESPONSE_MESSAGES.ALREADY_EXIST
    },
    SOMETHING_WENT_WRONG: {                                       // Generic error response
      status: this.RESPONSE_CODES.BAD_REQUEST,
      message: this.RESPONSE_MESSAGES.SOMETHING_WENT_WRONG
    },
    TOO_MANY_REQUESTS: {                                          // Rate limit exceeded error response
      status: this.RESPONSE_CODES.TOO_MANY_REQUESTS,
      message: this.RESPONSE_MESSAGES.TOO_MANY_REQUESTS
    }
  }
}

// Export the ResponseCodes class for use throughout the application
module.exports = ResponseCodes