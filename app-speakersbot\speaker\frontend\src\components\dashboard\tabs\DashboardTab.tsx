import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { TodayTab } from './TodayTab';
import { OpportunityTab } from './OpportunityTab';
import { ApplicationsTab } from './ApplicationsTab';
import { ProfileTab } from './ProfileTab';
import { SupportTab } from './SupportTab';

export function DashboardTab() {

  const ui =
  "data-[state=active]:bg-gradient-tertiary data-[state=active]:text-tertiary-foreground";
  return (
    <div className="h-full">
      <Tabs defaultValue="today" className="h-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="today" className={`${ui}`}>Today</TabsTrigger>
          <TabsTrigger value="opportunities" className={`${ui}`}>Opportunities</TabsTrigger>
          <TabsTrigger value="applications" className={`${ui}`}>Applications</TabsTrigger>
          <TabsTrigger value="profile" className={`${ui}`}>Profile</TabsTrigger>
          <TabsTrigger value="support" className={`${ui}`}>Support</TabsTrigger>
        </TabsList>
        
        <TabsContent value="today" className="mt-6">
          <TodayTab />
        </TabsContent>
        
        <TabsContent value="opportunities" className="mt-6">
          <OpportunityTab />
        </TabsContent>
        
        <TabsContent value="applications" className="mt-6">
          <ApplicationsTab />
        </TabsContent>
        
        <TabsContent value="profile" className="mt-6">
          <ProfileTab />
        </TabsContent>
        
        <TabsContent value="support" className="mt-6">
          <SupportTab />
        </TabsContent>
      </Tabs>
    </div>
  );
}