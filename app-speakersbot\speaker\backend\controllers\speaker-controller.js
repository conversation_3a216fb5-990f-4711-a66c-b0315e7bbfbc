

const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");

const speakerService = require("../services/speaker-service");
const ApiResponse = require("../helpers/api-response");
const CONFIG = require("../config/config");
const jwtHelper = require("../helpers/jwt-helper");
const emailService = require("../services/email-service");
const { encodeToBase64, decodeFromBase64 } = require("../helpers/app-hepler");
const speakerHistoryService = require("../services/gamification-service");

/**
 * Get all speakers with filters and pagination
 */
exports.getAllSpeakers = async (req, res, next) => {

    try {
        const result = await speakerService.getAllSpeakers(req);
        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data: { speakers: data.speakers, summary: data.summary }, pagination: data.pageData }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
}

/**
 * Get speaker by id
 */
exports.getSpeakerById = async (req, res, next) => {

    try {
        const result = await speakerService.getSpeakerById(req);
        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
}

/**
 * Update speaker by id
 */
exports.updateSpeaker = async (req, res, next) => {

    try {
        const result = await speakerService.updateSpeaker(req);
        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
}


/**
 * Export speakers data to CSV
 */
exports.exportSpeakers = async (req, res, next) => {
    try {
        const result = await speakerService.exportSpeakers(req, res);
        const { status, message } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
}


/**
 * Intake speakers from external source
 */
exports.intakeSpeakers = async (req, res, next) => {
    try {

        const result = await speakerService.intakeSpeakers(req);
        const { status, message } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
}

/**
 * Speaker signup/registration
 */
exports.signupSpeakerExtension = async (req, res, next) => {
    try {
        const { name, email, phone, password } = req.body;

        if (!name || !email || !phone || !password) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "name, email, phone and password fields are required");
        }

        const speaker = await speakerService.signup(name, email, phone, password);

        const structuredSpeaker = {
            id: speaker.id,
            name: speaker.name,
            email: speaker.email,
            phone: speaker.phone_number,
            email_verified: speaker.email_verified
        };

        res.status(RESPONSE_CODES.SUCCESS).
            json(ApiResponse({
                message: "Speaker signed up successfully and verification email sent",
                data: structuredSpeaker
            }));
        // Send verification email
        try {
            const baseUrl = CONFIG.EXTENSION_BASE_URL;
            const jwtToken = jwtHelper.generateToken({ id: structuredSpeaker.id, email: structuredSpeaker.email }, '1d');

            console.log("JWT Token:", jwtToken);

            emailService.sendVerificationEmail(
                structuredSpeaker.email,
                structuredSpeaker.name,
                encodeToBase64(jwtToken),
                baseUrl
            );
            speakerHistoryService.callEventPoints(speaker.id, 'complete_intake_form');
        } catch (emailError) {
            console.error('Failed to send verification email:', emailError);
        }
    } catch (error) {
        next(error);
    }
}

/**
 * Get opportunities for specific speaker
 */
exports.getSpeakerOpportunities = async (req, res, next) => {
    try {
        const result = await speakerService.getSpeakerOpportunities(req);
        const { status, message, data, pageData } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data, pagination: pageData }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
}

/**
 * Get speaker history/activity
 */
exports.getSpeakerHistory = async (req, res, next) => {
    try {
        const result = await speakerService.getSpeakerHistory(req);
        const { status, message, data, pageData } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data, pagination: pageData }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
}

/**
 * Update speaker opportunity status
 */
exports.updateSpeakerOpportunity = async (req, res, next) => {
    try {
        const result = await speakerService.updateSpeakerOpportunity(req);
        const { status, message } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
}

/**
 * Verify speaker email
 */
exports.verifyEmail = async (req, res, next) => {
    try {
        const { token } = req.query;

        if (!token) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Verification token is required");
        }

        const speaker = await speakerService.verifyEmail(decodeFromBase64(token));

        res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({
            data: speaker,
            message: "Email verified successfully",
        }));
        // Send welcome email
        emailService.sendWelcomeEmail(speaker.email, speaker.name)
            .catch(error => {
                console.error('Failed to send welcome email:', error);
            });

    } catch (error) {
        next(error);
    }
}

/**
 * Resend verification email
 */
exports.resendVerificationEmail = async (req, res, next) => {
    try {
        const { email } = req.body;

        if (!email) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email is required");
        }

        await speakerService.resendVerificationEmail(email);

        res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({
            message: "Verification email sent successfully"
        }));

    } catch (error) {
        next(error);
    }
}

/**
 * Check if speaker email is verified
 */
exports.checkEmailVerified = async (req, res, next) => {
    try {
        const { speakerId } = req.params;

        const isVerified = await speakerService.checkEmailVerified(speakerId);

        res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({
            data: isVerified,
            message: isVerified ? "Email verified" : "Email not verified",
        }));

    } catch (error) {
        next(error);
    }
}




/** * Get intake form structure
 */
exports.getIntakeForm = async (req, res, next) => {
    try {
        const result = await speakerService.getIntakeForm(req);
        const { status, message, data } = result;
        if (status) {
            res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        } else {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
        }
    } catch (error) {
        next(error);
    }
}



/**
 * Get Intake form progress for a speaker
 */

exports.getIntakeFormProgress = async (req, res, next) => {
    try {

        const result = await speakerService.getIntakeFormProgress(req);
        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));

    } catch (error) {
        next(error)
    }
} 