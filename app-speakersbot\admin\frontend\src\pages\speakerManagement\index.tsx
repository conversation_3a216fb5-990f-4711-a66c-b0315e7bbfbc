import React, { useMemo, useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Combobox } from '@/components/ui/combobox';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis
} from '@/components/ui/pagination';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { useAppState } from '@/state/AppStateProvider';
import { Users, UserCheck, Crown, CreditCard, Search, RotateCcw, Eye, Edit, MapPin, ChevronDown, CalendarIcon, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { DateRange } from 'react-day-picker';
import { Speaker } from '@/types';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import { useGetSpeakersQuery, useGetCitiesQuery } from '@/apis/speakersApi';
import { Skeleton } from '@/components/ui/skeleton';

const Speakers: React.FC = () => {
  const { speakers, matches } = useAppState();
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [planFilter, setPlanFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [cityFilter, setCityFilter] = useState<string>('all');
  const [geographyFilter, setGeographyFilter] = useState<string>('all');
  const [primaryCategoryFilter, setPrimaryCategoryFilter] = useState<string>('all');
  const [subCategoryFilter, setSubCategoryFilter] = useState<string>('all');
  const [experienceFilter, setExperienceFilter] = useState<string>('all');
  const [willingToTravelFilter, setWillingToTravelFilter] = useState<string>('all');
  const [dateRange, setDateRange] = useState<DateRange | undefined>();
  const [showMoreFilters, setShowMoreFilters] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Debounce search term
  useEffect(() => {
    if (searchTerm !== debouncedSearchTerm) {
      setIsSearching(true);
    }

    const timer = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setIsSearching(false);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchTerm, debouncedSearchTerm]);

  // Build server-side filter object based on UI filters
  const serverFilter = React.useMemo(() => {
    const f: Record<string, string> = {};
    if (statusFilter !== 'all') {
      // backend expects status values like 'active' | 'inactive'
      f.status = statusFilter;
    }
    if (cityFilter !== 'all') {
      f.city = cityFilter;
    }
    if (planFilter !== 'all') {
      // API expects key `plan` (values: not_activated | basic | premium)
      // Pass through selected value as-is
      if (['not_activated', 'basic', 'premium'].includes(planFilter)) {
        f.plan = planFilter;
      }
    }
    return Object.keys(f).length > 0 ? f : null;
  }, [statusFilter, cityFilter, planFilter]);

  // Fetch speakers from API
  const { data: apiData, isLoading: isApiLoading, isFetching: isApiFetching } = useGetSpeakersQuery(
    {
      page: currentPage,
      limit: itemsPerPage,
      search: debouncedSearchTerm || undefined,
      filter: serverFilter ? JSON.stringify(serverFilter) : undefined,
    },
    { refetchOnMountOrArgChange: true }
  );

  // Table should show skeleton on initial load and on refetch from filters
  const isTableLoading = isApiLoading || isApiFetching;

  // Fetch cities list from API (admin service)
  const { data: citiesApiData, isLoading: isCitiesLoading } = useGetCitiesQuery(undefined);

  // Normalize API cities into a simple string[] list
  const apiCities: string[] = useMemo(() => {
    const raw = (citiesApiData as any)?.data ?? (citiesApiData as any);
    // New shape: { status: true, message: string, data: { cities: string[] } }
    const nestedCities = (citiesApiData as any)?.data?.cities;
    if (Array.isArray(nestedCities)) {
      return nestedCities.filter((c: any) => typeof c === 'string');
    }
    if (Array.isArray(raw)) {
      return raw.filter((c: any) => typeof c === 'string');
    }
    if (Array.isArray((raw as any)?.cities)) {
      return (raw as any).cities.filter((c: any) => typeof c === 'string');
    }
    return [];
  }, [citiesApiData]);

  // Map API speaker to existing Speaker shape (minimal fields used by UI)
  const mapApiSpeaker = (s: any): Speaker => {
    const preferredGeography: string[] = [];
    if (s.city) preferredGeography.push(s.city);
    if (s.state) preferredGeography.push(s.state);

    // Parse other expertise from field_13
    const otherExpertise: string[] = [];
    if (s.field_13) {
      otherExpertise.push(s.field_13);
    }

    const planNormalized = s.planType === 'not_activated'
      ? 'Not Activated'
      : (s.planType || 'basic');

    return {
      id: String(s.id),
      name: s.name || '',
      email: s.email || '',
      createdAt: s.created_at || new Date().toISOString(),
      activityStatus: s.status === 'active' ? 'active' : (s.status === 'pending' ? 'pending' : 'inactive'),
      subscriptionPlan: planNormalized,
      intakeData: {
        preferredGeography,
        primaryExpertise: s.field_11 || '',
        otherExpertise,
        yearsOfExperience: (s.yearsExperienceSpeaker && !isNaN(Number(s.yearsExperienceSpeaker)))
          ? Number(s.yearsExperienceSpeaker)
          : (s.field_21 ? Number(s.field_21) : undefined),
        // Keep boolean for legacy UI; we'll also read raw string at render time
        willingToTravel: typeof s.willingToTravel === 'string'
          ? (s.willingToTravel.trim().toLowerCase().startsWith('yes'))
          : (s.field_64 ? String(s.field_64).toLowerCase() === 'yes' : undefined),
        preferredTalkFormats: s.field_55 ? String(s.field_55).split(',').map((x) => x.trim()) : [],
        speakerWebsite: s.field_68 || undefined,
        company: s.company || undefined,
        title: s.title || undefined,
        phoneNumber: s.phone_number || undefined,
      },
      // Store raw API data for additional fields
      rawData: s,
    } as Speaker & { rawData: any };
  };

  const apiSpeakers: Speaker[] = useMemo(() => {
    const list = apiData?.data?.speakers || [];
    return Array.isArray(list) ? list.map(mapApiSpeaker) : [];
  }, [apiData]);

  // Source list prefers API data when available
  const sourceSpeakers: Speaker[] = apiSpeakers.length > 0 ? apiSpeakers : speakers.speakers;

  // KPIs: prefer API summary if provided
  const kpiData = useMemo(() => {
    if (apiData?.data?.summary) {
      return {
        totalSpeakers: apiData.data.summary?.alltotalSpeaker || 0,
        activeSpeakers: apiData.data.summary?.activeSpeaker || 0,
        premiumSpeakers: apiData.data.summary?.premiumSpeakers || 0,
        unpaidSpeakers: apiData.data.summary?.basicSpeakers || 0,
      };
    }

    // Fallback to calculated values from source speakers
    const total = sourceSpeakers.length;
    const active = sourceSpeakers.filter(s => s.activityStatus === 'active').length;
    const premium = sourceSpeakers.filter(s => s.subscriptionPlan === 'premium' || s.subscriptionPlan === 'enterprise').length;
    const basic = sourceSpeakers.filter(s => s.subscriptionPlan === 'basic').length;

    return {
      totalSpeakers: total,
      activeSpeakers: active,
      premiumSpeakers: premium,
      unpaidSpeakers: basic,
    };
  }, [apiData, sourceSpeakers]);
  // Get phone number from API data
  const getPhoneNumber = (speaker: Speaker) => {
    // Check if speaker has rawData with phone_number
    if ((speaker as any).rawData?.phone_number) {
      return (speaker as any).rawData.phone_number;
    }

    // Fallback to intakeData phoneNumber
    if (speaker.intakeData?.phoneNumber) {
      return speaker.intakeData.phoneNumber;
    }

    // No phone number available
    return '';
  };

  // Enhanced search function
  const enhancedSearch = (speaker: Speaker, searchTerm: string) => {
    if (!searchTerm) return true;
    const term = searchTerm.toLowerCase();

    // Check rawData first for API fields
    const rawData = (speaker as any).rawData;
    if (rawData) {
      return (
        speaker.name.toLowerCase().includes(term) ||
        speaker.email.toLowerCase().includes(term) ||
        getPhoneNumber(speaker).toLowerCase().includes(term) ||
        (rawData.company || '').toLowerCase().includes(term) ||
        (rawData.title || '').toLowerCase().includes(term) ||
        (rawData.field_68 || '').toLowerCase().includes(term) ||
        (rawData.city || '').toLowerCase().includes(term) ||
        (rawData.state || '').toLowerCase().includes(term) ||
        (rawData.field_11 || '').toLowerCase().includes(term) ||
        (rawData.field_13 || '').toLowerCase().includes(term)
      );
    }

    // Fallback to intakeData
    return (
      speaker.name.toLowerCase().includes(term) ||
      speaker.email.toLowerCase().includes(term) ||
      getPhoneNumber(speaker).toLowerCase().includes(term) ||
      (speaker.intakeData?.company || '').toLowerCase().includes(term) ||
      (speaker.intakeData?.title || '').toLowerCase().includes(term) ||
      (speaker.intakeData?.speakerWebsite || '').toLowerCase().includes(term)
    );
  };

  // Get unique filter options from API data
  const filterOptions = useMemo(() => {
    const geographies = new Set<string>();
    const primaryCategories = new Set<string>();
    const subCategories = new Set<string>();

    sourceSpeakers.forEach(speaker => {
      // Extract data from rawData if available
      const rawData = (speaker as any).rawData;

      if (rawData) {
        // Cities from API data
        // (removed unused computed cities)

        // Geographies from API data
        if (rawData.city && rawData.state) {
          geographies.add(`${rawData.city}, ${rawData.state}`);
        } else if (rawData.city) {
          geographies.add(rawData.city);
        }

        // Primary categories from API data
        if (rawData.field_11) {
          primaryCategories.add(rawData.field_11);
        }

        // Sub categories from API data
        if (rawData.field_13) {
          subCategories.add(rawData.field_13);
        }
      }

      // Fallback to intakeData
      if (speaker.intakeData?.preferredGeography) {
        speaker.intakeData.preferredGeography.forEach(geo => {
          geographies.add(geo);
          // (removed unused computed cities from geography parsing)
        });
      }
      if (speaker.intakeData?.primaryExpertise) {
        primaryCategories.add(speaker.intakeData.primaryExpertise);
      }
      if (speaker.intakeData?.otherExpertise) {
        speaker.intakeData.otherExpertise.forEach(cat => subCategories.add(cat));
      }
    });

    return {
      geographies: Array.from(geographies).sort(),
      primaryCategories: Array.from(primaryCategories).sort(),
      subCategories: Array.from(subCategories).sort()
    };
  }, [sourceSpeakers]);

  // Filter speakers
  const filteredSpeakers = useMemo(() => {
    return sourceSpeakers.filter(speaker => {
      const matchesSearch = enhancedSearch(speaker, debouncedSearchTerm);
      const matchesPlan = planFilter === 'all' ||
        (planFilter === 'not_activated' ?
          ((speaker as any).rawData?.planType === 'not_activated') :
          speaker.subscriptionPlan === planFilter);
      const matchesStatus = statusFilter === 'all' || speaker.activityStatus === statusFilter;

      const matchesCity = cityFilter === 'all' ||
        ((speaker as any).rawData?.city === cityFilter) ||
        (speaker.intakeData?.preferredGeography?.some(geo => (geo.split(', ')[0] || geo) === cityFilter));

      const matchesGeography = geographyFilter === 'all' ||
        ((speaker as any).rawData?.city === geographyFilter) ||
        ((speaker as any).rawData?.state === geographyFilter) ||
        (`${(speaker as any).rawData?.city}, ${(speaker as any).rawData?.state}` === geographyFilter) ||
        (speaker.intakeData?.preferredGeography?.includes(geographyFilter));

      const matchesPrimaryCategory = primaryCategoryFilter === 'all' ||
        ((speaker as any).rawData?.field_11 === primaryCategoryFilter) ||
        speaker.intakeData?.primaryExpertise === primaryCategoryFilter;

      const matchesSubCategory = subCategoryFilter === 'all' ||
        ((speaker as any).rawData?.field_13 === subCategoryFilter) ||
        (speaker.intakeData?.otherExpertise?.includes(subCategoryFilter));

      const matchesExperience = experienceFilter === 'all' ||
        ((speaker as any).rawData?.field_21?.toString() === experienceFilter) ||
        speaker.intakeData?.yearsOfExperience?.toString() === experienceFilter;

      const matchesWillingToTravel = willingToTravelFilter === 'all' ||
        ((speaker as any).rawData?.field_64?.toLowerCase() === willingToTravelFilter) ||
        (speaker.intakeData?.willingToTravel?.toString() === willingToTravelFilter);

      // Date filters (using createdAt as registration date)
      let matchesDateRange = true;
      if (dateRange?.from || dateRange?.to) {
        const speakerDate = dayjs(speaker.createdAt);
        if (dateRange.from) {
          matchesDateRange = matchesDateRange && speakerDate.isAfter(dayjs(dateRange.from).subtract(1, 'day'));
        }
        if (dateRange.to) {
          matchesDateRange = matchesDateRange && speakerDate.isBefore(dayjs(dateRange.to).add(1, 'day'));
        }
      }

      return matchesSearch && matchesPlan && matchesStatus && matchesCity &&
        matchesGeography && matchesPrimaryCategory && matchesSubCategory &&
        matchesExperience && matchesWillingToTravel && matchesDateRange;
    });
  }, [sourceSpeakers, debouncedSearchTerm, planFilter, statusFilter, cityFilter, geographyFilter,
    primaryCategoryFilter, subCategoryFilter, experienceFilter, willingToTravelFilter,
    dateRange]);

  // Get paginated speakers (use API data directly when available)
  const paginatedSpeakers = useMemo(() => {
    // If we have API data, use it directly (already paginated by server)
    if (apiSpeakers.length > 0) {
      return filteredSpeakers;
    }

    // Otherwise, client-side pagination
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredSpeakers.slice(startIndex, endIndex);
  }, [filteredSpeakers, currentPage, itemsPerPage, apiSpeakers]);

  // Calculate total pages (prefer API pagination if available)
  const totalPages = apiData?.pagination?.totalPages || Math.ceil(filteredSpeakers.length / itemsPerPage);
  const totalSpeakers = apiData?.pagination?.total || filteredSpeakers.length;

  // Get opportunity counts for each speaker from API data
  const getOpportunityCounts = (speaker: Speaker) => {
    // Check if speaker has rawData with opportunityCounts
    if ((speaker as any).rawData?.opportunityCounts) {
      const counts = (speaker as any).rawData.opportunityCounts;
      return {
        matched: counts.Matched || 0,
        accepted: counts.Accepted || 0,
        interested: counts.Interested || 0,
        rejected: counts.Rejected || 0
      };
    }

    // Fallback to zeros when API does not provide counts
    return {
      matched: 0,
      accepted: 0,
      interested: 0,
      rejected: 0
    };
  };



  // Get location (country, state, city) from API data
  const getLocation = (speaker: Speaker) => {
    // Check if speaker has rawData with city and state
    if ((speaker as any).rawData) {
      const rawData = (speaker as any).rawData;
      const locationParts = [];
      if (rawData.city) locationParts.push(rawData.city);
      if (rawData.state) locationParts.push(rawData.state);
      if (locationParts.length > 0) {
        return locationParts.join(', ');
      }
    }

    // Fallback to preferred geography
    if (speaker.intakeData?.preferredGeography && speaker.intakeData.preferredGeography.length > 0) {
      return speaker.intakeData.preferredGeography.slice(0, 3).join('; ');
    }
    return '';
  };

  const resetFilters = () => {
    setSearchTerm('');
    setDebouncedSearchTerm('');
    setIsSearching(false);
    setPlanFilter('all');
    setStatusFilter('all');
    setCityFilter('all');
    setGeographyFilter('all');
    setPrimaryCategoryFilter('all');
    setSubCategoryFilter('all');
    setExperienceFilter('all');
    setWillingToTravelFilter('all');
    setDateRange(undefined);
    setCurrentPage(1); // Reset to first page when filters are reset
  };

  const getPlanBadgeColor = (plan: string) => {
    switch (plan) {
      case 'basic': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 capitalize';
      case 'premium': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300 capitalize';
      case 'enterprise': return 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-300 capitalize';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300 whitespace-nowrap capitalize';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    return status === 'active'
      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300 capitalize'
      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300 capitalize';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="m-0 text-2xl font-bold text-foreground">Speaker Management</h1>
          <p className="text-sm text-muted-foreground mt-2">Manage and monitor speaker profiles and engagement</p>
        </div>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 gap-4">
        {
          isApiLoading ? (
            (
              [...Array(4)].map((_, i) => (
                <Card key={i} className="bg-tertiary border-border rounded-xl shadow-sm">
                  <CardHeader className="pb-2">
                    <CardTitle className="flex items-center gap-2">
                      <Skeleton className="h-4 w-4 rounded-full" />
                      <Skeleton className="h-4 w-24" />
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <Skeleton className="h-7 w-16 mb-2" />
                    <Skeleton className="h-3 w-32" />
                  </CardContent>
                </Card>
              ))
            )
          ) : (
            <>
              <Card className="bg-tertiary border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Total Speakers
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-foreground">{kpiData?.totalSpeakers ?? 0}</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    All registered speakers
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-tertiary border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                    <UserCheck className="h-4 w-4" />
                    Active Speakers
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-foreground">{kpiData?.activeSpeakers ?? 0}</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Currently active profiles
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-tertiary border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                    <Crown className="h-4 w-4" />
                    Premium Speakers
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-foreground">{kpiData?.premiumSpeakers ?? 0}</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Premium & enterprise plans
                  </p>
                </CardContent>
              </Card>

              <Card className="bg-tertiary border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300">
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground flex items-center gap-2">
                    <CreditCard className="h-4 w-4" />
                    Unpaid Speakers
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-foreground">{kpiData?.unpaidSpeakers ?? 0}</div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Basic plan speakers
                  </p>
                </CardContent>
              </Card>
            </>

          )
        }

      </div>

      {/* Filters */}
      <Card className="bg-tertiary border-border rounded-xl shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-foreground">Filters</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Reset - First Row */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">

                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search by name, email, phone, company, title ..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <Button
              onClick={resetFilters}
              variant="outline"
              className="w-full md:w-auto"
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>

          {/* Main Filters - Second Row */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 flex-1">
              <Select value={planFilter} onValueChange={setPlanFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by Plan" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Plans</SelectItem>
                  <SelectItem value="basic">Basic</SelectItem>
                  <SelectItem value="premium">Premium</SelectItem>
                  <SelectItem value="enterprise">Enterprise</SelectItem>
                  <SelectItem value="not_activated">Not Activated</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter by Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>

              <Combobox
                value={cityFilter}
                onValueChange={setCityFilter}
                options={[{ value: 'all', label: 'All Cities' }, ...apiCities.map(c => ({ value: c, label: c }))]}
                placeholder={isCitiesLoading ? 'Loading cities…' : 'Filter by City'}
                searchPlaceholder="Search city..."
                emptyText="No cities"
                disabled={isCitiesLoading}
                className="hover:bg-black hover:text-white"
              />
            </div>

            {/* <Button 
              onClick={() => setShowMoreFilters(!showMoreFilters)} 
              variant="outline"
              className="w-full md:w-auto"
              disabled
            >
              More Filters
              <ChevronDown className={`h-4 w-4 ml-2 transition-transform ${showMoreFilters ? 'rotate-180' : ''}`} />
            </Button> */}
          </div>

          {/* More Filters - Collapsible */}
          <Collapsible open={showMoreFilters} onOpenChange={setShowMoreFilters}>
            <CollapsibleContent className="space-y-4">
              {/* Additional Filters Row 1 */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Select value={geographyFilter} onValueChange={setGeographyFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by Geography" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Geography</SelectItem>
                    {filterOptions.geographies.map(geo => (
                      <SelectItem key={geo} value={geo}>{geo}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={primaryCategoryFilter} onValueChange={setPrimaryCategoryFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by Primary Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    {filterOptions.primaryCategories.map(cat => (
                      <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={subCategoryFilter} onValueChange={setSubCategoryFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by Sub Category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Sub Categories</SelectItem>
                    {filterOptions.subCategories.map(cat => (
                      <SelectItem key={cat} value={cat}>{cat}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={experienceFilter} onValueChange={setExperienceFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by Experience" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Experience</SelectItem>
                    <SelectItem value="1">1 Year</SelectItem>
                    <SelectItem value="2">2 Years</SelectItem>
                    <SelectItem value="3">3 Years</SelectItem>
                    <SelectItem value="5">5+ Years</SelectItem>
                    <SelectItem value="10">10+ Years</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Additional Filters Row 2 */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Select value={willingToTravelFilter} onValueChange={setWillingToTravelFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by Travel Willingness" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All</SelectItem>
                    <SelectItem value="true">Willing to Travel</SelectItem>
                    <SelectItem value="false">Not Willing to Travel</SelectItem>
                  </SelectContent>
                </Select>

                <div className="flex-1">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-left font-normal"
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateRange?.from ? (
                          dateRange.to ? (
                            <>
                              {format(dateRange.from, "LLL dd, y")} -{" "}
                              {format(dateRange.to, "LLL dd, y")}
                            </>
                          ) : (
                            format(dateRange.from, "LLL dd, y")
                          )
                        ) : (
                          <span>Registration Date Range</span>
                        )}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        initialFocus
                        mode="range"
                        defaultMonth={dateRange?.from}
                        selected={dateRange}
                        onSelect={setDateRange}
                        numberOfMonths={2}
                        className="p-3 pointer-events-auto"
                      />
                    </PopoverContent>
                  </Popover>
                </div>
              </div>
            </CollapsibleContent>
          </Collapsible>
        </CardContent>
      </Card>

      {/* Speakers Table */}
      <Card className="bg-tertiary border-border shadow-sm rounded-xl">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-foreground flex justify-between items-center">
            <span>Speaker Directory ({filteredSpeakers.length} speakers)</span>
            <div className="text-sm text-muted-foreground hidden sm:flex items-center gap-3">
              <span>Rows per page</span>
              <Select value={String(itemsPerPage)} onValueChange={(v) => { setItemsPerPage(Number(v)); setCurrentPage(1); }}>
                <SelectTrigger className="w-[60px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="p-0">
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="grid w-full [&>div]:max-h-[700px] [&>div]:border [&>div]:rounded">
            <Table>
              <TableHeader className="*:whitespace-nowrap sticky top-0 bg-background after:content-[''] after:inset-x-0 after:h-px after:bg-border after:absolute after:bottom-0 bg-muted/50 z-10 ">
                <TableRow className="border-b bg-tertiary backdrop-blur-md">
                  <TableHead className="font-semibold w-[200px]">Speaker Info</TableHead>
                  <TableHead className="font-semibold w-[150px]">Location</TableHead>
                  <TableHead className="font-semibold w-[100px]">Plan</TableHead>
                  <TableHead className="font-semibold w-[90px]">Status</TableHead>
                  <TableHead className="font-semibold w-[140px]">Opportunities</TableHead>
                  <TableHead className="font-semibold w-[150px]">Registered</TableHead>
                  <TableHead className="font-semibold w-[150px]">Primary Category</TableHead>
                  <TableHead className="font-semibold w-[150px]">Sub Category</TableHead>
                  <TableHead className="font-semibold w-[150px]">Topic</TableHead>
                  {/* <TableHead className="font-semibold w-[100px]">Experience</TableHead>
                  <TableHead className="font-semibold w-[80px]">Travel</TableHead>
                  <TableHead className="font-semibold w-[120px]">Talk Type</TableHead> */}
                  <TableHead className="font-semibold w-[100px] border-l sticky right-0 top-0 bg-tertiary z-10">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className="overflow-hidden">
                {isTableLoading ? (
                  // Skeleton rows while loading
                  Array.from({ length: 8 }).map((_, index) => (
                    <TableRow key={`skeleton-${index}`} className="hover:bg-muted/30 border-b">
                      <TableCell className="w-[200px]">
                        <div className="flex flex-col space-y-1 pr-2">
                          <Skeleton className="h-4 w-32" />
                          <Skeleton className="h-3 w-40" />
                          <Skeleton className="h-3 w-24" />
                        </div>
                      </TableCell>
                      <TableCell className="w-[150px]">
                        <div className="flex items-center gap-1">
                          <Skeleton className="h-3 w-3 rounded-full" />
                          <Skeleton className="h-3 w-20" />
                        </div>
                      </TableCell>
                      <TableCell className="w-[100px]">
                        <Skeleton className="h-6 w-16 rounded-full" />
                      </TableCell>
                      <TableCell className="w-[90px]">
                        <Skeleton className="h-6 w-14 rounded-full" />
                      </TableCell>
                      <TableCell className="w-[140px]">
                        <div className="space-y-1">
                          <div className="flex items-center gap-1">
                            <Skeleton className="h-3 w-12" />
                            <Skeleton className="h-3 w-4" />
                          </div>
                          <div className="flex items-center gap-1">
                            <Skeleton className="h-3 w-12" />
                            <Skeleton className="h-3 w-4" />
                          </div>
                          <div className="flex items-center gap-1">
                            <Skeleton className="h-3 w-12" />
                            <Skeleton className="h-3 w-4" />
                          </div>
                          <div className="flex items-center gap-1">
                            <Skeleton className="h-3 w-12" />
                            <Skeleton className="h-3 w-4" />
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="w-[110px]">
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell className="w-[150px]">
                        <Skeleton className="h-4 w-24" />
                      </TableCell>
                      <TableCell className="w-[100px]">
                        <Skeleton className="h-4 w-8" />
                      </TableCell>
                      <TableCell className="w-[80px]">
                        <Skeleton className="h-6 w-10 rounded-full" />
                      </TableCell>
                      {/* <TableCell className="w-[120px]">
                        <Skeleton className="h-4 w-20" />
                      </TableCell> */}
                      <TableCell className="w-[100px] border-l sticky right-0 bg-tertiary">
                        <div className="flex justify-center">
                          <Skeleton className="h-8 w-8 rounded" />
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  paginatedSpeakers.map((speaker) => {
                    const counts = getOpportunityCounts(speaker);
                    const { activityStatus, email, name, subscriptionPlan } = speaker
                    return (
                      <TableRow key={speaker.id} className="hover:bg-muted/30 border-b">
                        <TableCell className="w-[200px]">
                          <div className="flex flex-col space-y-1 pr-2">
                            <span className="font-medium text-foreground truncate">{speaker.name ?? ""}</span>
                            <span className="text-sm text-muted-foreground truncate">{speaker.email ?? ""}</span>
                            <span className="text-xs text-muted-foreground">{getPhoneNumber(speaker) ?? ""}</span>
                          </div>
                        </TableCell>
                        <TableCell className="w-[150px]">
                          <div className="flex items-center gap-1 text-sm text-muted-foreground">
                            {getLocation(speaker) ? (
                              <>
                                <MapPin className="h-3 w-3 flex-shrink-0" />
                                <span className="truncate">{getLocation(speaker)}</span>
                              </>
                            ) : null}
                          </div>
                        </TableCell>
                        <TableCell className="w-[100px] whitespace-n">
                          <Badge className={getPlanBadgeColor(speaker.subscriptionPlan)}>
                            {speaker?.subscriptionPlan ?? ""}
                          </Badge>
                        </TableCell>
                        <TableCell className="w-[90px]">
                          {
                            activityStatus && (
                              <Badge className={getStatusBadgeColor(speaker.activityStatus)}>
                                {activityStatus}
                              </Badge>
                            )
                          }

                        </TableCell>
                        <TableCell className="w-[140px]">
                          <div className="text-xs space-y-0.5">
                            <div className="flex items-center gap-1">
                              <span className="text-muted-foreground">Matched:</span>
                              <span className="text-primary font-medium">{counts.matched ?? 0}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <span className="text-muted-foreground">Accepted:</span>
                              <span className="text-green-600 dark:text-green-400 font-medium">{counts.accepted ?? 0}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <span className="text-muted-foreground">Interested:</span>
                              <span className="text-orange-600 dark:text-orange-400 font-medium">{counts.interested ?? 0}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <span className="text-muted-foreground">Rejected:</span>
                              <span className="text-red-600 dark:text-red-400 font-medium">{counts.rejected ?? 0}</span>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="w-[150px] text-sm text-muted-foreground whitespace-nowrap">
                          {dayjs(speaker.createdAt).format('MMM DD, YYYY') ?? ""}
                        </TableCell>
                        <TableCell className="w-[150px] text-sm text-muted-foreground">
                          <span className="truncate block">{(speaker as any)?.rawData?.primary_category || '-'}</span>
                        </TableCell>
                        <TableCell className="w-[150px] text-sm text-muted-foreground">
                        <span className="truncate block">{(speaker as any)?.rawData?.subcategory || '-'}</span>
                        </TableCell>
                        <TableCell className="w-[180px] text-sm text-muted-foreground">
                              <span className="truncate block">{(speaker as any)?.rawData?.topic ||  '-'}</span>
                              </TableCell>
                        {/* <TableCell className="w-[100px] text-sm text-muted-foreground">
                          {speaker.intakeData?.yearsOfExperience ? `${speaker.intakeData.yearsOfExperience}y` : ''}
                        </TableCell> */}
                        {/* <TableCell className="w-[80px]">
                          {(() => {
                            const rawTravel = (speaker as any)?.rawData?.willingToTravel;
                            if (typeof rawTravel === 'string' && rawTravel.trim() !== '') {
                              return (
                                <Badge variant={rawTravel == "yes" ? "secondary" : "destructive"} className="text-xs whitespace-nowrap truncate">
                                  {rawTravel ? "yes" : "no"}
                                </Badge>
                              );
                            }
                            if (speaker.intakeData?.willingToTravel !== undefined) {
                              return (
                                <Badge variant={speaker.intakeData.willingToTravel ? 'destructive' : 'secondary'} className="text-xs whitespace-nowrap">
                                  {speaker.intakeData.willingToTravel ? 'Yes' : 'No'}
                                </Badge>
                              );
                            }
                            return null;
                          })()}
                        </TableCell> */}
                        {/* <TableCell className="w-[120px] text-sm text-muted-foreground">
                          <span className="truncate block">{speaker.intakeData?.preferredTalkFormats?.[0] || ''}</span>
                        </TableCell> */}
                        <TableCell className="w-[100px] border-l sticky right-0 bg-tertiary">
                          <div className="flex justify-center">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => navigate(`/admin/speakers/${speaker?.id}`)}
                              className="h-8 w-8 p-0 hover:bg-muted hover:text-muted-foreground"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>

            {/* Empty state now rendered as a table row above */}
          </div>

          {/* Pagination */}
          {!isTableLoading && filteredSpeakers.length > 0 && (
            <div className="flex items-center justify-between px-4 py-3 border-t">
              <div className="text-sm text-muted-foreground whitespace-nowrap">
                Showing {Math.min((currentPage - 1) * itemsPerPage + 1, totalSpeakers)} to {Math.min(currentPage * itemsPerPage, totalSpeakers)} of {totalSpeakers} speakers
              </div>

              <div className="flex items-center gap-4">


                <Pagination>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                        aria-disabled={currentPage <= 1}
                      />
                    </PaginationItem>
                    {(() => {
                      const pages: number[] = [];
                      const start = Math.max(1, currentPage - 2);
                      const end = Math.min(totalPages, start + 4);

                      // Show first page if not included
                      if (start > 1) {
                        pages.push(1);
                      }
                      // Left ellipsis if gap after first page
                      const showLeftEllipsis = start > 2;

                      // Core window pages
                      const windowPages: number[] = [];
                      for (let p = start; p <= end; p++) windowPages.push(p);

                      // Right ellipsis if gap before last page
                      const showRightEllipsis = end < (totalPages - 1);

                      // Build JSX
                      return (
                        <>
                          {pages.map((p) => (
                            <PaginationItem key={`first-${p}`}>
                              <PaginationLink isActive={p === currentPage} onClick={() => setCurrentPage(p)}>
                                {p}
                              </PaginationLink>
                            </PaginationItem>
                          ))}
                          {showLeftEllipsis && (
                            <PaginationItem key="left-ellipsis">
                              <PaginationEllipsis />
                            </PaginationItem>
                          )}
                          {windowPages.map((p) => (
                            <PaginationItem key={p}>
                              <PaginationLink isActive={p === currentPage} onClick={() => setCurrentPage(p)}>
                                {p}
                              </PaginationLink>
                            </PaginationItem>
                          ))}
                          {showRightEllipsis && (
                            <PaginationItem key="right-ellipsis">
                              <PaginationEllipsis />
                            </PaginationItem>
                          )}
                          {end < totalPages && (
                            <PaginationItem key={`last-${totalPages}`}>
                              <PaginationLink isActive={totalPages === currentPage} onClick={() => setCurrentPage(totalPages)}>
                                {totalPages}
                              </PaginationLink>
                            </PaginationItem>
                          )}
                        </>
                      );
                    })()}
                    <PaginationItem>
                      <PaginationNext
                        onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                        aria-disabled={currentPage >= totalPages}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default Speakers;