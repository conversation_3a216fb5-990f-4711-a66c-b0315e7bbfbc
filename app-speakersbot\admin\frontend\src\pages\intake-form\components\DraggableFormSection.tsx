import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { DragOutlined } from '@ant-design/icons';
import { useDrag, useDrop } from 'react-dnd';

interface DraggableFormSectionProps {
  id: string;
  header: React.ReactNode;
  index: number;
  moveSection: (dragIndex: number, hoverIndex: number) => void;
  children: React.ReactNode;
  activeKey?: string | string[];
  onChange?: (key: string | string[]) => void;
  onDrop?: () => void;
}

const DraggableFormSection: React.FC<DraggableFormSectionProps> = ({
  id,
  header,
  index,
  moveSection,
  children,
  activeKey,
  onChange,
  onDrop
}) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'formSection',
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  });

  const [, drop] = useDrop({
    accept: 'formSection',
    hover: (item: { index: number }) => {
      if (item.index !== index) {
        moveSection(item.index, index);
        item.index = index;
      }
    },
    drop: () => {
      onDrop?.();
    }
  });

  const dragDropRef = (node: HTMLDivElement | null) => {
    drag(drop(node));
  };

  return (
    <div 
      ref={dragDropRef} 
      style={{ opacity: isDragging ? 0.5 : 1 }}
      className="mb-2"
    >
      <Accordion
        type={Array.isArray(activeKey) ? "multiple" : "single"}
        value={activeKey as any}
        onValueChange={(val) => onChange?.(val as any)}
        className="draggable-section border border-border-tertiary rounded-lg"
      >
        <AccordionItem value={id} className="border-none">
          <AccordionTrigger className="text-left py-4 px-6 flex gap-3 items-center">    
            <div className="flex items-center justify-between w-full">
              <DragOutlined className="mr-2 text-muted-foreground cursor-move" />
              {header}
            </div>
          </AccordionTrigger>
          <AccordionContent className='px-6'>
            {children}
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default DraggableFormSection;