"""
Topic Distribution Service

This service handles database operations for topic distribution.
It separates database concerns from pure business logic.

Key Features:
- Database operations for topic distribution
- Round management and tracking
- Topic usage tracking
- Error handling and logging

Author: Speaker <PERSON>t Team
Version: 2.0 (Refactored)
Last Updated: 2024
"""

from typing import List, Dict, Tuple, Optional
from datetime import datetime
from app.helpers.database_manager import db_manager
from sqlalchemy.exc import SQLAlchemyError

from app.config.config import config
from app.config.logger import get_logger
from app.models.subcategory import SubCategory, TopicDistribution, Base
from app.helpers.topic_distribution_helper import (
    distribute_topics_with_rotation,
    create_topic_id_to_name_mapping,
    SEARCH_ENGINES
)

logger = get_logger(__name__, file_name="scraper.log")


class TopicDistributionService:
    """Service for managing topic distribution database operations."""
    def __init__(self):
        """Initialize the service with database connection."""
        self.Session = db_manager.SessionLocal
    def get_all_active_topics(self) -> List[Tuple[int, str]]:
        """
        Get all active topics with their IDs from subcategories table.
        Returns:
            List of tuples containing (topic_id, topic_name)
        Raises:
            Exception: If database operation fails
        """
        try:
            with db_manager.get_session() as session:
                subcategories = session.query(SubCategory).filter(
                    SubCategory.is_active == '1'
                ).all()
                topics = [(s.id, s.name) for s in subcategories]
                logger.info(f"Retrieved {len(topics)} active topics from database")
                return topics
                
        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving topics: {e}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error retrieving topics: {e}")
            raise
    
    def get_next_round_number(self) -> int:
        """
        Get the next round number for topic distribution.
        Returns:
            Next round number (1-based)
        """
        try:
            with db_manager.get_session() as session:
                last_round = session.query(TopicDistribution.distribution_round).order_by(
                    TopicDistribution.distribution_round.desc()
                ).first()
                
                next_round = (last_round[0] + 1) if last_round else 1
                logger.debug(f"Next round number: {next_round}")
                return next_round
                
        except SQLAlchemyError as e:
            logger.error(f"Database error getting round number: {e}")
            return 1  # Fallback to round 1
        except Exception as e:
            logger.error(f"Unexpected error getting round number: {e}")
            return 1
    
    def get_used_topic_ids_in_round(self, round_number: int) -> List[int]:
        """
        Get all topic IDs that were used in a specific round.
        Args:
            round_number: Round number to check
        Returns:
            List of used topic IDs
        """
        try:
            with db_manager.get_session() as session:
                used_topics = session.query(TopicDistribution.topic_id).filter_by(
                    distribution_round=round_number,
                    is_active=True
                ).all()
                
                topic_ids = [topic[0] for topic in used_topics]
                logger.debug(f"Found {len(topic_ids)} used topics in round {round_number}")
                return topic_ids
                
        except SQLAlchemyError as e:
            logger.error(f"Database error getting used topics: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error getting used topics: {e}")
            return []
    
    def clear_all_distribution_records(self) -> bool:
        """
        Clear all distribution records to start a fresh cycle.
        Returns:
            True if successful, False otherwise
        """
        try:
            with db_manager.get_session() as session:
                deleted_count = session.query(TopicDistribution).delete()
                session.commit()
                return True
                
        except SQLAlchemyError as e:
            logger.error(f"Database error clearing records: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error clearing records: {e}")
            return False
    
    def distribute_topics_optimized(self, round_number: int) -> Dict[str, any]:
        """
        Optimized topic distribution with exact logic:
        1. Base topics: perfectly divisible among engines
        2. Rotating topics: rotate systematically through engines
        3. Batch tracking: track when all topics used once
        
        Args:
            round_number: Current round number
            
        Returns:
            Dictionary containing engine-to-topics mapping and metadata
        """
        try:
            # Get all active topics from database
            all_topics = self.get_all_active_topics()
            total_topics = len(all_topics)
            if total_topics == 0:
                error_msg = "❌ No active topics found in database"
                logger.error(error_msg)
                return {"error": error_msg}
            # Get unused topics (simplified - no complex batch tracking)
            unused_topic_ids = self._get_unused_topics_simple()
            if not unused_topic_ids:
                # Clear all previous round records
                if self.clear_all_distribution_records():
                    logger.info("✅ Cleared all previous round entries")
                else:
                    logger.warning("⚠️ Failed to clear previous round entries")
 
                unused_topic_ids = [tid for tid, _ in all_topics]
           
            # Use optimized distribution logic
            distribution_data = distribute_topics_with_rotation(unused_topic_ids, round_number)
            # Create topic ID to name mapping for better readability
            topic_id_to_name = create_topic_id_to_name_mapping(all_topics)
            # Convert to final distribution format with topic names
            final_distribution = {}
            for engine, topics_data in distribution_data.items():
                base_topics = topics_data["base_topics"]
                rotating_topics = topics_data["rotating_topics"]
                # Combine base and rotating topics
                all_engine_topics = base_topics + rotating_topics
                # Map topic IDs to readable names
                topic_names = [topic_id_to_name.get(tid, f"Topic_{tid}") for tid in all_engine_topics]
                final_distribution[engine] = topic_names
                # Mark topics as used in database
                self._mark_topics_as_used_simple(
                    engine, base_topics, rotating_topics, round_number, topic_id_to_name
                )
            # Add simple metadata
            final_distribution["_metadata"] = {
                "round_number": round_number,
                "total_topics": len(unused_topic_ids),
                "distribution_type": "systematic_rotation",
                "timestamp": datetime.now().isoformat()
            }
            return final_distribution
            
        except Exception as e:
            error_msg = f"❌ Failed to distribute topics: {str(e)}"
            logger.error(error_msg)
            logger.exception("Full error details:")
            return {"error": error_msg}
    
    def _get_unused_topics_simple(self) -> List[int]:
        """Get topic IDs that haven't been used in the current round."""
        try:
            with db_manager.get_session() as session:
                used_topic_ids = session.query(TopicDistribution.topic_id)\
                    .filter(TopicDistribution.is_active == True)\
                    .all()
                used_topic_set = {tid[0] for tid in used_topic_ids}
                all_topics = self.get_all_active_topics()
                all_topic_ids = {tid for tid, _ in all_topics}
                unused_topic_ids = list(all_topic_ids - used_topic_set)
                return unused_topic_ids
        except Exception as e:
            logger.error(f"Failed to get unused topics: {str(e)}")
            return []
    
    def _mark_topics_as_used_simple(
        self, 
        engine: str, 
        base_topics: List[int], 
        rotating_topics: List[int], 
        round_number: int,
        topic_id_to_name: Dict[int, str]
    ):
        """Mark topics as used with simplified tracking."""
        try:
            with db_manager.get_session() as session:
                # Mark base topics
                for topic_id in base_topics:
                    distribution_record = TopicDistribution(
                        search_engine=engine,
                        topic_id=topic_id,
                        topic_name=topic_id_to_name.get(topic_id, f"Topic_{topic_id}"),
                        distribution_round=round_number,
                        is_rotating_topic=False,
                        used_at=datetime.now(),
                        is_active=True
                    )
                    session.add(distribution_record)
                
                # Mark rotating topics
                for topic_id in rotating_topics:
                    distribution_record = TopicDistribution(
                        search_engine=engine,
                        topic_id=topic_id,
                        topic_name=topic_id_to_name.get(topic_id, f"Topic_{topic_id}"),
                        distribution_round=round_number,
                        is_rotating_topic=True,
                        used_at=datetime.now(),
                        is_active=True
                    )
                    session.add(distribution_record)
                
                session.commit()
                logger.info(f"Marked {len(base_topics)} base + {len(rotating_topics)} rotating topics for {engine}")
                
        except Exception as e:
            logger.error(f"Failed to mark topics as used: {str(e)}")
            raise
