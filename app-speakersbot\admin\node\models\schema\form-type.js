const { DataTypes } = require('sequelize');
const connection = require('../connection');


const FormType = connection.define(
  'FormType',
  {
    id: {
      type: DataTypes.INTEGER,
      primaryKey: true,
      autoIncrement: true,
      comment: 'Primary key for the form type',
    },
    priority: {
      type: DataTypes.INTEGER,

      comment: 'Priority order of the form type',
    },
    title: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: 'Title or name of the form type',
    },
    is_deletable: {
      type: DataTypes.ENUM('0', '1'), // 0: not deletable, 1: deletable
      allowNull: true,
      defaultValue: '1',
      comment: 'Indicates if the form type is deletable',
    },
    created_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: connection.literal('CURRENT_TIMESTAMP'),
      comment: 'Record creation timestamp',
    },
    updated_at: {
      type: DataTypes.DATE,
      allowNull: false,
      defaultValue: DataTypes.NOW,
      comment: 'Record last update timestamp',
    },
    deleted_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: 'Record deletion timestamp(soft delete)',
    }
  },
  {
    tableName: 'form_types',
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    paranoid: true, // enables soft deletes
    deletedAt: "deleted_at",
  }
);


module.exports = FormType;
