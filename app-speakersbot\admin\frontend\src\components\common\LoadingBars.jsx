import React from "react";

const LoadingBars = () => {
  return (
    <div className="flex justify-center items-center h-screen">
      <div id="bars" className="flex justify-center items-center gap-1">
        {Array.from({ length: 10 }).map((_, i) => (
          <div
            key={i}
            className={`bar bg-muted-foreground w-2 animate-sound rounded-lg`}
            style={{
              animationDuration: `${500 + Math.floor(Math.random() * 100)}ms`,
              animationDelay: `-${Math.floor(Math.random() * 600)}ms`,
            }}
          />
        ))}
      </div>

      {/* custom keyframes */}
      <style>{`
        @keyframes sound {
          0% {
            opacity: 0.35;
            height: 3px;
          }
          100% {
            opacity: 1;
            height: 70px;
          }
        }
        .animate-sound {
          animation: sound 0ms linear infinite alternate;
        }
      `}</style>
    </div>
  );
};

export default LoadingBars;
