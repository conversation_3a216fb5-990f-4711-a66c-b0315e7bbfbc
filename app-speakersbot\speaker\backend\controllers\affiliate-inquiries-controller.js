const ApiResponse = require("../helpers/api-response");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const affiliateInquiriesService = require("../services/affiliate-inquiries-service");

/**
 * Create new affiliate inquiry
 */
exports.createAffiliateInquiry = async (req, res, next) => {
    try {
        const { status, message } = await affiliateInquiriesService.createAffiliateInquiry(req);
        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    }
    catch (error) {
        next(error);
    }
}

/**
 * Get affiliate inquiries history
 */
exports.getAffiliateInquiriesHistory = async (req, res, next) => {
    try {
        const result = await affiliateInquiriesService.getAffiliateInquiriesHistory(req);
        const { status, message, data, pageData } = result;
        
        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data, pagination:pageData }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    }
    catch (error) {
        next(error);
    }
}