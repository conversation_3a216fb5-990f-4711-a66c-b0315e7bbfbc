const connection = require('../connection');
const { DataTypes } = require('sequelize');

const Permissions = connection.define('Permissions', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    comment: 'Primary key for the permission',
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Name of the permission (must be unique)',
  },
  description: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Description of the permission',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Record creation timestamp',
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Record last update timestamp',
  },
  deleted_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Record deletion timestamp(soft delete)',
  }


}, {
  tableName: 'permissions',
  timestamps: true,
  createdAt: "created_at",
  updatedAt: "updated_at",
  paranoid: true,
  deletedAt: "deleted_at", 

});

module.exports = Permissions;

