from sqlalchemy import create_engine, Column, Integer, String, DateTime, text
from app.models.base import Base
from app.config.config import config

class SpeakerDetails(Base):
    __tablename__ = "speaker_details"
    
    id = Column(
        Integer, 
        primary_key=True, 
        autoincrement=True,
        comment='Primary key for the speaker details record'
    )
    speaker_id = Column(
        Integer, 
        nullable=False,
        comment='Foreign key referencing the speaker'
    )
    field_id = Column(
        String(255), 
        nullable=False,
        comment='Identifier for the form field'
    )
    
    key = Column(
        String(255), 
        nullable=False,
        comment='Key or name of the detail'
    )
    value = Column(
        String(255), 
        nullable=False,
        comment='Value of the detail'
    )
    
    created_at = Column(
        DateTime, 
        nullable=False, 
        server_default=text('CURRENT_TIMESTAMP'),
        comment='Record creation timestamp'
    )
    updated_at = Column(
        DateTime, 
        nullable=False, 
        server_default=text('CURRENT_TIMESTAMP'),
        comment='Record last update timestamp'
    )
    deleted_at = Column(
        DateTime,
        server_default=text('CURRENT_TIMESTAMP'),
        comment='Record deletion timestamp'
    )

def create_speaker_details_table():
    """Create the speaker_details table in the database"""
    db_url = config.get_database_url()
    engine = create_engine(db_url)
    Base.metadata.create_all(engine)

if __name__ == "__main__":
    create_speaker_details_table()
    print("Table 'speaker_details' created in MySQL database")
