from sqlalchemy import Column, Integer, String, Enum, DateTime, func
from sqlalchemy.ext.declarative import declarative_base
from app.config.config import config
from sqlalchemy import create_engine

Base = declarative_base()
class Category(Base):
    __tablename__ = "categories"

    id = Column(Integer, primary_key=True, autoincrement=True, comment="Primary key for the category")
    name = Column(String(100), nullable=False, unique=True, comment="Name of the category (must be unique)")
    created_at = Column(DateTime, nullable=False, server_default=func.now(), comment="Record creation timestamp")
    updated_at = Column(DateTime, nullable=False, server_default=func.now(), onupdate=func.now(), comment="Record last update timestamp")
    deleted_at = Column(DateTime, nullable=True, comment="Record deletion timestamp (soft delete)")

def create_category_table():
    """Create the topic_distribution table in the database"""
    db_url = config.get_database_url()
    engine = create_engine(db_url)
    Base.metadata.create_all(engine)
    print("Category table created successfully")

if __name__ == "__main__":
    create_category_table()
    print("Tables 'categories' created in MySQL database")