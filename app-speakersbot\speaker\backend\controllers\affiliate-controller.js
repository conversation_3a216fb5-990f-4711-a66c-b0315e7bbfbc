const ApiResponse = require("../helpers/api-response");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");

const affiliateService = require("../services/affiliate-service");

/**
 * Get affiliate names only
 */
exports.getAffiliateNames = async (req, res, next) => {
    try {
        const result = await affiliateService.getAffiliateNames(req);
        const { status, message, data,pagination } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data,pagination }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    } catch (error) {
        next(error);
    }
};

