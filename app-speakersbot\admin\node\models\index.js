const { Sequelize } = require('sequelize');
const sequelize = require('./connection');

const db = {};
db.Sequelize = Sequelize;
db.sequelize = sequelize;


// Import Models
db.Users = require('./schema/users');
db.Roles = require('./schema/roles');
db.Permissions = require('./schema/permissions');
db.RolePermissions = require('./schema/role-permissions');
db.AffiliateUsersDetails = require('./schema/affiliate-users-details');
db.Settings = require('./schema/setting');
db.OpportunityForm = require('./schema/opportunity-form');
db.ScrapingLogging = require('./schema/scraping-logging');
db.AffiliateInquiries = require('./schema/affiliate-inquiries');
db.AffiliateInquiriesHistory = require('./schema/affiliate-inquiries-history');
db.OpportunitiesUrl = require('./schema/opportunities-url');
db.TopicDistribution = require('./schema/topic-distribution');

// New Models
db.Category = require('./schema/category');
db.Subcategory = require('./schema/subcategory');

db.Speakers = require("./schema/speakers");
db.Opportunities = require('./schema/opportunities');
db.SpeakerOpportunity = require('./schema/speaker-opportunity');
db.SpeakerDetails = require('./schema/speaker-details');
db.PricingPlan = require('./schema/pricing-plan');
db.Subscriptions = require('./schema/subscriptions');
db.SpeakerHistory = require('./schema/speaker-history');

db.FormType = require('./schema/form-type');
db.FormQuestion = require('./schema/form-questions');
db.GamificationHistory = require('./schema/gamification-history');
db.GamificationRules = require('./schema/gamification-rules');
db.Streaks = require('./schema/streaks');
db.SpeakerOpportunityHistory = require('./schema/speaker-opportunity-history');
db.Feedback = require('./schema/feedback');


// 1 Role → Many Users
db.Roles.hasMany(db.Users, { foreignKey: 'role_id', sourceKey: 'id', as: 'users' });
db.Users.belongsTo(db.Roles, { foreignKey: 'role_id', targetKey: 'id', as: 'role' });

// Many Permissions ↔ Many Roles
db.Permissions.belongsToMany(db.Roles, {
    through: db.RolePermissions,
    foreignKey: 'permission_id',
    otherKey: 'role_id',
    as: 'roles'
});

// Many Roles ↔ Many Permissions
db.Roles.belongsToMany(db.Permissions, {
    through: db.RolePermissions,
    foreignKey: 'role_id',
    otherKey: 'permission_id',
    as: 'permissions'
});

// Each RolePermission belongs to one Role
db.RolePermissions.belongsTo(db.Roles, { foreignKey: 'role_id', as: 'role' });
db.Roles.hasMany(db.RolePermissions, { foreignKey: 'role_id', as: 'rolePermissions' });

// Each RolePermission belongs to one Permission
db.RolePermissions.belongsTo(db.Permissions, { foreignKey: 'permission_id', as: 'permission' });
db.Permissions.hasMany(db.RolePermissions, { foreignKey: 'permission_id', as: 'rolePermissions' });



db.Users.hasMany(db.AffiliateUsersDetails, { foreignKey: 'affiliate_id', as: 'affiliateUsersDetails' });
db.AffiliateUsersDetails.belongsTo(db.Users, { foreignKey: 'affiliate_id', as: 'user' });


// 1 Category → Many SubCategories
db.Category.hasMany(db.Subcategory, { foreignKey: 'category_id', as: 'subCategories', onDelete: 'CASCADE' });
db.Subcategory.belongsTo(db.Category, { foreignKey: 'category_id', as: 'category' });


// Many-to-Many: Speakers <-> Opportunities through SpeakerOpportunity
db.Speakers.belongsToMany(db.Opportunities, {
    through: db.SpeakerOpportunity,
    foreignKey: 'speaker_id',
    otherKey: 'opportunity_id',
    as: 'opportunities'
});


db.Opportunities.belongsToMany(db.Speakers, {
    through: db.SpeakerOpportunity,
    foreignKey: 'opportunity_id',
    otherKey: 'speaker_id',
    as: 'speakers'
});

// Through table associations
db.SpeakerOpportunity.belongsTo(db.Speakers, { foreignKey: 'speaker_id', as: 'speaker' });
db.SpeakerOpportunity.belongsTo(db.Opportunities, { foreignKey: 'opportunity_id', as: 'opportunity' });

db.Speakers.hasMany(db.SpeakerOpportunity, { foreignKey: 'speaker_id', as: 'speakerOpportunities' });
db.Opportunities.hasMany(db.SpeakerOpportunity, { foreignKey: 'opportunity_id', as: 'speakerOpportunities' });



db.Speakers.hasMany(db.SpeakerDetails, { foreignKey: 'speaker_id', as: 'details' });
db.SpeakerDetails.belongsTo(db.Speakers, { foreignKey: 'speaker_id', as: 'speaker' });

db.FormType.hasMany(db.FormQuestion, { foreignKey: 'form_type_id', as: 'questions' });
db.FormQuestion.belongsTo(db.FormType, { foreignKey: 'form_type_id', as: 'phase' });

// FormQuestion <-> SpeakerDetails (One-to-Many)
// Disable DB-level FK constraints here because form_questions.field_id is a non-primary unique key
// and creating FK constraints at sync time can fail in some DB setups. We rely on application-level join using sourceKey/targetKey.
db.FormQuestion.hasMany(db.SpeakerDetails, { foreignKey: 'field_id', sourceKey: 'field_id', as: 'speakerDetails', constraints: false });
db.SpeakerDetails.belongsTo(db.FormQuestion, { foreignKey: 'field_id', targetKey: 'field_id', as: 'formQuestion', constraints: false });

// Speaker <-> Subscription (One-to-One)
db.Speakers.hasOne(db.Subscriptions, { foreignKey: 'speaker_id', as: 'subscription' });
db.Subscriptions.belongsTo(db.Speakers, { foreignKey: 'speaker_id', as: 'speaker' });

// PricingPlan <-> Subscriptions (One-to-Many)
db.PricingPlan.hasMany(db.Subscriptions, { foreignKey: 'plan_id', sourceKey: 'id', as: 'subscriptions' });
db.Subscriptions.belongsTo(db.PricingPlan, { foreignKey: 'plan_id', targetKey: 'id', as: 'plan' });


db.Users.hasMany(db.Speakers, { foreignKey: 'affiliate_id', as: 'affiliateSpeakers' });
db.Speakers.belongsTo(db.Users, { foreignKey: 'affiliate_id', as: 'affiliateUser' });

db.Speakers.hasMany(db.GamificationHistory, { foreignKey: 'speaker_id', as: 'gamificationData' });
db.GamificationHistory.belongsTo(db.Speakers, { foreignKey: 'speaker_id', as: 'speaker' });

db.AffiliateUsersDetails.hasMany(db.Speakers, { foreignKey: 'affiliate_id', as: 'speakers' });
db.Speakers.belongsTo(db.AffiliateUsersDetails, { foreignKey: 'affiliate_id', as: 'affiliate' });

db.Speakers.hasMany(db.SpeakerHistory, { foreignKey: 'speaker_id', as: 'speakerHistory' });
db.SpeakerHistory.belongsTo(db.Speakers, { foreignKey: 'speaker_id', as: 'speaker' });

// AffiliateInquiries associations
db.Speakers.hasMany(db.AffiliateInquiries, { foreignKey: 'speaker_id', as: 'affiliateInquiries' });
db.AffiliateInquiries.belongsTo(db.Speakers, { foreignKey: 'speaker_id', as: 'speaker' });

// AffiliateInquiries.affiliate_id references AffiliateUsersDetails.id
db.AffiliateUsersDetails.hasMany(db.AffiliateInquiries, {
    foreignKey: 'affiliate_id',
    sourceKey: 'id',
    as: 'affiliateInquiries',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
});
db.AffiliateInquiries.belongsTo(db.AffiliateUsersDetails, {
    foreignKey: 'affiliate_id',
    targetKey: 'id',
    as: 'affiliate',
    onDelete: 'CASCADE',
    onUpdate: 'CASCADE'
});

db.AffiliateInquiries.hasMany(db.AffiliateInquiriesHistory, { foreignKey: 'inquiry_id', as: 'affiliateInquiriesHistory' });
db.AffiliateInquiriesHistory.belongsTo(db.AffiliateInquiries, { foreignKey: 'inquiry_id', as: 'affiliateInquiry' });

db.GamificationHistory.belongsTo(db.GamificationRules, { foreignKey: 'gamification_id', as: 'action' });
db.GamificationRules.hasMany(db.GamificationHistory, { foreignKey: 'gamification_id', as: 'actions' });

db.Speakers.hasMany(db.Feedback, { foreignKey: 'speaker_id', as: 'feedbacks' });
db.Feedback.belongsTo(db.Speakers, { foreignKey: 'speaker_id', as: 'speaker' });

db.Opportunities.hasOne(db.OpportunityForm, { foreignKey: 'opportunity_id', as: 'forms-data' });
db.OpportunityForm.belongsTo(db.Opportunities, { foreignKey: 'opportunity_id', as: 'opportunity' });

db.AffiliateUsersDetails.hasMany(db.Subscriptions, {foreignKey:'affiliate_id' , as: 'referrel-affiliate'});
db.Subscriptions.belongsTo(db.AffiliateUsersDetails,{foreignKey:'affiliate_id', as: 'affiliate details'});

db.SpeakerOpportunity.hasMany(db.SpeakerOpportunityHistory, { foreignKey: 'speaker_opportunity_id', as: 'history' });
db.SpeakerOpportunityHistory.belongsTo(db.SpeakerOpportunity, { foreignKey: 'speaker_opportunity_id', as: 'speakerOpportunity' });

module.exports = db;