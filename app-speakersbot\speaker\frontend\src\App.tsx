import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { useEffect } from "react";
import {
  BrowserRouter,
  Routes,
  Route,
  Navigate,
  useNavigate,
} from "react-router-dom";
import ProtectedRoute from "@/components/auth/ProtectedRoute";
import PublicRoute from "@/components/auth/PublicRoute";
import { useDispatch } from "react-redux";
import { clearCredentials } from "@/store/slices/authSlice";
import Login from "./pages/auth/Login";
import ForgotPassword from "./pages/auth/ForgotPassword";
import ResetPassword from "./pages/auth/ResetPassword";
import Profile from "./pages/Profile";
import DashboardLayout from "./layouts/DashboardLayout";
import DashboardPage from "./pages/dashboard/DashboardPage";
import IntakePage from "./pages/intake-form/IntakePage";
import { SubscriptionPage } from "./pages/subscriptions/SubscriptionPage";
import { XPPage } from "./pages/xp-points/XPPage";
import { InvitePage } from "./pages/invite-page/InvitePage";
import EarlyAccess from "./pages/landing-page/EarlyAccess";

const queryClient = new QueryClient();

const AppRoutes = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleLogin = () => {
    navigate("/speaker/dashboard");
  };

  const handleLogout = () => {
    dispatch(clearCredentials());
    navigate("/speaker");
  };

  return (
    <Routes>
      <Route
        path="/"
        element={
          <PublicRoute>
            <EarlyAccess />
          </PublicRoute>
        }
      />
      <Route
        path="/speaker"
        element={
          <PublicRoute>
            <Login onLogin={handleLogin} />
          </PublicRoute>
        }
      />
      <Route
        path="/speaker/forgot-password"
        element={
          <PublicRoute>
            <ForgotPassword />
          </PublicRoute>
        }
      />
      <Route
        path="/speaker/reset-password"
        element={
          <PublicRoute>
            <ResetPassword />
          </PublicRoute>
        }
      />
      <Route
        path="/speaker/dashboard"
        element={
          <ProtectedRoute>
            <DashboardLayout onLogout={handleLogout}>
              <DashboardPage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/speaker/intake"
        element={
          <ProtectedRoute>
            <DashboardLayout onLogout={handleLogout}>
              <IntakePage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/speaker/subscription"
        element={
          <ProtectedRoute>
            <DashboardLayout onLogout={handleLogout}>
              <SubscriptionPage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/speaker/xp"
        element={
          <ProtectedRoute>
            <DashboardLayout onLogout={handleLogout}>
              <XPPage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/speaker/invite"
        element={
          <ProtectedRoute>
            <DashboardLayout onLogout={handleLogout}>
              <InvitePage />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route
        path="/speaker/profile"
        element={
          <ProtectedRoute>
            <DashboardLayout onLogout={handleLogout}>
              <Profile />
            </DashboardLayout>
          </ProtectedRoute>
        }
      />
      <Route path="*" element={<Navigate to="/speaker" replace />} />
    </Routes>
  );
};

const App = () => {
  useEffect(() => {
    // Always set dark mode
    document.documentElement.className = "dark";
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <div className="dark min-h-screen bg-background">
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <AppRoutes />
          </BrowserRouter>
        </div>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
