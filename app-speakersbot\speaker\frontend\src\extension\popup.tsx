import React, { useEffect } from 'react';
import { createRoot } from 'react-dom/client';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { TooltipProvider } from '@/components/ui/tooltip';
import { Toaster } from '@/components/ui/toaster';
import { Toaster as Sonner } from '@/components/ui/sonner';
import { ExtensionApp } from './components/ExtensionApp';
import '../index.css';

const queryClient = new QueryClient();

const PopupRoot = () => {
  useEffect(() => {
    // Always set dark mode
    document.documentElement.className = 'dark';
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <div className="dark min-h-screen bg-background popup-container">
          <Toaster />
          <Sonner />
          <ExtensionApp />
        </div>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

// Initialize the popup
const container = document.getElementById('root');
if (container) {
  const root = createRoot(container);
  root.render(<PopupRoot />);
}