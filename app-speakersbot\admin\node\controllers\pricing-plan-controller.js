const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const pricingPlanService = require("../services/pricing-plan-service.js");

/**
 * Create new pricing plan
 */
exports.createPlan = async (req, res, next) => {
    try {
        const { status, message, data } = await pricingPlanService.createPlan(req.body);
        if (status) res.status(RESPONSE_CODES.SUCCESS).json({ message: RESPONSE_MESSAGES.SUCCESS, data });
        else res.status(RESPONSE_CODES.BAD_REQUEST).json({ status: false, message: message || RESPONSE_MESSAGES.BAD_REQUEST });
    } catch (error) {
        next(error)
    }
};

/**
 * Get all active pricing plans
 */
exports.getPlans = async (req, res, next) => {
    try {
        const { status, message, data } = await pricingPlanService.getPlans();
        if (status) res.status(RESPONSE_CODES.SUCCESS).json({ message: RESPONSE_MESSAGES.SUCCESS, data });
        else res.status(RESPONSE_CODES.SUCCESS).json({ status: false, message: message || RESPONSE_MESSAGES.NOT_FOUND, data: [] });
    } catch (error) {
        next(error)
    }
}

/**
 * Update pricing plan by id
 */
exports.updatePlan = async (req, res, next) => {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const { status, message, data } = await pricingPlanService.updatePlan(id, updateData);
        if (status) res.status(RESPONSE_CODES.SUCCESS).json({ message: RESPONSE_MESSAGES.SUCCESS, data });
        else res.status(RESPONSE_CODES.BAD_REQUEST).json({ status: false, message: message || RESPONSE_MESSAGES.BAD_REQUEST });
    } catch (error) {
        next(error);
    }
}

/**
 * Delete pricing plan by id
 */
exports.deletePlan = async (req, res, next) => {
    try {
        const { id } = req.params;
        const { status, message, data } = await pricingPlanService.deletePlan(id);
        if (status) res.status(RESPONSE_CODES.SUCCESS).json({ message: RESPONSE_MESSAGES.SUCCESS, data });
        else res.status(RESPONSE_CODES.BAD_REQUEST).json({ status: false, message: message });
    } catch (error) {
        next(error);
    }
}