const { DataTypes } = require("sequelize");
const connection = require("../connection");

const AffiliateInquiries = connection.define("AffiliateInquiries", {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: 'Primary key for the meeting schedule',
    },
    speaker_id: {
        type: DataTypes.INTEGER,
        allowNull: false,

    },
    affiliate_id: {
        type: DataTypes.INTEGER,
        allowNull: false,

    },
    amount: {
        type: DataTypes.FLOAT,
        allowNull: false,
        comment: 'Amount for the affiliate inquiry',
    },
    xp_points_status: {
        type: DataTypes.ENUM('pending', 'captured', 'release'),
        defaultValue: 'pending',
        comment: 'XP points status for the affiliate inquiry'
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW,
    },

}, {
    tableName: 'affiliate_inquiries',
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",

});

module.exports = AffiliateInquiries;