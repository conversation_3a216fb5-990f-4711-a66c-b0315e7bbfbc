import React, { useEffect } from "react";
// App shell: Shadcn sidebar layout, header controls, and content container
import { useLazyGetAuthUserQuery } from "@/apis";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { setCredentials } from "@/store/slices/authSlice";
import {
  CalendarOutlined,
  CloudSyncOutlined,
  DashboardOutlined,
  DollarOutlined,
  FormOutlined,
  LinkOutlined,
  LogoutOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  MoonOutlined,
  SettingOutlined,
  ShareAltOutlined,
  SunOutlined,
  TeamOutlined,
  UserOutlined,
} from "@ant-design/icons";
import clsx from "clsx";
import { useLocation, useNavigate } from "react-router-dom";
import { usePermissions } from "../../hooks/usePermissions";
import { useAppState } from "../../state/AppStateProvider";
import { useAuth } from "../../state/AuthContext";
import type { Permission } from "../../store/slices/rbacSlice";
import { InvalidTokenHandler } from "../common/InvalidTokenHandler";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Button } from "../ui/button";
import LogoLight from "/logo-light.png";
import LogoDark from "/logo-dark.png";
import { Skeleton } from "../ui/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "../ui/dropdown-menu";
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarInset,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
} from "../ui/sidebar";
import { Switch } from "../ui/switch";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../ui/tooltip";

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const { user, logout } = useAuth();
  const { ui, dispatch } = useAppState();
  const token = useAppSelector((s) => s.auth.token);
  const dispatchState = useAppDispatch();
  const location = useLocation();
  const navigate = useNavigate();
  const [getAuthUser, { error: errorUsers }] = useLazyGetAuthUserQuery();
  const { hasPermission } = usePermissions();

  const handleLogout = () => {
    logout();
    navigate("/admin/login");
  };

  // Helper function to determine if a menu item should be active
  const isMenuItemActive = (menuKey: string, currentPath: string) => {
    // Exact match
    if (currentPath == menuKey) {
      return true;
    }

    // For nested routes, check if current path starts with the menu key
    // but exclude exact matches to avoid conflicts
    if (currentPath.startsWith(menuKey + "/")) {
      return true;
    }

    return false;
  };

  // Primary navigation items rendered in the sidebar with permission checks
  const menuItems: Array<{
    key: string;
    icon: React.ReactNode;
    label: string;
    onClick: () => void;
    permission?: Permission;
    permissions?: Permission[]; // any-of permissions
  }> = [
    {
      key: "/admin/dashboard",
      icon: <DashboardOutlined />,
      label: "Dashboard",
      permission: "read_dashboard" as const,
      onClick: () => navigate("/admin/dashboard"),
    },
    {
      key: "/admin/opportunities",
      icon: <CalendarOutlined />,
      label: "Opportunities",
      permission: "read_opportunities" as const,
      onClick: () => navigate("/admin/opportunities"),
    },
    {
      key: "/admin/speakers",
      icon: <TeamOutlined />,
      label: "Speaker Management",
      permission: "read_speakers" as const,
      onClick: () => navigate("/admin/speakers"),
    },
    {
      key: "/admin/matching",
      icon: <LinkOutlined />,
      label: "Matching",
      permission: "read_matching_queue" as const,
      onClick: () => navigate("/admin/matching"),
    },
    {
      key: "/admin/users",
      icon: <UserOutlined />,
      label: "Manage Users",
      permission: "read_users" as const,
      onClick: () => navigate("/admin/users"),
    },
    {
      key: "/admin/STAiGENT-identity-profile",
      icon: <FormOutlined />,
      label: "STAiGENT Identity Profile",
      permission: "read_form_types" as const,
      onClick: () => navigate("/admin/STAiGENT-identity-profile"),
    },
    {
      key: "/admin/scraping",
      icon: <CloudSyncOutlined />,
      label: "Scraping",
      permission: "read_scraping_logging" as const,
      onClick: () => navigate("/admin/scraping"),
    },
    {
      key: "/admin/settings",
      icon: <SettingOutlined />,
      label: "Settings",
      permission: "write_settings" as const,
      onClick: () => navigate("/admin/settings"),
    },
    {
      key: "/admin/pricing",
      icon: <DollarOutlined />,
      label: "Pricing Plans",
      permission: "read_pricing_plans" as const,
      onClick: () => navigate("/admin/pricing"),
    },
    {
      key: "/admin/affiliates",
      icon: <ShareAltOutlined />,
      label: "Affiliates",
      // show if user can view affiliates list OR can view their own affiliate details
      permissions: ["read_affiliates", "read_affiliate_users_details"] as const,
      onClick: () => navigate("/admin/affiliates"),
    },
  ];

  // Derive simple breadcrumbs from current route path
  const getBreadcrumbs = () => {
    const pathSegments = location.pathname.split("/").filter(Boolean);
    const breadcrumbs = [{ title: "Home" }];

    let currentPath = "";
    pathSegments.forEach((segment, index) => {
      // Skip the "admin" segment in breadcrumbs
      if (segment === "admin") return;

      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;
      let title =
        segment.charAt(0).toUpperCase() + segment.slice(1).replace("-", " ");
      if (segment === "STAiGENT-identity-profile") title = "STAiGENT Identity Profile";
      if (segment === "matching") title = "Matching Queue";
      breadcrumbs.push({ title });
    });

    return breadcrumbs;
  };
  const fetchAuthUser = async () => {
    try {
      const userResult = await getAuthUser(undefined).unwrap();
      if (userResult.status && userResult.data) {
        const userData = userResult.data;
        dispatchState(setCredentials({ user: userData, token }));
      }
    } catch (userError) {
      console.error("Error fetching user data:", userError);
    }
  };

  useEffect(() => {
    fetchAuthUser();
  }, [token]);

  // Redirect affiliates from dashboard to affiliates page
  useEffect(() => {
    if (
      user?.role?.name === "Affiliate" &&
      location.pathname === "/admin/dashboard"
    ) {
      navigate(`/admin/affiliates/${user.id}`, { replace: true });
    }
  }, [user, location.pathname, navigate]);

  return (
    // Control sidebar open/close via global UI state
    <>
      <InvalidTokenHandler error={errorUsers} />
      <SidebarProvider
        defaultOpen={!ui.sidebarCollapsed}
        open={!ui.sidebarCollapsed}
        onOpenChange={(open) => {
          const shouldBeOpen = !ui.sidebarCollapsed;
          if (open !== shouldBeOpen) {
            dispatch({ type: "TOGGLE_SIDEBAR" });
          }
        }}
      >
        {/* Left navigation rail */}
        <Sidebar
          variant="sidebar"
          collapsible="icon"
          className="transition-all duration-300 ease-in-out !w-unset"
        >
          {/* Product mark / Logo */}
          <SidebarHeader style={{ padding: 0, margin: 16 }}>
            <div>
              <p className="transition-all duration-300 ease-in-out text-sm text-center">
                {ui.sidebarCollapsed ? (
                  "SB"
                ) : ui.theme === "dark" ? (
                  <img
                    src={LogoLight}
                    alt="Speaker Bot"
                    className="w-full max-w-[183px] h-full object-contain mx-auto"
                  />
                ) : (
                  <img
                    src={LogoDark}
                    alt="Speaker Bot"
                    className="w-full max-w-[183px] h-full object-contain mx-auto"
                  />
                )}
              </p>
            </div>
          </SidebarHeader>

          <SidebarContent>
            <SidebarGroup>
              <SidebarGroupContent>
                <SidebarMenu>
                  {/* Sidebar navigation links with permission filtering */}
                  {menuItems
                    .filter((item) => {
                      if (item.permission)
                        return hasPermission(item.permission);
                      if (item.permissions && item.permissions.length > 0)
                        return (
                          hasPermission(item.permissions[0]) ||
                          item.permissions.some((p) =>
                            hasPermission(p as Permission)
                          )
                        );
                      return true;
                    })
                    .map((item) => (
                      <SidebarMenuItem key={item.key}>
                        <SidebarMenuButton
                          onClick={item.onClick}
                          isActive={isMenuItemActive(
                            item.key,
                            location.pathname
                          )}
                          tooltip={item.label}
                          className={clsx(
                            "transition-all w-full duration-200 ease-in-out h-10 hover:bg-muted",
                            isMenuItemActive(item.key, location.pathname) &&
                              "!bg-dashboard-dark"
                          )}
                          style={{
                            padding: ui.sidebarCollapsed
                              ? "0 28px"
                              : "0 16px 0 24px",
                          }}
                        >
                          {item.icon}
                          <span>{item.label}</span>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                    ))}
                </SidebarMenu>
              </SidebarGroupContent>
            </SidebarGroup>
          </SidebarContent>
        </Sidebar>

        <SidebarInset
          className={`${
            ui.sidebarCollapsed
              ? "max-w-[calc(100dvw-79px)]"
              : "max-w-[calc(100dvw-255px)]"
          }`}
        >
          {/* Top app bar with breadcrumbs, theme toggle, and user menu */}
          <header className="flex h-16 items-center justify-between border-b bg-card px-4 transition-all duration-300 ease-in-out sticky top-0 z-20">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="icon"
                className="h-7 w-7 transition-all duration-200 ease-in-out hover:bg-muted hover:text-muted-foreground"
                onClick={() => dispatch({ type: "TOGGLE_SIDEBAR" })}
              >
                <span className="transition-transform duration-200 ease-in-out">
                  {ui.sidebarCollapsed ? (
                    <MenuUnfoldOutlined />
                  ) : (
                    <MenuFoldOutlined />
                  )}
                </span>
                <span className="sr-only">Toggle Sidebar</span>
              </Button>
              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                {/* Simple breadcrumb trail */}
                {getBreadcrumbs().map((crumb, index) => (
                  <React.Fragment key={index}>
                    {index > 0 && <span>/</span>}
                    <span
                      className={
                        index === getBreadcrumbs().length - 1
                          ? "text-foreground"
                          : ""
                      }
                    >
                      {crumb.title}
                    </span>
                  </React.Fragment>
                ))}
              </div>
            </div>

            <div className="flex items-center gap-4">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <div className="relative transition-all duration-200 ease-in-out h-[23px]">
                      {/* Theme toggle switch with sun/moon indicators */}
                      <Switch
                        checked={ui.theme === "dark"}
                        onCheckedChange={() =>
                          dispatch({ type: "TOGGLE_THEME" })
                        }
                        className="data-[state=checked]:bg-blue-500 transition-all duration-300 ease-in-out h-full"
                      />
                      <SunOutlined
                        className={`h-3 w-3 absolute right-1.5 top-1.5 transition-all duration-300 ease-in-out pointer-events-none z-20 ${
                          ui.theme === "dark"
                            ? "opacity-0 scale-75"
                            : "opacity-100 scale-100"
                        }`}
                      />
                      <MoonOutlined
                        className={`h-3 w-3 absolute left-1.5 top-1.5 transition-all duration-300 ease-in-out pointer-events-none z-20 ${
                          ui.theme === "dark"
                            ? "opacity-100 scale-100"
                            : "opacity-0 scale-75"
                        }`}
                      />
                    </div>
                  </TooltipTrigger>
                  <TooltipContent>
                    {ui.theme === "dark"
                      ? "Switch to Light Theme"
                      : "Switch to Dark Theme"}
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="flex items-center gap-2 transition-all duration-200 ease-in-out hover:bg-transparent hover:text-inherit"
                  >
                    {/* Current user avatar and name */}
                    <Avatar className="h-8 w-8 transition-all duration-200 ease-in-out">
                      <AvatarImage src="" />
                      <AvatarFallback className="bg-gradient-to-r from-blue-500 to-blue-600 text-white transition-all duration-200 ease-in-out">
                        <UserOutlined />
                      </AvatarFallback>
                    </Avatar>
                    <span className="transition-all duration-200 ease-in-out">
                      {user?.name ? (
                        user.name
                      ) : (
                        <Skeleton className="h-4 w-24 rounded-lg" />
                      )}
                    </span>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {/* <DropdownMenuItem onClick={() => {}}>
                    <UserOutlined className="mr-2 h-4 w-4" />
                    Profile
                  </DropdownMenuItem> */}
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogoutOutlined className="mr-2 h-4 w-4" />
                    Logout
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </header>

          {/* Routed page content */}
          <main className="flex-1 p-6 transition-all duration-300 ease-in-out">
            <div className="min-h-[calc(100dvh-112px)] rounded-lg border bg-card p-6 shadow-sm transition-all duration-300 ease-in-out hover:shadow-md">
              {children}
            </div>
          </main>
        </SidebarInset>
      </SidebarProvider>
    </>
  );
};

export default AppLayout;
