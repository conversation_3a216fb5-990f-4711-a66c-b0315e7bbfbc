const dotenv = require('dotenv');
dotenv.config();
dotenv.config({
  path: `./${process.env.NODE_ENV}.env`
});

const CONFIG = {
  NODE_ENV: process.env.NODE_ENV,
  PORT: process.env.PORT,
  JWT_SECRET: process.env.JWT_SECRET,
  HOST: process.env.HOST,
  FRONTEND_BASE_URL: process.env.FRONTEND_BASE_URL,
  EXTENSION_BASE_URL: process.env.EXTENSION_BASE_URL,
  ADMIN_BASE_URL: process.env.ADMIN_BASE_URL,
  database: {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    dialect: process.env.DB_DIALECT || 'mysql',
    logging: process.env.DB_LOGGING === 'true',
    pool: {
      max: parseInt(process.env.DB_POOL_MAX) || 5,
      min: parseInt(process.env.DB_POOL_MIN) || 0,
      acquire: parseInt(process.env.DB_POOL_ACQUIRE) || 30000,
      idle: parseInt(process.env.DB_POOL_IDLE) || 10000
    }
  },
  SMTP: { 
    USER: process.env.SMTP_USER,
    PASS: process.env.SMTP_PASS,
    HOST: process.env.SMTP_HOST,
    PORT: process.env.SMTP_PORT,
  }
}

module.exports = CONFIG;
