const CustomError = require("../helpers/custome-error");
const Setting = require("../models/schema/setting");
const appHelper = require("../helpers/app-hepler");
const settingService = {};

// ------------------------- setting-service -------------------------

/**
 * Get email template by key
 */
settingService.getEmailTemplateByKey = async (key) => {
  try {
    const setting = await Setting.findOne({ where: { key } });
    if (!setting) return null;
    
    try {
      const template = JSON.parse(setting.value);
      return template;
    } catch (parseError) {
      console.error(`Error parsing email template for key ${key}:`, parseError);
      return null;
    }
  }
  catch (err) {
    return null;
  }
}

module.exports = settingService;
