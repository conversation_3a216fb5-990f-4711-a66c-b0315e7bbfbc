// Opportunities page: listing, filtering, sorting, pagination, CRUD and import/export
// Notes on data model:
// - Server performs search/filter/sort/pagination; client only applies local date picker visuals
// - All query params are composed in `apiQueryParams` and passed to RTK Query hook
import React, { useState, useMemo, useEffect } from 'react';
import { 
  PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined, CopyOutlined,
  SearchOutlined, FilterOutlined, ExportOutlined, ImportOutlined, 
  SettingOutlined, MoreOutlined, LinkOutlined, ReloadOutlined
} from '@ant-design/icons';
import { CaretUpOutlined, CaretDownOutlined } from '@ant-design/icons';
import { Search, RotateCcw, Calendar, MoreHorizontal, Trash2, Eye, Check, ChevronsUpDown } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Textarea } from '../components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../components/ui/form';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { Badge } from '../components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../components/ui/tooltip';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '../components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '../components/ui/alert-dialog';
import { Checkbox } from '../components/ui/checkbox';
import { Calendar as CalendarComponent } from '../components/ui/calendar';
import DatePicker from "react-multi-date-picker";
import { Popover, PopoverContent, PopoverTrigger } from '../components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '../components/ui/command';
import { cn } from '../lib/utils';
import { format } from 'date-fns';
import { toast } from "../hooks/use-toast";
import { useAppState } from '../state/AppStateProvider';
import { 
  useGetOpportunitiesQuery,
  useCreateOpportunityMutation,
  useUpdateOpportunityMutation,
  useDeleteOpportunityMutation,
  useExportOpportunitiesMutation,
  useImportOpportunitiesMutation,
  useGetEventTypesQuery,
  useGetAllCityQuery,
} from '../apis/opportunitiesApi';
import { useAuth } from '../state/AuthContext';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import type { Opportunity, OpportunityStatus, OpportunityCategory, UserRole } from '../types';
import { getPermissions, createActivityLog } from '../utils/permissions';
import { exportOpportunitiesToCsv } from '../utils/import-export';
import CsvImportModal from '../components/CsvImportModal';
import StatusTag from '@/components/common/StatusTag';
import { Skeleton } from '@/components/ui/skeleton';
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "../components/ui/pagination";
import { InvalidTokenHandler } from "../components/common/InvalidTokenHandler";

// Validation schema for the add/edit opportunity modal
const opportunitySchema = z.object({
  title: z.string().min(1, 'Please enter title'),
  organization: z.string().min(1, 'Please enter organization'),
  description: z.string().min(1, 'Please enter description'),
  category: z.string().min(1, 'Please select category'),
  status: z.string().min(1, 'Please select status'),
  budget: z.string().min(1, 'Please enter budget'),
  location: z.string().min(1, 'Please enter location'),
  externalUrl: z.string().optional(),
  eventDate: z.date().optional(),
  deadline: z.date().optional(),
  tags: z.string().optional(),
  requirements: z.string().optional(),
}).refine((data) => {
  if (data.deadline && data.eventDate) {
    return data.deadline < data.eventDate;
  }
  return true;
}, {
  message: "Deadline must be before event date",
  path: ["deadline"],
});


interface ColumnVisibility {
  [key: string]: boolean;
}

const DEFAULT_COLUMNS = {
  title: true,
  organization: true,
  eventType: true,
  dateRange: true,
  location: true,
  status: true,
  actions: true
};

const Opportunities: React.FC = () => {
  const { matches, speakers, dispatch } = useAppState();
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [sort, setSort] = useState<Record<string, 'asc' | 'desc'> | undefined>(undefined);
  const [createOpportunity] = useCreateOpportunityMutation();
  const [updateOpportunity] = useUpdateOpportunityMutation();
  const [deleteOpportunity] = useDeleteOpportunityMutation();
  const [exportOpportunities] = useExportOpportunitiesMutation();
  const [importOpportunities] = useImportOpportunitiesMutation();
  const { user } = useAuth();
  const navigate = useNavigate();
  const form = useForm<z.infer<typeof opportunitySchema>>({
    resolver: zodResolver(opportunitySchema),
    defaultValues: {
      status: 'draft',
      category: 'all',
    },
  });
  const permissions = getPermissions(user?.role as unknown as UserRole);
  
  // UI and filter state (search is debounced before being sent to API)
  const [searchText, setSearchText] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState<string>('');
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [categoryFilter, setCategoryFilter] = useState<string> ('');
  const { data: eventTypesResp } = useGetEventTypesQuery();
  // Build unique, normalized event type options
  const eventTypeOptions = useMemo(() => {
    const source: string[] = Array.isArray(eventTypesResp)
      ? (eventTypesResp as string[])
      : (eventTypesResp?.data as string[]) || [];
    const firstSeenByLower = new Map<string, string>();
    for (const et of source) {
      const raw = String(et || '').trim();
      if (!raw) continue;
      const lower = raw.toLowerCase();
      if (!firstSeenByLower.has(lower)) firstSeenByLower.set(lower, raw);
    }
    return Array.from(firstSeenByLower.entries()).map(([value, label]) => ({ value, label }));
  }, [eventTypesResp]);
  const [dateRange, setDateRange] = useState(null);
  const [countryFilter, setCountryFilter] = useState<string>('');
  const { data: citiesResp } = useGetAllCityQuery({});
  const cityOptions = useMemo(() => {
    const list = (citiesResp as any)?.data || (Array.isArray(citiesResp) ? citiesResp : []);
    const unique = Array.from(new Set(list.map((c: any) => String(c).trim()).filter(Boolean)));
    return unique.map((c: string) => ({ value: c, label: c }));
  }, [citiesResp]);
  const [goToPageInput, setGoToPageInput] = useState<string>('');
  const [cityOpen, setCityOpen] = useState(false);
  const [eventTypeOpen, setEventTypeOpen] = useState(false);

  // Build server query params (pagination, sort, search, filters, date range)
  // - `sort` is JSON stringified object like {"title":"asc"}
  // - `filter` contains small key-value map (e.g., { is_active, event_type, city })
  // - `dateRange` is JSON stringified object: { start_date, end_date }
  const apiQueryParams = useMemo(() => ({
    page,
    limit: pageSize,
    sort: sort ? JSON.stringify(sort) : undefined,
    search: debouncedSearch && debouncedSearch.trim() ? debouncedSearch.trim() : undefined,
    filter: (() => {
      const filterObj: Record<any, any> = {};
      // Only send is_active when not 'all'
      if (statusFilter && statusFilter !== 'all') {
        filterObj.is_active = statusFilter === 'active';
      }
      // Only send event_type when not 'all'
      if (categoryFilter && categoryFilter !== 'all') {
        filterObj.event_type = String(categoryFilter);
      }
      if (countryFilter && countryFilter !== 'all' && countryFilter.trim()) filterObj.city = countryFilter.trim();
      return Object.keys(filterObj).length ? JSON.stringify(filterObj) : undefined;
    })(),
    dateRange: (() => {
      if (!dateRange || !dateRange[0] || !dateRange[1]) return undefined;
      return JSON.stringify({
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD'),
      });
    })(),
  }), [page, pageSize, sort, debouncedSearch, statusFilter, categoryFilter, countryFilter, dateRange]);
  const { data: apiResponse, isLoading: opportunitiesLoading, isFetching: opportunitiesFetching, error: opportunitiesError } = useGetOpportunitiesQuery(apiQueryParams as any, {refetchOnMountOrArgChange: true, refetchOnReconnect: true});
  const isOpportunitiesLoading = opportunitiesLoading || opportunitiesFetching;
  
  // Modal/dialog state
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingOpportunity, setEditingOpportunity] = useState<Opportunity | null>(null);
  const [isImportModalVisible, setIsImportModalVisible] = useState(false);
  const [showBulkDeleteDialog, setShowBulkDeleteDialog] = useState(false);
  const [showExportDialog, setShowExportDialog] = useState(false);
  
  // Table selection and column visibility state
  const [selectedRowKeys, setSelectedRowKeys] = useState<(string | number)[]>([]);
  const [columnVisibility, setColumnVisibility] = useState<ColumnVisibility>(() => {
    const saved = localStorage.getItem('opportunityColumnVisibility');
    return saved ? JSON.parse(saved) : DEFAULT_COLUMNS;
  });

  // Load persisted filters on mount
  useEffect(() => {
    const savedFilters = localStorage.getItem('opportunityFilters');
    if (savedFilters) {
      const filters = JSON.parse(savedFilters);
      setSearchText(filters.searchText || '');
      setStatusFilter(filters.statusFilter || '');
      setCategoryFilter(filters.categoryFilter || '');
      setCountryFilter(filters.countryFilter || '');
    }
  }, []);

  // Normalize server data and enrich with local matches counts for badges
  const apiOpportunities = (apiResponse as any)?.data?.opportunities ?? [];
  const apiSummary = (apiResponse as any)?.data?.summary ?? { totalOpportunities: 0, active: 0, totalMatches: 0, acceptedMatches: 0 };
  const apiPagination = (apiResponse as any)?.pagination ?? { total: 0, limit: pageSize, page, totalPages: 1 };

  const opportunitiesWithCounts = useMemo(() => {
    return (apiOpportunities as any[]).map((opp: any) => {
      const normalized = {
        ...opp,
        category:  opp.event_type,
      };
      const oppMatches = matches.matches.filter(m => m.opportunityId === opp.id);
      return {
        ...normalized,
        matchedCount: oppMatches.length,
        interestedCount: oppMatches.filter(m => m.status === 'interested').length,
        acceptedCount: oppMatches.filter(m => m.status === 'accepted').length,
        rejectedCount: oppMatches.filter(m => m.status === 'rejected').length,
      };
    });
  }, [apiOpportunities, matches.matches]);

  // Server filters (search/status/category/city/dateRange); avoid double filtering client-side
  const filteredOpportunities = useMemo(() => {
    return opportunitiesWithCounts;
  }, [opportunitiesWithCounts]);

  // Persist filters to localStorage on change
  useEffect(() => {
    const filters = { searchText, statusFilter, categoryFilter, cityFilter: countryFilter };
    localStorage.setItem('opportunityFilters', JSON.stringify(filters));
  }, [searchText, statusFilter, categoryFilter, countryFilter]);

  // Debounce search changes -> updates `debouncedSearch` consumed by API params
  useEffect(() => {
    const id = setTimeout(() => {
      setDebouncedSearch(searchText);
    }, 400);
    return () => clearTimeout(id);
  }, [searchText]);

  // Reset pagination on any filter change
  useEffect(() => {
    setPage(1);
  }, [debouncedSearch, statusFilter, categoryFilter, countryFilter, dateRange]);

  // Advance to a specific page (server-side pagination)
  const changePage = (newPage: number) => {
    setPage(newPage);
  };

  // Change per-page size (resets to page 1)
  const changePageSize = (newSize: number) => {
    setPageSize(newSize);
    setPage(1);
  };

  // Save column visibility
  useEffect(() => {
    localStorage.setItem('opportunityColumnVisibility', JSON.stringify(columnVisibility));
  }, [columnVisibility]);

  // Open the add new opportunity modal
  const handleAdd = () => {
    if (!permissions.canCreate) {
      toast({ description: 'You do not have permission to create opportunities', variant: "destructive" });
      return;
    }
    setEditingOpportunity(null);
    form.reset();
    setIsModalVisible(true);
  };

  // Open the edit modal with selected row prefilled
  const handleEdit = (opportunity: Opportunity) => {
    if (!permissions.canEdit) {
      toast({ description: 'You do not have permission to edit opportunities', variant: "destructive" });
      return;
    }
    setEditingOpportunity(opportunity);
    form.reset({
      title: opportunity.title,
      organization: opportunity.organization,
      description: opportunity.description,
      category: opportunity.category,
      status: opportunity.status,
      budget: opportunity.budget?.toString() || '',
      location: opportunity.location,
      externalUrl: opportunity.externalUrl,
      eventDate: dayjs(opportunity.eventDate).toDate(),
      deadline: dayjs(opportunity.deadline).toDate(),
      requirements: (opportunity.requirements || []).join('\n'),
      tags: (opportunity.tags || []).join(', '),
    });
    setIsModalVisible(true);
  };

  // Duplicate an opportunity into local state (quick create)
  const handleDuplicate = (opportunity: Opportunity) => {
    if (!permissions.canCreate) {
      toast({ description: 'You do not have permission to create opportunities', variant: "destructive" });
      return;
    }
    dispatch({ type: 'DUPLICATE_OPPORTUNITY', payload: opportunity });
    dispatch({ 
      type: 'ADD_ACTIVITY_LOG', 
      payload: createActivityLog(
        'opportunity_duplicated',
        `Duplicated opportunity: ${opportunity.title}`,
        (user?.role || 'super_admin') as UserRole
      )
    });
    toast({ description: 'Opportunity duplicated successfully' });
  };

  const handleDelete = async (id: string | number, title: string) => {
    if (!permissions.canDelete) {
      toast({ description: 'You do not have permission to delete opportunities', variant: "destructive" });
      return;
    }

    try {
      await deleteOpportunity(id).unwrap();
      dispatch({ 
        type: 'ADD_ACTIVITY_LOG', 
        payload: createActivityLog(
          'opportunity_deleted',
          `Deleted opportunity: ${title}`,
          (user?.role || 'super_admin') as UserRole
        )
      });
      toast({ description: 'Opportunity deleted successfully' });
    } catch (e) {
      toast({ description: 'Failed to delete opportunity', variant: "destructive" });
    }
  };

  // Trigger bulk delete confirmation dialog
  const handleBulkDelete = () => {
    if (!permissions.canBulkActions) {
      toast({ description: 'You do not have permission to perform bulk actions', variant: "destructive" });
      return;
    }
    
    // We'll implement this with AlertDialog later
    setShowBulkDeleteDialog(true);
  };

  // Apply a status to all selected rows in local state
  const handleBulkStatusUpdate = (status: OpportunityStatus) => {
    if (!permissions.canBulkActions) {
      toast({ description: 'You do not have permission to perform bulk actions', variant: "destructive" });
      return;
    }
    
    dispatch({ type: 'BULK_UPDATE_OPPORTUNITY_STATUS', payload: { ids: selectedRowKeys, status } });
    dispatch({ 
      type: 'ADD_ACTIVITY_LOG', 
      payload: createActivityLog(
        'opportunities_bulk_status_updated',
        `Updated ${selectedRowKeys.length} opportunities to ${status}`,
        (user?.role || 'super_admin') as UserRole
      )
    });
    setSelectedRowKeys([]);
    toast({ description: `${selectedRowKeys.length} opportunities updated to ${status}` });
  };

  // Export selected or all rows via server CSV endpoint
  const handleExport = async () => {
    if (!permissions.canImportExport) {
      toast({ description: 'You do not have permission to export data', variant: "destructive" });
      return;
    }
    try {
      const payload = selectedRowKeys.length ? { ids: selectedRowKeys as (string | number)[] } : undefined;
      let blob;
      if(payload) {
        blob =await exportOpportunities({payload, params: undefined}).unwrap();
      }else{

      blob = await exportOpportunities({undefined, params: apiQueryParams}).unwrap();
      }
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = 'opportunities.csv';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
      toast({ description: 'Export started' });
    
    dispatch({ 
      type: 'ADD_ACTIVITY_LOG', 
      payload: createActivityLog(
        'opportunities_exported',
          selectedRowKeys.length ? `Exported ${selectedRowKeys.length} selected opportunities to CSV` : 'Exported opportunities to CSV',
        (user?.role || 'super_admin') as UserRole
      )
    });
    } catch (e) {
      toast({ description: 'Failed to export opportunities', variant: "destructive" });
    }
  };

  // Import via CSV: accepts File payload and posts multipart/form-data
  const handleImport = async (payload: any, options: any) => {
    if (!permissions.canImportExport) {
      toast({ description: 'You do not have permission to import data', variant: "destructive" });
      return;
    }
    try {
      // If CsvImportModal passes a File or { file: File }
      const file: File | undefined = payload instanceof File ? payload : payload?.file;
      if (file) {
        const fd = new FormData();
        fd.append('csvFile', file);
        await importOpportunities(fd).unwrap();
        toast({ description: 'CSV imported successfully' });
        setIsImportModalVisible(false);
        dispatch({ 
          type: 'ADD_ACTIVITY_LOG', 
          payload: createActivityLog(
            'opportunities_imported',
            `Imported opportunities from CSV (${file.name})`,
            (user?.role || 'super_admin') as UserRole
          )
      });
      return;
    }
    
      // Legacy path: array of opportunities
      const importedOpportunities: Opportunity[] = Array.isArray(payload) ? payload : [];
      if (importedOpportunities.length) {
        await Promise.all(importedOpportunities.map(opp => createOpportunity(opp).unwrap()));
    dispatch({ 
      type: 'ADD_ACTIVITY_LOG', 
      payload: createActivityLog(
        'opportunities_imported',
        `Imported ${importedOpportunities.length} opportunities from CSV`,
        (user?.role || 'super_admin') as UserRole
      )
    });
        toast({ description: `Successfully imported ${importedOpportunities.length} opportunities` });
      }
    } catch (e) {
      toast({ description: 'Failed to import CSV', variant: "destructive" });
    }
  };

  // Save handler for add/edit modal (creates or updates based on `editingOpportunity`)
  const handleSubmit = async (values: z.infer<typeof opportunitySchema>) => {
    try {
      const opportunityData = {
        ...values,
        budget: parseFloat(values.budget),
        eventDate: values.eventDate?.toISOString(),
        // Send Application Deadline as end_date for backend
        end_date: values.deadline?.toISOString(),
        requirements: values.requirements ? values.requirements.split('\n').filter((req: string) => req.trim()) : [],
        tags: values.tags ? values.tags.split(',').map((tag: string) => tag.trim()).filter(Boolean) : [],
        notes: editingOpportunity?.notes || [],
        matchedCount: editingOpportunity?.matchedCount || 0,
        interestedCount: editingOpportunity?.interestedCount || 0,
        acceptedCount: editingOpportunity?.acceptedCount || 0,
        rejectedCount: editingOpportunity?.rejectedCount || 0,
        createdAt: editingOpportunity?.createdAt || dayjs().toISOString(),
        updatedAt: dayjs().toISOString(),
      };

      if (editingOpportunity) {
        await updateOpportunity({ id: editingOpportunity.id, ...opportunityData }).unwrap();
        dispatch({ 
          type: 'ADD_ACTIVITY_LOG', 
          payload: createActivityLog(
            'opportunity_updated',
            `Updated opportunity: ${opportunityData.title}`,
            (user?.role || 'super_admin') as UserRole
          )
        });
        toast({ description: 'Opportunity updated successfully' });
      } else {
        await createOpportunity(opportunityData).unwrap();
        dispatch({ 
          type: 'ADD_ACTIVITY_LOG', 
          payload: createActivityLog(
            'opportunity_created',
            `Created opportunity: ${opportunityData.title}`,
            (user?.role || 'super_admin') as UserRole
          )
        });
        toast({ description: 'Opportunity created successfully' });
      }
      
      setIsModalVisible(false);
      form.reset();
    } catch (error) {
      toast({ description: 'Failed to save opportunity', variant: "destructive" });
    }
  };

  const getStatusColor = (status: OpportunityStatus) => {
    switch (status) {
      case 'active': return 'green';    // Green for active
      case 'draft': return 'orange';    // Orange for pending/draft
      case 'paused': return 'orange';   // Orange for paused (pending state)
      case 'completed': return 'green'; // Green for completed (successful state)
      case 'cancelled': return 'red';   // Red for cancelled/rejected
      default: return 'default';
    }
  };

  // Table columns configuration for shadCN Table
  const tableColumns = [
    { key: 'title', label: 'Title', sortable: true, apiKey: 'title' },
    { key: 'organization', label: 'Organization', sortable: true, apiKey: 'organization' },
    { key: 'category', label: 'Type', sortable: true, apiKey: 'event_type' },
    { key: 'dateRange', label: 'Start - End Date', sortable: true, apiKey: 'start_date' },
    { key: 'location', label: 'Location', sortable: true, apiKey: 'city' },
    { key: 'status', label: 'Status', sortable: true, apiKey: 'is_active' },
    { key: 'actions', label: 'Actions', sortable: false },
  ];

  const bulkActionMenuItems = [
    {
      key: 'export',
      label: 'Export CSV',
      onClick: () => setShowExportDialog(true),
      disabled: !permissions.canImportExport,
    },
    // {
    //   key: 'import',
    //   label: 'Import Opportunities',
    //   onClick: () => setIsImportModalVisible(true),
    //   disabled: !permissions.canImportExport,
    // },
    {
      key: 'delete',
      label: 'Delete Selected',
      onClick: handleBulkDelete,
      disabled: !permissions.canBulkActions,
    },
  ];

  const columnMenuItems = Object.keys(DEFAULT_COLUMNS).map(col => ({
    key: col,
    label: col.charAt(0).toUpperCase() + col.slice(1),
    checked: columnVisibility[col],
    onCheckedChange: (checked: boolean) => setColumnVisibility(prev => ({ ...prev, [col]: checked })),
  }));

  return (
    <>
    <InvalidTokenHandler error={opportunitiesError} />
    <TooltipProvider>
    <div className="min-h-screen space-y-6">
      {/* Shared Export Dialog */}
      <AlertDialog open={showExportDialog} onOpenChange={setShowExportDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Export opportunities</AlertDialogTitle>
            <AlertDialogDescription>
              Do you want to export the selected or current list to CSV?
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={async () => { setShowExportDialog(false); await handleExport(); }}>Export</AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
            <h2 className="text-2xl font-bold text-foreground m-0">
            Opportunity Management
          </h2>
        </div>
        <div className="flex gap-2">
          {opportunitiesLoading ? (
            <>
              <Skeleton className="h-9 w-28" />
              <Skeleton className="h-9 w-28" />
            </>
          ) : (
            <>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    onClick={() => setIsImportModalVisible(true)}
                    disabled={!permissions.canImportExport}
                    variant="outline"
                    className="bg-card"
                  style={{ borderColor: 'hsl(var(--border))' }}
                  >
                    <ImportOutlined className="mr-2 h-4 w-4" />
                    Import CSV
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {permissions.canImportExport ? "Import from CSV" : "No import permission"}
                </TooltipContent>
              </Tooltip>
            
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    onClick={() => setShowExportDialog(true)}
                    disabled={!permissions.canImportExport}
                    variant="outline"
                    className="bg-card"
                    style={{ borderColor: 'hsl(var(--border))' }}
                  >
                    <ExportOutlined className="mr-2 h-4 w-4" />
                    Export CSV
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {permissions.canImportExport ? "Export to CSV" : "No export permission"}
                </TooltipContent>
              </Tooltip>
            </>
          )}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        {opportunitiesLoading ? (
          <>
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="bg-card border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 bg-tertiary">
                <CardContent className="p-6">
                  <Skeleton className="h-4 w-32 mb-2" />
                  <Skeleton className="h-8 w-16" />
                </CardContent>
              </Card>
            ))}
          </>
        ) : (
          <>
            <Card className="bg-card border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 bg-tertiary">
              <CardContent className="p-6">
                  <div className="text-sm font-medium text-muted-foreground mb-2">Total Opportunities</div>
                  <div className="text-2xl font-bold" style={{ color: 'hsl(var(--dashboard-dark-blue))' }}>
                  {apiSummary.totalOpportunities}
                </div>
              </CardContent>
            </Card>
              
            <Card className="bg-card border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 bg-tertiary">
              <CardContent className="p-6">
                  <div className="text-sm font-medium text-muted-foreground mb-2">Active</div>
                  <div className="text-2xl font-bold" style={{ color: 'hsl(var(--dashboard-medium-blue))' }}>
                  {apiSummary.active}
                </div>
              </CardContent>
            </Card>
              
            <Card className="bg-card border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 bg-tertiary">
              <CardContent className="p-6">
                  <div className="text-sm font-medium text-muted-foreground mb-2">Total Matches</div>
                  <div className="text-2xl font-bold" style={{ color: 'hsl(var(--dashboard-light-blue))' }}>
                  {apiSummary.totalMatches}
                </div>
              </CardContent>
            </Card>
              
            <Card className="border-border rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300 bg-tertiary">
              <CardContent className="p-6">
                  <div className="text-sm font-medium text-muted-foreground mb-2">Accepted Matches</div>
                  <div className="text-2xl font-bold" style={{ color: 'hsl(var(--dashboard-dark-blue))' }}>
                  {apiSummary.acceptedMatches}
                </div>
              </CardContent>
            </Card>
          </>
        )}
      </div>

      {/* Filters */}
        <Card className="border-border rounded-xl shadow-sm bg-tertiary">
          {isOpportunitiesLoading ? <Skeleton className="h-[200px] w-full rounded-lg" />:
          <>
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-foreground">Filters</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {opportunitiesLoading ? (
              <>
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <Skeleton className="h-10 w-full" />
                  </div>
                  <Skeleton className="h-10 w-28" />
                </div>
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                  <Skeleton className="h-10 w-full" />
                </div>
              </>
            ) : (
              <>
                {/* Search and Reset - First Row */}
                <div className="flex flex-col md:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                        <Input
                        placeholder="Search by title, organization, source link, venue..."
                        value={searchText}
                        onChange={(e) => setSearchText(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>
                  
                    <Button 
                    variant="outline"
                    className="w-full md:w-auto"
                    onClick={() => {
                      setSearchText('');
                        setStatusFilter('');
                        setCategoryFilter('');
                      setCountryFilter('');
                      setDateRange(null);
                    }}
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Reset
                    </Button>
                </div>

                {/* Main Filters - Second Row */}
                <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as any)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Filter by Status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="completed">Completed</SelectItem>
                    </SelectContent>
                    </Select>
                  
                    <Popover open={eventTypeOpen} onOpenChange={setEventTypeOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={eventTypeOpen}
                          className="w-full justify-between"
                        >
                          <span className="truncate">
                            {categoryFilter && categoryFilter !== 'all' ? (
                              eventTypeOptions.find((et) => et.value === categoryFilter)?.label?.replace(/^./, (c) => c.toUpperCase()) || 'Filter by Event Type'
                            ) : (
                              'Filter by Event Type'
                            )}
                          </span>
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="p-0 w-[--radix-popover-trigger-width]">
                        <Command>
                          <CommandInput placeholder="Search type..." />
                          <CommandList>
                            <CommandEmpty>No type found.</CommandEmpty>
                            <CommandGroup>
                              <CommandItem
                                value="all"
                                onSelect={() => {
                                  setCategoryFilter('all');
                                  setEventTypeOpen(false);
                                }}
                              >
                                <Check className={cn('mr-2 h-4 w-4', categoryFilter === 'all' ? 'opacity-100' : 'opacity-0')} />
                                All
                              </CommandItem>
                              {eventTypeOptions.map((et) => (
                                <CommandItem
                                  key={et.value}
                                  value={et.value}
                                  onSelect={(val) => {
                                    setCategoryFilter(val);
                                    setEventTypeOpen(false);
                                  }}
                                >
                                  <Check className={cn('mr-2 h-4 w-4', categoryFilter === et.value ? 'opacity-100' : 'opacity-0')} />
                                  {et.label.charAt(0).toUpperCase() + et.label.slice(1)}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>

                    <Popover open={cityOpen} onOpenChange={setCityOpen}>
                      <PopoverTrigger asChild>
                        <Button
                          variant="outline"
                          role="combobox"
                          aria-expanded={cityOpen}
                          className="w-full justify-between"
                        >
                          <span className="truncate">
                            {countryFilter && countryFilter !== 'all' ? (
                              cityOptions.find((c) => c.value === countryFilter)?.label?.slice(0, 25) || 'Filter by City'
                            ) : (
                              'Filter by City'
                            )}
                          </span>
                          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="p-0 w-[--radix-popover-trigger-width]">
                        <Command>
                          <CommandInput placeholder="Search city..." />
                          <CommandList>
                            <CommandEmpty>No city found.</CommandEmpty>
                            <CommandGroup>
                              <CommandItem
                                value="all"
                                onSelect={() => {
                                  setCountryFilter('all');
                                  setCityOpen(false);
                                }}
                              >
                                <Check className={cn('mr-2 h-4 w-4', countryFilter === 'all' ? 'opacity-100' : 'opacity-0')} />
                                All
                              </CommandItem>
                              {cityOptions.map((c) => (
                                <CommandItem
                                  key={c.value}
                                  value={c.value}
                                  onSelect={(val) => {
                                    setCountryFilter(val);
                                    setCityOpen(false);
                                  }}
                                >
                                  <Check className={cn('mr-2 h-4 w-4', countryFilter === c.value ? 'opacity-100' : 'opacity-0')} />
                                  {c.label}
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                  <DatePicker
                              value={dateRange }
                              onChange={setDateRange}
                            range
                            numberOfMonths={2}
                              arrow={false}
                              className="custom-calendar"
                              placeholder="Select Date Range"
                              inputClass="h-10 rounded-md border border-input bg-background w-full px-2 text-sm placeholder:text-foreground"
                            />
                </div>
              </>
            )}
          </CardContent>
          </>
          }
        </Card>

      {/* Bulk Actions */}
      {selectedRowKeys.length > 0 && (
          <Card className="mb-4 bg-tertiary">
            <div className="flex justify-between items-center p-4">
              <div>
                <span className="font-semibold">{selectedRowKeys.length} opportunities selected</span>
              </div>
              <div className="flex gap-2">
                <Button onClick={() => setSelectedRowKeys([])} variant="outline">
                  Clear Selection
                </Button>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button disabled={!permissions.canBulkActions}>
                      <MoreOutlined className="mr-2 h-4 w-4" />
                    Bulk Actions
                  </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    {bulkActionMenuItems.map((item) => (
                      <DropdownMenuItem
                        key={item.key}
                        onClick={(e) => {
                          e.preventDefault();
                          item.onClick();
                        }}
                        disabled={item.disabled}
                      >
                        {item.label}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
        </Card>
      )}
      {isOpportunitiesLoading? (
        <div className="flex items-center justify-between mb-4">
          <Skeleton className="h-4 w-4 rounded" />
          <Skeleton className="h-4 w-4 rounded" />
        </div>
      ) : (
        <div className="flex items-center justify-between mb-4">
            <span className="text-muted-foreground text-sm">
                Showing {filteredOpportunities.length} of {
                  apiPagination.total || filteredOpportunities.length
                } opportunities
            </span>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground text-sm">
                Rows per page
              </span>
              <Select
               value={String(pageSize)} onValueChange={(v) => changePageSize(Number(v))}
              >
                <SelectTrigger className="w-[80px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        )}
        {/* Table */}
        {isOpportunitiesLoading ? (
          <Card className="bg-tertiary rounded-xl">
            <div className="overflow-x-auto w-full">
              <Table className="min-w-[1200px]">
                <TableHeader>
                  <TableRow>
                    {permissions.canBulkActions && (
                      <TableHead className="w-12">
                        <Skeleton className="h-4 w-4 rounded" />
                      </TableHead>
                    )}
                    <TableHead>
                      <Skeleton className="h-4 w-32" />
                    </TableHead>
                    <TableHead>
                      <Skeleton className="h-4 w-28" />
                    </TableHead>
                    <TableHead>
                      <Skeleton className="h-4 w-16" />
                    </TableHead>
                    <TableHead>
                      <Skeleton className="h-4 w-28" />
                    </TableHead>
                    <TableHead>
                      <Skeleton className="h-4 w-20" />
                    </TableHead>
                    <TableHead className="w-24 text-right">
                      <Skeleton className="h-4 w-16 ml-auto" />
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {[...Array(8)].map((_, i) => (
                    <TableRow key={i}>
                      {permissions.canBulkActions && (
                        <TableCell className="w-12">
                          <Skeleton className="h-4 w-4 rounded" />
                        </TableCell>
                      )}
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Skeleton className="h-4 w-6 rounded" />
                          <Skeleton className="h-4 w-48" />
                        </div>
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-36" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-6 w-10 rounded" />
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          <Skeleton className="h-4 w-28" />
                          <Skeleton className="h-3 w-20" />
                        </div>
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-6 w-16 rounded-full" />
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex justify-end gap-2">
                          <Skeleton className="h-8 w-8 rounded" />
                          <Skeleton className="h-8 w-8 rounded" />
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </Card>
        ) : (
      <div className="bg-tertiary rounded-xl">
      {filteredOpportunities.length === 0 ? (
        <Card className="bg-tertiary">
            <div className="flex flex-col items-center justify-center py-12">
              <div className="text-center">
                <h4 className="text-lg font-semibold mb-2">No Opportunities Found</h4>
                <p className="text-muted-foreground">
                {searchText || statusFilter || categoryFilter || countryFilter || dateRange 
                  ? 'No opportunities match your current filters. Try adjusting your search criteria.'
                  : 'Get started by adding your first opportunity.'
                }
                </p>
              </div>
            </div>
        </Card>
      ) : (
          <Card className="bg-tertiary rounded-xl">
            <div className="overflow-x-auto w-full">
              <Table className="min-w-[1200px]">
                <TableHeader>
                  <TableRow>
                    {permissions.canBulkActions && (
                      <TableHead className="w-12">
                        <Checkbox
                          checked={selectedRowKeys.length === filteredOpportunities.length}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedRowKeys(filteredOpportunities.map(opp => opp.id));
                            } else {
                              setSelectedRowKeys([]);
                            }
                          }}
                        />
                      </TableHead>
                    )}
                    {tableColumns.map((column) => (
                      <TableHead key={column.key} className="text-center select-none">
                        <button
                          className={`inline-flex w-full justify-center items-center gap-1 ${column.sortable ? 'hover:text-foreground' : 'text-foreground'} disabled:opacity-50`}
                          disabled={!column.sortable}
                          onClick={() => {
                            if (!column.sortable) return;
                            const key = (column.apiKey || column.key) as string;
                            const currentOrder = sort && sort[key];
                            const nextOrder: 'asc' | 'desc' = currentOrder === 'asc' ? 'desc' : 'asc';
                            setSort({ [key]: nextOrder });
                            setPage(1);
                          }}
                        >
                          <span>{column.label}</span>
                          {column.sortable && (
                            <span className="inline-flex flex-col leading-none">
                              <CaretUpOutlined style={{ fontSize: 10, opacity: sort && sort[(column.apiKey || column.key) as string] === 'asc' ? 1 : 0.35 }} />
                              <CaretDownOutlined style={{ fontSize: 10, marginTop: -2, opacity: sort && sort[(column.apiKey || column.key) as string] === 'desc' ? 1 : 0.35 }} />
                            </span>
                          )}
                        </button>
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredOpportunities.map((record) => (
                    <TableRow 
                      key={record.id}
                      className="cursor-pointer hover:bg-muted/50"
                      onDoubleClick={() => navigate(`/opportunities/${record.id}`)}
                    >
                      {permissions.canBulkActions && (
                        <TableCell>
                          <Checkbox
                            checked={selectedRowKeys.includes(record.id)}
                            onCheckedChange={(checked) => {
                              if (checked) {
                                setSelectedRowKeys([...selectedRowKeys, record.id]);
                              } else {
                                setSelectedRowKeys(selectedRowKeys.filter(id => id !== record.id));
                              }
                            }}
                          />
                        </TableCell>
                      )}
                      <TableCell className="max-w-[180px] text-center">
                        <div className="flex items-center justify-center gap-2">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button 
                                variant="link" 
                                onClick={() => navigate(`/opportunities/${record.id}`)}
                                className="p-0 h-auto font-medium text-sm text-left justify-start"
                              >
                                {record.title?record.title?.slice(0, 20):'---'}
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              {record.title?record.title:'---'}
                            </TooltipContent>
                          </Tooltip>
                          {record.event_url && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => window.open(record.event_url, '_blank')}
                              className="p-1 h-6 w-6"
                            >
                              <LinkOutlined style={{ fontSize: 12 }} />
                            </Button>
                          )}
                        </div>
                      </TableCell>
                      <TableCell className="max-w-[140px] text-center">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <span className="text-sm truncate block text-center">{record.organization?record.organization: '---'}</span>
                          </TooltipTrigger>
                          <TooltipContent>
                            {record.organization?record.organization: '---'}
                          </TooltipContent>
                        </Tooltip>
                      </TableCell>
                      <TableCell className="text-center">
                        <span className="inline-flex items-center rounded-md px-2 py-0.5 text-xs font-medium bg-blue-500/15 text-blue-300 border border-blue-500/30">
                          {record.event_type?record.event_type?.charAt(0)?.toUpperCase() + record.event_type?.slice(1): '---'}
                        </span>
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="text-xs">
                          <div className={`font-medium ${dayjs(record.deadline).diff(dayjs(), 'days') <= 7 ? 'text-red-500' : ''}`}>
                            {dayjs(record.deadline).format('MMM DD')} - {dayjs(record.eventDate).format('MMM DD')}
                          </div>
                          <div className="text-muted-foreground text-xs">
                            {dayjs(record.deadline).format('YYYY')} - {dayjs(record.eventDate).format('YYYY')}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="max-w-[130px] text-center">
                        <div>
                          {(() => {
                            const isVirtual = record.location?.toLowerCase().includes('virtual') || record.location?.toLowerCase().includes('online');
                            if (isVirtual) {
                              return (
                                <span className="inline-flex items-center rounded-md px-2 py-0.5 text-xs font-medium bg-emerald-500/15 text-emerald-300 border border-emerald-500/30">
                                  Virtual
                                </span>
                              );
                            }
                            // const parts = (record.location || '').split(',');
                            const city = record.city || '---';
                            const country = record.country ;
                            return (
                              <>
                                <span className="inline-flex items-center rounded-md px-2 py-0.5 text-xs font-medium  border bg-blue-500/15 text-blue-300 border border-blue-500/30">
                                  In-Person
                                </span>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <div className="text-xs text-muted-foreground mt-1 truncate">
                                      {city}{country && country !== city ? `, ${country}` : ''}
                                    </div>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    {record.location?record.location:'---'}
                                  </TooltipContent>
                                </Tooltip>
                              </>
                            );
                          })()}
                        </div>
                      </TableCell>
                      <TableCell className="text-center">
                        <StatusTag status={record.is_active? 'active':'completed'} label={record.is_active?'Active':'Completed'} />
                      </TableCell>
                      <TableCell className="text-center">
                        <div className="flex items-center justify-center gap-1">
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <Button 
                                variant="ghost" 
                                size="sm"
                                onClick={() => navigate(`/opportunities/${record.id}`)}
                                className="p-1 h-6 w-6"
                              >
                                <Eye className="h-3 w-3" />
                              </Button>
                            </TooltipTrigger>
                            <TooltipContent>
                              View Details
                            </TooltipContent>
                          </Tooltip>
                          
                          <Tooltip>
                            <TooltipTrigger asChild>
                              <AlertDialog>
                                <AlertDialogTrigger asChild>
                                  <Button 
                                    variant="ghost" 
                                    size="sm"
                                    disabled={!permissions.canDelete}
                                    className="p-1 h-6 w-6 text-destructive hover:text-destructive"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                  </Button>
                                </AlertDialogTrigger>
                                <AlertDialogContent>
                                  <AlertDialogHeader>
                                    <AlertDialogTitle>Delete opportunity</AlertDialogTitle>
                                    <AlertDialogDescription>
                                      Are you sure you want to delete this opportunity? This action cannot be undone.
                                    </AlertDialogDescription>
                                  </AlertDialogHeader>
                                  <AlertDialogFooter>
                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                    <AlertDialogAction
                                      onClick={() => handleDelete(record.id, record.title)}
                                      className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                    >
                                      Delete
                                    </AlertDialogAction>
                                  </AlertDialogFooter>
                                </AlertDialogContent>
                              </AlertDialog>
                            </TooltipTrigger>
                            <TooltipContent>
                              {permissions.canDelete ? "Delete Opportunity" : "No delete permission"}
                            </TooltipContent>
                          </Tooltip>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </Card>
        )}

        {/* Pagination (shadcn) */}
        <div className="flex items-center justify-center p-4 gap-5 bg-tertiary rounded-xl">
          {/* <div className="flex items-center gap-3 bg-tertiary"> */}
          {/* <span className="text-sm text-muted-foreground">
                  {(apiPagination.page - 1) * apiPagination.limit + 1}-
                  {Math.min(apiPagination.page * apiPagination.limit, apiPagination.total)} of {apiPagination.total} opportunities
                </span> */}
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious onClick={() => { if (apiPagination.page === 1 || (apiPagination.totalPages || 1) <= 1 || (apiPagination.total || 0) === 0) return; changePage(apiPagination.page - 1); }} aria-disabled={apiPagination.page === 1 || (apiPagination.totalPages || 1) <= 1 || (apiPagination.total || 0) === 0} />
                </PaginationItem>
                {(() => {
                  const totalPages = apiPagination.totalPages || 1;
                  const current = apiPagination.page;
                  const pages: number[] = [];
                  const start = Math.max(1, current - 2);
                  const end = Math.min(totalPages, start + 4);
                  for (let p = start; p <= end; p++) pages.push(p);
                  return pages.map((p) => (
                    <PaginationItem key={p}>
                      <PaginationLink isActive={p === current} onClick={() => { if (p === current) return; changePage(p); }}>
                        {p}
                      </PaginationLink>
                    </PaginationItem>
                  ));
                })()}
                {apiPagination.totalPages > 5 && (
                  <PaginationItem>
                    <PaginationEllipsis />
                  </PaginationItem>
                )}
                <PaginationItem>
                  <PaginationNext onClick={() => { if (apiPagination.page >= apiPagination.totalPages || (apiPagination.totalPages || 1) <= 1 || (apiPagination.total || 0) === 0) return; changePage(apiPagination.page + 1); }} aria-disabled={apiPagination.page >= apiPagination.totalPages || (apiPagination.totalPages || 1) <= 1 || (apiPagination.total || 0) === 0} />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          {/* </div> */}
          </div>
        </div>
        )}
        {/* Add/Edit Modal */}
        <Dialog open={isModalVisible} onOpenChange={(open) => {
          if (!open) {
            setIsModalVisible(false);
            form.reset();
          }
        }}>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>{editingOpportunity ? 'Edit Opportunity' : 'Add Opportunity'}</DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="organization"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Organization</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea {...field} rows={4} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <FormField
                  control={form.control}
                name="category"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select category" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="conference">Conference</SelectItem>
                          <SelectItem value="podcast">Podcast</SelectItem>
                          <SelectItem value="webinar">Webinar</SelectItem>
                          <SelectItem value="workshop">Workshop</SelectItem>
                          <SelectItem value="panel">Panel</SelectItem>
                          <SelectItem value="keynote">Keynote</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                name="status"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Status</FormLabel>
                      <Select onValueChange={field.onChange} defaultValue={field.value}>
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="draft">Draft</SelectItem>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="paused">Paused</SelectItem>
                          <SelectItem value="completed">Completed</SelectItem>
                          <SelectItem value="cancelled">Cancelled</SelectItem>
                        </SelectContent>
                </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                name="budget"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Budget</FormLabel>
                      <FormControl>
                        <Input {...field} type="number" placeholder="$" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                name="externalUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>External URL</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="https://..." />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                name="eventDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Event Date</FormLabel>
                      <FormControl>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-start text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              <Calendar className="mr-2 h-4 w-4" />
                              {field.value ? format(field.value, "PPP") : "Select event date"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <CalendarComponent
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                name="deadline"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Application Deadline</FormLabel>
                      <FormControl>
                        <Popover>
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              className={cn(
                                "w-full justify-start text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              <Calendar className="mr-2 h-4 w-4" />
                              {field.value ? format(field.value, "PPP") : "Select deadline"}
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-auto p-0" align="start">
                            <CalendarComponent
                              mode="single"
                              selected={field.value}
                              onSelect={field.onChange}
                              initialFocus
                            />
                          </PopoverContent>
                        </Popover>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
            name="tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tags (comma-separated)</FormLabel>
                    <FormControl>
                      <Input {...field} placeholder="AI, Technology, Conference..." />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
            name="requirements"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Requirements (one per line)</FormLabel>
                    <FormControl>
                      <Textarea 
                        {...field}
              rows={3} 
              placeholder="Enter requirements, one per line..." 
            />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

                <div className="flex justify-end gap-2 pt-4">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setIsModalVisible(false)}
                  >
                Cancel
              </Button>
                  <Button type="submit">
                {editingOpportunity ? 'Update' : 'Create'}
              </Button>
                </div>
              </form>
        </Form>
          </DialogContent>
        </Dialog>

        {/* Bulk Delete Dialog */}
        <AlertDialog open={showBulkDeleteDialog} onOpenChange={setShowBulkDeleteDialog}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Delete {selectedRowKeys.length} Opportunities</AlertDialogTitle>
              <AlertDialogDescription>
                Are you sure you want to delete all selected opportunities? This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction
                onClick={async () => {
                  try {
                    await Promise.all(
                      (selectedRowKeys as (string | number)[]).map((id) => deleteOpportunity(id).unwrap())
                    );
                    dispatch({ 
                      type: 'ADD_ACTIVITY_LOG', 
                      payload: createActivityLog(
                        'opportunities_bulk_deleted',
                        `Bulk deleted ${selectedRowKeys.length} opportunities`,
                        (user?.role || 'super_admin') as UserRole
                      )
                    });
                    setSelectedRowKeys([]);
                    setShowBulkDeleteDialog(false);
                    toast({ description: `${selectedRowKeys.length} opportunities deleted successfully` });
                  } catch (e) {
                    toast({ description: 'Failed to delete selected opportunities', variant: "destructive" });
                  }
                }}
                className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
              >
                Delete
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>

      {/* Import Modal */}
      <CsvImportModal
        visible={isImportModalVisible}
        onCancel={() => setIsImportModalVisible(false)}
        onImport={handleImport}
        existingOpportunities={apiOpportunities as any}
      />
    </div>
    </TooltipProvider>
    </>
  );
};

export default Opportunities;