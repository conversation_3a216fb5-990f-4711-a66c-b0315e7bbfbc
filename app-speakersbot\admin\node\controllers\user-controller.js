const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");

const userService = require("../services/user-service");
const ApiResponse = require("../helpers/api-response");

/**
 * Create or update user
 */
exports.upsertUser = async (req, res, next) => {
    try {
        const result = await userService.upsertUser(req);
        const { status, message } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error creating user:", error);
        next(error);
    }
};

/**
 * Delete user by id
 */
exports.deleteUser = async (req, res, next) => {
    try {
        const result = await userService.deleteUser(req);
        const { status, message } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error deleting user:", error);
        next(error);
    }
};

/**
 * Get user by id
 */
exports.getUser = async (req, res, next) => {
    try {
        const result = await userService.getUser(req);
        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
};

exports.getUsers = async (req, res, next) => {
    try {


        const result = await userService.getUsers(req);
        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data: { users: data.users }, pagination: data.pageData }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
};

exports.authUser = async (req, res, next) => {
    try {
        const result = await userService.authUser(req);
        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error in authUser:", error);
        next(error);
    }
};