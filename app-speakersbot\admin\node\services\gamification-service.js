const gamificationService = {}
const db = require("../models");
const { parsePagination, getPagingData } = require("../helpers/app-hepler");
/*
 * Get gamification points data with pagination.
 * Groups points by speaker and includes speaker information.
 * 
 * @param {Object} [filters={}] - Filter options
 * @param {number} [filters.page] - Page number for pagination
 * @param {number} [filters.limit] - Items per page
 * @returns {Promise<Object>} Paginated gamification data grouped by speaker
 * @throws {Error} When database operation fails
 */
gamificationService.getPointsData = async (filters = {}) => {
    try {
        const mappedFilters = {
            page: filters.currentPage || filters.page || 1,
            limit: filters.perPage || filters.limit || 10,
            search: filters.search,
            type: filters.type
        };

        const { page, limit, offset } = parsePagination(mappedFilters);
        const whereConditions = {};
        if (mappedFilters.type) {
            whereConditions.type = mappedFilters.type;
        }
        if (mappedFilters.search) {
            const searchTerm = `%${mappedFilters.search}%`;
            whereConditions[db.Sequelize.Op.or] = [
                {
                    '$action.description$': {
                        [db.Sequelize.Op.like]: searchTerm
                    }
                },
                {
                    '$speaker.name$': {
                        [db.Sequelize.Op.like]: searchTerm
                    }
                }
            ];
        }
        const { rows: rawData, count: totalCount } = await db.GamificationHistory.findAndCountAll({
            where: whereConditions,
            include: [
                {
                    model: db.Speakers,
                    attributes: ['name'],
                    as: 'speaker',
                    required: false
                },
                {
                    model: db.GamificationRules,
                    attributes: ['description'],
                    as: 'action',
                    required: false
                }
            ],
            order: [['speaker_id', 'ASC'], ['created_at', 'DESC']],
            limit,
            offset
        });

        const formattedData = rawData.map(item => ({
            id: item.id,
            speaker_id: item.speaker_id,
            speaker: item.speaker?.name || 'Unknown',
            description: item.action?.description || "",
            type: item.type,
            points: item.points,
            created_at: item.created_at,
            updated_at: item.updated_at
        }));

        const pageData = getPagingData(totalCount, limit, page);
        if (!formattedData || formattedData.length === 0) {
            return {
                status: true,
                message: "No gamification data found",
                data: { data: [], pageData },
            };
        }

        return {
            status: true,
            message: "Gamification data fetched successfully",
            data: { gamification_data: formattedData, pageData },
        };

    } catch (error) {
        console.error("Error fetching gamification points data:", error);
        throw error;
    }
};

gamificationService.getAllRules = async () => {
    try {
        const rules = await db.GamificationRules.findAll({
            order: [['type', 'ASC'], ['id', 'ASC']]
        });

        if (!rules || rules.length === 0) {
            return {
                status: false,
                message: "No gamification rules found",
                data: null
            };
        }

        const grouped = rules.reduce((acc, rule) => {
            const t = rule.type || 'unknown';
            acc[t] = acc[t] || [];
            acc[t].push(rule);
            return acc;
        }, {});

        return {
            status: true,
            message: "Gamification rules fetched successfully",
            data: grouped
        };

    } catch (error) {
        console.error("Error fetching gamification rules:", error);
        throw error;
    }
}

gamificationService.updateRules = async (rules) => {
    try {
        if (!Array.isArray(rules) || rules.length === 0) {
            return {
                status: false,
                message: "No rules provided for update",
                data: null
            };
        }

        const updatePromises = rules.map(async (rule) => {
            if (!rule.id) {
                throw new Error("Rule ID is required for update");
            }
            const existingRule = await db.GamificationRules.findByPk(rule.id);
            if (!existingRule) {
                throw new Error(`No rule found with ID: ${rule.id}`);
            }
            return existingRule.update({
                key: rule.key || existingRule.key,
                points: rule.points !== undefined ? rule.points : existingRule.points,
                limit_count: rule.limit_count !== undefined ? rule.limit_count : existingRule.limit_count,
                limit_period: rule.limit_period !== undefined ? rule.limit_period : existingRule.limit_period,
                type: rule.type || existingRule.type,
                is_active: rule.is_active !== undefined ? rule.is_active : existingRule.is_active,
            });
        });

        await Promise.all(updatePromises);

        const ids = Array.from(new Set(rules.map(r => r.id)));
        const updatedRules = await db.GamificationRules.findAll({
            where: { id: ids },
            order: [['type', 'ASC'], ['id', 'ASC']]
        });

        const grouped = updatedRules.reduce((acc, rule) => {
            const t = rule.type || 'unknown';
            acc[t] = acc[t] || [];
            acc[t].push(rule);
            return acc;
        }, {});

        return {
            status: true,
            message: "Gamification rules updated successfully",
            data: grouped
        };

    } catch (error) {
        console.error("Error updating gamification rules:", error);
        throw error;
    }
}

gamificationService.awardEventPoints = async (speaker_id, event) => {
    try {
        switch (event) {
            case 'daily_login':
                return await gamificationService.handleDailyLogin(speaker_id);

            case 'bio_update':
            case 'add_testimonial':
            case 'upload_video':
            case 'upload_photo':
            case 'optional_questions':
            case 'complete_intake_form':
            case 'upload_headshot':
            case 'affiliate_meeting':
                return await gamificationService.handleFormUpdate(speaker_id, event);

            case 'referral_points':
                return await gamificationService.referralPoints(speaker_id);

            default:
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid event type");
        }
    } catch (error) {
        console.error("Error awarding event points:", error);
        throw error;
    }
};

gamificationService.handleDailyLogin = async (speaker_id) => {
    try {
        const { status, message, data } = await gamificationService.updateStreak(speaker_id);
        if (status) {
            const streakMap = {
                5: '5_day_login',
                30: '30_day_login',
                365: 'annual_login',
            };
            const key = data ? (streakMap[data.streak_count] || 'daily_login') : 'daily_login';
            const eventRule = await db.GamificationRules.findOne({
                where: { key }
            });

            if (!eventRule) {
                throw new Error(`No rule found for event type: ${key}. Please add it to gamification_points table.`);
            }

            const rule = {
                points: eventRule.points,
                limit: eventRule.limit_count,
                period: eventRule.limit_period,
                type: eventRule.type,
                gamification_id: eventRule.id
            };

            const result = await gamificationService.awardPointsWithRule(speaker_id, key, rule);
            return result;
        }
        return { status: false, message: "Failed to update streak", data: null };
    } catch (error) {
        console.error("Error handling daily login:", error);
        throw error;
    }
};

gamificationService.updateStreak = async (speaker_id) => {
    try {
        const today = new Date();
        const todayDateOnly = new Date(today.getFullYear(), today.getMonth(), today.getDate());

        // Use existing streak if provided, otherwise fetch it
        let streak = await db.Streaks.findOne({ where: { speaker_id } });

        if (!streak) {
            // Create new streak record
            streak = await db.Streaks.create({
                speaker_id,
                count: 1,
                last_login: today
            });
            return { status: true, message: "New streak created", data: { streak_count: streak.count } };
        } else {
            // Check if user already logged in today
            const lastLoginDate = new Date(streak.last_login);
            const lastLoginDateOnly = new Date(lastLoginDate.getFullYear(), lastLoginDate.getMonth(), lastLoginDate.getDate());

            // If already logged in today, don't update streak
            if (lastLoginDateOnly.getTime() === todayDateOnly.getTime()) {
                return { status: false, message: "Already logged in today", data: { streak_count: streak.count } };
            }

            // Check if yesterday was the last login (consecutive day)
            const yesterday = new Date(todayDateOnly);
            yesterday.setDate(yesterday.getDate() - 1);

            if (lastLoginDateOnly.getTime() === yesterday.getTime()) {
                // Consecutive day - increment streak count by +1
                streak.count += 1;
            } else {
                // Not consecutive - reset streak to 1 (current day)
                streak.count = 1;
            }

            // Update last login date
            streak.last_login = today;
            await streak.save();
        }

        return { status: true, message: "Streak updated", data: { streak_count: streak.count } };

    } catch (error) {
        console.error("Error updating streak:", error);
        throw error;
    }
};

gamificationService.awardPointsWithRule = async (speaker_id, key, rule) => {
    try {
        const points = rule.points;
        const type = rule.type || 'earn';

        if (!points || points <= 0) {
            return {
                status: false,
                message: "Invalid points value",
                data: null
            };
        }

        // Create gamification data record
        const GamificationHistory = await db.GamificationHistory.create({
            speaker_id,
            description: key,
            type,
            points: type === 'redeem' ? -Math.abs(points) : Math.abs(points),
            gamification_id: rule.gamification_id || null
        });

        return {
            status: true,
            message: `Points ${type === 'redeem' ? 'deducted' : 'awarded'} successfully`,
            data: {
                id: GamificationHistory.id,
                speaker_id,
                key,
                points: GamificationHistory.points,
                type,
                created_at: GamificationHistory.created_at
            }
        };

    } catch (error) {
        console.error("Error awarding points with rule:", error);
        throw error;
    }
};


gamificationService.checkEventLimit = async (speaker_id, event, limit_period, limit_count) => {
    try {
        let whereCondition = {
            speaker_id,
            description: event
        };

        if (limit_period > 0) {
            const now = new Date();

            // Always calculate month-wise (1st to last day of current month)
            const startDate = new Date(now.getFullYear(), now.getMonth(), 1); // 1st of current month
            const endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999); // Last day of current month

            whereCondition.created_at = {
                [db.Sequelize.Op.gte]: startDate,
                [db.Sequelize.Op.lte]: endDate
            };
        }

        const count = await db.GamificationHistory.count({
            where: whereCondition
        });

        return count < limit_count;

    } catch (error) {
        console.error("Error checking event limit:", error);
        throw error;
    }
};

gamificationService.handleFormUpdate = async (speaker_id, event) => {
    try {
        // Fetch gamification rule for update first
        const rule = await db.GamificationRules.findOne({
            where: { key: event }
        });

        if (!rule) {
            throw new Error(`No rule found for event type: ${event}.`);
        }

        const limit = await gamificationService.checkEventLimit(speaker_id, event, rule.limit_period, rule.limit_count);
        if (!limit) {
            return { status: false, message: "Update points limit reached for the period", data: null };
        }

        // Award points
        const award = await gamificationService.awardPointsWithRule(speaker_id, event, {
            points: rule.points,
            gamification_id: rule.id,
            type: rule.type || 'earn'
        });
        return { status: award.status, message: award.message, data: award.data };

    } catch (error) {
        console.error("Error handling form update:", error);
        throw error;
    }
}

gamificationService.referralPoints = async (speaker_id) => {
    try {
        const count = await db.GamificationHistory.count({
            where: { speaker_id, description: 'referral_points' }
        });

        let key;
        if (count == 10) {
            key = '10_referral_points';
        } else if (count == 5) {
            key = '5_referral_points';
        } else {
            key = 'referral_points';
        }

        const limit = await gamificationService.checkEventLimit(speaker_id, event, rule.limit_period, rule.limit_count);
        if (!limit) {
            return { status: false, message: "Update points limit reached for the period", data: null };
        }

        // Award points
        const award = await gamificationService.awardPointsWithRule(speaker_id, event, {
            points: rule.points,
            gamification_id: rule.id,
            type: rule.type || 'earn'
        });
        return { status: award.status, message: award.message, data: award.data };

    } catch (error) {
        console.error("Error handling referral points:", error);
        throw error;
    }
}

gamificationService.handlePointsRedemption = async (speaker_id, key, pointsToRedeem) => {
    try {

    } catch (error) {
        console.error("Error processing points redemption:", error);
        throw error;
    }
}


module.exports = gamificationService;
