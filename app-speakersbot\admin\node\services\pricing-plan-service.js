const pricingPlanModel = require("../models/schema/pricing-plan");
const { getStripe } = require("../helpers/stripe");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");
const { Op } = require('sequelize');

const pricingPlanService = {};

// ------------------------- pricing-plan-service -------------------------

/**
 * Create a new pricing plan with Stripe integration.
 * Creates both Stripe product and price, then stores in database.
 * 
 * @param {Object} payload - Plan creation data
 * @param {string} payload.name - Plan name
 * @param {string} payload.billing_interval - Billing interval (month/year)
 * @param {string} payload.description - Plan description
 * @param {number} payload.amount - Plan amount in dollars
 * @returns {Promise<Object>} Created plan data
 * @throws {CustomError} When required fields missing or Stripe/Database operations fail
 */
pricingPlanService.createPlan = async (payload) => {
  try {
    const stripe = await getStripe('secret_key');

    const { name, billing_interval, description, amount } = payload;
    if (!name || !amount) {
      throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Missing required fields");
    }

    const product = await stripe.products.create({
      name,
      description,
    });

    let price;

    if (billing_interval === 'one-time' || !billing_interval) {
      price = await stripe.prices.create({
        unit_amount: amount * 100,
        currency: "usd",
        product: product.id,
      });
    } else {
      price = await stripe.prices.create({
        unit_amount: amount * 100,
        currency: "usd",
        recurring: { interval: billing_interval },
        product: product.id,
      });
    }

    if (!product || !price) {
      throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Stripe product/price creation failed");
    }

    const data = await pricingPlanModel.create({
      name,
      billing_interval: billing_interval || 'one-time',
      description,
      amount,
      currency: "usd",
      stripe_product_id: product.id,
      stripe_price_id: price.id,
      is_active: "1"
    });

    if (!data) {
      throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Database entry creation failed");
    }

    return { status: true, message: "Plan created successfully", data };
  } catch (error) {
    console.log("Error creating plan", error);
    throw error;
  }
};

/**
 * Get all active pricing plans from database.
 * 
 * @returns {Promise<Object>} List of active pricing plans
 * @throws {CustomError} When no plans found or database error occurs
 */
pricingPlanService.getPlans = async () => {
  try {
    const plans = await pricingPlanModel.findAll({
      where: { is_active: "1" },
    });

    if (!plans || plans.length === 0) {
      return { status: false, message: "No active plans found", data: [] };
    }
    const parsePlans = plans.map(plan => {
      const parsedPlan = plan.toJSON();
      parsedPlan.amount = parseFloat(parsedPlan.amount);

      // parse description if it's a JSON string that represents an array/object
      let desc = parsedPlan.description;
      if (typeof desc === 'string') {
        try {
          const parsedDesc = JSON.parse(desc);
          parsedPlan.description = parsedDesc;
        } catch (e) {
          // leave as string
          parsedPlan.description = desc;
        }
      } else {
        parsedPlan.description = desc;
      }

      return parsedPlan;
    });
    return { status: true, message: "Plans fetched successfully", data: parsePlans };
  } catch (error) {
    console.log("Error fetching plans", error);
    throw error;
  }
};

/**
 * Update an existing pricing plan.
 * Updates both Stripe product/price and database record.
 * Creates new Stripe price if amount or billing interval changes.
 * 
 * @param {number|string} id - Plan ID to update
 * @param {Object} updateData - Update data
 * @param {string} [updateData.name] - New plan name
 * @param {string} [updateData.description] - New plan description
 * @param {number} [updateData.amount] - New plan amount
 * @param {string} [updateData.billing_interval] - New billing interval
 * @returns {Promise<Object>} Updated plan data
 * @throws {CustomError} When plan not found or update operations fail
 */
pricingPlanService.updatePlan = async (id, updateData) => {
  try {
    // find plan by id
    const plan = await pricingPlanModel.findByPk(id);
    if (!plan) {
      throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Plan not found");
    }
    const stripe = await getStripe('secret_key');

    // update product data
    if (updateData.name || updateData.description) {
      await stripe.products.update(plan.stripe_product_id, {
        name: updateData.name || plan.name,
        description: updateData.description || plan.description,
      });
    }

    // update price data
    if (updateData.amount || updateData.billing_interval) {
      let newPrice;

      // Determine the billing interval to use
      const newBillingInterval = updateData.billing_interval || plan.billing_interval;

      if (newBillingInterval === 'one-time' || newBillingInterval === 'one_time') {
        // Create one-time price
        newPrice = await stripe.prices.create({
          unit_amount: (updateData.amount || plan.amount) * 100,
          currency: "usd",
          product: plan.stripe_product_id,
        });
      } else {

        newPrice = await stripe.prices.create({
          unit_amount: (updateData.amount || plan.amount) * 100,
          currency: "usd",
          recurring: {
            interval: newBillingInterval,
          },
          product: plan.stripe_product_id,
        });
      }
      updateData.stripe_price_id = newPrice.id;
    }

    // deactivate old price
    await stripe.prices.update(plan.stripe_price_id, {
      active: false,
    });

    // update database record
    const updatedPlan = await plan.update(updateData);

    if (!updatedPlan) {
      throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Plan update failed");
    }
    return { status: true, message: "Plan updated successfully", data: updatedPlan };
  } catch (error) {
    console.log("Error updating plan", error);
    throw error;
  }
};

/**
 * Delete a pricing plan (soft delete).
 * Deactivates Stripe product and marks plan as inactive in database.
 * 
 * @param {number|string} id - Plan ID to delete
 * @returns {Promise<Object>} Success message
 * @throws {CustomError} When plan not found or deletion operations fail
 */
pricingPlanService.deletePlan = async (id) => {
  try {
    const plan = await pricingPlanModel.findByPk(id);
    if (!plan) {
      throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Plan not found");
    }
    const stripe = await getStripe('secret_key');


    await stripe.products.update(plan.stripe_product_id, {
      active: false,
    });

    const data = await plan.update({ is_active: "0" });

    return { status: true, message: "Plan deactivated successfully", data };
  } catch (error) {
    console.log("Error deleting plan", error);
    throw error;
  }
};

module.exports = pricingPlanService;
