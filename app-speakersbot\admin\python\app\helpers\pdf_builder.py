"""
PDF Builder Helper
Generates professional PDF reports from digital form data.
"""

import io
import logging
from datetime import datetime
from typing import Dict, Any, List
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib.enums import TA_LEFT, TA_CENTER, TA_JUSTIFY

from app.helpers.database_manager import db_manager
from app.config.config import config
from app.models.opportunities import SpeakerOpportunity as Opportunity
from app.models.speaker_opportunities import SpeakerOpportunity
from app.helpers.s3_service import S3Service

logger = logging.getLogger(__name__)

# Use centralized database manager
SessionLocal = db_manager.SessionLocal


class PDFGeneratorService:
    """Service to generate PDF reports from digital form data."""

    def __init__(self):
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
        self.session = SessionLocal()
        try:
            self.s3_service = S3Service()
        except ValueError as e:
            logger.error(f"S3 configuration error: {e}")
            self.s3_service = None

    def _setup_custom_styles(self):
        """Setup custom paragraph styles for the PDF using standardized spacing."""
        
        # Introduction text style
        if 'IntroText' not in self.styles:
            font_size = 12
            self.styles.add(ParagraphStyle(
                name='IntroText',
                parent=self.styles['Normal'],
                fontSize=font_size,
                leading=round(font_size * 1.25),       # 15
                spaceBefore=round(font_size * 0.8),    # 10
                spaceAfter=round(font_size * 1.2),     # 14
                fontName='Helvetica',
                alignment=TA_LEFT,
                textColor=colors.black
            ))

        # Section header style (numbered headings)
        if 'SectionHeader' not in self.styles:
            font_size = 18
            self.styles.add(ParagraphStyle(
                name='SectionHeader',
                parent=self.styles['Normal'],
                fontSize=font_size,
                leading=round(font_size * 1.25),       # 23
                spaceBefore=round(font_size * 1.0),    # 18
                spaceAfter=round(font_size * 0.7),     # 13
                fontName='Helvetica-Bold',
                alignment=TA_LEFT,
                textColor=colors.black
            ))

        # Body text style
        if 'CustomBodyText' not in self.styles:
            font_size = 11
            self.styles.add(ParagraphStyle(
                name='CustomBodyText',
                parent=self.styles['Normal'],
                fontSize=font_size,
                leading=round(font_size * 1.25),       # 14
                spaceBefore=round(font_size * 0.4),    # 4
                spaceAfter=round(font_size * 0.6),     # 7
                fontName='Helvetica',
                alignment=TA_LEFT,
                leftIndent=0,
                textColor=colors.black
            ))

        # Bullet point style
        if 'BulletText' not in self.styles:
            font_size = 11
            self.styles.add(ParagraphStyle(
                name='BulletText',
                parent=self.styles['Normal'],
                fontSize=font_size,
                leading=round(font_size * 1.2),        # 14
                spaceBefore=round(font_size * 0.4),    # 4
                spaceAfter=round(font_size * 0.7),     # 8
                leftIndent=round(font_size * 3.0),     # 22
                fontName='Helvetica',
                alignment=TA_LEFT,
                textColor=colors.black
            ))

        # Event title style (bold and highlighted)
        if 'EventTitleText' not in self.styles:
            font_size = 13
            self.styles.add(ParagraphStyle(
                name='EventTitleText',
                parent=self.styles['Normal'],
                fontSize=font_size,
                leading=round(font_size * 1.25),       # 16
                spaceBefore=round(font_size * 0.8),    # 10
                spaceAfter=round(font_size * 0.7),     # 9
                leftIndent=round(font_size * 0.8),     # 10
                fontName='Helvetica-Bold',
                alignment=TA_LEFT,
                textColor=colors.black
            ))

        # URL style (clickable link)
        if 'EventUrlText' not in self.styles:
            font_size = 9
            self.styles.add(ParagraphStyle(
                name='EventUrlText',
                parent=self.styles['Normal'],
                fontSize=font_size,
                leading=round(font_size * 1.2),        # 11
                spaceAfter=round(font_size * 0.5),     # 5
                leftIndent=round(font_size * 2.0),     # 18
                fontName='Helvetica',
                alignment=TA_LEFT,
                textColor=colors.black
            ))

        # Similar opportunities URL style
        if 'SimilarEventUrlText' not in self.styles:
            font_size = 9
            self.styles.add(ParagraphStyle(
                name='SimilarEventUrlText',
                parent=self.styles['Normal'],
                fontSize=font_size,
                leading=round(font_size * 1.2),        # 11
                spaceAfter=round(font_size * 0.5),     # 5
                leftIndent=round(font_size * 2.0),     # 18
                fontName='Helvetica',
                alignment=TA_LEFT,
                textColor=colors.blue,
            ))

        # Section subheader style
        if 'SectionHeaderText' not in self.styles:
            font_size = 13
            self.styles.add(ParagraphStyle(
                name='SectionHeaderText',
                parent=self.styles['Normal'],
                fontSize=font_size,
                leading=round(font_size * 1.25),       # 18
                spaceBefore=round(font_size * 0.8),    # 11
                spaceAfter=round(font_size * 0.6),     # 8
                leftIndent=round(font_size * 1.4),     # 20
                fontName='Helvetica-Bold',
                alignment=TA_LEFT,
                textColor=colors.black
            ))

        # Section divider
        if 'DividerText' not in self.styles:
            font_size = 9
            self.styles.add(ParagraphStyle(
                name='DividerText',
                parent=self.styles['Normal'],
                fontSize=font_size,
                leading=round(font_size * 1.25),       # 11
                spaceBefore=round(font_size * 1.0),    # 9
                spaceAfter=round(font_size * 1.0),     # 9
                fontName='Helvetica',
                alignment=TA_LEFT,
                textColor=colors.black
            ))

        # Enhanced section header
        if 'EnhancedSectionHeader' not in self.styles:
            font_size = 16
            self.styles.add(ParagraphStyle(
                name='EnhancedSectionHeader',
                parent=self.styles['Normal'],
                fontSize=font_size,
                leading=round(font_size * 1.25),       # 20
                spaceBefore=round(font_size * 1.2),    # 19
                spaceAfter=round(font_size * 1.0),     # 16
                fontName='Helvetica-Bold',
                alignment=TA_LEFT,
                textColor=colors.black
            ))

        # Enhanced subsection header
        if 'EnhancedSubHeader' not in self.styles:
            font_size = 13
            self.styles.add(ParagraphStyle(
                name='EnhancedSubHeader',
                parent=self.styles['Normal'],
                fontSize=font_size,
                leading=round(font_size * 1.25),       # 18
                spaceBefore=round(font_size * 0.9),    # 13
                spaceAfter=round(font_size * 0.7),     # 10
                leftIndent=round(font_size * 1.4),     # 20
                fontName='Helvetica-Bold',
                alignment=TA_LEFT,
                textColor=colors.black
            ))

    def _get_similar_2026_opportunities_direct(self, current_opportunity_id: int) -> List[Dict[str, Any]]:
        """Get exactly 10 similar 2026 opportunities directly from MySQL with fallback strategy."""
        try:
            # Get current opportunity to find its industry and search query
            current_opp = self.session.query(Opportunity).filter_by(id=current_opportunity_id).first()
            if not current_opp:
                logger.error(f"Opportunity {current_opportunity_id} not found in MySQL database")
                return []
            
            if not current_opp.industry:
                logger.warning(f"Opportunity {current_opportunity_id} has no industry field")
                return []
            
            current_industry = current_opp.industry
            search_query = getattr(current_opp, 'search_query', '') or ''
            
            # Step 1: Get 2026 opportunities with same industry
            industry_opportunities = self.session.query(Opportunity)\
                .filter(Opportunity.start_date.like('2026%'))\
                .filter(Opportunity.industry.like(f'%{current_industry}%'))\
                .filter(Opportunity.id != current_opportunity_id)\
                .limit(10)\
                .all()
            # Step 2: If less than 10, fill remaining with 2026 opportunities matching search query
            remaining_needed = 10 - len(industry_opportunities)
            additional_opportunities = []
            if remaining_needed > 0 and search_query:
                # Get additional opportunities that match search query but not already included
                existing_ids = [opp.id for opp in industry_opportunities]
                existing_ids.append(current_opportunity_id)
                additional_opportunities = self.session.query(Opportunity)\
                    .filter(Opportunity.start_date.like('2026%'))\
                    .filter(Opportunity.search_query.like(f'%{search_query}%'))\
                    .filter(~Opportunity.id.in_(existing_ids))\
                    .limit(remaining_needed)\
                    .all()
            # Combine both lists
            all_opportunities = industry_opportunities + additional_opportunities
            # Format the results for PDF
            formatted_opportunities = []
            for opp in all_opportunities:
                # Format date and location
                date_location = "TBD"
                if opp.start_date:
                    if opp.country:
                        date_location = f"{opp.start_date.strftime('%B %d, %Y')} | {opp.country}"
                    else:
                        date_location = f"{opp.start_date.strftime('%B %d, %Y')}"
                # Create unique relevance message based on opportunity data
                relevance_message = self._generate_unique_relevance_message(opp, current_industry)
                formatted_opp = {
                    "title": opp.title or "Unknown Event",
                    "event_url": opp.event_url or opp.source_url or f"https://example.com/{opp.id}",
                    "date": date_location,
                    "relevance_message": relevance_message,
                    "organization": opp.organization or "Unknown Organization",
                    "event_type": opp.event_type or "Conference",
                    "industry": opp.industry or current_industry
                }
                formatted_opportunities.append(formatted_opp)
            return formatted_opportunities
        except Exception as e:
            logger.error(f"Error getting similar opportunities from MySQL: {e}")
            return []

    def _generate_unique_relevance_message(self, opp: Opportunity, current_industry: str) -> str:
        """Generate a unique relevance message based on opportunity data."""
        relevance_parts = []
        # Add industry relevance
        if opp.industry:
            if opp.industry.lower() != current_industry.lower():
                relevance_parts.append(f"Perfect crossover opportunity from {opp.industry} industry")
            else:
                relevance_parts.append(f"Highly relevant {opp.industry} industry event")
        # Add organization relevance
        if opp.organization:
            relevance_parts.append(f"Excellent platform through {opp.organization}")
        # Add location relevance
        if opp.city and opp.country:
            relevance_parts.append(f"Great networking opportunity in {opp.city}, {opp.country}")
        elif opp.country:
            relevance_parts.append(f"International reach in {opp.country}")
        # Add event type relevance
        if opp.event_type:
            event_type_lower = opp.event_type.lower()
            if opp.event_type:
                relevance_parts.append(f"Ideal {opp.event_type.lower()} format for speaker engagement")
        # Add date relevance (if it's 2026)
        if opp.start_date and opp.start_date.year == 2026:
            relevance_parts.append("Perfect timing for 2026 speaker calendar")
        # Add virtual/physical relevance
        if hasattr(opp, 'is_virtual') and opp.is_virtual:
            relevance_parts.append("Virtual format expands speaker reach")
        elif opp.city:
            relevance_parts.append("In-person engagement for stronger connections")
        # Default message if no specific data
        if not relevance_parts:
            relevance_parts.append(f"This {current_industry} event aligns well with speaker expertise")
        
        return ". ".join(relevance_parts) + "."

    def generate_speaker_opportunity_pdf(self, data: Dict[str, Any], opportunity_id: int = None) -> bytes:
        """Generate a PDF report from digital form data in the specified format."""
        try:
            buffer = io.BytesIO()
            # Extract metadata
            metadata = data.get('_metadata', {})
            speaker_name = metadata.get('speaker_name', 'Unknown Speaker')
            organization = metadata.get('organization', 'Unknown Organization')
            event_title = metadata.get('event_title', 'Unknown Event')

            # Create PDF with metadata
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18,
                title=f"{speaker_name} - DSA Report",
                author="SpeakerBot System",
                subject=f"Digital PDF for {speaker_name}",
                creator="SpeakerBot Digital Form Service"
            )
           
            # Build the story (content)
            story = []
           
            # Title and introduction
            story.append(Paragraph(f"Thank you for the details. Here is a fully customized outreach, proposal, and event analysis package for {speaker_name} at the {event_title}.", self.styles['IntroText']))
            # Add divider line after introduction
            story.append(Paragraph("___________________________________________________________________________________", self.styles['DividerText']))
            # Section divider function
            def add_section_divider():
                story.append(Paragraph("___________________________________________________________________________________", self.styles['DividerText']))
            # 1) Top 3 Attendee Challenges
            challenges = data.get('Top 3 Attendee Challenges')
            if challenges:
                story.append(Paragraph("1) Top 3 Attendee Challenges", self.styles['EnhancedSectionHeader']))
                for i in range(1, 4):
                    challenge_key = f'Challenge #{i}'
                    challenge = challenges.get(challenge_key)
                    if challenge:
                        title = challenge.get('title', f'Challenge {i}')
                        description = challenge.get('description', 'No description available')
                        
                        story.append(Paragraph(f" Challenge #{i}: {title}.", self.styles['EnhancedSubHeader']))
                        story.append(Paragraph(f" {description}", self.styles['BulletText']))
                        story.append(Spacer(1, 6))
                
                add_section_divider()
            
            # 2) SWOT Analysis
            swot_key = next((key for key in data.keys() if 'SWOT Analysis' in key), None)
            if swot_key:
                swot_data = data.get(swot_key)
                if swot_data:
                    story.append(Paragraph("2) SWOT Analysis", self.styles['EnhancedSectionHeader']))
                    
                    # Extract organization name from SWOT key
                    org_name = swot_key.replace('SWOT Analysis for ', '')
                    
                    # Strengths section
                    strengths = swot_data.get('Strengths', [])
                    if strengths:
                        story.append(Paragraph(" Strengths:", self.styles['EnhancedSubHeader']))
                        for strength in strengths:
                            story.append(Paragraph(f" • {strength}", self.styles['BulletText']))
                        story.append(Spacer(1, 6))
                    # Weaknesses section
                    weaknesses = swot_data.get('Weaknesses', [])
                    if weaknesses:
                        story.append(Paragraph(" Weaknesses:", self.styles['EnhancedSubHeader']))
                        for weakness in weaknesses:
                            story.append(Paragraph(f"       • {weakness}", self.styles['BulletText']))
                        story.append(Spacer(1, 6))
                    
                    # Opportunities section
                    opportunities = swot_data.get('Opportunities', [])
                    if opportunities:
                        story.append(Paragraph(" Opportunities:", self.styles['EnhancedSubHeader']))
                        for opportunity in opportunities:
                            story.append(Paragraph(f"       • {opportunity}", self.styles['BulletText']))
                        story.append(Spacer(1, 6))
                    
                    # Threats section
                    threats = swot_data.get('Threats', [])
                    if threats:
                        story.append(Paragraph(" Threats:", self.styles['EnhancedSubHeader']))
                        for threat in threats:
                            story.append(Paragraph(f"       • {threat}", self.styles['BulletText']))
                        story.append(Spacer(1, 6))
                    
                    add_section_divider()
            
            # 3) SMART Analysis
            smart_data = data.get('SMART Analysis')
            if smart_data:
                story.append(Paragraph("3) SMART Analysis", self.styles['EnhancedSectionHeader']))
                
                smart_items = ['Specific', 'Measurable', 'Attainable', 'Relevant', 'Timely']
                for item in smart_items:
                    value = smart_data.get(item, 'Not specified')
                    story.append(Paragraph(f" {item}:", self.styles['EnhancedSubHeader']))
                    story.append(Paragraph(f" {value}", self.styles['BulletText']))
                    story.append(Spacer(1, 4))
                
                add_section_divider()
            
            # 4) Previous Conference Research
            conference_key = next((key for key in data.keys() if 'Previous Two Annual' in key), None)
            if conference_key:
                conference_data = data.get(conference_key)
                if conference_data:
                    story.append(Paragraph("4) Previous Two Annual Conferences – Keynote & Breakout Speaker Research", self.styles['EnhancedSectionHeader']))
                    
                    # 2025 Keynote Speakers
                    keynote_2025 = conference_data.get('2025 Keynote Speakers', [])
                    if keynote_2025:
                        story.append(Paragraph("2025 Keynote Speakers:", self.styles['SectionHeaderText']))
                        for speaker in keynote_2025:
                            name = speaker.get('name', 'Unknown')
                            topic = speaker.get('topic', '')
                            fee = speaker.get('estimated_fee_range', 'Fee TBD')
                            if topic:
                                story.append(Paragraph(f" {name} ({topic}) – Est. Fee: {fee}", self.styles['BulletText']))
                            else:
                                story.append(Paragraph(f" {name} – Est. Fee: {fee}", self.styles['BulletText']))
                    
                    # 2025 Breakouts
                    breakout_2025 = conference_data.get('2025 Breakouts', [])
                    if breakout_2025:
                        story.append(Paragraph("• 2025 Breakouts:", self.styles['SectionHeaderText']))
                        for speaker in breakout_2025:
                            name = speaker.get('name', 'Unknown')
                            fee = speaker.get('estimated_fee_range', 'Fee TBD')
                            story.append(Paragraph(f"  {name} – Est. Fee: {fee}", self.styles['BulletText']))
                    
                    # 2024 Keynote Speakers
                    keynote_2024 = conference_data.get('2024 Keynote Speakers', [])
                    if keynote_2024:
                        story.append(Paragraph("• 2024 Keynote Speakers:", self.styles['SectionHeaderText']))
                        for speaker in keynote_2024:
                            name = speaker.get('name', 'Unknown')
                            topic = speaker.get('topic', '')
                            fee = speaker.get('estimated_fee_range', 'Fee TBD')
                            if topic:
                                story.append(Paragraph(f"  {name} ({topic}) – Est. Fee: {fee}", self.styles['BulletText']))
                            else:
                                story.append(Paragraph(f"  {name} – Est. Fee: {fee}", self.styles['BulletText']))
                    
                    # 2024 Breakouts
                    breakout_2024 = conference_data.get('2024 Breakouts', [])
                    if breakout_2024:
                        story.append(Paragraph("• 2024 Breakouts:", self.styles['SectionHeaderText']))
                        for speaker in breakout_2024:
                            name = speaker.get('name', 'Unknown')
                            fee = speaker.get('estimated_fee_range', 'Fee TBD')
                            story.append(Paragraph(f"  {name} – Est. Fee: {fee}", self.styles['BulletText']))
                    
                    add_section_divider()
            
            # 5) Introduction Email
            intro_email = data.get('Introduction Email')
            if intro_email:
                story.append(Paragraph("5) Introduction Email", self.styles['EnhancedSectionHeader']))
                
                subject = intro_email.get('Subject Line', 'No subject')
                body = intro_email.get('Body', 'No content')
                
                story.append(Paragraph("Subject Line:", self.styles['SectionHeaderText']))
                story.append(Paragraph(subject, self.styles['BulletText']))
                story.append(Paragraph("Email Body:", self.styles['SectionHeaderText']))
                
                # Format email body with proper line breaks
                if body and isinstance(body, str):
                    body_lines = body.split('\n')
                    for i, line in enumerate(body_lines):
                        line = line.strip()
                        if not line:
                            continue
                        
                        # Add greeting line
                        if line.startswith('Dear '):
                            story.append(Paragraph(line, self.styles['BulletText']))
                            story.append(Spacer(1, 6))
                        # Add signature with space before
                        elif line.startswith('Best regards') or line.startswith('Best,') or line.startswith('Sincerely'):
                            story.append(Spacer(1, 12))
                            story.append(Paragraph(line, self.styles['BulletText']))
                        # Add other content
                        else:
                            story.append(Paragraph(line, self.styles['BulletText']))
                            # Add space after lines that contain CTAs
                            if 'let me know' in line.lower() or 'contact me' in line.lower():
                                story.append(Spacer(1, 8))
                            elif i < len(body_lines) - 1:
                                story.append(Spacer(1, 4))
                else:
                    story.append(Paragraph(body, self.styles['BulletText']))
                
                add_section_divider()
            
            # 6) Follow-Up Email Sequence
            follow_up = data.get('Follow-Up Email Sequence')
            if follow_up:
                story.append(Paragraph("6) Follow-Up Email Sequence", self.styles['EnhancedSectionHeader']))
                
                for email_key, email_data in follow_up.items():
                    if isinstance(email_data, dict):
                        story.append(Paragraph(email_key, self.styles['EnhancedSubHeader']))
                        story.append(Spacer(1, 8))
                        
                        send_date = email_data.get('Send', 'TBD')
                        subject = email_data.get('Subject Line', 'No subject')
                        body = email_data.get('Body', 'No content')
                        
                        story.append(Paragraph(f"Send: {send_date}", self.styles['SectionHeaderText']))
                        story.append(Paragraph("Subject Line:", self.styles['SectionHeaderText']))
                        story.append(Paragraph(subject, self.styles['BulletText']))
                        story.append(Paragraph("Email Body:", self.styles['SectionHeaderText']))
                        
                        # Format email body with proper line breaks
                        if body and isinstance(body, str):
                            body_lines = body.split('\n')
                            for i, line in enumerate(body_lines):
                                line = line.strip()
                                if not line:
                                    continue
                                
                                # Add greeting line
                                if line.startswith('Dear '):
                                    story.append(Paragraph(line, self.styles['BulletText']))
                                    story.append(Spacer(1, 6))
                                # Add signature with space before
                                elif line.startswith('Best regards') or line.startswith('Best,') or line.startswith('Sincerely'):
                                    story.append(Spacer(1, 12))
                                    story.append(Paragraph(line, self.styles['BulletText']))
                                # Add other content
                                else:
                                    story.append(Paragraph(line, self.styles['BulletText']))
                                    # Add space after lines that contain CTAs
                                    if 'let me know' in line.lower() or 'contact me' in line.lower():
                                        story.append(Spacer(1, 8))
                                    elif i < len(body_lines) - 1:
                                        story.append(Spacer(1, 4))
                        else:
                            story.append(Paragraph(body, self.styles['BulletText']))
                        
                        story.append(Spacer(1, 12))
                
                add_section_divider()
            
            # 7) Voicemail Script
            voicemail = data.get('20–30 Second Voicemail Script')
            if voicemail:
                story.append(Paragraph("7) 20–30 Second Voicemail Script", self.styles['EnhancedSectionHeader']))
                story.append(Paragraph(voicemail, self.styles['BulletText']))
                
                add_section_divider()
            
            # 8) Customized RFP
            rfp = data.get('Customized RFP')
            if rfp:
                story.append(Paragraph("8) Customized RFP", self.styles['EnhancedSectionHeader']))
                
                title = rfp.get('Proposed Keynote Title', 'No title')
                short_desc = rfp.get('Short Description', 'No description')
                detailed_desc = rfp.get('Detailed Description', 'No detailed description')
                learning_obj = rfp.get('Learning Objectives', [])
                attendee_benefits = rfp.get('Attendee Benefits', [])
                key_challenges = rfp.get('Key Challenges', [])
                speaker_bio = rfp.get('Speaker Bio (Tailored)', 'No bio')
                
                story.append(Paragraph("Proposed Keynote Title:", self.styles['SectionHeaderText']))
                story.append(Paragraph(title, self.styles['BulletText']))
                story.append(Spacer(1, 5))
                
                story.append(Paragraph("Short Description:", self.styles['SectionHeaderText']))
                story.append(Paragraph(short_desc, self.styles['BulletText']))
                story.append(Spacer(1, 5))
                
                story.append(Paragraph("Detailed Description:", self.styles['SectionHeaderText']))
                story.append(Paragraph(detailed_desc, self.styles['BulletText']))
                story.append(Spacer(1, 5))
                
                if learning_obj:
                    story.append(Paragraph("Learning Objectives:", self.styles['SectionHeaderText']))
                    for obj in learning_obj:
                        story.append(Paragraph(f"• {obj}", self.styles['BulletText']))
                    story.append(Spacer(1, 8))
                
                if attendee_benefits:
                    story.append(Paragraph("Attendee Benefits:", self.styles['SectionHeaderText']))
                    for benefit in attendee_benefits:
                        story.append(Paragraph(f"• {benefit}", self.styles['BulletText']))
                    story.append(Spacer(1, 8))
                
                if key_challenges:
                    story.append(Paragraph("Key Challenges:", self.styles['SectionHeaderText']))
                    for challenge in key_challenges:
                        story.append(Paragraph(f"• {challenge}", self.styles['BulletText']))
                    story.append(Spacer(1, 8))
                
                story.append(Paragraph("Speaker Bio (Tailored):", self.styles['SectionHeaderText']))
                story.append(Paragraph(speaker_bio, self.styles['BulletText']))
                
                # Add Speaker Bio (Long) inside RFP
                long_bio = rfp.get('Speaker Bio (Long)', '')
                if long_bio:
                    story.append(Paragraph("Speaker Bio (Long):", self.styles['SectionHeaderText']))
                    story.append(Paragraph(long_bio, self.styles['BulletText']))
                
                add_section_divider()
            
            # 9) NYT Article
            nyt_article = data.get('500-Word New York Times-Style Article')
            if nyt_article:
                story.append(Paragraph("9) 500-Word New York Times-Style Article", self.styles['EnhancedSectionHeader']))
                
                article_title = nyt_article.get('Title', 'No title')
                byline = nyt_article.get('Byline', 'Unknown Author')
                body = nyt_article.get('Body', 'No content')
                
                story.append(Paragraph(article_title, self.styles['BulletText']))
                story.append(Paragraph(f"By {byline}", self.styles['BulletText']))
                story.append(Paragraph(body, self.styles['BulletText']))
                
                add_section_divider()
            
            # 10) Similar Events - Direct from MySQL
            story.append(Paragraph("10) Ten Similar Verified Events for 2026", self.styles['EnhancedSectionHeader']))
            
            if opportunity_id:
                # Get similar opportunities directly from MySQL
                similar_events = self._get_similar_2026_opportunities_direct(opportunity_id)
                
                if similar_events:
                    for i, event in enumerate(similar_events[:10], 1):  # Limit to 10 events
                        title = event.get('title', 'Unknown Event')
                        event_url = event.get('event_url', 'https://...')
                        date = event.get('date', 'TBD')
                        relevance_message = event.get('relevance_message', 'N/A')
                        
                        # Highlighted event title (bold)
                        story.append(Paragraph(f"{i}. {title}", self.styles['EventTitleText']))
                        
                        # URL as clickable link (blue color)
                        story.append(Paragraph(f"   {event_url}", self.styles['SimilarEventUrlText']))
                        
                        # Date and location
                        story.append(Paragraph(f"   {date}", self.styles['BulletText']))
                        
                        # Unique relevance message
                        story.append(Paragraph(f"   {relevance_message}", self.styles['BulletText']))
                        
                        # Add spacing between events
                        story.append(Paragraph("", self.styles['BulletText']))
                else:
                    story.append(Paragraph("No similar opportunities found in the database.", self.styles['BulletText']))
            else:
                story.append(Paragraph("Opportunity ID not provided for similar events lookup.", self.styles['BulletText']))
            
            add_section_divider()
            
            # Custom personalized note
            speaker_name = metadata.get('speaker_name', 'Unknown Speaker')
            event_title = metadata.get('event_title', 'Unknown Event')
            organization = metadata.get('organization', 'Unknown Organization')
            
            # Get current date and time in UTC
            current_time = datetime.utcnow()
            formatted_time = current_time.strftime("%I:%M %p %b %d, %Y UTC")
            
            personalized_note = f"All content above is customized for {speaker_name} and fully aligned with {organization}'s mission, theme, and the current state of physician well-being."
            timestamp_note = f"{formatted_time}"
            
            story.append(Paragraph(personalized_note, self.styles['IntroText']))
            story.append(Paragraph(timestamp_note, self.styles['BulletText']))
            
            # Build PDF
            doc.build(story)
            buffer.seek(0)
            return buffer.getvalue()
            
        except Exception as e:
            logger.error(f"Error generating PDF: {e}")
            raise RuntimeError(f"PDF generation failed: {e}")

    def generate_simple_pdf(self, title: str, content: str) -> bytes:
        """Generate a simple PDF with title and content."""
        try:
            buffer = io.BytesIO()
            doc = SimpleDocTemplate(
                buffer,
                pagesize=A4,
                title=title,
                author="SpeakerBot System",
                subject="Generated Document",
                creator="SpeakerBot Digital Form Service"
            )            
            story = []
            story.append(Paragraph(title, self.styles['CustomTitle']))
            story.append(Spacer(1, 20))
            story.append(Paragraph(content, self.styles['CustomBodyText']))
            
            doc.build(story)
            buffer.seek(0)
            return buffer.getvalue()
            
        except Exception as e:
            logger.error(f"Error generating simple PDF: {e}")
            raise RuntimeError(f"Simple PDF generation failed: {e}")
    
    def generate_and_upload_pdf(self, data: Dict[str, Any], speaker_id: int, opportunity_id: int) -> str:
        """Generate PDF, upload to S3, update database, and return URL."""
        try:
            # Check if S3 service is available
            if not self.s3_service:
                raise RuntimeError("S3 service is not configured. Please set AWS credentials and S3_BUCKET_NAME environment variables.")
            # Generate PDF
            pdf_bytes = self.generate_speaker_opportunity_pdf(data, opportunity_id)
            # Create filename with timestamp
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            filename = f"speaker_{speaker_id}_opportunity_{opportunity_id}_report_{timestamp}.pdf"
            # Upload to S3
            pdf_url = self.s3_service.upload_pdf(pdf_bytes, filename)
            if not pdf_url:
                raise RuntimeError("Failed to upload PDF to S3")
            # Update speaker_opportunities table
            speaker_opportunity = self.session.query(SpeakerOpportunity).filter_by(
                speaker_id=speaker_id, 
                opportunity_id=opportunity_id
            ).first()
            
            if speaker_opportunity:
                speaker_opportunity.speaker_dsa_url = pdf_url
                self.session.commit()
            else:
                logger.warning(f"No speaker_opportunity record found for speaker_id={speaker_id}, opportunity_id={opportunity_id}")
            
            return pdf_url
            
        except Exception as e:
            logger.error(f"Error in generate_and_upload_pdf: {e}")
            raise RuntimeError(f"PDF generation and upload failed: {e}")
