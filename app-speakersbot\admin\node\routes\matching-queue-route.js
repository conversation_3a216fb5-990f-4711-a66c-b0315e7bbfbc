const matchingQueueController = require("../controllers/matching-queue-controller");
const verifyToken = require("../middlewares/verify-token");
const router = require("express").Router();

module.exports = (app) => { 

    // ------------------------- matching-queue -------------------------

    // get all matching queue items
    router.get("/matching-queue", matchingQueueController.getMatchingQueue);

   
    // export matching queue to csv
    router.get("/matching-queue/export", matchingQueueController.exportMatchingQueue);

     // get matching queue item by id
     router.get("/matching-queue/:id", matchingQueueController.getMatchingQueueById);

    app.use("/admin/api/v1", router);
    

}