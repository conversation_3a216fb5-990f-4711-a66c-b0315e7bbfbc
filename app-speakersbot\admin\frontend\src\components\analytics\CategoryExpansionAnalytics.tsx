import React, { useEffect, useState } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "../ui/card";
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from "recharts";
import {
  Database,
  Search,
  Trophy,
  ArrowUp,
  ArrowDown,
  ArrowRight,
  AlertTriangle,
} from "lucide-react";
import { Badge } from "../ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import { Button } from "../ui/button";
import {
  useGetDashboardCategoriesQuery,
  usePromoteToCategoryMutation,
} from "../../apis/dashboardApi";
import { Skeleton } from "../ui/skeleton";
import { toast } from "@/hooks/use-toast";
import { InvalidTokenHandler } from "../common/InvalidTokenHandler";

const CategoryExpansionAnalytics: React.FC = () => {
  // State to store categories data
  const [categoriesState, setCategoriesState] = useState<any>(null);
  const [isDataLoading, setIsDataLoading] = useState(false);

  // API call for dashboard categories
  const {
    data: categoriesData,
    isLoading,
    error,
    refetch: refetchCategories,
  } = useGetDashboardCategoriesQuery(undefined);

  const [promoteToCategory] = usePromoteToCategoryMutation();

  const handlePromoteToCategory = async (entry: any) => {
    let payload = {
      category: entry.otherEntry || "--",
    };

    try {
      const result = await promoteToCategory(payload).unwrap();

      // Show success toast
      toast({
        title: "Category promoted successfully",
        description:
          "The category has been promoted and data has been refreshed.",
      });

      // Refetch categories data
      refetchCategories();
    } catch (error) {
      console.error("Error promoting category:", error);

      // Show error toast
      toast({
        title: "Failed to promote category",
        description: "Please try again later.",
        variant: "destructive",
      });
    }
  };

  // Update state and log when data changes
  useEffect(() => {
    if (isLoading) {
      setIsDataLoading(true);
    } else if (categoriesData) {
      setCategoriesState(categoriesData);
      setIsDataLoading(false);
      // Show success toast when data loads
      // toast({
      //   title: "Category analytics loaded successfully",
      // });
    } else if (error) {
      setIsDataLoading(false);
      console.error("Dashboard Categories Error:", error);
      toast({
        title: "Failed to load categories data. Please try again.",
        variant: "destructive",
      });
    }
  }, [categoriesData, error, isLoading]);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <InvalidTokenHandler error={error} />
        <div>
          <Skeleton className="h-5 w-72" />
          <Skeleton className="h-4 w-96 mt-2" />
        </div>

        {/* KPI Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="bg-tertiary border-border">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-6 w-20" />
                  </div>
                  <Skeleton className="h-6 w-6 rounded" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Charts Skeleton */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          {Array.from({ length: 2 }).map((_, i) => (
            <Card key={i} className="bg-tertiary border-border">
              <CardHeader>
                <Skeleton className="h-6 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-[300px] w-full" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Table Skeleton */}
        <Card className="bg-tertiary border-border">
          <CardHeader>
            <Skeleton className="h-6 w-64" />
            <Skeleton className="h-4 w-96 mt-2" />
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Array.from({ length: 5 }).map((_, i) => (
                <div key={i} className="flex items-center space-x-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-4 w-40" />
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-8 w-8" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <InvalidTokenHandler error={error} />
      <div>
        <h3 className="text-xl font-semibold text-foreground mb-2">
          Category Expansion Insights
        </h3>
        <p className="text-muted-foreground text-sm mb-6">
          Analyze category performance, emerging trends, and expansion
          opportunities
        </p>
      </div>

      {/* Show error state */}
      {error && (
        <Card className="bg-tertiary border-border">
          <CardContent className="p-8 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Error Loading Data
            </h3>
            <p className="text-muted-foreground">
              Unable to fetch category analytics data. Please try again later.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Show data when loaded and no error */}
      {!isLoading && !error && (
        <>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
        {/* Active Categories Card */}
        <Card className="bg-tertiary border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Active Categories
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {categoriesState?.data?.activeCategories?.totalActive ||
                    0}
                </p>
                <Badge
                  variant={
                    categoriesState?.data?.activeCategories
                      ?.addedThisQuarter >= 0
                      ? "secondary"
                      : "destructive"
                  }
                  className="mt-1"
                >
                  {categoriesState?.data?.activeCategories
                    ?.addedThisQuarter >= 0
                    ? "+"
                    : ""}
                  {categoriesState?.data?.activeCategories
                    ?.addedThisQuarter || 0}{" "}
                  this quarter
                </Badge>
              </div>

              <Database className="h-5 w-5 text-dashboard-dark-blue" />
            </div>
          </CardContent>
        </Card>

        {/* Fastest Growing Card */}
        <Card className="bg-tertiary border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Fastest Growing
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {categoriesState?.data?.fastestGrowingCategory
                    ?.categoryName || "--"}
                </p>
                <Badge
                  variant={
                    categoriesState?.data?.fastestGrowingCategory
                      ?.growthPercentage >= 0
                      ? "outline"
                      : "destructive"
                  }
                  className="mt-1 w-max"
                >
                  {categoriesState?.data?.fastestGrowingCategory
                    ?.growthPercentage >= 0
                    ? "+"
                    : ""}
                  {categoriesState?.data?.fastestGrowingCategory?.growthPercentage?.toFixed(
                    1
                  ) || 0}
                  % growth
                </Badge>
              </div>

              <Trophy className="h-5 w-5 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        {/* Biggest Gap Card */}
        <Card className="bg-tertiary border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Biggest Gap
                </p>
                <p className="text-2xl font-bold break-words text-foreground">
                  {categoriesState?.data?.biggestGapCategory
                    ?.categoryName || "--"}
                </p>
                <Badge variant="destructive" className="mt-1 w-max">
                  {categoriesState?.data?.biggestGapCategory
                    ?.speakerCount || 0}{" "}
                  speakers needed
                </Badge>
              </div>

              <Search className="h-5 w-5 text-red-500" />
            </div>
          </CardContent>
        </Card>

        {/* Average Category Growth Card */}
        <Card className="bg-tertiary border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex flex-col gap-1">
                <p className="text-sm font-medium text-muted-foreground">
                  Avg Category Growth
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {categoriesState?.data?.avgCategoryGrowth?.percentage?.toFixed(
                    1
                  ) || 0}
                  %
                </p>
                <p
                  className={`text-xs flex items-center mt-1 ${
                    categoriesState?.data?.avgCategoryGrowth
                      ?.fromLastQuarter >= 0
                      ? "text-green-500"
                      : "text-red-500"
                  }`}
                >
                  {categoriesState?.data?.avgCategoryGrowth
                    ?.fromLastQuarter >= 0 ? (
                    <ArrowUp className="h-3 w-3 mr-1" />
                  ) : (
                    <ArrowDown className="h-3 w-3 mr-1" />
                  )}
                  {categoriesState?.data?.avgCategoryGrowth
                    ?.fromLastQuarter >= 0
                    ? "+"
                    : ""}
                  {categoriesState?.data?.avgCategoryGrowth?.fromLastQuarter?.toFixed(
                    1
                  ) || 0}
                  % from last quarter
                </p>
              </div>

              {categoriesState?.data?.avgCategoryGrowth?.fromLastQuarter >=
                0 ? (
                <ArrowUp className="h-6 w-6 text-green-500" />
              ) : (
                <ArrowDown className="h-6 w-6 text-red-500" />
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        <Card className="bg-tertiary border-border">
          <CardHeader>
            <CardTitle className="text-foreground">
              Category Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              {categoriesState?.data?.categoryPerformance?.length > 0 ? (   
              <BarChart
                data={categoriesState?.data?.categoryPerformance || []}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="categoryName" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar
                  dataKey="opportunityCount"
                  fill="hsl(var(--dashboard-light-blue))"
                  name="Opportunities"
                />
                <Bar
                  dataKey="speakerCount"
                  fill="hsl(var(--dashboard-dark-blue))"
                  name="Speakers"
                />
              </BarChart>
              ) : (
                <div className="flex justify-center items-center h-full">
                  <p className="text-muted-foreground text-sm">No Category Performance Data Available</p>
                </div>
              )}
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="bg-tertiary border-border">
          <CardHeader>
            <CardTitle className="text-foreground">
              Emerging Categories
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              {categoriesState?.data?.emergingCategories?.length > 0 ? (   
              <PieChart>
                <Pie
                  data={categoriesState?.data?.emergingCategories || []}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="percentage"
                  label={({ categoryName, percentage }: any) =>
                    `${categoryName}: ${percentage?.toFixed(2)}%`
                  }
                >
                  {(categoriesState?.data?.emergingCategories || []).map(
                    (entry, index: number) => {
                      // Generate random colors
                      const colors = [
                        "hsl(var(--dashboard-dark-blue))",
                        "hsl(var(--dashboard-medium-blue))",
                        "hsl(var(--dashboard-light-blue))",
                        "#10b981",
                        "#f59e0b",
                        "#ef4444",
                        "#8b5cf6",
                        "#06b6d4",
                        "#84cc16",
                        "#f97316",
                      ];
                      return (
                        <Cell
                          key={`cell-${index}`}
                          fill={colors[index % colors.length]}
                        />
                      );
                    }
                  )}
                </Pie>
                <Tooltip />
              </PieChart>
              ) : (
                <div className="flex justify-center items-center h-full">
                  <p className="text-muted-foreground text-sm">No Emerging Categories Data Available</p>
                </div>
              )}
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Other Entries Analysis Table */}
      <Card className="bg-tertiary border-border">
        <CardHeader>
          <CardTitle className="text-foreground flex items-center gap-2">
            <Search className="h-5 w-5" />
            "Other" Entries Analysis
          </CardTitle>
          <p className="text-sm text-muted-foreground">
            Speaker taxonomy entries marked as "Other" - opportunities for
            category expansion
          </p>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="text-foreground">
                  Question Field
                </TableHead>
                <TableHead className="text-foreground">Other Entry</TableHead>
                <TableHead className="text-foreground">Frequency</TableHead>
                <TableHead className="text-foreground">Recent Users</TableHead>
                <TableHead className="text-foreground">Status</TableHead>
                <TableHead className="text-foreground">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {categoriesState?.data?.otherEntries &&
              categoriesState.data.otherEntries.length > 0 ? (
                // Real data rows
                categoriesState.data.otherEntries.map(
                  (entry: any, i: number) => (
                    <TableRow key={i}>
                      <TableCell className="font-medium text-foreground">
                        {entry.questionField}
                      </TableCell>
                      <TableCell className="text-foreground font-medium">
                        {entry.otherEntry}
                      </TableCell>
                      <TableCell className="text-center">
                        <Badge
                          variant="outline"
                          className="bg-dashboard-light-blue/10"
                        >
                          {entry.frequency}x
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <p className="text-sm text-muted-foreground">
                          {entry.recentUsers}
                        </p>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            entry.status === "approved"
                              ? "default"
                              : entry.status === "reviewed"
                              ? "secondary"
                              : "outline"
                          }
                          className="text-xs"
                        >
                          {entry.status}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Button
                          size="sm"
                          variant="outline"
                          className="h-7 px-2 text-xs"
                          title="Promote to Category"
                          onClick={() => handlePromoteToCategory(entry)}
                        >
                          <ArrowRight className="h-3 w-3" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  )
                )
              ) : (
                // Empty state
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-12">
                    <div className="flex flex-col items-center space-y-2">
                      <Search className="h-8 w-8 text-muted-foreground" />
                      <p className="text-muted-foreground font-medium">
                        No "Other" entries found
                      </p>
                      <p className="text-sm text-muted-foreground">
                        All category entries have been properly classified
                      </p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
        </>
      )}
    </div>
  );
};

export default CategoryExpansionAnalytics;
