import React, { useState, useEffect, useRef } from 'react';

interface DogAnimationProps {
  isTyping?: boolean;
  fieldCompleted?: boolean;
  activeFieldPosition?: { x: number, y: number };
}

export function DogAnimation({ isTyping, fieldCompleted, activeFieldPosition }: DogAnimationProps) {
  const [position, setPosition] = useState({ x: 100, y: 100 });
  const [isSniffing, setIsSniffing] = useState(false);
  const [isJumping, setIsJumping] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const dogRef = useRef<HTMLDivElement>(null);
  const runningIntervalRef = useRef<NodeJS.Timeout>();

  // Random running around animation
  useEffect(() => {
    const startRunning = () => {
      if (!isTyping && !fieldCompleted) {
        const newX = Math.random() * (window.innerWidth - 100);
        const newY = Math.random() * (window.innerHeight - 100) + 50;
        
        setIsRunning(true);
        setPosition({ x: newX, y: newY });
        
        setTimeout(() => setIsRunning(false), 1000);
      }
    };

    runningIntervalRef.current = setInterval(startRunning, 5000);
    return () => {
      if (runningIntervalRef.current) {
        clearInterval(runningIntervalRef.current);
      }
    };
  }, [isTyping, fieldCompleted]);

  // Move to field when typing
  useEffect(() => {
    if (isTyping && activeFieldPosition) {
      setIsRunning(true);
      setPosition({ 
        x: activeFieldPosition.x - 60, 
        y: activeFieldPosition.y - 20 
      });
      
      setTimeout(() => {
        setIsRunning(false);
        setIsSniffing(true);
        setTimeout(() => setIsSniffing(false), 2000);
      }, 800);
    }
  }, [isTyping, activeFieldPosition]);

  // Jump when field completed
  useEffect(() => {
    if (fieldCompleted) {
      setIsJumping(true);
      setTimeout(() => setIsJumping(false), 1000);
    }
  }, [fieldCompleted]);

  const DogSVG = () => (
    <svg width="60" height="60" viewBox="0 0 100 100" className="drop-shadow-lg">
      {/* Dog body */}
      <ellipse cx="50" cy="70" rx="25" ry="20" fill="#6B9FFF" />
      
      {/* Dog head */}
      <ellipse cx="50" cy="40" rx="20" ry="18" fill="#6B9FFF" />
      
      {/* Ears */}
      <ellipse cx="35" cy="30" rx="8" ry="12" fill="#5B8FEF" />
      <ellipse cx="65" cy="30" rx="8" ry="12" fill="#5B8FEF" />
      
      {/* Eyes */}
      <circle cx="43" cy="38" r="3" fill="#000" />
      <circle cx="57" cy="38" r="3" fill="#000" />
      <circle cx="44" cy="37" r="1" fill="#fff" />
      <circle cx="58" cy="37" r="1" fill="#fff" />
      
      {/* Nose */}
      <ellipse cx="50" cy="45" rx="2" ry="1.5" fill="#000" />
      
      {/* Mouth */}
      <path d="M50 47 Q47 50 44 49 Q47 52 50 51 Q53 52 56 49 Q53 50 50 47" fill="#000" stroke="none" />
      
      {/* Tail */}
      <ellipse cx="75" cy="65" rx="6" ry="15" fill="#6B9FFF" transform="rotate(30 75 65)" />
      
      {/* Legs */}
      <ellipse cx="40" cy="85" rx="4" ry="8" fill="#5B8FEF" />
      <ellipse cx="50" cy="85" rx="4" ry="8" fill="#5B8FEF" />
      <ellipse cx="60" cy="85" rx="4" ry="8" fill="#5B8FEF" />
    </svg>
  );

  return (
    <div
      ref={dogRef}
      className={`
        fixed pointer-events-none z-50 transition-all duration-1000 ease-in-out
        ${isRunning ? 'animate-bounce' : ''}
        ${isSniffing ? 'animate-pulse' : ''}
        ${isJumping ? 'animate-jump' : ''}
      `}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
        transform: `
          ${isJumping ? 'translateY(-20px) scale(1.1)' : 'translateY(0) scale(1)'}
          ${isSniffing ? 'rotateZ(-10deg)' : 'rotateZ(0deg)'}
        `,
      }}
    >
      <div className={`
        transition-transform duration-300
        ${isSniffing ? 'animate-wiggle' : ''}
      `}>
        <DogSVG />
      </div>
      
      {/* Sniffing particles */}
      {isSniffing && (
        <div className="absolute -top-2 left-8">
          <div className="animate-ping w-1 h-1 bg-primary rounded-full opacity-75"></div>
          <div className="animate-ping w-1 h-1 bg-primary rounded-full opacity-50 animation-delay-75"></div>
          <div className="animate-ping w-1 h-1 bg-primary rounded-full opacity-25 animation-delay-150"></div>
        </div>
      )}
      
      {/* Celebration hearts when jumping */}
      {isJumping && (
        <div className="absolute -top-8 left-1/2 transform -translate-x-1/2">
          <div className="animate-bounce text-red-500 text-sm">❤️</div>
          <div className="animate-bounce text-red-400 text-xs animation-delay-100 absolute -left-3">❤️</div>
          <div className="animate-bounce text-red-400 text-xs animation-delay-200 absolute -right-3">❤️</div>
        </div>
      )}
    </div>
  );
}