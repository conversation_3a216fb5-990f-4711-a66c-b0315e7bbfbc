from sqlalchemy import Column, Integer, String, Boolean, Date, Text, DateTime, Enum
from sqlalchemy.ext.declarative import declarative_base
from app.config.config import config
from sqlalchemy import create_engine
import enum

Base = declarative_base()

class ScrapingStatus(enum.Enum):
    SUCCESS = "success"
    ERROR = "error"
    RUNNING = "running"
    PAUSED = "paused"

class ScrapingLogging(Base):
    __tablename__ = 'scraping_logging'
    id = Column(Integer, primary_key=True, autoincrement=True, comment='Primary key for the scraping logging')
    search_engine = Column(String(255), nullable=True, comment='Search engine of the scraping logging')
    topic = Column(String(255), nullable=False, comment='Topic of the scraping logging')
    item_count = Column(Integer, nullable=False, comment='Item count of the scraping logging')
    status = Column(Enum(ScrapingStatus), nullable=False, comment='Status of the scraping logging')
    reason = Column(Text, nullable=True, comment='Message of the scraping logging')
    started_at = Column(DateTime, nullable=False, comment='Start time of the scraping logging')
    ended_at = Column(DateTime, nullable=False, comment='End time of the scraping logging')
    created_at = Column(DateTime, nullable=False, comment='Record creation timestamp')
    updated_at = Column(DateTime, nullable=False, comment='Record last update timestamp')

def create_scraping_tables():
    db_url = config.get_database_url()
    engine = create_engine(db_url)
    Base.metadata.create_all(engine)

create_scraping_table = create_scraping_tables

if __name__ == "__main__":
    create_scraping_tables()
    print("Tables 'scraping_topic' and 'scraping_logging' created in MySQL database")
