
const roleController = require("../controllers/role-controller");
const router = require("express").Router();
const checkPermission = require("../middlewares/permission-middleware");

module.exports = (app) => {

    // ------------------------- role -------------------------

    // create new role
    router.post("/createRole", checkPermission(["create_role"]), roleController.createRole);

    // update role by id
    router.put("/updateRole/:id", checkPermission(["create_role"]), roleController.updateRole);

    // delete role
    router.delete("/deleteRole", checkPermission(["create_role"]), roleController.deleteRole);

    // get all roles
    router.get("/getRoles", roleController.getRoles);


    router.get("/getPermissions", roleController.getPermissions);

    app.use("/admin/api/v1", router);

};