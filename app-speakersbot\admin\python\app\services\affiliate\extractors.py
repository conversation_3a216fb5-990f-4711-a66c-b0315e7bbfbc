"""
Affiliate Extractors
Specialized data extraction functions for affiliate scraping
"""

import re
import json
import logging
import urllib.parse
from datetime import datetime
from typing import List, Dict, Any, Optional
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from app.config.logger import get_logger
logger = get_logger(__name__, file_name="scraper.log")

from .constants import (
    LEARNING_OBJECTIVE_PATTERNS, CHALLENGE_PATTERNS, NON_LOGO_KEYWORDS,
    ENTITY_PATTERNS, REACH_PATTERNS, CHANNEL_PATTERNS, TAX_PATTERNS,TERMS_PATTERNS,SIGNATURE_PATTERNS
)
from .affiliate_llm import AffiliateLLMExtractor


class AffiliateExtractors:
    """Specialized data extraction functions for affiliate scraping"""
    
    def __init__(self):
        """Initialize extractors"""
        self.llm_extractor = AffiliateLLMExtractor()
    
    def safe_extract(self, extract_func, *args, **kwargs) -> str:
        try:
            result = extract_func(*args, **kwargs)
            return result
        except Exception as e:
            logger.warning(f"Error in {extract_func.__name__}: {e}")
            return f"Error extracting {extract_func.__name__}"
    
    def extract_logo(self, soup: BeautifulSoup, base_url: str) -> str:
        """Extract logo URL from the webpage using multiple detection strategies"""
        try:
            def decode_optimized_image_url(url: str) -> str:
                """Decode optimized image URLs (Next.js, Shopify, etc.) to get original source"""
                try:
                    # Handle Next.js image optimization URLs
                    if '_next/image' in url and 'url=' in url:
                        import urllib.parse
                        # Extract the encoded URL parameter
                        parsed = urllib.parse.urlparse(url)
                        query_params = urllib.parse.parse_qs(parsed.query)
                        if 'url' in query_params:
                            original_url = urllib.parse.unquote(query_params['url'][0])
                            logger.info(f"🔄 Decoded Next.js image URL: {url} -> {original_url}")
                            return original_url

                    # Handle other image optimization services
                    # Cloudinary: res.cloudinary.com/[cloud]/image/fetch/[params]/[encoded_url]
                    if 'cloudinary.com' in url and '/fetch/' in url:
                        parts = url.split('/fetch/')
                        if len(parts) > 1:
                            # Extract URL after fetch parameters
                            fetch_part = parts[1]
                            # Remove optimization parameters and get original URL
                            if '/' in fetch_part:
                                original_url = '/'.join(fetch_part.split('/')[1:])  # Skip first param
                                if original_url.startswith('http'):
                                    logger.info(f"🔄 Decoded Cloudinary URL: {url} -> {original_url}")
                                    return original_url

                    # Return original URL if no optimization detected
                    return url
                except Exception as e:
                    logger.warning(f"Error decoding optimized image URL {url}: {e}")
                    return url

            def resolve_img_url(el) -> Optional[str]:
                # Prefer largest srcset candidate
                srcset = el.get('srcset') or el.get('data-srcset')
                if srcset:
                    candidates = []
                    for part in srcset.split(','):
                        url_part = part.strip().split(' ')
                        if not url_part:
                            continue
                        candidate_url = urljoin(base_url, url_part[0])
                        # Decode optimized URLs
                        candidate_url = decode_optimized_image_url(candidate_url)
                        width = 0
                        if len(url_part) > 1 and url_part[1].endswith('w'):
                            try:
                                width = int(url_part[1][:-1])
                            except Exception:
                                width = 0
                        candidates.append((width, candidate_url))
                    if candidates:
                        candidates.sort(key=lambda c: c[0], reverse=True)
                        return candidates[0][1]
                # Fallback attributes
                for attr in ['src', 'data-src', 'data-original', 'data-lazy-src', 'data-src-retina']:
                    val = el.get(attr)
                    if val:
                        resolved_url = urljoin(base_url, val)
                        # Decode optimized URLs
                        return decode_optimized_image_url(resolved_url)
                return None

            def is_probable_logo(url: str, alt_text: str, class_text: str, parent_class: str = '') -> bool:
                lower_url = url.lower()
                lower_alt = alt_text.lower()
                lower_class = class_text.lower()
                lower_parent = parent_class.lower()
                
                # Exclude common non-logo assets
                if any(token in lower_url or token in lower_alt or token in lower_class or token in lower_parent for token in NON_LOGO_KEYWORDS):
                    return False
                
                # Strong logo indicators
                logo_indicators = ['logo', 'brand', 'brandmark', 'wordmark', 'site-logo', 'navbar-brand', 'header-logo']
                if any(word in lower_alt or word in lower_class or word in lower_parent for word in logo_indicators):
                    return True
                
                # File name hints
                if any(word in lower_url for word in ['logo', 'brandmark', 'wordmark', 'brand']):
                    return True
                
                # Additional heuristics for better detection
                # If it's in a header/nav area and not explicitly excluded, it's likely a logo
                if any(word in lower_parent for word in ['header', 'nav', 'navbar', 'brand', 'logo']) and not any(word in lower_url for word in ['icon', 'button', 'menu']):
                    return True
                
                return False

            # 1) Explicit logo containers (enhanced selectors)
            enhanced_logo_selectors = [
                '[class*="logo" i]', '[id*="logo" i]', '.navbar-brand', '.site-logo', 
                'header .brand', 'header [class*="brand" i]', '.header-logo', '.main-logo',
                '.brand-logo', '.company-logo', '.site-brand', '.header-brand',
                '[class*="brand" i]', '[class*="header" i] img', '.top-logo'
            ]
            
            logo_containers = soup.select(', '.join(enhanced_logo_selectors))
            
            for container in logo_containers:
                img = container.find('img') or container.find('source')
                if img:
                    url_candidate = resolve_img_url(img)
                    if url_candidate:
                        alt_text = (img.get('alt') or '').lower()
                        class_text = ' '.join(img.get('class', [])).lower()
                        parent_class = ' '.join(container.get('class', [])).lower()
                        if is_probable_logo(url_candidate, alt_text, class_text, parent_class):
                            return url_candidate

            # 2) Header images as fallback (enhanced)
            header_selectors = [
                'header img', 'nav img', '.navbar img', '.navigation img',
                '.header img', '.top-bar img', '.site-header img', '.main-header img',
                '.page-header img', '.banner img', '.masthead img'
            ]
            header_images = soup.select(', '.join(header_selectors))
            
            for img in header_images:
                url_candidate = resolve_img_url(img)
                if not url_candidate:
                    continue
                alt_text = (img.get('alt') or '').lower()
                class_text = ' '.join(img.get('class', [])).lower()
                if is_probable_logo(url_candidate, alt_text, class_text):
                    return url_candidate

            # 3) SVG-based logos (enhanced)
            svg_selectors = [
                'svg[id*="logo" i]', 'svg[class*="logo" i]', '.logo svg', '.navbar-brand svg',
                'header svg', '.header svg', '.brand svg', '[class*="brand" i] svg'
            ]
            svg_logo = soup.select_one(', '.join(svg_selectors))
            if svg_logo:
                # If the SVG references an external file via <use xlink:href>
                use_el = svg_logo.find('use')
                href = None
                if use_el:
                    href = use_el.get('xlink:href') or use_el.get('href')
                if href and not href.startswith('#'):
                    return urljoin(base_url, href)

            # 4) Link icons (favicon, apple-touch-icon) as last-resort brand mark
            link_icons = soup.find_all('link', rel=re.compile(r'(?:icon|shortcut icon|apple-touch-icon|mask-icon)', re.I))
            
            best_icon = None
            best_size = 0
            for link in link_icons:
                href = link.get('href')
                if not href:
                    continue
                sizes = link.get('sizes', '')
                size_val = 0
                if sizes:
                    # sizes like "180x180"
                    m = re.match(r'(\d+)[xX](\d+)', sizes)
                    if m:
                        try:
                            size_val = int(m.group(1)) * int(m.group(2))
                        except Exception:
                            size_val = 0
                url_candidate = urljoin(base_url, href)
                if size_val > best_size:
                    best_icon = url_candidate
                    best_size = size_val
            if best_icon:
                return best_icon

            # 5) Meta og:logo or brand-specific og:image hints
            og_logo = soup.find('meta', property=re.compile(r'og:(?:image:logo|logo)', re.I))
            if og_logo and og_logo.get('content'):
                return urljoin(base_url, og_logo['content'])

            # 6) JSON-LD structured data logos
            json_scripts = soup.find_all('script', type='application/ld+json')
            for script in json_scripts:
                try:
                    data = json.loads(script.string or '{}')
                    if isinstance(data, dict) and 'logo' in data:
                        logo_url = data['logo']
                        if isinstance(logo_url, str):
                            return urljoin(base_url, logo_url)
                except:
                    continue

            # 7) Generic: scan all images and pick the most logo-like
            candidates = []
            all_images = soup.find_all('img')
            
            for img in all_images:
                url_candidate = resolve_img_url(img)
                if not url_candidate:
                    continue
                alt_text = (img.get('alt') or '').lower()
                class_text = ' '.join(img.get('class', [])).lower()
                
                # Get parent element classes for better detection
                parent_classes = ''
                if img.parent:
                    parent_classes = ' '.join(img.parent.get('class', [])).lower()
                
                if is_probable_logo(url_candidate, alt_text, class_text, parent_classes):
                    candidates.append(url_candidate)
            
            if candidates:
                return candidates[0]

            return "No logo found"

        except Exception as e:
            logger.warning(f"Error extracting logo: {e}")
            logger.warning(f"Error type: {type(e).__name__}")
            import traceback
            logger.warning(f"Traceback: {traceback.format_exc()}")
            return "Error extracting logo"
    
    def extract_product_images(self, soup: BeautifulSoup, base_url: str) -> str:
        """Extract up to 2 product or service images with modern patterns support"""
        try:
            def resolve_img(el) -> Optional[str]:
                srcset = el.get('srcset') or el.get('data-srcset')
                if srcset:
                    candidates = []
                    for part in srcset.split(','):
                        url_part = part.strip().split(' ')
                        if not url_part:
                            continue
                        candidate_url = urljoin(base_url, url_part[0])
                        width = 0
                        if len(url_part) > 1 and url_part[1].endswith('w'):
                            try:
                                width = int(url_part[1][:-1])
                            except Exception:
                                width = 0
                        candidates.append((width, candidate_url))
                    if candidates:
                        candidates.sort(key=lambda c: c[0], reverse=True)
                        return candidates[0][1]
                for attr in ['src', 'data-src', 'data-original', 'data-lazy-src', 'data-src-retina', 'data-zoom-src']:
                    val = el.get(attr)
                    if val:
                        return urljoin(base_url, val)
                return None
            product_images: List[str] = []
            def add_candidate(url: Optional[str]):
                if not url:
                    return
                lower = url.lower()
                # Enhanced image extensions
                if not any(ext in lower for ext in ['.png', '.jpg', '.jpeg', '.webp', '.gif', '.svg', '.avif']):
                    return
                # Exclude common non-product images (enhanced filtering)
                exclude_keywords = [
                    'logo', 'icon', 'avatar', 'profile', 'banner', 'header', 'footer', 'background',
                    'person', 'people', 'team', 'staff', 'member', 'founder', 'ceo', 'director',
                    'kimberly', 'gini', 'speaker', 'presenter', 'host', 'facilitator',
                    'testimonial', 'review', 'quote', 'social', 'facebook', 'twitter', 'instagram',
                    'decoration', 'ornament', 'pattern', 'texture', 'gradient'
                ]
                if any(keyword in lower for keyword in exclude_keywords):
                    return
                if url not in product_images:
                    product_images.append(url)

            # 1) Open Graph / Twitter cards (enhanced)
            enhanced_meta_tags = [
                'og:image', 'og:image:secure_url', 'twitter:image', 'twitter:image:src',
                'og:image:url', 'twitter:image:url', 'og:image:alt', 'twitter:image:alt'
            ]
            for meta_name in enhanced_meta_tags:
                meta = soup.find('meta', property=meta_name) or soup.find('meta', attrs={'name': meta_name})
                if meta and meta.get('content'):
                    add_candidate(urljoin(base_url, meta['content']))
                if len(product_images) >= 2:
                    return "; ".join(product_images[:2])

            # 2) JSON-LD Product schema (enhanced)
            json_scripts = soup.find_all('script', type='application/ld+json')
            
            for script in json_scripts:
                try:
                    data = json.loads(script.string or '{}')
                except Exception:
                    continue
                def extract_images(obj):
                    if isinstance(obj, dict):
                        # Check for product/service/offer types
                        schema_types = ['Product', 'Offer', 'ItemList', 'Service', 'Organization', 'WebPage']
                        if obj.get('@type') in schema_types and 'image' in obj:
                            imgs = obj['image']
                            if isinstance(imgs, list):
                                for u in imgs:
                                    if isinstance(u, str):
                                        add_candidate(urljoin(base_url, u))
                                    elif isinstance(u, dict) and 'url' in u:
                                        add_candidate(urljoin(base_url, u['url']))
                            elif isinstance(imgs, str):
                                add_candidate(urljoin(base_url, imgs))
                        # Check for gallery/album types
                        if obj.get('@type') in ['ImageGallery', 'MediaGallery'] and 'associatedMedia' in obj:
                            media = obj['associatedMedia']
                            if isinstance(media, list):
                                for m in media:
                                    if isinstance(m, dict) and 'contentUrl' in m:
                                        add_candidate(urljoin(base_url, m['contentUrl']))
                        for v in obj.values():
                            extract_images(v)
                    elif isinstance(obj, list):
                        for it in obj:
                            extract_images(it)
                extract_images(data)
                if len(product_images) >= 2:
                    return "; ".join(product_images[:2])

            # 3) Enhanced Product/Gallery sections
            enhanced_product_selectors = [
                '.product img', '.product-picture img', '.product-card img', '.product-media img',
                '.service img', '.gallery img', '.carousel img', '.slider img', '.collection img',
                '[class*="product" i] img', '[data-testid*="product" i] img',
                '.hero img', '.banner img', '.featured img', '.showcase img',
                '.portfolio img', '.work img', '.project img', '.case-study img',
                '.testimonial img', '.client img', '.partner img', '.team img',
                '.about img', '.company img', '.business img', '.service img',
                '.main-content img', '.content-area img', '.page-content img',
                '.article img', '.post img', '.blog img', '.news img'
            ]
            
            for selector in enhanced_product_selectors:
                images = soup.select(selector)
                logger.info(f"📸 Found {len(images)} images with selector: {selector}")
                for img in images:
                    add_candidate(resolve_img(img))
                    if len(product_images) >= 2:
                        return "; ".join(product_images[:2])

            # 4) Enhanced main content images
            main_content_selectors = [
                'main img', '.main img', '.content img', 'article img', 'section img',
                '.container img', '.wrapper img', '.inner img', '.body img',
                '.page img', '.site img', '.primary img', '.secondary img'
            ]
            
            for selector in main_content_selectors:
                images = soup.select(selector)
                for img in images:
                    add_candidate(resolve_img(img))
                    if len(product_images) >= 2:
                        break
                if len(product_images) >= 2:
                    break

            # 5) Fallback: All images with strict filtering
            if len(product_images) < 2:
                all_images = soup.find_all('img')
                for img in all_images:
                    url_candidate = resolve_img(img)
                    if url_candidate:
                        # Additional filtering for better quality
                        alt_text = (img.get('alt') or '').lower()
                        class_text = ' '.join(img.get('class', [])).lower()
                        
                        # Skip if it's clearly not a product image (enhanced filtering)
                        skip_keywords = [
                            'logo', 'icon', 'avatar', 'profile', 'banner', 'header', 'footer', 'background', 'decoration',
                            'person', 'people', 'team', 'staff', 'member', 'founder', 'ceo', 'director',
                            'kimberly', 'gini', 'speaker', 'presenter', 'host', 'facilitator',
                            'testimonial', 'review', 'quote', 'social', 'facebook', 'twitter', 'instagram'
                        ]
                        if any(keyword in alt_text or keyword in class_text for keyword in skip_keywords):
                            continue
                            
                        add_candidate(url_candidate)
                        if len(product_images) >= 2:
                            break

            return "; ".join(product_images[:2]) if product_images else "No product images found"

        except Exception as e:
            logger.warning(f"Error extracting product images: {e}")
            logger.warning(f"Error type: {type(e).__name__}")
            import traceback
            logger.warning(f"Traceback: {traceback.format_exc()}")
            return "Error extracting product images"
    
    def extract_color_codes(self, soup: BeautifulSoup, base_url: str = "") -> str:
        """Extract color codes using optimized approach"""
        try:
            colors = []
            
            # Method 1: Extract from CSS files (most reliable)
            css_links = soup.find_all('link', rel='stylesheet', href=True)
            for link in css_links[:2]:  # Limit to 2 files
                try:
                    import requests
                    css_url = urljoin(base_url, link['href'])
                    response = requests.get(css_url, timeout=5)
                    if response.status_code == 200:
                        hex_colors = re.findall(r'#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})', response.text)
                        colors.extend([f"#{color}" for color in hex_colors])
                except:
                    continue
            
            # Method 2: Extract from HTML content
            if not colors:
                html_content = str(soup)
                hex_colors = re.findall(r'#([0-9a-fA-F]{6}|[0-9a-fA-F]{3})', html_content)
                colors.extend([f"#{color}" for color in hex_colors])
            
            # Method 3: Brand colors fallback
            if not colors and base_url:
                domain = urllib.parse.urlparse(base_url).netloc
                brand_colors = {
                    'facebook.com': ['#1877F2', '#42A5F5', '#1E88E5'],
                    'twitter.com': ['#1DA1F2', '#0D8BD9', '#0A7BC7'],
                    'instagram.com': ['#E4405F', '#F77737', '#FCAF45'],
                    'linkedin.com': ['#0077B5', '#005885', '#004471'],
                    'youtube.com': ['#FF0000', '#CC0000', '#990000']
                }
                if domain in brand_colors:
                    colors = brand_colors[domain]
            
            # Method 4: Default colors
            if not colors:
                return "Primary: #007BFF, Secondary: #6C757D, Accent: #28A745"
            
            # Format results
            unique_colors = list(dict.fromkeys(colors))[:3]
            if len(unique_colors) >= 3:
                return f"Primary: {unique_colors[0]}, Secondary: {unique_colors[1]}, Accent: {unique_colors[2]}"
            elif len(unique_colors) >= 2:
                return f"Primary: {unique_colors[0]}, Secondary: {unique_colors[1]}"
            else:
                return f"Primary: {unique_colors[0]}"
                
        except Exception as e:
            logger.warning(f"Error extracting color codes: {e}")
            return "Primary: #007BFF, Secondary: #6C757D, Accent: #28A745"
    
    def extract_font_stack(self, soup: BeautifulSoup) -> str:
        """Extract font stack information"""
        try:
            fonts = []
            
            # 1. Look for font-family in style tags
            style_tags = soup.find_all('style')
            for style in style_tags:
                if style.string:
                    # Look for font-family declarations
                    font_matches = re.findall(r'font-family:\s*([^;]+)', style.string, re.IGNORECASE)
                    fonts.extend(font_matches)
            
                    # Look for @font-face declarations
                    font_face_matches = re.findall(r'@font-face\s*{[^}]*font-family:\s*([^;]+)', style.string, re.IGNORECASE | re.DOTALL)
                    fonts.extend(font_face_matches)
            
            # 2. Look for inline styles
            elements_with_style = soup.find_all(attrs={"style": True})
            for element in elements_with_style:
                style = element['style']
                font_matches = re.findall(r'font-family:\s*([^;]+)', style, re.IGNORECASE)
                fonts.extend(font_matches)
            
            # 3. Look for Google Fonts links
            google_font_links = soup.find_all('link', href=lambda x: x and 'fonts.googleapis.com' in x)
            for link in google_font_links:
                href = link.get('href', '')
                # Extract font names from Google Fonts URL
                font_names = re.findall(r'family=([^&]+)', href)
                for font_name in font_names:
                    # Decode URL encoding and clean up
                    font_name = urllib.parse.unquote(font_name)
                    font_name = font_name.replace('+', ' ')
                    # Split by colon to get font name only (remove weights)
                    if ':' in font_name:
                        font_name = font_name.split(':')[0]
                    fonts.append(font_name)
            
            # 4. Look for font imports in CSS
            for style in style_tags:
                if style.string:
                    import_matches = re.findall(r'@import\s+url\([^)]*fonts\.googleapis\.com[^)]*\)', style.string, re.IGNORECASE)
                    for import_match in import_matches:
                        font_names = re.findall(r'family=([^&]+)', import_match)
                        for font_name in font_names:
                            font_name = urllib.parse.unquote(font_name)
                            font_name = font_name.replace('+', ' ')
                            if ':' in font_name:
                                font_name = font_name.split(':')[0]
                            fonts.append(font_name)
            
            # 5. Look for web font files
            font_files = soup.find_all(['link', 'style'], href=lambda x: x and any(ext in x.lower() for ext in ['.woff', '.woff2', '.ttf', '.otf', '.eot']))
            for font_file in font_files:
                href = font_file.get('href', '')
                # Extract font name from filename
                font_name = re.search(r'/([^/]+)\.(woff|woff2|ttf|otf|eot)', href, re.IGNORECASE)
                if font_name:
                    fonts.append(font_name.group(1))
            
            # Clean up font names
            cleaned_fonts = []
            for font in fonts:
                # Remove quotes and clean up
                font = font.strip().strip('"\'')
                # Split by comma and take first font (main font)
                if ',' in font:
                    font = font.split(',')[0].strip()
                # Remove generic font families
                if font.lower() not in ['serif', 'sans-serif', 'monospace', 'cursive', 'fantasy', 'inherit', 'initial', 'unset']:
                    if font and font not in cleaned_fonts:
                        cleaned_fonts.append(font)
            
            return "; ".join(cleaned_fonts[:3]) if cleaned_fonts else "No font stack found"
            
        except Exception as e:
            logger.warning(f"Error extracting font stack: {e}")
            return "Error extracting font stack"
    
    def extract_learning_objectives(self, soup: BeautifulSoup) -> str:
        """Extract learning objectives or takeaways from webpage content"""
        try:
            objectives = []
            # Extract text content for pattern matching
            text_content = soup.get_text()
            # Search for patterns and extract objectives
            for pattern in LEARNING_OBJECTIVE_PATTERNS:
                matches = re.findall(pattern, text_content, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    objective = match.strip()
                    # Clean up extra whitespace and newlines
                    objective = re.sub(r'\s+', ' ', objective).strip()
                    # Validate objective quality and avoid duplicates
                    if objective and len(objective) > 10 and objective not in objectives:
                        objectives.append(objective)
                        if len(objectives) >= 3:
                            break
                if len(objectives) >= 3:
                    break
            
            return "; ".join(objectives) if objectives else "No learning objectives found"
            
        except Exception as e:
            logger.warning(f"Error extracting learning objectives: {e}")
            return "Error extracting learning objectives"
    
    def extract_challenges_solved(self, soup: BeautifulSoup) -> str:
        """Extract 3 challenges solved"""
        try:
            challenges = []
            text_content = soup.get_text()
            for pattern in CHALLENGE_PATTERNS:
                matches = re.findall(pattern, text_content, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    challenge = match.strip()
                    # Clean up extra whitespace and newlines
                    challenge = re.sub(r'\s+', ' ', challenge).strip()
                    if challenge and len(challenge) > 10 and challenge not in challenges:
                        challenges.append(challenge)
                        if len(challenges) >= 3:
                            break
                if len(challenges) >= 3:
                    break
            return "; ".join(challenges) if challenges else "No challenges solved found"
            
        except Exception as e:
            logger.warning(f"Error extracting challenges solved: {e}")
            return "Error extracting challenges solved"
    
    def extract_overarching_offer(self, soup: BeautifulSoup) -> str:
        """Extract overarching offer (one paragraph)"""
        try:
            # Look for offer/value proposition patterns - more flexible
            offer_patterns = [
                r'offer[s]?\s*:?\s*([^.!?]*[.!?])',
                r'value\s+proposition[^.!?]*[.!?]',
                r'what\s+we\s+offer[^.!?]*[.!?]',
                r'our\s+offer[^.!?]*[.!?]',
                r'special\s+offer[^.!?]*[.!?]',
                # More flexible patterns
                r'what\s+you\s+get[^.!?]*[.!?]',
                r'benefits?\s+(?:include|are)[^.!?]*[.!?]',
                r'we\s+provide[^.!?]*[.!?]',
                r'our\s+service[s]?[^.!?]*[.!?]',
                r'we\s+help[^.!?]*[.!?]',
                r'we\s+deliver[^.!?]*[.!?]',
                r'our\s+solution[s]?[^.!?]*[.!?]',
                r'what\s+makes\s+us[^.!?]*[.!?]',
                r'our\s+mission[^.!?]*[.!?]',
                r'we\s+specialize[^.!?]*[.!?]'
            ]
            
            text_content = soup.get_text()
            
            for pattern in offer_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    offer = match.strip()
                    # Clean up extra whitespace and newlines
                    offer = re.sub(r'\s+', ' ', offer).strip()
                    if offer and len(offer) > 20:
                        return offer
            
            # Fallback: look for paragraphs with offer-related keywords
            paragraphs = soup.find_all(['p', 'div', 'section'])
            for p in paragraphs:
                text = p.get_text().strip()
                # Clean up extra whitespace and newlines
                text = re.sub(r'\s+', ' ', text).strip()
                if any(keyword in text.lower() for keyword in ['offer', 'value', 'benefit', 'solution', 'service', 'provide', 'help', 'deliver', 'mission', 'specialize']):
                    if len(text) > 20 and len(text) < 500:  # Reasonable paragraph length
                        return text
            
            return "No overarching offer found"
            
        except Exception as e:
            logger.warning(f"Error extracting overarching offer: {e}")
            return "Error extracting overarching offer"
    
    def extract_differentiator(self, soup: BeautifulSoup) -> str:
        """Extract differentiator (one paragraph)"""
        try:
            # Look for differentiator patterns
            diff_patterns = [
                r'different[^.!?]*[.!?]',
                r'unique[^.!?]*[.!?]',
                r'stand\s+out[^.!?]*[.!?]',
                r'what\s+makes\s+us\s+different[^.!?]*[.!?]',
                r'why\s+choose\s+us[^.!?]*[.!?]',
                r'competitive\s+advantage[^.!?]*[.!?]'
            ]
            
            text_content = soup.get_text()
            
            for pattern in diff_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    diff = match.strip()
                    # Clean up extra whitespace and newlines
                    diff = re.sub(r'\s+', ' ', diff).strip()
                    if diff and len(diff) > 20:
                        return diff
            
            # Fallback: look for paragraphs with differentiator keywords
            paragraphs = soup.find_all(['p', 'div', 'section'])
            for p in paragraphs:
                text = p.get_text().strip()
                # Clean up extra whitespace and newlines
                text = re.sub(r'\s+', ' ', text).strip()
                if any(keyword in text.lower() for keyword in ['different', 'unique', 'exclusive', 'premium', 'superior', 'better', 'only', 'first', 'innovative', 'revolutionary', 'stand out', 'competitive advantage']):
                    if len(text) > 20 and len(text) < 500:  # Reasonable paragraph length
                        return text
            
            return "No differentiator found"
            
        except Exception as e:
            logger.warning(f"Error extracting differentiator: {e}")
            return "Error extracting differentiator"
    
    def extract_core_benefit(self, soup: BeautifulSoup) -> str:
        """Extract core benefit (one sentence)"""
        try:
            # Look for benefit patterns
            benefit_patterns = [
                r'benefit[s]?\s*:?\s*([^.!?]*[.!?])',
                r'advantage[s]?\s*:?\s*([^.!?]*[.!?])',
                r'why\s+[^.!?]*[.!?]',
                r'help[s]?\s+you[^.!?]*[.!?]',
                r'get[^.!?]*[.!?]'
            ]
            
            text_content = soup.get_text()
            
            for pattern in benefit_patterns:
                matches = re.findall(pattern, text_content, re.IGNORECASE | re.MULTILINE)
                for match in matches:
                    if isinstance(match, tuple):
                        match = match[0]
                    benefit = match.strip()
                    if benefit and len(benefit) > 10 and len(benefit) < 200:
                        return benefit
            
            return "No core benefit found"
            
        except Exception as e:
            logger.warning(f"Error extracting core benefit: {e}")
            return "Error extracting core benefit"
    
    def extract_business_entity_type(self, soup: BeautifulSoup) -> str:
        """Extract business entity type"""
        try:
            text_content = soup.get_text()
            
            for pattern in ENTITY_PATTERNS:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                if matches:
                    return matches[0]
            
            return "Business entity type not specified"
            
        except Exception as e:
            logger.warning(f"Error extracting business entity type: {e}")
            return "Error extracting business entity type"
    
    def extract_reach_estimate(self, soup: BeautifulSoup) -> str:
        """Extract reach estimate"""
        try:
            text_content = soup.get_text()
            
            for pattern in REACH_PATTERNS:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                if matches:
                    return matches[0] if isinstance(matches[0], str) else matches[0][0]
            
            return "Reach estimate not specified"
            
        except Exception as e:
            logger.warning(f"Error extracting reach estimate: {e}")
            return "Error extracting reach estimate"
    
    def extract_promo_channels(self, soup: BeautifulSoup) -> str:
        """Extract promo channels"""
        try:
            channels = []
            text_content = soup.get_text().lower()
            for pattern in CHANNEL_PATTERNS:
                if re.search(pattern, text_content, re.IGNORECASE):
                    channel_name = pattern.replace('(?:', '').replace(')', '').replace('|', ' or ')
                    if channel_name not in channels:
                        channels.append(channel_name)
            
            return "; ".join(channels) if channels else "No promo channels specified"
        except Exception as e:
            logger.warning(f"Error extracting promo channels: {e}")
            return "Error extracting promo channels"
    
    def extract_tax_docs(self, soup: BeautifulSoup) -> str:
        """Extract tax document information"""
        try:
            # Search in text content
            text_content = soup.get_text()
            found_docs = []
            for pattern in TAX_PATTERNS:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                if matches:
                    # Use the actual matched text instead of the pattern
                    found_docs.extend(matches[:2])  # Limit to 2 matches per pattern    
            # Search in specific elements that might contain tax requirements
            tax_elements = soup.find_all(['div', 'section', 'p', 'li'], 
                                       string=re.compile(r'(?:tax|W-9|W-8|1099|compliance)', re.I))
            for element in tax_elements:
                element_text = element.get_text()
                for pattern in TAX_PATTERNS:
                    if re.search(pattern, element_text, re.IGNORECASE):
                        doc_type = pattern.replace('(?:', '').replace(')', '').replace('|', ' or ')
                        if doc_type not in found_docs:
                            found_docs.append(doc_type)
            # Look for forms or links that might be tax-related
            forms = soup.find_all(['form', 'a'], string=re.compile(r'(?:tax|W-9|W-8|1099|compliance)', re.I))
            for form in forms:
                form_text = form.get_text()
                for pattern in TAX_PATTERNS:
                    if re.search(pattern, form_text, re.IGNORECASE):
                        doc_type = pattern.replace('(?:', '').replace(')', '').replace('|', ' or ')
                        if doc_type not in found_docs:
                            found_docs.append(doc_type)
            
            if found_docs:
                return f"Tax documents required: {', '.join(found_docs[:3])}"  # Limit to 3 most relevant
            
            return "Tax document requirements not specified"
            
        except Exception as e:
            logger.warning(f"Error extracting tax docs: {e}")
            return "Error extracting tax docs"
    
    def extract_terms_acceptance(self, soup: BeautifulSoup) -> str:
        """Extract terms acceptance checkbox information"""
        try:
            # Search in text content
            text_content = soup.get_text()
            found_terms = []
            for pattern in TERMS_PATTERNS:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                if matches:
                    # Use the actual matched text instead of the pattern
                    found_terms.extend(matches[:2])  # Limit to 2 matches per pattern
            # Look for actual checkbox elements with better detection
            checkboxes = soup.find_all('input', {'type': 'checkbox'})
            checkbox_terms = []
            
            for checkbox in checkboxes:
                name = checkbox.get('name', '').lower()
                id_attr = checkbox.get('id', '').lower()
                class_attr = ' '.join(checkbox.get('class', [])).lower()
                value = checkbox.get('value', '').lower()
                # Check various attributes for terms-related keywords
                terms_keywords = ['terms', 'agreement', 'accept', 'agree', 'privacy', 'legal', 'contract', 'policy']
                if any(keyword in name or keyword in id_attr or keyword in class_attr or keyword in value 
                       for keyword in terms_keywords):
                    checkbox_terms.append(f"Checkbox found: {name or id_attr or 'terms-related'}")
            
            # Look for labels associated with checkboxes
            labels = soup.find_all('label')
            for label in labels:
                label_text = label.get_text().lower()
                if any(keyword in label_text for keyword in ['terms', 'agree', 'accept', 'privacy', 'legal']):
                    # Check if this label is associated with a checkbox
                    for_input = label.get('for')
                    if for_input:
                        checkbox = soup.find('input', {'id': for_input, 'type': 'checkbox'})
                        if checkbox:
                            checkbox_terms.append(f"Label checkbox: {label_text.strip()[:50]}")
            
            # Look for forms that might contain terms acceptance
            forms = soup.find_all('form')
            for form in forms:
                form_text = form.get_text().lower()
                if any(keyword in form_text for keyword in ['terms', 'agree', 'accept', 'privacy']):
                    # Check if form has checkboxes
                    form_checkboxes = form.find_all('input', {'type': 'checkbox'})
                    if form_checkboxes:
                        checkbox_terms.append("Form with terms checkbox found")
            
            # Combine results
            all_terms = found_terms + checkbox_terms
            if all_terms:
                return f"Terms acceptance required: {', '.join(all_terms[:3])}"  # Limit to 3 most relevant
            
            return "Terms acceptance not specified"
            
        except Exception as e:
            logger.warning(f"Error extracting terms acceptance: {e}")
            return "Error extracting terms acceptance"
    
    def extract_digital_signature(self, soup: BeautifulSoup) -> str:
        """Extract digital signature information"""
        try:
            # Search in text content
            text_content = soup.get_text()
            found_signatures = []
            
            for pattern in SIGNATURE_PATTERNS:
                matches = re.findall(pattern, text_content, re.IGNORECASE)
                if matches:
                    # Use the actual matched text instead of the pattern
                    found_signatures.extend(matches[:2])  # Limit to 2 matches per pattern
            
            # Look for signature input elements with better detection
            signature_inputs = []
            
            # Check input elements with signature-related attributes
            inputs = soup.find_all('input')
            for input_elem in inputs:
                name = input_elem.get('name', '').lower()
                id_attr = input_elem.get('id', '').lower()
                class_attr = ' '.join(input_elem.get('class', [])).lower()
                placeholder = input_elem.get('placeholder', '').lower()
                type_attr = input_elem.get('type', '').lower()
                
                # Check for signature-related keywords
                sig_keywords = ['sign', 'signature', 'authorize', 'approve', 'confirm']
                
                if any(keyword in name or keyword in id_attr or keyword in class_attr or keyword in placeholder 
                       for keyword in sig_keywords):
                    signature_inputs.append(f"Input field: {name or id_attr or 'signature-related'}")
            
            # Check textarea elements
            textareas = soup.find_all('textarea')
            for textarea in textareas:
                name = textarea.get('name', '').lower()
                id_attr = textarea.get('id', '').lower()
                class_attr = ' '.join(textarea.get('class', [])).lower()
                placeholder = textarea.get('placeholder', '').lower()
                
                sig_keywords = ['sign', 'signature', 'authorize', 'approve', 'confirm']
                
                if any(keyword in name or keyword in id_attr or keyword in class_attr or keyword in placeholder 
                       for keyword in sig_keywords):
                    signature_inputs.append(f"Textarea field: {name or id_attr or 'signature-related'}")
            
            # Look for canvas elements (often used for signature pads)
            canvases = soup.find_all('canvas')
            for canvas in canvases:
                id_attr = canvas.get('id', '').lower()
                class_attr = ' '.join(canvas.get('class', [])).lower()
                
                if any(keyword in id_attr or keyword in class_attr 
                       for keyword in ['sign', 'signature', 'pad', 'draw']):
                    signature_inputs.append("Signature canvas found")
            
            # Look for forms that might contain signature requirements
            forms = soup.find_all('form')
            for form in forms:
                form_text = form.get_text().lower()
                if any(keyword in form_text for keyword in ['sign', 'signature', 'authorize', 'approve']):
                    # Check if form has signature-related inputs
                    form_inputs = form.find_all(['input', 'textarea', 'canvas'])
                    sig_inputs = []
                    for inp in form_inputs:
                        name = inp.get('name', '').lower()
                        id_attr = inp.get('id', '').lower()
                        class_attr = ' '.join(inp.get('class', [])).lower()
                        
                        if any(keyword in name or keyword in id_attr or keyword in class_attr 
                               for keyword in ['sign', 'signature', 'authorize']):
                            sig_inputs.append("Form signature field")
                    
                    if sig_inputs:
                        signature_inputs.extend(sig_inputs)
            
            # Look for buttons that might be signature-related
            buttons = soup.find_all(['button', 'input'])
            for button in buttons:
                button_text = button.get_text().lower() if button.get_text() else ''
                value = button.get('value', '').lower()
                class_attr = ' '.join(button.get('class', [])).lower()
                
                if any(keyword in button_text or keyword in value or keyword in class_attr 
                       for keyword in ['sign', 'signature', 'authorize', 'approve', 'confirm']):
                    signature_inputs.append(f"Signature button: {button_text or value or 'signature-related'}")
            
            # Combine results
            all_signatures = found_signatures + signature_inputs
            if all_signatures:
                return f"Digital signature required: {', '.join(all_signatures[:3])}"  # Limit to 3 most relevant
            
            return "Digital signature not specified"
            
        except Exception as e:
            logger.warning(f"Error extracting digital signature: {e}")
            return "Error extracting digital signature"
    
    def extract_meta_description(self, soup: BeautifulSoup) -> str:
        """Extract meta description"""
        try:
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc and meta_desc.get('content'):
                return meta_desc['content'].strip()
            return "No meta description found"
        except Exception as e:
            logger.warning(f"Error extracting meta description: {e}")
            return "Error extracting meta description"
    
    def extract_all_data_with_llm(self, soup: BeautifulSoup, base_url: str) -> Dict[str, Any]:
        """
        Extract all affiliate data using LLM as primary method with fallback to traditional extraction
        Args:
            soup (BeautifulSoup): Parsed HTML content
            base_url (str): The base URL being scraped
        Returns:
            Dict[str, Any]: Complete extracted data
        """
        try:
            # Try LLM extraction first
            llm_data = {}
            llm_data = self.llm_extractor.extract_affiliate_data_with_llm(soup, base_url)
            
            # Prepare final data structure
            extracted_data = {
                'scraped_url': base_url,
                'scraped_at': datetime.now().isoformat(),
                'extraction_method': 'hybrid' if llm_data else 'traditional'
            }
            extracted_data.update({
                'logo_url': self.safe_extract(self.extract_logo, soup, base_url),
                'product_images': self.safe_extract(self.extract_product_images, soup, base_url),
                'color_codes': self.safe_extract(self.extract_color_codes, soup, base_url),
                'font_stack': self.safe_extract(self.extract_font_stack, soup),
                'business_entity_type': self.safe_extract(self.extract_business_entity_type, soup),
                'tax_docs': self.safe_extract(self.extract_tax_docs, soup),
                'terms_acceptance': self.safe_extract(self.extract_terms_acceptance, soup),
                'digital_signature': self.safe_extract(self.extract_digital_signature, soup),
                'page_title': soup.find('title').get_text().strip() if soup.find('title') else 'No title found'
            })
            
            # Use LLM data for content-based fields if available
            if llm_data:
                extracted_data.update({
                    'meta_description': llm_data.get('meta_description', 'No meta description found'),
                    'promo_channels': llm_data.get('promo_channels', 'No promo channels specified'),
                    'reach_estimate': llm_data.get('reach_estimate', 'Reach estimate not specified'),
                    'learning_objectives': llm_data.get('learning_objectives', 'No learning objectives found'),
                    'challenges_solved': llm_data.get('challenges_solved', 'No challenges solved found'),
                    'overarching_offer': llm_data.get('overarching_offer', 'No overarching offer found'),
                    'differentiator': llm_data.get('differentiator', 'No differentiator found'),
                    'core_benefit': llm_data.get('core_benefit', 'No core benefit found')
                })
            else:
                extracted_data.update({
                    'meta_description': self.safe_extract(self.extract_meta_description, soup),
                    'promo_channels': self.safe_extract(self.extract_promo_channels, soup),
                    'reach_estimate': self.safe_extract(self.extract_reach_estimate, soup),
                    'learning_objectives': self.safe_extract(self.extract_learning_objectives, soup),
                    'challenges_solved': self.safe_extract(self.extract_challenges_solved, soup),
                    'overarching_offer': self.safe_extract(self.extract_overarching_offer, soup),
                    'differentiator': self.safe_extract(self.extract_differentiator, soup),
                    'core_benefit': self.safe_extract(self.extract_core_benefit, soup)
                })
            return extracted_data
            
        except Exception as e:
            logger.error(f"❌ Error in comprehensive data extraction: {e}")
            logger.error(f"🔍 Error details: {str(e)}", exc_info=True)
            return {
                'scraped_url': base_url,
                'scraped_at': datetime.now().isoformat(),
                'extraction_method': 'error',
                'error': str(e)
            }