import { Zap, Search, Filter, Sparkles } from "lucide-react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";

export const QuickApply = () => {
  const quickActions = [
    {
      icon: Search,
      title: "Find Opportunities",
      description: "Discover new speaking gigs",
      action: "search",
      count: "127 new"
    },
    {
      icon: Sparkles,
      title: "AI Autofill",
      description: "Smart application assistance",
      action: "ai-assist",
      count: "Ready"
    },
    {
      icon: Filter,
      title: "My Applications",
      description: "Track submitted proposals",
      action: "applications",
      count: "8 pending"
    }
  ];

  return (
    <Card className="bg-card border-border-subtle p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-sm font-semibold text-foreground flex items-center gap-2">
          <Zap className="h-4 w-4 text-primary" />
          Quick Actions
        </h3>
      </div>
      
      {/* Main Quick Apply Button */}
      <Button className="w-full bg-gradient-primary hover:shadow-glow transition-all duration-300 text-primary-foreground font-medium">
        <Sparkles className="h-4 w-4 mr-2" />
        Quick Apply with AI
      </Button>
      
      {/* Action Grid */}
      <div className="grid grid-cols-1 gap-2">
        {quickActions.map((action, index) => {
          const IconComponent = action.icon;
          return (
            <button
              key={index}
              className="flex items-center justify-between p-3 bg-surface-elevated border border-border-subtle rounded-lg hover:bg-surface hover:border-primary/30 transition-all duration-200 group"
            >
              <div className="flex items-center gap-3">
                <div className="p-1.5 bg-muted rounded-md group-hover:bg-primary/20 transition-colors">
                  <IconComponent className="h-3.5 w-3.5 text-foreground-muted group-hover:text-primary transition-colors" />
                </div>
                <div className="text-left">
                  <div className="text-xs font-medium text-foreground">
                    {action.title}
                  </div>
                  <div className="text-xs text-foreground-subtle">
                    {action.description}
                  </div>
                </div>
              </div>
              
              <div className="text-xs text-foreground-muted bg-accent px-2 py-1 rounded-md">
                {action.count}
              </div>
            </button>
          );
        })}
      </div>
      
      {/* Stats Row */}
      <div className="grid grid-cols-3 gap-2 pt-2 border-t border-border-subtle">
        <div className="text-center">
          <div className="text-sm font-semibold text-foreground">24</div>
          <div className="text-xs text-foreground-muted">Applied</div>
        </div>
        <div className="text-center">
          <div className="text-sm font-semibold text-success">8</div>
          <div className="text-xs text-foreground-muted">Accepted</div>
        </div>
        <div className="text-center">
          <div className="text-sm font-semibold text-silver">92%</div>
          <div className="text-xs text-foreground-muted">Success Rate</div>
        </div>
      </div>
    </Card>
  );
};