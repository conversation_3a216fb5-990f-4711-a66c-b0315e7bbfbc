"""
Constants and Configuration Module for Speaker Opportunity Scraper

This module contains all constants, configuration values, and patterns used throughout
the scraper service. It centralizes configuration to make the code more maintainable
and easier to modify.

Author: Speaker Bot Team
Version: 2.0 (Optimized)
Last Updated: 2024
"""

import re

# =============================================================================
# DATA STRUCTURE CONFIGURATION
# =============================================================================

# Standard column order for DataFrame output - ensures consistent data structure
COLUMNS_ORDER = [
    "title",           # Event title
    "organization",    # Organizing entity
    "event_type",      # Type of event (conference, workshop, etc.)
    "start_date",      # Event start date
    "end_date",        # Event end date
    "event_url",       # Direct URL to event page
    "source_url",      # URL where opportunity was found
    "city",           # Event city
    "state",          # Event state/province
    "country",        # Event country
    "venue",          # Specific venue name
    "is_virtual",     # Boolean: virtual event flag
    "description",    # Event description
    "industry",       # Industry/sector
    "search_query",   # Query used to find this opportunity
    "feed_back"       # Extraction feedback and notes
]

# =============================================================================
# DOMAIN FILTERING AND PRIORITIZATION
# =============================================================================

# Domain prioritization: Higher priority domains are more likely to contain
# legitimate speaking opportunities rather than job postings
PREFERRED_DOMAINS = (".org", "association", "society")  # Highest priority
SECONDARY_DOMAINS = (".com",)                           # Medium priority
TERTIARY_DOMAINS = (".gov", ".edu")                     # Lower priority

# Domains to completely exclude - these are job boards and social media
# that rarely contain legitimate speaking opportunities
DROP_DOMAINS = (
    "linkedin.com", "indeed.com", "glassdoor.com", "ziprecruiter.com",
    "facebook.com", "twitter.com", "x.com", "monster.com", "careerbuilder.com",
    "simplyhired.com", "snagajob.com", "flexjobs.com", "upwork.com", "freelancer.com"
)

# =============================================================================
# CONTENT FILTERING KEYWORDS
# =============================================================================

# Keywords that indicate job postings rather than speaking opportunities
# Used to filter out irrelevant content
JOB_KEYWORDS = [
    "we are hiring", "join our team", "career opportunities", "employment",
    "full-time", "part-time", "contract position", "job opening", "vacancy",
    "staff position", "employee", "work with us", "apply for position",
    "job description", "salary", "benefits package", "work from home job",
    "remote job", "freelance work", "consultant position", "staffing"
]

# Keywords that indicate legitimate speaking opportunities and events
# Used to validate that content is event-related
EVENT_KEYWORDS = [
    "conference", "summit", "symposium", "webinar", "workshop", "seminar",
    "convention", "expo", "forum", "gathering", "event", "meeting",
    "annual meeting", "annual conference", "keynote", "presentation",
    "speaking engagement", "panel discussion", "breakout session"
]

# =============================================================================
# SEARCH QUERY BUILDING STRINGS
# =============================================================================

# Pre-built query components for constructing search queries
# These are used to find speaking opportunities across different search engines
CALL_PHRASES = '( "call for speakers" OR "call for proposals" OR "submit a proposal" OR "speaker application" OR "speaker submissions" OR "CFP" OR "RFP" OR "speaking opportunities" OR "presentation opportunities" OR "keynote speakers" )'
EVENT_WORDS  = '( "conference" OR "summit" OR "symposium" OR "annual meeting" OR "webinar" OR "workshop" OR "seminar" OR "convention" OR "expo" OR "forum" OR "gathering" OR "event" )'
PAID_PHRASES = '( "paid keynote" OR honorarium OR "payment provided" OR "speaker fee" OR "compensation" OR "stipend" )'
NOT_JOBS     = '(NOT jobs OR NOT "we are hiring" OR NOT recruitment OR NOT "career" OR NOT "employment")'

# =============================================================================
# REGULAR EXPRESSIONS FOR DATA EXTRACTION
# =============================================================================

# Email address pattern - matches standard email formats
EMAIL_RX = re.compile(r"[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}", re.I)

# Phone number pattern - matches various phone number formats
PHONE_RX = re.compile(r"(?:\+?\d{1,2}\s*)?(?:\(?\d{3}\)?[\s\-.]?\d{3}[\s\-.]?\d{4})")

# =============================================================================
# DATE EXTRACTION PATTERNS
# =============================================================================

# Basic date pattern for month-day-year formats
# Matches: "January 15, 2024", "Jan 15", "March 1st, 2024"
DATE_CANDIDATE_RX = re.compile(
    r"(?:Jan(?:uary)?|Feb(?:ruary)?|Mar(?:ch)?|Apr(?:il)?|May|Jun(?:e)?|"
    r"Jul(?:y)?|Aug(?:ust)?|Sep(?:t(?:ember)?)?|Oct(?:ober)?|Nov(?:ember)?|"
    r"Dec(?:ember)?)\s+\d{1,2}(?:,\s*\d{4})?", re.I
)

# Enhanced date patterns for comprehensive date detection
# Matches multiple date formats commonly found on event pages
ENHANCED_DATE_RX = re.compile(
    r"(?:\d{1,2}[/-]\d{1,2}[/-]\d{2,4})|"  # MM/DD/YYYY or DD/MM/YYYY
    r"(?:\d{4}[/-]\d{1,2}[/-]\d{1,2})|"    # YYYY/MM/DD
    r"(?:\d{1,2}\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{4})|"  # DD Month YYYY
    r"(?:(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{1,2},?\s+\d{4})|"  # Month DD, YYYY
    r"(?:\d{1,2}\s+(?:st|nd|rd|th)\s+(?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)[a-z]*\s+\d{4})",  # DDth Month YYYY
    re.I
)

# =============================================================================
# GEOGRAPHICAL DATA FOR LOCATION EXTRACTION
# =============================================================================

# US state abbreviations - used for location validation and extraction
US_STATE_ABBR = {
    "AL","AK","AZ","AR","CA","CO","CT","DE","FL","GA","HI","ID","IL","IN","IA","KS","KY","LA","ME","MD","MA","MI","MN","MS","MO","MT","NE","NV","NH","NJ","NM","NY","NC","ND","OH","OK","OR","PA","RI","SC","SD","TN","TX","UT","VT","VA","WA","WV","WI","WY","DC"
}

# US state full names - used for location validation and extraction
US_STATE_FULL_NAMES = {
    "alabama", "alaska", "arizona", "arkansas", "california", "colorado", "connecticut", "delaware",
    "florida", "georgia", "hawaii", "idaho", "illinois", "indiana", "iowa", "kansas", "kentucky",
    "louisiana", "maine", "maryland", "massachusetts", "michigan", "minnesota", "mississippi",
    "missouri", "montana", "nebraska", "nevada", "new hampshire", "new jersey", "new mexico",
    "new york", "north carolina", "north dakota", "ohio", "oklahoma", "oregon", "pennsylvania",
    "rhode island", "south carolina", "south dakota", "tennessee", "texas", "utah", "vermont",
    "virginia", "washington", "west virginia", "wisconsin", "wyoming", "district of columbia"
}

# International country names and variations - used for location validation
COUNTRY_NAMES = {
    "united states", "usa", "us", "america", "united states of america",
    "canada", "united kingdom", "uk", "britain", "great britain", "england", "scotland", "wales",
    "australia", "germany", "france", "india", "spain", "italy", "netherlands", "holland",
    "sweden", "norway", "denmark", "finland", "switzerland", "ireland", "belgium", "austria",
    "poland", "japan", "china", "south korea", "singapore", "brazil", "mexico", "argentina",
    "chile", "south africa", "egypt", "israel", "turkey", "russia", "ukraine", "czech republic",
    "hungary", "portugal", "greece", "new zealand", "thailand", "vietnam", "philippines",
    "indonesia", "malaysia", "taiwan", "hong kong", "uae", "united arab emirates", "saudi arabia",
    "qatar", "kuwait", "bahrain", "oman", "jordan", "lebanon", "cyprus", "malta", "iceland",
    "luxembourg", "monaco", "liechtenstein", "andorra", "san marino", "vatican", "croatia",
    "slovenia", "slovakia", "estonia", "latvia", "lithuania", "bulgaria", "romania", "serbia",
    "montenegro", "bosnia", "macedonia", "albania", "moldova", "belarus", "georgia", "armenia",
    "azerbaijan", "kazakhstan", "uzbekistan", "kyrgyzstan", "tajikistan", "turkmenistan",
    "afghanistan", "pakistan", "bangladesh", "sri lanka", "nepal", "bhutan", "myanmar",
    "cambodia", "laos", "mongolia", "north korea", "iran", "iraq", "syria", "yemen",
    "ethiopia", "kenya", "uganda", "tanzania", "ghana", "nigeria", "morocco", "tunisia",
    "algeria", "libya", "sudan", "chad", "niger", "mali", "burkina faso", "senegal",
    "guinea", "sierra leone", "liberia", "ivory coast", "ghana", "togo", "benin",
    "cameroon", "central african republic", "democratic republic of congo", "congo",
    "gabon", "equatorial guinea", "sao tome and principe", "angola", "zambia", "zimbabwe",
    "botswana", "namibia", "lesotho", "swaziland", "madagascar", "mauritius", "seychelles",
    "comoros", "djibouti", "somalia", "eritrea", "rwanda", "burundi", "malawi", "mozambique"
}

# International state/province patterns for non-US locations
INTERNATIONAL_STATE_PATTERNS = {
    "canada": {"ab", "bc", "mb", "nb", "nl", "ns", "nt", "nu", "on", "pe", "qc", "sk", "yt",
               "alberta", "british columbia", "manitoba", "new brunswick", "newfoundland",
               "nova scotia", "northwest territories", "nunavut", "ontario", "prince edward island",
               "quebec", "saskatchewan", "yukon"},
    "australia": {"nsw", "vic", "qld", "wa", "sa", "tas", "act", "nt",
                  "new south wales", "victoria", "queensland", "western australia",
                  "south australia", "tasmania", "australian capital territory", "northern territory"},
    "germany": {"bw", "by", "be", "bb", "hb", "hh", "he", "mv", "ni", "nw", "rp", "sl", "sn", "st", "sh", "th",
                "baden-württemberg", "bayern", "berlin", "brandenburg", "bremen", "hamburg", "hessen",
                "mecklenburg-vorpommern", "niedersachsen", "nordrhein-westfalen", "rheinland-pfalz",
                "saarland", "sachsen", "sachsen-anhalt", "schleswig-holstein", "thüringen"},
    "uk": {"england", "scotland", "wales", "northern ireland", "london", "birmingham", "manchester", "liverpool"},
    "france": {"idf", "paca", "auvergne-rhône-alpes", "occitanie", "nouvelle-aquitaine", "hauts-de-france",
               "grand est", "bretagne", "normandie", "centre-val de loire", "bourgogne-franche-comté",
               "pays de la loire", "corse", "guadeloupe", "martinique", "guyane", "la réunion", "mayotte"}
}

# =============================================================================
# VENUE AND LOCATION DETECTION KEYWORDS
# =============================================================================

# Keywords that indicate venue names or event locations
VENUE_KEYWORDS = (
    'hotel', 'center', 'centre', 'convention', 'hall', 'campus', 'university', 'college',
    'marriott', 'hilton', 'hyatt', 'sheraton', 'westin', 'radisson', 'holiday inn',
    'resort', 'lodge', 'inn', 'galleria', 'arena', 'auditorium', 'theatre', 'theater',
    'expo', 'pavilion', 'stadium', 'gymnasium', 'gym', 'club', 'center', 'centre',
    'conference center', 'convention center', 'exhibition center', 'exhibition hall',
    'trade center', 'business center', 'community center', 'cultural center',
    'performing arts center', 'arts center', 'science center', 'research center',
    'innovation center', 'technology center', 'medical center', 'health center',
    'hospital', 'clinic', 'library', 'museum', 'gallery', 'studio', 'workshop',
    'facility', 'building', 'complex', 'plaza', 'square', 'mall', 'shopping center',
    'office', 'headquarters', 'hq', 'tower', 'towers', 'skyscraper', 'high-rise',
    'ballroom', 'meeting room', 'conference room', 'boardroom', 'seminar room',
    'classroom', 'lecture hall', 'presentation room', 'training room',
    'exhibition space', 'showroom', 'show floor', 'booth', 'stand',
    'outdoor', 'garden', 'patio', 'terrace', 'rooftop', 'deck', 'veranda',
    'park', 'recreation', 'sports', 'fitness', 'wellness', 'spa',
    'restaurant', 'cafe', 'bar', 'lounge', 'dining', 'catering',
    'airport', 'terminal', 'station', 'depot', 'hub', 'gateway',
    'marina', 'harbor', 'harbour', 'port', 'dock', 'pier', 'wharf',
    'casino', 'gaming', 'entertainment', 'nightclub', 'clubhouse',
    'church', 'cathedral', 'temple', 'mosque', 'synagogue', 'chapel',
    'school', 'academy', 'institute', 'foundation', 'association',
    'society', 'organization', 'institution', 'establishment'
)

# Street suffixes commonly found in addresses - used for location parsing
STREET_SUFFIXES = (
    'road', 'rd', 'street', 'st', 'avenue', 'ave', 'boulevard', 'blvd', 'parkway', 'pkwy',
    'drive', 'dr', 'lane', 'ln', 'court', 'ct', 'place', 'pl', 'highway', 'hwy',
    'route', 'way', 'terrace', 'ter', 'circle', 'cir', 'crescent', 'cres',
    'close', 'cl', 'grove', 'gr', 'heights', 'hts', 'hill', 'hills',
    'ridge', 'rdg', 'valley', 'vly', 'view', 'vista', 'vst', 'walk',
    'square', 'sq', 'plaza', 'pz', 'commons', 'cmns', 'green', 'grn',
    'meadow', 'mdw', 'field', 'fld', 'park', 'pk', 'garden', 'gdn',
    'manor', 'mnr', 'estate', 'est', 'village', 'vlg', 'town', 'tn',
    'city', 'cty', 'borough', 'boro', 'county', 'cnty', 'state', 'st',
    'national', 'natl', 'international', 'intl', 'global', 'world'
)

# =============================================================================
# TEXT PROCESSING AND FILTERING
# =============================================================================

# Common words to filter out during text processing - improves extraction accuracy
NOISE_WORDS = set([
    'register', 'now', 'apply', 'submit', 'proposal', 'proposals', 'framework', 'operations',
    'conference', 'summit', 'symposium', 'workshop', 'webinar', 'live', 'join', 'agenda',
    'schedule', 'call', 'for', 'speakers', 'will', 'be', 'held', 'by', 'the', 'at', 'in',
    'on', 'and', 'or', 'of', 'to', 'from', 'with', 'about', 'regarding', 'concerning',
    'event', 'meeting', 'gathering', 'session', 'presentation', 'keynote', 'panel',
    'discussion', 'breakout', 'networking', 'reception', 'dinner', 'lunch', 'breakfast',
    'coffee', 'tea', 'refreshments', 'registration', 'check-in', 'welcome', 'opening',
    'closing', 'award', 'ceremony', 'celebration', 'party', 'social', 'entertainment',
    'music', 'dance', 'performance', 'show', 'exhibition', 'display', 'demo', 'demo',
    'demonstration', 'tutorial', 'training', 'education', 'learning', 'development',
    'professional', 'career', 'business', 'industry', 'sector', 'field', 'domain',
    'technology', 'innovation', 'research', 'science', 'engineering', 'medicine',
    'healthcare', 'finance', 'banking', 'investment', 'marketing', 'sales', 'management',
    'leadership', 'strategy', 'planning', 'consulting', 'advisory', 'expertise',
    'experience', 'knowledge', 'skills', 'talent', 'expert', 'specialist', 'professional'
])

# =============================================================================
# LOCATION PATTERN MATCHING
# =============================================================================

# Regex patterns for identifying city names in text
COMMON_CITY_PATTERNS = [
    r'\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\s+(?:City|Town|Village|Borough|Heights|Hills|Park|Gardens|Manor|Estates)\b',
    r'\b(?:New|Old|North|South|East|West|Upper|Lower|Great|Little|Big|Small)\s+[A-Z][a-z]+\b',
    r'\b[A-Z][a-z]+\s+(?:Springs|Falls|Ridge|Valley|Harbor|Harbour|Port|Bay|Creek|River|Lake|Mountain|Mount|Hill|Hills)\b',
    r'\b[A-Z][a-z]+\s+(?:Beach|Shore|Coast|Island|Isle|Peninsula|Cape|Point|Head|Neck|Nook|Corner|Crossing|Junction|Center|Centre)\b'
]

# Major cities by country for international location validation
INTERNATIONAL_CITY_PATTERNS = {
    'canada': ['toronto', 'vancouver', 'montreal', 'calgary', 'ottawa', 'edmonton', 'winnipeg', 'quebec', 'hamilton', 'kitchener'],
    'uk': ['london', 'birmingham', 'manchester', 'liverpool', 'leeds', 'sheffield', 'bristol', 'edinburgh', 'glasgow', 'cardiff'],
    'australia': ['sydney', 'melbourne', 'brisbane', 'perth', 'adelaide', 'gold coast', 'newcastle', 'wollongong', 'hobart', 'darwin'],
    'germany': ['berlin', 'hamburg', 'munich', 'cologne', 'frankfurt', 'stuttgart', 'düsseldorf', 'dortmund', 'essen', 'leipzig'],
    'france': ['paris', 'marseille', 'lyon', 'toulouse', 'nice', 'nantes', 'strasbourg', 'montpellier', 'bordeaux', 'lille'],
    'spain': ['madrid', 'barcelona', 'valencia', 'seville', 'zaragoza', 'málaga', 'murcia', 'palma', 'las palmas', 'bilbao'],
    'italy': ['rome', 'milan', 'naples', 'turin', 'palermo', 'genoa', 'bologna', 'florence', 'bari', 'catania'],
    'japan': ['tokyo', 'yokohama', 'osaka', 'nagoya', 'sapporo', 'fukuoka', 'kobe', 'kawasaki', 'kyoto', 'saitama'],
    'china': ['beijing', 'shanghai', 'guangzhou', 'shenzhen', 'tianjin', 'wuhan', 'chengdu', 'nanjing', 'xian', 'hangzhou'],
    'india': ['mumbai', 'delhi', 'bangalore', 'hyderabad', 'ahmedabad', 'chennai', 'kolkata', 'pune', 'jaipur', 'lucknow']
}
