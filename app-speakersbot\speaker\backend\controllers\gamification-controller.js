const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");
const ApiResponse = require("../helpers/api-response");
const speakerHistoryService = require("../services/gamification-service");

// ------------------------- Speaker History Controller -------------------------

/**
 * Get points history for the authenticated speaker
 * Retrieves paginated points activity history with filtering capabilities
 * 
 * @param {Object} req - Express request object
 * @param {Object} req.user - Authenticated user object (set by verifyToken middleware)
 * @param {Object} req.query - Query parameters for filtering and pagination
 * @param {number} [req.query.page] - Page number (defaults to 1)
 * @param {number} [req.query.limit] - Items per page (defaults to 10)
 * @param {string} [req.query.type] - Filter by type (opportunity, subscription, gamification)
 * @param {string} [req.query.start_date] - Filter by start date (YYYY-MM-DD)
 * @param {string} [req.query.end_date] - Filter by end date (YYYY-MM-DD)
 * @param {string} [req.query.search] - Search in activity descriptions
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {Object} JSON response with paginated points history
 * @throws {CustomError} When speaker authentication is missing or service fails
 */
exports.getSpeakerPointsHistory = async (req, res, next) => {
    try {
        // Extract speaker ID from authenticated session
        const speakerId = req.user?.id || req.userId;
        
        if (!speakerId) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Speaker authentication required");
        }        

        // Add speaker ID to request for service
        req.speakerId = speakerId;
        
        // Get points history from service
        const result = await speakerHistoryService.getSpeakerPointsHistory(req);
        const { status, message, data,pageData } = result;

        if (status) {
            res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ 
                message, 
                data,
                pagination: pageData
            }));
        } else {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
        }

    } catch (error) {
        console.error("Error in getSpeakerPointsHistory:", error);
        next(error);
    }
};

/**
 * Process user streak for daily login
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {Promise<void>}
 */
exports.processPoints = async (req, res, next) => {
    try {
        const speaker_id = req.user.id;
        const event = req.body.event

        const { status, data, message } = await speakerHistoryService.callEventPoints(speaker_id, event);

        if (status) {
            res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ data, message }));
        } else {
            res.status(RESPONSE_CODES.BAD_REQUEST).json({ status: false, data: data, message: message });
        }
    } catch (error) {
        console.error("Error processing user points:", error);
        next(error);
    }
}