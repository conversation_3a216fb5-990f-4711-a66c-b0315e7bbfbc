"""
Video Analysis Service
======================
Analyzes video files to extract cadence and delivery timing metrics:
- Extracts audio from video
- Analyzes audio prosody (using AudioAnalysisService)
- Extracts video metadata (duration, fps, resolution)
- Combines metrics for cadence analysis

Uses moviepy for audio extraction and opencv for video metadata.
"""

import os
from moviepy import VideoFileClip
from typing import Dict, Any, Optional
from pathlib import Path

from app.config.logger import get_logger
from app.services.audio_analysis_service import AudioAnalysisService

logger = get_logger(__name__, file_name="voiceprint.log")


class VideoAnalysisService:
    """
    Service for analyzing video files and extracting cadence metrics.
    - Extracts audio track from video using moviepy
    - Analyzes extracted audio using AudioAnalysisService
    - Extracts video metadata using opencv
    - Combines metrics for comprehensive cadence analysis
    """
    
    def __init__(self):
        """Initialize the video analysis service."""
        self.audio_analysis = AudioAnalysisService()
        self.max_video_duration = 60.0  # Maximum video duration to process (seconds)
        
    def analyze_video(self, video_path: str, extract_audio_to: Optional[str] = None) -> Dict[str, Any]:
        """
        Analyze video file and extract cadence metrics.
        
        Args:
            video_path: Path to the video file (.mp4, .mov)
            extract_audio_to: Optional path to save extracted audio (for debugging/cleanup)
            
        Returns:
            Dictionary containing video metrics and audio analysis
        """
        logger.info(f"Starting video analysis for: {video_path}")
        
        video_clip = None
        audio_temp_path = None
        
        try:
            # Validate video file exists
            if not os.path.exists(video_path):
                raise ValueError(f"Video file not found: {video_path}")
            
            # Load video using moviepy
            video_clip = VideoFileClip(video_path)
            video_duration = video_clip.duration
            
            # Validate video duration
            if video_duration < 0.5:
                raise ValueError(f"Video too short for analysis ({video_duration:.2f}s)")
            
            # Extract audio track
            logger.info(f"Extracting audio from video (duration: {video_duration:.2f}s)")
            audio_clip = video_clip.audio
            
            if audio_clip is None:
                raise ValueError("Video file contains no audio track")
            
            # Determine audio output path
            if extract_audio_to:
                audio_temp_path = extract_audio_to
            else:
                # Generate temp audio file path
                video_dir = Path(video_path).parent
                video_name = Path(video_path).stem
                audio_temp_path = str(video_dir / f"{video_name}_extracted_audio.wav")
            
            # Ensure directory exists
            audio_dir = os.path.dirname(audio_temp_path)
            if audio_dir:
                os.makedirs(audio_dir, exist_ok=True)
            
            # Write audio to temporary file
            logger.info(f"Writing extracted audio to: {audio_temp_path}")
            audio_clip.write_audiofile(
                audio_temp_path,
                codec='pcm_s16le',  # WAV format
                logger=None  # Suppress moviepy's logger output
            )
            
            # Close audio clip
            audio_clip.close()
            
            # Validate extracted audio file
            if not os.path.exists(audio_temp_path) or os.path.getsize(audio_temp_path) == 0:
                raise RuntimeError("Failed to extract audio from video")
            
            logger.info(f"Audio extracted successfully: {audio_temp_path} ({os.path.getsize(audio_temp_path)} bytes)")
            
            # Analyze extracted audio using AudioAnalysisService
            logger.info("Analyzing extracted audio for prosody metrics")
            audio_metrics = self.audio_analysis.analyze_audio(audio_temp_path)
            
            # Calculate cadence-specific metrics
            cadence_metrics = self._calculate_cadence_metrics(video_duration, audio_metrics)
            
            # Combine all metrics
            video_metrics = {
                "audio_analysis": audio_metrics,
                "cadence": cadence_metrics
            }
            
            logger.info(f"Video analysis completed successfully")
            return video_metrics
            
        except Exception as e:
            logger.error(f"Error analyzing video {video_path}: {e}", exc_info=True)
            raise
            
        finally:
            # Cleanup video clip
            if video_clip is not None:
                try:
                    video_clip.close()
                except Exception:
                    pass
            
            # Cleanup extracted audio file (if we created it, not if provided)
            if audio_temp_path and extract_audio_to is None:
                try:
                    if os.path.exists(audio_temp_path):
                        os.remove(audio_temp_path)
                        logger.info(f"Cleaned up extracted audio file: {audio_temp_path}")
                except Exception as e:
                    logger.warning(f"Error cleaning up extracted audio file: {e}")

    def _calculate_cadence_metrics(self, duration: float, audio_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Calculate cadence-specific metrics from video duration and audio analysis.
        
        Args:
            duration: Video duration in seconds
            audio_metrics: Audio analysis metrics from AudioAnalysisService
            
        Returns:
            Dictionary with cadence metrics
        """
        try:
            # Extract values from audio metrics
            speech_rate_wpm = audio_metrics.get("tempo", {}).get("speech_rate_wpm", 0.0)
            speech_rate_spm = audio_metrics.get("tempo", {}).get("speech_rate_spm", 0.0)
            pause_ratio = audio_metrics.get("tempo", {}).get("pause_ratio", 0.0)
            
            # Calculate delivery timing metrics
            speech_time = duration * (1.0 - pause_ratio) if pause_ratio >= 0 else duration
            pause_time = duration * pause_ratio if pause_ratio >= 0 else 0.0
            
            # Calculate cadence score (lower pause ratio = more consistent delivery)
            cadence_consistency = 1.0 - pause_ratio if pause_ratio <= 1.0 else 0.0
            
            # Calculate words/syllables in video
            total_words = (speech_rate_wpm * duration) / 60.0 if duration > 0 else 0.0
            total_syllables = (speech_rate_spm * duration) / 60.0 if duration > 0 else 0.0
            
            return {
                "delivery_timing": {
                    "speech_duration_seconds": float(round(speech_time, 2)),
                    "pause_duration_seconds": float(round(pause_time, 2)),
                    "speech_ratio": float(round(1.0 - pause_ratio, 3)) if pause_ratio >= 0 else 1.0,
                    "pause_ratio": float(round(pause_ratio, 3)) if pause_ratio >= 0 else 0.0
                },
                "cadence_score": {
                    "consistency": float(round(cadence_consistency, 4)),
                    "words_estimated": float(round(total_words, 1)),
                    "syllables_estimated": float(round(total_syllables, 1))
                },
                "pace_characteristics": {
                    "words_per_minute": float(round(speech_rate_wpm, 2)),
                    "syllables_per_minute": float(round(speech_rate_spm, 2)),
                    "average_pause_length": float(round(pause_time / max(1, pause_ratio * duration * 10), 2)) if pause_ratio > 0 and duration > 0 else 0.0
                }
            }
            
        except Exception as e:
            logger.warning(f"Error calculating cadence metrics: {e}")
            return {
                "delivery_timing": {
                    "speech_duration_seconds": 0.0,
                    "pause_duration_seconds": 0.0,
                    "speech_ratio": 0.0,
                    "pause_ratio": 0.0
                },
                "cadence_score": {
                    "consistency": 0.0,
                    "words_estimated": 0.0,
                    "syllables_estimated": 0.0
                },
                "pace_characteristics": {
                    "words_per_minute": 0.0,
                    "syllables_per_minute": 0.0,
                    "average_pause_length": 0.0
                }
            }

