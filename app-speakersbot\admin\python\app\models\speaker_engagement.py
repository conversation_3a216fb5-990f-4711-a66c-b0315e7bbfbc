from sqlalchemy import create_engine, Column, Integer, String, DateTime, JSON, func
from sqlalchemy.ext.declarative import declarative_base
from app.config.config import config

Base = declarative_base()

class SpeakerEngagement(Base):
    __tablename__ = "speaker_engagements"
    
    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True, comment='Primary key for the speaker engagement record')
    
    # Basic information
    name = Column(String(255), nullable=False, comment='Name of the speaker')
    email = Column(String(255), nullable=False, comment='Email address of the speaker')
    
    # Location information
    city = Column(String(255), nullable=True, comment='City of the speaker')
    state = Column(String(255), nullable=True, comment='State of the speaker')
    country = Column(String(255), nullable=True, comment='Country of the speaker')
    
    # Categorization and topics
    primary_category = Column(String(255), nullable=False, comment='Primary category of the speaker')
    sub_category = Column(String(255), nullable=False, comment='Sub category of the speaker')
    topic = Column(String(255), nullable=False, comment='Topic of the speaker')
    
    # Preferences
    preferred_speaker_geography = Column(String(255), nullable=True, comment='Preferred speaker geography')
    
    # Opportunity data
    opportunity = Column(JSON, nullable=True, comment='Opportunity data stored as JSON')
    
    # Timestamps
    created_at = Column(DateTime, default=func.current_timestamp(), comment='Date and time when the record was created')
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='Date and time when the record was updated')


def create_speaker_engagement_table():
    db_url = config.get_database_url()
    engine = create_engine(db_url)
    Base.metadata.create_all(engine)

if __name__ == "__main__":
    create_speaker_engagement_table()
    print("Table 'speaker_engagements' created in MySQL database")
