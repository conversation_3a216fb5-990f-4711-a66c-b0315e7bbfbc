"""
Optimized Matches Controller - Clean Endpoint for Speaker Matching
===============================================================

This module provides a clean, optimized FastAPI endpoint for speaker matching operations
focused solely on the core matching functionality with best optimization strategies.

Key Features:
- Optimized request/response handling
- Comprehensive input validation
- Robust error handling with detailed logging
- Performance optimizations without extra endpoints
- Clean separation of concerns
- Maintains existing endpoint compatibility

Author: Speaker Bot Team
Version: 2.1 (Clean Optimized)
Last Updated: 2024
"""

import logging
import time
from datetime import datetime
from typing import Dict, Any, Optional
import random
from fastapi import APIRouter, HTTPException, Request, Path, Body

from app.config.config import config
from app.background_tasks.match_worker import match_worker
from app.services.speaker_matching_service import SpeakerMatchingService
from app.models.speaker_engagement import SpeakerEngagement
from pydantic import BaseModel

# Configure structured logging
logger = logging.getLogger(__name__)

class WebsiteMatchingRequest(BaseModel):
    name: str
    email: str
    city: Optional[str] = None
    state: Optional[str] = None
    country: Optional[str] = None
    primary_category: str
    sub_category: str
    topic: str
    preferred_speaker_geography: Optional[str] = None

class OptimizedMatchesController:
    """
    Clean optimized controller focused solely on core matching functionality.
    
    Provides only the essential endpoint with optimizations:
    - Fast request processing
    - Comprehensive validation
    - Error handling
    - Response optimization
    """
    
    def __init__(self):
        """Initialize the controller with minimal configuration."""
        self.router = APIRouter(
            prefix="/opportunity",
            tags=["Speaker Matching"],
            responses={
                400: {"description": "Bad Request - Invalid input parameters"},
                404: {"description": "Speaker not found"},
                500: {"description": "Internal Server Error - Processing failed"}
            }
        )
        
        # Register the core routes
        self._register_core_route()
        self._register_website_matching_route()
        
        logger.info("OptimizedMatchesController initialized")

    def _register_core_route(self):
        """Register the core speaker matching route with optimizations."""
        
        @self.router.post(
            "/speaker_matching/{speaker_id}",
            summary="Process Speaker Matching",
            description="""
            Process comprehensive speaker-to-opportunity matching using optimized hybrid approach:
            """,
            response_model=Dict[str, Any]
        )
        async def process_speaker_matching(speaker_id: int) -> Dict[str, Any]:
            """
            Core speaker matching endpoint with optimizations.
            Args:
                speaker_id: Speaker ID to process
            Returns:
                Matching results with metadata (ChromaDB + manual scoring only)
            """
            
            try:
                # Call optimized match worker with default batch size
                result = match_worker.process_speaker_matching(
                    speaker_id=speaker_id
                )
                
                # Handle success response
                if result.get("success", False):
                    return {
                        "success": True,
                        "message": result.get("message", f"Successfully processed speaker {speaker_id}"),
                        "total_matches": result.get("total_matches", 0),
                        "processing_time": result.get("processing_time", 0.0),
                        "batches_processed": result.get("batches_processed", 0),
                    }
                
                else:
                    # Handle worker failure
                    return {
                        "success": False,
                        "error": result.get("error", "Matching process failed"),
                        "message": f"Failed to process speaker {speaker_id}"
                    }
                
            except Exception as e:
                raise HTTPException(
                    status_code=500,
                    detail={
                        "error": "Internal processing error",
                        "message": str(e),
                        "speaker_id": speaker_id,
                    }
                )

    def _register_website_matching_route(self):
        """Register the website matching route."""
        
        @self.router.post(
            "/website_matching/{speaker_id}",
            summary="Website Speaker Matching",
            description="Match speaker data from website form with opportunities and return top match",
            response_model=Dict[str, Any]
        )
        async def website_matching(speaker_id: int, request: WebsiteMatchingRequest) -> Dict[str, Any]:
            """Website matching endpoint for external speaker submissions."""
            try:
                matching_service = SpeakerMatchingService()
                result = matching_service.website_matching(
                    speaker_id=speaker_id,
                    name=request.name,
                    email=request.email,
                    city=request.city,
                    state=request.state,
                    country=request.country,
                    primary_category=request.primary_category,
                    sub_category=request.sub_category,
                    topic=request.topic,
                    preferred_speaker_geography=request.preferred_speaker_geography
                )
                
                return {
                    "success": True,
                    "message": "Matching completed successfully",
                    "data": result
                }
                
            except Exception as e:
                logger.error(f"Website matching error: {e}")
                raise HTTPException(
                    status_code=500,
                    detail={
                        "error": "Matching process failed",
                        "message": str(e)
                    }
                )

# Create router instance
router = OptimizedMatchesController().router