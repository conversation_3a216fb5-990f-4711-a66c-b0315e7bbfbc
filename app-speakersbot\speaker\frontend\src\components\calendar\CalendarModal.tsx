import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Calendar } from '@/components/ui/calendar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { MapPin, Clock, Zap } from 'lucide-react';
import { format, isSameDay } from 'date-fns';

interface OpportunityEvent {
  id: number;
  title: string;
  location: string;
  eventDate: string;
  status: 'accepted' | 'pending' | 'coming';
  matchScore: number;
  compensation: string;
}

interface CalendarModalProps {
  isOpen: boolean;
  onClose: () => void;
  opportunities: OpportunityEvent[];
}

export function CalendarModal({ isOpen, onClose, opportunities }: CalendarModalProps) {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();

  // Extended demo opportunities with more dates
  const extendedOpportunities = [
    ...opportunities,
    { id: 10, title: "React Summit 2025", location: "Amsterdam, NL", eventDate: "2025-09-20", status: 'coming' as const, matchScore: 93, compensation: "$4,500" },
    { id: 11, title: "Vue.js Conference", location: "Toronto, CA", eventDate: "2025-09-25", status: 'pending' as const, matchScore: 85, compensation: "$3,200" },
    { id: 12, title: "Angular Connect", location: "London, UK", eventDate: "2025-10-05", status: 'accepted' as const, matchScore: 90, compensation: "$5,200" },
    { id: 13, title: "Node.js Interactive", location: "Vancouver, CA", eventDate: "2025-10-12", status: 'coming' as const, matchScore: 88, compensation: "$3,800" },
    { id: 14, title: "GraphQL Summit", location: "San Francisco, CA", eventDate: "2025-10-18", status: 'pending' as const, matchScore: 92, compensation: "$4,100" },
    { id: 15, title: "DevOps Days", location: "Berlin, DE", eventDate: "2025-11-02", status: 'coming' as const, matchScore: 86, compensation: "$3,500" },
    { id: 16, title: "Kubernetes Conference", location: "Paris, FR", eventDate: "2025-11-08", status: 'accepted' as const, matchScore: 94, compensation: "$5,800" },
    { id: 17, title: "Docker Summit", location: "Barcelona, ES", eventDate: "2025-11-15", status: 'pending' as const, matchScore: 89, compensation: "$4,200" },
    { id: 18, title: "Next.js Conf", location: "Online", eventDate: "2025-12-03", status: 'coming' as const, matchScore: 90, compensation: "$3,900" },
    { id: 19, title: "Serverless Days", location: "Dublin, IE", eventDate: "2026-01-14", status: 'pending' as const, matchScore: 84, compensation: "$3,300" },
    { id: 20, title: "AI & ML Summit", location: "Tokyo, JP", eventDate: "2026-02-20", status: 'accepted' as const, matchScore: 96, compensation: "$6,200" },
  ];

  // Get opportunities for selected date
  const getOpportunitiesForDate = (date: Date) => {
    return extendedOpportunities.filter(opp => 
      isSameDay(new Date(opp.eventDate), date)
    );
  };

  // Generate calendar grid
  const generateCalendarGrid = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const firstDayOfWeek = firstDay.getDay();
    const daysInMonth = lastDay.getDate();
    
    const days = [];
    
    // Previous month's trailing days
    for (let i = firstDayOfWeek - 1; i >= 0; i--) {
      const date = new Date(year, month, -i);
      days.push({ date, isCurrentMonth: false });
    }
    
    // Current month days
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(year, month, day);
      days.push({ date, isCurrentMonth: true });
    }
    
    // Next month's leading days
    const remainingCells = 42 - days.length;
    for (let day = 1; day <= remainingCells; day++) {
      const date = new Date(year, month + 1, day);
      days.push({ date, isCurrentMonth: false });
    }
    
    return days;
  };

  const calendarDays = generateCalendarGrid();
  const selectedDateOpportunities = selectedDate ? getOpportunitiesForDate(selectedDate) : [];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'accepted':
        return 'bg-green-500';
      case 'pending':
        return 'bg-yellow-500';
      case 'coming':
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + (direction === 'next' ? 1 : -1));
    setCurrentDate(newDate);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[85vh] overflow-y-auto bg-surface border-border">
        <DialogHeader>
          <DialogTitle className="text-foreground flex items-center gap-2">
            <Zap className="h-5 w-5 text-primary" />
            Speaking Opportunities Calendar
          </DialogTitle>
          <DialogDescription className="text-foreground-muted">
            View all opportunities organized by their event dates
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col h-full">
          {/* Calendar Header */}
          <div className="flex items-center justify-between mb-6">
            <Button variant="outline" onClick={() => navigateMonth('prev')}>
              ←
            </Button>
            <h3 className="text-xl font-semibold text-foreground">
              {format(currentDate, 'MMMM yyyy')}
            </h3>
            <Button variant="outline" onClick={() => navigateMonth('next')}>
              →
            </Button>
          </div>

          {/* Calendar Grid */}
          <div className="flex-1">
            {/* Days of Week Header */}
            <div className="grid grid-cols-7 gap-1 mb-2">
              {['SUNDAY', 'MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY', 'SATURDAY'].map((day) => (
                <div key={day} className="text-xs font-medium text-foreground-muted text-center py-2">
                  {day}
                </div>
              ))}
            </div>

            {/* Calendar Days */}
            <div className="grid grid-cols-7 gap-1 flex-1">
              {calendarDays.map((day, index) => {
                const dayOpportunities = getOpportunitiesForDate(day.date);
                const isToday = isSameDay(day.date, new Date());
                const isSelected = selectedDate && isSameDay(day.date, selectedDate);
                
                return (
                  <div
                    key={index}
                    className={`
                      min-h-[100px] border border-border p-1 cursor-pointer hover:bg-accent/50 transition-colors
                      ${!day.isCurrentMonth ? 'text-foreground-muted/50 bg-surface' : 'bg-background'}
                      ${isToday ? 'ring-2 ring-primary' : ''}
                      ${isSelected ? 'bg-primary/10' : ''}
                    `}
                    onClick={() => setSelectedDate(day.date)}
                  >
                    <div className="flex flex-col h-full">
                      <div className={`text-sm font-medium mb-1 ${isToday ? 'text-primary font-bold' : ''}`}>
                        {day.date.getDate()}
                      </div>
                      <div className="flex-1 space-y-1">
                        {dayOpportunities.slice(0, 3).map((opp) => (
                          <div
                            key={opp.id}
                            className={`
                              text-xs px-1 py-0.5 rounded text-white truncate
                              ${getStatusColor(opp.status)}
                            `}
                            title={opp.title}
                          >
                            {opp.title}
                          </div>
                        ))}
                        {dayOpportunities.length > 3 && (
                          <div className="text-xs text-foreground-muted">
                            +{dayOpportunities.length - 3} more
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Selected Date Details */}
          {selectedDate && (
            <div className="mt-6 p-4 bg-surface-elevated rounded-lg border border-border-subtle">
              <h4 className="font-semibold text-foreground mb-3">
                {format(selectedDate, 'EEEE, MMMM d, yyyy')}
              </h4>
              {selectedDateOpportunities.length > 0 ? (
                <div className="space-y-2 max-h-40 overflow-y-auto">
                  {selectedDateOpportunities.map((opp) => (
                    <div key={opp.id} className="flex items-center justify-between p-2 bg-background rounded border border-border">
                      <div className="flex-1">
                        <div className="font-medium text-sm text-foreground">{opp.title}</div>
                        <div className="text-xs text-foreground-muted flex items-center gap-2">
                          <MapPin className="h-3 w-3" />
                          {opp.location}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={`text-xs ${getStatusColor(opp.status)} text-white`}>
                          {opp.status}
                        </Badge>
                        <div className="text-xs font-bold text-primary">
                          {opp.matchScore}%
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4 text-foreground-muted">
                  <p className="text-sm">No opportunities scheduled</p>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="flex justify-end pt-4">
          <Button variant="outline" onClick={onClose}>
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}