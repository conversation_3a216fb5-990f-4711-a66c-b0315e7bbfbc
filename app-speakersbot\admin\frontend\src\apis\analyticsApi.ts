import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../store';

// Define the base URL for your API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

export const analyticsApi = createApi({
  reducerPath: 'analyticsApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Get the token from the state
      const token = (getState() as RootState).auth?.token;
      
      // If we have a token, set the authorization header
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      
      // Set content type
      headers.set('content-type', 'application/json');
      
      return headers;
    },
  }),
  tagTypes: ['Analytics'],
  endpoints: (builder) => ({
    getAnalytics: builder.query({
      query: (params = {}) => ({
        url: '/analytics',
        params,
      }),
      providesTags: ['Analytics'],
    }),
    
    getDashboardStats: builder.query({
      query: () => '/analytics/dashboard',
      providesTags: ['Analytics'],
    }),
  }),
});

export const {
  useGetAnalyticsQuery,
  useGetDashboardStatsQuery,
} = analyticsApi;
