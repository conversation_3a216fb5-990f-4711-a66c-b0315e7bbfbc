"""
Affiliate Scraper API
API endpoint for scraping affiliate information from URLs

This module provides REST API endpoints for scraping affiliate program information
from websites. It extracts various business details including logos, product images,
color schemes, learning objectives, challenges solved, offers, and compliance requirements.

Author: digitalspeakeragent.ai
Version: 1.0.0
"""

from fastapi import APIRouter, HTTPException, status, Query
from pydantic import BaseModel
from typing import Dict, Any, Optional
import logging
import time
from datetime import datetime
from app.services.affiliate_scraper_service import AffiliateService, ScraperHttpError
from app.config.logger import get_logger
logger = get_logger(__name__, file_name="scraper.log")

# Create FastAPI router for affiliate endpoints
router = APIRouter(prefix="/affiliate", tags=["Affiliate Scraper"])
affiliate_service = AffiliateService()

class AffiliateScrapeResponse(BaseModel):
    """
    Response model for affiliate scraping endpoint
    
    Attributes:
        success (bool): Whether the scraping operation was successful
        message (str): Human-readable message describing the result
        data (Dict[str, Any]): Scraped affiliate data in string format
        processing_time (float): Time taken to process the request in seconds
        timestamp (str): ISO timestamp when the response was generated
        error (Optional[str]): Error message if affiliate ID not found or other errors
    """
    success: bool
    message: str
    data: Dict[str, Any]
    processing_time: float
    timestamp: str
    error: Optional[str] = None

@router.post("/scraper", response_model=AffiliateScrapeResponse)
async def scrape_affiliate_website(
    url: str = Query(..., description="The URL of the website to scrape for affiliate information"),
    affiliate_id: Optional[str] = Query(None, description="Affiliate ID for tracking purposes")
) -> AffiliateScrapeResponse:
    """
    Scrape affiliate information from a given URL
    
    This endpoint extracts comprehensive affiliate program information from websites including:
    - Logo upload (JPG or PNG under 2MB)
    - Up to 2 product or service images (JPG or PNG under 2MB each)
    - Primary, secondary, and accent color hex codes (optional)
    - Font stack (optional)
    - 3 learning objectives or takeaways
    - 3 challenges solved
    - Overarching offer (one paragraph)
    - Differentiator (one paragraph)
    - Core benefit (one sentence)
    - Business entity type
    - Reach estimate
    - Promo channels
    - Tax docs (W-9 or W-8BEN)
    - Terms acceptance checkbox
    - Digital signature
    
    Args:
        url (str): The URL of the website to scrape (required query parameter)
        affiliate_id (str, optional): Affiliate ID for tracking purposes (optional query parameter)
        
    Returns:
        AffiliateScrapeResponse: Response with scraped data including affiliate_id
        
    Raises:
        HTTPException: 400 for invalid URL, 500 for scraping errors
    """
    # Record start time for performance tracking
    start_time = time.time()
    request_id = f"scrape_req_{int(time.time())}"
    
    try:
        # Input validation: Check for empty URL
        if not url or not url.strip():
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="URL cannot be empty")
        # Input validation: Check URL protocol and auto-prepend https:// for www. URLs
        if not url.startswith(('http://', 'https://')):
            if url.startswith('www.'):
                url = f"https://{url}"
            else:
                logger.warning(f"[{request_id}] Invalid URL format: {url}")
                raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="URL must start with http://, https://, or www.")
        
        # Call the affiliate service to perform the actual scraping
        scraped_data = affiliate_service.scrape_affiliate_data(url, affiliate_id)
        # Calculate processing time
        processing_time = time.time() - start_time
        # Check if there's an error in the scraped data (e.g., affiliate not found)
        error_message = scraped_data.get('error')
        success = error_message is None
        # Return response
        return AffiliateScrapeResponse(
            success=success,
            message=f"Successfully scraped affiliate data from {url}" if success else f"Scraped data from {url} but {error_message}",
            data=scraped_data,
            processing_time=round(processing_time, 2),
            timestamp=datetime.now().isoformat(),
            error=error_message
        )
        
    except ScraperHttpError as she:
        # Map upstream site HTTP errors to client-visible status codes
        processing_time = time.time() - start_time
        logger.warning(f"[{request_id}] Upstream site returned {she.status_code} for {url} after {processing_time:.2f}s")
        upstream_status = she.status_code or 502
        
        # Fast status code mapping
        status_map = {
            401: status.HTTP_403_FORBIDDEN,
            403: status.HTTP_403_FORBIDDEN,
            404: status.HTTP_404_NOT_FOUND,
            408: status.HTTP_504_GATEWAY_TIMEOUT,
            429: status.HTTP_429_TOO_MANY_REQUESTS,
        }
        mapped_status = status_map.get(upstream_status, 
                                     status.HTTP_502_BAD_GATEWAY if 500 <= upstream_status <= 599 
                                     else status.HTTP_400_BAD_REQUEST)
        
        raise HTTPException(status_code=mapped_status, detail={
            "success": False,
            "message": "Website blocked or rejected the request",
            "error": str(she),
            "upstream_status": she.status_code,
            "url": url,
        })
    except Exception as e:
        # Handle unexpected exceptions during scraping
        processing_time = time.time() - start_time
        logger.error(f"[{request_id}] Error scraping affiliate data after {processing_time:.2f}s: {str(e)}", exc_info=True)
        
        # Provide user-friendly error messages
        error_message = str(e)
        if "timeout" in error_message.lower():
            error_message = f"Website timeout: {url} took too long to respond"
        elif "connection" in error_message.lower():
            error_message = f"Connection error: Unable to connect to {url}"
        elif "404" in error_message or "not found" in error_message.lower():
            error_message = f"Page not found: {url}"
        else:
            error_message = f"Error scraping affiliate data: {error_message}"
            
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail={
            "success": False,
            "message": error_message,
            "url": url
        })