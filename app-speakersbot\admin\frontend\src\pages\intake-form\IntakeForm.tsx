import { <PERSON><PERSON>, <PERSON>ertDes<PERSON>, AlertTitle } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { Combobox } from "@/components/ui/combobox";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogFooter,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "@/components/ui/use-toast";
import {
  DeleteOutlined,
  EditOutlined,
  FormOutlined,
  InfoCircleOutlined,
  PlusOutlined,
  SaveOutlined,
} from "@ant-design/icons";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useAppState } from "../../state/AppStateProvider";
import { useAuth } from "../../state/AuthContext";
import type { IntakeFormField, IntakeFormVersion, UserRole } from "../../types";
import { getPermissions } from "../../utils/permissions";
import SpeakerIntakeForm from "./components/SpeakerIntakeForm";
import ButtonLoader from "@/components/common/ButtonLoader";
import {
  useCreateFormFieldMutation,
  useUpdateFormFieldMutation,
  useDeleteFormFieldMutation,
} from "../../apis/formApi";
dayjs.extend(relativeTime);

// Minimal default sections used to seed an initial version when empty
const FORM_SECTIONS: Record<
  string,
  Array<{
    id: string;
    name: string;
    label: string;
    type: string;
    required: boolean;
    options?: string[];
  }>
> = {
  "Personal Information": [
    {
      id: "name",
      name: "name",
      label: "Full Name",
      type: "text",
      required: true,
    },
    {
      id: "email",
      name: "email",
      label: "Email Address",
      type: "email",
      required: true,
    },
  ],
};

const IntakeForm: React.FC = () => {
  const { intakeForm, dispatch } = useAppState();
  const { user } = useAuth();
  const permissions = getPermissions(user?.roleId as unknown as UserRole);

  // State management
  const [isPreviewModalVisible, setIsPreviewModalVisible] = useState(false);
  const [isFieldModalVisible, setIsFieldModalVisible] = useState(false);
  const [isStepModalVisible, setIsStepModalVisible] = useState(false);
  const [editingField, setEditingField] = useState<IntakeFormField | null>(
    null
  );
  const [currentSection, setCurrentSection] = useState<{
    id: string;
    title: string;
  }>({ id: "", title: "" });
  const [fieldOptions, setFieldOptions] = useState<string[]>([]);
  const fieldForm = useForm<any>({ defaultValues: {} });
  const stepFormRHF = useForm<any>({ defaultValues: {} });
  const [createFormField, { isLoading: isCreatingField }] =
    useCreateFormFieldMutation();
  const [updateFormField, { isLoading: isUpdatingField }] =
    useUpdateFormFieldMutation();
  const [deleteFormField, { isLoading: isDeletingField }] =
    useDeleteFormFieldMutation();

  // Effect to ensure form is properly populated when editing
  useEffect(() => {
    if (editingField && isFieldModalVisible) {
      // Use originalType if available (from API), otherwise fallback to type
      const fieldTypeToUse =
        (editingField as any).originalType || editingField.type;

      // Force re-set all values to ensure they're properly populated
      fieldForm.setValue("label", editingField.label || "");
      fieldForm.setValue("type", fieldTypeToUse || "");
      fieldForm.setValue("required", editingField.required || false);
      fieldForm.setValue("placeholder", editingField.placeholder || "");
      fieldForm.setValue("section", editingField.section || "");
      fieldForm.setValue("name", editingField.name || "");
    }
  }, [editingField, isFieldModalVisible, fieldForm]);

  const activeVersion = intakeForm.activeVersion;

  // Initialize default sections if none exist
  useEffect(() => {
    if (!activeVersion || activeVersion.fields.length === 0) {
      const defaultFields: IntakeFormField[] = [];
      let order = 1;

      Object.entries(FORM_SECTIONS).forEach(([sectionName, sectionFields]) => {
        sectionFields.forEach((fieldTemplate) => {
          const field: IntakeFormField = {
            id: `${fieldTemplate.id}_${Date.now()}_${order}`,
            name: fieldTemplate.name,
            label: fieldTemplate.label,
            type: fieldTemplate.type as any,
            required: fieldTemplate.required,
            order: order++,
            section: sectionName,
            options: fieldTemplate.options,
          };
          defaultFields.push(field);
        });
      });

      if (defaultFields.length > 0) {
        const newVersion: IntakeFormVersion = {
          id: `version_${Date.now()}`,
          version: 1,
          fields: defaultFields,
          isActive: true,
          createdAt: dayjs().toISOString(),
          author: user?.email || "System",
        };
        dispatch({ type: "UPDATE_INTAKE_FORM", payload: newVersion });
      }
    }
  }, [activeVersion, dispatch, user]);

  const handleSaveNewVersion = () => {
    if (!activeVersion) return;

    dispatch({ type: "SAVE_INTAKE_VERSION" });
    toast({
      title: "New form version saved",
      description: "New form version saved successfully",
    });
  };

  const handleAddField = (section?: { id: string; title: string }) => {
    setEditingField(null);
    setCurrentSection(section || { id: "", title: "" });
    setFieldOptions([]);
    fieldForm.reset();
    if (section) {
      fieldForm.setValue("section", section.id);
    }
    setIsFieldModalVisible(true);
  };

  const handleStepSubmit = (values: any) => {
    // Add new section to FORM_SECTIONS or create a new section
    const newSectionName = values.stepName;
    toast({
      title: "Step added",
      description: `Step "${newSectionName}" added successfully`,
    });
    setIsStepModalVisible(false);
    stepFormRHF.reset();
  };

  const handleEditField = (field: IntakeFormField) => {
    setEditingField(field);

    // Set all field values including type
    Object.entries(field).forEach(([key, value]) => {
      fieldForm.setValue(key as any, value as any);
    });

    // Use originalType if available (from API), otherwise fallback to type
    const fieldTypeToUse = (field as any).originalType || field.type;
    if (fieldTypeToUse) {
      fieldForm.setValue("type", fieldTypeToUse);
    }

    // Initialize field options for editing
    if (field.options && Array.isArray(field.options)) {
      setFieldOptions(field.options);
    } else if (field.options && typeof field.options === "string") {
      setFieldOptions(
        (field.options as string).split("|").map((opt) => opt.trim())
      );
    } else {
      setFieldOptions([]);
    }
    setIsFieldModalVisible(true);
  };

  const handleDeleteField = async (fieldId: string) => {
    try {
      // Call the API to delete the field
      const response = await deleteFormField(fieldId).unwrap();

      if (response) {
        // Update local state
        dispatch({ type: "DELETE_INTAKE_FIELD", payload: fieldId });

        // Trigger refetch of forms and fields
        window.dispatchEvent(new CustomEvent("fieldUpdated"));
        toast({
          title: "Field deleted",
          description: "Field deleted successfully",
        });
      }
    } catch (error) {
      console.error("Failed to delete field:", error);
      toast({
        title: "Error",
        description: "Failed to delete field. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleFieldSubmit = async (values: any) => {
    try {
      let response;

      if (editingField) {
        // Update existing field - field_type cannot be changed
        const updatePayload = {
          fieldData: {
            question: values.label,
            placeholder: values.placeholder || "",
            is_required: Boolean(values.required),
            options: fieldOptions.length > 0 ? fieldOptions : undefined,
          },
        };

        response = await updateFormField({
          id: editingField.id,
          ...updatePayload,
        }).unwrap();
      } else {
        // Create new field
        const createPayload = {
          fieldData: {
            question: values.label,
            field_type: values.type,
            is_required: Boolean(values.required),
            placeholder: values.placeholder || "",
            options: fieldOptions.length > 0 ? fieldOptions : undefined,
          },
        };

        response = await createFormField({
          formId: currentSection.id,
          ...createPayload,
        }).unwrap();
      }

      // Update local state
      const fieldData: IntakeFormField = {
        ...values,
        section: values.section || currentSection.id,
        id: response.data?.id || editingField?.id || `field_${Date.now()}`,
        order: editingField?.order || (activeVersion?.fields.length || 0) + 1,
        options: fieldOptions.length > 0 ? fieldOptions : undefined,
      };

      if (editingField) {
        dispatch({ type: "UPDATE_INTAKE_FIELD", payload: fieldData });
        toast({
          title: "Field updated",
          description: `Field updated in "${currentSection.title}" step`,
        });
      } else {
        dispatch({ type: "ADD_INTAKE_FIELD", payload: fieldData });
        toast({
          title: "Field added",
          description: `Field added to "${currentSection.title}" step successfully`,
        });
      }

      // Trigger refetch of forms and fields
      // The SpeakerIntakeForm component will handle the actual refetch
      window.dispatchEvent(new CustomEvent("fieldUpdated"));

      setIsFieldModalVisible(false);
      fieldForm.reset();
      setFieldOptions([]);
    } catch (error) {
      console.error("Failed to save field:", error);
      toast({
        title: "Error",
        description: `Failed to ${
          editingField ? "update" : "create"
        } field. Please try again.`,
        variant: "destructive",
      });
    }
  };

  const addFieldOption = () => {
    setFieldOptions([...fieldOptions, ""]);
  };

  const removeFieldOption = (index: number) => {
    setFieldOptions(fieldOptions.filter((_, i) => i !== index));
  };

  const updateFieldOption = (index: number, value: string) => {
    const newOptions = [...fieldOptions];
    newOptions[index] = value;
    setFieldOptions(newOptions);
  };

  // Group fields by section
  const fieldsBySection =
    activeVersion?.fields.reduce((acc, field) => {
      const section = field.section || "Uncategorized";
      if (!acc[section]) acc[section] = [];
      acc[section].push(field);
      return acc;
    }, {} as Record<string, IntakeFormField[]>) || {};

  return (
    <>
      {/* // <InvalidTokenHandler error={matchesError} /> */}
      <div>
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="m-0 text-2xl font-bold text-foreground">
              Manage STAiGENT Identity Profile
            </h1>
            <p className="text-sm text-muted-foreground mt-3">
              Configure and manage speaker STAiGENT Identity Profile fields and versions
            </p>
          </div>
        </div>

        {/* Speaker Intake Form */}
        <SpeakerIntakeForm
          onAddField={handleAddField}
          onEditField={handleEditField}
          onDeleteField={handleDeleteField}
          isDeletingField={isDeletingField}
          onFieldUpdate={() => {
            // This will be called when forms data changes
            // The SpeakerIntakeForm will handle the refetch internally
          }}
        />

        {/* Preview Dialog (shadcn) */}
        <Dialog
          open={isPreviewModalVisible}
          onOpenChange={setIsPreviewModalVisible}
        >
          <DialogContent className="max-w-[90vw]">
            <DialogHeader>
              <DialogTitle>Form Preview</DialogTitle>
            </DialogHeader>
            <Alert className="mb-4">
              <AlertTitle>Preview Mode</AlertTitle>
              <AlertDescription>
                This is a read-only preview. Form submissions are disabled.
              </AlertDescription>
            </Alert>
            <SpeakerIntakeForm
              onAddField={handleAddField}
              onEditField={handleEditField}
              onDeleteField={handleDeleteField}
              isDeletingField={isDeletingField}
            />
            <DialogFooter>
              <div className="flex w-full justify-end gap-2">
                <Button onClick={() => setIsPreviewModalVisible(false)}>
                  Close
                </Button>
                <Button
                  onClick={() => {
                    setIsPreviewModalVisible(false);
                  }}
                  disabled={!permissions.canEdit}
                  variant="secondary"
                >
                  <EditOutlined />
                  Edit Form
                </Button>
                <Button
                  onClick={() => {
                    handleSaveNewVersion();
                    setIsPreviewModalVisible(false);
                  }}
                  disabled={!permissions.canEdit}
                >
                  <SaveOutlined />
                  Save as New Version
                </Button>
              </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Add/Edit Field Modal */}
        <Dialog
          open={isFieldModalVisible}
          onOpenChange={(open) => {
            setIsFieldModalVisible(open);
            if (!open) {
              fieldForm.reset();
            }
          }}
        >
          <DialogContent className="max-h-[90vh] flex flex-col overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-base font-semibold">
                <div className="flex items-center gap-2">
                  <PlusOutlined />
                  {editingField ? "Edit Field" : "Add New Field"}
                </div>
              </DialogTitle>
            </DialogHeader>
            <Alert className="mb-4 bg-muted">
              <AlertTitle className="text-base font-normal flex items-center gap-2">
                <InfoCircleOutlined />
                {`Adding Field to: ${currentSection.title || "Selected Step"}`}
              </AlertTitle>
              <AlertDescription className="text-dashboard-light">
                The field will be added to the step from which the Add Field
                button was clicked.
              </AlertDescription>
            </Alert>
            <Form {...fieldForm}>
              <form
                onSubmit={fieldForm.handleSubmit(handleFieldSubmit)}
                className="space-y-6"
              >
                <div className="grid grid-cols-2 gap-4">
                  <div className={editingField ? "col-span-1" : "col-span-2"}>
                    <FormField
                      control={fieldForm.control}
                      name="label"
                      rules={{ required: "Field label is required" }}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Field Label *</FormLabel>
                          <FormControl>
                            <Input
                              className="bg-input-secondary placeholder:text-dashboard-light border-dashboard-light/40"
                              placeholder="Enter field label"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  {editingField && (
                    <div className="col-span-1">
                      <FormField
                        control={fieldForm.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Field Name</FormLabel>
                            <FormControl>
                              <Input
                                className="bg-input-secondary placeholder:text-dashboard-light border-dashboard-light/40"
                                placeholder="fieldName"
                                disabled
                                readOnly
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="col-span-2">
                    <FormField
                      control={fieldForm.control}
                      name="type"
                      rules={{ required: "Please select field type" }}
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Field Type *</FormLabel>
                          <FormControl>
                            <Select
                              value={field.value || ""}
                              onValueChange={field.onChange}
                              disabled={editingField ? true : false}
                            >
                              <SelectTrigger className="bg-input-secondary placeholder:text-dashboard-light border-dashboard-light/40">
                                <SelectValue placeholder="Select field type" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="Text">Text</SelectItem>
                                <SelectItem value="Textarea">
                                  Textarea
                                </SelectItem>
                                <SelectItem value="Number">Number</SelectItem>
                                <SelectItem value="Select">Select</SelectItem>
                                <SelectItem value="Multi-Select">
                                  Multi-Select
                                </SelectItem>
                                <SelectItem value="Multi-Text">
                                  Multi-Text
                                </SelectItem>
                                <SelectItem value="Radio">Radio</SelectItem>
                                <SelectItem value="File-upload">
                                  File-upload
                                </SelectItem>
                                <SelectItem value="Date">Date</SelectItem>
                              </SelectContent>
                            </Select>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                {/* Hidden field to store the section */}
                <FormField
                  control={fieldForm.control}
                  name="section"
                  render={({ field }) => <input type="hidden" {...field} />}
                />

                <div className="grid grid-cols-2 gap-4">
                  <div className="col-span-1">
                    <FormField
                      control={fieldForm.control}
                      name="required"
                      render={({ field }) => (
                        <FormItem className="flex flex-col gap-2">
                          <FormLabel>Make Required Field</FormLabel>
                          <FormControl>
                            <Switch
                              checked={!!field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="col-span-1">
                    <FormField
                      control={fieldForm.control}
                      name="placeholder"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Placeholder Text</FormLabel>
                          <FormControl>
                            <Input
                              className="bg-input-secondary placeholder:text-dashboard-light border-dashboard-light/40"
                              placeholder="Enter helpful placeholder text"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <FormField
                  control={fieldForm.control}
                  name="options"
                  render={({ field }) => {
                    const fieldType = fieldForm.watch("type");
                    const fieldLabel = fieldForm.watch("label");
                    const needsOptions = [
                      "Select",
                      "Multi-Select",
                      "Multi-Text",
                      "Radio",
                    ].includes(fieldType);

                    // Hide Field Options for Primary Category and Subcategory fields
                    const shouldHideFieldOptions = fieldLabel === "Primary Category" || fieldLabel === "Subcategory";

                    if (!needsOptions || shouldHideFieldOptions) return null;

                    return (
                      <FormItem>
                        <FormLabel>
                          Field Options
                          <span className="ml-1 text-xs text-muted-foreground">
                            (Add options for {fieldType})
                          </span>
                        </FormLabel>
                        <div className="space-y-2">
                          {fieldOptions.map((option, index) => (
                            <div
                              key={index}
                              className="flex items-center gap-2"
                            >
                              <Input
                                className="bg-input-secondary placeholder:text-dashboard-light border-dashboard-light/40"
                                placeholder={`Option ${index + 1}`}
                                value={option}
                                onChange={(e) =>
                                  updateFieldOption(index, e.target.value)
                                }
                              />
                              <Button
                                type="button"
                                variant="outline"
                                size="sm"
                                onClick={() => removeFieldOption(index)}
                                className="text-red-500 hover:text-red-700 hover:bg-red-50"
                              >
                                <DeleteOutlined />
                              </Button>
                            </div>
                          ))}
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={addFieldOption}
                            className="w-full border-dashed"
                          >
                            <PlusOutlined className="mr-2" />
                            Add Option
                          </Button>
                        </div>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />

                <div className="mb-0">
                  <div className="w-full flex items-center justify-end gap-4">
                    <Button
                      size="lg"
                      variant="outline"
                      type="button"
                      disabled={isCreatingField || isUpdatingField}
                      onClick={() => {
                        setIsFieldModalVisible(false);
                        fieldForm.reset();
                      }}
                    >
                      Cancel
                    </Button>
                    <ButtonLoader
                      size="lg"
                      type="submit"
                      loading={isCreatingField || isUpdatingField}
                      disabled={isCreatingField || isUpdatingField}
                    >
                      {editingField ? <EditOutlined /> : <PlusOutlined />}
                      {editingField ? "Update Field" : "Confirm & Add Field"}
                    </ButtonLoader>
                  </div>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>

        {/* Add Step Dialog */}
        <Dialog open={isStepModalVisible} onOpenChange={setIsStepModalVisible}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Step</DialogTitle>
            </DialogHeader>
            <Form {...stepFormRHF}>
              <form onSubmit={stepFormRHF.handleSubmit(handleStepSubmit)}>
                <FormField
                  control={stepFormRHF.control}
                  name="stepName"
                  rules={{ required: "Please enter step name" }}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Step Name</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter step name (e.g., Contact Information)"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div className="mb-0">
                  <div className="w-full justify-end">
                    <Button
                      type="button"
                      onClick={() => setIsStepModalVisible(false)}
                    >
                      Cancel
                    </Button>
                    <Button type="submit">Confirm</Button>
                  </div>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
};

export default IntakeForm;
