import * as React from "react"

import { cn } from "@/lib/utils"

type InputProps = React.ComponentProps<"input"> & {
  startIcon?: React.ReactNode
  endIcon?: React.ReactNode
}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, startIcon, endIcon, ...props }, ref) => {
    const inputElement = (
      <input
        type={type}
        className={cn(
          "flex h-10 w-full rounded-lg border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-foreground focus-visible:outline-none focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
          startIcon && "pl-9",
          endIcon && "pr-9",
          className
        )}
        ref={ref}
        {...props}
      />
    )

    if (!startIcon && !endIcon) return inputElement

    return (
      <div className={cn("relative w-full", props.disabled && "opacity-50")}> 
        {startIcon ? (
          <span className="pointer-events-none absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
            {startIcon}
          </span>
        ) : null}
        {endIcon ? (
          <span className="pointer-events-none absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground">
            {endIcon}
          </span>
        ) : null}
        {inputElement}
      </div>
    )
  }
)
Input.displayName = "Input"

export { Input }
