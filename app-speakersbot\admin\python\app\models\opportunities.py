from app.config.config import config
from sqlalchemy import create_engine, Column, String, Text, Date, Boolean, Integer, DateTime
from datetime import datetime
from app.models.base import Base

class SpeakerOpportunity(Base):
    __tablename__ = "opportunities"
    id = Column(Integer, primary_key=True, autoincrement=True, comment="Primary key for the opportunity/event")
    opportunity_url_id = Column(Integer, nullable=True, comment="ID of the scraped URL from opportunities_url table")
    title = Column(Text, comment="Title of the opportunity/event")
    organization = Column(String(255), comment="Name of the organizing entity")
    event_type = Column(String(255), comment="Type of the event (e.g., conference, webinar)")
    start_date = Column(Date, comment="Event start date")
    end_date = Column(Date, comment="Event end date")
    event_url = Column(Text, comment="URL to the event page")
    source_url = Column(Text, comment="Source URL where the opportunity was found")
    city = Column(String(255), comment="City where the event is held")
    state = Column(String(255), comment="State where the event is held")
    country = Column(String(255), comment="Country where the event is held")
    venue = Column(Text, comment="Venue of the event")
    is_virtual = Column(Boolean, default=False, comment="Indicates if the event is virtual")
    is_active = Column(Boolean, default=False, comment="Indicates if the opportunity is active")
    description = Column(Text, comment="Description of the opportunity/event")
    industry = Column(Text, comment="Industry related to the opportunity")
    search_query = Column(Text, comment="Search query used to find the opportunity")
    feed_back = Column(Text, comment="Feedback or notes about the opportunity")
    source = Column(String(255), comment="Source of the opportunity (e.g., brave, serpapi, tavily)")
    applied_by = Column(Date, nullable=True, comment="Speaker application deadline date")
    tag = Column(String(255), nullable=True, comment="Tag to categorize opportunity status (e.g., incomplete, complete)")
    subcategory = Column(String(255), nullable=True, comment="Subcategory extracted from scraped content for better matching")
    created_at = Column(DateTime, default=datetime.utcnow, comment="Record creation timestamp")
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="Record last update timestamp")
    deleted_at = Column(DateTime, nullable=True, comment="Record deletion timestamp(soft delete)")

def create_speaker_opportunities_table():
    db_url = config.get_database_url()
    engine = create_engine(db_url)
    Base.metadata.create_all(engine)

if __name__ == "__main__":
    create_speaker_opportunities_table()
    print("Table 'speaker_opportunities' created in MySQL database")
