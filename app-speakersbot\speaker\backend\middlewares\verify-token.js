const jwtHelper = require('../helpers/jwt-helper');
const { Speakers } = require("../models");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES } = require("../helpers/response-codes");

// Middleware to verify JWT and extract speaker information
async function verifyToken(req, res, next) {
    try {
        const authHeader = req.headers['authorization'];
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, 'No token provided');
        }
        
        const token = authHeader.split(' ')[1];
        const decoded = jwtHelper.verifyToken(token);

        if (!decoded || decoded instanceof Error) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, 'Invalid or expired token');
        }

        // For speakers, verify the speaker exists and is active
        const speaker = await Speakers.findByPk(decoded.id);
        if (!speaker) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, 'Speaker not found');
        }

        if (speaker.status !== 'active') {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, 'Speaker account is not active');
        }

        // Set user information for use in controllers
        req.user = {
            id: speaker.id,
            email: speaker.email,
            name: speaker.name,
            userType: 'speaker'
        };

        // Legacy support
        req.userId = decoded.id;

        next();
    } catch (error) {
        next(error);
    }
}

module.exports = verifyToken;
