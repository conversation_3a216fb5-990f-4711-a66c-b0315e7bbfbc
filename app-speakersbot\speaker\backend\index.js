const express = require("express");
const cors = require('cors');
const db = require("./models");
const path = require("path");
const swaggerUi = require('swagger-ui-express');
const yaml = require('yamljs');
const swaggerDocument = yaml.load(path.join(__dirname, './openapi-spec.yaml'));
const { exceptionHandling } = require("./middlewares/exceptionalHandling");
const verifyToken = require("./middlewares/verify-token");

db.sequelize.sync({ alter: false }).then(() => {
    console.log("Synced DB");
}).catch((err) => {
    console.error("Failed to sync DB:", err);
});


const app = express();
const port = process.env.PORT || 3000;
app.use(cors());

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

app.use("/speaker/api/v1", verifyToken);

app.set('trust proxy', true);
// health check
app.get('/health', (req, res)=> {
    return res.status(200).json({ message: 'OK' });
})

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

require("./routes/auth-route")(app)
require("./routes/opportunity-route")(app)
require("./routes/speaker-route")(app)
require("./routes/subscription-route")(app)
require("./routes/gamification-route")(app)
require("./routes/affiliate-inquiries-route")(app)
require("./routes/affiliate-route")(app)
require("./routes/dashboard-route")(app)
require("./routes/early-access-landing-route")(app)

app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument));


const landingBuildDir = path.join(__dirname, 'dist', 'landing');
 
app.use(express.static(landingBuildDir));
app.get('/*splat', (req, res) => {
    res.sendFile(path.join(landingBuildDir, 'index.html'));
});

app.use(exceptionHandling) 



app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}/`);
});

// Handle uncaught exceptions to prevent server crashes
process.on('uncaughtException', (err) => {
    console.error('Uncaught Exception:', err);
    process.exit(1);

});
