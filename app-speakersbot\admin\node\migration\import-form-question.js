const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const {FormQuestion} = require('../models');
const connection = require("../models/connection");

const csvFilePath = path.join(__dirname, 'csv', 'form_questions.csv');

async function insertFormQuestions() {
  try {
    await connection.authenticate();
    console.log('Database connection established.');

    const formQuestions = [];

    // Check if CSV file exists
    if (!fs.existsSync(csvFilePath)) {
      console.error(`CSV file not found at: ${csvFilePath}`);
      console.log('Please create a CSV file with the following columns:');
      console.log('id,question,form_type_id,field_type,field_id,is_required,options_json');
      console.log('Example:');
      console.log('1,What is your name?,1,text,name,true,');
      console.log('2,What is your email?,1,text,email,true,');
      console.log('3,Select your expertise area,2,select,expertise,true,"[{""value"":""tech"",""label"":""Technology""},{""value"":""business"",""label"":""Business""}]"');
      process.exit(1);
    }

    // Map CSV headers to model fields
    const headerMap = {
      'id': 'id', // We'll ignore this and use auto-increment
      'question': 'question',
      'form_type_id': 'form_type_id',
      'field_type': 'field_type',
      'field_id': 'field_id',
      'is_required': 'is_required',
      'options_json': 'options_json'
    };

    // Helper to convert values to correct types
    function convertValue(key, value) {
      if (value === undefined || value === null) return null;
      value = value.trim();
      if (value === '') return null;
      
      if (key === 'form_type_id') {
        const n = parseInt(value, 10);
        return isNaN(n) ? null : n;
      }
      
      if (key === 'is_required') {
        if (typeof value === 'string') {
          const v = value.toLowerCase();
          if (['yes', 'true', '1', 'required'].includes(v)) return true;
          if (['no', 'false', '0', 'optional'].includes(v)) return false;
          return false; // Default to false
        }
        return Boolean(value);
      }
      
      if (key === 'field_type') {
        const validTypes = ['select', 'text', 'radio', 'file-upload'];
        const valueLower = value.toLowerCase();
        
        // Map common field types to valid enum values
        const typeMapping = {
          'string': 'text',
          'text': 'text',
          'textarea': 'text',
          'select': 'select',
          'dropdown': 'select',
          'radio': 'radio',
          'file': 'file-upload',
          'upload': 'file-upload',
          'file-upload': 'file-upload'
        };
        
        const mappedType = typeMapping[valueLower] || valueLower;
        if (validTypes.includes(mappedType)) {
          return mappedType;
        }
        return 'text'; // Default to text
      }
      
      return value;
    }

    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('data', (row) => {
        // Map CSV row to model fields and convert types
        const mapped = {};
        for (const [csvKey, modelKey] of Object.entries(headerMap)) {
          if (row.hasOwnProperty(csvKey) && modelKey !== 'id') { // Skip id field
            mapped[modelKey] = convertValue(modelKey, row[csvKey]);
          }
        }
        
        // Add timestamps
        mapped.created_at = new Date();
        mapped.updated_at = new Date();
        
        formQuestions.push(mapped);
      })
      .on('end', async () => {
        console.log(`Read ${formQuestions.length} form questions from CSV.`);

        let success = 0;
        let fail = 0;

        for (const formQuestionData of formQuestions) {
          try {
            // Check if form question already exists to avoid duplicates
            const existingFormQuestion = await FormQuestion.findOne({
              where: { field_id: formQuestionData.field_id }
            });

            if (existingFormQuestion) {
              console.log(`Form question with field_id "${formQuestionData.field_id}" already exists, skipping...`);
              continue;
            }

            await FormQuestion.create(formQuestionData);
            console.log(`✓ Inserted form question: ${formQuestionData.question} (Field ID: ${formQuestionData.field_id})`);
            success++;
          } catch (err) {
            fail++;
            console.error(`✗ Failed to insert form question "${formQuestionData.question}":`, err.message);
          }
        }

        console.log(`\nForm question insertion complete!`);
        console.log(`Success: ${success}`);
        console.log(`Failed: ${fail}`);
        console.log(`Total processed: ${formQuestions.length}`);

        // Display all form questions in the database
        const allFormQuestions = await FormQuestion.findAll({
          order: [['form_type_id', 'ASC'], ['id', 'ASC']]
        });
        
        console.log('\nCurrent form questions in database:');
        allFormQuestions.forEach((formQuestion, index) => {
          console.log(`${index + 1}. [Form Type ID: ${formQuestion.form_type_id}] ${formQuestion.question} (${formQuestion.field_type})`);
        });

        process.exit(0);
      })
      .on('error', (error) => {
        console.error('Error reading CSV file:', error);
        process.exit(1);
      });
  } catch (error) {
    console.error('Unable to connect to the database:', error);
    process.exit(1);
  }
}

// Run the migration
insertFormQuestions();
