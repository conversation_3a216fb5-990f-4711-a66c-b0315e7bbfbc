"""
Dynamic Discovery Worker for Speaker Opportunities
This worker orchestrates the discovery of speaker opportunity URLs using
dynamic topic distribution across multiple search engines.
Key Features:
- Dynamic topic distribution across search engines
- URL discovery and storage
- Comprehensive filtering and validation
- Proper error handling and logging
- Clean separation of concerns
Author: Speaker Bot Team
Version: 1.0
Last Updated: 2025
"""

import asyncio
from datetime import datetime
from typing import List, Dict, Set, Optional
import sys
import os

# Add app directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.config.config import config
from app.config.logger import get_logger
from app.models.opportunities_url import OpportunitiesUrl
from app.helpers.database_manager import db_manager
from sqlalchemy.exc import SQLAlchemyError

# Import services and helper modules
from app.services.topic_distribution_service import TopicDistributionService
from app.helpers.llm_query_generator import build_dynamic_queries
from app.helpers.database import log_scraping_result
from app.helpers.search_providers import (
    tavily_search, exa_search
)
from app.helpers.utils import (
    is_job_related, is_event_related, normalize_text
)
from app.helpers.constants import DROP_DOMAINS

logger = get_logger(__name__, file_name="scraper.log")

class DiscoveryWorker:
    """
    Dynamic discovery worker for URL collection using topic distribution.
    This class orchestrates the discovery process by:
    1. Getting topic distribution from TopicDistributionService
    2. Searching for URLs using multiple search engines
    3. Filtering and validating results
    4. Storing valid URLs in the database
    """
    
    def __init__(self):
        """Initialize the discovery worker with database connection and services."""
        self.Session = db_manager.SessionLocal
        self.topic_service = TopicDistributionService()
        
        # Search engine mapping
        self.search_engines = {
            "tavily": tavily_search,
            "exa": exa_search,
        }
    
    def _is_blocked_domain(self, url: str) -> bool:
        """
        Check if URL is from a blocked domain (job boards, social media).
        Args:
            url (str): URL to check
        Returns:
            bool: True if domain should be blocked
        """
        try:
            host = url.split("//", 1)[-1].split("/", 1)[0].lower()
            return any(domain in host for domain in DROP_DOMAINS)
        except Exception:
            return False
    
    async def _search_with_engine(self, engine: str, query: str) -> tuple[List[Dict], str]:
        """
        Search using the specified engine with proper error handling.
        Args:
            engine: Name of the search engine
            query: Search query string
        Returns:
            Tuple of (search results, error_message)
            - If successful: (results, "")
            - If failed: ([], error_description)
        """
        try:
            if engine not in self.search_engines:
                error_msg = f"Unknown search engine: {engine}"
                logger.warning(error_msg)
                return [], error_msg
            
            search_function = self.search_engines[engine]
            
            # Set result count based on engine
            if engine == "tavily":
                want_results = 20
            elif engine == "exa":
                want_results = 50
            else:
                want_results = 20  # Default fallback
           
            results = await search_function(query, want_results=want_results)
            return results, ""
                
        except Exception as e:
            error_msg = str(e)
            
            # Categorize the error for better logging
            if "rate limit" in error_msg.lower() or "quota" in error_msg.lower() or "429" in error_msg:
                categorized_error = f"Rate limit exceeded for {engine}: {error_msg}"
            elif "timeout" in error_msg.lower() or "connection" in error_msg.lower():
                categorized_error = f"Connection timeout for {engine}: {error_msg}"
            elif "api key" in error_msg.lower() or "unauthorized" in error_msg.lower() or "401" in error_msg:
                categorized_error = f"API authentication failed for {engine}: {error_msg}"
            elif "invalid" in error_msg.lower() or "422" in error_msg:
                categorized_error = f"Invalid request for {engine}: {error_msg}"
            elif "server error" in error_msg.lower() or "500" in error_msg:
                categorized_error = f"Server error for {engine}: {error_msg}"
            else:
                categorized_error = f"Search failed for {engine}: {error_msg}"
            
            logger.error(f"Search failed for {engine} with query '{query[:50]}...': {categorized_error}")
            return [], categorized_error
    
    def _get_existing_urls(self) -> Set[str]:
        """
        Get existing URLs from database to avoid duplicates.
        Returns:
            Set of existing URLs
        """
        try:
            with db_manager.get_session() as session:
                urls = {url for url, in session.query(OpportunitiesUrl.url).all()}
                return urls
        except SQLAlchemyError as e:
            logger.error(f"Database error retrieving existing URLs: {e}")
            return set()
        except Exception as e:
            logger.error(f"Unexpected error retrieving existing URLs: {e}")
            return set()
    
    def _save_urls(self, urls: List[Dict], topic: str, engine: str) -> int:
        """
        Save URLs to opportunities_url table.
        Args:
            urls: List of URL data dictionaries
            topic: Topic name
            engine: Search engine name
        Returns:
            Number of URLs successfully saved
        """
        if not urls:
            return 0
        
        try:
            with db_manager.get_session() as session:
                saved_count = 0
                
                for url_data in urls:
                    url = url_data.get("url", "").strip()
                    if not url:
                        continue
                    
                    opportunity = OpportunitiesUrl(
                        url=url,
                        topic=topic,
                        browser=engine,
                        status="pending",
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    session.add(opportunity)
                    saved_count += 1
                session.commit()
                return saved_count
                
        except SQLAlchemyError as e:
            logger.error(f"Database error saving URLs for topic '{topic}': {e}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error saving URLs for topic '{topic}': {e}")
            return 0
    
    def _filter_search_results(self, search_results: List[Dict], existing_urls: Set[str], topic: str) -> List[Dict]:
        """
        Apply comprehensive filtering to search results.
        Args:
            search_results: Raw search results from search engine
            existing_urls: Set of URLs already in database
            topic: Topic name for context
        Returns:
            List of filtered and validated URL data
        """
        filtered_urls = []
        
        for result in search_results:
            url = normalize_text(result.get("url", ""))
            title = normalize_text(result.get("title", ""))
            snippet = normalize_text(result.get("content", ""))
            
            # Skip if no URL
            if not url:
                continue
            # Skip if already exists
            if url in existing_urls:
                continue
            # Skip blocked domains
            if self._is_blocked_domain(url):
                continue
            # Skip job-related content
            if is_job_related(title, snippet):
                continue
            
            # Skip non-event content
            if not is_event_related(title, snippet):
                continue
            
            # Add to filtered results
            filtered_urls.append({
                "url": url,
                "title": title,
                "content": snippet,
                "topic": topic
            })
        
        logger.debug(f"Filtered {len(filtered_urls)} valid URLs from {len(search_results)} results")
        return filtered_urls
    
    def _remove_duplicates(self, url_list: List[Dict]) -> List[Dict]:
        """
        Remove duplicate URLs from the list.
        Args:
            url_list: List of URL data dictionaries
        Returns:
            List of unique URL data dictionaries
        """
        seen_urls = set()
        unique_urls = []
        for url_data in url_list:
            url = url_data.get("url")
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_urls.append(url_data)
        
        removed_count = len(url_list) - len(unique_urls)
        if removed_count > 0:
            logger.debug(f"Removed {removed_count} duplicate URLs")
        
        return unique_urls
    
    async def discover_topic_with_engine(self, topic: str, engine: str) -> int:
        """
        Discover URLs for a single topic using the specified search engine.
        Args:
            topic: Topic name to search for
            engine: Search engine to use
        Returns:
            Number of new URLs discovered and saved
        """
        logger.info(f"Starting discovery for topic: '{topic}' using engine: {engine}")
        
        # Track statistics for better logging
        queries_run = 0
        successful_queries = 0
        failed_queries = 0
        total_raw_results = 0
        search_errors = []
        
        try:
            # Build optimized queries
            queries = await build_dynamic_queries(topic)
            queries_run = len(queries)
            # Get existing URLs to avoid duplicates
            existing_urls = self._get_existing_urls()
            
            all_new_urls = []
            
            # Process each query
            for query_type, query in queries:
                # Search for URLs using the query
                search_results, error_msg = await self._search_with_engine(engine, query)
                if error_msg:
                    # Search failed
                    failed_queries += 1
                    search_errors.append(f"{query_type}: {error_msg}")
                    logger.error(f"Query '{query_type}' failed: {error_msg}")
                    continue
                # Search successful
                successful_queries += 1
                total_raw_results += len(search_results)
                # Apply comprehensive filtering
                filtered_urls = self._filter_search_results(search_results, existing_urls, topic)
                if filtered_urls:
                    all_new_urls.extend(filtered_urls)
                    logger.info(f"Query '{query_type}' found {len(filtered_urls)} valid URLs")
                else:
                    logger.info(f"Query '{query_type}' found 0 valid URLs")
            
            # Check if all queries failed
            if failed_queries == queries_run:
                # All queries failed - this is an error
                # Extract unique error types for cleaner reporting
                unique_errors = set()
                for error in search_errors:
                    # Extract just the error type (e.g., "Rate limit exceeded for serpapi")
                    if ": " in error:
                        error_type = error.split(": ", 1)[1]
                        unique_errors.add(error_type)
                    else:
                        unique_errors.add(error)
                
                if len(unique_errors) == 1:
                    error_reason = f"All {queries_run} queries failed for topic '{topic}' using {engine}. Error: {list(unique_errors)[0]}"
                else:
                    error_reason = f"All {queries_run} queries failed for topic '{topic}' using {engine}. Errors: {'; '.join(sorted(unique_errors))}"
                
                logger.error(f"Discovery failed for topic '{topic}' with {engine}: All queries failed")
                log_scraping_result(topic, engine, 0, "error", error_reason)
                return 0
            
            # Remove duplicates from all queries
            unique_urls = self._remove_duplicates(all_new_urls)
            
            # Save new URLs
            saved_count = 0
            if unique_urls:
                saved_count = self._save_urls(unique_urls, topic, engine)
            
            if saved_count > 0:
                # Found URLs - this is success
                success_reason = f"Successfully discovered {saved_count} URLs for topic '{topic}' using {engine}. "
                success_reason += f"Executed {successful_queries} successful queries out of {queries_run} total queries. "
                success_reason += f"Processed {total_raw_results} raw results."
                
                if failed_queries > 0:
                    success_reason += f" {failed_queries} queries failed but discovery continued."
                
                log_scraping_result(topic, engine, saved_count, "success", success_reason)
            else:
                # No URLs found but some queries succeeded - this is still success
                # The queries worked, but filtering removed all results
                success_reason = f"No URLs found for topic '{topic}' using {engine}. "
                success_reason += f"Executed {successful_queries} successful queries out of {queries_run} total queries. "
                success_reason += f"Processed {total_raw_results} raw results but no valid URLs found after filtering."
                
                if failed_queries > 0:
                    success_reason += f" {failed_queries} queries failed."
                
                log_scraping_result(topic, engine, 0, "success", success_reason)
            
            return saved_count
            
        except Exception as e:
            logger.error(f"Error discovering topic '{topic}' with {engine}: {e}")
            
            # Use the exact error without manual categorization
            error_reason = f"Discovery failed for topic '{topic}' using {engine}: {str(e)}"
            if search_errors:
                # Add unique search errors if any
                unique_errors = set()
                for error in search_errors:
                    # Extract just the error type (e.g., "Rate limit exceeded for serpapi")
                    if ": " in error:
                        error_type = error.split(": ", 1)[1]
                        unique_errors.add(error_type)
                    else:
                        unique_errors.add(error)
                if unique_errors:
                    error_reason += f" Query errors: {'; '.join(sorted(unique_errors))}"
            
            # Log the error with exact reason
            log_scraping_result(topic, engine, 0, "error", error_reason)
            
            return 0
    
    def _get_current_round_number(self) -> int:
        """
        Production-ready method to get the current round number for topic distribution.
        This method queries the database to find the latest distribution round
        and returns the next round number for systematic rotation.
        Returns:
            Current round number (latest + 1, or 1 if no previous rounds)
        Production Features:
            - Database-driven round tracking
            - Graceful error handling with fallback
            - Proper session management
            - Comprehensive logging for debugging
        """
        try:
            # Get the latest round number from database
            with db_manager.get_session() as session:
                from app.models.subcategory import TopicDistribution
                latest_round = session.query(TopicDistribution.distribution_round)\
                    .order_by(TopicDistribution.distribution_round.desc())\
                    .first()
                
                if latest_round:
                    next_round = latest_round[0] + 1
                    logger.info(f"📊 Retrieved latest round: {latest_round[0]}, using next round: {next_round}")
                    return next_round
                else:
                    logger.info("🆕 No previous rounds found, starting with round 1")
                    return 1
                
        except Exception as e:
            logger.warning(f"⚠️ Failed to get current round number: {str(e)}, using round 1")
            return 1
    
    async def discover_with_distribution(self) -> Dict[str, any]:
        """
        Discover URLs using dynamic topic distribution.
        
        Returns:
            Dictionary containing results for each search engine and metadata
        """
        try:
            # Get current round number for systematic rotation
            current_round = self._get_current_round_number()
            # Get optimized topic distribution
            distribution = self.topic_service.distribute_topics_optimized(current_round)
            if "error" in distribution:
                error_msg = f"❌ Topic distribution failed: {distribution['error']}"
                logger.error(error_msg)
                return {"error": error_msg}
            # Process each search engine with assigned topics
            results = {}
            total_urls = 0
            for search_engine, topics in distribution.items():
                if search_engine.startswith("_"):
                    continue
                
                # Ensure topics is a list
                if not isinstance(topics, list):
                    logger.error(f"❌ Topics for {search_engine} is not a list: {type(topics)} - {topics}")
                    continue
 
                engine_urls = 0
                for topic in topics:
                    try:
                        urls_saved = await self.discover_topic_with_engine(topic, search_engine)
                        # Ensure urls_saved is an integer
                        if isinstance(urls_saved, dict):
                            urls_saved = 0
                        elif not isinstance(urls_saved, int):
                            urls_saved = 0
                        engine_urls += urls_saved
                        total_urls += urls_saved
                    except Exception as e:
                        logger.error(f"❌ Error processing topic '{topic}' with {search_engine}: {e}")
                
                results[search_engine] = {
                    "topics": topics,
                    "urls_saved": engine_urls,
                    "status": "completed"
                }
                
                logger.info(f"✅ {search_engine} completed: {engine_urls} URLs saved")
            
            # Add simple metadata to results
            results["_summary"] = {
                "total_urls_saved": total_urls,
                "round_number": current_round,
                "timestamp": datetime.now().isoformat(),
                "status": "success"
            }
            return results
            
        except Exception as e:
            error_msg = f"❌ Discovery failed: {str(e)}"
            logger.error(error_msg)
            logger.exception("Full error details:")
            return {"error": error_msg}

async def run_discovery_worker() -> int:
    """
    Main function to run the discovery worker.
    
    Returns:
        Number of URLs discovered and saved
    """
    try:
        worker = DiscoveryWorker()
        results = await worker.discover_with_distribution()
        
        if "error" in results:
            return 0
        
        # Calculate total URLs from engine results (exclude metadata)
        total_urls = 0
        for engine, result in results.items():
            if not engine.startswith("_") and isinstance(result, dict):
                urls_saved = result.get("urls_saved", 0)
                # Ensure urls_saved is an integer
                if isinstance(urls_saved, dict):
                    urls_saved = 0
                elif not isinstance(urls_saved, int):
                    urls_saved = 0
                
                total_urls += urls_saved
        # Log per-engine results
        for engine, result in results.items():
            if not engine.startswith("_") and isinstance(result, dict):
                urls_saved = result.get("urls_saved", 0)
                logger.info(f"  {engine}: {urls_saved} URLs")
        
        logger.info("=" * 60)
        return total_urls
        
    except Exception as e:
        logger.error(f"Discovery worker failed: {e}")
        return 0


if __name__ == "__main__":
    """Run the discovery worker when executed directly."""
    asyncio.run(run_discovery_worker())

