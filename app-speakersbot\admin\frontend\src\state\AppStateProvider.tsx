import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { storage } from '../utils/storage';
import { 
  generateDemoSpeakers, generateDemoOpportunities, generateDemoMatches,
  generateDemoIntakeForm, generateDemoAffiliates,
  generateDemoSettings, generateDemoPricingPlans, generateDemoUsers
} from '../utils/demo-data';
import {
  generateDemoSubmissionLogs, generateDemoAffiliateMetrics, generateDemoAffiliateCommissions,
  generateDemoSubscriptionEvents, generateDemoChurnData, generateDemoFlaggedItems,
  generateDemoUserFeedback, generateDemoFeatureRequests, generateDemoBotStatus,
  generateDemoScrapingAlerts, generateDemoPaymentFailures
} from '../utils/analytics-seed-data';
import type {
  Speaker, Opportunity, Match, IntakeFormVersion, ScrapingLog, ScrapingTopic,
  Affiliate, Invite, AppSettings, PricingPlan, ActivityLogEntry, User, Note,
  SpeakersState, OpportunitiesState, MatchesState, IntakeFormState,
  ScrapingState, AffiliatesState, SettingsState, UIState, MatchStatus,
  UsersState, IntakeFormField, OpportunityStatus, SubmissionLog, AffiliateMetrics,
  AffiliateCommission, SubscriptionEvent, ChurnData, FlaggedItem, ModerationAction,
  UserFeedback, FeatureRequest, SessionData, OtherFieldAnalysis, CategorySuggestion,
  BotStatus, ScrapingAlert, PaymentFailure
} from '../types';
import dayjs from 'dayjs';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import {
  initData,
  addSpeaker,
  updateSpeaker,
  addOpportunity,
  updateOpportunity,
  updateMatchStatus,
  addMatch,
  // Scraping actions removed - now handled via API directly in components
  addAffiliate,
  updateSettings,
  setDateRange,
  toggleSidebar,
  toggleTheme,
  addActivityLog,
  addUser,
  updateUserLocal,
  deleteUserLocal,
  addIntakeField,
  updateIntakeField,
  deleteIntakeField,
  saveIntakeVersion,
  rollbackIntakeVersion,
  updatePaymentSettings,
  // Scraping actions removed - now handled via API directly in components
  addInvite,
  deleteInvite,
  addOpportunityNote,
  updateOpportunityNote,
  deleteOpportunityNote,
  duplicateOpportunity,
  deleteOpportunityWithMatches,
  bulkDeleteOpportunities,
  bulkUpdateOpportunityStatus,
  bulkUpdateMatchStatus,
  setIntakeFormVersion,
} from '../store/slices/appSlice';
import type { RootState } from '../store';

// Combined app state
interface AppState {
  speakers: SpeakersState;
  opportunities: OpportunitiesState;
  matches: MatchesState;
  users: UsersState;
  intakeForm: IntakeFormState;
  // Scraping data is now fetched from API directly in components
  affiliates: AffiliatesState;
  settings: SettingsState;
  ui: UIState;
  activityLog: ActivityLogEntry[];
  pricingPlans: PricingPlan[];
  // New analytics state slices
  submissions: SubmissionLog[];
  affiliateMetrics: AffiliateMetrics[];
  affiliateCommissions: AffiliateCommission[];
  subscriptionEvents: SubscriptionEvent[];
  churnData: ChurnData[];
  flaggedItems: FlaggedItem[];
  moderationActions: ModerationAction[];
  userFeedback: UserFeedback[];
  featureRequests: FeatureRequest[];
  sessionData: SessionData[];
  categoryAnalysis: OtherFieldAnalysis[];
  categorySuggestions: CategorySuggestion[];
  botStatus: BotStatus[];
  scrapingAlerts: ScrapingAlert[];
  paymentFailures: PaymentFailure[];
}

type AppAction = 
  | { type: 'INIT_DATA' }
  | { type: 'ADD_SPEAKER'; payload: Speaker }
  | { type: 'UPDATE_SPEAKER'; payload: Speaker }
  | { type: 'DELETE_SPEAKER'; payload: string }
  | { type: 'ADD_OPPORTUNITY'; payload: Opportunity }
  | { type: 'UPDATE_OPPORTUNITY'; payload: Opportunity }
  | { type: 'DELETE_OPPORTUNITY'; payload: string }
  | { type: 'DELETE_OPPORTUNITY_WITH_MATCHES'; payload: { opportunityId: string; deleteMatches: boolean } }
  | { type: 'DUPLICATE_OPPORTUNITY'; payload: Opportunity }
  | { type: 'BULK_DELETE_OPPORTUNITIES'; payload: string[] }
  | { type: 'BULK_UPDATE_OPPORTUNITY_STATUS'; payload: { ids: string[]; status: OpportunityStatus } }
  | { type: 'ADD_OPPORTUNITY_NOTE'; payload: { opportunityId: string; note: Note } }
  | { type: 'UPDATE_OPPORTUNITY_NOTE'; payload: { opportunityId: string; noteId: string; content: string } }
  | { type: 'DELETE_OPPORTUNITY_NOTE'; payload: { opportunityId: string; noteId: string } }
  | { type: 'UPDATE_MATCH_STATUS'; payload: { matchId: string; status: MatchStatus; reason?: string } }
  | { type: 'BULK_UPDATE_MATCH_STATUS'; payload: { matchIds: string[]; status: MatchStatus; reason?: string } }
  | { type: 'ADD_MATCH'; payload: Match }
  | { type: 'ADD_USER'; payload: User }
  | { type: 'UPDATE_USER'; payload: User }
  | { type: 'DELETE_USER'; payload: string }
  | { type: 'UPDATE_INTAKE_FORM'; payload: IntakeFormVersion }
  | { type: 'ADD_INTAKE_FIELD'; payload: IntakeFormField }
  | { type: 'UPDATE_INTAKE_FIELD'; payload: IntakeFormField }
  | { type: 'DELETE_INTAKE_FIELD'; payload: string }
  | { type: 'SAVE_INTAKE_VERSION' }
  | { type: 'ROLLBACK_INTAKE_VERSION'; payload: string }
  | { type: 'UPDATE_PAYMENT_SETTINGS'; payload: any }
  // Scraping actions removed - now handled via API directly in components
  | { type: 'ADD_INVITE'; payload: Invite }
  | { type: 'DELETE_INVITE'; payload: string }
  | { type: 'ADD_AFFILIATE'; payload: Affiliate }
  | { type: 'UPDATE_SETTINGS'; payload: AppSettings }
  | { type: 'SET_DATE_RANGE'; payload: [string, string] | null }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'TOGGLE_THEME' }
  | { type: 'ADD_ACTIVITY_LOG'; payload: ActivityLogEntry };

const recalculateOpportunityCounts = (opportunities: Opportunity[], matches: Match[]): Opportunity[] => {
  return opportunities.map(opp => {
    const oppMatches = matches.filter(m => m.opportunityId === opp.id);
    return {
      ...opp,
      matchedCount: oppMatches.length,
      interestedCount: oppMatches.filter(m => m.status === 'interested').length,
      acceptedCount: oppMatches.filter(m => m.status === 'accepted').length,
      rejectedCount: oppMatches.filter(m => m.status === 'rejected').length,
    };
  });
};

const appReducer = (state: AppState, action: AppAction): AppState => {
  switch (action.type) {
    case 'INIT_DATA': {
      // Always generate fresh demo data to ensure we have the latest version
      const speakers = generateDemoSpeakers();
      const opportunities = generateDemoOpportunities();
      const matches = generateDemoMatches();
      const affiliates = generateDemoAffiliates(); // Always generate fresh affiliate data
      const users = generateDemoUsers(); // Always generate fresh user data to get latest roles
      const intakeForm = storage.get<IntakeFormVersion[]>('INTAKE_FORM') || [generateDemoIntakeForm()];
      // Scraping data is now fetched from API directly in components
      const settings = storage.get<AppSettings>('SETTINGS') || generateDemoSettings();
      const ui = storage.get<UIState>('UI') || { sidebarCollapsed: false, dateRange: null, theme: 'dark' };
      const activityLog = storage.get<ActivityLogEntry[]>('ACTIVITY_LOG') || [];
      const pricingPlans = generateDemoPricingPlans();

      // Generate new analytics seed data
      const submissions = generateDemoSubmissionLogs();
      const affiliateMetrics = generateDemoAffiliateMetrics();
      const affiliateCommissions = generateDemoAffiliateCommissions();
      const subscriptionEvents = generateDemoSubscriptionEvents();
      const churnData = generateDemoChurnData();
      const flaggedItems = generateDemoFlaggedItems();
      const userFeedback = generateDemoUserFeedback();
      const featureRequests = generateDemoFeatureRequests();
      const botStatus = generateDemoBotStatus();
      const scrapingAlerts = generateDemoScrapingAlerts();
      const paymentFailures = generateDemoPaymentFailures();

      // Always save fresh demo data to ensure updates are reflected
      storage.set('SPEAKERS', speakers);
      storage.set('OPPORTUNITIES', opportunities);
      storage.set('MATCHES', matches);
      storage.set('USERS', users);
      storage.set('INTAKE_FORM', intakeForm);
      // Scraping data is now fetched from API directly in components
      storage.set('AFFILIATES', affiliates);
      storage.set('SETTINGS', settings);
      storage.set('UI', ui);
      storage.set('ACTIVITY_LOG', activityLog);

      return {
        speakers: { speakers, loading: false },
        opportunities: { opportunities: recalculateOpportunityCounts(opportunities, matches), loading: false },
        matches: { matches, loading: false },
        users: { users, loading: false },
        intakeForm: { 
          versions: intakeForm, 
          activeVersion: intakeForm.find(v => v.isActive) || intakeForm[0] || null, 
          loading: false 
        },
        // Scraping data is now fetched from API directly in components
        affiliates: { affiliates, loading: false },
        settings: { settings, invites: [], loading: false },
        ui,
        activityLog,
        pricingPlans,
        // Initialize new analytics data with generated seed data
        submissions,
        affiliateMetrics,
        affiliateCommissions,
        subscriptionEvents,
        churnData,
        flaggedItems,
        moderationActions: [],
        userFeedback,
        featureRequests,
        sessionData: [],
        categoryAnalysis: [],
        categorySuggestions: [],
        botStatus,
        scrapingAlerts,
        paymentFailures,
      };
    }

    case 'ADD_SPEAKER': {
      const newSpeakers = [...state.speakers.speakers, action.payload];
      storage.set('SPEAKERS', newSpeakers);
      return {
        ...state,
        speakers: { ...state.speakers, speakers: newSpeakers },
      };
    }

    case 'UPDATE_SPEAKER': {
      const updatedSpeakers = state.speakers.speakers.map(s => 
        s.id === action.payload.id ? action.payload : s
      );
      storage.set('SPEAKERS', updatedSpeakers);
      return {
        ...state,
        speakers: { ...state.speakers, speakers: updatedSpeakers },
      };
    }

    case 'ADD_OPPORTUNITY': {
      const newOpportunities = [...state.opportunities.opportunities, action.payload];
      storage.set('OPPORTUNITIES', newOpportunities);
      return {
        ...state,
        opportunities: { ...state.opportunities, opportunities: newOpportunities },
      };
    }

    case 'UPDATE_OPPORTUNITY': {
      const updatedOpportunities = state.opportunities.opportunities.map(o => 
        o.id === action.payload.id ? action.payload : o
      );
      storage.set('OPPORTUNITIES', updatedOpportunities);
      return {
        ...state,
        opportunities: { ...state.opportunities, opportunities: updatedOpportunities },
      };
    }

    case 'UPDATE_MATCH_STATUS': {
      const { matchId, status, reason } = action.payload;
      const updatedMatches = state.matches.matches.map(m => 
        m.id === matchId 
          ? { ...m, status, reason, updatedAt: dayjs().toISOString() }
          : m
      );
      
      // Recalculate opportunity counts
      const updatedOpportunities = recalculateOpportunityCounts(
        state.opportunities.opportunities, 
        updatedMatches
      );

      storage.set('MATCHES', updatedMatches);
      storage.set('OPPORTUNITIES', updatedOpportunities);

      return {
        ...state,
        matches: { ...state.matches, matches: updatedMatches },
        opportunities: { ...state.opportunities, opportunities: updatedOpportunities },
      };
    }

    case 'ADD_MATCH': {
      const newMatches = [...state.matches.matches, action.payload];
      const updatedOpportunities = recalculateOpportunityCounts(
        state.opportunities.opportunities,
        newMatches
      );
      
      storage.set('MATCHES', newMatches);
      storage.set('OPPORTUNITIES', updatedOpportunities);
      
      return {
        ...state,
        matches: { ...state.matches, matches: newMatches },
        opportunities: { ...state.opportunities, opportunities: updatedOpportunities },
      };
    }

    // Scraping actions removed - now handled via API directly in components

    case 'ADD_AFFILIATE': {
      const newAffiliates = [...state.affiliates.affiliates, action.payload];
      storage.set('AFFILIATES', newAffiliates);
      return {
        ...state,
        affiliates: { ...state.affiliates, affiliates: newAffiliates },
      };
    }

    case 'UPDATE_SETTINGS': {
      storage.set('SETTINGS', action.payload);
      return {
        ...state,
        settings: { ...state.settings, settings: action.payload },
      };
    }

    case 'SET_DATE_RANGE': {
      const newUI = { ...state.ui, dateRange: action.payload };
      storage.set('UI', newUI);
      return {
        ...state,
        ui: newUI,
      };
    }

    case 'TOGGLE_SIDEBAR': {
      const newUI = { ...state.ui, sidebarCollapsed: !state.ui.sidebarCollapsed };
      storage.set('UI', newUI);
      return {
        ...state,
        ui: newUI,
      };
    }

    case 'TOGGLE_THEME': {
      const newTheme: 'light' | 'dark' = state.ui.theme === 'dark' ? 'light' : 'dark';
      const newUI = { ...state.ui, theme: newTheme };
      storage.set('UI', newUI);
      return {
        ...state,
        ui: newUI,
      };
    }

    case 'ADD_ACTIVITY_LOG': {
      const newLog = [action.payload, ...state.activityLog].slice(0, 100); // Keep last 100
      storage.set('ACTIVITY_LOG', newLog);
      return {
        ...state,
        activityLog: newLog,
      };
    }

    case 'ADD_USER': {
      const newUsers = [...state.users.users, action.payload];
      storage.set('USERS', newUsers);
      return {
        ...state,
        users: { ...state.users, users: newUsers },
      };
    }

    case 'UPDATE_USER': {
      const updatedUsers = state.users.users.map(u => 
        u.id === action.payload.id ? action.payload : u
      );
      storage.set('USERS', updatedUsers);
      return {
        ...state,
        users: { ...state.users, users: updatedUsers },
      };
    }

    case 'DELETE_USER': {
      const filteredUsers = state.users.users.filter(u => u.id !== action.payload);
      storage.set('USERS', filteredUsers);
      return {
        ...state,
        users: { ...state.users, users: filteredUsers },
      };
    }

    case 'ADD_INTAKE_FIELD': {
      if (!state.intakeForm.activeVersion) return state;
      
      const updatedVersion = {
        ...state.intakeForm.activeVersion,
        fields: [...state.intakeForm.activeVersion.fields, action.payload]
      };
      const updatedVersions = state.intakeForm.versions.map(v => 
        v.isActive ? updatedVersion : v
      );
      
      storage.set('INTAKE_FORM', updatedVersions);
      return {
        ...state,
        intakeForm: {
          ...state.intakeForm,
          versions: updatedVersions,
          activeVersion: updatedVersion,
        },
      };
    }

    case 'UPDATE_INTAKE_FIELD': {
      if (!state.intakeForm.activeVersion) return state;
      
      const updatedFields = state.intakeForm.activeVersion.fields.map(f => 
        f.id === action.payload.id ? action.payload : f
      );
      const updatedVersion = {
        ...state.intakeForm.activeVersion,
        fields: updatedFields
      };
      const updatedVersions = state.intakeForm.versions.map(v => 
        v.isActive ? updatedVersion : v
      );
      
      storage.set('INTAKE_FORM', updatedVersions);
      return {
        ...state,
        intakeForm: {
          ...state.intakeForm,
          versions: updatedVersions,
          activeVersion: updatedVersion,
        },
      };
    }

    case 'DELETE_INTAKE_FIELD': {
      if (!state.intakeForm.activeVersion) return state;
      
      const updatedFields = state.intakeForm.activeVersion.fields.filter(f => f.id !== action.payload);
      const updatedVersion = {
        ...state.intakeForm.activeVersion,
        fields: updatedFields
      };
      const updatedVersions = state.intakeForm.versions.map(v => 
        v.isActive ? updatedVersion : v
      );
      
      storage.set('INTAKE_FORM', updatedVersions);
      return {
        ...state,
        intakeForm: {
          ...state.intakeForm,
          versions: updatedVersions,
          activeVersion: updatedVersion,
        },
      };
    }

    case 'SAVE_INTAKE_VERSION': {
      if (!state.intakeForm.activeVersion) return state;
      
      const newVersion: IntakeFormVersion = {
        ...state.intakeForm.activeVersion,
        id: Date.now().toString(),
        version: Math.max(...state.intakeForm.versions.map(v => v.version)) + 1,
        isActive: true,
        createdAt: new Date().toISOString(),
      };
      
      const updatedVersions = [
        ...state.intakeForm.versions.map(v => ({ ...v, isActive: false })),
        newVersion
      ];
      
      storage.set('INTAKE_FORM', updatedVersions);
      return {
        ...state,
        intakeForm: {
          ...state.intakeForm,
          versions: updatedVersions,
          activeVersion: newVersion,
        },
      };
    }

    case 'ROLLBACK_INTAKE_VERSION': {
      const targetVersion = state.intakeForm.versions.find(v => v.id === action.payload);
      if (!targetVersion) return state;
      
      const updatedVersions = state.intakeForm.versions.map(v => ({
        ...v,
        isActive: v.id === action.payload
      }));
      
      storage.set('INTAKE_FORM', updatedVersions);
      return {
        ...state,
        intakeForm: {
          ...state.intakeForm,
          versions: updatedVersions,
          activeVersion: targetVersion,
        },
      };
    }

    // Scraping topic actions removed - now handled via API directly in components

    case 'UPDATE_PAYMENT_SETTINGS': {
      const newSettings = {
        ...state.settings.settings,
        payment: action.payload
      };
      storage.set('SETTINGS', newSettings);
      return {
        ...state,
        settings: { ...state.settings, settings: newSettings },
      };
    }

    // Scraping setting actions removed - now handled via API directly in components

    case 'ADD_INVITE': {
      const newInvites = [...state.settings.invites, action.payload];
      const currentSettings = storage.get<any>('SETTINGS') || { invites: [] };
      storage.set('SETTINGS', { ...currentSettings, invites: newInvites });
      return {
        ...state,
        settings: { ...state.settings, invites: newInvites },
      };
    }

    case 'DELETE_INVITE': {
      const filteredInvites = state.settings.invites.filter(i => i.id !== action.payload);
      const currentSettings = storage.get<any>('SETTINGS') || { invites: [] };
      storage.set('SETTINGS', { ...currentSettings, invites: filteredInvites });
      return {
        ...state,
        settings: { ...state.settings, invites: filteredInvites },
      };
    }

    case 'ADD_OPPORTUNITY_NOTE': {
      const { opportunityId, note } = action.payload;
      const updatedOpportunities = state.opportunities.opportunities.map(o => 
        o.id === opportunityId 
          ? { ...o, notes: [...o.notes, note] }
          : o
      );
      storage.set('OPPORTUNITIES', updatedOpportunities);
      return {
        ...state,
        opportunities: { ...state.opportunities, opportunities: updatedOpportunities },
      };
    }

    case 'UPDATE_OPPORTUNITY_NOTE': {
      const { opportunityId, noteId, content } = action.payload;
      const updatedOpportunities = state.opportunities.opportunities.map(o => 
        o.id === opportunityId 
          ? { 
              ...o, 
              notes: o.notes.map(n => n.id === noteId ? { ...n, content } : n) 
            }
          : o
      );
      storage.set('OPPORTUNITIES', updatedOpportunities);
      return {
        ...state,
        opportunities: { ...state.opportunities, opportunities: updatedOpportunities },
      };
    }

    case 'DELETE_OPPORTUNITY_NOTE': {
      const { opportunityId, noteId } = action.payload;
      const updatedOpportunities = state.opportunities.opportunities.map(o => 
        o.id === opportunityId 
          ? { ...o, notes: o.notes.filter(n => n.id !== noteId) }
          : o
      );
      storage.set('OPPORTUNITIES', updatedOpportunities);
      return {
        ...state,
        opportunities: { ...state.opportunities, opportunities: updatedOpportunities },
      };
    }

    case 'DUPLICATE_OPPORTUNITY': {
      const duplicatedOpportunity = {
        ...action.payload,
        id: `op_${Date.now()}`,
        title: `${action.payload.title} (Copy)`,
        createdAt: dayjs().toISOString(),
        updatedAt: dayjs().toISOString(),
        matchedCount: 0,
        interestedCount: 0,
        acceptedCount: 0,
        rejectedCount: 0,
        notes: [],
      };
      const newOpportunities = [...state.opportunities.opportunities, duplicatedOpportunity];
      storage.set('OPPORTUNITIES', newOpportunities);
      return {
        ...state,
        opportunities: { ...state.opportunities, opportunities: newOpportunities },
      };
    }

    case 'DELETE_OPPORTUNITY_WITH_MATCHES': {
      const { opportunityId, deleteMatches } = action.payload;
      const filteredOpportunities = state.opportunities.opportunities.filter(o => o.id !== opportunityId);
      
      let updatedMatches = state.matches.matches;
      if (deleteMatches) {
        updatedMatches = state.matches.matches.filter(m => m.opportunityId !== opportunityId);
        storage.set('MATCHES', updatedMatches);
      }
      
      storage.set('OPPORTUNITIES', filteredOpportunities);
      return {
        ...state,
        opportunities: { ...state.opportunities, opportunities: filteredOpportunities },
        matches: { ...state.matches, matches: updatedMatches },
      };
    }

    case 'BULK_DELETE_OPPORTUNITIES': {
      const filteredOpportunities = state.opportunities.opportunities.filter(
        o => !action.payload.includes(o.id)
      );
      storage.set('OPPORTUNITIES', filteredOpportunities);
      return {
        ...state,
        opportunities: { ...state.opportunities, opportunities: filteredOpportunities },
      };
    }

    case 'BULK_UPDATE_OPPORTUNITY_STATUS': {
      const { ids, status } = action.payload;
      const updatedOpportunities = state.opportunities.opportunities.map(o => 
        ids.includes(o.id) 
          ? { ...o, status, updatedAt: dayjs().toISOString() }
          : o
      );
      storage.set('OPPORTUNITIES', updatedOpportunities);
      return {
        ...state,
        opportunities: { ...state.opportunities, opportunities: updatedOpportunities },
      };
    }

    case 'BULK_UPDATE_MATCH_STATUS': {
      const { matchIds, status, reason } = action.payload;
      const updatedMatches = state.matches.matches.map(m => 
        matchIds.includes(m.id) 
          ? { ...m, status, reason, updatedAt: dayjs().toISOString() }
          : m
      );
      
      const updatedOpportunities = recalculateOpportunityCounts(
        state.opportunities.opportunities, 
        updatedMatches
      );

      storage.set('MATCHES', updatedMatches);
      storage.set('OPPORTUNITIES', updatedOpportunities);

      return {
        ...state,
        matches: { ...state.matches, matches: updatedMatches },
        opportunities: { ...state.opportunities, opportunities: updatedOpportunities },
      };
    }

    default:
      return state;
  }
};

const initialState: AppState = {
  speakers: { speakers: [], loading: true },
  opportunities: { opportunities: [], loading: true },
  matches: { matches: [], loading: true },
  users: { users: [], loading: true },
  intakeForm: { versions: [], activeVersion: null, loading: true },
  // Scraping data is now fetched from API directly in components
  affiliates: { affiliates: [], loading: true },
  settings: { 
    settings: { 
      payment: { gateway: 'stripe', publicKey: '', webhookUrl: '' }, 
      scrapingEnabled: false 
    }, 
    invites: [], 
    loading: true 
  },
  ui: { sidebarCollapsed: false, dateRange: null, theme: 'dark' },
  activityLog: [],
  pricingPlans: [],
  // New analytics state slices
  submissions: [],
  affiliateMetrics: [],
  affiliateCommissions: [],
  subscriptionEvents: [],
  churnData: [],
  flaggedItems: [],
  moderationActions: [],
  userFeedback: [],
  featureRequests: [],
  sessionData: [],
  categoryAnalysis: [],
  categorySuggestions: [],
  botStatus: [],
  scrapingAlerts: [],
  paymentFailures: [],
};

type AppContextType = RootState['app'] & { dispatch: (action: any) => void };

const AppContext = createContext<AppContextType | undefined>(undefined);

export const AppStateProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const reduxDispatch = useAppDispatch();
  const appState = useAppSelector((state) => state.app);

  useEffect(() => {
    reduxDispatch(initData());
  }, [reduxDispatch]);

  const legacyDispatch = (action: any) => {
    switch (action.type) {
      case 'INIT_DATA':
        return reduxDispatch(initData());
      case 'ADD_SPEAKER':
        return reduxDispatch(addSpeaker(action.payload));
      case 'UPDATE_SPEAKER':
        return reduxDispatch(updateSpeaker(action.payload));
      case 'ADD_OPPORTUNITY':
        return reduxDispatch(addOpportunity(action.payload));
      case 'UPDATE_OPPORTUNITY':
        return reduxDispatch(updateOpportunity(action.payload));
      case 'UPDATE_MATCH_STATUS':
        return reduxDispatch(updateMatchStatus(action.payload));
      case 'ADD_MATCH':
        return reduxDispatch(addMatch(action.payload));
      // Scraping actions removed - now handled via API directly in components
      case 'ADD_AFFILIATE':
        return reduxDispatch(addAffiliate(action.payload));
      case 'UPDATE_SETTINGS':
        return reduxDispatch(updateSettings(action.payload));
      case 'SET_DATE_RANGE':
        return reduxDispatch(setDateRange(action.payload));
      case 'TOGGLE_SIDEBAR':
        return reduxDispatch(toggleSidebar());
      case 'TOGGLE_THEME':
        return reduxDispatch(toggleTheme());
      case 'ADD_ACTIVITY_LOG':
        return reduxDispatch(addActivityLog(action.payload));
      case 'ADD_USER':
        return reduxDispatch(addUser(action.payload));
      case 'UPDATE_USER':
        return reduxDispatch(updateUserLocal(action.payload));
      case 'DELETE_USER':
        return reduxDispatch(deleteUserLocal(action.payload));
      case 'ADD_INTAKE_FIELD':
        return reduxDispatch(addIntakeField(action.payload));
      case 'UPDATE_INTAKE_FIELD':
        return reduxDispatch(updateIntakeField(action.payload));
      case 'DELETE_INTAKE_FIELD':
        return reduxDispatch(deleteIntakeField(action.payload));
      case 'SAVE_INTAKE_VERSION':
        return reduxDispatch(saveIntakeVersion());
      case 'ROLLBACK_INTAKE_VERSION':
        return reduxDispatch(rollbackIntakeVersion(action.payload));
      case 'UPDATE_PAYMENT_SETTINGS':
        return reduxDispatch(updatePaymentSettings(action.payload));
      // Scraping actions removed - now handled via API directly in components
      case 'ADD_INVITE':
        return reduxDispatch(addInvite(action.payload));
      case 'DELETE_INVITE':
        return reduxDispatch(deleteInvite(action.payload));
      case 'ADD_OPPORTUNITY_NOTE':
        return reduxDispatch(addOpportunityNote(action.payload));
      case 'UPDATE_OPPORTUNITY_NOTE':
        return reduxDispatch(updateOpportunityNote(action.payload));
      case 'DELETE_OPPORTUNITY_NOTE':
        return reduxDispatch(deleteOpportunityNote(action.payload));
      case 'DUPLICATE_OPPORTUNITY':
        return reduxDispatch(duplicateOpportunity(action.payload));
      case 'DELETE_OPPORTUNITY_WITH_MATCHES':
        return reduxDispatch(deleteOpportunityWithMatches(action.payload));
      case 'BULK_DELETE_OPPORTUNITIES':
        return reduxDispatch(bulkDeleteOpportunities(action.payload));
      case 'BULK_UPDATE_OPPORTUNITY_STATUS':
        return reduxDispatch(bulkUpdateOpportunityStatus(action.payload));
      case 'BULK_UPDATE_MATCH_STATUS':
        return reduxDispatch(bulkUpdateMatchStatus(action.payload));
      case 'UPDATE_INTAKE_FORM':
        return reduxDispatch(setIntakeFormVersion(action.payload));
      default:
        return undefined;
    }
  };

  return (
    <AppContext.Provider value={{ ...appState, dispatch: legacyDispatch }}>
      {children}
    </AppContext.Provider>
  );
};

export const useAppState = () => {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error('useAppState must be used within an AppStateProvider');
  }
  return context;
};