export type DebouncedFunction<TArgs extends unknown[]> = ((
  ...args: TArgs
) => void) & { cancel: () => void };

// Simple, typed debounce utility for global reuse
export function debounce<TArgs extends unknown[]>(
  fn: (...args: TArgs) => void,
  delayMs: number
): DebouncedFunction<TArgs> {
  let timer: ReturnType<typeof setTimeout> | undefined;

  const core = (...args: TArgs) => {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn(...args);
    }, delayMs);
  };

  const debounced = core as DebouncedFunction<TArgs>;
  debounced.cancel = () => {
    if (timer) {
      clearTimeout(timer);
      timer = undefined;
    }
  };

  return debounced;
}

// React-friendly hook wrapper for convenience
import { useMemo, useRef, useEffect } from "react";

export function useDebouncedCallback<TArgs extends unknown[]>(
  callback: (...args: TArgs) => void,
  delayMs: number
): DebouncedFunction<TArgs> {
  const latestRef = useRef(callback);
  useEffect(() => {
    latestRef.current = callback;
  }, [callback]);

  const debounced = useMemo(() =>
    debounce<TArgs>(((...args: TArgs) => latestRef.current(...args)) as (
      ...args: TArgs
    ) => void, delayMs),
  [delayMs]);

  useEffect(() => () => debounced.cancel(), [debounced]);
  return debounced;
}


