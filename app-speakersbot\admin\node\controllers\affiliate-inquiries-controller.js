const ApiResponse = require("../helpers/api-response");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const affiliateInquiriesService = require("../services/affiliate-inquiries-service");

/**
 * Create new affiliate inquiry
 */
exports.createAffiliateInquiry = async (req, res, next) => {
    try {
          const result = await affiliateInquiriesService.createAffiliateInquiry(req);
          const { status, message } = result;
        if (status) res.status(RESPONSE_CODES.ACCEPTED).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    }
    catch (error) {
        next(error);
    }
}

/**
 * Update affiliate inquiry
 */
exports.updateAffiliateInquiry = async (req, res, next) => {
    try {
        const result = await affiliateInquiriesService.updateAffiliateInquiry(req);
        const { status, message } = result;
        
        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    }
    catch (error) {
        next(error);
    }
}

 
/**
 * Get affiliate inquiries
 */
exports.getAffiliateInquiries = async (req, res, next) => {
    try {
        const result = await affiliateInquiriesService.getAffiliateInquiries(req);
        const { status, message, data, pageData } = result;
        
        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data, pagination:pageData }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    }
    catch (error) {
        next(error);
    }

}

/**
 * Get affiliate inquiry by affiliate and speaker
 */
exports.getAffiliateInquiryRequest = async (req, res, next) => {
    try {
        const result = await affiliateInquiriesService.getAffiliateInquiryRequest(req);
        const { status, message, data } = result;
        
        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    }
    catch (error) {
        next(error);
    }
}

        
/**
 * Get affiliate inquiries history
 */
exports.getAffiliateInquiriesHistory = async (req, res, next) => {
    try {
        const result = await affiliateInquiriesService.getAffiliateInquiriesHistory(req);
        const { status, message, data, pageData } = result;
        
        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data, pagination:pageData }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    }
    catch (error) {
        next(error);
    }
}