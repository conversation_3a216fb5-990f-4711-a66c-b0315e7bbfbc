const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const ApiResponse = require("../helpers/api-response");

const dashboardService = require("../services/dashboard-service");

/**
 * Get revenue metrics including MRR calculations
 */
exports.getRevenueMetrics = async (req, res, next) => {
    try {
        const result = await dashboardService.getRevenueMetrics(req);
        const { status, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).send(ApiResponse({ message: "Revenue metrics retrieved successfully", data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error in getRevenueMetrics:", error);
        next(error);
    }
};

/**
 * Get comprehensive category analytics dashboard
 * 
 * This endpoint calculates and returns key category performance metrics:
 * 
 * 1. ACTIVE CATEGORIES:
 *    - totalActive: Count of all active categories in the system
 *    - addedThisQuarter: Number of new categories added in current quarter
 * 
 * 2. FASTEST GROWING CATEGORY:
 *    - Identifies category with highest percentage increase in opportunities
 *    - Calculation: ((Current Quarter Opportunities - Previous Quarter Opportunities) / Previous Quarter Opportunities) * 100
 *    - Example: Technology had 1 opportunity last quarter, 3 this quarter = 200% growth
 * 
 * 3. BIGGEST GAP CATEGORY:
 *    - Identifies category with largest gap between speaker supply and opportunity demand
 *    - Calculation: Number of speakers with expertise - Number of available opportunities
 *    - Example: Healthcare has 4 speakers but only 1 opportunity = 3 speaker gap
 * 
 * 4. AVERAGE CATEGORY GROWTH:
 *    - Calculates aggregate growth rate across all categories
 *    - Formula: Sum of all category growth percentages / Number of categories
 *    - Example: Tech(200%) + Business(0%) + Education(100%) + Healthcare(0%) + Finance(0%) = 60% average
 * 
 * 5. CATEGORY PERFORMANCE:
 *    - Shows top 5 categories with opportunity count and speaker count
 *    - Helps identify which categories are most active and have speaker coverage
 * 
 * 6. EMERGING CATEGORIES:
 *    - Shows percentage distribution of opportunities across categories
 *    - Used for pie chart visualization
 *    - Example: Technology represents 33.33% of all opportunities
 * 
 * Query Parameters:
 * - startDate (optional): Start date for analysis period
 * - endDate (optional): End date for analysis period  
 * - period (optional): 'quarter' or 'month' for comparison period
 * 
 * Data Sources:
 * - categories: Master list of all categories
 * - opportunities: Events/opportunities with industry field
 * - speaker_details: Speaker expertise linked to categories via 'Subcategory' key
 * - speaker_opportunities: Speaker-opportunity matching data
 */
exports.getCategoryAnalytics = async (req, res, next) => {
    try {
        const result = await dashboardService.getCategoryAnalytics(req);
        const { status, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).send(ApiResponse({ message: "Category analytics retrieved successfully", data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error in getCategoryAnalytics:", error);
        next(error);
    }
};

/**
 * Get dashboard pipeline
 */
exports.getDashboardPipeline = async (req, res, next) => {
    try {
        const result = await dashboardService.getDashboardPipeline();
        const { status, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).send(ApiResponse({ message: "Dashboard pipeline retrieved successfully", data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error in getDashboardPipeline:", error);
        next(error);
    }
};



/**
 * Get dashboard onboarding
 */
exports.getDashboardOnboarding = async (req, res, next) => {
    try {
        const result = await dashboardService.getDashboardOnboarding();
        const { status, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).send(ApiResponse({ message: "Dashboard onboarding retrieved successfully", data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error in getDashboardOnboarding:", error);
        next(error);
    }
};


exports.getMatchingAnalytics = async (req, res, next) => {
    try {
        const result = await dashboardService.getMatchingAnalytics();
        const { status, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).send(ApiResponse({ message: "Matching analytics retrieved successfully", data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error in getMatchingAnalytics:", error);
        next(error);

    }
}
/**
 * Get feedback analytics
 */
exports.getFeedbackAnalytics = async (req, res, next) => {
    try {
        const result = await dashboardService.getFeedbackAnalytics(req);
        const { status, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).send(ApiResponse({ message: "Feedback analytics retrieved successfully", data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error in getFeedbackAnalytics:", error);
        next(error);
    }
};

/**
 * Get health analytics
 */
exports.getHealthAnalytics = async (req, res, next) => {
    try {
        const result = await dashboardService.getHealthAnalytics();
        const { status, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).send(ApiResponse({ message: "Health analytics retrieved successfully", data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error in getHealthAnalytics:", error);
        next(error);
    }
};

/**
 * Add category
 */
exports.addCategory = async (req, res, next) => {
    try {
        const { category } = req.body;
        const createdCategory = await dashboardService.addCategory(category);
        const { status, data } = createdCategory;
        const responseData = {
            id: data.id,
            name: data.name
        }

        if (status) res.status(RESPONSE_CODES.SUCCESS).send(ApiResponse({ message: "Category added successfully", data: responseData }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error in addCategory:", error);
        next(error);
    }
};

/** Get affiliate analytics
 */
exports.getAffiliateAnalytics = async (req, res, next) => {
    try {
        const result = await dashboardService.getAffiliateAnalytics();
        const { status, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).send(ApiResponse({ message: "Affiliate analytics retrieved successfully", data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    } catch (error) {
        console.error("Error in getAffiliateAnalytics:", error);
        next(error);
    }
}
/**
 * Get comprehensive monthly revenue analytics dashboard
 * 
 * This endpoint calculates and returns key subscription revenue metrics for the current month:
 * 
 * 1. NEW MONTHLY PLAN SUBSCRIPTIONS:
 *    - Count of users who purchased/subscribed to monthly plans this month
 *    - Based on Foundation Plan (plan_id: 1) subscriptions created this month
 * 
 * 2. EXPANSION MONTHLY PLAN SUBSCRIPTIONS:
 *    - Count of users who upgraded from monthly to yearly/lifetime plans this month
 *    - Identifies users with previous monthly subscriptions who upgraded
 * 
 * 3. CHURNED MONTHLY PLAN SUBSCRIPTIONS:
 *    - Count of users who canceled monthly plans this month
 *    - Based on subscriptions with status 'expired' or 'failed' this month
 * 
 * 4. NEW YEARLY PLAN SUBSCRIPTIONS:
 *    - Count of users who purchased/subscribed to yearly plans this month
 *    - Based on Pro Plan (plan_id: 2) subscriptions created this month
 * 
 * 5. EXPANSION YEARLY PLAN SUBSCRIPTIONS:
 *    - Count of users who upgraded from yearly to lifetime plans this month
 *    - Identifies users with previous yearly subscriptions who upgraded
 * 
 * 6. CHURNED YEARLY PLAN SUBSCRIPTIONS:
 *    - Count of users who canceled yearly plans this month
 *    - Based on yearly subscriptions with status 'expired' or 'failed' this month
 * 
 * 7. NEW LIFETIME PLAN SUBSCRIPTIONS:
 *    - Count of users who purchased/subscribed to lifetime plans this month
 *    - Based on Lifeme Plan (plan_id: 3) subscriptions created this month
 * 
 * 8. TOTAL REVENUE THIS MONTH:
 *    - Sum of all subscription amounts received this month
 *    - Includes all active subscriptions created this month
 * 
 * Query Parameters:
 * - startDate (optional): Start date for analysis period
 * - endDate (optional): End date for analysis period
 * - month (optional): Specific month to analyze (format: YYYY-MM)
 * 
 * Data Sources:
 * - subscriptions: Subscription records with status, dates, and amounts
 * - pricing_plans: Plan details including billing_interval and amounts
 * - speakers: Speaker information linked to subscriptions
 */
exports.getMonthlyRevenueAnalytics = async (req, res, next) => {
    try {
        const result = await dashboardService.getMonthlyRevenueAnalytics(req);
        const { status, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).send(ApiResponse({ message: "Monthly revenue analytics retrieved successfully", data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error in getMonthlyRevenueAnalytics:", error);
        next(error);
    }
};