const AWS = require("aws-sdk");


const s3 = new AWS.S3({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION,
});

const uploadFileToS3 = async (file, folder) => {
    const params = {
        Bucket: process.env.AWS_BUCKET_NAME,
        Key: `${folder}/${file.originalname}-${Date.now()}`, 
        Body: file.buffer,
        ContentType: file.mimetype,
        ACL: 'public-read',
    };

    const data = await s3.upload(params).promise();
    return data.Location; 
} 

module.exports = uploadFileToS3;