import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { 
  User, 
  FileText, 
  Settings, 
  ThumbsUp, 
  AlertCircle, 
  CheckCircle2,
  Edit3,
  Upload,
  Star,
  MessageSquare
} from 'lucide-react';
import { FeedbackModal } from '@/components/ui/feedback-modal';

export function ProfileTab() {
  const [profileData, setProfileData] = useState({
    topics: ['AI', 'Machine Learning', 'Data Science'],
    feeRange: '$3,000 - $8,000',
    travelRules: 'Domestic only, 3 days advance notice',
    availability: 'Available weekdays',
    voiceprintTone: 'professional',
    voiceprintStyle: 'informative'
  });

  const [isFeedbackModalOpen, setIsFeedbackModalOpen] = useState(false);
  const [profileHealth] = useState(72);

  const healthItems = [
    { field: 'Bio', status: 'complete', lastUpdated: '2 days ago' },
    { field: 'Topics', status: 'complete', lastUpdated: '1 week ago' },
    { field: 'Fee range', status: 'complete', lastUpdated: '3 days ago' },
    { field: 'Reels/Videos', status: 'outdated', lastUpdated: '2 months ago' },
    { field: 'One-sheet', status: 'missing', lastUpdated: 'Never' },
    { field: 'Availability', status: 'complete', lastUpdated: '1 day ago' }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'complete': return <CheckCircle2 className="h-4 w-4 text-green-600" />;
      case 'outdated': return <AlertCircle className="h-4 w-4 text-yellow-600" />;
      case 'missing': return <AlertCircle className="h-4 w-4 text-red-600" />;
      default: return null;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'complete': return 'bg-green-50 text-green-700';
      case 'outdated': return 'bg-yellow-50 text-yellow-700';
      case 'missing': return 'bg-red-50 text-red-700';
      default: return 'bg-gray-50 text-gray-700';
    }
  };

  const handleFeedbackSubmit = (tags: string[], feedback: string) => {
    console.log('Feedback submitted:', { tags, feedback });
    // In real app, send to backend
  };

  return (
    <div className="space-y-6">
      {/* Profile Health Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5 text-primary" />
            Profile Health Score
          </CardTitle>
          <CardDescription>
            Keep your profile updated to get better opportunities
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="text-3xl font-bold text-primary">{profileHealth}%</div>
            <Badge variant={profileHealth >= 80 ? "default" : profileHealth >= 60 ? "secondary" : "destructive"}>
              {profileHealth >= 80 ? "Excellent" : profileHealth >= 60 ? "Good" : "Needs Improvement"}
            </Badge>
          </div>
          <Progress value={profileHealth} className="h-2" />
          
          <div className="grid gap-3">
            {healthItems.map((item, index) => (
              <div key={index} className="flex items-center justify-between p-3 border border-border rounded-lg">
                <div className="flex items-center gap-3">
                  {getStatusIcon(item.status)}
                  <div>
                    <p className="font-medium text-sm">{item.field}</p>
                    <p className="text-xs text-muted-foreground">Last updated: {item.lastUpdated}</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={getStatusColor(item.status)}>
                    {item.status}
                  </Badge>
                  <Button size="sm" variant="outline">
                    <Edit3 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Profile Fields */}
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Basic Information
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Speaking Topics</Label>
              <div className="flex flex-wrap gap-2">
                {profileData.topics.map((topic, index) => (
                  <Badge key={index} variant="secondary">{topic}</Badge>
                ))}
              </div>
              <Button size="sm" variant="outline">Add Topic</Button>
            </div>

            <div className="space-y-2">
              <Label>Fee Range</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select fee range" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1000-3000">$1,000 - $3,000</SelectItem>
                  <SelectItem value="3000-5000">$3,000 - $5,000</SelectItem>
                  <SelectItem value="5000-8000">$5,000 - $8,000</SelectItem>
                  <SelectItem value="8000+">$8,000+</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Travel Rules</Label>
              <Textarea 
                placeholder="e.g., Domestic only, 3 days advance notice"
                value={profileData.travelRules}
                className="min-h-20"
              />
            </div>

            <div className="space-y-2">
              <Label>Availability</Label>
              <Input 
                placeholder="e.g., Available weekdays"
                value={profileData.availability}
              />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Media & Assets
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                <p className="text-sm font-medium">Upload Speaker Reel</p>
                <p className="text-xs text-muted-foreground">MP4, MOV up to 50MB</p>
                <Button size="sm" variant="outline" className="mt-2">Choose File</Button>
              </div>

              <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                <Upload className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
                <p className="text-sm font-medium">Upload One-Sheet</p>
                <p className="text-xs text-muted-foreground">PDF up to 10MB</p>
                <Button size="sm" variant="outline" className="mt-2">Choose File</Button>
              </div>

              <div className="space-y-2">
                <Label>Bio (Short)</Label>
                <Textarea 
                  placeholder="150-word professional bio"
                  className="min-h-20"
                />
              </div>

              <div className="space-y-2">
                <Label>Bio (Long)</Label>
                <Textarea 
                  placeholder="500-word detailed bio"
                  className="min-h-32"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Voiceprint Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Voiceprint Settings
          </CardTitle>
          <CardDescription>
            Control the tone and style for AI-generated outreach templates
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label>Communication Tone</Label>
              <Select value={profileData.voiceprintTone}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="professional">Professional</SelectItem>
                  <SelectItem value="friendly">Friendly</SelectItem>
                  <SelectItem value="enthusiastic">Enthusiastic</SelectItem>
                  <SelectItem value="formal">Formal</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Writing Style</Label>
              <Select value={profileData.voiceprintStyle}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="informative">Informative</SelectItem>
                  <SelectItem value="conversational">Conversational</SelectItem>
                  <SelectItem value="concise">Concise</SelectItem>
                  <SelectItem value="detailed">Detailed</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <Separator />

          <div className="space-y-3">
            <Label>Personal Touch Settings</Label>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Include personal anecdotes</p>
                  <p className="text-xs text-muted-foreground">Add relevant personal stories to outreach</p>
                </div>
                <Switch />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Reference mutual connections</p>
                  <p className="text-xs text-muted-foreground">Mention shared contacts when applicable</p>
                </div>
                <Switch />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium">Include social proof</p>
                  <p className="text-xs text-muted-foreground">Mention recent speaking engagements</p>
                </div>
                <Switch defaultChecked />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Feedback Loop */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Feedback & Improvements
          </CardTitle>
          <CardDescription>
            Help us improve your opportunity matching and experience
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-3">
            <Button 
              variant="outline" 
              onClick={() => setIsFeedbackModalOpen(true)}
              className="flex items-center gap-2"
            >
              <ThumbsUp className="h-4 w-4" />
              Provide Feedback
            </Button>
            <Button variant="outline">
              Rate Match Quality
            </Button>
            <Button variant="outline">
              Suggest Features
            </Button>
          </div>

          <div className="p-4 bg-surface rounded-lg">
            <p className="text-sm text-muted-foreground">
              Your feedback helps us improve opportunity matching. Recent feedback: 
              "More tech conferences in the Pacific Northwest" - Thanks for the suggestion!
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Feedback Modal */}
      <FeedbackModal
        isOpen={isFeedbackModalOpen}
        onClose={() => setIsFeedbackModalOpen(false)}
        onSubmit={handleFeedbackSubmit}
        title="Share your feedback"
      />
    </div>
  );
}