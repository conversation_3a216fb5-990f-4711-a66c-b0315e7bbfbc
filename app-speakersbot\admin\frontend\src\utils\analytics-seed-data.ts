import dayjs from 'dayjs';
import type {
  SubmissionLog, AffiliateMetrics, AffiliateCommission, SubscriptionEvent, 
  ChurnData, FlaggedItem, ModerationAction, UserFeedback, FeatureRequest, 
  SessionData, OtherFieldAnalysis, CategorySuggestion, BotStatus, 
  ScrapingAlert, PaymentFailure, AutofillMetrics, RevenueMetrics
} from '../types';

// Generate demo submission logs for Form Autofill Tracker
export const generateDemoSubmissionLogs = (): SubmissionLog[] => {
  const logs: SubmissionLog[] = [];
  const errorTypes = ['captcha', 'js-heavy', '404', 'validation', 'timeout', 'other'];
  const browsers = ['Chrome 122', 'Chrome 121', 'Chrome 120', 'Edge 122', 'Safari 17'];
  const extensionVersions = ['2.1.4', '2.1.3', '2.1.2', '2.0.9'];
  
  for (let i = 0; i < 150; i++) {
    const isSuccess = Math.random() > 0.25; // 75% success rate
    logs.push({
      id: `sub_${i + 1}`,
      speakerId: `speaker_${Math.floor(Math.random() * 50) + 1}`,
      opportunityId: `opp_${Math.floor(Math.random() * 80) + 1}`,
      sourceUrl: `https://eventsite${Math.floor(Math.random() * 20) + 1}.com/speaker-form`,
      attemptTime: dayjs().subtract(Math.floor(Math.random() * 90), 'days').toISOString(),
      duration: Math.floor(Math.random() * 120) + 10, // 10-130 seconds
      result: isSuccess ? 'success' : 'failed',
      errorType: isSuccess ? undefined : errorTypes[Math.floor(Math.random() * errorTypes.length)] as any,
      extensionVersion: extensionVersions[Math.floor(Math.random() * extensionVersions.length)],
      browserInfo: browsers[Math.floor(Math.random() * browsers.length)],
      chromeOS: Math.random() > 0.7 ? 'Windows 11' : Math.random() > 0.5 ? 'macOS 14' : 'Ubuntu 22.04',
      notes: Math.random() > 0.8 ? 'User reported issue with form validation' : undefined,
      investigated: Math.random() > 0.9
    });
  }
  
  return logs.sort((a, b) => dayjs(b.attemptTime).unix() - dayjs(a.attemptTime).unix());
};

// Generate demo affiliate metrics
export const generateDemoAffiliateMetrics = (): AffiliateMetrics[] => {
  const affiliates: AffiliateMetrics[] = [];
  const names = ['TechSpeakers Pro', 'EventBoost', 'SpeakerConnect', 'TalentBridge', 'ExpertLink'];
  
  for (let i = 0; i < 5; i++) {
    const clicks = Math.floor(Math.random() * 500) + 100;
    const signups = Math.floor(clicks * (Math.random() * 0.15 + 0.05)); // 5-20% conversion
    const conversions = Math.floor(signups * (Math.random() * 0.4 + 0.1)); // 10-50% conversion
    
    affiliates.push({
      id: `aff_${i + 1}`,
      name: names[i],
      email: `contact@${names[i].toLowerCase().replace(/\s+/g, '')}.com`,
      affiliateId: `AFF${(1000 + i).toString()}`,
      link: `https://app.digitalspeakeragent.com?ref=AFF${1000 + i}`,
      clicks,
      signups,
      conversions,
      createdAt: dayjs().subtract(Math.floor(Math.random() * 365), 'days').toISOString(),
      revenue: conversions * (Math.random() * 80 + 20), // $20-100 per conversion
      conversionRate: (conversions / clicks) * 100,
      clickThroughRate: (signups / clicks) * 100,
      activeCampaigns: [`Campaign ${i + 1}A`, `Campaign ${i + 1}B`],
      payoutStatus: Math.random() > 0.7 ? 'pending' : 'paid',
      lastPayoutDate: Math.random() > 0.3 ? dayjs().subtract(Math.floor(Math.random() * 30), 'days').toISOString() : undefined,
      totalPayout: conversions * (Math.random() * 30 + 10) // $10-40 payout per conversion
    });
  }
  
  return affiliates;
};

// Generate demo affiliate commissions
export const generateDemoAffiliateCommissions = (): AffiliateCommission[] => {
  const commissions: AffiliateCommission[] = [];
  
  for (let i = 0; i < 20; i++) {
    commissions.push({
      id: `comm_${i + 1}`,
      affiliateId: `aff_${Math.floor(Math.random() * 5) + 1}`,
      period: dayjs().subtract(i, 'months').format('YYYY-MM'),
      amount: Math.floor(Math.random() * 500) + 50,
      status: Math.random() > 0.7 ? 'pending' : Math.random() > 0.5 ? 'paid' : 'processing',
      payoutDate: Math.random() > 0.3 ? dayjs().subtract(Math.floor(Math.random() * 60), 'days').toISOString() : undefined,
      description: `Commission for ${dayjs().subtract(i, 'months').format('MMMM YYYY')}`
    });
  }
  
  return commissions;
};

// Generate demo subscription events
export const generateDemoSubscriptionEvents = (): SubscriptionEvent[] => {
  const events: SubscriptionEvent[] = [];
  const eventTypes = ['trial_started', 'trial_converted', 'subscription_created', 'subscription_upgraded', 'subscription_cancelled', 'subscription_renewed'];
  const planIds = ['starter', 'pro', 'concierge'];
  const amounts = { starter: 29, pro: 99, concierge: 299 };
  
  for (let i = 0; i < 100; i++) {
    const eventType = eventTypes[Math.floor(Math.random() * eventTypes.length)] as any;
    const planId = planIds[Math.floor(Math.random() * planIds.length)] as keyof typeof amounts;
    
    events.push({
      id: `evt_${i + 1}`,
      userId: `user_${Math.floor(Math.random() * 50) + 1}`,
      eventType,
      planId,
      amount: amounts[planId],
      timestamp: dayjs().subtract(Math.floor(Math.random() * 90), 'days').toISOString(),
      metadata: { source: 'web', campaign: Math.random() > 0.7 ? 'summer2024' : undefined }
    });
  }
  
  return events.sort((a, b) => dayjs(b.timestamp).unix() - dayjs(a.timestamp).unix());
};

// Generate demo churn data
export const generateDemoChurnData = (): ChurnData[] => {
  const churnData: ChurnData[] = [];
  const reasons = ['Too expensive', 'Not enough matches', 'Found alternative', 'Budget cuts', 'Feature gaps'];
  const planIds = ['starter', 'pro', 'concierge'];
  const mrr = { starter: 29, pro: 99, concierge: 299 };
  
  for (let i = 0; i < 25; i++) {
    const planId = planIds[Math.floor(Math.random() * planIds.length)] as keyof typeof mrr;
    
    churnData.push({
      userId: `user_${i + 1}`,
      planId,
      churnDate: dayjs().subtract(Math.floor(Math.random() * 90), 'days').toISOString(),
      churnReason: reasons[Math.floor(Math.random() * reasons.length)],
      mrr: mrr[planId]
    });
  }
  
  return churnData;
};

// Generate demo flagged items
export const generateDemoFlaggedItems = (): FlaggedItem[] => {
  const items: FlaggedItem[] = [];
  const itemTypes = ['speaker', 'opportunity', 'submission', 'affiliate'];
  const flagTypes = ['spam', 'duplicate', 'inappropriate', 'quality', 'fraud'];
  const statuses = ['pending', 'approved', 'rejected', 'banned'];
  
  for (let i = 0; i < 30; i++) {
    const itemType = itemTypes[Math.floor(Math.random() * itemTypes.length)] as any;
    const flagType = flagTypes[Math.floor(Math.random() * flagTypes.length)] as any;
    const status = statuses[Math.floor(Math.random() * statuses.length)] as any;
    
    items.push({
      id: `flag_${i + 1}`,
      itemType,
      itemId: `${itemType}_${Math.floor(Math.random() * 50) + 1}`,
      flagType,
      flaggedBy: `admin_${Math.floor(Math.random() * 3) + 1}@company.com`,
      flaggedAt: dayjs().subtract(Math.floor(Math.random() * 30), 'days').toISOString(),
      status,
      adminNote: status !== 'pending' ? 'Reviewed and processed according to guidelines' : undefined,
      resolvedBy: status !== 'pending' ? `admin_${Math.floor(Math.random() * 3) + 1}@company.com` : undefined,
      resolvedAt: status !== 'pending' ? dayjs().subtract(Math.floor(Math.random() * 15), 'days').toISOString() : undefined
    });
  }
  
  return items;
};

// Generate demo user feedback
export const generateDemoUserFeedback = (): UserFeedback[] => {
  const feedback: UserFeedback[] = [];
  const feedbackTypes = ['why_not_apply', 'post_application', 'feature_request', 'general'];
  const whyNotApplyTags = ['Budget too low', 'Location mismatch', 'Topic not relevant', 'Timing conflict', 'Requirements unclear'];
  const postApplicationTags = ['Quick response', 'No response', 'Great experience', 'Booked successfully', 'Needs improvement'];
  const statuses = ['new', 'reviewed', 'addressed'];
  
  for (let i = 0; i < 80; i++) {
    const feedbackType = feedbackTypes[Math.floor(Math.random() * feedbackTypes.length)] as any;
    let tags: string[] = [];
    
    if (feedbackType === 'why_not_apply') {
      tags = [whyNotApplyTags[Math.floor(Math.random() * whyNotApplyTags.length)]];
    } else if (feedbackType === 'post_application') {
      tags = [postApplicationTags[Math.floor(Math.random() * postApplicationTags.length)]];
    }
    
    feedback.push({
      id: `feedback_${i + 1}`,
      userId: `user_${Math.floor(Math.random() * 50) + 1}`,
      feedbackType,
      tags,
      rating: Math.random() > 0.3 ? Math.floor(Math.random() * 5) + 1 as any : undefined,
      comment: Math.random() > 0.5 ? 'This is sample feedback from a user about their experience.' : undefined,
      timestamp: dayjs().subtract(Math.floor(Math.random() * 60), 'days').toISOString(),
      status: statuses[Math.floor(Math.random() * statuses.length)] as any
    });
  }
  
  return feedback;
};

// Generate demo feature requests
export const generateDemoFeatureRequests = (): FeatureRequest[] => {
  const requests: FeatureRequest[] = [];
  const titles = [
    'Better mobile app experience',
    'Advanced filtering options',
    'Calendar integration',
    'Bulk operations for opportunities',
    'Enhanced reporting dashboard',
    'API access for integrations',
    'Custom email templates',
    'Video call scheduling',
    'Multi-language support',
    'Advanced matching algorithm'
  ];
  
  for (let i = 0; i < titles.length; i++) {
    requests.push({
      id: `req_${i + 1}`,
      title: titles[i],
      description: `Detailed description for ${titles[i].toLowerCase()}`,
      requestedBy: `user_${Math.floor(Math.random() * 50) + 1}`,
      votes: Math.floor(Math.random() * 25) + 1,
      status: ['pending', 'in_progress', 'completed', 'rejected'][Math.floor(Math.random() * 4)] as any,
      priority: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
      timestamp: dayjs().subtract(Math.floor(Math.random() * 120), 'days').toISOString()
    });
  }
  
  return requests;
};

// Generate demo bot status
export const generateDemoBotStatus = (): BotStatus[] => {
  const bots: BotStatus[] = [];
  const botNames = ['LinkedIn Scraper', 'Eventbrite Monitor', 'Meetup Collector', 'Conference Tracker', 'Speaker Bureau Monitor'];
  const engines = ['Playwright', 'Puppeteer', 'Scrapy', 'Selenium'];
  const statuses = ['success', 'failed', 'running', 'stopped'];
  
  for (let i = 0; i < botNames.length; i++) {
    const lastStatus = statuses[Math.floor(Math.random() * statuses.length)] as any;
    
    bots.push({
      id: `bot_${i + 1}`,
      botName: botNames[i],
      engine: engines[Math.floor(Math.random() * engines.length)] as any,
      lastRunAt: dayjs().subtract(Math.floor(Math.random() * 24), 'hours').toISOString(),
      lastDuration: Math.floor(Math.random() * 300) + 30, // 30-330 seconds
      successRate24h: Math.floor(Math.random() * 40) + 60, // 60-100%
      lastStatus,
      errorMessage: lastStatus === 'failed' ? 'Rate limit exceeded on target site' : undefined,
      nextScheduledRun: dayjs().add(Math.floor(Math.random() * 6) + 1, 'hours').toISOString()
    });
  }
  
  return bots;
};

// Generate demo scraping alerts
export const generateDemoScrapingAlerts = (): ScrapingAlert[] => {
  const alerts: ScrapingAlert[] = [];
  const alertTypes = ['low_success_rate', 'bot_down', 'quota_exceeded', 'captcha_block'];
  const severities = ['low', 'medium', 'high', 'critical'];
  const messages = {
    low_success_rate: 'Bot success rate below 80% in last 24 hours',
    bot_down: 'Bot has been offline for more than 2 hours',
    quota_exceeded: 'Daily scraping quota has been exceeded',
    captcha_block: 'Multiple bots blocked by CAPTCHA protection'
  };
  
  for (let i = 0; i < 8; i++) {
    const alertType = alertTypes[Math.floor(Math.random() * alertTypes.length)] as keyof typeof messages;
    
    alerts.push({
      id: `alert_${i + 1}`,
      alertType,
      message: messages[alertType],
      severity: severities[Math.floor(Math.random() * severities.length)] as any,
      timestamp: dayjs().subtract(Math.floor(Math.random() * 168), 'hours').toISOString(), // Last week
      acknowledged: Math.random() > 0.4,
      acknowledgedBy: Math.random() > 0.4 ? `admin_${Math.floor(Math.random() * 3) + 1}@company.com` : undefined
    });
  }
  
  return alerts.sort((a, b) => dayjs(b.timestamp).unix() - dayjs(a.timestamp).unix());
};

// Generate demo payment failures
export const generateDemoPaymentFailures = (): PaymentFailure[] => {
  const failures: PaymentFailure[] = [];
  const reasons = ['Insufficient funds', 'Card expired', 'Card declined', 'Payment processor error', 'Bank rejection'];
  
  for (let i = 0; i < 15; i++) {
    const recovered = Math.random() > 0.6;
    
    failures.push({
      id: `fail_${i + 1}`,
      userId: `user_${Math.floor(Math.random() * 50) + 1}`,
      amount: [29, 99, 299][Math.floor(Math.random() * 3)],
      currency: 'USD',
      failureReason: reasons[Math.floor(Math.random() * reasons.length)],
      timestamp: dayjs().subtract(Math.floor(Math.random() * 60), 'days').toISOString(),
      recovered,
      recoveryDate: recovered ? dayjs().subtract(Math.floor(Math.random() * 30), 'days').toISOString() : undefined,
      retryAttempts: Math.floor(Math.random() * 3) + 1
    });
  }
  
  return failures;
};

// Calculate autofill metrics from submission logs
export const calculateAutofillMetrics = (submissions: SubmissionLog[]): AutofillMetrics => {
  const totalAttempts = submissions.length;
  const successfulSubmissions = submissions.filter(s => s.result === 'success').length;
  const successRate = totalAttempts > 0 ? (successfulSubmissions / totalAttempts) * 100 : 0;
  const avgTimeToSubmit = submissions.reduce((sum, s) => sum + s.duration, 0) / totalAttempts || 0;
  
  const errorDistribution: { [key: string]: number } = {};
  submissions.filter(s => s.errorType).forEach(s => {
    errorDistribution[s.errorType!] = (errorDistribution[s.errorType!] || 0) + 1;
  });
  
  return {
    successRate: Number(successRate.toFixed(1)),
    avgTimeToSubmit: Number(avgTimeToSubmit.toFixed(1)),
    totalAttempts,
    successfulSubmissions,
    errorDistribution
  };
};

// Calculate revenue metrics from subscription events
export const calculateRevenueMetrics = (subscriptionEvents: SubscriptionEvent[], churnData: ChurnData[]): RevenueMetrics => {
  const currentMonth = dayjs().format('YYYY-MM');
  const lastMonth = dayjs().subtract(1, 'month').format('YYYY-MM');
  
  // Current active subscriptions (simplified calculation)
  const activeSubscriptions = subscriptionEvents.filter(e => 
    ['subscription_created', 'subscription_upgraded', 'subscription_renewed'].includes(e.eventType)
  );
  
  const totalMRR = activeSubscriptions.reduce((sum, s) => sum + s.amount, 0);
  const newMRR = subscriptionEvents
    .filter(e => e.eventType === 'subscription_created' && dayjs(e.timestamp).format('YYYY-MM') === currentMonth)
    .reduce((sum, s) => sum + s.amount, 0);
  
  const expansionMRR = subscriptionEvents
    .filter(e => e.eventType === 'subscription_upgraded' && dayjs(e.timestamp).format('YYYY-MM') === currentMonth)
    .reduce((sum, s) => sum + s.amount, 0);
  
  const churnedMRR = churnData
    .filter(c => dayjs(c.churnDate).format('YYYY-MM') === currentMonth)
    .reduce((sum, c) => sum + c.mrr, 0);
  
  const netMRRGrowth = newMRR + expansionMRR - churnedMRR;
  const totalARR = totalMRR * 12;
  
  return {
    totalMRR,
    newMRR,
    expansionMRR,
    churnedMRR,
    netMRRGrowth,
    totalARR
  };
};