import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "../ui/card";
import {
  Re<PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>hart,
  Bar,
  // <PERSON><PERSON><PERSON>,
  // Pie,
  // Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
} from "recharts";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle, RefreshCw } from "lucide-react";
import { Badge } from "../ui/badge";
import { useGetDashboardHealthQuery } from "@/apis";
import { Skeleton } from "../ui/skeleton";
import { useToast } from "@/hooks/use-toast";
import { InvalidTokenHandler } from "../common/InvalidTokenHandler";
const SystemHealthAnalytics: React.FC = () => {
  // Static data for bot status pie chart (keeping this as it's not in API response)
  // const botStatusData = [
  //   { name: "Active", count: 12, color: "#10b981" },
  //   { name: "Inactive", count: 3, color: "#ef4444" },
  //   { name: "Maintenance", count: 2, color: "#f59e0b" },
  // ];

  // Static data for service uptime (keeping this as it's not in API response)
  // const uptimeData = [
  //   { service: "Web Scraper", uptime: 99.8, incidents: 1 },
  //   { service: "Matching Engine", uptime: 99.9, incidents: 0 },
  //   { service: "Email Service", uptime: 98.5, incidents: 3 },
  //   { service: "Payment Gateway", uptime: 99.7, incidents: 1 },
  //   { service: "File Storage", uptime: 99.2, incidents: 2 },
  //   { service: "Authentication", uptime: 99.9, incidents: 0 },
  // ];

  const { data: healthData, isLoading, error } = useGetDashboardHealthQuery({});
  const { toast } = useToast();

  // Show toast notifications based on data fetch status
  React.useEffect(() => {
    if (healthData && !isLoading) {
      // toast({
      //   title: "System health analytics loaded successfully",
      // });
    }
  }, [healthData, isLoading, toast]);

  React.useEffect(() => {
    if (error) {
      toast({
        title: "Failed to load health data",
        variant: "destructive",
      });
    }
  }, [error, toast]);

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="space-y-6">
      {isLoading ? (
        <div>
          <Skeleton className="h-5 w-72" />
          <Skeleton className="h-4 w-96 mt-2" />
        </div>
      ) : (
        <div>
          <h3 className="text-xl font-semibold text-foreground mb-2">
            System Health & Bot Monitoring
          </h3>
          <p className="text-muted-foreground text-sm mb-6">
            Monitor system performance, bot status, and service uptime metrics
          </p>
        </div>
      )}

      {/* KPI Cards Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i} className="bg-tertiary border-border">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-8 w-16" />
                  <Skeleton className="h-6 w-20" />
                </div>
                <Skeleton className="h-6 w-6 rounded" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Charts Skeleton */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i} className="bg-tertiary border-border">
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-[300px] w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  if (isLoading) {
    return <LoadingSkeleton />;
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-xl font-semibold text-foreground mb-2">
            System Health & Bot Monitoring
          </h3>
          <p className="text-muted-foreground text-sm mb-6">
            Monitor system performance, bot status, and service uptime metrics
          </p>
        </div>
        <Card className="bg-tertiary border-border">
          <CardContent className="p-8 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Error Loading Data
            </h3>
            <p className="text-muted-foreground">
              Unable to fetch system health data. Please try again later.
            </p>
          </CardContent>
        </Card>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "good":
        return "hsl(var(--status-active))"; // Green for good/active
      case "active":
        return "hsl(var(--status-active))"; // Green for active
      case "warning":
        return "hsl(var(--status-pending))"; // Orange for warning/pending
      case "pending":
        return "hsl(var(--status-pending))"; // Orange for pending
      case "critical":
        return "hsl(var(--status-rejected))"; // Red for critical/rejected
      case "rejected":
        return "hsl(var(--status-rejected))"; // Red for rejected
      case "premium":
        return "hsl(var(--status-premium))"; // Golden for premium
      default:
        return "hsl(var(--muted))";
    }
  };

  return (
    <div className="space-y-6">
      <InvalidTokenHandler error={error} />
      <div>
        <h3 className="text-xl font-semibold text-foreground mb-2">
          System Health & Bot Monitoring
        </h3>
        <p className="text-muted-foreground text-sm mb-6">
          Monitor system performance, bot status, and service uptime metrics
        </p>
      </div>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="bg-tertiary border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  System Uptime
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {healthData?.data?.health?.systemUptime?.percentage?.toFixed(
                    1
                  )}
                  %
                </p>
                <Badge variant="secondary" className="mt-1">
                  {healthData?.data?.health?.systemUptime?.change}
                </Badge>
              </div>
              <CheckCircle className="h-6 w-6 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-tertiary border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Critical Alerts
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {healthData?.data?.health?.criticalAlerts?.count}
                </p>
                <Badge variant="destructive" className="mt-1">
                  {healthData?.data?.health?.criticalAlerts?.status}
                </Badge>
              </div>
              <AlertTriangle className="h-6 w-6 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-tertiary border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Avg Response Time
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {(healthData?.data?.health?.avgResponse?.timeHours).toFixed(
                    2
                  )}{" "}
                  h
                </p>
                <Badge
                  variant={
                    healthData?.data?.health?.avgResponse?.status === "Slow"
                      ? "destructive"
                      : healthData?.data?.health?.avgResponse?.status ===
                        "Good performance"
                      ? "secondary"
                      : "outline"
                  }
                  className="mt-1"
                >
                  {healthData?.data?.health?.avgResponse?.status}
                </Badge>
              </div>
              <RefreshCw className="h-6 w-6 text-dashboard-light-blue" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-tertiary hidden border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  System Status
                </p>
                <p className="text-2xl font-bold text-foreground">
                  {healthData?.data?.health?.systemUptime?.percentage > 90
                    ? "Healthy"
                    : "Degraded"}
                </p>
                <Badge
                  variant={
                    healthData?.data?.health?.systemUptime?.percentage > 90
                      ? "secondary"
                      : "destructive"
                  }
                  className="mt-1"
                >
                  {healthData?.data?.health?.systemUptime?.percentage > 90
                    ? "Operational"
                    : "Issues Detected"}
                </Badge>
              </div>
              <Bot className="h-6 w-6 text-dashboard-medium-blue" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* <Card className="bg-tertiary border-border">
          <CardHeader>
            <CardTitle className="text-foreground">
              Bot Status Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={botStatusData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="count"
                  label={({ name, count }: any) => `${name}: ${count}`}
                >
                  {botStatusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card> */}

        <Card className="bg-tertiary border-border">
          <CardHeader>
            <CardTitle className="text-foreground">
              System Alerts Timeline
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              {healthData?.data?.alertsTimeline?.length > 0 ? (   
              <BarChart data={healthData?.data?.alertsTimeline || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="error" stackId="a" fill="#ef4444" name="Errors" />
                <Bar
                  dataKey="success"
                  stackId="a"
                  fill="#10b981"
                  name="Success"
                />
              </BarChart>
              ) : (
                <div className="flex justify-center items-center h-full">
                  <p className="text-muted-foreground text-sm">No Alerts Timeline Data Available</p>
                </div>
              )}
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="bg-tertiary border-border">
          <CardHeader>
            <CardTitle className="text-foreground">
              Performance Timeline
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              {healthData?.data?.performanceTimeline?.length > 0 ? (   
              <BarChart data={healthData?.data?.performanceTimeline || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="month" />
                <YAxis />
                <Tooltip
                  formatter={(value) => [
                    `${(value as number).toFixed(2)} hours`,
                    "Avg Response Time",
                  ]}
                />
                <Bar
                  dataKey="avgTimeHours"
                  fill="hsl(var(--dashboard-dark-blue))"
                  name="Avg Response Time (hours)"
                />
              </BarChart>
              ) : (
                <div className="flex justify-center items-center h-full">
                  <p className="text-muted-foreground text-sm">No Performance Timeline Data Available</p>
                </div>
              )}
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* <Card className="bg-tertiary border-border">
          <CardHeader>
            <CardTitle className="text-foreground">Service Uptime</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={uptimeData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="service" />
                <YAxis />
                <Tooltip />
                <Bar
                  dataKey="uptime"
                  fill="hsl(var(--dashboard-dark-blue))"
                  name="Uptime %"
                />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card> */}
      </div>
    </div>
  );
};

export default SystemHealthAnalytics;
