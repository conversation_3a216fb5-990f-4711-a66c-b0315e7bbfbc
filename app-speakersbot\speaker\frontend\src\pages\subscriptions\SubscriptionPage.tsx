import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, Star, Zap, TreePine, Loader2, AlertCircle } from "lucide-react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { useGetSubscriptionPlansQuery } from "@/apis/subscriptionApi";
import { Alert, AlertDescription } from "@/components/ui/alert";

export const SubscriptionPage = () => {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [expandedPlans, setExpandedPlans] = useState<Record<string, boolean>>(
    {}
  );

  // Fetch subscription plans from API
  const {
    data: plansResponse,
    isLoading,
    error,
    refetch,
  } = useGetSubscriptionPlansQuery();

  type PlanView = {
    id: string | number;
    name: string;
    price: string;
    period: string;
    description: string;
    features: string[];
    buttonText: string;
    popular: boolean;
    color: string;
    iconColor: string;
  };

  // Normalize API response: support both { data: { pricing_plans: [] } } and { data: [] }
  const pricingPlans: any[] = Array.isArray(
    (plansResponse as any)?.data?.pricing_plans
  )
    ? (plansResponse as any).data.pricing_plans
    : Array.isArray((plansResponse as any)?.data)
    ? (plansResponse as any).data
    : [];

  // Use API data if available, otherwise fallback to hardcoded plans
  const plans: Record<string, PlanView> =
    (pricingPlans?.length ?? 0) > 0
      ? pricingPlans.reduce((acc: Record<string, PlanView>, plan: any) => {
          // Format price using currency and amount
          const amountNumber = Number(plan.amount);
          const currencyCode = (plan.currency || "USD")
            .toString()
            .toUpperCase();
          const formattedPrice = Number.isFinite(amountNumber)
            ? new Intl.NumberFormat("en-US", {
                style: "currency",
                currency: currencyCode,
              }).format(amountNumber)
            : `$${plan.amount}`;

          // Map billing interval to period label
          const period =
            plan.billing_interval === "month"
              ? "per month"
              : plan.billing_interval === "year"
              ? "per year"
              : "one-time";

          // Parse features from description which arrives as a JSON string array
          let features: string[] = [];
          if (
            typeof plan.description === "string" &&
            plan.description.trim().length > 0
          ) {
            try {
              const parsed = JSON.parse(plan.description);
              if (Array.isArray(parsed)) {
                features = parsed.filter((x) => typeof x === "string");
              } else if (typeof parsed === "string") {
                features = [parsed];
              }
            } catch {
              // Fallback: treat raw description as a single feature line
              features = [plan.description];
            }
          }

          const nameLower = (plan.name || "").toString().toLowerCase();
          const isPopular =
            nameLower.includes("pro") || nameLower.includes("premium");

          acc[String(plan.id)] = {
            id: plan.id,
            name: plan.name,
            price: formattedPrice,
            period,
            description: "",
            features,
            buttonText:
              plan.billing_interval === "one-time" ? "Buy" : "Subscribe",
            popular: isPopular,
            color: "bg-surface",
            iconColor: isPopular ? "text-orange-500" : "text-pink-500",
          };
          return acc;
        }, {} as Record<string, PlanView>)
      : ({} as unknown as Record<string, PlanView>);

  const handleSubscribe = (planId: string) => {
    // Handle subscription logic here
    // You can integrate with createSubscription mutation here
    // Example:
    // const [createSubscription] = useCreateSubscriptionMutation();
    // createSubscription({ planId, autoRenew: true });
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="space-y-8">
        <div className="text-center space-y-3">
          <div className="flex items-center justify-center gap-2 text-sm text-foreground-muted">
            <Zap className="h-4 w-4" />
            Pricing Plans
          </div>
          <h2 className="text-3xl font-bold text-foreground">
            Transparent Pricing, No Surprises
          </h2>
        </div>

        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span className="text-foreground-muted">
              Loading subscription plans...
            </span>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="space-y-8">
        <div className="text-center space-y-3">
          <div className="flex items-center justify-center gap-2 text-sm text-foreground-muted">
            <Zap className="h-4 w-4" />
            Pricing Plans
          </div>
          <h2 className="text-3xl font-bold text-foreground">
            Transparent Pricing, No Surprises
          </h2>
        </div>

        <Alert className="max-w-2xl mx-auto">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            <span>Failed to load subscription plans. Using fallback data.</span>
            <Button variant="outline" size="sm" onClick={() => refetch()}>
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center space-y-3">
        <div className="flex items-center justify-center gap-2 text-sm text-foreground-muted">
          <Zap className="h-4 w-4" />
          Pricing Plans
        </div>
        <h2 className="text-3xl font-bold text-foreground">
          Transparent Pricing, No Surprises
        </h2>
      </div>

      {/* Pricing Cards */}
      <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto !mt-20">
        {Object.entries(plans).map(([planId, plan]) => (
          <Card
            key={planId}
            className={`bg-gradient-to-br ${plan.color} border shadow-price hover:scale-105 transition-smooth duration-300 relative overflow-hidden`}
          >
            <div className="absolute top-4 right-4">
              <TreePine className={`h-12 w-12 ${plan.iconColor} opacity-60`} />
            </div>
            {/* {plan.popular && (
              <div className="absolute top-4 left-4">
                <Badge className="bg-primary text-primary-foreground">
                  <Star className="h-3 w-3 mr-1" />
                  Popular
                </Badge>
              </div>
            )} */}
            <CardContent className="p-8 h-full flex flex-col justify-between gap-5">
              <div>
                <div className="mb-6">
                  <h3 className="text-xl font-semibold text-foreground mb-2">
                    {plan.name}
                  </h3>
                  <div className="mb-2">
                    <span className="text-3xl font-bold text-foreground">
                      {plan.price}
                    </span>
                  </div>
                  <p className="text-sm text-foreground-muted mb-1">
                    {plan.period}
                  </p>
                </div>

                {(() => {
                  const isExpanded = !!expandedPlans[planId];
                  const maxCollapsed = 4;
                  const visibleFeatures = isExpanded
                    ? plan.features
                    : plan.features.slice(0, maxCollapsed);
                  const remaining = Math.max(
                    0,
                    plan.features.length - maxCollapsed
                  );
                  return (
                    <>
                      <ul className="space-y-3 mb-4">
                        {visibleFeatures.map(
                          (feature: string, index: number) => (
                            <li
                              key={index}
                              className="flex items-start gap-3 text-sm"
                            >
                              <div
                                className={`rounded-full p-1 ${
                                  plan.popular ? "bg-orange-100" : "bg-pink-100"
                                }`}
                              >
                                <Check
                                  className={`h-3 w-3 ${
                                    plan.popular
                                      ? "text-orange-600"
                                      : "text-pink-600"
                                  }`}
                                />
                              </div>
                              <span className="text-foreground">{feature}</span>
                            </li>
                          )
                        )}
                      </ul>
                      {remaining > 0 && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="px-0 h-auto text-primary hover:bg-transparent"
                          onClick={() =>
                            setExpandedPlans((prev) => ({
                              ...prev,
                              [planId]: !isExpanded,
                            }))
                          }
                        >
                          {isExpanded
                            ? "View less"
                            : `View more (+${remaining})`}
                        </Button>
                      )}
                    </>
                  );
                })()}
              </div>

              <Button
                className="w-full bg-primary text-primary-foreground hover:bg-primary/90"
                size="lg"
                onClick={() => handleSubscribe(planId)}
              >
                {plan.buttonText}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Unique Request Section */}
      {/* <div className="max-w-3xl mx-auto text-center space-y-4">
        <h3 className="text-2xl font-bold text-foreground">Unique Request</h3>
        <div className="space-y-2">
          <p className="text-foreground">
            Are you looking for something custom?
          </p>
          <p className="text-foreground-muted">
            Don't hesitate to contact us, and we'll help brainstorming your
            product to success.
          </p>
        </div>
        <Button
          className="bg-primary text-primary-foreground hover:bg-primary/90 px-8"
          size="lg"
          onClick={() => console.log("Custom request clicked")}
        >
          Let's Talk
        </Button>
      </div> */}
    </div>
  );
};
