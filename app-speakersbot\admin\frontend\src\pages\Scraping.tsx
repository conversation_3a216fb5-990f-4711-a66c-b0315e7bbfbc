// React & State
import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";

// Validation
import * as z from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

import DatePicker from "react-multi-date-picker";

// Icons
import {
  Plus,
  Edit,
  Trash2,
  PlayCircle,
  PauseCircle,
  Download,
  Filter,
  Info,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Search,
  Check,
  ChevronsUpDown,
} from "lucide-react";

// UI Components (shadcn)
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ThemeAlert } from "@/components/ui/theme-alert";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  Dialog<PERSON><PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// API Hooks
import {
  useUpdateScrapingStatusMutation,
  useGetScrapingStatusQuery,
  useExportScrapingLogsCSVMutation,
  useGetScrapingLogsQuery,
  useGetScrapingSubCategoriesQuery,
  useCreateScrapingTopicMutation,
  useUpdateScrapingTopicMutation,
  useDeleteScrapingTopicMutation,
  useFetchTopicsForLogsFilterQuery,
  useToggleScrapingSubCategoryActiveMutation,
  useFetchScrapingCategoriesQuery,
  useCreateSubcategoryMutation,
} from "@/apis";

// Context & Permissions
import { useAuth } from "../state/AuthContext";
import { getPermissions } from "../utils/permissions";

// Components
import { InvalidTokenHandler } from "../components/common/InvalidTokenHandler";
// Types
import type { UserRole } from "../types";

// Helpers
import { getStatusClasses, formatDateYMD } from "../utils/helper";
import { toast } from "@/hooks/use-toast";
import { cn } from "@/lib/utils";

// Page size options for pagination
const PAGE_SIZE_OPTIONS = [
  { value: 10, label: "10 per page" },
  { value: 25, label: "25 per page" },
  { value: 50, label: "50 per page" },
  { value: 100, label: "100 per page" },
];

// Form schema for subcategory validation
const subcategoryFormSchema = z.object({
  name: z.string().min(1, "Please enter subcategory name"),
  category_id: z.string().min(1, "Please select a category"),
});

type SubcategoryFormValues = z.infer<typeof subcategoryFormSchema>;

/**
 * Scraping Management Component
 *
 * This component provides a comprehensive interface for managing web scraping operations,
 * including viewing scraping logs, managing scraping topics, and controlling scraping status.
 *
 * Features:
 * - Real-time scraping status monitoring
 * - Scraping logs with filtering and pagination
 * - Topic management (CRUD operations)
 * - Export functionality for logs
 * - Permission-based access control
 */
const Scraping: React.FC = () => {
  const { user } = useAuth();
  const permissions = getPermissions(user?.role as unknown as UserRole);

  // UI State
  const [activeTab, setActiveTab] = useState("logs");
  const [isTopicModalVisible, setIsTopicModalVisible] = useState(false);

  // Category selection state
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [isCategorySelectOpen, setIsCategorySelectOpen] = useState(false);

  // Combobox state for status filter
  const [isStatusComboboxOpen, setIsStatusComboboxOpen] = useState(false);
  const [statusComboboxValue, setStatusComboboxValue] = useState("");

  // Combobox state for topic filter
  const [isTopicComboboxOpen, setIsTopicComboboxOpen] = useState(false);
  const [topicComboboxValue, setTopicComboboxValue] = useState("");

  // Export dialog state
  const [isExportDialogOpen, setIsExportDialogOpen] = useState(false);

  // Status options for Command component
  const statusOptions = [
    { value: "success", label: "Success" },
    { value: "error", label: "Error" },
    { value: "running", label: "Running" },
    { value: "paused", label: "Paused" },
  ];

  // Unified Logs Filter State
  const [logsFilterState, setLogsFilterState] = useState({
    page: 1,
    limit: 10,
    sortBy: "started_at",
    sortOrder: "DESC" as "ASC" | "DESC",
    startDate: null as any,
    endDate: null as any,
    search: "",
    status: [] as string[],
    topic: null as string | null,
  });

  // Unified Topics Filter State
  const [topicsFilterState, setTopicsFilterState] = useState({
    page: 1,
    limit: 10,
    search: "",
    is_active: "1",
  });

  // Subcategories filter state for active/inactive tabs
  const [subCategoriesFilter, setSubCategoriesFilter] = useState<
    "all" | "active" | "inactive"
  >("all");

  // Data State - using RTK Query cache
  const [logsTotal, setLogsTotal] = useState(0);
  const [topicsTotal, setTopicsTotal] = useState(0);
  const [isResettingFilters, setIsResettingFilters] = useState(false);

  // Debounced Search Values
  const [debouncedSearchText, setDebouncedSearchText] = useState("");
  const [debouncedTopicSearchText, setDebouncedTopicSearchText] = useState("");

  // Form Instance
  const form = useForm<SubcategoryFormValues>({
    resolver: zodResolver(subcategoryFormSchema),
    defaultValues: {
      name: "",
      category_id: "",
    },
  });

  // Reset filters when component mounts
  useEffect(() => {
    handleResetFilters();
  }, []);

  // API HOOKS (RTK Query)

  // Scraping Status Management
  const [updateScrapingStatus, { isLoading: isUpdatingScrapingStatus }] =
    useUpdateScrapingStatusMutation();
  const {
    data: scrapingStatusData,
    isLoading: isLoadingStatus,
    isFetching: isFetchingStatus,
    error: scrapingStatusError,
    refetch: refetchStatus,
  } = useGetScrapingStatusQuery(undefined);

  // Scraping Logs Management - only fetch when logs tab is active
  const {
    data: scrapingLogsData,
    isLoading: isLoadingLogs,
    isFetching: isFetchingLogs,
    error: logsError,
  } = useGetScrapingLogsQuery(
    {
      page: logsFilterState.page,
      limit: logsFilterState.limit,
      search: debouncedSearchText || undefined,
      status:
        logsFilterState.status.length > 0
          ? logsFilterState.status.join(",")
          : undefined,
      topic: logsFilterState.topic || undefined,
      startDate: logsFilterState.startDate?.format("YYYY-MM-DD"),
      endDate: logsFilterState.endDate?.format("YYYY-MM-DD"),
      sortBy: logsFilterState.sortBy,
      sortOrder: logsFilterState.sortOrder,
    },
    {
      skip: activeTab !== "logs", // Only fetch when logs tab is active
    }
  );

  // Scraping Topics Management - only fetch when topics tab is active
  const {
    data: scrapingSubCategoriesData,
    isLoading: isLoadingSubCategories,
    isFetching: isFetchingSubCategories,
    error: subCategoriesError,
  } = useGetScrapingSubCategoriesQuery(
    {
      page: topicsFilterState.page,
      limit: topicsFilterState.limit,
      search: debouncedTopicSearchText || undefined,
      is_active:
        subCategoriesFilter === "all"
          ? undefined
          : subCategoriesFilter === "active"
          ? "1"
          : "0",
    },
    { skip: activeTab !== "subcategories" }
  );

  // Export Mutation
  const [exportScrapingLogsCSV] = useExportScrapingLogsCSVMutation();

  // Fetch topics for logs filter - only fetch when logs tab is active
  const { data: topicsForLogsFilterData } = useFetchTopicsForLogsFilterQuery(
    undefined,
    {
      skip: activeTab !== "logs", // Only fetch when logs tab is active
    }
  );

  // Fetch scraping categories - only fetch when subcategories tab is active
  const { data: scrapingCategoriesData } = useFetchScrapingCategoriesQuery(
    undefined,
    {
      skip: activeTab !== "subcategories", // Only fetch when subcategories tab is active
    }
  );

  // Toggle scraping sub category active
  const [toggleScrapingSubCategoryActive] =
    useToggleScrapingSubCategoryActiveMutation();

  // Create subcategory mutation
  const [createSubcategory, { isLoading: isCreatingSubcategory }] =
    useCreateSubcategoryMutation();

  // Check if date range is valid (either no dates or both dates present)
  const isValidDateRange =
    !logsFilterState.startDate ||
    (logsFilterState.startDate && logsFilterState.endDate);

  // COMPUTED VALUES FROM QUERY DATA
  const isScrapingRunning = scrapingStatusData?.data?.isRunning ?? null;
  const scrapingLogs = scrapingLogsData?.data ?? [];
  const scrapingTopics = scrapingSubCategoriesData?.data ?? [];

  // Update totals from query data
  React.useEffect(() => {
    if (scrapingLogsData?.pagination?.total !== undefined) {
      setLogsTotal(scrapingLogsData.pagination.total);
    }
  }, [scrapingLogsData?.pagination?.total]);

  React.useEffect(() => {
    if (scrapingSubCategoriesData?.pagination?.total !== undefined) {
      setTopicsTotal(scrapingSubCategoriesData.pagination.total);
    }
  }, [scrapingSubCategoriesData?.pagination?.total]);

  // Use query data directly (already filtered and paginated by API)
  const filteredLogs = scrapingLogs;
  const filteredTopics = scrapingTopics;

  const logsTotalPages = Math.max(
    1,
    Math.ceil(logsTotal / logsFilterState.limit)
  );
  const topicsTotalPages = Math.max(
    1,
    Math.ceil(topicsTotal / topicsFilterState.limit)
  );

  // Current page from API data or state
  const logsCurrentPage = logsFilterState.page;
  const topicsCurrentPage = topicsFilterState.page;

  // For logs and topics, we use the API data directly (already paginated)
  const paginatedLogs = filteredLogs;
  const paginatedSubCategories = filteredTopics;

  // Page items logic for logs - matching Users.tsx pattern
  const logsPageItems = React.useMemo<(number | "ellipsis")[]>(() => {
    const totalP = logsTotalPages;
    const currentP = logsCurrentPage;
    if (totalP <= 7) {
      return Array.from({ length: totalP }, (_, i) => i + 1);
    }
    const pages: number[] = [];
    pages.push(1);
    const start = Math.max(2, currentP - 1);
    const end = Math.min(totalP - 1, currentP + 1);
    for (let i = start; i <= end; i++) pages.push(i);
    pages.push(totalP);

    // Build with ellipses where gaps exist
    const output: (number | "ellipsis")[] = [];
    let last = 0;
    for (const p of pages) {
      if (last && p - last > 1) output.push("ellipsis");
      output.push(p);
      last = p;
    }
    return output;
  }, [logsCurrentPage, logsTotalPages]);

  // Page items logic for topics - matching Users.tsx pattern
  const topicsPageItems = React.useMemo<(number | "ellipsis")[]>(() => {
    const totalP = topicsTotalPages;
    const currentP = topicsCurrentPage;
    if (totalP <= 7) {
      return Array.from({ length: totalP }, (_, i) => i + 1);
    }
    const pages: number[] = [];
    pages.push(1);
    const start = Math.max(2, currentP - 1);
    const end = Math.min(totalP - 1, currentP + 1);
    for (let i = start; i <= end; i++) pages.push(i);
    pages.push(totalP);

    // Build with ellipses where gaps exist
    const output: (number | "ellipsis")[] = [];
    let last = 0;
    for (const p of pages) {
      if (last && p - last > 1) output.push("ellipsis");
      output.push(p);
      last = p;
    }
    return output;
  }, [topicsCurrentPage, topicsTotalPages]);

  // Toggles the scraping status (start/pause)
  const handleToggleScraping = async () => {
    if (!permissions.canEdit) {
      toast({
        title: "You do not have permission to modify scraping settings",
        variant: "destructive",
      });
      return;
    }

    const newRunningState = !isScrapingRunning;

    try {
      const body = { isRunning: newRunningState };
      await updateScrapingStatus(body).unwrap();

      // Manually refetch the status to get the updated value
      // This ensures the UI shows the correct status immediately
      refetchStatus();

      toast({
        title: `Scraping ${newRunningState ? "started" : "paused"}`,
      });
    } catch (err) {
      console.error("Failed to control scraping:", err);
      toast({
        title: "Failed to control scraping. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle export confirmation
  const handleExportConfirm = async () => {
    setIsExportDialogOpen(false);
    await handleExportLogs();
  };

  // Exports scraping logs to CSV format
  const handleExportLogs = async () => {
    try {
      const exportParams = {
        page: logsFilterState.page,
        limit: logsFilterState.limit,
        search: debouncedSearchText || undefined,
        status:
          logsFilterState.status.length > 0
            ? logsFilterState.status.join(",")
            : undefined,
        topic: logsFilterState.topic || undefined,
        startDate: logsFilterState.startDate?.format("YYYY-MM-DD"),
        endDate: logsFilterState.endDate?.format("YYYY-MM-DD"),
      };

      const response = await exportScrapingLogsCSV(exportParams).unwrap();

      // Create a blob from the response data
      const blob = new Blob([response], { type: "text/csv;charset=utf-8;" });

      // Create download link
      const link = document.createElement("a");
      const url = URL.createObjectURL(blob);
      link.setAttribute("href", url);
      link.setAttribute("download", `scraping-logs.csv`);
      link.style.visibility = "hidden";

      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL object
      URL.revokeObjectURL(url);

      toast({
        title: "Scraping logs exported successfully",
      });
    } catch (error) {
      toast({
        title: "Failed to export scraping logs. Please try again.",
        variant: "destructive",
      });
      console.error("Failed to export scraping logs:", error);
    }
  };

  const handleResetFilters = () => {
    setIsResettingFilters(true);
    setLogsFilterState({
      page: 1,
      limit: 10,
      sortBy: "started_at",
      sortOrder: "DESC",
      startDate: null,
      endDate: null,
      search: "",
      status: [],
      topic: null,
    });
    // Reset Combobox component state
    setStatusComboboxValue("");
    setIsStatusComboboxOpen(false);
    setTopicComboboxValue("");
    setIsTopicComboboxOpen(false);
    // Reset the loading state after a short delay to ensure skeleton shows
    setTimeout(() => {
      setIsResettingFilters(false);
    }, 100);
  };

  // Handle sorting for logs table
  const handleSort = (column: string) => {
    if (logsFilterState.sortBy === column) {
      // Toggle sort order if same column
      setLogsFilterState((prev) => ({
        ...prev,
        page: 1, // Reset to first page
        sortOrder: prev.sortOrder === "ASC" ? "DESC" : "ASC",
      }));
    } else {
      // Set new column and default to DESC
      setLogsFilterState((prev) => ({
        ...prev,
        page: 1, // Reset to first page
        sortBy: column,
        sortOrder: "DESC",
      }));
    }
  };

  // Helper function to render sort icon
  const renderSortIcon = (column: string) => {
    if (logsFilterState.sortBy !== column) {
      return <ArrowUpDown className="h-4 w-4 text-muted-foreground" />;
    }
    return logsFilterState.sortOrder === "ASC" ? (
      <ArrowUp className="h-4 w-4 text-primary" />
    ) : (
      <ArrowDown className="h-4 w-4 text-primary" />
    );
  };

  // Skeleton components for loading states
  const LogsTableSkeleton = () => (
    <>
      {Array.from({ length: logsFilterState.limit }).map((_, index) => (
        <TableRow key={index}>
          <TableCell>
            <Skeleton className="h-4 w-32" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-32" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-6 w-16 rounded-md" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-24" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-12" />
          </TableCell>
        </TableRow>
      ))}
    </>
  );

  const TopicsTableSkeleton = () => (
    <>
      {Array.from({ length: topicsFilterState.limit }).map((_, index) => (
        <TableRow key={index}>
          <TableCell>
            <Skeleton className="h-4 w-40" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-32" />
          </TableCell>
          <TableCell>
            <div className="flex items-center gap-2">
              <Skeleton className="h-6 w-14 rounded-xl" />
            </div>
          </TableCell>
        </TableRow>
      ))}
    </>
  );

  const handleLogsPageSizeChange = (newPageSize: number) => {
    setLogsFilterState((prev) => ({
      ...prev,
      limit: newPageSize,
      page: 1, // Reset to first page when changing page size
    }));
  };

  const handleTopicsPageSizeChange = (newPageSize: number) => {
    setTopicsFilterState((prev) => ({
      ...prev,
      limit: newPageSize,
      page: 1, // Reset to first page when changing page size
    }));
  };

  const handleLogsPageChange = (newPage: number) => {
    setLogsFilterState((prev) => ({
      ...prev,
      page: newPage,
    }));
  };

  const handleTopicsPageChange = (newPage: number) => {
    setTopicsFilterState((prev) => ({
      ...prev,
      page: newPage,
    }));
  };

  // Handle toggle for is_active status
  const handleToggleActive = async (topic: any) => {
    try {
      await toggleScrapingSubCategoryActive(topic.id).unwrap();
      toast({
        title: "Subcategory status updated successfully",
      });
    } catch (err) {
      console.error("Failed to update subcategory status:", err);
      toast({
        title: "Failed to update subcategory status. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle status selection from Combobox component
  const handleStatusSelect = (currentValue: string) => {
    const newValue = currentValue === statusComboboxValue ? "" : currentValue;
    setStatusComboboxValue(newValue);
    setLogsFilterState((prev) => ({
      ...prev,
      page: 1, // Reset to first page
      status: newValue ? [newValue] : [],
    }));
    setIsStatusComboboxOpen(false);
  };

  // Handle topic selection from Combobox component
  const handleTopicSelect = (currentValue: string) => {
    const newValue = currentValue === topicComboboxValue ? "" : currentValue;
    setTopicComboboxValue(newValue);
    setLogsFilterState((prev) => ({
      ...prev,
      page: 1, // Reset to first page
      topic: newValue || null,
    }));
    setIsTopicComboboxOpen(false);
  };

  // Opens the modal to add a new scraping topic
  const handleAddTopic = () => {
    if (!permissions.canCreate) {
      toast({
        title: "You do not have permission to add topics",
        variant: "destructive",
      });
      return;
    }
    form.reset();
    setIsTopicModalVisible(true);
  };

  const handleSubmitSubcategory = async (values: SubcategoryFormValues) => {
    try {
      await createSubcategory({
        name: values.name,
        category_id: parseInt(values.category_id),
      }).unwrap();

      // Show success toast
      toast({
        title: "Sub Category added successfully",
      });

      // Close popup and reset form on success
      setIsTopicModalVisible(false);
      form.reset();
      setSelectedCategory("");
      setIsCategorySelectOpen(false);
    } catch (err) {
      console.error("Failed to save sub category:", err);

      // Show error toast
      toast({
        title: "Failed to create sub category",
        description: err?.data?.error?.message || "Please try again later.",
        variant: "destructive",
      });
    }
  };

  const handleCategorySelect = (categoryId: string) => {
    setSelectedCategory(categoryId);
    form.setValue("category_id", categoryId);
    setIsCategorySelectOpen(false);
  };

  /**
   * Debounce search input for logs
   * Delays search execution by 500ms to avoid excessive API calls
   */
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchText(logsFilterState.search);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [logsFilterState.search]);

  /**
   * Debounce search input for topics
   * Delays search execution by 500ms to avoid excessive API calls
   */
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedTopicSearchText(topicsFilterState.search);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [topicsFilterState.search]);

  /**
   * Reset logs page when filters change (RTK Query handles refetching automatically)
   */
  useEffect(() => {
    if (isValidDateRange) {
      setLogsFilterState((prev) => ({ ...prev, page: 1 }));
    }
  }, [
    debouncedSearchText,
    logsFilterState.status,
    logsFilterState.topic,
    logsFilterState.startDate,
    logsFilterState.endDate,
    logsFilterState.sortBy,
    logsFilterState.sortOrder,
  ]);

  /**
   * Reset subcategories page when filter changes
   */
  useEffect(() => {
    setTopicsFilterState((prev) => ({ ...prev, page: 1 }));
  }, [subCategoriesFilter]);

  return (
    <TooltipProvider>
      <div>
        <InvalidTokenHandler error={scrapingStatusError} />

        <div className="flex justify-between items-center mb-[24px]">
          <div>
            <h1 className="m-0 text-2xl font-bold">Scraping Management</h1>

            {isLoadingStatus || isFetchingStatus ? (
              <Skeleton className="h-[24px] w-28 rounded-lg" />
            ) : (
              <div className="flex items-center gap-2 pt-1.5">
                <span
                  className={`w-2 h-2 rounded-full ${
                    isScrapingRunning
                      ? "bg-green-500 animate-pulse"
                      : "bg-orange-500"
                  }`}
                ></span>
                <p>{isScrapingRunning ? "Running" : "Paused"}</p>
              </div>
            )}
          </div>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="default"
                onClick={handleToggleScraping}
                disabled={!permissions.canEdit || isUpdatingScrapingStatus}
                className="rounded-lg"
              >
                {isScrapingRunning === null ? (
                  "Loading..."
                ) : isUpdatingScrapingStatus ? (
                  isScrapingRunning ? (
                    <>
                      <PauseCircle className="h-4 w-4" />
                      Pausing...
                    </>
                  ) : (
                    <>
                      <PlayCircle className="h-4 w-4" />
                      Starting...
                    </>
                  )
                ) : isScrapingRunning ? (
                  <>
                    <PauseCircle className="h-4 w-4" />
                    Pause Scraping
                  </>
                ) : (
                  <>
                    <PlayCircle className="h-4 w-4" />
                    Start Scraping
                  </>
                )}
              </Button>
            </TooltipTrigger>
            {!permissions.canEdit && (
              <TooltipContent>
                <p>You don't have permission to control scraping</p>
              </TooltipContent>
            )}
          </Tooltip>
        </div>

        {isScrapingRunning && (
          <ThemeAlert
            variant="info"
            title="Scraping is currently active"
            description="The system is actively collecting speaker opportunities from configured sources."
            className="mb-6"
          />
        )}

        <div
          style={{
            backgroundColor: "hsl(var(--card))",
            borderRadius: "12px 12px 0 0",
            padding: "0",
            display: "flex",
            gap: "0",
            border: "1px solid hsl(var(--border))",
            borderBottom: "none",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
          }}
        >
          <div
            onClick={() => setActiveTab("logs")}
            style={{
              padding: "18px 28px",
              cursor: "pointer",
              color:
                activeTab === "logs"
                  ? "hsl(var(--primary))"
                  : "hsl(var(--muted-foreground))",
              backgroundColor:
                activeTab === "logs"
                  ? "hsl(var(--tertiary))"
                  : "hsl(var(--muted))",
              borderBottom:
                activeTab === "logs"
                  ? "3px solid hsl(var(--primary))"
                  : "3px solid transparent",
              fontWeight: activeTab === "logs" ? "600" : "400",
              fontSize: "15px",
              transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
              borderRadius: "12px 0 0 0",
              position: "relative",
              flex: 1,
              textAlign: "center",
            }}
          >
            Scraping Logs
          </div>
          <div
            onClick={() => setActiveTab("subcategories")}
            style={{
              padding: "18px 28px",
              cursor: "pointer",
              color:
                activeTab === "subcategories"
                  ? "hsl(var(--primary))"
                  : "hsl(var(--muted-foreground))",
              backgroundColor:
                activeTab === "subcategories"
                  ? "hsl(var(--tertiary))"
                  : "hsl(var(--muted))",
              borderBottom:
                activeTab === "subcategories"
                  ? "3px solid hsl(var(--primary))"
                  : "3px solid transparent",
              fontWeight: activeTab === "subcategories" ? "600" : "400",
              fontSize: "15px",
              transition: "all 0.3s cubic-bezier(0.4, 0, 0.2, 1)",
              borderRadius: "0 12px 0 0",
              position: "relative",
              flex: 1,
              textAlign: "center",
            }}
          >
            Subcategories
          </div>
        </div>
        {/* Tab Content */}
        <div
          style={{
            backgroundColor: "hsl(var(--tertiary))",
            borderRadius: "0 0 12px 12px",
            border: "1px solid hsl(var(--border))",
            borderTop: "none",
            boxShadow: "0 4px 6px rgba(0,0,0,0.1)",
          }}
          className="p-6"
        >
          {activeTab === "logs" && (
            <div
              style={{
                backgroundColor: "hsl(var(--card))",
                borderColor: "hsl(var(--border))",
                borderRadius: "12px",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              }}
              className="border"
            >
              <div className="flex flex-wrap justify-end gap-3 border-b p-3 px-6 mb-3">
                {/* <div className="flex items-center gap-2">
                  <Filter className="h-4 w-4" />
                  Scraping Logs
                </div> */}
                <div className="flex flex-wrap gap-2">
                  <Dialog
                    open={isExportDialogOpen}
                    onOpenChange={setIsExportDialogOpen}
                  >
                    <DialogTrigger asChild>
                      <Button
                        variant="outline"
                        disabled={!permissions.canImportExport}
                        className="flex items-center gap-2 border dark:border-[#9CD2E766] rounded-lg px-2 py-1 dark:text-white"
                      >
                        <Download className="h-4 w-4" />
                        Export CSV
                      </Button>
                    </DialogTrigger>
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Export Scraping Logs</DialogTitle>
                        <DialogDescription>
                          Are you sure you want to export the scraping logs?
                          This will download a CSV file with the current
                          filtered data.
                        </DialogDescription>
                      </DialogHeader>
                      <DialogFooter>
                        <Button
                          variant="outline"
                          onClick={() => setIsExportDialogOpen(false)}
                        >
                          Cancel
                        </Button>
                        <Button onClick={handleExportConfirm}>
                          Export CSV
                        </Button>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>

                  <Button
                    variant="outline"
                    onClick={handleResetFilters}
                    className="flex items-center gap-2 border dark:border-[#9CD2E766] rounded-lg px-2 py-1 dark:text-white"
                  >
                    Reset Filters
                  </Button>
                </div>
              </div>

              <div className="m-6">
                <div className="bg-card border p-3 rounded-xl shadow-sm mb-6">
                  <div>
                    <h1 className="text-lg font-semibold text-foreground">
                      Filters
                    </h1>
                  </div>
                  <div className="space-y-4 mt-5">
                    {/* Search Input */}
                    <div className="flex flex-col md:flex-row gap-2">
                      <div className="flex-1">
                        {isLoadingLogs || isResettingFilters ? (
                          <Skeleton className="h-10 w-full" />
                        ) : (
                          <div className="relative">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                            <Input
                              type="search"
                              placeholder="Search by topic, status, or error..."
                              value={logsFilterState.search}
                              onChange={(e) =>
                                setLogsFilterState((prev) => ({
                                  ...prev,
                                  page: 1, // Reset to first page
                                  search: e.target.value,
                                }))
                              }
                              className="pl-10"
                            />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Main Filters - Second Row */}
                    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4">
                      {isLoadingLogs || isResettingFilters ? (
                        <Skeleton className="h-10 w-full" />
                      ) : (
                        <Popover
                          open={isStatusComboboxOpen}
                          onOpenChange={setIsStatusComboboxOpen}
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              role="combobox"
                              aria-expanded={isStatusComboboxOpen}
                              className="w-full justify-between"
                              disabled={isLoadingLogs}
                            >
                              {statusComboboxValue
                                ? statusOptions.find(
                                    (option) =>
                                      option.value === statusComboboxValue
                                  )?.label
                                : "Filter by Status"}
                              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0">
                            <Command>
                              <CommandInput
                                placeholder="Search status..."
                                className="h-9"
                              />
                              <CommandList>
                                <CommandEmpty>No status found.</CommandEmpty>
                                <CommandGroup>
                                  <CommandItem
                                    value=""
                                    onSelect={() => handleStatusSelect("")}
                                  >
                                    All Status
                                    <Check
                                      className={cn(
                                        "ml-auto h-4 w-4",
                                        statusComboboxValue === ""
                                          ? "opacity-100"
                                          : "opacity-0"
                                      )}
                                    />
                                  </CommandItem>
                                  {statusOptions.map((option) => (
                                    <CommandItem
                                      key={option.value}
                                      value={option.value}
                                      onSelect={() =>
                                        handleStatusSelect(option.value)
                                      }
                                    >
                                      {option.label}
                                      <Check
                                        className={cn(
                                          "ml-auto h-4 w-4",
                                          statusComboboxValue === option.value
                                            ? "opacity-100"
                                            : "opacity-0"
                                        )}
                                      />
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      )}

                      {isLoadingLogs || isResettingFilters ? (
                        <Skeleton className="h-10 w-full" />
                      ) : (
                        <Popover
                          open={isTopicComboboxOpen}
                          onOpenChange={setIsTopicComboboxOpen}
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              role="combobox"
                              aria-expanded={isTopicComboboxOpen}
                              className="w-full justify-between"
                              disabled={isLoadingLogs}
                            >
                              {topicComboboxValue
                                ? topicComboboxValue
                                : "Filter by Topic"}
                              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-full p-0">
                            <Command>
                              <CommandInput
                                placeholder="Search topic..."
                                className="h-9"
                              />
                              <CommandList>
                                <CommandEmpty>No topic found.</CommandEmpty>
                                <CommandGroup>
                                  <CommandItem
                                    value=""
                                    onSelect={() => handleTopicSelect("")}
                                  >
                                    All Topics
                                    <Check
                                      className={cn(
                                        "ml-auto h-4 w-4",
                                        topicComboboxValue === ""
                                          ? "opacity-100"
                                          : "opacity-0"
                                      )}
                                    />
                                  </CommandItem>
                                  {topicsForLogsFilterData?.data?.map(
                                    (topic: string) => (
                                      <CommandItem
                                        key={topic}
                                        value={topic}
                                        onSelect={() =>
                                          handleTopicSelect(topic)
                                        }
                                      >
                                        {topic}
                                        <Check
                                          className={cn(
                                            "ml-auto h-4 w-4",
                                            topicComboboxValue === topic
                                              ? "opacity-100"
                                              : "opacity-0"
                                          )}
                                        />
                                      </CommandItem>
                                    )
                                  )}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      )}

                      {isLoadingLogs || isResettingFilters ? (
                        <Skeleton className="h-10 w-full" />
                      ) : (
                        <DatePicker
                          value={[
                            logsFilterState.startDate,
                            logsFilterState.endDate,
                          ]}
                          onChange={(dates) =>
                            setLogsFilterState((prev) => ({
                              ...prev,
                              page: 1, // Reset to first page
                              startDate: dates?.[0] || null,
                              endDate: dates?.[1] || null,
                            }))
                          }
                          range
                          numberOfMonths={2}
                          arrow={false}
                          className="custom-calendar"
                          placeholder="Select Date Range"
                          inputClass="h-10 rounded-md border border-input bg-background w-[220px] px-3"
                        />
                      )}
                    </div>
                  </div>
                </div>

                <div className="mt-6">
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-muted-foreground text-sm">
                      {isLoadingLogs ? (
                        <Skeleton className="h-[20px] w-[200px] rounded-lg" />
                      ) : (
                        `Showing ${
                          logsTotal > 0
                            ? (logsCurrentPage - 1) * logsFilterState.limit + 1
                            : 0
                        }-${Math.min(
                          logsCurrentPage * logsFilterState.limit,
                          logsTotal
                        )} of ${logsTotal || 0} sub categories`
                      )}
                    </span>
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground text-sm">
                        Rows per page
                      </span>
                      <Select
                        value={String(logsFilterState.limit)}
                        onValueChange={(v) => {
                          handleLogsPageSizeChange(Number(v));
                        }}
                      >
                        <SelectTrigger className="w-[80px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="10">10</SelectItem>
                          <SelectItem value="25">25</SelectItem>
                          <SelectItem value="50">50</SelectItem>
                          <SelectItem value="100">100</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="border rounded-xl">
                    <Table>
                      <TableHeader>
                        <TableRow>
                          <TableHead
                            className="cursor-pointer hover:bg-muted/50 select-none"
                            onClick={() => handleSort("started_at")}
                          >
                            <div className="flex items-center gap-2">
                              Started At
                              {renderSortIcon("started_at")}
                            </div>
                          </TableHead>
                          <TableHead className="cursor-pointer hover:bg-muted/50 select-none">
                            <div className="flex items-center gap-2">
                              Ended At
                            </div>
                          </TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Topic</TableHead>
                          <TableHead
                            className="cursor-pointer hover:bg-muted/50 select-none"
                            onClick={() => handleSort("items_collected")}
                          >
                            <div className="flex items-center gap-2">
                              Items Collected
                              {renderSortIcon("items_collected")}
                            </div>
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {!isValidDateRange && logsFilterState.startDate ? (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-8">
                              <div className="text-yellow-600 dark:text-yellow-400">
                                Please select both start and end dates to view
                                logs, or clear the date range to show all logs.
                              </div>
                            </TableCell>
                          </TableRow>
                        ) : isLoadingLogs ||
                          isFetchingLogs ||
                          isResettingFilters ? (
                          <LogsTableSkeleton />
                        ) : logsError ? (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-8">
                              <div className="text-red-500">
                                Failed to load scraping logs. Please try again.
                              </div>
                            </TableCell>
                          </TableRow>
                        ) : paginatedLogs.length === 0 ? (
                          <TableRow>
                            <TableCell colSpan={5} className="text-center py-8">
                              <div>
                                {logsTotal > 0
                                  ? "No logs found for current page"
                                  : "No scraping logs found"}
                              </div>
                            </TableCell>
                          </TableRow>
                        ) : (
                          paginatedLogs?.map((log, index: number) => {
                            return (
                              <TableRow key={index}>
                                <TableCell>
                                  {formatDateYMD(log.started_at) || "--"}
                                </TableCell>
                                <TableCell>
                                  {formatDateYMD(log.ended_at) || "--"}
                                </TableCell>
                                <TableCell>
                                  <div className="flex items-center gap-2">
                                    <div
                                      className={`px-3 py-1 rounded-md border font-medium uppercase ${getStatusClasses(
                                        log.status
                                      )}`}
                                    >
                                      {log.status}
                                    </div>

                                    {log.status === "error" && log.reason && (
                                      <Tooltip>
                                        <TooltipTrigger asChild>
                                          <Info
                                            className="h-4 w-4"
                                            style={{ color: "#f5222d" }}
                                          />
                                        </TooltipTrigger>
                                        <TooltipContent>
                                          <p>{log.reason || ""}</p>
                                        </TooltipContent>
                                      </Tooltip>
                                    )}
                                  </div>
                                </TableCell>
                                <TableCell title={log.topic}>
                                  {log.topic
                                    ? log.topic.length > 25
                                      ? log.topic.slice(0, 25) + "..."
                                      : log.topic
                                    : "-"}
                                </TableCell>
                                <TableCell>{log.item_count || 0}</TableCell>
                              </TableRow>
                            );
                          })
                        )}
                      </TableBody>
                    </Table>
                  </div>

                  {/* Pagination for Logs - centered at bottom */}
                  {logsTotalPages > 0 &&
                    logsTotal > 0 &&
                    !isLoadingLogs &&
                    !isFetchingLogs &&
                    !isResettingFilters && (
                      <div className="flex items-center justify-center p-4">
                        <Pagination style={{ margin: 0, width: "auto" }}>
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  if (
                                    logsCurrentPage > 1 &&
                                    !isLoadingLogs &&
                                    !isFetchingLogs
                                  )
                                    handleLogsPageChange(logsCurrentPage - 1);
                                }}
                                className={
                                  logsCurrentPage === 1 ||
                                  isLoadingLogs ||
                                  isFetchingLogs
                                    ? "pointer-events-none opacity-50"
                                    : ""
                                }
                              />
                            </PaginationItem>
                            {logsPageItems.map((item, idx) => (
                              <PaginationItem key={`${item}-${idx}`}>
                                {item === "ellipsis" ? (
                                  <PaginationEllipsis />
                                ) : (
                                  <PaginationLink
                                    href="#"
                                    isActive={item === logsCurrentPage}
                                    onClick={(e) => {
                                      e.preventDefault();
                                      if (!isLoadingLogs && !isFetchingLogs)
                                        handleLogsPageChange(item as number);
                                    }}
                                    className={
                                      isLoadingLogs || isFetchingLogs
                                        ? "pointer-events-none opacity-50"
                                        : ""
                                    }
                                  >
                                    {item}
                                  </PaginationLink>
                                )}
                              </PaginationItem>
                            ))}
                            <PaginationItem>
                              <PaginationNext
                                href="#"
                                onClick={(e) => {
                                  e.preventDefault();
                                  if (
                                    logsCurrentPage < logsTotalPages &&
                                    !isLoadingLogs &&
                                    !isFetchingLogs
                                  )
                                    handleLogsPageChange(logsCurrentPage + 1);
                                }}
                                className={
                                  logsCurrentPage === logsTotalPages ||
                                  isLoadingLogs ||
                                  isFetchingLogs
                                    ? "pointer-events-none opacity-50"
                                    : ""
                                }
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    )}
                </div>
              </div>
            </div>
          )}

          {activeTab === "subcategories" && (
            <div
              style={{
                backgroundColor: "hsl(var(--card))",
                borderColor: "hsl(var(--border))",
                borderRadius: "12px",
                boxShadow: "0 2px 4px rgba(0,0,0,0.1)",
              }}
              className="border"
            >
              <div className="flex flex-wrap justify-end gap-3 border-b p-3 px-6 mb-3">
                {/* <div className="flex items-center gap-2">Manage Topics</div> */}
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="default"
                      onClick={handleAddTopic}
                      disabled={!permissions.canCreate}
                    >
                      <Plus className="h-4 w-4" />
                      Add Sub Category
                    </Button>
                  </TooltipTrigger>
                </Tooltip>
              </div>
              <div className="m-6">
                {/* Search and Filter in one line */}
                <div className="flex gap-4 mb-6">
                  <div className="relative flex-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search by name..."
                      value={topicsFilterState.search}
                      onChange={(e) =>
                        setTopicsFilterState((prev) => ({
                          ...prev,
                          page: 1, // Reset to first page
                          search: e.target.value,
                        }))
                      }
                      disabled={
                        isLoadingSubCategories || isFetchingSubCategories
                      }
                      className="pl-10"
                    />
                  </div>
                  <Select
                    value={subCategoriesFilter}
                    onValueChange={(value: "all" | "active" | "inactive") =>
                      setSubCategoriesFilter(value)
                    }
                    disabled={isLoadingSubCategories || isFetchingSubCategories}
                  >
                    <SelectTrigger className="w-[200px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All</SelectItem>
                      <SelectItem value="active">Active</SelectItem>
                      <SelectItem value="inactive">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex items-center justify-between mb-4">
                  <span className="text-muted-foreground text-sm">
                    {isLoadingSubCategories ? (
                      <Skeleton className="h-[20px] w-[200px] rounded-lg" />
                    ) : (
                      `Showing ${
                        topicsTotal > 0
                          ? (topicsCurrentPage - 1) * topicsFilterState.limit +
                            1
                          : 0
                      }-${Math.min(
                        topicsCurrentPage * topicsFilterState.limit,
                        topicsTotal
                      )} of ${topicsTotal || 0} sub categories`
                    )}
                  </span>
                  <div className="flex items-center gap-2">
                    <span className="text-muted-foreground text-sm">
                      Rows per page
                    </span>
                    <Select
                      value={String(topicsFilterState.limit)}
                      onValueChange={(v) => {
                        handleTopicsPageSizeChange(Number(v));
                      }}
                    >
                      <SelectTrigger className="w-[80px]">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="25">25</SelectItem>
                        <SelectItem value="50">50</SelectItem>
                        <SelectItem value="100">100</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="border rounded-xl">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className="cursor-pointer hover:bg-muted/50 select-none">
                          <div className="flex items-center gap-2">
                            Category
                          </div>
                        </TableHead>
                        <TableHead className="cursor-pointer hover:bg-muted/50 select-none">
                          <div className="flex items-center gap-2">
                            Subcategory
                          </div>
                        </TableHead>
                        <TableHead>Active/Inactive</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoadingSubCategories || isFetchingSubCategories ? (
                        <TopicsTableSkeleton />
                      ) : subCategoriesError ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center py-8">
                            <div className="text-red-500">
                              Failed to load scraping sub categories. Please try
                              again.
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : paginatedSubCategories?.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={3} className="text-center py-8">
                            <div>No sub categories found</div>
                          </TableCell>
                        </TableRow>
                      ) : (
                        paginatedSubCategories?.map((topic, i: number) => (
                          <TableRow key={i}>
                            <TableCell
                              title={topic.category_name}
                              className="font-medium"
                            >
                              {topic.category_name
                                ? topic.category_name.length > 25
                                  ? topic.category_name.slice(0, 25) + "..."
                                  : topic.category_name
                                : "--"}
                            </TableCell>
                            <TableCell title={topic.name}>
                              {topic.name
                                ? topic.name.length > 25
                                  ? topic.name.slice(0, 25) + "..."
                                  : topic.name
                                : "--"}
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center">
                                {topic.is_active === "1" ? (
                                  <Switch
                                    className="data-[state=unchecked]:bg-border"
                                    defaultChecked={true}
                                    onCheckedChange={() =>
                                      handleToggleActive(topic)
                                    }
                                  />
                                ) : topic.is_active === "0" ? (
                                  <Switch
                                    className="data-[state=unchecked]:bg-border"
                                    defaultChecked={false}
                                    onCheckedChange={() =>
                                      handleToggleActive(topic)
                                    }
                                  />
                                ) : (
                                  "--"
                                )}
                              </div>
                            </TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>

                {/* Pagination for Topics - centered at bottom */}
                {topicsTotalPages > 0 &&
                  topicsTotal > 0 &&
                  !isLoadingSubCategories &&
                  !isFetchingSubCategories && (
                    <div className="flex items-center justify-center p-4">
                      <Pagination style={{ margin: 0, width: "auto" }}>
                        <PaginationContent>
                          <PaginationItem>
                            <PaginationPrevious
                              href="#"
                              onClick={(e) => {
                                e.preventDefault();
                                if (
                                  topicsCurrentPage > 1 &&
                                  !isLoadingSubCategories &&
                                  !isFetchingSubCategories
                                )
                                  handleTopicsPageChange(topicsCurrentPage - 1);
                              }}
                              className={
                                topicsCurrentPage === 1 ||
                                isLoadingSubCategories ||
                                isFetchingSubCategories
                                  ? "pointer-events-none opacity-50"
                                  : ""
                              }
                            />
                          </PaginationItem>
                          {topicsPageItems.map((item, idx) => (
                            <PaginationItem key={`${item}-${idx}`}>
                              {item === "ellipsis" ? (
                                <PaginationEllipsis />
                              ) : (
                                <PaginationLink
                                  href="#"
                                  isActive={item === topicsCurrentPage}
                                  onClick={(e) => {
                                    e.preventDefault();
                                    if (
                                      !isLoadingSubCategories &&
                                      !isFetchingSubCategories
                                    )
                                      handleTopicsPageChange(item as number);
                                  }}
                                  className={
                                    isLoadingSubCategories ||
                                    isFetchingSubCategories
                                      ? "pointer-events-none opacity-50"
                                      : ""
                                  }
                                >
                                  {item}
                                </PaginationLink>
                              )}
                            </PaginationItem>
                          ))}
                          <PaginationItem>
                            <PaginationNext
                              href="#"
                              onClick={(e) => {
                                e.preventDefault();
                                if (
                                  topicsCurrentPage < topicsTotalPages &&
                                  !isLoadingSubCategories &&
                                  !isFetchingSubCategories
                                )
                                  handleTopicsPageChange(topicsCurrentPage + 1);
                              }}
                              className={
                                topicsCurrentPage === topicsTotalPages ||
                                isLoadingSubCategories ||
                                isFetchingSubCategories
                                  ? "pointer-events-none opacity-50"
                                  : ""
                              }
                            />
                          </PaginationItem>
                        </PaginationContent>
                      </Pagination>
                    </div>
                  )}
              </div>
            </div>
          )}
        </div>

        {/* Topic Dialog */}
        <Dialog
          open={isTopicModalVisible}
          onOpenChange={(open) => {
            if (!open) {
              // Reset form and selected category when dialog is closed
              form.reset();
              setSelectedCategory("");
              setIsCategorySelectOpen(false);
            }
            setIsTopicModalVisible(open);
          }}
        >
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>{"Add Sub Category"}</DialogTitle>
              <DialogDescription>
                {"Enter a name for the new scraping sub category."}
              </DialogDescription>
            </DialogHeader>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleSubmitSubcategory)}
                className="space-y-4"
              >
                <FormField
                  control={form.control}
                  name="category_id"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Category *</FormLabel>
                      <Popover
                        open={isCategorySelectOpen}
                        onOpenChange={setIsCategorySelectOpen}
                      >
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant="outline"
                              role="combobox"
                              aria-expanded={isCategorySelectOpen}
                              className="w-full justify-between"
                              disabled={isCreatingSubcategory}
                            >
                              {selectedCategory
                                ? scrapingCategoriesData?.data?.find(
                                    (category: any) =>
                                      category.id.toString() ===
                                      selectedCategory
                                  )?.name
                                : "Select a category..."}
                              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-full p-0">
                          <Command>
                            <CommandInput
                              placeholder="Search category..."
                              className="h-9"
                            />
                            <CommandList>
                              <CommandEmpty>No category found.</CommandEmpty>
                              <CommandGroup>
                                {scrapingCategoriesData?.data?.map(
                                  (category: any) => (
                                    <CommandItem
                                      key={category.id}
                                      value={category.name}
                                      onSelect={() =>
                                        handleCategorySelect(
                                          category.id.toString()
                                        )
                                      }
                                    >
                                      {category.name}
                                      <Check
                                        className={`ml-auto h-4 w-4 ${
                                          selectedCategory ===
                                          category.id.toString()
                                            ? "opacity-100"
                                            : "opacity-0"
                                        }`}
                                      />
                                    </CommandItem>
                                  )
                                )}
                              </CommandGroup>
                            </CommandList>
                          </Command>
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sub Category Name</FormLabel>
                      <FormControl>
                        <Input
                          type="text"
                          placeholder="e.g., AI Conference Speakers"
                          disabled={isCreatingSubcategory}
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <DialogFooter>
                  <Button
                    type="button"
                    variant="outline"
                    disabled={isCreatingSubcategory}
                    onClick={() => {
                      form.reset();
                      setSelectedCategory("");
                      setIsCategorySelectOpen(false);
                      setIsTopicModalVisible(false);
                    }}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={isCreatingSubcategory}
                    className="min-w-[120px]"
                  >
                    {isCreatingSubcategory ? (
                      <>
                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                        Adding...
                      </>
                    ) : (
                      "Add Sub Category"
                    )}
                  </Button>
                </DialogFooter>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  );
};

export default Scraping;
