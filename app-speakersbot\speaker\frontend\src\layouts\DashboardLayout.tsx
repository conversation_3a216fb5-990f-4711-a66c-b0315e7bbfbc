import { SidebarProvider, SidebarTrigger, SidebarInset } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/common/AppSidebar";
import { Button } from "@/components/ui/button";
import { LogOut, User } from "lucide-react";
import { Outlet, useNavigate } from "react-router-dom";
import { ReactNode } from "react";

interface DashboardLayoutProps {
  onLogout: () => void;
  children?: ReactNode;
}

export default function DashboardLayout({ onLogout, children }: DashboardLayoutProps) {
  const navigate = useNavigate();

  return (
    <SidebarProvider defaultOpen={true}>
      <div className="min-h-screen flex w-full bg-background">
        <AppSidebar />

        <SidebarInset className="w-full">
          <header className="bg-surface border-b border-border-subtle sticky top-0 z-50 flex h-16 shrink-0 items-center gap-2 px-4">
            <SidebarTrigger className="-ml-1" />
            <div className="flex-1" />
            <div className="flex items-center gap-2">
              <Button
                size="sm"
                variant="ghost"
                className="p-2 hover:bg-surface-elevated"
                onClick={() => navigate('/speaker/profile')}
                title="Profile"
              >
                <User className="h-4 w-4 text-foreground-muted" />
              </Button>
              <Button
                size="sm"
                variant="ghost"
                className="p-2 hover:bg-surface-elevated"
                onClick={onLogout}
                title="Logout"
              >
                <LogOut className="h-4 w-4 text-foreground-muted" />
              </Button>
            </div>
          </header>

          <main className="flex-1 overflow-auto p-6">
            {children ?? <Outlet />}
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
}


