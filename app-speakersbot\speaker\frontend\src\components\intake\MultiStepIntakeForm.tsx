import React, { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AnimatedStepper } from '@/components/ui/animated-stepper';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { DogAnimation } from '@/components/ui/dog-animation';
import { AlertCircle, CheckCircle, User, Mail, Building, MapPin, Award, Upload, ArrowLeft, ArrowRight, Phone, Globe, Calendar, Settings, FileText, Briefcase } from 'lucide-react';

interface FormData {
  // Core Phase 1
  primaryCategory: string;
  subcategory: string;
  topic: string;
  
  // Core Phase 2
  name: string;
  email: string;
  phoneNumber: string;
  city: string;
  state: string;
  country: string;
  speakerWebsite: string;
  linkedin: string;
  companyName: string;
  title: string;
  bio: string;
  headshot: File | null;
  learningObjectives: string[];
  takeaways: string[];
  challenges: string[];
  preferredGeography: string[];
  speakerCredentials: string;
  topKeywords: string;
  differentiators: string;
  
  // Psychometric Self-Report
  mbti: string;
  disc: string;
  bigFive: string;
  enneagram: string;
  cliftonStrengths: string;
  via: string;
  riasec: string;
  
  // Media Uploads
  voiceClip: File | null;
  videoClip: File | null;
  
  // Consent
  consent: boolean;
  
  // Evergreen Vault
  fullBio: string;
  logo: File | null;
  additionalHeadshots: File | null;
  oneSheetPdf: File | null;
  pastTalkDecks: File | null;
  sizzleReel: File | null;
  pressCoverageLinks: string;
  testimonials: string;
  awardsRecognitions: string;
  podcastAppearances: string;
  tedTalkLink: string;
  tedxTranscript: File | null;
  affiliateProgramParticipation: string;
  affiliateProgramDetails: string;
  referNewAffiliates: string;
  affiliateNotes: string;
  travelRider: File | null;
  liveSpeakingFee: string;
  virtualSpeakingFee: string;
  monthlyPaidSpeakingGoals: string;
}

export function MultiStepIntakeForm() {
  const [showOverview, setShowOverview] = useState(true);
  const [currentStep, setCurrentStep] = useState(1);
  const [isTyping, setIsTyping] = useState(false);
  const [fieldCompleted, setFieldCompleted] = useState(false);
  const [activeFieldPosition, setActiveFieldPosition] = useState<{ x: number, y: number } | undefined>();
  const [lastCompletedField, setLastCompletedField] = useState('');
  const [formData, setFormData] = useState<FormData>({
    // Core Phase 1
    primaryCategory: '',
    subcategory: '',
    topic: '',
    
    // Core Phase 2
    name: '',
    email: '',
    phoneNumber: '',
    city: '',
    state: '',
    country: '',
    speakerWebsite: '',
    linkedin: '',
    companyName: '',
    title: '',
    bio: '',
    headshot: null,
    learningObjectives: ['', '', '', '', ''],
    takeaways: ['', '', '', '', ''],
    challenges: ['', '', '', '', ''],
    preferredGeography: [],
    speakerCredentials: '',
    topKeywords: '',
    differentiators: '',
    
    // Psychometric Self-Report
    mbti: '',
    disc: '',
    bigFive: '',
    enneagram: '',
    cliftonStrengths: '',
    via: '',
    riasec: '',
    
    // Media Uploads
    voiceClip: null,
    videoClip: null,
    
    // Consent
    consent: false,
    
    // Evergreen Vault
    fullBio: '',
    logo: null,
    additionalHeadshots: null,
    oneSheetPdf: null,
    pastTalkDecks: null,
    sizzleReel: null,
    pressCoverageLinks: '',
    testimonials: '',
    awardsRecognitions: '',
    podcastAppearances: '',
    tedTalkLink: '',
    tedxTranscript: null,
    affiliateProgramParticipation: '',
    affiliateProgramDetails: '',
    referNewAffiliates: '',
    affiliateNotes: '',
    travelRider: null,
    liveSpeakingFee: '',
    virtualSpeakingFee: '',
    monthlyPaidSpeakingGoals: '',
  });

  const steps = [
    { id: 1, title: 'STAiGENT Setting', icon: User, description: 'Core intake phase 1' },
    { id: 2, title: 'STAiGENT Storyline', icon: Briefcase, description: 'Core intake phase 2' },
    { id: 3, title: 'STAiGENT Script™', icon: Upload, description: 'Upload media content' },
    { id: 4, title: 'Psycometric Self-Report', icon: FileText, description: 'our responses fuel the creation of your STAiGENT Signature the unique profile that defines how your AiGENT represents you' },
    { id: 5, title: 'STAiGENT Insights', icon: Building, description: 'Identity insights that guide your AiGENT Voice and Tone' },
    { id: 6, title: 'Evergreen Vault', icon: Globe, description: 'Evergreen content repository' },
    { id: 7, title: 'Consent', icon: CheckCircle, description: 'Consent and agreements' },
    { id: 8, title: 'Your Agent Score', icon: Award, description: 'Agent performance metrics' },
  ];

  const totalSteps = steps.length;


  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleStepClick = (stepId: number) => {
    // Allow navigation to completed steps and current step
    if (stepId <= currentStep) {
      setCurrentStep(stepId);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Form submitted:', formData);
    // Save to localStorage for now
    localStorage.setItem('speakerProfile', JSON.stringify(formData));
    // Later: sync with API
  };


  const updateFormData = (field: keyof FormData, value: string | File | null | boolean) => {
    const wasEmpty = !formData[field] || formData[field] === '' || formData[field] === null;
    const isNowFilled = value && value !== '' && value !== null;
    
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Trigger celebration if field was completed
    if (wasEmpty && isNowFilled && field !== lastCompletedField) {
      setFieldCompleted(true);
      setLastCompletedField(field as string);
      setTimeout(() => setFieldCompleted(false), 1000);
    }
  };

  const updateArrayField = (field: 'learningObjectives' | 'takeaways' | 'challenges', index: number, value: string) => {
    setFormData(prev => {
      const newArray = [...prev[field]];
      newArray[index] = value;
      return { ...prev, [field]: newArray };
    });
  };

  const handleGeographyToggle = (geography: string) => {
    setFormData(prev => {
      const currentGeographies = prev.preferredGeography;
      const updatedGeographies = currentGeographies.includes(geography)
        ? currentGeographies.filter(g => g !== geography)
        : [...currentGeographies, geography];
      return { ...prev, preferredGeography: updatedGeographies };
    });
  };

  const handleInputFocus = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const rect = e.target.getBoundingClientRect();
    setActiveFieldPosition({
      x: rect.left + rect.width / 2,
      y: rect.top + window.scrollY
    });
    setIsTyping(true);
  };

  const handleInputBlur = () => {
    setIsTyping(false);
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    updateFormData('headshot', file);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1: // Core Phase 1
        return (
          <div className="space-y-6">
            <div className="grid gap-4">
              <div className="space-y-2">
                <Label htmlFor="primaryCategory">Primary Category *</Label>
                <Select value={formData.primaryCategory} onValueChange={(value) => updateFormData('primaryCategory', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select primary category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="technology">Technology</SelectItem>
                    <SelectItem value="business">Business</SelectItem>
                    <SelectItem value="marketing">Marketing</SelectItem>
                    <SelectItem value="healthcare">Healthcare</SelectItem>
                    <SelectItem value="education">Education</SelectItem>
                    <SelectItem value="finance">Finance</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="subcategory">Subcategory *</Label>
                <Select value={formData.subcategory} onValueChange={(value) => updateFormData('subcategory', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select subcategory" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="artificial-intelligence">Artificial Intelligence</SelectItem>
                    <SelectItem value="digital-transformation">Digital Transformation</SelectItem>
                    <SelectItem value="leadership">Leadership</SelectItem>
                    <SelectItem value="innovation">Innovation</SelectItem>
                    <SelectItem value="sustainability">Sustainability</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="topic">Topic</Label>
                <Input
                  id="topic"
                  value={formData.topic}
                  onChange={(e) => updateFormData('topic', e.target.value)}
                  placeholder="Enter your speaking topic"
                />
              </div>
            </div>
          </div>
        );
        
      case 2: // Core Phase 2
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => updateFormData('name', e.target.value)}
                  onFocus={handleInputFocus}
                  onBlur={handleInputBlur}
                  placeholder="Your full name"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => updateFormData('email', e.target.value)}
                  onFocus={handleInputFocus}
                  onBlur={handleInputBlur}
                  placeholder="<EMAIL>"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phoneNumber">Phone Number</Label>
                <Input
                  id="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={(e) => updateFormData('phoneNumber', e.target.value)}
                  placeholder="+****************"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => updateFormData('city', e.target.value)}
                  placeholder="City"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="state">State</Label>
                <Input
                  id="state"
                  value={formData.state}
                  onChange={(e) => updateFormData('state', e.target.value)}
                  placeholder="State/Province"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="country">Country</Label>
                <Input
                  id="country"
                  value={formData.country}
                  onChange={(e) => updateFormData('country', e.target.value)}
                  placeholder="Country"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="speakerWebsite">Speaker Website</Label>
                <Input
                  id="speakerWebsite"
                  type="url"
                  value={formData.speakerWebsite}
                  onChange={(e) => updateFormData('speakerWebsite', e.target.value)}
                  placeholder="https://yourwebsite.com"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="linkedin">LinkedIn</Label>
                <Input
                  id="linkedin"
                  type="url"
                  value={formData.linkedin}
                  onChange={(e) => updateFormData('linkedin', e.target.value)}
                  placeholder="https://linkedin.com/in/yourprofile"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="companyName">Your Company Name</Label>
                <Input
                  id="companyName"
                  value={formData.companyName}
                  onChange={(e) => updateFormData('companyName', e.target.value)}
                  placeholder="Company name"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => updateFormData('title', e.target.value)}
                  placeholder="Your job title"
                />
              </div>
            </div>
            
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  value={formData.bio}
                  onChange={(e) => updateFormData('bio', e.target.value)}
                  onFocus={handleInputFocus}
                  onBlur={handleInputBlur}
                  placeholder="Tell us about yourself, your expertise, and your speaking experience..."
                  rows={4}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="headshot">Headshot</Label>
                <div className="flex items-center gap-3">
                  <input
                    id="headshot"
                    type="file"
                    accept="image/*"
                    onChange={handleFileChange}
                    className="text-sm text-foreground file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-primary-foreground hover:file:bg-primary/90"
                  />
                  {formData.headshot && (
                    <span className="text-sm text-foreground-muted">{formData.headshot.name}</span>
                  )}
                </div>
              </div>
              
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <Label>Learning Objectives</Label>
                    {formData.learningObjectives.map((objective, index) => (
                      <Input
                        key={`learningObjective-${index}`}
                        value={objective}
                        onChange={(e) => updateArrayField('learningObjectives', index, e.target.value)}
                        placeholder={`Learning Objective ${index + 1}`}
                      />
                    ))}
                  </div>
                  
                  <div className="space-y-4">
                    <Label>Takeaways</Label>
                    {formData.takeaways.map((takeaway, index) => (
                      <Input
                        key={`takeaway-${index}`}
                        value={takeaway}
                        onChange={(e) => updateArrayField('takeaways', index, e.target.value)}
                        placeholder={`Takeaway ${index + 1}`}
                      />
                    ))}
                  </div>
                </div>
                
                <div className="space-y-4">
                  <Label>Challenges</Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {formData.challenges.map((challenge, index) => (
                      <Input
                        key={`challenge-${index}`}
                        value={challenge}
                        onChange={(e) => updateArrayField('challenges', index, e.target.value)}
                        placeholder={`Challenge ${index + 1}`}
                      />
                    ))}
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="speakerCredentials">Speaker Credentials</Label>
                  <Textarea
                    id="speakerCredentials"
                    value={formData.speakerCredentials}
                    onChange={(e) => updateFormData('speakerCredentials', e.target.value)}
                    placeholder="Your speaking credentials and experience"
                    rows={3}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="topKeywords">Top Keywords</Label>
                  <Input
                    id="topKeywords"
                    value={formData.topKeywords}
                    onChange={(e) => updateFormData('topKeywords', e.target.value)}
                    placeholder="Key topics and expertise areas"
                  />
                </div>
                
                <div className="space-y-4">
                  <Label>Preferred Speaker Geography</Label>
                  <div className="flex flex-wrap gap-3">
                    {['Local', 'Regional', 'State', 'National', 'International', 'Other'].map((geography) => (
                      <button
                        key={geography}
                        type="button"
                        onClick={() => handleGeographyToggle(geography)}
                        className={`px-4 py-2 rounded-full border text-sm font-medium transition-all ${
                          formData.preferredGeography.includes(geography)
                            ? 'bg-primary text-primary-foreground border-primary'
                            : 'bg-background text-foreground border-border hover:bg-muted'
                        }`}
                      >
                        {geography}
                      </button>
                    ))}
                  </div>
                </div>
                
                <div className="md:col-span-2 space-y-2">
                  <Label htmlFor="differentiators">Differentiators</Label>
                  <Textarea
                    id="differentiators"
                    value={formData.differentiators}
                    onChange={(e) => updateFormData('differentiators', e.target.value)}
                    placeholder="What makes you unique as a speaker?"
                    rows={3}
                  />
                </div>
              </div>
            </div>
          </div>
        );
        
      case 3: // Media Uploads
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 gap-6">
              <div className="space-y-2">
                <Label htmlFor="voiceClip">Voice Clip Upload</Label>
                <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 mx-auto mb-2 text-foreground-muted" />
                  <p className="text-sm text-foreground-muted mb-2">Upload a short voice clip</p>
                  <Input
                    id="voiceClip"
                    type="file"
                    accept="audio/*"
                    onChange={(e) => updateFormData('voiceClip', e.target.files?.[0] || null)}
                    className="max-w-sm mx-auto"
                  />
                  {formData.voiceClip && (
                    <p className="text-sm text-primary mt-2">
                      Selected: {formData.voiceClip.name}
                    </p>
                  )}
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="videoClip">Video Clip Upload</Label>
                <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 mx-auto mb-2 text-foreground-muted" />
                  <p className="text-sm text-foreground-muted mb-2">Upload a short video clip</p>
                  <Input
                    id="videoClip"
                    type="file"
                    accept="video/*"
                    onChange={(e) => updateFormData('videoClip', e.target.files?.[0] || null)}
                    className="max-w-sm mx-auto"
                  />
                  {formData.videoClip && (
                    <p className="text-sm text-primary mt-2">
                      Selected: {formData.videoClip.name}
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        );
        
      case 4: // Psychometric Self-Report
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="mbti">MBTI</Label>
                <Input
                  id="mbti"
                  value={formData.mbti}
                  onChange={(e) => updateFormData('mbti', e.target.value)}
                  placeholder="Self-report: Myers-Briggs type (e.g., ENFP, ISTJ)"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="disc">DiSC</Label>
                <Input
                  id="disc"
                  value={formData.disc}
                  onChange={(e) => updateFormData('disc', e.target.value)}
                  placeholder="Self-report: DiSC style (e.g., D, I, S, C)"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="bigFive">Big Five</Label>
                <Input
                  id="bigFive"
                  value={formData.bigFive}
                  onChange={(e) => updateFormData('bigFive', e.target.value)}
                  placeholder="Self-report: Big Five (OCEAN) profile"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="enneagram">Enneagram</Label>
                <Input
                  id="enneagram"
                  value={formData.enneagram}
                  onChange={(e) => updateFormData('enneagram', e.target.value)}
                  placeholder="Self-report: Enneagram type (e.g., Type 1, Type 2)"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="cliftonStrengths">CliftonStrengths</Label>
                <Input
                  id="cliftonStrengths"
                  value={formData.cliftonStrengths}
                  onChange={(e) => updateFormData('cliftonStrengths', e.target.value)}
                  placeholder="Self-report: CliftonStrengths top 5"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="via">VIA</Label>
                <Input
                  id="via"
                  value={formData.via}
                  onChange={(e) => updateFormData('via', e.target.value)}
                  placeholder="Self-report: VIA Character Strengths top 5"
                />
              </div>
              
              <div className="md:col-span-2 space-y-2">
                <Label htmlFor="riasec">RIASEC</Label>
                <Input
                  id="riasec"
                  value={formData.riasec}
                  onChange={(e) => updateFormData('riasec', e.target.value)}
                  placeholder="Self-report: Holland Code (e.g., Realistic, Investigative, Artistic)"
                />
              </div>
            </div>
          </div>
        );
        
      case 5: // Psychometric Proxies
        return (
          <div className="space-y-10">
            {/* Question 1 */}
            <div className="space-y-4">
              <Label className="text-base font-medium flex items-start gap-3">
                <span className="flex-shrink-0 font-semibold text-foreground">1.)</span>
                <span>When you walk off stage, do you usually feel more energized by the crowd or drained and ready for quiet?</span>
              </Label>
              <div className="ml-8">
                <RadioGroup value={formData.mbti} onValueChange={(value) => updateFormData('mbti', value)} className="flex flex-wrap gap-8">
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Energized by crowd" id="q1-energized" />
                    <Label htmlFor="q1-energized" className="cursor-pointer">Energized by crowd</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Drained, need quiet" id="q1-drained" />
                    <Label htmlFor="q1-drained" className="cursor-pointer">Drained, need quiet</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Mixed" id="q1-mixed" />
                    <Label htmlFor="q1-mixed" className="cursor-pointer">Mixed</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>

            {/* Separator Line */}
            <div className="border-b border-border/30"></div>

            {/* Question 2 */}
            <div className="space-y-4">
              <Label className="text-base font-medium flex items-start gap-3">
                <span className="flex-shrink-0 font-semibold text-foreground">2.)</span>
                <span>Do you prefer sticking to a clear plan and checklist, or adapting in the moment as things unfold?</span>
              </Label>
              <div className="ml-8">
                <RadioGroup value={formData.bigFive} onValueChange={(value) => updateFormData('bigFive', value)} className="flex flex-wrap gap-8">
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Plan / Checklist" id="q2-plan" />
                    <Label htmlFor="q2-plan" className="cursor-pointer">Plan / Checklist</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Adapt / Flow" id="q2-adapt" />
                    <Label htmlFor="q2-adapt" className="cursor-pointer">Adapt / Flow</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Depends" id="q2-depends" />
                    <Label htmlFor="q2-depends" className="cursor-pointer">Depends</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>

            {/* Separator Line */}
            <div className="border-b border-border/30"></div>

            {/* Question 3 */}
            <div className="space-y-4">
              <Label className="text-base font-medium flex items-start gap-3">
                <span className="flex-shrink-0 font-semibold text-foreground">3.)</span>
                <span>What pushes you hardest when preparing a talk — making it flawless, inspiring people, solving problems, or connecting deeply?</span>
              </Label>
              <div className="ml-8">
                <RadioGroup value={formData.enneagram} onValueChange={(value) => updateFormData('enneagram', value)} className="flex flex-wrap gap-8">
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Flawless" id="q3-flawless" />
                    <Label htmlFor="q3-flawless" className="cursor-pointer">Flawless</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Inspiring People" id="q3-inspiring" />
                    <Label htmlFor="q3-inspiring" className="cursor-pointer">Inspiring People</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Solving Problems" id="q3-solving" />
                    <Label htmlFor="q3-solving" className="cursor-pointer">Solving Problems</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>

            {/* Separator Line */}
            <div className="border-b border-border/30"></div>

            {/* Question 4 */}
            <div className="space-y-4">
              <Label className="text-base font-medium flex items-start gap-3">
                <span className="flex-shrink-0 font-semibold text-foreground">4.)</span>
                <span>When an audience challenges you, do you win them with stories and enthusiasm, with facts and data, or by inviting collaboration?</span>
              </Label>
              <div className="ml-8">
                <RadioGroup value={formData.disc} onValueChange={(value) => updateFormData('disc', value)} className="flex flex-wrap gap-8">
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Stories & Enthusiasm" id="q4-stories" />
                    <Label htmlFor="q4-stories" className="cursor-pointer">Stories & Enthusiasm</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Facts & Data" id="q4-facts" />
                    <Label htmlFor="q4-facts" className="cursor-pointer">Facts & Data</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Invite Collaboration" id="q4-collaboration" />
                    <Label htmlFor="q4-collaboration" className="cursor-pointer">Invite Collaboration</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>

            {/* Separator Line */}
            <div className="border-b border-border/30"></div>

            {/* Question 5 */}
            <div className="space-y-4">
              <Label className="text-base font-medium flex items-start gap-3">
                <span className="flex-shrink-0 font-semibold text-foreground">5.)</span>
                <span>Do you naturally weave themes like gratitude, fairness, hope, or strategy into your message?</span>
              </Label>
              <div className="ml-8">
                <RadioGroup value={formData.via} onValueChange={(value) => updateFormData('via', value)} className="flex flex-wrap gap-8">
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Gratitude" id="q5-gratitude" />
                    <Label htmlFor="q5-gratitude" className="cursor-pointer">Gratitude</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Fairness" id="q5-fairness" />
                    <Label htmlFor="q5-fairness" className="cursor-pointer">Fairness</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Hope" id="q5-hope" />
                    <Label htmlFor="q5-hope" className="cursor-pointer">Hope</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Strategy" id="q5-strategy" />
                    <Label htmlFor="q5-strategy" className="cursor-pointer">Strategy</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>

            {/* Separator Line */}
            <div className="border-b border-border/30"></div>

            {/* Question 6 */}
            <div className="space-y-4">
              <Label className="text-base font-medium flex items-start gap-3">
                <span className="flex-shrink-0 font-semibold text-foreground">6.)</span>
                <span>If you weren't speaking, would you rather be hands-on building something, exploring ideas, leading a team, or helping people grow?</span>
              </Label>
              <div className="ml-8">
                <RadioGroup value={formData.riasec} onValueChange={(value) => updateFormData('riasec', value)} className="flex flex-wrap gap-8">
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Building something" id="q6-building" />
                    <Label htmlFor="q6-building" className="cursor-pointer">Building something</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Exploring ideas" id="q6-exploring" />
                    <Label htmlFor="q6-exploring" className="cursor-pointer">Exploring ideas</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Leading a team" id="q6-leading" />
                    <Label htmlFor="q6-leading" className="cursor-pointer">Leading a team</Label>
                  </div>
                  <div className="flex items-center space-x-3">
                    <RadioGroupItem value="Helping people grow" id="q6-helping" />
                    <Label htmlFor="q6-helping" className="cursor-pointer">Helping people grow</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>
          </div>
        );
        
        
      case 6: // Evergreen Vault
        return (
          <div className="space-y-6">
            {/* Summary of Previous Steps */}
            <div className="bg-accent/10 border border-border rounded-lg p-6">
              <h3 className="text-lg font-semibold text-foreground mb-4">Summary of Your Information</h3>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                {/* Personal Information */}
                <div>
                  <h4 className="font-medium text-foreground mb-2">Personal Information</h4>
                  <div className="space-y-1 text-foreground-muted">
                    {formData.name && <p><span className="font-medium">Name:</span> {formData.name}</p>}
                    {formData.email && <p><span className="font-medium">Email:</span> {formData.email}</p>}
                    {formData.bio && <p><span className="font-medium">Bio:</span> {formData.bio.slice(0, 100)}...</p>}
                    {formData.topic && <p><span className="font-medium">Topic:</span> {formData.topic}</p>}
                  </div>
                </div>

                {/* Professional Information */}
                <div>
                  <h4 className="font-medium text-foreground mb-2">Professional Information</h4>
                  <div className="space-y-1 text-foreground-muted">
                    {formData.companyName && <p><span className="font-medium">Company:</span> {formData.companyName}</p>}
                    {formData.title && <p><span className="font-medium">Job Title:</span> {formData.title}</p>}
                    {formData.speakerCredentials && <p><span className="font-medium">Credentials:</span> {formData.speakerCredentials}</p>}
                    {formData.speakerWebsite && <p><span className="font-medium">Website:</span> {formData.speakerWebsite}</p>}
                  </div>
                </div>

                {/* Speaking Information */}
                <div>
                  <h4 className="font-medium text-foreground mb-2">Speaking Information</h4>
                  <div className="space-y-1 text-foreground-muted">
                    {formData.primaryCategory && <p><span className="font-medium">Primary Category:</span> {formData.primaryCategory}</p>}
                    {formData.subcategory && <p><span className="font-medium">Subcategory:</span> {formData.subcategory}</p>}
                    {formData.topKeywords && <p><span className="font-medium">Top Keywords:</span> {formData.topKeywords}</p>}
                    {formData.differentiators && <p><span className="font-medium">Differentiators:</span> {formData.differentiators.slice(0, 100)}...</p>}
                  </div>
                </div>

                {/* Assessment Results */}
                <div>
                  <h4 className="font-medium text-foreground mb-2">Assessment Results</h4>
                  <div className="space-y-1 text-foreground-muted">
                    {formData.mbti && <p><span className="font-medium">MBTI:</span> {formData.mbti}</p>}
                    {formData.disc && <p><span className="font-medium">DISC:</span> {formData.disc}</p>}
                    {formData.bigFive && <p><span className="font-medium">Big Five:</span> {formData.bigFive}</p>}
                    {formData.enneagram && <p><span className="font-medium">Enneagram:</span> {formData.enneagram}</p>}
                  </div>
                </div>
              </div>

              {/* Media Assets */}
              <div className="mt-4">
                <h4 className="font-medium text-foreground mb-2">Uploaded Media Assets</h4>
                <div className="flex flex-wrap gap-2 text-xs">
                  {formData.voiceClip && <span className="bg-primary/10 text-primary px-2 py-1 rounded">Voice Clip ✓</span>}
                  {formData.videoClip && <span className="bg-primary/10 text-primary px-2 py-1 rounded">Video Clip ✓</span>}
                  {formData.headshot && <span className="bg-primary/10 text-primary px-2 py-1 rounded">Headshot ✓</span>}
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 gap-6">
              {/* Full Bio */}
              <div className="space-y-2">
                <Label htmlFor="fullBio">Full Bio (long version)</Label>
                <Textarea
                  id="fullBio"
                  value={formData.fullBio}
                  onChange={(e) => updateFormData('fullBio', e.target.value)}
                  placeholder="Enter your complete bio"
                  rows={6}
                />
              </div>
              
              {/* Logo Upload */}
              <div className="space-y-2">
                <Label htmlFor="logo">Logo</Label>
                <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 mx-auto mb-2 text-foreground-muted" />
                  <p className="text-sm text-foreground-muted mb-2">Upload your logo</p>
                  <Input
                    id="logo"
                    type="file"
                    accept="image/*"
                    onChange={(e) => updateFormData('logo', e.target.files?.[0] || null)}
                    className="max-w-sm mx-auto"
                  />
                  {formData.logo && (
                    <p className="text-sm text-primary mt-2">Selected: {formData.logo.name}</p>
                  )}
                </div>
              </div>
              
              {/* Additional Headshots */}
              <div className="space-y-2">
                <Label htmlFor="additionalHeadshots">Additional Headshots</Label>
                <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 mx-auto mb-2 text-foreground-muted" />
                  <p className="text-sm text-foreground-muted mb-2">Upload additional headshots</p>
                  <Input
                    id="additionalHeadshots"
                    type="file"
                    accept="image/*"
                    onChange={(e) => updateFormData('additionalHeadshots', e.target.files?.[0] || null)}
                    className="max-w-sm mx-auto"
                  />
                  {formData.additionalHeadshots && (
                    <p className="text-sm text-primary mt-2">Selected: {formData.additionalHeadshots.name}</p>
                  )}
                </div>
              </div>
              
              {/* One-sheet PDF */}
              <div className="space-y-2">
                <Label htmlFor="oneSheetPdf">One-sheet PDF</Label>
                <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 mx-auto mb-2 text-foreground-muted" />
                  <p className="text-sm text-foreground-muted mb-2">Upload your one-sheet PDF</p>
                  <Input
                    id="oneSheetPdf"
                    type="file"
                    accept=".pdf"
                    onChange={(e) => updateFormData('oneSheetPdf', e.target.files?.[0] || null)}
                    className="max-w-sm mx-auto"
                  />
                  {formData.oneSheetPdf && (
                    <p className="text-sm text-primary mt-2">Selected: {formData.oneSheetPdf.name}</p>
                  )}
                </div>
              </div>
              
              {/* Past Talk Decks */}
              <div className="space-y-2">
                <Label htmlFor="pastTalkDecks">Past Talk Decks</Label>
                <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 mx-auto mb-2 text-foreground-muted" />
                  <p className="text-sm text-foreground-muted mb-2">Upload past presentation decks</p>
                  <Input
                    id="pastTalkDecks"
                    type="file"
                    accept=".pdf,.ppt,.pptx"
                    onChange={(e) => updateFormData('pastTalkDecks', e.target.files?.[0] || null)}
                    className="max-w-sm mx-auto"
                  />
                  {formData.pastTalkDecks && (
                    <p className="text-sm text-primary mt-2">Selected: {formData.pastTalkDecks.name}</p>
                  )}
                </div>
              </div>
              
              {/* Sizzle Reel */}
              <div className="space-y-2">
                <Label htmlFor="sizzleReel">Sizzle Reel</Label>
                <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 mx-auto mb-2 text-foreground-muted" />
                  <p className="text-sm text-foreground-muted mb-2">Upload your sizzle reel video</p>
                  <Input
                    id="sizzleReel"
                    type="file"
                    accept="video/*"
                    onChange={(e) => updateFormData('sizzleReel', e.target.files?.[0] || null)}
                    className="max-w-sm mx-auto"
                  />
                  {formData.sizzleReel && (
                    <p className="text-sm text-primary mt-2">Selected: {formData.sizzleReel.name}</p>
                  )}
                </div>
              </div>
              
              {/* Press Coverage Links */}
              <div className="space-y-2">
                <Label htmlFor="pressCoverageLinks">Press Coverage Links</Label>
                <Textarea
                  id="pressCoverageLinks"
                  value={formData.pressCoverageLinks}
                  onChange={(e) => updateFormData('pressCoverageLinks', e.target.value)}
                  placeholder="Enter press coverage URLs (one per line)"
                  rows={3}
                />
              </div>
              
              {/* Testimonials */}
              <div className="space-y-2">
                <Label htmlFor="testimonials">Testimonials</Label>
                <Textarea
                  id="testimonials"
                  value={formData.testimonials}
                  onChange={(e) => updateFormData('testimonials', e.target.value)}
                  placeholder="Enter client testimonials"
                  rows={4}
                />
              </div>
              
              {/* Awards/Recognitions */}
              <div className="space-y-2">
                <Label htmlFor="awardsRecognitions">Awards/Recognitions</Label>
                <Textarea
                  id="awardsRecognitions"
                  value={formData.awardsRecognitions}
                  onChange={(e) => updateFormData('awardsRecognitions', e.target.value)}
                  placeholder="List your awards and recognitions"
                  rows={3}
                />
              </div>
              
              {/* Podcast Appearances */}
              <div className="space-y-2">
                <Label htmlFor="podcastAppearances">Podcast Appearances</Label>
                <Textarea
                  id="podcastAppearances"
                  value={formData.podcastAppearances}
                  onChange={(e) => updateFormData('podcastAppearances', e.target.value)}
                  placeholder="Enter podcast appearance URLs (one per line)"
                  rows={3}
                />
              </div>
              
              {/* TED/TEDx Talk Link */}
              <div className="space-y-2">
                <Label htmlFor="tedTalkLink">TED or TEDx Talk Link</Label>
                <Input
                  id="tedTalkLink"
                  value={formData.tedTalkLink}
                  onChange={(e) => updateFormData('tedTalkLink', e.target.value)}
                  placeholder="https://www.ted.com/talks/..."
                />
              </div>
              
              {/* TEDx Transcript Upload */}
              <div className="space-y-2">
                <Label htmlFor="tedxTranscript">TEDx Transcript Upload</Label>
                <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 mx-auto mb-2 text-foreground-muted" />
                  <p className="text-sm text-foreground-muted mb-2">Upload TEDx talk transcript</p>
                  <Input
                    id="tedxTranscript"
                    type="file"
                    accept=".pdf,.doc,.docx,.txt"
                    onChange={(e) => updateFormData('tedxTranscript', e.target.files?.[0] || null)}
                    className="max-w-sm mx-auto"
                  />
                  {formData.tedxTranscript && (
                    <p className="text-sm text-primary mt-2">Selected: {formData.tedxTranscript.name}</p>
                  )}
                </div>
              </div>
              
              {/* Affiliate Program Participation */}
              <div className="space-y-2">
                <Label htmlFor="affiliateProgramParticipation">Affiliate Program Participation</Label>
                <Select value={formData.affiliateProgramParticipation} onValueChange={(value) => updateFormData('affiliateProgramParticipation', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select participation status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="yes">Yes</SelectItem>
                    <SelectItem value="no">No</SelectItem>
                  </SelectContent>
                </Select>
                
                {formData.affiliateProgramParticipation === 'yes' && (
                  <div className="mt-3">
                    <Label htmlFor="affiliateProgramDetails">Program Details</Label>
                    <Textarea
                      id="affiliateProgramDetails"
                      value={formData.affiliateProgramDetails}
                      onChange={(e) => updateFormData('affiliateProgramDetails', e.target.value)}
                      placeholder="Provide details about your affiliate program participation"
                      rows={3}
                    />
                  </div>
                )}
              </div>
              
              {/* Refer New Affiliates */}
              <div className="space-y-2">
                <Label htmlFor="referNewAffiliates">Refer New Affiliates</Label>
                <Textarea
                  id="referNewAffiliates"
                  value={formData.referNewAffiliates}
                  onChange={(e) => updateFormData('referNewAffiliates', e.target.value)}
                  placeholder="List potential affiliate referrals (one per line)"
                  rows={3}
                />
              </div>
              
              {/* Affiliate Notes */}
              <div className="space-y-2">
                <Label htmlFor="affiliateNotes">Affiliate Notes</Label>
                <Textarea
                  id="affiliateNotes"
                  value={formData.affiliateNotes}
                  onChange={(e) => updateFormData('affiliateNotes', e.target.value)}
                  placeholder="Additional notes about affiliate programs"
                  rows={3}
                />
              </div>
              
              {/* Travel Rider */}
              <div className="space-y-2">
                <Label htmlFor="travelRider">Travel Rider</Label>
                <div className="border-2 border-dashed border-border rounded-lg p-6 text-center">
                  <Upload className="h-8 w-8 mx-auto mb-2 text-foreground-muted" />
                  <p className="text-sm text-foreground-muted mb-2">Upload your travel rider document</p>
                  <Input
                    id="travelRider"
                    type="file"
                    accept=".pdf,.doc,.docx,.txt"
                    onChange={(e) => updateFormData('travelRider', e.target.files?.[0] || null)}
                    className="max-w-sm mx-auto"
                  />
                  {formData.travelRider && (
                    <p className="text-sm text-primary mt-2">Selected: {formData.travelRider.name}</p>
                  )}
                </div>
              </div>
              
              {/* Speaking Fees */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="liveSpeakingFee">Live Speaking Fee</Label>
                  <Input
                    id="liveSpeakingFee"
                    value={formData.liveSpeakingFee}
                    onChange={(e) => updateFormData('liveSpeakingFee', e.target.value)}
                    placeholder="$5,000"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="virtualSpeakingFee">Virtual Speaking Fee</Label>
                  <Input
                    id="virtualSpeakingFee"
                    value={formData.virtualSpeakingFee}
                    onChange={(e) => updateFormData('virtualSpeakingFee', e.target.value)}
                    placeholder="$2,500"
                  />
                </div>
              </div>
              
              {/* Monthly Goals */}
              <div className="space-y-2">
                <Label htmlFor="monthlyPaidSpeakingGoals">Monthly Paid Speaking Goals</Label>
                <Input
                  id="monthlyPaidSpeakingGoals"
                  value={formData.monthlyPaidSpeakingGoals}
                  onChange={(e) => updateFormData('monthlyPaidSpeakingGoals', e.target.value)}
                  placeholder="3"
                />
              </div>
            </div>
          </div>
        );
        
      case 7: // Consent
        return (
          <div className="space-y-6">
            <div className="max-w-2xl mx-auto space-y-4">
              {/* AI Note - Above the card */}
              <div className="text-center">
                <p className="text-sm text-foreground-muted">
                  This form adapts dynamically based on your inputs using AI. It is not a static questionnaire
                </p>
              </div>
              
              {/* Consent Card */}
              <div className="bg-surface border border-border rounded-lg p-6">
                <div className="flex items-start space-x-3">
                  <Checkbox
                    id="consent"
                    checked={formData.consent}
                    onCheckedChange={(checked) => updateFormData('consent', checked as boolean)}
                    className="mt-1"
                  />
                  <Label htmlFor="consent" className="text-sm leading-relaxed cursor-pointer text-left">
                    By submitting, I agree to share my profile, recordings, and materials for event matching and speaker promotion
                  </Label>
                </div>
              </div>
            </div>
          </div>
        );
        
      case 8: // Your Agent Score
        return (
          <div className="space-y-6">
            <div className="max-w-4xl mx-auto">
              {/* Score Header */}
              <div className="text-center mb-8">
                <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-primary/10 mb-4">
                  <Award className="h-10 w-10 text-primary" />
                </div>
                <h3 className="text-2xl font-bold text-foreground mb-2">Your Agent Score</h3>
                <p className="text-foreground-muted">Complete more fields to improve your matching potential</p>
              </div>

              {/* Score Calculation */}
              {(() => {
                const totalFields = Object.keys(formData).length - 1; // Exclude consent
                const completedFields = Object.entries(formData).filter(([key, value]) => {
                  if (key === 'consent') return false; // Skip consent in scoring
                  if (typeof value === 'string') return value.trim() !== '';
                  if (value instanceof File) return value !== null;
                  if (typeof value === 'boolean') return true;
                  return value !== null && value !== '';
                }).length;
                
                const score = Math.round((completedFields / totalFields) * 100);
                const incompleteFields = Object.entries(formData).filter(([key, value]) => {
                  if (key === 'consent') return false;
                  if (typeof value === 'string') return value.trim() === '';
                  if (value instanceof File) return value === null;
                  return value === null || value === '';
                });

                const fieldLabels: Record<string, string> = {
                  primaryCategory: 'Primary Category',
                  subcategory: 'Subcategory', 
                  topic: 'Topic',
                  name: 'Name',
                  email: 'Email',
                  phoneNumber: 'Phone Number',
                  city: 'City',
                  state: 'State',
                  country: 'Country',
                  speakerWebsite: 'Speaker Website',
                  linkedin: 'LinkedIn',
                  companyName: 'Company Name',
                  title: 'Title',
                  bio: 'Bio',
                  headshot: 'Headshot',
                  learningObjectives: 'Learning Objectives',
                  takeaways: 'Takeaways',
                  challenges: 'Challenges',
                  preferredGeography: 'Preferred Speaker Geography',
                  speakerCredentials: 'Speaker Credentials',
                  topKeywords: 'Top Keywords',
                  differentiators: 'Differentiators',
                  mbti: 'MBTI',
                  disc: 'DiSC',
                  bigFive: 'Big Five',
                  enneagram: 'Enneagram',
                  cliftonStrengths: 'CliftonStrengths',
                  via: 'VIA Character Strengths',
                  riasec: 'RIASEC',
                  voiceClip: 'Voice Clip',
                  videoClip: 'Video Clip',
                  fullBio: 'Full Bio',
                  logo: 'Logo',
                  additionalHeadshots: 'Additional Headshots',
                  oneSheetPdf: 'One-sheet PDF',
                  pastTalkDecks: 'Past Talk Decks',
                  sizzleReel: 'Sizzle Reel',
                  pressCoverageLinks: 'Press Coverage Links',
                  testimonials: 'Testimonials',
                  awardsRecognitions: 'Awards/Recognitions',
                  podcastAppearances: 'Podcast Appearances',
                  tedTalkLink: 'TED/TEDx Talk Link',
                  tedxTranscript: 'TEDx Transcript',
                  affiliateProgramParticipation: 'Affiliate Program Participation',
                  affiliateProgramDetails: 'Affiliate Program Details',
                  referNewAffiliates: 'Refer New Affiliates',
                  affiliateNotes: 'Affiliate Notes',
                  travelRider: 'Travel Rider',
                  liveSpeakingFee: 'Live Speaking Fee',
                  virtualSpeakingFee: 'Virtual Speaking Fee',
                  monthlyPaidSpeakingGoals: 'Monthly Paid Speaking Goals'
                };

                const criticalFields = ['name', 'email'];
                const scoringFields = ['mbti', 'disc', 'bigFive', 'enneagram', 'cliftonStrengths', 'via', 'riasec'];
                const marketingFields = ['voiceClip', 'videoClip', 'sizzleReel', 'testimonials', 'tedTalkLink'];

                return (
                  <>
                    {/* Score Display */}
                    <div className="bg-surface-elevated rounded-xl p-6 text-center mb-6">
                      <div className="text-4xl font-bold text-primary mb-2">{score}%</div>
                      <div className="text-foreground-muted">Profile Completion Score</div>
                      <div className="text-sm text-foreground-muted mt-1">
                        {completedFields} of {totalFields} fields completed
                      </div>
                    </div>

                    {/* Incomplete Fields */}
                    {incompleteFields.length > 0 && (
                      <div className="space-y-4">
                        <h4 className="text-lg font-semibold text-foreground">Incomplete Fields</h4>
                        
                        {/* Critical Fields */}
                        {(() => {
                          const incompleteCritical = incompleteFields.filter(([key]) => criticalFields.includes(key));
                          if (incompleteCritical.length === 0) return null;
                          
                          return (
                            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                              <h5 className="font-medium text-red-800 mb-2 flex items-center">
                                <AlertCircle className="h-4 w-4 mr-2" />
                                Critical Fields (Required for matching)
                              </h5>
                              <ul className="text-sm text-red-700 space-y-1">
                                {incompleteCritical.map(([key]) => (
                                  <li key={key}>• {fieldLabels[key] || key}</li>
                                ))}
                              </ul>
                            </div>
                          );
                        })()}

                        {/* Scoring Fields */}
                        {(() => {
                          const incompleteScoring = incompleteFields.filter(([key]) => scoringFields.includes(key));
                          if (incompleteScoring.length === 0) return null;
                          
                          return (
                            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                              <h5 className="font-medium text-orange-800 mb-2">
                                Psychometric Assessments (Improves AI matching accuracy)
                              </h5>
                              <ul className="text-sm text-orange-700 space-y-1">
                                {incompleteScoring.map(([key]) => (
                                  <li key={key}>• {fieldLabels[key] || key}</li>
                                ))}
                              </ul>
                            </div>
                          );
                        })()}

                        {/* Marketing Fields */}
                        {(() => {
                          const incompleteMarketing = incompleteFields.filter(([key]) => marketingFields.includes(key));
                          if (incompleteMarketing.length === 0) return null;
                          
                          return (
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                              <h5 className="font-medium text-blue-800 mb-2">
                                Marketing Assets (These assets help us capture your tone, cadence, and style so we can lock in your Digital Voice Twin)
                              </h5>
                              <ul className="text-sm text-blue-700 space-y-1">
                                {incompleteMarketing.map(([key]) => (
                                  <li key={key}>• {fieldLabels[key] || key}</li>
                                ))}
                              </ul>
                            </div>
                          );
                        })()}

                        {/* Other Fields */}
                        {(() => {
                          const otherIncomplete = incompleteFields.filter(([key]) => 
                            !criticalFields.includes(key) && 
                            !scoringFields.includes(key) && 
                            !marketingFields.includes(key)
                          );
                          if (otherIncomplete.length === 0) return null;
                          
                          return (
                            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                              <h5 className="font-medium text-gray-800 mb-2">
                                Additional Fields (Enhances profile completeness)
                              </h5>
                              <ul className="text-sm text-gray-700 space-y-1">
                                {otherIncomplete.slice(0, 8).map(([key]) => (
                                  <li key={key}>• {fieldLabels[key] || key}</li>
                                ))}
                                {otherIncomplete.length > 8 && (
                                  <li className="text-gray-500">... and {otherIncomplete.length - 8} more</li>
                                )}
                              </ul>
                            </div>
                          );
                        })()}
                      </div>
                    )}

                    {/* Completion Message */}
                    {score === 100 && (
                      <div className="bg-green-50 border border-green-200 rounded-lg p-6 text-center">
                        <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-3" />
                        <h4 className="text-lg font-semibold text-green-800 mb-2">Profile Complete!</h4>
                        <p className="text-green-700">
                          Congratulations! Your profile is 100% complete and optimized for the best matching opportunities.
                        </p>
                      </div>
                    )}
                  </>
                );
              })()}
            </div>
          </div>
        );
        
      default:
        return (
          <div className="text-center py-8">
            <p className="text-foreground-muted">
              Form fields for {steps[currentStep - 1]?.title} will be implemented soon.
            </p>
          </div>
        );
    }
  };

    // Show overview screen first
    if (showOverview) {
      return (
        <div className="min-h-screen flex items-center justify-center p-6 relative">
          {/* Dog Animation */}
          <DogAnimation 
            isTyping={isTyping}
            fieldCompleted={fieldCompleted}
            activeFieldPosition={activeFieldPosition}
          />
          
          {/* Overview Screen */}
          <div className="animate-fade-in max-w-2xl w-full text-center">
            <Card className="bg-surface border-border-subtle">
              <CardHeader className="pb-8">
                <CardTitle className="text-3xl font-bold text-foreground">Overview</CardTitle>
                <CardDescription className="text-lg text-foreground-muted mt-4">
                  Welcome to the Speaker Intake Process. Share your details to help us match you with the best opportunities — you can update anytime
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-0">
                <Button 
                  onClick={() => setShowOverview(false)}
                  className="bg-primary hover:bg-primary-hover hover-scale transition-all duration-200 text-lg px-8 py-3"
                >
                  Start Intake Form
                  <ArrowRight className="h-5 w-5 ml-2" />
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      );
    }

    return (
      <div className="space-y-6 relative">
        {/* Dog Animation */}
        <DogAnimation 
          isTyping={isTyping}
          fieldCompleted={fieldCompleted}
          activeFieldPosition={activeFieldPosition}
        />
        
        {/* Header Section */}
      <div className="animate-fade-in">
        <h2 className="text-3xl font-bold text-foreground">Speaker Intake Form</h2>
        <p className="text-foreground-muted mt-2">Complete your comprehensive speaker profile</p>
      </div>

      {/* Animated Stepper */}
      <div className="animate-fade-in">
        <AnimatedStepper 
          steps={steps}
          currentStep={currentStep}
          className="w-full mb-8"
          onStepClick={handleStepClick}
        />
        
        {/* Current Step Description */}
        <div className="text-center mt-6 animate-fade-in">
          <h3 className="text-xl font-bold text-foreground">{steps[currentStep - 1].title}</h3>
          <p className="text-sm text-foreground-muted mt-1">{steps[currentStep - 1].description}</p>
          <div className="text-xs text-foreground-muted mt-2">
            Step {currentStep} of {totalSteps}
          </div>
        </div>
      </div>

      {/* Form Steps */}
      <form onSubmit={handleSubmit} className="animate-fade-in">
        <div className="relative overflow-hidden">
          {steps.map((step, index) => (
            currentStep === step.id && (
              <div key={step.id} className="animate-fade-in-up">
                <Card className="bg-surface border-border-subtle transform transition-all duration-500 ease-out">
                  <CardHeader>
                    <CardTitle className="text-xl flex items-center gap-3">
                      {React.createElement(step.icon, { className: "h-6 w-6 text-primary animate-wiggle" })}
                      {step.title}
                    </CardTitle>
                    <CardDescription>
                      {step.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="animate-fade-in-up">
                      {renderStepContent()}
                    </div>
                  </CardContent>
                </Card>
              </div>
            )
          ))}
        </div>

        {/* Enhanced Navigation */}
        <div className="flex items-center justify-between pt-8 animate-fade-in">
          <div className="flex items-center gap-3">
            <AlertCircle className="h-5 w-5 text-primary" />
            <span className="text-foreground-muted">Fields marked with * are required</span>
          </div>
          
          <div className="flex items-center gap-4">
            {currentStep > 1 && (
              <Button 
                type="button" 
                variant="outline" 
                onClick={handlePrevious}
                className="hover-scale transition-all duration-200"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Previous
              </Button>
            )}
            
            {currentStep < totalSteps ? (
              <Button 
                type="button" 
                onClick={handleNext} 
                className="bg-primary hover:bg-primary-hover hover-scale transition-all duration-200"
              >
                Save and Next
                <ArrowRight className="h-4 w-4 ml-2" />
              </Button>
            ) : (
              <Button 
                type="submit" 
                className="bg-primary hover:bg-primary-hover hover-scale transition-all duration-200"
              >
                <CheckCircle className="h-4 w-4 mr-2" />
                Complete Profile
              </Button>
            )}
          </div>
        </div>
      </form>
    </div>
  );
}
