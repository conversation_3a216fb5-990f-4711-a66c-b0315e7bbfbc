"""
Audio Analysis Service
======================
Analyzes audio files to extract prosody metrics including:
- Pitch variance (fundamental frequency analysis)
- Tempo (speech rate)
- Inflection patterns

Uses librosa for audio processing and prosody analysis.
Best practices based on speech prosody research:
- PYIN algorithm for accurate F0 extraction in speech
- Onset detection for syllable/word boundary estimation
- RMS energy for speech activity detection
- Spectral features for tempo estimation

References:
- Librosa documentation for speech analysis
- Speech prosody analysis best practices
"""

import numpy as np
import librosa
import soundfile as sf  # Used by librosa for file I/O
from typing import Dict, Any, Optional
from app.config.logger import get_logger

logger = get_logger(__name__, file_name="voiceprint.log")

class AudioAnalysisService:
    """
    Service for analyzing audio files and extracting prosody metrics.
    - Uses librosa.pyin for F0 estimation (robust for speech)
    - Uses energy-based voiced segment detection for pause/speech stats
    - Estimates speech tempo via onset detection (syllable-like events)
    - Computes jitter/shimmer approximations
    """

    def __init__(self, sample_rate: int = 22050, frame_length: int = 2048, hop_length: int = 512):
        self.sample_rate = sample_rate
        self.frame_length = frame_length
        self.hop_length = hop_length
        # limits / thresholds
        self.min_duration_seconds = 0.5  # audio too short to analyze
        self.max_trim_seconds = 60.0     # optionally trim very long files to first N seconds

    def analyze_audio(self, audio_path: str, trim_to_seconds: Optional[float] = None) -> Dict[str, Any]:
        """
        Main entry. Returns a dictionary of metrics.

        :param audio_path: Path to .wav/.mp3 file
        :param trim_to_seconds: optional length to trim to (useful for long files)
        :return: metrics dict
        """
        logger.info("Starting analysis for: %s", audio_path)
        y, sr = self._load_audio(audio_path, trim_to_seconds or self.max_trim_seconds)

        if y is None or y.size == 0:
            raise ValueError("Loaded audio is empty or unreadable")

        duration = len(y) / sr
        if duration < self.min_duration_seconds:
            raise ValueError(f"Audio too short for analysis ({duration:.2f}s)")

        # voiced regions using energy-based VAD
        voiced_intervals = self._detect_voiced_intervals(y, sr)
        total_voiced_seconds = sum((end - start) for start, end in voiced_intervals)
        pause_seconds = duration - total_voiced_seconds
        pause_ratio = float(pause_seconds / duration) if duration > 0 else 0.0

        # pitch (F0) estimation using pyin
        f0, voiced_flag = self._extract_f0_pyin(y, sr)

        # filter F0 by voiced frames only
        f0_voiced = f0[~np.isnan(f0)]
        pitch_mean = float(np.nanmean(f0_voiced)) if f0_voiced.size else 0.0
        pitch_variance = float(np.nanvar(f0_voiced)) if f0_voiced.size else 0.0
        pitch_range = float(np.nanmax(f0_voiced) - np.nanmin(f0_voiced)) if f0_voiced.size else 0.0

        # pitch contour complexity & intonation variance
        pitch_contour_complexity = self._pitch_contour_complexity(f0_voiced)
        intonation_variance = pitch_variance / (pitch_mean**2) if pitch_mean > 0 else 0.0
        pitch_stability = 1.0 / (1.0 + intonation_variance) if intonation_variance >= 0 else 1.0

        # speech tempo / speech rate proxy: onset events per minute (syllable-like proxy)
        speech_events = self._count_speech_onsets(y, sr)
        speech_rate_wpm = float((speech_events / duration) * 60.0 / 1.7) if duration > 0 else 0.0
        # 1.7 onsets ~ 1 word (empirical proxy). You can tune this or replace with ASR for exact WPM.
        speech_rate_spm = speech_rate_wpm * 1.3  # approximate syllables per word

        metrics = {
            "pitch_variance": float(pitch_variance),
            "pitch_mean": float(pitch_mean),
            "pitch_range": float(pitch_range),
            "tempo": {
                "speech_rate_wpm": float(round(speech_rate_wpm, 2)),
                "speech_rate_spm": float(round(speech_rate_spm, 2)),
                "pause_ratio": float(round(pause_ratio, 3)),
                "speech_events_per_min": float(round((speech_events / duration) * 60.0 if duration > 0 else 0.0, 2))
            },
            "inflection": {
                "pitch_contour_complexity": float(round(pitch_contour_complexity, 4)),
                "intonation_variance": float(round(min(1.0, intonation_variance), 4)),
                "pitch_stability": float(round(pitch_stability, 4)),
            },
        }

        return metrics

    # -------------------------------
    # Helper methods
    # -------------------------------
    def _load_audio(self, path: str, trim_to_seconds: Optional[float] = None) -> tuple[np.ndarray, int]:
        """
        Loads audio using librosa. Optionally trims to first N seconds for performance.
        """
        try:
            if trim_to_seconds is not None and trim_to_seconds > 0:
                y, sr = librosa.load(path, sr=self.sample_rate, mono=True, duration=trim_to_seconds)
            else:
                y, sr = librosa.load(path, sr=self.sample_rate, mono=True)
            return y, sr
        except Exception as e:
            logger.error("Error loading audio: %s", e, exc_info=True)
            raise

    def _extract_f0_pyin(self, y: np.ndarray, sr: int) -> tuple[np.ndarray, np.ndarray]:
        """
        Use librosa.pyin to extract f0 contour and a voiced flag per frame.
        Returns f0 array (Hz) with np.nan for unvoiced frames, and voiced_flag boolean array.
        """
        try:
            fmin = librosa.note_to_hz('C2')  # ~65 Hz
            fmax = librosa.note_to_hz('C7')  # ~2093 Hz
            f0, voiced_flag, voiced_probabilities = librosa.pyin(
                y,
                fmin=fmin,
                fmax=fmax,
                sr=sr,
                frame_length=self.frame_length,
                hop_length=self.hop_length
            )
            # f0: np.ndarray shape (n_frames,) with np.nan for unvoiced
            return f0, voiced_flag
        except Exception as e:
            logger.warning("pyin F0 extraction failed, falling back to piptrack: %s", e)
            # fallback piptrack (less ideal)
            pitches, magnitudes = librosa.piptrack(y=y, sr=sr, n_fft=self.frame_length, hop_length=self.hop_length)
            # pick max magnitude per frame
            f0s = []
            voiced = []
            for t in range(pitches.shape[1]):
                idx = magnitudes[:, t].argmax()
                p = pitches[idx, t]
                if p > 0:
                    f0s.append(p)
                    voiced.append(True)
                else:
                    f0s.append(np.nan)
                    voiced.append(False)
            return np.array(f0s), np.array(voiced, dtype=bool)

    def _detect_voiced_intervals(self, y: np.ndarray, sr: int) -> tuple[tuple[float, float], ...]:
        """
        Detect voiced intervals (non-silent regions) using librosa.effects.split.
        Returns list of (start_seconds, end_seconds).
        """
        # top_db tuned slightly conservatively (lower => more silence)
        intervals = librosa.effects.split(y, top_db=30, frame_length=self.frame_length, hop_length=self.hop_length)
        # convert frames to seconds
        result = [(start / sr, end / sr) for start, end in intervals]
        return result

    def _count_speech_onsets(self, y: np.ndarray, sr: int) -> int:
        """
        Count spectral onsets as proxy for syllable-like events.
        Use aggressive pre-filtering to reduce false positives.
        """
        # high-pass filter to remove low-frequency hum (librosa doesn't have simple builtin hp; use simple diff)
        # we will use onset_strength with tuned hop_length
        onset_env = librosa.onset.onset_strength(y=y, sr=sr, hop_length=self.hop_length)
        onsets = librosa.onset.onset_detect(onset_envelope=onset_env, sr=sr, hop_length=self.hop_length, backtrack=False)
        return int(len(onsets))

    def _pitch_contour_complexity(self, f0_voiced: np.ndarray) -> float:
        """
        Complexity: normalized std of consecutive pitch changes.
        Returns 0..1 (clipped).
        """
        if f0_voiced is None or f0_voiced.size < 2:
            return 0.0
        pitch_changes = np.diff(f0_voiced)
        complexity = float(np.std(pitch_changes))
        # normalize by typical pitch range (200 Hz) to map to 0..1
        normalized = min(1.0, complexity / 200.0)
        return normalized