variables:
  DEPLOY_HOST: ${DEV_SCRAPPER_IP_ADDRESS}
  EC2_USER: "ec2-user"
  SERVER_PATH: "/home/<USER>/dev-speakerbot-scrapper"
  S3_BUCKET: ${DEV_S3_BUCKET}
  SOURCE_DIR: "admin/python"
  SSH_PRIVATE_KEY: ${DEV_SCRAPPER_EC2_SSH_PRIVATE_KEY}

workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      changes:
        - admin/python/**/*
    - if: '$CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"'
      changes:
        - admin/python/**/*

deploy_to_ec2:
  stage: deploy
  image: amazon/aws-cli:2.15.39

  before_script:
    - apk add --no-cache openssh zip
    - mkdir -p ~/.ssh
    - echo "$SSH_PRIVATE_KEY" > ~/.ssh/id_ed25519
    - chmod 600 ~/.ssh/id_ed25519
    - ssh-keyscan -H "$DEPLOY_HOST" >> ~/.ssh/known_hosts

  script:
    - echo "📦 Preparing Python build from $SOURCE_DIR..."
    - cd $SOURCE_DIR
    - export VERSION_TAG="$(date +%Y%m%d%H%M%S)-$CI_COMMIT_SHORT_SHA"
    - export APP_ZIP="dev-speakerbot-scrapper-${VERSION_TAG}.zip"

    # Zip project
    - zip -r $APP_ZIP . -x "*.git*" "__pycache__/*" "*.venv*" "*.DS_Store"
    - echo "☁️ Uploading $APP_ZIP to S3..."
    - aws s3 cp $APP_ZIP s3://$S3_BUCKET/$APP_ZIP
    - echo "✅ Uploaded $APP_ZIP successfully!"

    # Deploy to EC2
    - echo "🚀 Deploying version $APP_ZIP to EC2..."
    - ssh $EC2_USER@$DEPLOY_HOST "
        cd /tmp &&
        aws s3 cp s3://$S3_BUCKET/$APP_ZIP . &&
        unzip -o $APP_ZIP -d $SERVER_PATH &&
        rm -f $APP_ZIP &&
        cd $SERVER_PATH &&
        source venv/bin/activate &&
        pip install -r requirements.txt &&
        sudo systemctl restart dev-speakerbot-scrapper
      "
    - echo "✅ Deployment of $APP_ZIP completed successfully!"
