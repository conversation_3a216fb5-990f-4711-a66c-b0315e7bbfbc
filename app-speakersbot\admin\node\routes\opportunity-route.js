
const opportunityController = require("../controllers/opportunity-controller");

const upload = require("../middlewares/upload-middleware");
const router = require("express").Router();

module.exports = (app) => {

    // ------------------------- opportunity -------------------------

    // get distinct event types
    router.get("/opportunity/event-types", opportunityController.getDistinctEventTypes);


    // generate sample csv
    router.get("/opportunity/sample-csv", opportunityController.generateSampleCSV);

    // get all opportunities
    router.get("/opportunities", opportunityController.getAllOpportunities);

    // get all distinct city
    router.get("/opportunity/cities", opportunityController.getDistinctCities);

    // create new opportunity
    router.post("/opportunity", opportunityController.createOpportunity);

    // export opportunities to csv
    router.post("/export/opportunities", opportunityController.exportOpportunities);

    // import opportunities from csv file
    router.post("/import/opportunities", upload.single('csvFile'), opportunityController.importOpportunities);

    // get speakers for specific opportunity
    router.get("/opportunity/speakers/:id", opportunityController.getOpportunitySpeakers);

    // delete opportunity by id
    router.delete("/opportunity/:id", opportunityController.deleteOpportunity);

    // update opportunity by id
    router.put("/opportunity/:id", opportunityController.updateOpportunity);

    // get opportunity by id
    router.get("/opportunity/:id", opportunityController.getOpportunityById);


    app.use("/admin/api/v1", router);
};