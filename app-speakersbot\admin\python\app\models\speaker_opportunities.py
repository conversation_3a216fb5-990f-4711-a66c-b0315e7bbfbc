from app.config.config import config
from sqlalchemy import create_engine, Column, Integer, String, Float, Text, DateTime, Boolean, Enum, JSON
from app.models.base import Base

class SpeakerOpportunity(Base):
    __tablename__ = "speaker_opportunities"
    id = Column(Integer, primary_key=True, autoincrement=True, comment="Primary key for the speaker opportunity record")
    speaker_id = Column(Integer, nullable=False, comment="Foreign key referencing the speaker")
    opportunity_id = Column(Integer, nullable=False, comment="Foreign key referencing the opportunity")
    speaker_email = Column(String(255), nullable=True, comment="Email address of the speaker")
    overall_score = Column(Float, nullable=True, comment="Overall match score for the speaker and opportunity")
    topic_score = Column(Float, nullable=True, comment="Score for topic relevance")
    primary_category_score = Column(Float, nullable=True, comment="Score for primary category match")
    subcategory_score = Column(Float, nullable=True, comment="Score for secondary category match")
    geo_location_score = Column(Float, nullable=True, comment="Score for geographic/location match")
    match_strength = Column(Text, nullable=True, comment="Strengths in the speaker-opportunity match")
    match_concern = Column(Text, nullable=True, comment="Concerns in the speaker-opportunity match")
    recommendation = Column(Text, nullable=True, comment="Recommendation for the speaker regarding the opportunity")
    application_submitted = Column(Boolean, default=False, comment="Indicates if the application has been submitted")
    match_considerations = Column(Text, nullable=True, comment="Considerations for the match")
    ai_match = Column(Boolean, default=False, comment="Indicates if AI matching was used")
    speaker_dsa_url = Column(String(500), nullable=True, comment="URL for the speaker's DSA profile")
    speaker_prefill_data = Column(JSON, nullable=True, comment="Pre-filled data for the speaker's application")
    status = Column(Enum('pending', 'accepted', 'rejected', 'interested', 'applied', 'booked', 'interviewing'), default="pending", comment="Status of the speaker opportunity")
    reasons = Column(Text, nullable=True, comment="Reasons for the current status")
    created_at = Column(DateTime, nullable=False, comment="Record creation timestamp")
    updated_at = Column(DateTime, nullable=False, comment="Record last update timestamp")
    deleted_at = Column(DateTime, nullable=True, comment="Record deletion timestamp(soft delete)")


def create_speaker_opportunities_table():
    db_url = config.get_database_url()
    engine = create_engine(db_url)
    Base.metadata.create_all(engine)

create_speaker_table = create_speaker_opportunities_table

if __name__ == "__main__":
    create_speaker_opportunities_table()
    print("Table 'speaker_opportunities' created in MySQL database")
