import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "@/components/ui/sidebar";
import { CreditCard, FileText, Trophy, UserPlus, Zap } from "lucide-react";
import logoLight from "/logo-light.png";
import { NavLink } from "react-router-dom";

interface AppSidebarProps {
  activeSection?: string;
  onSectionChange?: (section: string) => void;
}

const menuItems = [
  { id: "today", title: "Dashboard", icon: Zap, to: "/speaker/dashboard" },
  { id: "intake", title: "Intake Form", icon: FileText, to: "/speaker/intake" },
  { id: "subscription", title: "Subscription", icon: CreditCard, to: "/speaker/subscription" },
  { id: "gamification", title: "XP Points", icon: Trophy, to: "/speaker/xp" },
  { id: "invite", title: "Invite", icon: UserP<PERSON>, to: "/speaker/invite" },
];

export function AppSidebar({ activeSection, onSectionChange }: AppSidebarProps) {
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";

  return (
    <Sidebar collapsible="icon" className="bg-surface">
      <SidebarHeader className="px-[15px]">
        <div className="flex items-center gap-3 mt-8 mb-2">
          {isCollapsed ? (
            <div className="p-2 bg-primary rounded-lg shadow-glow w-full text-center">
              SB
            </div>
          ) : (
            <img src={logoLight} alt="SpeakerBot" className="h-[50px] w-full" />
          )}
        </div>
      </SidebarHeader>

      <SidebarContent className="px-3">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="gap-2">
              {menuItems.map((item) => {
                const Icon = item.icon;
                const isActive = activeSection === item.id;

                return (
                  <SidebarMenuItem
                    key={item.id}
                    className={isCollapsed ? "w-10" : ""}
                  >
                    <NavLink to={item.to!} className={({ isActive: isNavActive }) => `
                        block rounded-lg ${isCollapsed ? "" : "px-3"}
                        ${
                          isNavActive
                            ? "bg-gradient-tertiary text-tertiary-foreground hover:bg-primary hover:text-tertiary-foreground shadow-sm"
                            : "hover:bg-muted text-muted-foreground hover:text-foreground"
                        }
                      `}>
                      <SidebarMenuButton
                        asChild
                        isActive={isActive}
                        tooltip={isCollapsed ? item.title : undefined}
                        size="lg"
                        className={`h-12 text-base font-medium transition-all duration-200 ${isCollapsed ? "justify-center" : ""}`}
                      >
                        <div className="flex items-center">
                          <Icon className="h-5 w-5" />
                          {!isCollapsed && <span className="ml-3">{item.title}</span>}
                        </div>
                      </SidebarMenuButton>
                    </NavLink>
                  </SidebarMenuItem>
                );
              })}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  );
}
