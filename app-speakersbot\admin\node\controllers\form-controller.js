const ApiResponse = require("../helpers/api-response");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const formService = require("../services/form-service");

/**
 * Create new form with questions
 */
exports.createForm = async (req, res, next) => {
  try {
    const { title, questions } = req.body;

    const createdForm = await formService.createFormWithQuestions(title, questions);

    res
      .status(RESPONSE_CODES.ACCEPTED)
      .json(
        ApiResponse({
          data: { id: createdForm.id, title: createdForm.title, priority: createdForm.priority, is_deletable: createdForm.is_deletable },
          message: "Form created successfully",
        })
      );
  } catch (err) {
    next(err);
  }
};

/**
 * Get all forms with priorities
 */
exports.getFormswithPriorities = async (req, res, next) => {
  try {
    const formsData = await formService.getFormswithPriorities();

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(ApiResponse({ data: formsData, message: "Forms fetched successfully" }));
  } catch (err) {
    next(err);
  }
};

/**
 * Update form (title)
 */
exports.updateForm = async (req, res, next) => {
  try {
    const { id } = req.params
    const { title } = req.body;

    if (!id && !title) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "form-id or title is required"
      );
    }

    const updatedForm = await formService.updateForm(id, title);

    const structuredForm = {
      id: updatedForm.id,
      title: updatedForm.title,
      priority: updatedForm.priority,
      is_deletable: updatedForm.is_deletable,
    }
    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(
        ApiResponse({ data: structuredForm, message: "Form updated successfully" })
      );
  } catch (err) {
    next(err);
  }
};

/**
 * Update form priority
 */

exports.updateFormPriority = async (req, res, next) => {
  try {
    const { priorities } = req.body;

    if (!Array.isArray(priorities) || priorities.length === 0) {
      throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Priorities array required");
    }

    const updatedForms = await formService.updatePriorities(priorities);

    const structuredUpdated = updatedForms.map((p) => ({
      id: p.id,
      title: p.title,
      priority: p.priority,
      is_deletable: p.is_deletable,
    }));

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(ApiResponse({
        data: structuredUpdated,
        message: "Form priorities updated successfully"
      }));
  } catch (err) {
    next(err)
  }
}

/**
 * Delete form by form id
 */
exports.deleteForm = async (req, res, next) => {
  try {
    const { id } = req.params;
    if (!id)
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Required parameters are missing"
      );

    await formService.deleteForm(id);

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(ApiResponse({ message: "Form deleted successfully" }));
  } catch (err) {
    next(err);
  }
};

// ------------------------------------- form fields --------------------------------------------

/**
 * Get all fields/questions for a form
 */
exports.getFormFields = async (req, res, next) => {
  try {
    const { formId } = req.params;
    const formData = await formService.getFormAndQuestions(formId);
    if (!formData)
      throw new CustomError(
        RESPONSE_CODES.NOT_FOUND,
        "Form not found"
      );

    const fields = formService.structureQuestions(formData.questions || []);

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(
        ApiResponse({
          data: {
            id: formData.id,
            title: formData.title,
            priority: formData.priority,
            is_deletable: formData.is_deletable,
            questions: fields
          },
          message: "Fields fetched successfully"
        })
      );
  } catch (err) {
    next(err);
  }
};

/**
 * Create new question/field for a form
 */
exports.createFormField = async (req, res, next) => {
  try {
    const { formId } = req.params;
    const { fieldData } = req.body;

    const createdField = await formService.createFormField(formId, fieldData);

    const reponseData = {
      id: createdField.id,
      question: createdField.question,
      placeholder: createdField.placeholder,
      field_type: createdField.field_type,
      field_id: createdField.field_id,
      is_required: createdField.is_required,
      options: createdField.options_json ? JSON.parse(createdField.options_json) : null,
      is_deletable: createdField.is_deletable,
    }

    res
      .status(RESPONSE_CODES.ACCEPTED)
      .json(
        ApiResponse({ data: reponseData, message: "Field created successfully" })
      );
  } catch (err) {
    next(err);
  }
};

/**
 * Update a field/question
 */
exports.updateFormField = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { fieldData } = req.body;

    const updatedField = await formService.updateFormField(id, fieldData);

    const structuredFields = formService.structureQuestions([updatedField]);

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(
        ApiResponse({ data: structuredFields, message: "Field updated successfully" })
      );
  } catch (err) {
    next(err);
  }
};

/**
 * Delete form field
 */
exports.deleteFormField = async (req, res, next) => {
  try {
    const { id } = req.params;
    if (!id)
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Required parameters are missing"
      );

    await formService.deleteFormField(id);

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(ApiResponse({ message: "Form field deleted successfully" }));
  } catch (err) {
    next(err);
  }
};