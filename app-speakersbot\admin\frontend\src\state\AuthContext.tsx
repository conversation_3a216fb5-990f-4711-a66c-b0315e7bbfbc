import React, { createContext, useContext } from 'react';
import { useAppDispatch, useAppSelector } from '../store/hooks';
import { setCredentials, logout as logoutAction } from '../store/slices/authSlice';
import { storage } from '../utils/storage';
import { generateDemoUsers } from '../utils/demo-data';
import type { User } from '../types';
import type { RootState } from '../store';

interface AuthContextType {
  user: RootState['auth']['user'];
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const dispatch = useAppDispatch();
  const { user, isAuthenticated } = useAppSelector((state) => state.auth);

  // Ensure demo users exist
  const existingUsers = storage.get('USERS');
  if (!existingUsers) {
    storage.set('USERS', generateDemoUsers());
  }

  const login = async (email: string, password: string): Promise<boolean> => {
    const users = storage.get<User[]>('USERS') || [];
    const foundUser = users.find(u => u.email === email);
    if (foundUser && (foundUser as any).status === 'active') {
      // In this demo, create a fake token
      dispatch(setCredentials({ user: {
        id: foundUser.id,
        email: foundUser.email,
        name: foundUser.name,
        role: (foundUser as any).role || 'user',
        avatar: (foundUser as any).avatar,
        createdAt: (foundUser as any).createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      }, token: 'demo-token' }));
      return true;
    }
    return false;
  };

  const logout = () => {
    dispatch(logoutAction());
  };

  return (
    <AuthContext.Provider value={{ user, isAuthenticated, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};