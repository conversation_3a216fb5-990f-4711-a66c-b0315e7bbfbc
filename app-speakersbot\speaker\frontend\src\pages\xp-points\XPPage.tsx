import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Coins,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
} from "lucide-react";

export const XPPage = () => {
  const [selectedAffiliate, setSelectedAffiliate] = useState(null);
  const [meetingNote, setMeetingNote] = useState("");
  const [meetingDate, setMeetingDate] = useState("");
  const [meetingTime, setMeetingTime] = useState("");

  // Mock data for points and activities
  const pointsBalance = 2450;
  const pointsHistory = [
    {
      date: "2024-01-15",
      activity: "Speaking Opportunity Accepted",
      earned: 500,
      balance: 2450,
    },
    {
      date: "2024-01-12",
      activity: "Referral Bonus",
      earned: 150,
      balance: 1950,
    },
    {
      date: "2024-01-10",
      activity: "Profile Completion",
      earned: 100,
      balance: 1800,
    },
    {
      date: "2024-01-08",
      activity: "Affiliate Meeting - Sarah Chen",
      used: -300,
      balance: 1700,
    },
    {
      date: "2024-01-05",
      activity: "Workshop Attendance",
      earned: 200,
      balance: 2000,
    },
    {
      date: "2024-01-03",
      activity: "Community Engagement",
      earned: 75,
      balance: 1800,
    },
    {
      date: "2024-01-01",
      activity: "Monthly Bonus",
      earned: 250,
      balance: 1725,
    },
    {
      date: "2023-12-28",
      activity: "Speaking Event Completed",
      earned: 300,
      balance: 1475,
    },
    {
      date: "2023-12-25",
      activity: "Holiday Bonus",
      earned: 100,
      balance: 1175,
    },
    {
      date: "2023-12-22",
      activity: "Affiliate Meeting - Mike Rodriguez",
      used: -250,
      balance: 1075,
    },
    {
      date: "2023-12-20",
      activity: "Webinar Participation",
      earned: 150,
      balance: 1325,
    },
    {
      date: "2023-12-18",
      activity: "Content Creation",
      earned: 200,
      balance: 1175,
    },
    {
      date: "2023-12-15",
      activity: "Networking Event",
      earned: 125,
      balance: 975,
    },
    {
      date: "2023-12-12",
      activity: "Platform Tutorial Completion",
      earned: 50,
      balance: 850,
    },
    {
      date: "2023-12-10",
      activity: "First Speaking Opportunity",
      earned: 400,
      balance: 800,
    },
  ];

  // Mock affiliate partners data (removed rating)
  const affiliatePartners = [
    {
      id: 1,
      name: "Sarah Chen",
      avatar: "/placeholder.svg",
      expertise: "Tech Conference Speaking",
      cost: 300,
      description:
        "Expert in helping speakers land tech conference opportunities. Specializes in developer events and startup pitches.",
    },
    {
      id: 2,
      name: "Mike Rodriguez",
      avatar: "/placeholder.svg",
      expertise: "Corporate Training",
      cost: 250,
      description:
        "Corporate training specialist with connections to Fortune 500 companies. Focus on leadership and business development talks.",
    },
    {
      id: 3,
      name: "Alex Thompson",
      avatar: "/placeholder.svg",
      expertise: "Healthcare Speaking",
      cost: 400,
      description:
        "Medical conference specialist. Helps healthcare professionals secure speaking slots at medical conferences and symposiums.",
    },
    {
      id: 4,
      name: "Emma Wilson",
      avatar: "/placeholder.svg",
      expertise: "Startup Events",
      cost: 200,
      description:
        "Startup ecosystem expert. Connects speakers with pitch competitions, demo days, and entrepreneurship events.",
    },
  ];

  // Mock meeting requests data
  const meetingRequests = [
    {
      id: 1,
      affiliate: "Sarah Chen",
      date: "2024-01-20 2:00 PM",
      status: "approved",
      pointsUsed: 300,
      notes: "Looking for tech conference opportunities in Q2",
    },
    {
      id: 2,
      affiliate: "Mike Rodriguez",
      date: "2024-01-18 10:00 AM",
      status: "pending",
      pointsUsed: 250,
      notes: "Corporate training workshop opportunities",
    },
    {
      id: 3,
      affiliate: "Emma Wilson",
      date: "2024-01-15 3:30 PM",
      status: "completed",
      pointsUsed: 200,
      notes: "Startup pitch event guidance",
    },
    {
      id: 4,
      affiliate: "Alex Thompson",
      date: "2024-01-12 11:00 AM",
      status: "rescheduled",
      pointsUsed: 400,
      notes: "Medical conference speaking opportunities",
    },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved":
        return <CheckCircle className="h-4 w-4 text-success" />;
      case "pending":
        return <Clock className="h-4 w-4 text-warning" />;
      case "completed":
        return <CheckCircle className="h-4 w-4 text-primary" />;
      case "declined":
        return <XCircle className="h-4 w-4 text-destructive" />;
      case "rescheduled":
        return <AlertCircle className="h-4 w-4 text-warning" />;
      default:
        return <Clock className="h-4 w-4 text-foreground-muted" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved":
        return "bg-success/10 text-success border-success/20";
      case "pending":
        return "bg-warning/10 text-warning border-warning/20";
      case "completed":
        return "bg-primary/10 text-primary border-primary/20";
      case "declined":
        return "bg-destructive/10 text-destructive border-destructive/20";
      case "rescheduled":
        return "bg-warning/10 text-warning border-warning/20";
      default:
        return "bg-muted text-foreground-muted";
    }
  };

  const handleMeetingRequest = (affiliate: any) => {
    // Reset form
    setMeetingNote("");
    setMeetingDate("");
    setMeetingTime("");
    setSelectedAffiliate(null);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-foreground">XP Point</h2>
          <p className="text-foreground-muted mt-1">
            Manage your XP points and book sessions with expert affiliates
          </p>
        </div>
        <div className="flex items-center gap-2 px-4 py-2 bg-gradient-glow border border-primary/20 rounded-lg">
          <Coins className="h-5 w-5 text-primary" />
          <div className="text-right">
            <div className="text-2xl font-bold text-primary">
              {pointsBalance.toLocaleString()}
            </div>
            <div className="text-xs text-foreground-muted">XP Points</div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <Tabs defaultValue="xp-points" className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="xp-points">XP Points</TabsTrigger>
          <TabsTrigger value="affiliates">Affiliates</TabsTrigger>
        </TabsList>

        <TabsContent value="xp-points" className="space-y-6">
          {/* Points Log Table */}
          <Card className="bg-surface border-border-subtle">
            <CardHeader className="pb-6">
              <CardTitle className="text-xl">Points Log</CardTitle>
              <CardDescription>
                Complete history of your XP point transactions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Date</TableHead>
                    <TableHead>Activity</TableHead>
                    <TableHead>Points</TableHead>
                    <TableHead>Balance</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pointsHistory.map((transaction, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-foreground-muted" />
                          {transaction.date}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium">
                        {transaction.activity}
                      </TableCell>
                      <TableCell>
                        <div
                          className={`flex items-center gap-1 font-medium ${
                            transaction.earned
                              ? "text-success"
                              : "text-destructive"
                          }`}
                        >
                          <Coins className="h-4 w-4" />
                          {transaction.earned ? "+" : ""}
                          {transaction.earned || transaction.used}
                        </div>
                      </TableCell>
                      <TableCell className="font-medium text-foreground">
                        {transaction.balance.toLocaleString()}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="affiliates" className="space-y-6">
          {/* Affiliate Partners Grid - Full screen 2x2 */}
          <Card className="bg-surface border-border-subtle">
            <CardHeader className="pb-6">
              <CardTitle className="text-xl">Available Affiliates</CardTitle>
              <CardDescription>
                Book one-on-one sessions with expert affiliates
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {affiliatePartners.map((affiliate) => (
                  <Card
                    key={affiliate.id}
                    className="bg-surface-elevated border-border-subtle hover:border-primary/30 transition-colors"
                  >
                    <CardHeader className="pb-4">
                      <div className="flex items-center gap-3">
                        <Avatar className="h-12 w-12">
                          <AvatarImage
                            src={affiliate.avatar}
                            alt={affiliate.name}
                          />
                          <AvatarFallback className="bg-primary text-primary-foreground">
                            {affiliate.name
                              .split(" ")
                              .map((n) => n[0])
                              .join("")}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <CardTitle className="text-base">
                            {affiliate.name}
                          </CardTitle>
                          <p className="text-sm text-primary font-medium">
                            {affiliate.expertise}
                          </p>
                        </div>
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <p className="text-sm text-foreground-muted leading-relaxed">
                        {affiliate.description}
                      </p>

                      <div className="flex items-center justify-between">
                        <div className="flex-1" />
                        <Badge className="bg-primary/10 text-primary border-primary/20">
                          {affiliate.cost} points
                        </Badge>
                      </div>

                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            className="w-full"
                            onClick={() => setSelectedAffiliate(affiliate)}
                          >
                            Request Meeting
                          </Button>
                        </DialogTrigger>
                        <DialogContent className="sm:max-w-md">
                          <DialogHeader>
                            <DialogTitle>
                              Request Meeting with {affiliate.name}
                            </DialogTitle>
                            <DialogDescription>
                              Schedule a one-on-one session to discuss your
                              speaking opportunities
                            </DialogDescription>
                          </DialogHeader>

                          <div className="space-y-4 py-4">
                            <div className="flex items-center gap-3 p-3 bg-primary/10 rounded-lg border border-primary/20">
                              <Coins className="h-5 w-5 text-primary" />
                              <div>
                                <p className="text-sm font-medium text-foreground">
                                  Session Cost
                                </p>
                                <p className="text-sm text-foreground-muted">
                                  {affiliate.cost} points
                                </p>
                              </div>
                            </div>

                            <div className="grid grid-cols-2 gap-3">
                              <div>
                                <Label htmlFor="meeting-date">Date</Label>
                                <Input
                                  id="meeting-date"
                                  type="date"
                                  value={meetingDate}
                                  onChange={(e) =>
                                    setMeetingDate(e.target.value)
                                  }
                                  className="mt-1"
                                />
                              </div>
                              <div>
                                <Label htmlFor="meeting-time">Time</Label>
                                <Input
                                  id="meeting-time"
                                  type="time"
                                  value={meetingTime}
                                  onChange={(e) =>
                                    setMeetingTime(e.target.value)
                                  }
                                  className="mt-1"
                                />
                              </div>
                            </div>

                            <div>
                              <Label htmlFor="meeting-note">Agenda/Notes</Label>
                              <Textarea
                                id="meeting-note"
                                placeholder="What would you like to discuss? Include your goals, target events, or specific questions..."
                                value={meetingNote}
                                onChange={(e) => setMeetingNote(e.target.value)}
                                className="mt-1 min-h-[100px]"
                              />
                            </div>
                          </div>

                          <DialogFooter>
                            <Button
                              onClick={() => handleMeetingRequest(affiliate)}
                              className="w-full"
                              disabled={
                                !meetingDate ||
                                !meetingTime ||
                                !meetingNote.trim()
                              }
                            >
                              Confirm Request ({affiliate.cost} points)
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Meeting Requests */}
          <Card className="bg-surface border-border-subtle">
            <CardHeader className="pb-6">
              <CardTitle className="text-xl">Meeting Requests Log</CardTitle>
              <CardDescription>
                Track your scheduled and past sessions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Affiliate</TableHead>
                    <TableHead>Date & Time</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Points Used</TableHead>
                    <TableHead>Notes</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {meetingRequests.map((request) => (
                    <TableRow key={request.id}>
                      <TableCell className="font-medium">
                        {request.affiliate}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-foreground-muted" />
                          {request.date}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge
                          className={`capitalize ${getStatusColor(
                            request.status
                          )}`}
                        >
                          <div className="flex items-center gap-1">
                            {getStatusIcon(request.status)}
                            {request.status}
                          </div>
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Coins className="h-4 w-4 text-primary" />
                          {request.pointsUsed}
                        </div>
                      </TableCell>
                      <TableCell className="max-w-xs truncate text-foreground-muted">
                        {request.notes}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};
