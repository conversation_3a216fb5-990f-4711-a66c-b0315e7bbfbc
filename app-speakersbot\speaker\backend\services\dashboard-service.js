const { getPagingData, parsePagination } = require("../helpers/app-hepler");
const CustomError = require("../helpers/custome-error");
const { SpeakerOpportunity, SpeakerOpportunityHistory } = require("../models");
const opportunities = require("../models/schema/opportunities");

const dashboardService = {};
/**
* Get application
*/

dashboardService.getApplicationStats = async (req) => {
  try {
    const speakerId = req.userId;
    const { page, limit, offset } = parsePagination(req.query);

    const { count, rows: applications } =
      await SpeakerOpportunity.findAndCountAll({
        where: { speaker_id: speakerId },
        include: [
          {
            model: SpeakerOpportunityHistory,
            as: "history",
            attributes: ["status", "reason", "created_at"], // Select specific fields
            order: [["created_at", "DESC"]], // Order history by most recent first
          },
          {
            model: opportunities,
            as: "opportunity",
            attributes: [
              "title",
              "organization",
              "event_url",
              "source_url"
            ],
          },
        ],
        attributes: ["id", "status", "created_at"],
        distinct: true,
        limit,
        offset,
      });

    const pageData = getPagingData(count, limit, page);

    return {
      status: true,
      message: "Application stats fetched successfully",
      result: applications ,
      pagination:pageData
    };
  } catch (error) {
    console.error("Error in getApplicationStats:", error);
    return {
      status: false,

      message: "Failed to fetch application stats",
      error: error.message,
    };
  }
};

module.exports = dashboardService;