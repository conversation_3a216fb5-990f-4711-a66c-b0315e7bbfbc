// Core entity types for the Speaker Bot Platform

export type UserRole = 'super_admin' | 'system_admin' | 'affiliate';

export type MatchStatus = 'pending' | 'interested' | 'accepted' | 'rejected';

export type OpportunityStatus = 'draft' | 'active' | 'paused' | 'completed' | 'cancelled';

export type OpportunityCategory = 'conference' | 'podcast' | 'webinar' | 'workshop' | 'panel' | 'keynote' | 'other';

export type SubscriptionPlan = 'free' | 'basic' | 'premium' | 'enterprise';

export type ActivityStatus = 'active' | 'inactive' | 'suspended';

export type ScrapingStatus = 'success' | 'error' | 'running' | 'paused';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  roleId: number;
  status: ActivityStatus;
  moduleAccess?: string[];
  createdAt: string;
  updatedAt: string;
  phone: string;
  legalBusinessName: string;
  website: string;
  commissionType?: "fixed" | "percentage";
  commissionValue?: number;
}

// Detailed Speaker Intake Data Interface
export interface SpeakerIntakeData {
  // Personal Info
  phoneNumber?: string;
  city?: string;
  state?: string;
  linkedin?: string;
  speakerWebsite?: string;
  company?: string;
  title?: string;
  headshot?: string;
  bio?: string;

  // Professional Info
  preferredGeography?: string[];
  primaryExpertise?: string;
  secondaryExpertise?: string;
  otherExpertise?: string[];
  differentiators?: string[];
  yearsOfExperience?: number;
  credentials?: string[];
  expertiseLevel?: 'beginner' | 'intermediate' | 'expert' | 'thought-leader';
  topicArea?: string[];
  specificClients?: string[];

  // Speaking Content
  keynote1Title?: string;
  keynote1LearningObjectives?: string;
  keynote1Takeaways?: string;
  keynote2Title?: string;
  keynote2LearningObjectives?: string;
  keynote2Takeaways?: string;
  keynote3Title?: string;
  keynote3LearningObjectives?: string;
  keynote3Takeaways?: string;
  keynoteTranscripts?: string[];
  workshop1Title?: string;
  workshop1LearningObjectives?: string;
  workshop1Takeaways?: string;
  workshop2Title?: string;
  workshop2LearningObjectives?: string;
  workshop2Takeaways?: string;
  workshop3Title?: string;
  workshop3LearningObjectives?: string;
  workshop3Takeaways?: string;
  workshopTranscripts?: string[];
  interactiveActivities?: string[];

  // Authority & Branding
  tedTedxStatus?: 'none' | 'ted' | 'tedx' | 'both';
  tedxTitles?: string[];
  tedxTranscripts?: string[];
  authorityStages?: Array<{ title: string; transcript: string }>;
  speakerBrand?: string;
  painPoints?: string[];

  // Speaking Engagements
  engagement1OrgName?: string;
  engagement1ContactName?: string;
  engagement1ContactPhone?: string;
  engagement1ContactEmail?: string;
  engagement2OrgName?: string;
  engagement2ContactName?: string;
  engagement2ContactPhone?: string;
  engagement2ContactEmail?: string;

  // Preferences & Logistics
  preferredTalkFormats?: string[];
  preferredAudience?: string[];
  preferredSessionDuration?: string[];
  willingToTravel?: boolean;
  travelRider?: string;
  audioVisualRider?: string;

  // Fees & Goals
  monthlyPaidSpeakingGoals?: number;
  paidLeadPlatformsUsed?: string[];
  liveSpeakingFee?: number;
  virtualSpeakingFee?: number;

  // Testimonials
  testimonials?: Array<{ content: string; author: string; organization?: string }>;

  // Other Data
  affiliate?: string;
  searchGigs?: boolean;
  top10Words?: string[];
  top3Outcomes?: string[];
}

export interface Speaker {
  id: string;
  name: string;
  email: string;
  subscriptionPlan: SubscriptionPlan;
  activityStatus: ActivityStatus;
  gamificationPoints: number;
  intakeData: SpeakerIntakeData;
  matchingScore: number;
  createdAt: string;
  updatedAt: string;
}

export interface Opportunity {
  id: string;
  title: string;
  description: string;
  organization: string;
  category: OpportunityCategory;
  status: OpportunityStatus;
  budget: number;
  location: string;
  eventDate: string;
  deadline: string;
  requirements: string[];
  tags: string[];
  externalUrl?: string;
  eventType?: string;
  notes: Note[];
  matchedCount: number;
  interestedCount: number;
  acceptedCount: number;
  rejectedCount: number;
  createdAt: string;
  updatedAt: string;
}

export interface Match {
  id: string;
  speakerId: string;
  opportunityId: string;
  status: MatchStatus;
  matchingScore: number;
  reason?: string;
  rejectionReason?: string;
  createdAt: string;
  updatedAt: string;
  proposalSummary?: string;
  keyThemes?: string[];
  matchStrengths?: string[];
  matchConcerns?: string[];
}

export interface Note {
  id: string;
  content: string;
  author: string;
  timestamp: string;
}

export interface IntakeFormField {
  id: string;
  name: string;
  label: string;
  type: 'text' | 'textarea' | 'select' | 'multiselect' | 'url' | 'email' | 'number' | 'upload' | 'switch' | 'dynamic-list';
  options?: string[];
  required: boolean;
  order: number;
  section?: string;
  placeholder?: string;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
  };
}

export interface IntakeFormVersion {
  id: string;
  version: number;
  fields: IntakeFormField[];
  isActive: boolean;
  createdAt: string;
  author: string;
}

export interface ScrapingLog {
  id: string;
  started_at: string;
  ended_at?: string;
  status: ScrapingStatus;
  topic: string;
  item_count: number;
  reason?: string;
}

export interface ScrapingTopic {
  id: string;
  title: string;
  keywords: string[];
  isActive: boolean;
  created_at: string;
}

export interface Affiliate {
  id: string;
  name: string;
  email: string;
  affiliateId: string;
  link: string;
  clicks: number;
  signups: number;
  conversions: number;
  createdAt: string;
  status: boolean;
}

export interface Invite {
  id: string;
  inviterEmail: string;
  inviteeEmail: string;
  status: 'pending' | 'accepted' | 'expired';
  sentAt: string;
}

export interface ActivityLogEntry {
  id: string;
  action: string;
  details: string;
  timestamp: string;
  userId: string;
}

export interface PricingPlan {
  id: number;
  name: string;
  billing_interval: string;
  description: string;
  features?: string[];
  currency: string;
  amount: string;
  stripe_price_id: string;
  stripe_product_id: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
}

export interface PaymentSettings {
  gateway: 'stripe' | 'paypal';
  publicKey: string;
  webhookUrl: string;
}

export interface AppSettings {
  payment: PaymentSettings;
  scrapingEnabled: boolean;
}

// State shapes for reducers
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
}

export interface SpeakersState {
  speakers: Speaker[];
  loading: boolean;
}

export interface OpportunitiesState {
  opportunities: Opportunity[];
  loading: boolean;
}

export interface MatchesState {
  matches: Match[];
  loading: boolean;
}

export interface UsersState {
  users: User[];
  loading: boolean;
}

export interface IntakeFormState {
  versions: IntakeFormVersion[];
  activeVersion: IntakeFormVersion | null;
  loading: boolean;
}

export interface ScrapingState {
  logs: ScrapingLog[];
  topics: ScrapingTopic[];
  isRunning: boolean;
  loading: boolean;
}

export interface AffiliatesState {
  affiliates: Affiliate[];
  loading: boolean;
}

export interface SettingsState {
  settings: AppSettings;
  invites: Invite[];
  loading: boolean;
}

export interface UIState {
  sidebarCollapsed: boolean;
  dateRange: [string, string] | null;
  theme: 'light' | 'dark';
}

// Chart data types
export interface ChartDataPoint {
  date: string;
  value: number;
  category: string;
}

export interface KPIData {
  totalSpeakers: number;
  activeSpeakers: number;
  totalOpportunities: number;
  accepted: number;
  rejected: number;
  interested: number;
}

// Form Autofill / Submission Tracker Types
export interface SubmissionLog {
  id: string;
  speakerId: string;
  opportunityId: string;
  sourceUrl: string;
  attemptTime: string;
  duration: number; // in seconds
  result: 'success' | 'failed' | 'timeout';
  errorType?: 'captcha' | 'js-heavy' | '404' | 'validation' | 'timeout' | 'other';
  extensionVersion: string;
  browserInfo: string;
  chromeOS: string;
  notes?: string;
  investigated: boolean;
}

export interface AutofillMetrics {
  successRate: number;
  avgTimeToSubmit: number;
  totalAttempts: number;
  successfulSubmissions: number;
  errorDistribution: { [key: string]: number };
}

// Enhanced Affiliate Types
export interface AffiliateMetrics extends Affiliate {
  revenue: number;
  conversionRate: number;
  clickThroughRate: number;
  activeCampaigns: string[];
  payoutStatus: 'pending' | 'paid' | 'processing';
  lastPayoutDate?: string;
  totalPayout: number;
}

export interface AffiliateCommission {
  id: string;
  affiliateId: string;
  period: string;
  amount: number;
  status: 'pending' | 'paid' | 'processing';
  payoutDate?: string;
  description: string;
}

// Revenue & Subscription Types
export interface RevenueMetrics {
  totalMRR: number;
  newMRR: number;
  expansionMRR: number;
  churnedMRR: number;
  netMRRGrowth: number;
  totalARR: number;
}

export interface SubscriptionEvent {
  id: string;
  userId: string;
  eventType: 'trial_started' | 'trial_converted' | 'subscription_created' | 'subscription_upgraded' | 'subscription_cancelled' | 'subscription_renewed';
  planId: string;
  amount: number;
  timestamp: string;
  metadata?: Record<string, any>;
}

export interface ChurnData {
  userId: string;
  planId: string;
  churnDate: string;
  churnReason?: string;
  mrr: number;
}

// Admin Actions & Moderation Types
export interface FlaggedItem {
  id: string;
  itemType: 'speaker' | 'opportunity' | 'submission' | 'affiliate';
  itemId: string;
  flagType: 'spam' | 'duplicate' | 'inappropriate' | 'quality' | 'fraud';
  flaggedBy: string;
  flaggedAt: string;
  status: 'pending' | 'approved' | 'rejected' | 'banned';
  adminNote?: string;
  resolvedBy?: string;
  resolvedAt?: string;
}

export interface ModerationAction {
  id: string;
  actionType: 'approve' | 'reject' | 'ban' | 'assign_manual' | 'flag_review';
  targetType: 'speaker' | 'opportunity' | 'submission' | 'affiliate';
  targetId: string;
  adminId: string;
  reason: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

// Feedback & User Behavior Types
export interface UserFeedback {
  id: string;
  userId: string;
  feedbackType: 'why_not_apply' | 'post_application' | 'feature_request' | 'general';
  tags: string[];
  rating?: 1 | 2 | 3 | 4 | 5;
  comment?: string;
  timestamp: string;
  status: 'new' | 'reviewed' | 'addressed';
}

export interface FeatureRequest {
  id: string;
  title: string;
  description: string;
  requestedBy: string;
  votes: number;
  status: 'pending' | 'in_progress' | 'completed' | 'rejected';
  priority: 'low' | 'medium' | 'high';
  timestamp: string;
}

export interface SessionData {
  id: string;
  userId: string;
  sessionStart: string;
  sessionEnd?: string;
  pageViews: number;
  clicks: number;
  rageClicks: number;
  formSubmissions: number;
  errors: number;
}

// Category Expansion Types
export interface OtherFieldAnalysis {
  fieldId: string;
  fieldName: string;
  totalResponses: number;
  otherUsage: number;
  otherPercentage: number;
  commonOtherValues: { value: string; count: number; }[];
  suggestedCategories: string[];
}

export interface CategorySuggestion {
  id: string;
  fieldId: string;
  suggestedName: string;
  mergeTargets: string[];
  proposedBy: string;
  timestamp: string;
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
}

// System Health & Bot Monitoring Types
export interface BotStatus {
  id: string;
  botName: string;
  engine: 'Playwright' | 'Puppeteer' | 'Scrapy' | 'Selenium';
  lastRunAt?: string;
  lastDuration?: number;
  successRate24h: number;
  lastStatus: 'success' | 'failed' | 'running' | 'stopped';
  errorMessage?: string;
  nextScheduledRun?: string;
}

export interface ScrapingAlert {
  id: string;
  alertType: 'low_success_rate' | 'bot_down' | 'quota_exceeded' | 'captcha_block';
  message: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: string;
  acknowledged: boolean;
  acknowledgedBy?: string;
}

export interface PaymentFailure {
  id: string;
  userId: string;
  amount: number;
  currency: string;
  failureReason: string;
  timestamp: string;
  recovered: boolean;
  recoveryDate?: string;
  retryAttempts: number;
}

// Setting Module => Payment Gateway
export interface ConfigItem {
  id: number;
  key: string;
  value: {
    api_key: string;
    secret_key: string;
  };
}