"""
Voiceprint Controller
=====================
FastAPI controller for voiceprint processing endpoints.
Handles audio, text, and video sample analysis.
"""

from fastapi import APIRouter, HTTPException, Path as PathParam, UploadFile, File, Body
from pydantic import BaseModel, Field
from typing import Dict, Any
from pathlib import Path
import uuid
import asyncio
from datetime import datetime

from app.config.logger import get_logger
from app.services.voiceprint_service import VoiceprintService

logger = get_logger(__name__, file_name="voiceprint.log")

# Initialize router
router = APIRouter(
    prefix="/api/v1/voiceprint",
    tags=["Voiceprint"],
    responses={
        400: {"description": "Bad Request - Invalid input parameters"},
        404: {"description": "Speaker not found"},
        500: {"description": "Internal Server Error - Processing failed"}
    }
)

# Initialize service
voiceprint_service = VoiceprintService()


class AudioUploadRequest(BaseModel):
    """Request model for audio upload."""
    file_url: str = Field(..., description="S3 URL of the uploaded audio file", example="https://s3.amazonaws.com/bucket/path/to/file.mp3")


class VideoUploadRequest(BaseModel):
    """Request model for video upload."""
    file_url: str = Field(..., description="S3 URL of the uploaded video file", example="https://s3.amazonaws.com/bucket/path/to/file.mp4")


class TextUploadRequest(BaseModel):
    """Request model for text upload."""
    file_url: str = Field(..., description="S3 URL of the uploaded text file", example="https://s3.amazonaws.com/bucket/path/to/file.pdf")


class CombinedVoiceprintRequest(BaseModel):
    """Request model for combined voiceprint processing."""
    file_url: str = Field(..., description="S3 URL of the text file", example="https://s3.amazonaws.com/bucket/path/to/file.pdf")
    audio_url: str = Field(..., description="S3 URL of the audio file", example="https://s3.amazonaws.com/bucket/path/to/file.mp3")
    video_url: str = Field(..., description="S3 URL of the video file", example="https://s3.amazonaws.com/bucket/path/to/file.mp4")



@router.post(
    "/audio/test-analyze",
    summary="[TEST] Analyze Audio File Directly",
    description="""
    TEST ENDPOINT: Upload audio file directly and analyze without database storage.
    
    This endpoint is for testing the audio analysis functionality:
    - Accepts direct file upload (.mp3 or .wav)
    - Saves file temporarily
    - Calls analyze_audio function directly
    - Returns metrics without storing in database
    - Automatically cleans up temp file
    
    Accepts:
    - Audio file formats: .MP3, .WAV
    - Max file size: 25 MB
    
    Returns audio metrics including:
    - Pitch variance, mean, range
    - Tempo (speech rate WPM/SPM)
    - Inflection patterns
    """,
    response_model=Dict[str, Any]
)
async def test_analyze_audio(
    file: UploadFile = File(..., description="Audio file to analyze (.mp3 or .wav)")
) -> Dict[str, Any]:
    """
    Test endpoint: Analyze uploaded audio file directly without database storage.
    
    Args:
        file: Uploaded audio file
        
    Returns:
        Dictionary with audio metrics (testing only)
        
    Raises:
        HTTPException: If processing fails
    """
    temp_file_path = None
    
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        # Validate file extension
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in ['.mp3', '.wav']:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported audio file format: {file_ext}. Only .mp3 and .wav are supported."
            )
        
        # Generate temporary file path
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        temp_dir = Path("voiceprint/audio/temp")
        temp_dir.mkdir(parents=True, exist_ok=True)
        temp_file_path = temp_dir / f"test_{timestamp}_{unique_id}{file_ext}"
        
        logger.info(f"Saving uploaded audio file for testing: {file.filename}")
        
        # Save uploaded file to temporary location
        with open(temp_file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Validate file size (25 MB max)
        file_size_mb = temp_file_path.stat().st_size / (1024 * 1024)
        if file_size_mb > 25:
            raise HTTPException(
                status_code=400,
                detail=f"File size ({file_size_mb:.2f} MB) exceeds maximum allowed size (25 MB)"
            )
        
        logger.info(f"Audio file saved for testing: {temp_file_path} ({file_size_mb:.2f} MB)")
        
        # Analyze audio file directly (without database storage)
        logger.info(f"Starting direct audio analysis for test file")
        result = voiceprint_service.analyze_audio_file_direct(str(temp_file_path))
        
        # Cleanup temporary file
        if temp_file_path.exists():
            temp_file_path.unlink()
            logger.info(f"Cleaned up test file: {temp_file_path}")
        
        logger.info(f"Direct audio analysis completed successfully")
        return result
        
    except HTTPException:
        # Cleanup on HTTP error
        if temp_file_path and temp_file_path.exists():
            temp_file_path.unlink()
        raise
    
    except ValueError as e:
        logger.error(f"Validation error in test audio analysis: {e}")
        if temp_file_path and temp_file_path.exists():
            temp_file_path.unlink()
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error in test audio analysis: {e}", exc_info=True)
        # Cleanup on error
        if temp_file_path and temp_file_path.exists():
            temp_file_path.unlink()
        raise HTTPException(
            status_code=500,
            detail=f"Error analyzing audio file: {str(e)}"
        )

@router.post(
    "/audio/{speaker_id}",
    summary="Process Audio Sample",
    description="""
    Analyze audio sample from S3 URL and extract prosody metrics.
    
    Accepts:
    - Audio file formats: .MP3, .WAV
    - Max file size: 25 MB
    - Duration: 30-60 seconds recommended
    
    Returns audio metrics including:
    - Pitch variance, mean, range
    - Tempo (speech rate WPM/SPM)
    - Inflection patterns
    """,
    response_model=Dict[str, Any]
)
async def process_audio_sample(
    speaker_id: int = PathParam(..., description="Speaker ID", gt=0),
    request: AudioUploadRequest = None
) -> Dict[str, Any]:
    """
    Process audio sample for voiceprint analysis.
    
    Args:
        speaker_id: ID of the speaker
        request: Request body containing S3 URL
        
    Returns:
        Dictionary with processing results and audio metrics
        
    Raises:
        HTTPException: If processing fails
    """
    try:
        if not request or not request.file_url:
            raise HTTPException(
                status_code=400,
                detail="file_url is required in request body"
            )
        
        logger.info(f"Processing audio sample for speaker {speaker_id} from URL: {request.file_url}")
        
        # Process audio sample
        result = voiceprint_service.process_audio_sample(
            speaker_id=speaker_id,
            s3_url=request.file_url
        )
        
        logger.info(f"Audio processing completed for speaker {speaker_id}")
        return result
        
    except ValueError as e:
        logger.error(f"Validation error for speaker {speaker_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except RuntimeError as e:
        logger.error(f"Runtime error for speaker {speaker_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error processing audio for speaker {speaker_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error processing audio sample: {str(e)}"
        )


# Video endpoints - specific routes must come before parameterized routes

@router.post(
    "/video/test-analyze",
    summary="[TEST] Analyze Video File Directly",
    description="""
    TEST ENDPOINT: Upload video file directly and analyze without database storage.
    
    This endpoint is for testing the video analysis functionality:
    - Accepts direct file upload (.mp4 or .mov)
    - Extracts audio from video
    - Analyzes cadence and delivery timing
    - Returns metrics without storing in database
    - Automatically cleans up temp files
    
    Accepts:
    - Video file formats: .MP4, .MOV
    - Max file size: 50 MB
    - Duration: 30-60 seconds recommended
    
    Returns video metrics including:
    - Video metadata (fps, resolution, codec)
    - Audio analysis (pitch, tempo, inflection)
    - Cadence metrics (delivery timing, pace characteristics)
    """,
    response_model=Dict[str, Any]
)
async def test_analyze_video(
    file: UploadFile = File(..., description="Video file to analyze (.mp4 or .mov)")
) -> Dict[str, Any]:
    """
    Test endpoint: Analyze uploaded video file directly without database storage.
    
    Args:
        file: Uploaded video file
        
    Returns:
        Dictionary with video metrics (testing only)
        
    Raises:
        HTTPException: If processing fails
    """
    temp_file_path = None
    
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        # Validate file extension
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in ['.mp4', '.mov']:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported video file format: {file_ext}. Only .mp4 and .mov are supported."
            )
        
        # Generate temporary file path
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        temp_dir = Path("voiceprint/video/temp")
        temp_dir.mkdir(parents=True, exist_ok=True)
        temp_file_path = temp_dir / f"test_{timestamp}_{unique_id}{file_ext}"
        
        logger.info(f"Saving uploaded video file for testing: {file.filename}")
        
        # Save uploaded file to temporary location
        with open(temp_file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Validate file size (50 MB max)
        file_size_mb = temp_file_path.stat().st_size / (1024 * 1024)
        if file_size_mb > 50:
            raise HTTPException(
                status_code=400,
                detail=f"File size ({file_size_mb:.2f} MB) exceeds maximum allowed size (50 MB)"
            )
        
        logger.info(f"Video file saved for testing: {temp_file_path} ({file_size_mb:.2f} MB)")
        
        # Analyze video file directly (without database storage)
        logger.info(f"Starting direct video analysis for test file")
        result = voiceprint_service.analyze_video_file_direct(str(temp_file_path))
        
        # Cleanup temporary file
        if temp_file_path.exists():
            temp_file_path.unlink()
            logger.info(f"Cleaned up test file: {temp_file_path}")
        
        logger.info(f"Direct video analysis completed successfully")
        return result
        
    except HTTPException:
        # Cleanup on HTTP error
        if temp_file_path and temp_file_path.exists():
            temp_file_path.unlink()
        raise
    
    except ValueError as e:
        logger.error(f"Validation error in test video analysis: {e}")
        if temp_file_path and temp_file_path.exists():
            temp_file_path.unlink()
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error in test video analysis: {e}", exc_info=True)
        # Cleanup on error
        if temp_file_path and temp_file_path.exists():
            temp_file_path.unlink()
        raise HTTPException(
            status_code=500,
            detail=f"Error analyzing video file: {str(e)}"
        )


@router.post(
    "/video/{speaker_id}",
    summary="Process Video Sample",
    description="""
    Analyze video sample from S3 URL and extract cadence metrics.
    
    Accepts:
    - Video file formats: .MP4, .MOV
    - Max file size: 50 MB
    - Duration: 30-60 seconds recommended
    
    Returns video metrics including:
    - Video metadata (fps, resolution, codec)
    - Audio analysis (pitch, tempo, inflection)
    - Cadence metrics (delivery timing, pace characteristics)
    """,
    response_model=Dict[str, Any]
)
async def process_video_sample(
    speaker_id: int = PathParam(..., description="Speaker ID", gt=0),
    request: VideoUploadRequest = None
) -> Dict[str, Any]:
    """
    Process video sample for voiceprint analysis.
    
    Args:
        speaker_id: ID of the speaker
        request: Request body containing S3 URL
        
    Returns:
        Dictionary with processing results and video metrics
        
    Raises:
        HTTPException: If processing fails
    """
    try:
        if not request or not request.file_url:
            raise HTTPException(
                status_code=400,
                detail="file_url is required in request body"
            )
        
        logger.info(f"Processing video sample for speaker {speaker_id} from URL: {request.file_url}")
        
        # Process video sample
        result = voiceprint_service.process_video_sample(
            speaker_id=speaker_id,
            s3_url=request.file_url
        )
        
        logger.info(f"Video processing completed for speaker {speaker_id}")
        return result
        
    except ValueError as e:
        logger.error(f"Validation error for speaker {speaker_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except RuntimeError as e:
        logger.error(f"Runtime error for speaker {speaker_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error processing video for speaker {speaker_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error processing video sample: {str(e)}"
        )


# Text endpoints - specific routes must come before parameterized routes

@router.post(
    "/text/test-analyze",
    summary="[TEST] Analyze Text File Directly",
    description="""
    TEST ENDPOINT: Upload text file directly and analyze without database storage.
    
    This endpoint is for testing the text analysis functionality:
    - Accepts direct file upload (.pdf, .docx, or .txt)
    - Extracts text from document
    - Analyzes linguistic metrics using OpenAI
    - Returns metrics without storing in database
    - Automatically cleans up temp file
    
    Accepts:
    - Text file formats: .PDF, .DOCX, .TXT
    - Max file size: 2 MB
    - Minimum text length: 50 characters
    
    Returns text metrics including:
    - Average sentence length
    - Pacing metrics (sentence length variance, punctuation density, rhythm score)
    - Tone variance (sentiment variance, tone shifts, dominant tone)
    """,
    response_model=Dict[str, Any]
)
async def test_analyze_text(
    file: UploadFile = File(..., description="Text file to analyze (.pdf, .docx, or .txt)")
) -> Dict[str, Any]:
    """
    Test endpoint: Analyze uploaded text file directly without database storage.
    
    Args:
        file: Uploaded text file
        
    Returns:
        Dictionary with text metrics (testing only)
        
    Raises:
        HTTPException: If processing fails
    """
    temp_file_path = None
    
    try:
        # Validate file
        if not file.filename:
            raise HTTPException(status_code=400, detail="No filename provided")
        
        # Validate file extension
        file_ext = Path(file.filename).suffix.lower()
        if file_ext not in ['.pdf', '.docx', '.txt']:
            raise HTTPException(
                status_code=400,
                detail=f"Unsupported text file format: {file_ext}. Only .pdf, .docx, and .txt are supported."
            )
        
        # Determine file type
        file_type = file_ext.lstrip('.')
        
        # Generate temporary file path
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]
        temp_dir = Path("voiceprint/text/temp")
        temp_dir.mkdir(parents=True, exist_ok=True)
        temp_file_path = temp_dir / f"test_{timestamp}_{unique_id}{file_ext}"
        
        logger.info(f"Saving uploaded text file for testing: {file.filename}")
        
        # Save uploaded file to temporary location
        with open(temp_file_path, "wb") as buffer:
            content = await file.read()
            buffer.write(content)
        
        # Validate file size (2 MB max)
        file_size_mb = temp_file_path.stat().st_size / (1024 * 1024)
        if file_size_mb > 2:
            raise HTTPException(
                status_code=400,
                detail=f"File size ({file_size_mb:.2f} MB) exceeds maximum allowed size (2 MB)"
            )
        
        logger.info(f"Text file saved for testing: {temp_file_path} ({file_size_mb:.2f} MB)")
        
        # Analyze text file directly (without database storage)
        logger.info(f"Starting direct text analysis for test file")
        result = voiceprint_service.analyze_text_file_direct(str(temp_file_path), file_type)
        
        # Cleanup temporary file
        if temp_file_path.exists():
            temp_file_path.unlink()
            logger.info(f"Cleaned up test file: {temp_file_path}")
        
        logger.info(f"Direct text analysis completed successfully")
        return result
        
    except HTTPException:
        # Cleanup on HTTP error
        if temp_file_path and temp_file_path.exists():
            temp_file_path.unlink()
        raise
    
    except ValueError as e:
        logger.error(f"Validation error in test text analysis: {e}")
        if temp_file_path and temp_file_path.exists():
            temp_file_path.unlink()
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error in test text analysis: {e}", exc_info=True)
        # Cleanup on error
        if temp_file_path and temp_file_path.exists():
            temp_file_path.unlink()
        raise HTTPException(
            status_code=500,
            detail=f"Error analyzing text file: {str(e)}"
        )


@router.post(
    "/text/{speaker_id}",
    summary="Process Text Sample",
    description="""
    Analyze text sample from S3 URL and extract linguistic metrics.
    
    Accepts:
    - Text file formats: .PDF, .DOCX, .TXT
    - Max file size: 2 MB
    - Minimum text length: 50 characters
    
    Returns text metrics including:
    - Average sentence length
    - Pacing metrics (sentence length variance, punctuation density, rhythm score)
    - Tone variance (sentiment variance, tone shifts, dominant tone)
    """,
    response_model=Dict[str, Any]
)
async def process_text_sample(
    speaker_id: int = PathParam(..., description="Speaker ID", gt=0),
    request: TextUploadRequest = None
) -> Dict[str, Any]:
    """
    Process text sample for voiceprint analysis.
    
    Args:
        speaker_id: ID of the speaker
        request: Request body containing S3 URL
        
    Returns:
        Dictionary with processing results and text metrics
        
    Raises:
        HTTPException: If processing fails
    """
    try:
        if not request or not request.file_url:
            raise HTTPException(
                status_code=400,
                detail="file_url is required in request body"
            )
        
        logger.info(f"Processing text sample for speaker {speaker_id} from URL: {request.file_url}")
        
        # Process text sample
        result = voiceprint_service.process_text_sample(
            speaker_id=speaker_id,
            s3_url=request.file_url
        )
        
        logger.info(f"Text processing completed for speaker {speaker_id}")
        return result
        
    except ValueError as e:
        logger.error(f"Validation error for speaker {speaker_id}: {e}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except RuntimeError as e:
        logger.error(f"Runtime error for speaker {speaker_id}: {e}")
        raise HTTPException(status_code=500, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error processing text for speaker {speaker_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error processing text sample: {str(e)}"
        )


@router.post(
    "/metrics/{speaker_id}",
    summary="Process Combined Voiceprint Samples",
    description="""
    Process all three voiceprint sample types (text, audio, video) concurrently in a single request.
    
    Processes:
    - Text from file_url (PDF, DOCX, TXT)
    - Audio from audio_url (MP3, WAV)
    - Video from video_url (MP4, MOV)
    
    Returns combined metrics for all three analyses.
    """,
    response_model=Dict[str, Any]
)
async def process_combined_voiceprint(
    speaker_id: int = PathParam(..., description="Speaker ID", gt=0),
    request: CombinedVoiceprintRequest = Body(...)
) -> Dict[str, Any]:
    """Process all three voiceprint sample types concurrently."""
    
    async def process_sample(process_func, url: str, sample_type: str) -> Dict[str, Any]:
        """Helper to process a single sample type with error handling."""
        try:
            return await asyncio.to_thread(process_func, speaker_id, url)
        except Exception as e:
            logger.error(f"Error processing {sample_type} for speaker {speaker_id}: {e}", exc_info=True)
            return {"success": False, "error": str(e), "sample_type": sample_type}
    
    try:
        logger.info(f"Processing combined voiceprint for speaker {speaker_id}")
        
        # Process all three concurrently
        text_task = process_sample(voiceprint_service.process_text_sample, request.file_url, "text")
        audio_task = process_sample(voiceprint_service.process_audio_sample, request.audio_url, "audio")
        video_task = process_sample(voiceprint_service.process_video_sample, request.video_url, "video")
        
        text_result, audio_result, video_result = await asyncio.gather(
            text_task, audio_task, video_task, return_exceptions=True
        )
        
        # Handle exceptions from gather
        if isinstance(text_result, Exception):
            text_result = {"success": False, "error": str(text_result), "sample_type": "text"}
        if isinstance(audio_result, Exception):
            audio_result = {"success": False, "error": str(audio_result), "sample_type": "audio"}
        if isinstance(video_result, Exception):
            video_result = {"success": False, "error": str(video_result), "sample_type": "video"}
        
        results = {
            "success": all(r.get("success", False) for r in [text_result, audio_result, video_result]),
            "message": "Combined voiceprint processing completed",
            "speaker_id": speaker_id,
            "results": {
                "text": text_result,
                "audio": audio_result,
                "video": video_result
            }
        }
        
        # Generate tone profile if all three samples processed successfully
        tone_profile = None
        if (text_result.get("success") and audio_result.get("success") and video_result.get("success")):
            try:
                logger.info(f"Generating tone profile for speaker {speaker_id}")
                
                # Extract metrics from results
                text_metrics = text_result.get("text_metrics", {})
                audio_metrics = audio_result.get("audio_metrics", {})
                video_metrics = video_result.get("video_metrics", {})
                
                # Generate tone profile using LLM
                tone_profile = await asyncio.to_thread(
                    voiceprint_service.generate_tone_profile,
                    speaker_id,
                    text_metrics,
                    audio_metrics,
                    video_metrics
                )
                
                # Store tone profile in database
                await asyncio.to_thread(
                    voiceprint_service.store_voiceprint,
                    speaker_id,
                    tone_profile
                )
                
                results["tone_profile"] = tone_profile
                logger.info(f"Tone profile generated and stored successfully for speaker {speaker_id}")
                
            except Exception as e:
                logger.error(f"Error generating tone profile for speaker {speaker_id}: {e}", exc_info=True)
                results["tone_profile"] = {
                    "success": False,
                    "error": str(e),
                    "message": "Tone profile generation failed"
                }
        else:
            logger.warning(
                f"Not generating tone profile for speaker {speaker_id} - "
                f"one or more samples failed. Text: {text_result.get('success')}, "
                f"Audio: {audio_result.get('success')}, Video: {video_result.get('success')}"
            )
        
        logger.info(f"Combined voiceprint processing completed for speaker {speaker_id}")
        return results
        
    except Exception as e:
        logger.error(f"Unexpected error in combined voiceprint for speaker {speaker_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Error processing combined voiceprint: {str(e)}")