import { Pagination, PaginationContent, <PERSON><PERSON>ation<PERSON><PERSON>psis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from "../components/ui/pagination";
// OpportunityDetail page: displays one opportunity with event info and speakers
// Notes:
// - Uses shadCN components (except for ant-design icons retained per requirement)
// - Edit dialog uses react-hook-form + zod for validation
import React, { useState, useMemo } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { 
  ArrowLeftOutlined, 
  EditOutlined,
  CalendarOutlined,
  EnvironmentOutlined 
} from '@ant-design/icons';
import { Info, ArrowLeft, Calendar, MapPin } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Textarea } from '../components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../components/ui/select';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../components/ui/dialog';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '../components/ui/form';
import { Badge } from '../components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../components/ui/tooltip';
import { Calendar as CalendarComponent } from '../components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '../components/ui/popover';
import { cn } from '../lib/utils';
import { format } from 'date-fns';
import { toast } from "../hooks/use-toast";
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { useAppState } from '../state/AppStateProvider';
import {
  useGetOpportunityByIdQuery,
  useUpdateOpportunityMutation,
  useGetAllSpeakersByOpportunityIdQuery,
} from '../apis/opportunitiesApi';
import type { Match } from '../types';
import StatusTag from '@/components/common/StatusTag';
import { Skeleton } from '@/components/ui/skeleton';
import dayjs from 'dayjs';
import { InvalidTokenHandler } from "../components/common/InvalidTokenHandler";

// Validation schema for edit dialog inputs
const opportunityEditSchema = z.object({
  title: z.string().min(1, 'Please enter opportunity title'),
  organization: z.string().min(1, 'Please enter organization name'),
  description: z.string().optional(),
  location: z.string().min(1, 'Please enter location'),
  eventDate: z.date().optional(),
  deadline: z.date().optional(),
  status: z.string().min(1, 'Please select status'),
  category: z.string().optional(),
  budget: z.string().optional(),
  externalUrl: z.string().optional(),
});

// OpportunityDetail component - displays detailed event information with speakers table
const OpportunityDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { matches, speakers, dispatch } = useAppState();
  const { data: apiOpportunityResponse, isLoading ,error: opportunityError} = useGetOpportunityByIdQuery(id as string, { skip: !id ,refetchOnMountOrArgChange: true, refetchOnReconnect: true});
  const [updateOpportunity] = useUpdateOpportunityMutation();
  // Speakers pagination state
  const [speakersPage, setSpeakersPage] = useState(1);
  const [speakersPageSize, setSpeakersPageSize] = useState(10);
  const { data: speakersResp, isLoading: speakersLoading,error: speakersError} = useGetAllSpeakersByOpportunityIdQuery({ id: id as string, page: speakersPage, limit: speakersPageSize }, {refetchOnMountOrArgChange: true, refetchOnReconnect: true});
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isEditModalVisible, setIsEditModalVisible] = useState(false);
  const form = useForm<z.infer<typeof opportunityEditSchema>>({
    resolver: zodResolver(opportunityEditSchema),
  });

  // Normalize API response to UI shape (fallback category from event_type)
  const opportunity = useMemo(() => {
    const raw: any = (apiOpportunityResponse as any)?.data?.opportunity || null;
    if (!raw) return undefined as any;
    // Normalize to UI shape
    return {
      ...raw,
      category: raw.category || raw.event_type,
    };
  }, [apiOpportunityResponse]);

  // Join matches with speakers (prefer API speakers-by-opportunity)
  const opportunityMatches = useMemo(() => {
    // Prefer API speakers-by-opportunity if available
    const api = (speakersResp as any)?.data || [];
    if (api.length) return api as any[];
    // fallback to local
    return matches.matches.filter(m => m.opportunityId === id);
  }, [matches.matches, id, speakersResp]);

  // query hook auto-fetches based on id

  // Filter by status and attach speaker objects for rendering
  const getAllMatchesWithSpeakers = useMemo(() => {
    const allMatches = opportunityMatches.map((match: any) => {
      // If API already includes a speaker object, prefer it
      if (match && match.speaker) {
        return match;
      }
      // Fallback: derive from local store by id (support both camelCase and snake_case)
      const speakerId = match?.speakerId ?? match?.speaker_id;
      const speaker = speakers.speakers.find(s => s.id === speakerId);
      return { ...match, speaker };
    });
    
    if (statusFilter === 'all') {
      return allMatches;
    }
    
    return allMatches.filter(match => match.status === statusFilter);
  }, [opportunityMatches, speakers.speakers, statusFilter]);

  if (isLoading) {
    return (
      <div className="p-6">
        <Button onClick={() => navigate('/admin/opportunities')} variant="outline">
          <ArrowLeftOutlined className="mr-2 h-4 w-4" />
          Back to Opportunities
        </Button>
        <div className="mt-6 space-y-6">
          <Card className="overflow-hidden shadow-lg mb-8 bg-tertiary">
            <div className="p-6 space-y-4">
              <Skeleton className="h-10 w-2/3 mx-auto" />
              <div className="flex items-center justify-center gap-4">
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-6 w-24" />
              </div>
            </div>
          </Card>

          <Card className="shadow-md bg-tertiary">
            <CardHeader>
              <Skeleton className="h-6 w-40" />
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {[...Array(6)].map((_, i) => (
                  <Skeleton key={i} className="h-12 w-full" />
                ))}
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-md bg-tertiary">
            <CardHeader>
              <div className="flex justify-between items-center">
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-8 w-40" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[...Array(6)].map((_, i) => (
                  <Skeleton key={i} className="h-10 w-full" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  if (!opportunity) {
    return (
      <div className="p-6">
        <Button onClick={() => navigate('/admin/opportunities')} variant="outline">
          <ArrowLeftOutlined className="mr-2 h-4 w-4" />
          Back to Opportunities
        </Button>
        <div className="flex flex-col items-center justify-center py-12">
          <div className="text-center">
            <h4 className="text-lg font-semibold mb-2">Opportunity not found</h4>
          </div>
        </div>
      </div>
    );
  }

  // Utility: map score -> level/color used in UI badges
  const getMatchingLevel = (score: number) => {
    if (score >= 90) return { level: 'Level 5', color: '#52c41a' };
    if (score >= 75) return { level: 'Level 4', color: '#1890ff' };
    if (score >= 60) return { level: 'Level 3', color: '#faad14' };
    if (score >= 40) return { level: 'Level 2', color: '#fa8c16' };
    return { level: 'Level 1', color: '#f5222d' };
  };


  const getStatusCounts = () => {
    const counts = {
      all: opportunityMatches.length,
      pending: opportunityMatches.filter(m => m.status === 'pending').length,
      interested: opportunityMatches.filter(m => m.status === 'interested').length,
      accepted: opportunityMatches.filter(m => m.status === 'accepted').length,
      rejected: opportunityMatches.filter(m => m.status === 'rejected').length,
    };
    return counts;
  };

  const statusCounts = getStatusCounts();
  // Prefer API total from speakers-by-opportunity endpoint if present
  const apiSpeakersPagination = (speakersResp as any)?.pagination;
  const allSpeakersTotal = apiSpeakersPagination?.total ?? statusCounts.all;
  const speakersPagination = apiSpeakersPagination || { total: allSpeakersTotal, limit: speakersPageSize, page: speakersPage, totalPages: Math.ceil(allSpeakersTotal / speakersPageSize) };

  const handleEditOpportunity = () => {
    if (!opportunity) return;
    
    form.reset({
      title: opportunity.title,
      organization: opportunity.organization,
      description: opportunity.description,
      location: opportunity.city,
      eventDate: opportunity.start_date ? dayjs(opportunity.start_date).toDate() : undefined,
      // Autofill Application Deadline from API's end_date when present
      deadline: (opportunity as any).end_date ? dayjs((opportunity as any).end_date).toDate() : (opportunity as any).deadline ? dayjs((opportunity as any).deadline).toDate() : undefined,
      // Map backend boolean is_active -> form status select
      status: (opportunity as any).is_active !== undefined ? ((opportunity as any).is_active ? 'active' : 'completed') : opportunity.status,
      category: opportunity.event_type,
      budget: opportunity.budget?.toString() || '',
      externalUrl: (opportunity as any).externalUrl || (opportunity as any).event_url || ''
    });
    setIsEditModalVisible(true);
  };

  const handleSaveOpportunity = async (values: z.infer<typeof opportunityEditSchema>) => {
    try {
      const payload = {
        id: opportunity.id,
        title: values.title,
        organization: values.organization,
        description: values.description,
        // Persist city from the form's `location` input
        city: values.location,
        created_at: values.eventDate?.toISOString(),
        // Send application deadline to API's end_date field
        end_date: values.deadline?.toISOString(),
        // Map form status -> backend is_active boolean
        is_active: values.status === 'active',
        event_type: values.category,
        budget: values.budget ? parseFloat(values.budget) : undefined,
        event_url: values.externalUrl,
      } as any;
      // Some APIs expect event_type
      if (!payload.event_type && values.category) {
        payload.event_type = values.category;
      }
      await updateOpportunity(payload).unwrap();
      toast({ description: 'Opportunity updated successfully' });
      setIsEditModalVisible(false);
    } catch (error) {
      toast({ description: 'Failed to update opportunity', variant: "destructive" });
    }
  };

  return (
      <>
  <InvalidTokenHandler error={opportunityError || speakersError} />
    <TooltipProvider>
      <div className="min-h-screen" style={{ background: 'hsl(var(--background))' }}>
        <div style={{ 
          background: 'hsl(var(--card))', 
          borderBottom: '1px solid hsl(var(--border))',
          padding: '0rem 0rem 1rem 0rem'
        }}>
          <div className="flex justify-between items-center">
            <div>
              <Button 
                onClick={() => navigate('/admin/opportunities')} 
                variant="outline" 
                className="bg-card"
                style={{ borderColor: 'hsl(var(--border))' }}
              >
                <ArrowLeftOutlined className="mr-2 h-4 w-4" />
                Back to Opportunities
              </Button>
            </div>
            {/* <Button onClick={handleEditOpportunity} className="ml-4">
              <EditOutlined className="mr-2 h-4 w-4" />
              Edit Opportunity
            </Button> */}
          </div>
        </div>

        <div className="pt-2 bg-card">
          <Card className="overflow-hidden shadow-lg mb-8 bg-tertiary">
            <div 
              className="relative h-64 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 flex items-center justify-center py-8 px-8"
              style={{ color: '#FFFFFF' }}
            >
              <div className="absolute inset-0" style={{ backgroundColor: 'rgba(0, 0, 0, 0.2)' }}></div>
              <div className="relative z-10 text-center space-y-6">
                <h1 className="text-4xl font-bold text-white m-0">
                  {opportunity.title}
                </h1>
                <div className="flex items-center justify-center space-x-8 text-lg">
                  <div className="flex items-center space-x-2">
                    <CalendarOutlined />
                    <span className="text-white">
                      {(() => {
                        const sd = (opportunity as any).start_date;
                        const start = sd ? new Date(sd) : null;
                        return `Start: ${start ? start.toLocaleDateString() : '---'}`;
                      })()}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <CalendarOutlined />
                    <span className="text-white">
                      {(() => {
                        const ed = (opportunity as any).end_date;
                        const end = ed ? new Date(ed) : null;
                        return `End: ${end ? end.toLocaleDateString() : '---'}`;
                      })()}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <EnvironmentOutlined />
                    <span className="text-white">{opportunity.city}</span>
                  </div>
                </div>
                <div className="mt-6 flex items-center justify-center space-x-4">
                  <div className="inline-flex items-center px-6 py-3 bg-white/20 rounded-lg text-white font-medium backdrop-blur-sm">
                    {opportunity.event_type || 'Conference'}
                  </div>
                  <Button 
                    size="lg"
                    className="bg-white/20 border-white text-white hover:bg-white hover:text-gray-800 backdrop-blur-sm"
                    onClick={() => window.open(opportunity.event_url || 'https://www.google.com/', '_blank')}
                  >
                    Go to Event Page
                  </Button>
                </div>
              </div>
            </div>
          </Card>

          <div className="space-y-8">
            {/* Event Information */}
            <Card className="shadow-md bg-tertiary">
              <CardHeader>
                <CardTitle>Event Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* Left panel */}
                  <div className="rounded-xl border overflow-hidden">
                    <div className="grid grid-cols-2 h-full">
                      <div className="px-4 py-3 bg-card border-b border-r text-sm text-muted-foreground">Organization</div>
                      <div className="px-4 py-3 bg-muted/5 border-b text-sm">{opportunity.title}</div>

                      <div className="px-4 py-3 bg-card border-b border-r text-sm text-muted-foreground">Status</div>
                      <div className="px-4 py-3 bg-muted/5 border-b">
                        <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-cyan-500/15 text-cyan-300 border border-cyan-500/30">
                          {opportunity?.is_active ? 'Active' : 'Completed'}
                        </span>
                      </div>

                      <div className="px-4 py-3 bg-card border-r text-sm text-muted-foreground">End date</div>
                      <div className="px-4 py-3 bg-muted/5 text-sm">{opportunity.end_date ? new Date(opportunity.end_date).toLocaleDateString() : '---'}</div>
                    </div>
                  </div>

                  {/* Right panel */}
                  <div className="rounded-xl border overflow-hidden">
                    <div className="grid grid-cols-2 h-full">
                      <div className="px-4 py-3 bg-card border-b border-r text-sm text-muted-foreground">Venue</div>
                      <div className="px-4 py-3 bg-muted/5 border-b text-sm">{opportunity.venue}</div>

                      <div className="px-4 py-3 bg-card border-b border-r text-sm text-muted-foreground">Format</div>
                      <div className="px-4 py-3 bg-muted/5 border-b">
                        <span className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium bg-teal-500/15 text-teal-300 border border-teal-500/30">
                          {opportunity.location?.toLowerCase().includes('virtual') ? 'Virtual' : 'In-Person'}
                        </span>
                      </div>

                      <div className="px-4 py-3 bg-card border-r text-sm text-muted-foreground">Event Type</div>
                      <div className="px-4 py-3 bg-muted/5 text-sm">{opportunity.event_type || 'Conference'}</div>
                    </div>
                  </div>
                </div>
                
                {opportunity.description && (
                  <div className="mt-6">
                    <h4 className="text-lg font-semibold mb-2">Description</h4>
                    <p className="text-muted-foreground">{opportunity.description}</p>
                  </div>
                )}
              </CardContent>
            </Card>
            {speakersLoading ? (
                  <div className="flex items-center justify-between mb-4">
                    <Skeleton className="h-4 w-36" />
                    <div className="flex items-center gap-2">
                      <Skeleton className="h-4 w-20" />
                      <Skeleton className="h-10 w-20" />
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-muted-foreground text-sm">
                      Showing {Math.min(getAllMatchesWithSpeakers.length, speakersPagination.limit)} of {speakersPagination.total} speakers
                    </span>
                    <div className="flex items-center gap-2">
                      <span className="text-muted-foreground text-sm">Rows per page</span>
                      <Select value={String(speakersPageSize)} onValueChange={(v) => { setSpeakersPageSize(Number(v)); setSpeakersPage(1); }}>
                        <SelectTrigger className="w-[80px]">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="10">10</SelectItem>
                          <SelectItem value="25">25</SelectItem>
                          <SelectItem value="50">50</SelectItem>
                          <SelectItem value="100">100</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

            {/* All Speakers Table with Filter */}
            <Card className="shadow-md bg-tertiary">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="m-0">All Speakers ({statusCounts.all})</CardTitle>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-[200px]">
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Speakers ({statusCounts.all})</SelectItem>
                      <SelectItem value="pending">Pending ({statusCounts.pending})</SelectItem>
                      <SelectItem value="interested">Interested ({statusCounts.interested})</SelectItem>
                      <SelectItem value="accepted">Accepted ({statusCounts.accepted})</SelectItem>
                      <SelectItem value="rejected">Rejected ({statusCounts.rejected})</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardHeader>
              <CardContent>
                {speakersLoading ? (
                  <div className="overflow-x-auto w-full">
                    <Table className="min-w-[1000px]">
                      <TableHeader>
                        <TableRow>
                          <TableHead>Speaker</TableHead>
                          <TableHead>Expertise</TableHead>
                          <TableHead>Match Score</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Date</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {[...Array(6)].map((_, i) => (
                          <TableRow key={i}>
                            <TableCell>
                              <div className="flex items-center gap-3">
                                <div className="space-y-2">
                                  <Skeleton className="h-4 w-40" />
                                  <Skeleton className="h-3 w-32" />
                                </div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex gap-2">
                                <Skeleton className="h-5 w-16 rounded" />
                              </div>
                            </TableCell>
                            <TableCell>
                              <Skeleton className="h-4 w-12" />
                            </TableCell>
                            <TableCell>
                              <Skeleton className="h-6 w-20 rounded-full" />
                            </TableCell>
                            <TableCell>
                              <Skeleton className="h-4 w-16" />
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : getAllMatchesWithSpeakers.length > 0 ? (
                  <div className="overflow-x-auto w-full">
                    <Table className="min-w-[1000px]">
                      <TableHeader>
                        <TableRow>
                          <TableHead>Speaker</TableHead>
                          <TableHead>Expertise</TableHead>
                          <TableHead>Match Score</TableHead>
                          <TableHead>Status</TableHead>
                          <TableHead>Date</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {getAllMatchesWithSpeakers.map((record: any) => (
                          <TableRow key={record.id}>
                            <TableCell>
                              <div className="space-y-1">
                                <div className="font-medium">{record.speaker?.name || '---'}</div>
                                <div className="text-sm text-muted-foreground">{record.speaker?.email || '---'}</div>
                              </div>
                            </TableCell>
                            <TableCell>
                              <div className="flex flex-wrap gap-1">
                                {record.expertise?.slice(0, 3).map((skill: string, index: number) => (
                                  <Badge key={index} variant="secondary" className="text-xs">{skill}</Badge>
                                )) || <span className="text-muted-foreground text-sm">No expertise listed</span>}
                              </div>
                            </TableCell>
                            <TableCell>
                              <span 
                                className="font-bold"
                                style={{ 
                                  color: record.matchingScore >= 75 ? '#52c41a' : 
                                         record.matchingScore >= 50 ? '#1890ff' : '#faad14' 
                                }}
                              >
                                {record.overall_score}%
                              </span>
                            </TableCell>
                            <TableCell>
                              <div className="flex items-center gap-2">
                                <StatusTag status={record.status} label={record.status.toUpperCase()} />
                                {record.status === 'rejected' && record.rejectionReason && (
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Info size={16} style={{ color: '#f5222d', cursor: 'pointer' }} />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <div className="max-w-[300px]">
                                        <div className="font-semibold">Rejection Reason:</div>
                                        <div>{record.rejectionReason}</div>
                                      </div>
                                    </TooltipContent>
                                  </Tooltip>
                                )}
                                {record.status === 'accepted' && (
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Info size={16} style={{ color: '#52c41a', cursor: 'pointer' }} />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      <div className="max-w-[300px]">
                                        <div className="font-semibold">Accepted Reason:</div>
                                        <div>{record.acceptedReason || 'Speaker meets all requirements and has excellent expertise match.'}</div>
                                      </div>
                                    </TooltipContent>
                                  </Tooltip>
                                )}
                              </div>
                            </TableCell>
                            <TableCell>
                              {record.created_at ? new Date(record.created_at).toLocaleDateString() : 'N/A'}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </div>
                ) : (
                  <div className="flex flex-col items-center justify-center py-12">
                    <div className="text-center">
                      <h4 className="text-lg font-semibold mb-2">
                        No {statusFilter === 'all' ? '' : statusFilter} speakers found
                      </h4>
                    </div>
                  </div>
                )}
                {/* Pagination (shadcn) */}
                <div className="flex items-center justify-center mt-4">
                  {/* <div className="text-sm text-muted-foreground">
                    {(() => {
                      const start = (speakersPagination.page - 1) * speakersPagination.limit + 1;
                      const end = Math.min(speakersPagination.page * speakersPagination.limit, speakersPagination.total);
                      return `${start}-${end} of ${speakersPagination.total} speakers`;
                    })()}
                  </div> */}
                  <div className="flex items-center gap-3">
                    <Pagination>
                      <PaginationContent>
                        <PaginationItem>
                          <PaginationPrevious onClick={() => { if (speakersPagination.page === 1 || (speakersPagination.totalPages || 1) <= 1 || (speakersPagination.total || 0) === 0) return; setSpeakersPage((p) => Math.max(1, p - 1)); }} aria-disabled={speakersPagination.page === 1 || (speakersPagination.totalPages || 1) <= 1 || (speakersPagination.total || 0) === 0} />
                        </PaginationItem>
                        {(() => {
                          const totalPages = speakersPagination.totalPages || 1;
                          const current = speakersPagination.page;
                          const pages: number[] = [];
                          const start = Math.max(1, current - 2);
                          const end = Math.min(totalPages, start + 4);
                          for (let p = start; p <= end; p++) pages.push(p);
                          return pages.map((p) => (
                            <PaginationItem key={p}>
                              <PaginationLink isActive={p === current} onClick={() => { if (p === current) return; setSpeakersPage(p); }}>
                                {p}
                              </PaginationLink>
                            </PaginationItem>
                          ));
                        })()}
                        {speakersPagination.totalPages > 5 && (
                          <PaginationItem>
                            <PaginationEllipsis />
                          </PaginationItem>
                        )}
                        <PaginationItem>
                          <PaginationNext onClick={() => { if (speakersPagination.page >= speakersPagination.totalPages || (speakersPagination.totalPages || 1) <= 1 || (speakersPagination.total || 0) === 0) return; setSpeakersPage((p) => p + 1); }} aria-disabled={speakersPagination.page >= speakersPagination.totalPages || (speakersPagination.totalPages || 1) <= 1 || (speakersPagination.total || 0) === 0} />
                        </PaginationItem>
                      </PaginationContent>
                    </Pagination>
                  </div>
                </div>
              </CardContent>
            </Card>
        </div>
      </div>

        {/* Edit Opportunity Modal */}
        <Dialog open={isEditModalVisible} onOpenChange={(open) => {
          if (!open) {
            setIsEditModalVisible(false);
            form.reset();
          }
        }}>
          <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto no-scrollbar">
            <DialogHeader>
              <DialogTitle>Edit Opportunity</DialogTitle>
            </DialogHeader>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(handleSaveOpportunity)} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Title</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Enter opportunity title" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="organization"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Organization</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Enter organization name" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea {...field} rows={4} placeholder="Enter opportunity description" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="location"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Location</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Enter event location" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="budget"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Budget</FormLabel>
                        <FormControl>
                          <Input {...field} placeholder="Enter budget (optional)" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="eventDate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Event Date</FormLabel>
                        <FormControl>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full justify-start text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                <Calendar className="mr-2 h-4 w-4" />
                                {field.value ? format(field.value, "PPP") : "Select event date"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <CalendarComponent
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="deadline"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Application Deadline</FormLabel>
                        <FormControl>
                          <Popover>
                            <PopoverTrigger asChild>
                              <Button
                                variant="outline"
                                className={cn(
                                  "w-full justify-start text-left font-normal",
                                  !field.value && "text-muted-foreground"
                                )}
                              >
                                <Calendar className="mr-2 h-4 w-4" />
                                {field.value ? format(field.value, "PPP") : "Select deadline"}
                              </Button>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <CalendarComponent
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="status"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Status</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="active">Active</SelectItem>
                            <SelectItem value="completed">Completed</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="category"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Category</FormLabel>
                        <Select onValueChange={field.onChange} defaultValue={field.value}>
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="conference">Conference</SelectItem>
                            <SelectItem value="workshop">Workshop</SelectItem>
                            <SelectItem value="webinar">Webinar</SelectItem>
                            <SelectItem value="meetup">Meetup</SelectItem>
                            <SelectItem value="panel">Panel</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="externalUrl"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>External URL</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="Enter event website URL (optional)" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="flex justify-end gap-2 pt-4">
                  <Button 
                    type="button" 
                    variant="outline" 
                    onClick={() => setIsEditModalVisible(false)}
                  >
                    Cancel
                  </Button>
                  <Button type="submit">
                    Save Changes
                  </Button>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
    </>
  );
};

export default OpportunityDetail;