import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { ResponsiveContainer, Bar<PERSON>hart, Bar, LineChart, Line, <PERSON>Chart, Pie, Cell, XAxis, YAxis, CartesianGrid, <PERSON><PERSON><PERSON>, Legend } from 'recharts';
import { SearchOutlined, EyeOutlined, CheckCircleOutlined, AlertOutlined } from '@ant-design/icons';
import { AlertTriangle } from 'lucide-react';
import { useGetPipelineQuery } from '../../apis/dashboardApi';
import { InvalidTokenHandler } from '../common/InvalidTokenHandler';
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from '../../hooks/use-toast';

const OpportunityPipelineAnalytics: React.FC = () => {
  const { data, isLoading, error } = useGetPipelineQuery(undefined, { refetchOnMountOrArgChange: true, refetchOnReconnect: true });
  const pipeline = data?.data;
  
  // State for pipeline data
  const [pipelineState, setPipelineState] = useState<any>(null);
  const { toast } = useToast();

  // Effect to handle data and show success toast when data loads
  useEffect(() => {
    if (data) {
      setPipelineState(data);
      // Show success toast when data loads
      // toast({
      //   title: "Pipeline analytics loaded successfully",
      // });
    }
  }, [data, toast]);

  // Show error toast when there's an error
  useEffect(() => {
    if (error) {
      toast({
        title: "Failed to load pipeline data",
        variant: "destructive",
      });
    }
  }, [error, toast]);

  // Map API to charts (with sensible fallbacks)
  const weeklyScrapingData = (pipeline?.dailyScrapingVolume || []).map((d: any) => ({
    day: d.label,
    scraped: Number(d.value) || 0,
  }));

  const monthlyScrapingData = (pipeline?.monthlyScrapingTrends || []).map((d: any) => ({
    week: d.label,
    scraped: Number(d.value) || 0,
  }));

  const palette = [
    'hsl(var(--dashboard-dark-blue))',
    'hsl(var(--dashboard-medium-blue))',
    'hsl(var(--dashboard-light-blue))',
    'hsl(var(--muted))',
    '#22c55e',
    '#f59e0b',
    '#a78bfa',
  ];
  const sourceBreakdownData = (pipeline?.sourceBreakdown || []).map((s: any, idx: number) => ({
    source: s.source,
    count: Number(s.count) || 0,
    color: palette[idx % palette.length],
  }));

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div>
          <Skeleton className="h-5 w-72" />
          <Skeleton className="h-4 w-96 mt-2" />
        </div>

        {/* KPI Skeletons */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="bg-tertiary border-border">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <Skeleton className="h-4 w-28" />
                  <Skeleton className="h-6 w-24" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Chart Skeletons */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Array.from({ length: 2 }).map((_, i) => (
            <Card key={i} className="bg-tertiary border-border">
              <CardHeader>
                <Skeleton className="h-5 w-56" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-[300px] w-full" />
              </CardContent>
            </Card>
          ))}
          <Card className="bg-tertiary border-border lg:col-span-2">
            <CardHeader>
              <Skeleton className="h-5 w-64" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-[300px] w-full" />
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <>
    <InvalidTokenHandler error={error} />
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-foreground mb-2">Opportunity Pipeline Management</h3>
        <p className="text-muted-foreground text-sm mb-6">Monitor scraping, classification, and health of speaking opportunities</p>
      </div>

      {/* Show error state */}
      {error && (
        <Card className="bg-tertiary border-border">
          <CardContent className="p-8 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Error Loading Data
            </h3>
            <p className="text-muted-foreground">
              Unable to fetch pipeline analytics data. Please try again later.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Show data when loaded and no error */}
      {!isLoading && !error && (
        <>

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-tertiary border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">This Week Scraped</p>
                <p className="text-2xl font-bold text-foreground">{Number(pipeline?.summary?.weekScraped || 0)}</p>
              </div>
              <SearchOutlined className="h-8 w-8 text-dashboard-dark-blue" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-tertiary border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Matched to Speakers</p>
                <p className="text-2xl font-bold text-foreground">{`${Number(pipeline?.summary?.matchedToSpeaker || 0).toFixed(1)}%`}</p>
              </div>
              <CheckCircleOutlined className="h-8 w-8 text-dashboard-medium-blue" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-tertiary border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Avg Health Score</p>
                <p className="text-2xl font-bold text-foreground">{`${(Number(pipeline?.summary?.healthScore || 0)).toFixed(1)}/10`}</p>
              </div>
              <EyeOutlined className="h-8 w-8 text-dashboard-light-blue" />
            </div>
          </CardContent>
        </Card>

        <Card className="bg-tertiary border-border">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Review Queue</p>
                <p className="text-2xl font-bold text-foreground">{Number(pipeline?.summary?.reviewQueue || 0)}</p>
              </div>
              <AlertOutlined className="h-8 w-8 text-destructive" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="bg-tertiary border-border">
          <CardHeader>
            <CardTitle className="text-foreground">Daily Scraping Volume (This Week)</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              {weeklyScrapingData.length > 0 ? (  
              <BarChart data={weeklyScrapingData}>
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                <XAxis dataKey="day" stroke="hsl(var(--muted-foreground))" />
                <YAxis stroke="hsl(var(--muted-foreground))" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'hsl(var(--card))', 
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                />
                <Bar dataKey="scraped" fill="hsl(var(--dashboard-dark-blue))" />
              </BarChart>
              ) : (
                <p className="flex items-center justify-center h-[300px] text-muted-foreground">No Scraping Data Available</p>
              )}
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="bg-tertiary border-border">
          <CardHeader>
            <CardTitle className="text-foreground">Source Breakdown</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              {sourceBreakdownData.length > 0 ? (  
                <PieChart>
                <Pie
                  data={sourceBreakdownData}
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  dataKey="count"
                  label={({ source, value }: { source: string, value: number }) => `${source}: ${value}`}
                >
                  {sourceBreakdownData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  separator=""
                  formatter={(value: any, _name: any, { payload }: any) => [
                    `${payload?.source}: ${value}`,
                    ''
                  ]}
                  labelFormatter={() => ''}
                  contentStyle={{ 
                    backgroundColor: 'hsl(var(--muted-foreground))', 
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                />
              </PieChart>
              ) : (
                <p className="flex items-center justify-center h-[300px] text-muted-foreground">No Source Breakdown Data Available</p>
              )}
            </ResponsiveContainer>
          </CardContent>
        </Card>

        <Card className="bg-tertiary border-border lg:col-span-2">
          <CardHeader>
            <CardTitle className="text-foreground">Monthly Scraping Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              {monthlyScrapingData.length > 0 ? (  
                <LineChart data={monthlyScrapingData}>
                <CartesianGrid strokeDasharray="3 3" stroke="hsl(var(--border))" />
                <XAxis dataKey="week" stroke="hsl(var(--muted-foreground))" />
                <YAxis stroke="hsl(var(--muted-foreground))" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: 'hsl(var(--card))', 
                    border: '1px solid hsl(var(--border))',
                    borderRadius: '6px'
                  }}
                />
                <Legend />
                <Line type="monotone" dataKey="scraped" stroke="hsl(var(--dashboard-dark-blue))" name="Opportunities Scraped" strokeWidth={2} />
              </LineChart>
              ) : (
                <p className="flex items-center justify-center h-[300px] text-muted-foreground">No Monthly Scraping Data Available</p>
              )}
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>
        </>
      )}
    </div>
    </>
  );
};

export default OpportunityPipelineAnalytics;