import { useState, useEffect, useCallback } from 'react';
import { useGetSubCategoriesQuery } from '@/apis/categoryApi';
import type { Subcategory } from '@/apis/categoryApi';

interface UseInfiniteSubcategoriesProps {
  primaryCategoryId: string;
  enabled?: boolean;
}

export function useInfiniteSubcategories({ 
  primaryCategoryId, 
  enabled = true 
}: UseInfiniteSubcategoriesProps) {
  const [allSubcategories, setAllSubcategories] = useState<Subcategory[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [isLoadingMore, setIsLoadingMore] = useState(false);

  const { 
    data, 
    isLoading, 
    error,
    refetch,
    isFetching
  } = useGetSubCategoriesQuery(
    { 
      primaryCategoryId: parseInt(primaryCategoryId), 
      page: currentPage,
      limit: 20 
    },
    { 
      skip: !enabled || !primaryCategoryId 
    }
  );

  // Reset when primary category changes
  useEffect(() => {
    if (primaryCategoryId) {
      setAllSubcategories([]);
      setCurrentPage(1);
      setHasMore(true);
      setIsLoadingMore(false);
    }
  }, [primaryCategoryId]);

  // Update subcategories when new data arrives
  useEffect(() => {
    if (data?.data) {
      if (currentPage === 1) {
        // First page - replace all data
        setAllSubcategories(data.data);
      } else {
        // Subsequent pages - append data
        setAllSubcategories(prev => [...prev, ...data.data]);
      }
      
      // Check if there are more pages
      const totalPages = Math.ceil((data as any).pagination?.total / 20) || 1;
      setHasMore(currentPage < totalPages);
      setIsLoadingMore(false);
    }
  }, [data, currentPage]);

  const loadMore = useCallback(() => {
    if (hasMore && !isLoadingMore && !isLoading) {
      setIsLoadingMore(true);
      setCurrentPage(prev => prev + 1);
    }
  }, [hasMore, isLoadingMore, isLoading]);

  const reset = useCallback(() => {
    setAllSubcategories([]);
    setCurrentPage(1);
    setHasMore(true);
    setIsLoadingMore(false);
  }, []);

  return {
    subcategories: allSubcategories,
    isLoading: isLoading && currentPage === 1,
    isLoadingMore,
    hasMore,
    error,
    loadMore,
    reset,
    refetch,
    isFetching
  };
}
