const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const Oppurtunities = require('../models/schema/opportunities');
const connection = require('../models/connection');

const csvFilePath = path.join(__dirname, 'csv', '2ac835.Speaker Opportunities.csv');

async function importEvents() {
  try {
    await connection.authenticate();
    console.log('Database connection established.');

    const events = [];
    const headerMap = {
      '🔒 Row ID': 'row_id',
      'Title': 'title',
      'Organization': 'organization',
      'Event Type': 'event_type',
      'Start Date': 'start_date',
      'End Date': 'end_date',
      'Event Url': 'event_url',
      'Source Url': 'source_url',
      'City': 'city',
      'State': 'state',
      'Country': 'country',
      'Venue': 'venue',
      'Is Virtual': 'is_virtual',
      'Description': 'description',
      'Industry': 'industry',
      'Search Query': 'search_query',
      'Feed Back': 'feed_back',
    };

    function convertValue(key, value) {
      if (value === undefined || value === null) return null;
      value = value.trim();
      if (value === '' || value === 'missing value') return null;
      if (key === 'is_virtual') {
        if (['true', 'yes', '1'].includes(value.toLowerCase())) return true;
        return false;
      }
      if (['start_date', 'end_date'].includes(key)) {
        // Accept only valid YYYY-MM-DD, else null
        const date = value.split(' ')[0];
        if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
          // Check if it's a real date
          const d = new Date(date);
          if (!isNaN(d.getTime())) return date;
        }
        return null;
      }
      return value;
    }

    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('data', (row) => {
        const mapped = {};
        for (const [csvKey, modelKey] of Object.entries(headerMap)) {
          if (row.hasOwnProperty(csvKey)) {
            mapped[modelKey] = convertValue(modelKey, row[csvKey]);
          }
        }
        events.push(mapped);
      })
      .on('end', async () => {
        console.log(`Read ${events.length} rows from CSV.`);
        let success = 0, fail = 0;
        for (const data of events) {
          try {
            await Oppurtunities.create(data);
            success++;
          } catch (err) {
            fail++;
            console.error('Insert error:', err.message, { data, error: err });
          }
        }
        console.log(`Import complete. Success: ${success}, Failed: ${fail}`);
        process.exit(0);
      });
  } catch (error) {
    console.error('Unable to connect to the database:', error);
    process.exit(1);
  }
}

importEvents();
