import React from 'react';
import { usePermissions } from '../hooks/usePermissions';
import { Permission } from '../store/slices/rbacSlice';

interface PermissionGateProps {
  children: React.ReactNode;
  permission?: Permission;
  permissions?: Permission[];
  requireAll?: boolean; // If true, user must have ALL permissions. If false, user needs ANY permission
  fallback?: React.ReactNode;
  showFallback?: boolean;
}

/**
 * Component that conditionally renders children based on user permissions
 */
const PermissionGate: React.FC<PermissionGateProps> = ({
  children,
  permission,
  permissions = [],
  requireAll = false,
  fallback = null,
  showFallback = true,
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions } = usePermissions();

  // Determine which permissions to check
  const permissionsToCheck = permission ? [permission] : permissions;

  if (permissionsToCheck.length === 0) {
    // If no permissions specified, render children
    return <>{children}</>;
  }

  // Check permissions
  const hasRequiredPermissions = requireAll
    ? hasAllPermissions(permissionsToCheck)
    : hasAnyPermission(permissionsToCheck);

  if (hasRequiredPermissions) {
    return <>{children}</>;
  }

  // Return fallback if user doesn't have required permissions
  return showFallback ? <>{fallback}</> : null;
};

export default PermissionGate;
