@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. 
All colors MUST be HSL.
*/

@layer base {
  * {
    font-family: "Segoe UI", system-ui, -apple-system, BlinkMacSystemFont,
      "Roboto", "Helvetica Neue", Arial, sans-serif;
  }

  :root {
    /* Light Theme */
    --background: 0 0% 100%;
    --foreground: 0 0% 9%;

    --card: 0 0% 100%;
    --card-foreground: 0 0% 9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 9%;

    --primary: 195 100% 39%; /* Dashboard dark blue */
    --primary-foreground: 210 40% 98%;

    --secondary: 187 69% 56%; /* Dashboard medium blue */
    --secondary-foreground: 0 0% 100%;
    
    --tertiary: 0 0% 100%;
    --tertiary-foreground: 0 0% 100%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 197 61% 76%; /* Dashboard light blue */
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    /* Status and utility colors */
    --status-active: 142 71% 45%; /* Green for active status */
    --status-pending: 45 93% 47%; /* Orange/Yellow for pending */
    --status-rejected: 0 84% 60%; /* Red for rejected */
    --status-premium: 45 93% 58%; /* Golden for premium */
    --green-600: 142 71% 45%; /* Green for success */
    --orange-500: 25 95% 53%; /* Orange for warning */
    --purple-600: 258 90% 66%; /* Purple for info */

    --border: 214.3 31.8% 91.4%;
    --border-secondary: 214.3 31.8% 91.4%; /* #00152A - Dark blue for borders */
    --input: 214.3 31.8% 91.4%;
    --input-secondary: 214.3 31.8% 91.4%;
    --ring: 195 100% 39%; /* Dashboard dark blue */

    --radius: 0.5rem;

    /* Dashboard specific blue palette */
    --dashboard-light-blue: 197 61% 76%; /* #A3DCED - Light blue */
    --dashboard-medium-blue: 187 69% 56%; /* #39C9E3 - Medium blue */
    --dashboard-dark-blue: 195 100% 39%; /* #009BC9 - Dark blue */

    /* Dashboard gradients for light theme */
    --dashboard-gradient-primary: linear-gradient(
      135deg,
      hsl(var(--dashboard-light-blue)),
      hsl(var(--dashboard-dark-blue))
    );
    --dashboard-gradient-medium: linear-gradient(
      135deg,
      hsl(var(--dashboard-medium-blue)),
      hsl(var(--dashboard-dark-blue))
    );
    --dashboard-gradient-light: linear-gradient(
      135deg,
      hsl(var(--dashboard-light-blue)),
      hsl(var(--dashboard-medium-blue))
    );
    --dashboard-gradient-progress: linear-gradient(
      90deg,
      hsl(var(--dashboard-light-blue)),
      hsl(var(--dashboard-dark-blue))
    );

    /* Sidebar - Light Theme */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 0 0% 15%;
    --sidebar-primary: 0 0% 9%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 0 0% 9%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    /* Dark Blue Modernist Theme */
    --background: 215 100% 3%; /* #00070E - Very dark blue */
    --foreground: 0 0% 100%; /* Pure white text */

    --card: 216 100% 8%; /* #00152A - Dark blue for cards */
    --card-foreground: 0 0% 100%;

    --popover: 216 100% 6%; /* Slightly lighter than background */
    --popover-foreground: 0 0% 100%;

    --primary: 195 100% 39%; /* Dashboard dark blue */
    --primary-foreground: 0 0% 100%;

    --secondary: 187 69% 56%; /* Dashboard medium blue */
    --secondary-foreground: 0 0% 100%;

    --tertiary: 217 85% 16%;       
    --tertiary-foreground: 210 100% 8%;

    --muted: 214 100% 10%;         /* #002445 - Darker blue */
    --muted-foreground: 197 61% 76%;  /* Dashboard light blue text */

    --accent: 197 61% 76%; /* Dashboard light blue */
    --accent-foreground: 215 100% 3%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    /* Status and utility colors */
    --status-active: 142 71% 45%; /* Green for active status */
    --status-pending: 45 93% 47%; /* Orange/Yellow for pending */
    --status-rejected: 0 84% 60%; /* Red for rejected */
    --status-premium: 45 93% 58%; /* Golden for premium */
    --green-600: 142 71% 45%; /* Green for success */
    --orange-500: 25 95% 53%; /* Orange for warning */
    --purple-600: 258 90% 66%; /* Purple for info */

    --border: 213 100% 19%; /* #003261 - Medium blue for borders */
    --border-tertiary: 217 59% 46%; /* #3066B9 - Medium blue for borders */
    --border-secondary: 207 100% 49% 0.13; /* #00152A - Dark blue for borders */
    --input: 216 100% 8%; /* Same as card */
    --input-secondary: 217 100% 8%;
    --ring: 195 100% 39%; /* Dashboard dark blue */

    /* Sidebar - Dark Blue */
    --sidebar-background: 216 100% 8%; /* Even darker than background */
    --sidebar-foreground: 0 0% 100%;
    --sidebar-primary: 195 100% 39%; /* Dashboard dark blue */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 216 100% 8%;
    --sidebar-accent-foreground: 0 0% 100%;
    --sidebar-border: 213 100% 19%;
    --sidebar-ring: 195 100% 39%; /* Dashboard dark blue */

    /* Dashboard gradients for dark theme - use lighter shades for visibility */
    --dashboard-gradient-primary: linear-gradient(
      135deg,
      hsl(var(--dashboard-light-blue)),
      hsl(var(--dashboard-medium-blue))
    );
    --dashboard-gradient-medium: linear-gradient(
      135deg,
      hsl(var(--dashboard-medium-blue)),
      hsl(var(--dashboard-light-blue))
    );
    --dashboard-gradient-light: linear-gradient(
      135deg,
      hsl(var(--dashboard-light-blue)),
      hsl(197 61% 85%)
    );
    --dashboard-gradient-progress: linear-gradient(
      90deg,
      hsl(var(--dashboard-light-blue)),
      hsl(var(--dashboard-medium-blue))
    );

    /* Dashboard shadows and effects */
    --dashboard-shadow: 0 4px 20px hsl(var(--dashboard-dark-blue) / 0.3);
    --dashboard-glow: 0 0 30px hsl(var(--dashboard-light-blue) / 0.4);
  }
}

@layer base {
  * {
    @apply border-border;
    color: inherit;
  }
  body{
    overflow-x: hidden;
  }
  body, html, #root {
    @apply bg-background text-foreground;
  }
}

/* Ant Design Theme Overrides for Consistent Styling */
.ant-table {
  @apply bg-card text-card-foreground;
}

.ant-table-thead > tr > th {
  @apply bg-card text-card-foreground border-border;
  color: hsl(var(--card-foreground)) !important;
  background: hsl(var(--card)) !important;
  border-bottom: 1px solid hsl(var(--border)) !important;
  padding: 16px 20px !important; /* Uniform horizontal padding */
}

.ant-table-tbody > tr > td {
  @apply bg-card text-card-foreground border-border;
  color: hsl(var(--card-foreground)) !important;
  background: hsl(var(--card)) !important;
  border-bottom: 1px solid hsl(var(--border)) !important;
  padding: 16px 20px !important; /* Uniform horizontal padding */
}

.ant-table-tbody > tr:hover > td {
  background: hsl(var(--muted)) !important;
}

.ant-card {
  @apply bg-card border-border;
  background: hsl(var(--card)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.ant-card-head {
  @apply border-border;
  background: hsl(var(--card)) !important;
  border-bottom: 1px solid hsl(var(--border)) !important;
}

.ant-card-head-title {
  color: hsl(var(--card-foreground)) !important;
}

.ant-card-body {
  color: hsl(var(--card-foreground)) !important;
}

/* Filter Bar Styling with Blue Palette */
.filter-bar {
  background: hsl(var(--card)) !important;
  border: 1px solid hsl(var(--dashboard-light-blue) / 0.3) !important;
  border-radius: 12px !important;
  padding: 24px !important;
  margin-bottom: 20px !important;
  box-shadow: 0 4px 12px hsl(var(--dashboard-dark-blue) / 0.1) !important;
}

.filter-bar .ant-row {
  align-items: end !important;
  gap: 0 !important;
}

.filter-bar .ant-col {
  display: flex !important;
  flex-direction: column !important;
  justify-content: flex-end !important;
}

.filter-bar .ant-form-item-label > label,
.filter-bar .filter-label,
.filter-bar label {
  color: hsl(var(--card-foreground)) !important;
  font-weight: 600 !important;
  font-size: 13px !important;
  margin-bottom: 8px !important;
  line-height: 1.2 !important;
  display: block !important;
}

.filter-bar .ant-input,
.filter-bar .ant-select-selector,
.filter-bar .ant-picker,
.filter-bar .ant-btn {
  height: 40px !important;
  min-height: 40px !important;
  display: flex !important;
  align-items: center !important;
}

.ant-input {
  @apply bg-card text-card-foreground border-border;
  background: hsl(var(--input)) !important;
  border: 1px solid hsl(var(--dashboard-light-blue) / 0.4) !important;
  color: hsl(var(--foreground)) !important;
  border-radius: 8px !important;
  padding: 10px 14px !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  transition: all 0.3s ease !important;
}

.ant-input:hover {
  border-color: hsl(var(--dashboard-medium-blue)) !important;
  box-shadow: 0 0 0 2px hsl(var(--dashboard-light-blue) / 0.1) !important;
}

.ant-input:focus,
.ant-input-focused {
  border-color: hsl(var(--dashboard-dark-blue)) !important;
  box-shadow: 0 0 0 3px hsl(var(--dashboard-light-blue) / 0.2) !important;
}

.ant-select-selector {
  @apply bg-card text-card-foreground !important;
  background: hsl(var(--input)) !important;
  border: 1px solid hsl(var(--dashboard-light-blue) / 0.4) !important;
  border-radius: 8px !important;
  padding: 10px 14px !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  transition: all 0.3s ease !important;
}

.ant-select:hover .ant-select-selector {
  border-color: hsl(var(--dashboard-medium-blue)) !important;
  box-shadow: 0 0 0 2px hsl(var(--dashboard-light-blue) / 0.1) !important;
}

.ant-select-focused .ant-select-selector {
  border-color: hsl(var(--dashboard-dark-blue)) !important;
  box-shadow: 0 0 0 3px hsl(var(--dashboard-light-blue) / 0.2) !important;
}

.ant-select-arrow {
  color: hsl(var(--dashboard-medium-blue)) !important;
}

.ant-picker {
  background: hsl(var(--input)) !important;
  border: 1px solid hsl(var(--dashboard-light-blue) / 0.4) !important;
  border-radius: 8px !important;
  color: hsl(var(--foreground)) !important;
  padding: 10px 14px !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  transition: all 0.3s ease !important;
}

.ant-picker:hover {
  border-color: hsl(var(--dashboard-medium-blue)) !important;
  box-shadow: 0 0 0 2px hsl(var(--dashboard-light-blue) / 0.1) !important;
}

.ant-picker-focused {
  border-color: hsl(var(--dashboard-dark-blue)) !important;
  box-shadow: 0 0 0 3px hsl(var(--dashboard-light-blue) / 0.2) !important;
}

.ant-btn-primary {
  background: linear-gradient(
    135deg,
    hsl(var(--dashboard-dark-blue)),
    hsl(var(--dashboard-medium-blue))
  ) !important;
  border: none !important;
  color: white !important;
  border-radius: 8px !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 2px 8px hsl(var(--dashboard-dark-blue) / 0.3) !important;
}

.ant-btn-primary:hover {
  background: linear-gradient(
    135deg,
    hsl(var(--dashboard-medium-blue)),
    hsl(var(--dashboard-light-blue))
  ) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px hsl(var(--dashboard-dark-blue) / 0.4) !important;
}

.ant-btn-default {
  background: hsl(var(--card)) !important;
  border: 1px solid hsl(var(--dashboard-light-blue) / 0.4) !important;
  color: hsl(var(--foreground)) !important;
  border-radius: 8px !important;
  transition: all 0.3s ease !important;
}

.ant-btn-default:hover {
  border-color: hsl(var(--dashboard-medium-blue)) !important;
  background: hsl(var(--dashboard-light-blue) / 0.1) !important;
  color: hsl(var(--dashboard-dark-blue)) !important;
}

.ant-tag {
  background: hsl(var(--dashboard-light-blue) / 0.2) !important;
  color: hsl(var(--dashboard-dark-blue)) !important;
  border: 1px solid hsl(var(--dashboard-medium-blue) / 0.3) !important;
  border-radius: 6px !important;
  padding: 2px 8px !important;
  font-weight: 500 !important;
}

.ant-tag-blue {
  background: hsl(var(--dashboard-medium-blue) / 0.2) !important;
  color: hsl(var(--dashboard-dark-blue)) !important;
  border-color: hsl(var(--dashboard-medium-blue)) !important;
}

.ant-tag-green {
  background: hsl(var(--dashboard-light-blue) / 0.3) !important;
  color: hsl(var(--dashboard-dark-blue)) !important;
  border-color: hsl(var(--dashboard-light-blue)) !important;
}

.ant-statistic-title {
  color: hsl(var(--foreground)) !important;
  font-weight: 500 !important;
}

.ant-statistic-content-value {
  color: hsl(var(--foreground)) !important;
  font-weight: 700 !important;
}

.ant-pagination-item {
  @apply bg-card border-border;
  background: hsl(var(--card)) !important;
  border: 1px solid hsl(var(--dashboard-light-blue) / 0.3) !important;
  border-radius: 6px !important;
  margin: 0 4px !important;
}

.ant-pagination-item a {
  color: hsl(var(--foreground)) !important;
}

.ant-pagination-item:hover {
  border-color: hsl(var(--dashboard-medium-blue)) !important;
  background: hsl(var(--dashboard-light-blue) / 0.1) !important;
}

.ant-pagination-item-active {
  background: hsl(var(--dashboard-dark-blue)) !important;
  border-color: hsl(var(--dashboard-dark-blue)) !important;
}

.ant-pagination-item-active a {
  color: white !important;
}

.ant-dropdown {
  background: hsl(var(--popover)) !important;
  border: 1px solid hsl(var(--dashboard-light-blue) / 0.3) !important;
  border-radius: 8px !important;
  box-shadow: 0 8px 24px hsl(var(--dashboard-dark-blue) / 0.15) !important;
}

.ant-dropdown-menu {
  background: hsl(var(--popover)) !important;
  border-radius: 8px !important;
}

.ant-dropdown-menu-item {
  color: hsl(var(--popover-foreground)) !important;
  border-radius: 4px !important;
  margin: 2px !important;
}

.ant-dropdown-menu-item:hover {
  background: hsl(var(--dashboard-light-blue) / 0.1) !important;
  color: hsl(var(--dashboard-dark-blue)) !important;
}

.ant-modal-content {
  background: hsl(var(--card)) !important;
  border-radius: 12px !important;
  border: 1px solid hsl(var(--dashboard-light-blue) / 0.3) !important;
}

.ant-modal-header {
  background: hsl(var(--card)) !important;
  border-bottom: 1px solid hsl(var(--dashboard-light-blue) / 0.2) !important;
  border-radius: 12px 12px 0 0 !important;
}

.ant-modal-title {
  color: hsl(var(--card-foreground)) !important;
  font-weight: 600 !important;
}

.ant-modal-body {
  background: hsl(var(--card)) !important;
  color: hsl(var(--card-foreground)) !important;
}

/* Table Enhancements */
.ant-table-container {
  border-radius: 12px !important;
  overflow: hidden !important;
  border: 1px solid hsl(var(--dashboard-light-blue) / 0.2) !important;
}

.ant-table-wrapper {
  box-shadow: 0 4px 12px hsl(var(--dashboard-dark-blue) / 0.1) !important;
}

/* Form Controls in Filter Bars */
.filter-label {
  color: hsl(var(--card-foreground)) !important;
  font-weight: 600 !important;
  font-size: 13px !important;
  margin-bottom: 6px !important;
  display: block !important;
  line-height: 1.2 !important;
}

/* Search Input Prefix Icon Styling */
.ant-input-prefix {
  color: hsl(var(--dashboard-medium-blue)) !important;
  margin-right: 8px !important;
}

/* Filter Bar Alignment and Spacing */
.filter-bar .ant-form-item {
  margin-bottom: 0 !important;
}

.filter-bar .ant-form-item-label {
  padding-bottom: 6px !important;
}

/* Dark theme Ant Design overrides for better text contrast */
.dark .ant-select-item-option-content {
  color: hsl(var(--foreground)) !important;
}

.dark .ant-select-dropdown {
  background: hsl(var(--popover)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dark .ant-table-placeholder {
  color: hsl(var(--muted-foreground)) !important;
}

.dark .ant-popover-content {
  background: hsl(var(--popover)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dark .ant-popover-arrow::before {
  background: hsl(var(--popover)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dark .ant-btn-text {
  color: hsl(var(--foreground)) !important;
}

.dark .ant-btn-text:hover {
  color: hsl(var(--primary)) !important;
  background: hsl(var(--muted)) !important;
}

.dark .ant-tag-blue {
  background: hsl(var(--dashboard-medium-blue) / 0.2) !important;
  color: hsl(var(--dashboard-light-blue)) !important;
  border-color: hsl(var(--dashboard-medium-blue) / 0.5) !important;
}

.dark .ant-tag-green {
  background: hsl(var(--dashboard-light-blue) / 0.2) !important;
  color: hsl(var(--dashboard-light-blue)) !important;
  border-color: hsl(var(--dashboard-light-blue) / 0.5) !important;
}

.dark .ant-tag-orange {
  background: hsl(var(--dashboard-medium-blue) / 0.3) !important;
  color: hsl(var(--dashboard-light-blue)) !important;
  border-color: hsl(var(--dashboard-medium-blue) / 0.6) !important;
}

.dark .ant-tag-red {
  background: hsl(var(--destructive) / 0.2) !important;
  color: hsl(var(--destructive)) !important;
  border-color: hsl(var(--destructive) / 0.5) !important;
}

/* Typography fixes for dark theme */
.dark .ant-typography,
.dark .ant-typography h1,
.dark .ant-typography h2,
.dark .ant-typography h3,
.dark .ant-typography h4,
.dark .ant-typography h5,
.dark .ant-typography h6 {
  color: hsl(var(--foreground)) !important;
}

.dark .ant-typography.ant-typography-secondary {
  color: hsl(var(--muted-foreground)) !important;
}

/* Alert component fixes */
.dark .ant-alert {
  background: hsl(var(--card)) !important;
  border: 1px solid hsl(var(--border)) !important;
}

.dark .ant-alert-message {
  color: hsl(var(--foreground)) !important;
}

.dark .ant-alert-description {
  color: hsl(var(--muted-foreground)) !important;
}

/* List component fixes */
.dark .ant-list-item {
  color: hsl(var(--foreground)) !important;
  border-color: hsl(var(--border)) !important;
}

.dark .ant-list-item-meta-title {
  color: hsl(var(--foreground)) !important;
}

.dark .ant-list-item-meta-description {
  color: hsl(var(--muted-foreground)) !important;
}

.dark .ant-tag-purple {
  background: hsl(var(--dashboard-dark-blue) / 0.3) !important;
  color: hsl(var(--dashboard-light-blue)) !important;
  border-color: hsl(var(--dashboard-dark-blue) / 0.6) !important;
}

/* Statistics Cards - ensuring visibility in dark theme */
.dark .ant-statistic .ant-statistic-title {
  color: hsl(var(--dashboard-light-blue)) !important;
}

.dark .ant-statistic .ant-statistic-content-value {
  color: hsl(var(--foreground)) !important;
}

/* 3D Pie Chart Styles */
.perspective-1000 {
  perspective: 1000px;
}

.-rotate-x-12 {
  transform: rotateX(-12deg);
}

/* Enhanced 3D effects */
.pie-3d-enhanced {
  transform-style: preserve-3d;
  filter: drop-shadow(0 10px 25px hsl(var(--dashboard-dark-blue) / 0.3))
    drop-shadow(0 5px 15px hsl(0, 0%, 0%, 0.2));
}

/* Custom utilities for 3D transforms */
.transform-3d {
  transform-style: preserve-3d;
}

.rotate-x-25 {
  transform: rotateX(25deg);
}

.rotate-y-neg5 {
  transform: rotateY(-5deg);
}

/* Hide scrollbars while keeping scroll functionality */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Draggable form sections styling */
.draggable-section {
  transition: all 0.2s ease;
}

.draggable-section:hover {
  border-color: hsl(var(--primary));
  box-shadow: 0 2px 8px hsl(0, 0%, 0%, 0.1);
}

.draggable-section .ant-collapse-header {
  transition: all 0.2s ease;
}

.draggable-section .ant-collapse-header:hover {
  background-color: hsl(var(--muted));
}

.custom-calendar .rmdp-day.rmdp-range {
  background-color: theme("colors.sidebar[primary]") !important;
  color: white !important;
}

/* Optional: first and last days in the range */
.custom-calendar .rmdp-day.rmdp-range-start,
.custom-calendar .rmdp-day.rmdp-range-end {
  background-color: theme("colors.sidebar[primary]") !important;
  color: white !important;
}
/* Global scrollbar styling aligned with theme (WebKit + Firefox) */
/* Firefox */
* {
  scrollbar-width: thin;
  scrollbar-color: hsl(var(--border)) hsl(var(--muted));
}

.dark * {
  scrollbar-color: hsl(var(--dashboard-light-blue)) hsl(var(--muted));
}

/* WebKit (Chromium, Safari, Edge) */
*::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

*::-webkit-scrollbar-track {
  background: hsl(var(--muted));
}

*::-webkit-scrollbar-thumb {
  background-color: hsl(var(--border));
  border-radius: 9999px;
  border: 2px solid hsl(var(--muted));
}

*::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--primary));
}

.dark *::-webkit-scrollbar-thumb {
  background-color: hsl(var(--dashboard-medium-blue));
  border-color: hsl(var(--muted));
}

.dark *::-webkit-scrollbar-thumb:hover {
  background-color: hsl(var(--dashboard-light-blue));
}

.custom-calendar .rmdp-day.rmdp-range {
  background-color: theme("colors.sidebar[primary]") !important;
  color: white !important;
}
 
/* Optional: first and last days in the range */
.custom-calendar .rmdp-day.rmdp-range-start,
.custom-calendar .rmdp-day.rmdp-range-end {
  background-color: theme("colors.sidebar[primary]") !important;
  color: white !important;
}
