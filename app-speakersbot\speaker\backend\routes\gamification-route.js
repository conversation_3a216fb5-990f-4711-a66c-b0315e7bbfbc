const speakerHistoryController = require("../controllers/gamification-controller");
const verifyToken = require("../middlewares/verify-token");
const router = require("express").Router();

module.exports = (app) => {

    // ------------------------- Speaker Points History Routes -------------------------

    /**
     * GET /speaker/api/v1/points/history
     * Retrieves paginated points history for the authenticated speaker
     * Supports filtering by type, date range, and search functionality
     * Includes comprehensive summary with current balance and statistics
     * 
     * Query Parameters:
     * - page: Page number (default: 1)
     * - limit: Items per page (default: 10, max: 100)
     * - type: Filter by type (opportunity, subscription, gamification)
     * - start_date: Filter from date (YYYY-MM-DD format)
     * - end_date: Filter to date (YYYY-MM-DD format)
     * - search: Search in activity descriptions
     * 
     * Requires: Valid JWT token (handled by global middleware)
     * Returns: Paginated points history with summary statistics and current balance
     */
    router.get("/history", speakerHistoryController.getSpeakerPointsHistory);
    router.post("/process-points", speakerHistoryController.processPoints);

    app.use("/speaker/api/v1/gamification", router);
};