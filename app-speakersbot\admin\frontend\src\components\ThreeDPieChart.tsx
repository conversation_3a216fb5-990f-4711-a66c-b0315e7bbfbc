import React, { useState } from 'react';

interface PieSliceData {
  name: string;
  value: number;
  percentage: number;
}

interface ThreeDPieChartProps {
  data: { name: string; value: number }[];
  colors: string[];
  size?: number;
}

export const ThreeDPieChart: React.FC<ThreeDPieChartProps> = ({ 
  data, 
  colors, 
  size = 200 
}) => {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const [mousePos, setMousePos] = useState({ x: 0, y: 0 });

  // Calculate percentages and angles
  const total = data.reduce((sum, item) => sum + item.value, 0);
  const slices: PieSliceData[] = data.map(item => ({
    ...item,
    percentage: (item.value / total) * 100
  }));

  let currentAngle = 0;
  const sliceAngles = slices.map(slice => {
    const angle = (slice.value / total) * 360;
    const result = { start: currentAngle, end: currentAngle + angle, angle };
    currentAngle += angle;
    return result;
  });

  const createSlicePath = (startAngle: number, endAngle: number, outerRadius: number, innerRadius = 0) => {
    const centerX = size / 2;
    const centerY = size / 2;
    
    const startAngleRad = (startAngle - 90) * Math.PI / 180;
    const endAngleRad = (endAngle - 90) * Math.PI / 180;
    
    const x1 = centerX + outerRadius * Math.cos(startAngleRad);
    const y1 = centerY + outerRadius * Math.sin(startAngleRad);
    const x2 = centerX + outerRadius * Math.cos(endAngleRad);
    const y2 = centerY + outerRadius * Math.sin(endAngleRad);
    
    const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";
    
    let path = `M ${centerX} ${centerY}`;
    path += ` L ${x1} ${y1}`;
    path += ` A ${outerRadius} ${outerRadius} 0 ${largeArcFlag} 1 ${x2} ${y2}`;
    path += ` Z`;
    
    return path;
  };

  const handleMouseMove = (event: React.MouseEvent) => {
    setMousePos({ x: event.clientX, y: event.clientY });
  };

  return (
    <div className="relative w-full h-full flex items-center justify-center">
      <div 
        className="relative"
        style={{ 
          width: size, 
          height: size,
          transform: 'perspective(1000px) rotateX(45deg)',
          transformStyle: 'preserve-3d'
        }}
        onMouseMove={handleMouseMove}
      >
        {/* Shadow base layer */}
        <svg
          width={size}
          height={size}
          className="absolute"
          style={{ 
            transform: 'translateZ(-20px) translateY(10px)',
            filter: 'blur(8px)',
            opacity: 0.3
          }}
        >
          <defs>
            <radialGradient id="shadowGradient" cx="50%" cy="50%" r="50%">
              <stop offset="0%" stopColor="rgba(0, 156, 201, 0.4)" />
              <stop offset="100%" stopColor="rgba(0, 0, 0, 0.6)" />
            </radialGradient>
          </defs>
          {sliceAngles.map((slice, index) => (
            <path
              key={`shadow-${index}`}
              d={createSlicePath(slice.start, slice.end, size * 0.35)}
              fill="url(#shadowGradient)"
            />
          ))}
        </svg>

        {/* Main pie layers */}
        {sliceAngles.map((slice, index) => {
          const isHovered = hoveredIndex === index;
          const baseColor = colors[index % colors.length];
          
          return (
            <div
              key={index}
              className="absolute inset-0"
              style={{
                transform: isHovered 
                  ? 'translateZ(10px) scale(1.05)' 
                  : 'translateZ(0px)',
                transition: 'all 0.3s ease-out'
              }}
            >
              {/* Extruded sides */}
              <svg
                width={size}
                height={size}
                className="absolute"
                style={{ 
                  transform: 'translateZ(-15px)',
                  filter: isHovered 
                    ? 'drop-shadow(0 0 20px rgba(163, 220, 237, 0.6))' 
                    : 'none'
                }}
              >
                <defs>
                  <linearGradient id={`sideGradient-${index}`} x1="0%" y1="0%" x2="100%" y2="100%">
                    <stop offset="0%" stopColor={baseColor} />
                    <stop offset="50%" stopColor={`${baseColor}CC`} />
                    <stop offset="100%" stopColor={`${baseColor}88`} />
                  </linearGradient>
                </defs>
                <path
                  d={createSlicePath(slice.start, slice.end, size * 0.35)}
                  fill={`url(#sideGradient-${index})`}
                  stroke="rgba(255, 255, 255, 0.1)"
                  strokeWidth="1"
                />
              </svg>

              {/* Top surface */}
              <svg
                width={size}
                height={size}
                className="absolute cursor-pointer"
                onMouseEnter={() => setHoveredIndex(index)}
                onMouseLeave={() => setHoveredIndex(null)}
              >
                <defs>
                  <radialGradient id={`topGradient-${index}`} cx="30%" cy="30%" r="70%">
                    <stop offset="0%" stopColor={`${baseColor}FF`} />
                    <stop offset="70%" stopColor={baseColor} />
                    <stop offset="100%" stopColor={`${baseColor}DD`} />
                  </radialGradient>
                </defs>
                <path
                  d={createSlicePath(slice.start, slice.end, size * 0.35)}
                  fill={`url(#topGradient-${index})`}
                  stroke="rgba(255, 255, 255, 0.2)"
                  strokeWidth="2"
                  style={{
                    filter: isHovered 
                      ? 'brightness(1.2) drop-shadow(0 0 15px rgba(163, 220, 237, 0.5))' 
                      : 'brightness(1)'
                  }}
                />
              </svg>

              {/* Highlight layer on hover */}
              {isHovered && (
                <svg
                  width={size}
                  height={size}
                  className="absolute pointer-events-none"
                  style={{ 
                    transform: 'translateZ(2px)',
                    animation: 'pulse 2s infinite'
                  }}
                >
                  <path
                    d={createSlicePath(slice.start, slice.end, size * 0.37)}
                    fill="none"
                    stroke="#A3DCED"
                    strokeWidth="3"
                    style={{
                      filter: 'drop-shadow(0 0 10px #A3DCED)'
                    }}
                  />
                </svg>
              )}
            </div>
          );
        })}
      </div>

      {/* Custom Tooltip */}
      {hoveredIndex !== null && (
        <div
          className="fixed z-50 pointer-events-none"
          style={{
            left: mousePos.x + 10,
            top: mousePos.y - 40,
            transform: 'translateZ(100px)'
          }}
        >
          <div className="bg-card border border-border rounded-xl shadow-lg px-3 py-2 text-sm">
            <div className="font-semibold text-foreground">
              {slices[hoveredIndex].name}
            </div>
            <div className="text-muted-foreground">
              {slices[hoveredIndex].value} ({slices[hoveredIndex].percentage.toFixed(1)}%)
            </div>
          </div>
        </div>
      )}
    </div>
  );
};