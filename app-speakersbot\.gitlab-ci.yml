stages:
  - pre
  - build
  - upload
  - deploy
  - post

# --- STAGING (develop branch) ---
include:
  - local: ci/staging/admin.yml
    rules:
      - if: '$CI_COMMIT_BRANCH == "develop"'
        changes:
          - admin/frontend/**/*
          - admin/node/**/*
          
  - local: ci/staging/python.yml
    rules:
      - if: '$CI_COMMIT_BRANCH == "develop"'
        changes:
          - admin/python/**/*
          
  - local: ci/staging/speaker.yml
    rules:
      - if: '$CI_COMMIT_BRANCH == "develop"'
        changes:
          - speaker/frontend/**/*
          - speaker/backend/**/*

# --- PRODUCTION (main branch) ---
  # - local: ci/production/node.yml
  #   rules:
  #     - if: '$CI_COMMIT_BRANCH == "main"'
  #       changes:
  #         - admin/node/**/*
  #         - speaker/backend/**/*
  
  # - local: ci/production/python.yml
  #   rules:
  #     - if: '$CI_COMMIT_BRANCH == "main"'
  #       changes:
  #         - admin/python/**/*
  
  # - local: ci/production/react.yml
  #   rules:
  #     - if: '$CI_COMMIT_BRANCH == "main"'
  #       changes:
  #         - admin/frontend/**/*
  #         - speaker/frontend/**/*

# --- Debug job ---
debug_test:
  stage: pre
  script:
    - echo "Pipeline is running on branch $CI_COMMIT_BRANCH"
    - echo "Changed files in this commit:"
    - git diff --name-only $CI_COMMIT_BEFORE_SHA $CI_COMMIT_SHA
    - echo "Listing included configs:"
    - ls -R ci/
