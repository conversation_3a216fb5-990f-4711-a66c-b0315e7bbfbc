"""
ScrapeOps Proxy Service Integration - Official Implementation
Follows the official ScrapeOps documentation exactly.
Reference: https://scrapeops.io/docs/intro/
"""
 
import asyncio
import logging
import random
from typing import Optional, Dict, Any, Tuple
import httpx
from app.config.config import config
 
logger = logging.getLogger(__name__)
 
 
class ScrapeOpsProxyService:
    """
    ScrapeOps Proxy API Aggregator Service following official documentation.
   
    Official API endpoint: https://proxy.scrapeops.io/v1/
    Documentation: https://scrapeops.io/docs/intro/
   
    Features:
    - Smart proxy rotation and management
    - Anti-bot bypass capabilities  
    - JavaScript rendering support
    - Geo-targeting options
    - Only charged for successful requests (200 and 404 status codes)
    """
   
    def __init__(self):
        self.api_key = config.SCRAPEOPS_API_KEY
        self.enabled = config.SCRAPEOPS_PROXY_ENABLED
        self.country = config.SCRAPEOPS_COUNTRY
        self.render_js = config.SCRAPEOPS_RENDER_JS
       
        # Official ScrapeOps API endpoints as per documentation
        self.api_endpoint = "https://proxy.scrapeops.io/v1/"
       
        # Performance tracking
        self.request_count = 0
        self.success_count = 0
        self.failure_count = 0
       
        logger.info(f"ScrapeOps Proxy Service initialized - Enabled: {self.enabled}, API Key: {'***' if self.api_key else 'Not set'}")
   
    def is_available(self) -> bool:
        """Check if proxy service is available and configured."""
        return self.enabled and bool(self.api_key)
   
    def get_proxy_stats(self) -> Dict[str, Any]:
        """Get proxy usage statistics."""
        return {
            "total_requests": self.request_count,
            "successful_requests": self.success_count,
            "failed_requests": self.failure_count,
            "success_rate": (self.success_count / self.request_count * 100) if self.request_count > 0 else 0
        }
   
    def _build_scrapeops_params(self, target_url: str, **kwargs) -> Dict[str, Any]:
        """
        Build parameters for ScrapeOps API following official documentation.
       
        Official format as per ScrapeOps docs:
        curl -k "https://proxy.scrapeops.io/v1/?api_key=YOUR_API_KEY&url=http://httpbin.org/anything"
       
        Optional parameters:
        - country: Country code for geo-targeting (us, uk, de, etc.)
        - render_js: Enable JavaScript rendering (true/false)
        """
        params = {
            'api_key': self.api_key,
            'url': target_url,
        }
       
        # Add optional parameters only if they're set
        if self.country and self.country.strip():
            params['country'] = self.country.strip()
            logger.debug(f"Added country targeting: {self.country}")
       
        if self.render_js:
            params['render_js'] = 'true'
            logger.debug("JavaScript rendering enabled")
       
        # Add any additional parameters (filtered to avoid conflicts)
        allowed_params = ['country', 'render_js', 'session_id', 'premium', 'keep_headers']
        for key, value in kwargs.items():
            if key in allowed_params:
                params[key] = value
                logger.debug(f"Added custom parameter: {key}={value}")
       
        return params
   
    async def make_request(
        self,
        target_url: str,
        method: str = "GET",
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        timeout: float = 30.0,
        **kwargs
    ) -> Tuple[bool, Optional[httpx.Response], Optional[str]]:
        """
        Make a request through ScrapeOps Proxy API Aggregator.
       
        This method follows the official ScrapeOps documentation:
        https://scrapeops.io/docs/intro/
       
        Args:
            target_url: The actual website URL you want to scrape
            method: HTTP method (GET/POST) - Note: ScrapeOps primarily supports GET
            headers: Additional headers to send with the request
            params: Additional query parameters for the target URL
            timeout: Request timeout in seconds
            **kwargs: Additional ScrapeOps parameters (country, render_js, etc.)
           
        Returns:
            Tuple of (success, response, error_message)
           
        Note:
            - Only charged for successful requests (200 and 404 status codes)
            - POST requests fall back to direct requests as ScrapeOps API is GET-focused
        """
        if not self.is_available():
            return False, None, "ScrapeOps proxy not available or not configured"
       
        self.request_count += 1
        logger.debug(f"Making ScrapeOps request to: {target_url} (attempt {self.request_count})")
       
        try:
            # Build ScrapeOps parameters following official documentation
            scrapeops_params = self._build_scrapeops_params(target_url, **kwargs)
           
            # Prepare headers for the request
            request_headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
                "Accept-Language": "en-US,en;q=0.5",
                "Accept-Encoding": "gzip, deflate, br",
                "DNT": "1",
                "Connection": "keep-alive",
                "Upgrade-Insecure-Requests": "1",
            }
           
            # Add custom headers if provided
            if headers:
                request_headers.update(headers)
           
            # Make request to ScrapeOps API (official method)
            async with httpx.AsyncClient(timeout=timeout, follow_redirects=True) as client:
                if method.upper() == "GET":
                    logger.debug(f"ScrapeOps GET request with params: {scrapeops_params}")
                    response = await client.get(
                        self.api_endpoint,
                        params=scrapeops_params,
                        headers=request_headers
                    )
                elif method.upper() == "POST":
                    # ScrapeOps API doesn't directly support POST, so we'll use direct request
                    logger.warning("POST requests not directly supported by ScrapeOps API, using direct request")
                    response = await client.post(
                        target_url,
                        headers=request_headers,
                        params=params,
                        **kwargs
                    )
                else:
                    return False, None, f"Unsupported HTTP method: {method}"
               
                # Check response status
                if response.status_code in [200, 404]:  # ScrapeOps only charges for 200 and 404
                    self.success_count += 1
                    logger.debug(f"ScrapeOps request successful: {response.status_code}")
                    return True, response, None
                else:
                    self.failure_count += 1
                    error_msg = f"ScrapeOps API returned status {response.status_code}: {response.text[:200]}"
                    logger.warning(error_msg)
                    return False, None, error_msg
                   
        except httpx.TimeoutException:
            self.failure_count += 1
            error_msg = f"ScrapeOps request timeout after {timeout} seconds"
            logger.warning(error_msg)
            return False, None, error_msg
        except httpx.ConnectError as e:
            self.failure_count += 1
            error_msg = f"ScrapeOps connection error: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg
        except Exception as e:
            self.failure_count += 1
            error_msg = f"ScrapeOps request failed: {str(e)}"
            logger.error(error_msg)
            return False, None, error_msg
   
    async def get_page_content(
        self,
        url: str,
        timeout: float = 30.0,
        max_retries: int = 3,
        **kwargs
    ) -> Tuple[bool, Optional[str], Optional[str]]:
        """
        Get page content through ScrapeOps proxy with retry logic.
       
        This method provides a convenient way to fetch page content using the
        ScrapeOps Proxy API Aggregator with automatic retry on failure.
       
        Args:
            url: The target URL to fetch content from
            timeout: Request timeout in seconds
            max_retries: Maximum number of retry attempts
            **kwargs: Additional ScrapeOps parameters (country, render_js, etc.)
           
        Returns:
            Tuple of (success, content, error_message)
        """
        logger.debug(f"Fetching page content for: {url}")
       
        for attempt in range(max_retries):
            success, response, error = await self.make_request(
                target_url=url,
                method="GET",
                timeout=timeout,
                **kwargs
            )
           
            if success and response:
                try:
                    content = response.text
                    if content and len(content.strip()) > 0:
                        logger.debug(f"Successfully fetched content ({len(content)} chars) for: {url}")
                        return True, content, None
                    else:
                        error = "Empty response content"
                        logger.warning(f"Empty content received for: {url}")
                except Exception as e:
                    error = f"Failed to extract content: {str(e)}"
                    logger.error(f"Content extraction error for {url}: {e}")
           
            if attempt < max_retries - 1:
                wait_time = 2 ** attempt  # Exponential backoff
                logger.warning(f"Attempt {attempt + 1} failed for {url}: {error}. Retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
            else:
                logger.error(f"All {max_retries} attempts failed for URL: {url}. Final error: {error}")
       
        return False, None, error
 
 
# Global proxy service instance
proxy_service = ScrapeOpsProxyService()
 
 
async def get_proxied_content(url: str, timeout: float = 30.0, **kwargs) -> Tuple[bool, Optional[str], Optional[str]]:
    """
    Convenience function to get content through ScrapeOps proxy.
   
    This is the main function used by the scraper to fetch content through
    the ScrapeOps Proxy API Aggregator.
   
    Args:
        url: Target URL to fetch
        timeout: Request timeout in seconds
        **kwargs: Additional ScrapeOps parameters (country, render_js, etc.)
       
    Returns:
        Tuple of (success, content, error_message)
    """
    return await proxy_service.get_page_content(url, timeout, **kwargs)
 
 
def get_proxy_stats() -> Dict[str, Any]:
    """
    Get proxy service statistics.
   
    Returns:
        Dictionary containing request statistics and success rates
    """
    return proxy_service.get_proxy_stats()
 
 
def is_proxy_available() -> bool:
    """
    Check if proxy service is available and properly configured.
   
    Returns:
        True if ScrapeOps proxy is enabled and API key is set
    """
    return proxy_service.is_available()
 
 
def get_proxy_info() -> Dict[str, Any]:
    """
    Get proxy service configuration information.
   
    Returns:
        Dictionary containing proxy configuration details
    """
    return {
        "enabled": proxy_service.enabled,
        "api_key_set": bool(proxy_service.api_key),
        "country": proxy_service.country,
        "render_js": proxy_service.render_js,
        "api_endpoint": proxy_service.api_endpoint,
        "stats": proxy_service.get_proxy_stats()
    }