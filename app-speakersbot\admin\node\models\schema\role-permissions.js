const connection = require('../connection');
const { DataTypes } = require('sequelize');

const RolePermissions = connection.define('RolePermissions', {
  role_id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    comment: 'Foreign key referencing the role',
  },
  permission_id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    comment: 'Foreign key referencing the permission',
  }
}, {
  tableName: 'role_permissions',
  timestamps: false
});

module.exports = RolePermissions;
