{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --mode development-local", "dev:landing": "vite --config vite.config.landing.ts", "build": "vite build", "build:landing": "vite build --config vite.config.landing.ts", "build:dev": "vite build --mode development", "build:extension": "vite build --config vite.config.extension.ts", "dev:extension": "vite --config vite.config.extension.ts", "build:all": "npm run build && npm run build:landing", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@tanstack/react-query": "^5.83.0", "@types/chrome": "^0.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "lucide-react": "^0.462.0", "react": "^19.0.0", "react-day-picker": "^8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.61.1", "react-resizable-panels": "^2.1.9", "react-redux": "^9.1.2", "react-router-dom": "^6.30.1", "recharts": "^2.15.4", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.9", "zod": "^4.1.8", "@reduxjs/toolkit": "^2.2.7"}, "devDependencies": {"@eslint/js": "^9.35.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^24.4.0", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react-swc": "^4.0.1", "autoprefixer": "^10.4.21", "eslint": "^9.35.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "lovable-tagger": "^1.1.9", "postcss": "^8.5.6", "tailwindcss": "^3.4.16", "typescript": "^5.9.2", "typescript-eslint": "^8.43.0", "vite": "^7.1.5"}}