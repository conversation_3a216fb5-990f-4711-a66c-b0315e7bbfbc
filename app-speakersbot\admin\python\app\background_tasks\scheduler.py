"""
Scheduler for Speaker Opportunities Scraper using APScheduler
This scheduler orchestrates the execution of discovery and scraping jobs
using APScheduler for reliable, configurable scheduling.
Author: Speaker <PERSON>t Team
Version: 1.0
Last Updated: 2025
"""

import asyncio
import signal
import sys
from datetime import datetime
from typing import Dict, Optional
import os

# Add app directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.helpers.database_manager import db_manager
from sqlalchemy.exc import SQLAlchemyError
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger

from app.config.config import config
from app.config.logger import get_logger
from app.models.opportunities_url import OpportunitiesUrl
from app.background_tasks.discovery_worker import DiscoveryWorker
from app.models.setting import Settings

logger = get_logger(__name__, file_name="scraper.log")

class OpportunityScheduler:
    """
    Scheduler for processing pending opportunities URLs and discovery using APScheduler.
    This class manages the scheduling and execution of:
    1. Discovery jobs - URL discovery using dynamic topic distribution
    2. Scraper jobs - Processing pending URLs from the database
    Key Features:
    - Configurable job intervals
    - Comprehensive error handling
    - Job status tracking
    - Graceful shutdown support
    """
    
    def __init__(self):
        """Initialize the scheduler with database connection and configuration."""
        self.Session = db_manager.SessionLocal
        self.scheduler = AsyncIOScheduler()
        # Configuration from environment
        self.discovery_interval_minutes = config.DISCOVERY_INTERVAL_MINUTES
        # Initialize workers
        self.discovery_worker = DiscoveryWorker()
        # Job tracking
        self.job_stats = {
            "discovery": {"runs": 0, "last_run": None, "last_success": None}
        }
        
    def get_pending_urls_count(self) -> int:
        """
        Get count of pending URLs from opportunities_url table.
        
        Returns:
            Number of pending URLs
        """
        try:
            with db_manager.get_session() as session:
                count = session.query(OpportunitiesUrl).filter(
                    OpportunitiesUrl.status == 'pending'
                ).count()
                logger.debug(f"Found {count} pending URLs in database")
                return count
        except SQLAlchemyError as e:
            logger.error(f"Database error fetching pending URLs count: {e}")
            return 0
        except Exception as e:
            logger.error(f"Unexpected error fetching pending URLs count: {e}")
            return 0
    
    def _update_job_stats(self, job_name: str, success: bool = True):
        """
        Update job statistics.
        
        Args:
            job_name: Name of the job ('discovery' or 'scraper')
            success: Whether the job completed successfully
        """
        now = datetime.now()
        self.job_stats[job_name]["runs"] += 1
        self.job_stats[job_name]["last_run"] = now
        
        if success:
            self.job_stats[job_name]["last_success"] = now
    
    async def run_discovery_job(self):
        """
        Run the discovery job using dynamic topic distribution.
        
        This job discovers new URLs using the dynamic topic distribution system
        and stores them in the opportunities_url table for later processing.
        """
        job_start_time = datetime.now()
        success = False
        try:
            # Check settings table for discovery toggle
            try:
                with db_manager.get_session() as session:
                    # Only check the explicit 'run_scraping' setting per project requirement
                    run_scraping_setting = session.query(Settings).filter(Settings.key == 'run_scraping').first()

                    if run_scraping_setting is not None:
                        # Per requirement: only consider the `value` column for run_scraping.
                        # Default to enabled unless the value explicitly represents false.
                        is_enabled = True
                        val = run_scraping_setting.value
                        # Interpret common textual representations of booleans
                        if isinstance(val, str):
                            v = val.strip().lower()
                            if v in ['0', 'false', 'no', 'off']:
                                is_enabled = False
                            elif v in ['1', 'true', 'yes', 'on']:
                                is_enabled = True
                        elif isinstance(val, (int, bool)):
                            # Numeric or boolean stored directly
                            is_enabled = bool(val)

                        if not is_enabled:
                            logger.info("run_scraping setting value is false; skipping discovery run")
                            # update stats but mark as skipped (not a failure)
                            self._update_job_stats("discovery", success=True)
                            return
                    else:
                        # If the key is not present, default to enabled behavior
                        logger.debug("run_scraping setting not found; defaulting to enabled")
            except Exception as e:
                # If settings check fails, log and proceed with discovery (safe default)
                logger.warning(f"Failed to read discovery setting, proceeding with discovery. Error: {e}")

            # Run discovery worker with dynamic distribution
            results = await self.discovery_worker.discover_with_distribution()
            if "error" in results:
                logger.error(f"Discovery job failed: {results['error']}")
                return
            # Log per-engine results
            for engine, result in results.items():
                if not engine.startswith("_") and isinstance(result, dict):
                    urls_saved = result.get("urls_saved", 0)
                    logger.info(f"  {engine}: {urls_saved} URLs")
            success = True
        except Exception as e:
            logger.error(f"Error in discovery job: {e}")
        finally:
            self._update_job_stats("discovery", success)
    

    def start_scheduler(self):
        """
        Start the APScheduler with both discovery and scraper jobs.
        This method configures and starts the scheduler with:
        - Discovery job: Runs at configurable intervals (default: every 2 hours)
        - Scraper job: Runs at configurable intervals (default: every 30 minutes)
        """
        try:
            logger.info("=" * 60)
            logger.info("🚀 Starting Opportunity Scheduler")
            logger.info(f"⏰ Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            # Add discovery job
            self.scheduler.add_job(
                func=self.run_discovery_job,
                trigger=IntervalTrigger(minutes=self.discovery_interval_minutes),
                id='discovery_job',
                name='Discovery Job (Dynamic Topic Distribution)',
                replace_existing=True,
                max_instances=1  # Prevent overlapping runs
            )
            # Start the scheduler
            self.scheduler.start()
            
        except Exception as e:
            logger.error(f"Error starting scheduler: {e}")
            raise
    
    def stop_scheduler(self):
        """
        Stop the APScheduler gracefully.
        
        This method shuts down the scheduler and waits for running jobs to complete.
        """
        try:
            self.scheduler.shutdown(wait=True)
        except Exception as e:
            logger.error(f"Error stopping scheduler: {e}")
    

# Global scheduler instance
scheduler: Optional[OpportunityScheduler] = None


def signal_handler(signum, frame):
    """
    Handle interrupt signals for graceful shutdown.
    
    Args:
        signum: Signal number
        frame: Current stack frame
    """
    global scheduler
    logger.info(f"Received signal {signum} - initiating graceful shutdown")
    
    if scheduler:
        scheduler.stop_scheduler()
    
    logger.info("Scheduler shutdown completed")
    sys.exit(0)


# Register signal handlers for graceful shutdown
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """
    Main function to start the scheduler.
    
    This function initializes the scheduler, starts it, and keeps it running
    until interrupted by a signal or error.
    """
    global scheduler
    
    try:
        # Initialize and start scheduler
        scheduler = OpportunityScheduler()
        scheduler.start_scheduler()
        # Main loop - keep scheduler running
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Scheduler interrupted by user (Ctrl+C)")
    except Exception as e:
        logger.error(f"Fatal error in scheduler: {e}")
        sys.exit(1)
    finally:
        if scheduler:
            logger.info("Performing cleanup...")
            scheduler.stop_scheduler()
            logger.info("Cleanup completed")


if __name__ == "__main__":
    """Run the scheduler when executed directly."""
    asyncio.run(main())