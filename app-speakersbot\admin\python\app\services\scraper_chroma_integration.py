"""
Scraper MySQL Integration Service
Tracks and matches NEW opportunities created by the scraper with ALL speakers
"""

import json
import logging
import sys
import os
from datetime import datetime
from typing import List, Dict, Any, Set, Optional, Tuple
from dataclasses import dataclass

# Add the app directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.background_tasks.match_worker import match_worker
from app.services.speaker_matching_service import matching_service
from app.models.opportunities import SpeakerOpportunity as Opportunity
from app.models.speakers import Speaker
from app.models.speaker_details import SpeakerDetails
from app.config.config import config
from app.helpers.database_manager import db_manager

# Setup logging
logger = logging.getLogger(__name__)


@dataclass
class MatchingConfig:
    """Configuration constants for matching operations."""
    DEFAULT_TOP_K: int = 20
    MAX_SPEAKERS_FOR_SCORING: int = 100
    MIN_SCORE_THRESHOLD: float = 0.58
    BATCH_PROCESSING_SIZE: int = 50


@dataclass
class MatchingResult:
    """Structured result data for matching operations."""
    success: bool
    total_matches: int = 0
    llm_enhanced: int = 0
    hybrid_only: int = 0
    saved_to_db: int = 0
    prefilled: int = 0
    prefill_errors: int = 0
    processing_time: float = 0.0
    error_message: Optional[str] = None


class OptimizedScraperMySQLIntegration:
    """
    Optimized service for integrating scraper with MySQL for new opportunities.
    
    This service implements the reverse of the API flow:
    - API Flow: 1 speaker + all future opportunities → MySQL scoring
    - Background Flow: 1 opportunity + all speakers → MySQL scoring
    """
    
    def __init__(self):
        """Initialize the integration service with optimized components."""
        self.config = MatchingConfig()
    
    def match_new_opportunities_with_speakers(self, new_opportunity_ids: List[int]) -> Dict[str, Any]:
        """
        Match newly added opportunities with speakers using optimized flow.
        Args:
            new_opportunity_ids: List of opportunity IDs to match
        Returns:
            Dictionary containing matching results and statistics
        """
        start_time = datetime.now()
        try:
            # Validate inputs
            if not new_opportunity_ids:
                return self._create_error_response("No opportunity IDs provided")
            # Process each opportunity
            results = self._process_opportunities_batch(new_opportunity_ids)
            # Calculate final statistics
            processing_time = (datetime.now() - start_time).total_seconds()
            results.processing_time = processing_time
            return self._format_success_response(results)
            
        except Exception as e:
            processing_time = (datetime.now() - start_time).total_seconds()
            logger.error(f"🎯 Error in optimized matching after {processing_time:.2f}s: {e}")
            return self._create_error_response(f"Matching failed: {str(e)}")
    
    def _process_opportunities_batch(self, opportunity_ids: List[int]) -> MatchingResult:
        """
        Process a batch of opportunities for matching.
        Args:
            opportunity_ids: List of opportunity IDs to process
        Returns:
            MatchingResult with aggregated statistics
        """
        result = MatchingResult(success=True)
        
        with db_manager.get_session() as session:
            # Get opportunities from database
            opportunities = session.query(Opportunity).filter(
                Opportunity.id.in_(opportunity_ids)
            ).all()
            
            if not opportunities:
                return result
            
            # Process each opportunity
            for opportunity in opportunities:
                try:
                    opportunity_result = self._process_single_opportunity(opportunity)
                    self._aggregate_results(result, opportunity_result)
                    
                except Exception as e:
                    logger.error(f"🎯 Error processing opportunity {opportunity.id}: {e}")
                    result.prefill_errors += 1
        
        return result
    
    def _process_single_opportunity(self, opportunity: Opportunity) -> MatchingResult:
        """
        Process a single opportunity for matching with batch processing.
        Args:
            opportunity: Opportunity object to process
        Returns:
            MatchingResult for this opportunity
        """
        batch_size = 50  # Process speakers in batches of 50
        all_matches = []
        offset = 0
        batch_number = 1
        
        while True:
            logger.info(f"Processing speaker batch {batch_number} for opportunity {opportunity.id} (offset={offset})")
            
            # Get batch of speakers
            speakers = self._get_all_speakers_from_mysql(limit=batch_size, offset=offset)
            
            if not speakers:
                logger.info(f"No more speakers found at offset {offset}")
                break
            
            # Calculate hybrid scores for this batch
            batch_matches = self._calculate_hybrid_scores_batch(opportunity, speakers)
            
            if batch_matches:
                all_matches.extend(batch_matches)
                logger.info(f"Batch {batch_number}: Processed {len(batch_matches)} matches")
            
            # Move to next batch
            offset += batch_size
            batch_number += 1
            
            # Check if we have more speakers
            total_count = self._get_speakers_count()
            if offset >= total_count:
                break
        
        if not all_matches:
            return MatchingResult(success=True)
        
        enhanced_matches = []  # No LLM enhancement
        remaining_matches = all_matches  # Use all matches as remaining
        
        # Step 5: Combine and save results
        final_matches = self._combine_and_save_matches(
            enhanced_matches, 
            remaining_matches, 
            all_matches
        )
        return MatchingResult(
            success=True,
            total_matches=len(final_matches),
            llm_enhanced=len(enhanced_matches),
            hybrid_only=len(remaining_matches),
            saved_to_db=len(final_matches),
        )
    
    def _get_all_speakers_from_mysql(self, limit: int = None, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get speakers from MySQL database with batch processing.
        Args:
            limit: Maximum number of speakers to return (for batch processing)
            offset: Number of speakers to skip (for batch processing)
        Returns:
            List of speaker dictionaries with basic data
        """
        try:
            with db_manager.get_session() as session:
                # Build query with optional pagination
                query = session.query(Speaker).filter(Speaker.status == 'active')
                
                # Apply pagination if specified
                if limit:
                    query = query.limit(limit).offset(offset)
                
                speakers = query.all()
                
                if not speakers:
                    logger.warning("No speakers found in MySQL database")
                    return []
                
                # Convert to dictionary format
                speaker_list = []
                for speaker in speakers:
                    speaker_data = {
                        'speaker_id': speaker.id,
                        'name': speaker.name or 'Unknown',
                        'email': speaker.email or '',
                        'vector_similarity_score': 0.0,  # No vector similarity anymore
                        'metadata': {
                            'name': speaker.name or 'Unknown',
                            'email': speaker.email or '',
                            'primary_category': speaker.primary_category or '',
                            'subcategory': speaker.subcategory or '',
                            'topic': speaker.topic or ''
                        }
                    }
                    speaker_list.append(speaker_data)
                return speaker_list
        except Exception as e:
            logger.error(f"🎯 Error getting speakers from MySQL: {e}")
            return []

    def _get_speakers_count(self) -> int:
        """Get total count of active speakers."""
        try:
            with db_manager.get_session() as session:
                count = session.query(Speaker).filter(Speaker.status == 'active').count()
                return count
        except Exception as e:
            logger.error(f"Error getting speakers count: {e}")
            return 0
    
# ChromaDB-related methods removed - using MySQL only
    def _calculate_hybrid_scores_batch(self, opportunity: Opportunity, speakers: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Calculate hybrid scores for all speakers with the opportunity.
        Args:
            opportunity: Opportunity object
            speakers: List of speaker data with similarity scores
        Returns:
            List of match dictionaries with hybrid scores
        """
        all_matches = []
        for i, speaker_data in enumerate(speakers):
            try:
                speaker_id = speaker_data['speaker_id']
                # Get comprehensive speaker data
                enhanced_speaker_data, error = matching_service.get_speaker_data(speaker_id)
                if error or enhanced_speaker_data is None:
                    logger.warning(f"Failed to get speaker data for {speaker_id}: {error}")
                    continue
                
                # Add vector similarity score (always 0.0 for MySQL-only approach)
                enhanced_speaker_data['vector_similarity_score'] = 0.0
                
                # Prepare opportunity data
                opp_data = self._prepare_opportunity_data(opportunity, 0.0)
                
                # Calculate hybrid scores
                scores = match_worker._calculate_hybrid_scores(enhanced_speaker_data, opp_data)
                
                # Create match record
                match = {
                    "speaker_id": speaker_id,
                    "opportunity_id": opportunity.id,
                    "speaker_email": enhanced_speaker_data.get('email', ''),
                    "created_at": datetime.now().isoformat(),
                    "ai_match": False,
                    **scores
                }
                
                all_matches.append(match)
                
            except Exception as e:
                logger.error(f"Error calculating hybrid score for speaker {speaker_data.get('speaker_id')}: {e}")
                continue
        
        logger.info(f"🎯 Calculated hybrid scores for {len(all_matches)} matches")
        return all_matches
    
    def _prepare_opportunity_data(self, opportunity: Opportunity, vector_score: float) -> Dict[str, Any]:
        """
        Prepare opportunity data for hybrid scoring.
        
        Args:
            opportunity: Opportunity object
            vector_score: Vector similarity score
            
        Returns:
            Dictionary with opportunity data
        """
        return {
            "id": opportunity.id,
            "title": opportunity.title,
            "description": opportunity.description,
            "subcategory": opportunity.subcategory,
            "organization": opportunity.organization,
            "event_type": opportunity.event_type,
            "industry": opportunity.industry,
            "city": opportunity.city,
            "state": opportunity.state,
            "country": opportunity.country,
            "venue": opportunity.venue,
            "is_virtual": opportunity.is_virtual,
            "start_date": opportunity.start_date,
            "end_date": opportunity.end_date,
            "vector_similarity_score": 0.0  # No vector similarity anymore
        }
    def _combine_and_save_matches(self, enhanced_matches: List[Dict[str, Any]], 
                                 remaining_matches: List[Dict[str, Any]], 
                                 all_matches: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        DISABLED: LLM combination disabled - using MySQL and manual scoring only.
        
        Args:
            enhanced_matches: LLM enhanced matches (always empty - disabled)
            remaining_matches: Lower-scoring matches (hybrid only)
            all_matches: All calculated matches (for validation)
            
        Returns:
            Combined list of final matches (using all_matches directly)
        """
        logger.info("LLM combination disabled - using MySQL and manual scoring only")
        # Use all matches directly since LLM is disabled
        final_matches = all_matches
        
        # Mark all matches as not AI enhanced
        for match in final_matches:
            match["ai_match"] = False
        
        # Save to database
        saved_count = matching_service.save_matches_to_database(final_matches)
        
        logger.info(f"🎯 Saved {saved_count} matches to database (MySQL + manual scoring only)")
        return final_matches
    
    
    def _aggregate_results(self, result: MatchingResult, opportunity_result: MatchingResult) -> None:
        """Aggregate results from individual opportunity processing."""
        result.total_matches += opportunity_result.total_matches
        result.llm_enhanced += opportunity_result.llm_enhanced
        result.hybrid_only += opportunity_result.hybrid_only
        result.saved_to_db += opportunity_result.saved_to_db
        result.prefilled += opportunity_result.prefilled
        result.prefill_errors += opportunity_result.prefill_errors
    
    def _create_error_response(self, error_message: str) -> Dict[str, Any]:
        """Create standardized error response."""
        return {
            "success": False,
            "error": error_message,
            "matches_created": 0
        }
    
    def _format_success_response(self, result: MatchingResult) -> Dict[str, Any]:
        """Create standardized success response."""
        return {
            "success": True,
            "message": f"Processed opportunities, created {result.total_matches} matches",
            "matches_created": result.total_matches,
            "llm_enhanced": result.llm_enhanced,
            "hybrid_only": result.hybrid_only,
            "saved_to_db": result.saved_to_db,
            "prefilled": result.prefilled,
            "prefill_errors": result.prefill_errors,
            "processing_time_seconds": round(result.processing_time, 2)
        }


# Backward compatibility - keep the old class name for existing code
ScraperChromaIntegration = OptimizedScraperMySQLIntegration
