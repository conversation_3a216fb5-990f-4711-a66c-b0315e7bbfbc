from sqlalchemy import create_engine, Column, Integer, String, Text, Float, Date, Boolean
from app.models.base import Base
from app.config.config import config

class SpeakerForm(Base):
    __tablename__ = "speaker_form"
    form_id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), nullable=True)
    email = Column(String(255), nullable=True)
    location = Column(String(255), nullable=True)
    hobbies = Column(Text)
    experience_years = Column(Float)
    expertise_area = Column(Text)
    event_type_preference = Column(String(255))
    available_dates = Column(Text)
    compensation_expectation = Column(Text)
    travel_preference = Column(Text)
    organization = Column(String(255))  # from speaker_opportunities
    event_type = Column(String(255))    # from speaker_opportunities
    topic_keywords = Column(Text)       # from speaker
    bio_summary = Column(Text)          # from speaker
    feedback = Column(Text)             # not in either
    website = Column(String(255))       # not in either
    linkedin = Column(String(255))      # not in either
    phone = Column(String(50))          # not in either
    is_virtual = Column(Boolean, default=False)  # from speaker_opportunities


def create_speaker_form_table():
    db_url = config.get_database_url()
    engine = create_engine(db_url)
    Base.metadata.create_all(engine)

if __name__ == "__main__":
    create_speaker_form_table()
    print("Table 'speaker_form' created in MySQL database")
