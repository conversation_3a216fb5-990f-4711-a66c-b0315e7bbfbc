from fastapi import APIRouter, HTTPException
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from app.config.logger import get_logger
from app.background_tasks.discovery_worker import DiscoveryWorker
from app.services.scraper_service import build_row

logger = get_logger(__name__, file_name="scraper.log")

router = APIRouter(prefix="/discovery", tags=["discovery"])

@router.post("/trigger-manual")
async def trigger_manual_discovery():
    """
    Manually trigger the discovery worker to process opportunity discovery.
    
    This endpoint allows manual execution of the discovery process that normally
    runs on a scheduled basis. It triggers the discovery worker to:
    - Discover new opportunities from configured sources
    - Process and categorize discovered opportunities
    - Update the opportunity database
    
    Returns:
        dict: Success message with execution details
        
    Raises:
        HTTPException: If the discovery process fails
    """
    try:
        logger.info("Manual discovery trigger requested")
        from app.background_tasks.discovery_worker import run_discovery_worker
        await run_discovery_worker()
        logger.info("Manual discovery trigger completed successfully")
        return {
            "message": "Discovery process triggered successfully",
            "status": "completed"
        }
    except Exception as e:
        logger.error(f"Error in manual discovery trigger: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error triggering discovery process: {str(e)}")



@router.post("/test-topic-engine")
async def test_discover_topic_with_engine(topic: str, engine: str):
    try:
        
        # Create DiscoveryWorker instance and call the method
        discovery_worker = DiscoveryWorker()
        urls_discovered = await discovery_worker.discover_topic_with_engine(topic=topic,engine=engine)
        return {
            "message": f"Discovery completed for topic.",
            "urls_discovered": urls_discovered,
            "status": "success"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500, 
            detail=f"Error during discovery test: {str(e)}"
        )


class BuildRowTestRequest(BaseModel):
    """Request model for testing build_row function"""
    url: str = Field(..., description="URL to extract opportunity data from", example="https://example.com/call-for-speakers")


@router.post("/test-build-row", 
             summary="[TEST] Test build_row Function",
             response_model=Dict[str, Any])
async def test_build_row(request: BuildRowTestRequest) -> Dict[str, Any]:
    """
    Test endpoint: Call build_row function directly with a URL for debugging.
    
    Args:
        request: BuildRowTestRequest containing URL and optional topic
        
    Returns:
        Dictionary with extracted opportunity data (testing only)
        
    Raises:
        HTTPException: If processing fails
    """
    try:
        logger.info(f"Test build_row requested for URL: {request.url}")
        
        # Create a mock hit object similar to what search results would provide
        hit = {
            "url": request.url,
            "title": "",  # Will be extracted from page
            "content": "",  # Will be extracted from page
            "provider": "test"
        }
        
        # Call build_row function directly
        logger.info(f"Calling build_row with URL: {request.url}")
        result = await build_row(
            topic="",
            hit=hit,
            source_url=request.url,
            html=None,
            soup=None,
            page_text=None,
            opportunity_url_id=None
        )

        if result is None:
            logger.warning(f"build_row returned None for URL: {request.url}")
            return {
                "message": "No opportunity data extracted (filtered out or not a valid opportunity)",
                "url": request.url,
                "result": None,
                "status": "filtered"
            }
        
        # Check if it was skipped for a specific reason
        if isinstance(result, dict) and result.get("skip_reason"):
            logger.info(f"Opportunity skipped: {result.get('skip_reason')}")
            return {
                "message": f"Opportunity skipped: {result.get('skip_reason')}",
                "url": request.url,
                "result": result,
                "status": "skipped"
            }
        
        logger.info(f"Successfully extracted data for URL: {request.url}")
        return {
            "message": "Successfully extracted opportunity data",
            "url": request.url,
            "result": result,
            "status": "success"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in test build_row: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error testing build_row function: {str(e)}"
        )