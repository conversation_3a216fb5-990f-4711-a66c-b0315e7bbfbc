"""
Voiceprint Service
==================
Handles voiceprint file processing including:
- Downloading files from S3
- Processing audio files
- Processing video files
- Storing metrics in database
- File cleanup
"""

import os
import re
import json
import uuid
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

from app.config.logger import get_logger
from app.config.config import config
from app.helpers.s3_service import S3Service
from app.helpers.database_manager import db_manager
from app.models.speakers import Speaker
from app.services.audio_analysis_service import AudioAnalysisService
from app.services.video_analysis_service import VideoAnalysisService
from app.services.text_analysis_service import TextAnalysisService
from openai import OpenAI

logger = get_logger(__name__, file_name="voiceprint.log")


class VoiceprintService:
    """Service for processing voiceprint files and storing metrics."""
    
    def __init__(self):
        """Initialize the voiceprint service."""
        self.s3_service = S3Service()
        self.audio_analysis = AudioAnalysisService()
        self.video_analysis = VideoAnalysisService()
        self.text_analysis = TextAnalysisService()
        self.temp_base_dir = Path("voiceprint")
        self.openai_client = None
        
        # Initialize OpenAI client if API key is available
        if config.OPENAI_API_KEY:
            self.openai_client = OpenAI(api_key=config.OPENAI_API_KEY)
        
        # Create temp directories if they don't exist
        self.temp_base_dir.mkdir(exist_ok=True)
        (self.temp_base_dir / "audio" / "temp").mkdir(parents=True, exist_ok=True)
        (self.temp_base_dir / "video" / "temp").mkdir(parents=True, exist_ok=True)
        (self.temp_base_dir / "text" / "temp").mkdir(parents=True, exist_ok=True)
    
    def process_audio_sample(self, speaker_id: int, s3_url: str) -> Dict[str, Any]:
        """
        Process audio sample from S3 URL and store metrics.
        
        Args:
            speaker_id: ID of the speaker
            s3_url: S3 URL of the uploaded audio file
            
        Returns:
            Dictionary with processing results and metrics
        """
        temp_file_path = None
        
        try:
            # Validate speaker exists
            self._validate_speaker(speaker_id)
            
            # Validate S3 URL
            if not s3_url or not isinstance(s3_url, str):
                raise ValueError("Invalid S3 URL provided")
            
            # Determine file extension from URL
            file_ext = self._get_file_extension(s3_url)
            if file_ext not in ['.mp3', '.wav', '.MP3', '.WAV']:
                raise ValueError(f"Unsupported audio file format: {file_ext}")
            
            # Generate temporary file path
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            temp_file_path = self.temp_base_dir / "audio" / "temp" / f"{speaker_id}_{timestamp}_{unique_id}{file_ext}"
            
            logger.info(f"Downloading audio file from S3: {s3_url}")
            
            # Download file from S3 (raises RuntimeError on failure)
            self.s3_service.download_file(s3_url, str(temp_file_path))
            
            # Validate file size (25 MB max)
            file_size_mb = temp_file_path.stat().st_size / (1024 * 1024)
            if file_size_mb > 25:
                raise ValueError(f"File size ({file_size_mb:.2f} MB) exceeds maximum allowed size (25 MB)")
            
            logger.info(f"Audio file downloaded: {temp_file_path} ({file_size_mb:.2f} MB)")
            
            # Analyze audio file
            logger.info(f"Starting audio analysis for speaker {speaker_id}")
            audio_metrics = self.audio_analysis.analyze_audio(str(temp_file_path))
            
            # Store metrics in database
            logger.info(f"Storing audio metrics for speaker {speaker_id}")
            self._store_audio_metrics(speaker_id, audio_metrics)
            
            # Cleanup temporary file
            self._cleanup_file(temp_file_path)
            
            logger.info(f"Audio processing completed successfully for speaker {speaker_id}")
            
            return {
                "success": True,
                "message": "Audio analysis completed",
                "speaker_id": speaker_id,
                "audio_metrics": audio_metrics
            }
            
        except Exception as e:
            logger.error(f"Error processing audio sample for speaker {speaker_id}: {e}", exc_info=True)
            
            # Cleanup on error
            if temp_file_path and temp_file_path.exists():
                self._cleanup_file(temp_file_path)
            
            raise
    
    def _validate_speaker(self, speaker_id: int) -> Speaker:
        """
        Validate that speaker exists in database.
        
        Args:
            speaker_id: ID of the speaker
            
        Returns:
            Speaker object
            
        Raises:
            ValueError: If speaker not found
        """
        with db_manager.get_session() as session:
            speaker = session.query(Speaker).filter_by(id=speaker_id).first()
            if not speaker:
                raise ValueError(f"Speaker with ID {speaker_id} not found")
    
    def _get_file_extension(self, url: str) -> str:
        """
        Extract file extension from URL by checking if URL contains any valid extension.
       
        Args:
            url: S3 URL
           
        Returns:
            File extension with dot (e.g., '.mp3') or '.mp3' as fallback
        """
        # List of valid extensions for voiceprint
        valid_extensions = ['.mp3', '.wav', '.mp4', '.mov', '.pdf', '.docx', '.txt']
       
        # Check if URL contains any valid extension (case insensitive)
        url_lower = url.lower()
        for ext in valid_extensions:
            if ext in url_lower:
                return ext
       
        # Fallback to .mp3 if no extension found
        return '.mp3' 
    
    def _store_audio_metrics(self, speaker_id: int, metrics: Dict[str, Any]) -> None:
        """
        Store audio metrics in database.
        
        Args:
            speaker_id: ID of the speaker
            metrics: Audio metrics dictionary
        """
        try:
            with db_manager.get_session() as session:
                speaker = session.query(Speaker).filter_by(id=speaker_id).first()
                if not speaker:
                    raise ValueError(f"Speaker with ID {speaker_id} not found")
                
                # Update audio metrics
                speaker.voiceprint_audio_metrics = metrics
                session.commit()
                
                logger.info(f"Audio metrics stored successfully for speaker {speaker_id}")
                
        except Exception as e:
            logger.error(f"Error storing audio metrics for speaker {speaker_id}: {e}", exc_info=True)
            raise
    
    def analyze_audio_file_direct(self, file_path: str) -> Dict[str, Any]:
        """
        Analyze audio file directly (for testing purposes).
        This method analyzes the audio file without storing in database.
        
        Args:
            file_path: Path to the audio file
            
        Returns:
            Dictionary with audio metrics only (no database storage)
        """
        try:
            logger.info(f"Direct audio analysis for file: {file_path}")
            
            # Analyze audio file
            audio_metrics = self.audio_analysis.analyze_audio(file_path)
            
            logger.info(f"Direct audio analysis completed successfully")
            
            return {
                "success": True,
                "message": "Audio analysis completed (testing mode)",
                "audio_metrics": audio_metrics
            }
            
        except Exception as e:
            logger.error(f"Error in direct audio analysis for {file_path}: {e}", exc_info=True)
            raise
    
    def process_video_sample(self, speaker_id: int, s3_url: str) -> Dict[str, Any]:
        """
        Process video sample from S3 URL and store metrics.
        
        Args:
            speaker_id: ID of the speaker
            s3_url: S3 URL of the uploaded video file
            
        Returns:
            Dictionary with processing results and metrics
        """
        temp_file_path = None
        extracted_audio_path = None
        
        try:
            # Validate speaker exists
            self._validate_speaker(speaker_id)
            
            # Validate S3 URL
            if not s3_url or not isinstance(s3_url, str):
                raise ValueError("Invalid S3 URL provided")
            
            # Determine file extension from URL
            file_ext = self._get_file_extension(s3_url)
            if file_ext not in ['.mp4', '.mov', '.MP4', '.MOV']:
                raise ValueError(f"Unsupported video file format: {file_ext}. Only .mp4 and .mov are supported.")
            
            # Generate temporary file path
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            temp_file_path = self.temp_base_dir / "video" / "temp" / f"{speaker_id}_{timestamp}_{unique_id}{file_ext}"
            
            logger.info(f"Downloading video file from S3: {s3_url}")
            
            # Download file from S3 (raises RuntimeError on failure)
            self.s3_service.download_file(s3_url, str(temp_file_path))
            
            # Validate file size (50 MB max)
            file_size_mb = temp_file_path.stat().st_size / (1024 * 1024)
            if file_size_mb > 50:
                raise ValueError(f"File size ({file_size_mb:.2f} MB) exceeds maximum allowed size (50 MB)")
            
            logger.info(f"Video file downloaded: {temp_file_path} ({file_size_mb:.2f} MB)")
            
            # Generate path for extracted audio (will be cleaned up by video_analysis)
            video_name = temp_file_path.stem
            extracted_audio_path = str(self.temp_base_dir / "video" / "temp" / f"{video_name}_extracted_audio.wav")
            
            # Analyze video file (extracts audio and analyzes)
            logger.info(f"Starting video analysis for speaker {speaker_id}")
            video_metrics = self.video_analysis.analyze_video(str(temp_file_path), extract_audio_to=extracted_audio_path)
            
            # Store metrics in database
            logger.info(f"Storing video metrics for speaker {speaker_id}")
            self._store_video_metrics(speaker_id, video_metrics)
            
            # Cleanup temporary files
            self._cleanup_file(temp_file_path)
            
            # Cleanup extracted audio file if it still exists
            extracted_audio_path_obj = Path(extracted_audio_path)
            if extracted_audio_path_obj.exists():
                self._cleanup_file(extracted_audio_path_obj)
            
            logger.info(f"Video processing completed successfully for speaker {speaker_id}")
            
            return {
                "success": True,
                "message": "Video analysis completed",
                "speaker_id": speaker_id,
                "video_metrics": video_metrics
            }
            
        except Exception as e:
            logger.error(f"Error processing video sample for speaker {speaker_id}: {e}", exc_info=True)
            
            # Cleanup on error
            if temp_file_path and temp_file_path.exists():
                self._cleanup_file(temp_file_path)
            
            if extracted_audio_path:
                extracted_audio_path_obj = Path(extracted_audio_path)
                if extracted_audio_path_obj.exists():
                    self._cleanup_file(extracted_audio_path_obj)
            
            raise
    
    def _store_video_metrics(self, speaker_id: int, metrics: Dict[str, Any]) -> None:
        """
        Store video metrics in database.
        
        Args:
            speaker_id: ID of the speaker
            metrics: Video metrics dictionary
        """
        try:
            with db_manager.get_session() as session:
                speaker = session.query(Speaker).filter_by(id=speaker_id).first()
                if not speaker:
                    raise ValueError(f"Speaker with ID {speaker_id} not found")
                
                # Update video metrics
                speaker.voiceprint_video_metrics = metrics
                session.commit()
                
                logger.info(f"Video metrics stored successfully for speaker {speaker_id}")
                
        except Exception as e:
            logger.error(f"Error storing video metrics for speaker {speaker_id}: {e}", exc_info=True)
            raise
    
    def analyze_video_file_direct(self, file_path: str) -> Dict[str, Any]:
        """
        Analyze video file directly (for testing purposes).
        This method analyzes the video file without storing in database.
        
        Args:
            file_path: Path to the video file
            
        Returns:
            Dictionary with video metrics only (no database storage)
        """
        try:
            logger.info(f"Direct video analysis for file: {file_path}")
            
            # Analyze video file (extracts audio and analyzes)
            video_metrics = self.video_analysis.analyze_video(file_path)
            
            logger.info(f"Direct video analysis completed successfully")
            
            return {
                "success": True,
                "message": "Video analysis completed (testing mode)",
                "video_metrics": video_metrics
            }
            
        except Exception as e:
            logger.error(f"Error in direct video analysis for {file_path}: {e}", exc_info=True)
            raise
    
    def process_text_sample(self, speaker_id: int, s3_url: str) -> Dict[str, Any]:
        """
        Process text sample from S3 URL and store metrics.
        
        Args:
            speaker_id: ID of the speaker
            s3_url: S3 URL of the uploaded text file
            
        Returns:
            Dictionary with processing results and metrics
        """
        temp_file_path = None
        
        try:
            # Validate speaker exists
            self._validate_speaker(speaker_id)
            
            # Validate S3 URL
            if not s3_url or not isinstance(s3_url, str):
                raise ValueError("Invalid S3 URL provided")
            
            # Determine file extension from URL
            file_ext = self._get_file_extension(s3_url)
            if file_ext not in ['.pdf', '.docx', '.txt', '.PDF', '.DOCX', '.TXT']:
                raise ValueError(f"Unsupported text file format: {file_ext}. Only .pdf, .docx, and .txt are supported.")
            
            # Determine file type
            file_type = file_ext.lower().lstrip('.')
            
            # Generate temporary file path
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            unique_id = str(uuid.uuid4())[:8]
            temp_file_path = self.temp_base_dir / "text" / "temp" / f"{speaker_id}_{timestamp}_{unique_id}{file_ext}"
            
            logger.info(f"Downloading text file from S3: {s3_url}")
            
            # Download file from S3 (raises RuntimeError on failure)
            self.s3_service.download_file(s3_url, str(temp_file_path))
            
            # Validate file size (2 MB max)
            file_size_mb = temp_file_path.stat().st_size / (1024 * 1024)
            if file_size_mb > 2:
                raise ValueError(f"File size ({file_size_mb:.2f} MB) exceeds maximum allowed size (2 MB)")
            
            logger.info(f"Text file downloaded: {temp_file_path} ({file_size_mb:.2f} MB)")
            
            # Analyze text file
            logger.info(f"Starting text analysis for speaker {speaker_id}")
            text_metrics = self.text_analysis.analyze_text(str(temp_file_path), file_type)
            
            # Store metrics in database
            logger.info(f"Storing text metrics for speaker {speaker_id}")
            self._store_text_metrics(speaker_id, text_metrics)
            
            # Cleanup temporary file
            self._cleanup_file(temp_file_path)
            
            logger.info(f"Text processing completed successfully for speaker {speaker_id}")
            
            return {
                "success": True,
                "message": "Text analysis completed",
                "speaker_id": speaker_id,
                "text_metrics": text_metrics
            }
            
        except Exception as e:
            logger.error(f"Error processing text sample for speaker {speaker_id}: {e}", exc_info=True)
            
            # Cleanup on error
            if temp_file_path and temp_file_path.exists():
                self._cleanup_file(temp_file_path)
            
            raise
    
    def _store_text_metrics(self, speaker_id: int, metrics: Dict[str, Any]) -> None:
        """
        Store text metrics in database.
        
        Args:
            speaker_id: ID of the speaker
            metrics: Text metrics dictionary
        """
        try:
            with db_manager.get_session() as session:
                speaker = session.query(Speaker).filter_by(id=speaker_id).first()
                if not speaker:
                    raise ValueError(f"Speaker with ID {speaker_id} not found")
                
                # Update text metrics
                speaker.voiceprint_text_metrics = metrics
                session.commit()
                
                logger.info(f"Text metrics stored successfully for speaker {speaker_id}")
                
        except Exception as e:
            logger.error(f"Error storing text metrics for speaker {speaker_id}: {e}", exc_info=True)
            raise
    
    def analyze_text_file_direct(self, file_path: str, file_type: str) -> Dict[str, Any]:
        """
        Analyze text file directly (for testing purposes).
        This method analyzes the text file without storing in database.
        
        Args:
            file_path: Path to the text file
            file_type: File type (pdf, docx, txt)
            
        Returns:
            Dictionary with text metrics only (no database storage)
        """
        try:
            logger.info(f"Direct text analysis for file: {file_path}")
            
            # Analyze text file
            text_metrics = self.text_analysis.analyze_text(file_path, file_type)
            
            logger.info(f"Direct text analysis completed successfully")
            
            return {
                "success": True,
                "message": "Text analysis completed (testing mode)",
                "text_metrics": text_metrics
            }
            
        except Exception as e:
            logger.error(f"Error in direct text analysis for {file_path}: {e}", exc_info=True)
            raise
    
    def _cleanup_file(self, file_path: Path) -> None:
        """
        Delete temporary file and log deletion.
        
        Args:
            file_path: Path to file to delete
        """
        try:
            if file_path.exists():
                file_size = file_path.stat().st_size
                file_path.unlink()
                logger.info(f"Deleted temporary file: {file_path} (size: {file_size} bytes)")
        except Exception as e:
            logger.warning(f"Error deleting temporary file {file_path}: {e}")
    
    def generate_tone_profile(self, speaker_id: int, text_metrics: Dict[str, Any], 
                              audio_metrics: Dict[str, Any], video_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate tone profile from combined voiceprint metrics using LLM.
        
        Analyzes text, audio, and video metrics to create a comprehensive tone profile
        with scores from 1-5 for various communication characteristics.
        
        Args:
            speaker_id: ID of the speaker
            text_metrics: Text analysis metrics dictionary
            audio_metrics: Audio analysis metrics dictionary
            video_metrics: Video analysis metrics dictionary
            
        Returns:
            Dictionary with tone profile in the specified format
            
        Raises:
            ValueError: If OpenAI API is not configured or call fails
        """
        if not self.openai_client:
            raise ValueError("OpenAI API key not configured")
        
        # Print metrics being passed to LLM
        print(f"\n=== METRICS PASSED TO LLM FOR SPEAKER {speaker_id} ===\nTEXT METRICS:\n{json.dumps(text_metrics, indent=2)}\n\nAUDIO METRICS:\n{json.dumps(audio_metrics, indent=2)}\n\nVIDEO METRICS:\n{json.dumps(video_metrics, indent=2)}\n=== END OF METRICS ===\n")
        
        # Build prompt with all three metrics
        prompt = f"""Analyze the following voiceprint metrics from text, audio, and video analysis and generate a comprehensive tone profile.

TEXT METRICS:
{json.dumps(text_metrics, indent=2)}

AUDIO METRICS:
{json.dumps(audio_metrics, indent=2)}

VIDEO METRICS:
{json.dumps(video_metrics, indent=2)}

Based on these metrics, analyze the speaker's communication style and provide scores (1-5 scale) for each tone profile dimension:

1. **directness** (1-5): How direct and straightforward is the communication? 1=very indirect/roundabout, 5=very direct/blunt
2. **formality** (1-5): Level of formality in language and delivery. 1=very casual, 5=very formal
3. **rhythm** (1-5): Consistency and flow of speech patterns. 1=irregular/choppy, 5=very smooth/consistent
4. **sentence_length** (1-5): Average sentence complexity. 1=very short/simple, 5=very long/complex
5. **warmth** (1-5): Emotional warmth and friendliness. 1=cold/distant, 5=very warm/approachable
6. **aggression** (1-5): Assertiveness and intensity. 1=very passive, 5=very aggressive/forceful
7. **emotional_weight** (1-5): Emotional depth and intensity. 1=very light/superficial, 5=very deep/intense
8. **complexity** (1-5): Intellectual complexity and sophistication. 1=very simple, 5=very complex/sophisticated
9. **rhetorical_devices** (1-5): Use of rhetorical techniques, metaphors, storytelling. 1=minimal, 5=extensive
10. **evidence_preference** (1-5): Reliance on data, facts, and evidence. 1=anecdotal/emotional, 5=data-driven/analytical
11. **jargon_tolerance** (1-5): Use of technical terms and specialized language. 1=plain language, 5=highly technical
12. **humor_intensity** (1-5): Use of humor and lightheartedness. 1=serious/formal, 5=very humorous/playful

Return ONLY a valid JSON object with this exact structure:
{{
  "speaker_id": "{speaker_id}",
  "voiceprint_version": "v1",
  "tone_profile": {{
    "directness": <integer 1-5>,
    "formality": <integer 1-5>,
    "rhythm": <integer 1-5>,
    "sentence_length": <integer 1-5>,
    "warmth": <integer 1-5>,
    "aggression": <integer 1-5>,
    "emotional_weight": <integer 1-5>,
    "complexity": <integer 1-5>,
    "rhetorical_devices": <integer 1-5>,
    "evidence_preference": <integer 1-5>,
    "jargon_tolerance": <integer 1-5>,
    "humor_intensity": <integer 1-5>
  }}
}}

Ensure all scores are integers between 1 and 5 inclusive."""
        
        try:
            logger.info(f"Generating tone profile for speaker {speaker_id} using LLM")
            
            response = self.openai_client.chat.completions.create(
                model=getattr(config, 'OPENAI_MODEL', 'gpt-4o-mini'),
                messages=[{"role": "user", "content": prompt}],
                temperature=0.3,
                response_format={"type": "json_object"}
            )
            
            content = response.choices[0].message.content.strip()
            logger.info(f"LLM response received for speaker {speaker_id}, length: {len(content)} characters")
            
            try:
                tone_profile = json.loads(content)
                
                # Validate the structure and scores
                self._validate_tone_profile(tone_profile, speaker_id)
                
                logger.info(f"Tone profile generated successfully for speaker {speaker_id}")
                return tone_profile
                
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse LLM JSON response for speaker {speaker_id}: {e}")
                # Fallback: extract JSON from response if wrapped in markdown
                json_match = re.search(r'\{.*\}', content, re.DOTALL)
                if json_match:
                    tone_profile = json.loads(json_match.group())
                    self._validate_tone_profile(tone_profile, speaker_id)
                    return tone_profile
                raise ValueError("Failed to parse LLM response")
                
        except Exception as e:
            logger.error(f"LLM call failed for speaker {speaker_id}: {e}", exc_info=True)
            raise ValueError(f"Tone profile generation failed: {str(e)}")
    
    def _validate_tone_profile(self, tone_profile: Dict[str, Any], speaker_id: int) -> None:
        """
        Validate tone profile structure and score ranges.
        
        Args:
            tone_profile: Tone profile dictionary to validate
            speaker_id: Speaker ID for error messages
            
        Raises:
            ValueError: If structure is invalid or scores are out of range
        """
        required_keys = [
            "speaker_id", "voiceprint_version", "tone_profile"
        ]
        
        for key in required_keys:
            if key not in tone_profile:
                raise ValueError(f"Missing required key '{key}' in tone profile")
        
        tone_profile_scores = tone_profile.get("tone_profile", {})
        required_scores = [
            "directness", "formality", "rhythm", "sentence_length", "warmth",
            "aggression", "emotional_weight", "complexity", "rhetorical_devices",
            "evidence_preference", "jargon_tolerance", "humor_intensity"
        ]
        
        for score_key in required_scores:
            if score_key not in tone_profile_scores:
                raise ValueError(f"Missing required score '{score_key}' in tone profile")
            
            score = tone_profile_scores[score_key]
            if not isinstance(score, int) or score < 1 or score > 5:
                raise ValueError(
                    f"Invalid score for '{score_key}': {score}. Must be an integer between 1 and 5"
                )
        
        # Ensure speaker_id matches
        if str(tone_profile.get("speaker_id")) != str(speaker_id):
            logger.warning(
                f"Speaker ID mismatch in tone profile: expected {speaker_id}, "
                f"got {tone_profile.get('speaker_id')}. Updating to match."
            )
            tone_profile["speaker_id"] = str(speaker_id)
    
    def store_voiceprint(self, speaker_id: int, tone_profile: Dict[str, Any]) -> None:
        """
        Store voiceprint tone profile in database.
        
        Args:
            speaker_id: ID of the speaker
            tone_profile: Tone profile dictionary to store
        """
        try:
            with db_manager.get_session() as session:
                speaker = session.query(Speaker).filter_by(id=speaker_id).first()
                if not speaker:
                    raise ValueError(f"Speaker with ID {speaker_id} not found")
                
                # Update voiceprint column
                speaker.voiceprint = tone_profile
                session.commit()
                
                logger.info(f"Voiceprint tone profile stored successfully for speaker {speaker_id}")
                
        except Exception as e:
            logger.error(f"Error storing voiceprint tone profile for speaker {speaker_id}: {e}", exc_info=True)
            raise

