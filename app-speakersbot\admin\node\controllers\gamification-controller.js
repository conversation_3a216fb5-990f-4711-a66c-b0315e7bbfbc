const ApiResponse = require("../helpers/api-response");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const gamificationService = require("../services/gamification-service");
const CustomError = require("../helpers/custome-error");

/**
 * Get gamification points data
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {Promise<void>}
 */
exports.getPointsHistory = async (req, res, next) => {
    try {

        const { status, data, message } = await gamificationService.getPointsData(req.query);

        if (status) {
            res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ data, message }));
        } else {
            res.status(RESPONSE_CODES.SUCCESS).json({ status: false, data: data, message: message });
        }
    } catch (error) {
        console.error("Error in gamification controller:", error);
        next(error);
    }
}

exports.getAllRules = async (req, res, next) => {
    try {
        const { status, data, message } = await gamificationService.getAllRules();

        if (status) {
            res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ data, message }));
        } else {
            res.status(RESPONSE_CODES.SUCCESS).json({ status: false, data: data, message: message });
        }
    } catch (error) {
        console.error("Error in gamification controller:", error);
        next(error);
    }
}

exports.updateRules = async (req, res, next) => {
    try {
        const { status, data, message } = await gamificationService.updateRules(req.body);  
        if (status) {
            res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ data, message }));
        } else {
            res.status(RESPONSE_CODES.BAD_REQUEST).json({ status: false, data: data, message: message });
        }
    } catch (error) {
        console.error("Error updating gamification rules:", error);
        next(error);    
    }
}


/**
 * Process user streak for daily login
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {Promise<void>}
 */
exports.processPoints = async (req, res, next) => {
    try {
        const speaker_id = req.user.id;
        const event = req.body.event

        const { status, data, message } = await exports.awardEventPoints(speaker_id, event);

        if (status) {
            res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ data, message }));
        } else {
            res.status(RESPONSE_CODES.BAD_REQUEST).json({ status: false, data: data, message: message });
        }
    } catch (error) {
        console.error("Error processing user points:", error);
        next(error);
    }
}


/**
 * Award event points - routes to specific event handlers
 * @param {number} speaker_id - The speaker ID  
 * @param {string} event - The event type
 * @param {Object} options - Additional options
 */
exports.awardEventPoints = async (speaker_id, event) => {
    try {
        switch (event) {
            case 'daily_login':
                return await gamificationService.handleDailyLogin(speaker_id);

            case 'bio_update':
            case 'add_testimonial':
            case 'upload_video':
            case 'upload_photo':
            case 'optional_questions':
                return await gamificationService.handleFormUpdate(speaker_id, event);

            case 'referral_points':
                return await gamificationService.referralPoints(speaker_id);

            case 'affiliate_meeting_completed':
                return await gamificationService.handlePointsRedemption(speaker_id, event, 0);

            default:
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid event type");
        }
    } catch (error) {
        console.error("Error awarding event points:", error);
        throw error;
    }
};