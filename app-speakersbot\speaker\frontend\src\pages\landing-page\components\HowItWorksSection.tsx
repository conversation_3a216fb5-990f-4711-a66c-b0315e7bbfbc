import FollowUpIcon from "@/assets/icons/followup-icon.svg";
import TargetingIcon from "@/assets/icons/target-icon.svg";
import VerifiedIcon from "@/assets/icons/verified-icon.svg";
import DashboardMockup from "@/assets/images/dashboard-preview.png";
import { HowItWorksItem } from "./HowItWorksItem";

const HowItWorksSection = () => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 items-start max-w-[1820px] mx-auto lg:mt-[100px] md:mt-[50px] mt-[30px] py-50 max-md:pb-0">
      <div className="">
        <div className="lg:mb-50 mb-5">
          <h2 className="text-white font-poppins font-medium lg:text-[36px] md:text-[28px] text-[24px]">
            How Digital Speaker Agent Works
          </h2>
          <p className="text-white font-segoe lg:text-base text-sm leading-relaxed mt-2">
            Our advanced cutting-edge technology revolutionizes how speakers
            fill their calendar with paid gig while giving speakers back the
            time it takes to get them.
          </p>
        </div>

        <div className="lg:mt-50 mt-5 flex flex-col lg:gap-10 gap-5 mb-5">
          <HowItWorksItem
            icon={TargetingIcon}
            title="Precision Targeting"
            description="Get matched lightning fast with events that align perfectly with your speaking category."
          />
          <HowItWorksItem
            icon={FollowUpIcon}
            title="Personalized Pitch Generation & Follow Up"
            description="Built from your materials in your own unique voice. We don't just apply to gigs for you, but as you. Every pitch sounds like you. Reads like you. Wins like you."
          />
          <HowItWorksItem
            icon={VerifiedIcon}
            title="Verified Opportunities"
            description="All events are pre-screened and verified for legitimacy and quality."
          />
        </div>
      </div>

      {/* Dashboard Preview */}
      <div className="flex items-end justify-center h-full">
        <img
          src={DashboardMockup}
          alt="Dashboard preview showing opportunities and match scores"
          className="w-full rounded-lg shadow-2xl lg:translate-x-[45px]"
        />
      </div>
    </div>
  );
};

export default HowItWorksSection;
