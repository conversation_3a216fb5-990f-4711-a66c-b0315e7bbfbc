const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const {SpeakerDetails, Speakers} = require('../models');
const connection = require("../models/connection");

const csvFilePath = path.join(__dirname, 'csv', 'Form Submissions.csv');

async function insertSpeakerDetails() {
  try {
    await connection.authenticate();
    console.log('Database connection established.');

    // First, load form questions to get proper question text for each field_id
    const formQuestions = {};
    const formQuestionsCsvPath = path.join(__dirname, 'csv', 'form_questions.csv');
    
    await new Promise((resolve, reject) => {
      fs.createReadStream(formQuestionsCsvPath)
        .pipe(csv())
        .on('data', (row) => {
          formQuestions[row.field_id] = row.question;
        })
        .on('end', resolve)
        .on('error', reject);
    });

    console.log(`Loaded ${Object.keys(formQuestions).length} form questions.`);

    // Map CSV headers to form question field_ids
    const headerToFieldIdMap = {
      
      '1/Name': 'field_1',
      'Crawler Payload': 'field_2',
      'Sent to crawler?': 'field_3',
      '1/Email': 'field_4',
      '1/Phone Number': 'field_5',
      '1/City': 'field_6',
      '1/State': 'field_7',
      '1/Linkedin': 'field_8',
      '1/Speaker One-Pager (1)': 'field_9',
      '1/Preferred Speaker Geography': 'field_10',
      '2/Primary Area Expertise': 'field_11',
      '2/Primary Area Expertise 2': 'field_12',
      '2/Main Area Expertise': 'field_13',
      '2/Main Area Expertise 2': 'field_14',
      "2/I'm an expert at one thing": 'field_15',
      '2/Speaker Credentials': 'field_16',
      '2/Headshot': 'field_17',
      '2/Bio': 'field_18',
      '2/Willing to travel': 'field_19',
      '2/Preferred Talk Formats': 'field_20',
      '2/Years of Experience as a Speaker': 'field_21',
      '6/Preferred Talk Formats': 'field_55',
      '6/Preferred Audience': 'field_56',
      '6/Preferred Session Duration': 'field_57',
      '6/Do you have Ted or TedX?': 'field_58',
      '7/Monthly Paid Speaking Goals': 'field_59',
      '7/Paid Lead Platforms Used': 'field_60',
      '7/Live Speaking Fee': 'field_61',
      '7/Virtual Speaking Fee': 'field_62',
      '6/TedX Transcript': 'field_63',
      'Paid Lead Platform?': 'field_64',
      '7/Testimonials': 'field_65',
      '2/Speaker Brand': 'field_66',
      '2/Pain Point?': 'field_67',
      '3/Keynote Transcript Upload': 'field_22',
      '1/Speaker One-pager (2)': 'field_23',
      '1/Speaker One-Pager (3)': 'field_24',
      '3/Keynote Title 1': 'field_25',
      '3/Keynote Title 2': 'field_26',
      '3/Keynote Title 3': 'field_27',
      '3/Keynote Learning Objective': 'field_28',
      '3/Keynote Takeaway 1': 'field_29',
      '3/Keynote Takeaway 2': 'field_30',
      '3/Keynote Takeaway 3': 'field_31',
      '5/Speaking Engagement 1 - Org Name': 'field_42',
      '5/Speaking Engagement 1 - Contact Name': 'field_43',
      '5/Speaking Engagement 1 - Contact Phone': 'field_44',
      '5/Speaking Engagement 1 - Contact Email': 'field_45',
      '5/Speaking Engagement 2 - Org Name': 'field_46',
      '5/Speaking Engagement 2 - Contact Name': 'field_47',
      '5/Speaking Engagement 2 - Contact Phone': 'field_48',
      '5/Speaking Engagement 2 - Contact Email': 'field_49',
      '6/TedX Title': 'field_50',
      '6/TedX Title 2': 'field_51',
      '6/TedX Transcript 2': 'field_52',
      '6/Authority Stages Title 1': 'field_53',
      '6/Authority Stages Transcript 1': 'field_54',
      '4/Workshop Title 1': 'field_32',
      '4/Workshop Title 2': 'field_33',
      '4/Workshop Title 3': 'field_34',
      '4/Workshop transcript upload': 'field_35',
      '4/Workshop learning objective 1': 'field_36',
      '4/Workshop learning objective 2': 'field_37',
      '4/Workshop learning objective 3': 'field_38',
      '4/Workshop takeaways 1': 'field_39',
      '4/Workshop takeaways 2': 'field_40',
      '4/Workshop takeaways 3': 'field_41',
      '1/Speaker Website': 'field_68',
      '2/Top 10 words': 'field_69',
      '6/Top three outcomes': 'field_70',
      '6/Top three outcomes other': 'field_71',
      '6/Auth Stages Drop': 'field_72',
      '6/Name of Stage 1': 'field_73',
      '6/Name of Stage 2': 'field_74',
      '6/Authority Stages Title 2': 'field_75',
      '7/Testimonials 2': 'field_76',
      '7/Testimonials 3': 'field_77',
      '7/Testimonials 4': 'field_78',
      '7/Testimonials 5': 'field_79',
      '7/Affiliate ': 'field_80',
      '1/Company': 'field_81',
      '1/Title': 'field_82',
      '2/Differentiators': 'field_83',
      '3/Interactive? Yes or No?': 'field_84',
      '3/five activities': 'field_85',
      '4/Present Interactive?': 'field_86',
      '4/5 activities workshop': 'field_87',
      '7/Search Gigs': 'field_88',
      '7/Travel Rider': 'field_89',
      '7/Audio Visual Rider': 'field_90',
      '2/Main Area Expertise Other': 'field_91',
      '2/Expertise Level 3': 'field_92',
      '2/Expertise Level 3 Other': 'field_93',
      '2/Topic Area': 'field_94',
      '2/Specific Clients': 'field_95',
      '2/Primary Area Expert Other': 'field_96',
      '6/Authority Stages Transcript 2': 'field_97'
    };

    // Helper to convert values to correct types
    function convertValue(value) {
      if (value === undefined || value === null) return null;
      value = value.toString().trim();
      if (value === '' || value === 'missing value') return null;
      return value;
    }

    const speakerDetails = [];
    let rowCount = 0;

    fs.createReadStream(csvFilePath)
      .pipe(csv())
      .on('data', (row) => {
        rowCount++;
        console.log(`Processing row ${rowCount}...`);

        // Get speaker_id from the name (assuming speakers are already imported)
        const speakerName = row['1/Name'];
        if (!speakerName || speakerName.trim() === '') {
          console.log(`Skipping row ${rowCount} - no speaker name`);
          return;
        }

        // Find speaker by name
        // Note: This assumes speakers are already imported
        // You might need to adjust this based on your speaker import process
        const speakerId = rowCount; // Using row number as speaker_id for now

         // Process each column in the row - ensure all 97 fields are included
         for (const [csvHeader, fieldId] of Object.entries(headerToFieldIdMap)) {
           if (row.hasOwnProperty(csvHeader)) {
             const value = convertValue(row[csvHeader]);
             
             // Get the proper question text from form questions
             const questionText = formQuestions[fieldId] || csvHeader;
             
             // Always add the entry, even if value is null
             speakerDetails.push({
               speaker_id: speakerId,
               field_id: fieldId,
               key: questionText, // Using proper question text as key
               value: value || '' // Use empty string if value is null
             });
           }
         }
      })
      .on('end', async () => {
         console.log(`Read ${rowCount} rows from CSV.`);
         console.log(`Prepared ${speakerDetails.length} speaker detail entries.`);
         console.log(`Expected: ${rowCount} users × 97 fields = ${rowCount * 97} entries`);

        let success = 0;
        let fail = 0;

        // Insert speaker details in batches
        const batchSize = 100;
        for (let i = 0; i < speakerDetails.length; i += batchSize) {
          const batch = speakerDetails.slice(i, i + batchSize);
          
          try {
            await SpeakerDetails.bulkCreate(batch, {
              ignoreDuplicates: true // Skip duplicates
            });
            success += batch.length;
            console.log(`✓ Inserted batch ${Math.floor(i/batchSize) + 1} (${batch.length} records)`);
          } catch (err) {
            fail += batch.length;
            console.error(`✗ Failed to insert batch ${Math.floor(i/batchSize) + 1}:`, err.message);
          }
        }

        console.log(`\nSpeaker details insertion complete!`);
        console.log(`Success: ${success}`);
        console.log(`Failed: ${fail}`);
        console.log(`Total processed: ${speakerDetails.length}`);

        // Display summary by speaker
        const speakerSummary = {};
        speakerDetails.forEach(detail => {
          if (!speakerSummary[detail.speaker_id]) {
            speakerSummary[detail.speaker_id] = 0;
          }
          speakerSummary[detail.speaker_id]++;
        });

         console.log('\nSpeaker details summary:');
         Object.entries(speakerSummary).forEach(([speakerId, count]) => {
           const status = count === 97 ? '✓' : '✗';
           console.log(`${status} Speaker ID ${speakerId}: ${count}/97 detail entries`);
         });
         
         const expectedTotal = rowCount * 97;
         console.log(`\nExpected total entries: ${expectedTotal}`);
         console.log(`Actual total entries: ${speakerDetails.length}`);
         console.log(`Missing entries: ${expectedTotal - speakerDetails.length}`);

        process.exit(0);
      })
      .on('error', (error) => {
        console.error('Error reading CSV file:', error);
        process.exit(1);
      });
  } catch (error) {
    console.error('Unable to connect to the database:', error);
    process.exit(1);
  }
}

// Run the migration
insertSpeakerDetails();
        