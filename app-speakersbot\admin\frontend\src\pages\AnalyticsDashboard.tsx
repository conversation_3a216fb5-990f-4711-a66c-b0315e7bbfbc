import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "../components/ui/tabs";
import OpportunityPipelineAnalytics from "../components/analytics/OpportunityPipelineAnalytics";
import SpeakerOnboardingAnalytics from "../components/analytics/SpeakerOnboardingAnalytics";
import MatchingEngineAnalytics from "../components/analytics/MatchingEngineAnalytics";
import AffiliateAnalytics from "../components/analytics/AffiliateAnalytics";
import RevenueAnalytics from "../components/analytics/RevenueAnalytics";
import FeedbackAnalytics from "../components/analytics/FeedbackAnalytics";
import CategoryExpansionAnalytics from "../components/analytics/CategoryExpansionAnalytics";
import SystemHealthAnalytics from "../components/analytics/SystemHealthAnalytics";

const AnalyticsDashboard: React.FC = () => {
  const tabs = [
    {
      value: "opportunity-pipeline",
      label: "Pipeline",
      component: Opportunity<PERSON><PERSON>elineAnalytics,
    },
    {
      value: "speaker-onboarding",
      label: "Onboarding",
      component: SpeakerOnboardingAnalytics,
    },
    {
      value: "matching-engine",
      label: "Matching",
      component: MatchingEngineAnalytics,
    },

    { value: "affiliate", label: "Affiliate", component: AffiliateAnalytics },
    { value: "revenue", label: "Revenue", component: RevenueAnalytics },

    { value: "feedback", label: "Feedback", component: FeedbackAnalytics },
    {
      value: "category-expansion",
      label: "Categories",
      component: CategoryExpansionAnalytics,
    },
    {
      value: "system-health",
      label: "Health",
      component: SystemHealthAnalytics,
    },
  ];

  return (
    <div className="space-y-12">
      <div>
        <h1 className="text-2xl font-bold text-foreground">Dashboard</h1>
        <p className="text-muted-foreground mt-2 text-sm">
          Comprehensive analytics across all platform operations
        </p>
      </div>

      <Tabs defaultValue="opportunity-pipeline" className="w-full">
        <TabsList className="flex h-auto overflow-x-auto justify-between w-full border rounded-lg border-border dark:bg-tertiary">
          {tabs.map((tab) => (
            <TabsTrigger
              key={tab.value}
              value={tab.value}
              className="text-xs sm:text-sm py-2 px-10"
            >
              {tab.label}
            </TabsTrigger>
          ))}
        </TabsList>

        {tabs.map((tab) => (
          <TabsContent key={tab.value} value={tab.value} className="mt-6">
            <tab.component />
          </TabsContent>
        ))}
      </Tabs>
    </div>
  );
};

export default AnalyticsDashboard;
