import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../store';

// Define the base URL for file upload API
const FILE_UPLOAD_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/admin/api/v1';

export const fileUploadApi = createApi({
  reducerPath: 'fileUploadApi',
  baseQuery: fetchBaseQuery({
    baseUrl: FILE_UPLOAD_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Get the token from the state
      const token = (getState() as RootState).auth?.token;
      
      // If we have a token, set the authorization header
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      
      // Don't set content-type for FormData, let browser set it with boundary
      return headers;
    },
  }),
  tagTypes: ['FileUpload'],
  endpoints: (builder) => ({
    // Generic file upload - can be used for any type of file
    uploadFile: builder.mutation<
      { success: boolean; url: string; message: string },
      { file: File; type?: string; affiliateId?: string | number }
    >({
      query: ({ file, type = 'general', affiliateId }) => {
        const formData = new FormData();
        formData.append('file', file);
        formData.append('type', type); // Optional: specify file type (logo, signature, etc.)
        if (affiliateId) {
          formData.append('affiliateId', String(affiliateId)); // Include affiliateId if provided
        }
        
        return {
          url: '/upload-file',
          method: 'POST',
          body: formData,
        };
      },
      transformResponse: (response: any) => {
        return {
          success: response?.status === true || response?.success === true,
          url: response?.data || response?.url || '',
          message: response?.message || 'File uploaded successfully',
        };
      },
    }),
  }),
});

export const {
  useUploadFileMutation,
} = fileUploadApi;
