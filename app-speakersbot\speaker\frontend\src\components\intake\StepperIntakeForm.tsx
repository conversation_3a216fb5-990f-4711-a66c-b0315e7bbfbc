import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { ChevronLeft, ChevronRight, Sparkles, Upload, CheckCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface FormData {
  // Personal Info
  name: string;
  email: string;
  phone: string;
  city: string;
  state: string;
  country: string;
  
  // Professional Info
  company: string;
  jobTitle: string;
  linkedin: string;
  bio: string;
  
  // Speaker Profile
  pastSpeaking: string;
  expertise: string;
  onePager: File | null;
  
  // Application specific
  proposalTitle: string;
  proposalDescription: string;
  whyYou: string;
}

interface StepperIntakeFormProps {
  isOpen: boolean;
  onClose: () => void;
  opportunityTitle?: string;
}

export function StepperIntakeForm({ isOpen, onClose, opportunityTitle }: StepperIntakeFormProps) {
  const { toast } = useToast();
  const [currentStep, setCurrentStep] = useState(1);
  const [isAutoFilled, setIsAutoFilled] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    name: '',
    email: '',
    phone: '',
    city: '',
    state: '',
    country: '',
    company: '',
    jobTitle: '',
    linkedin: '',
    bio: '',
    pastSpeaking: '',
    expertise: '',
    onePager: null,
    proposalTitle: '',
    proposalDescription: '',
    whyYou: ''
  });

  const steps = [
    { id: 1, title: 'Personal Info', description: 'Basic contact information' },
    { id: 2, title: 'Professional Info', description: 'Your professional background' },
    { id: 3, title: 'Speaker Profile', description: 'Speaking experience and expertise' },
    { id: 4, title: 'Proposal Details', description: 'Your speaking proposal' }
  ];

  const totalSteps = steps.length;
  const progress = (currentStep / totalSteps) * 100;

  const handleAutoFill = async () => {
    setIsAutoFilled(true);
    // Simulate AI filling with realistic data
    setFormData({
      name: 'Alex Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      city: 'San Francisco',
      state: 'California',
      country: 'United States',
      company: 'TechCorp Solutions',
      jobTitle: 'Senior Software Engineer',
      linkedin: 'https://linkedin.com/in/alexjohnson',
      bio: 'Passionate software engineer with 8+ years of experience in full-stack development, cloud architecture, and team leadership. Focused on building scalable solutions and mentoring junior developers.',
      pastSpeaking: 'Presented at 5+ tech conferences including DevCon 2023, Cloud Summit 2022. Regular speaker at local meetups and internal tech talks.',
      expertise: 'React, Node.js, AWS, Microservices, DevOps, Team Leadership, Agile Development',
      onePager: null,
      proposalTitle: `Scaling React Applications: Lessons from ${opportunityTitle || 'Production'}`,
      proposalDescription: 'In this talk, I\'ll share practical strategies for scaling React applications based on real-world experience. We\'ll cover performance optimization, code organization, state management at scale, and deployment strategies that have proven successful in production environments.',
      whyYou: 'As a senior engineer who has led multiple large-scale React projects, I bring both technical depth and practical experience. My speaking experience and passion for knowledge sharing make me well-suited to deliver valuable insights to your audience.'
    });

    toast({
      title: "AI Auto-fill Complete!",
      description: "Form has been filled with your profile data. Review and edit as needed.",
    });
  };

  const handleNext = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Submitting application:', formData);
    toast({
      title: "Application Submitted!",
      description: "Your speaking application has been submitted successfully.",
    });
    onClose();
  };

  const updateFormData = (field: keyof FormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Full Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => updateFormData('name', e.target.value)}
                  className="mt-2"
                />
              </div>
              <div>
                <Label htmlFor="email">Email *</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => updateFormData('email', e.target.value)}
                  className="mt-2"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="phone">Phone</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => updateFormData('phone', e.target.value)}
                  className="mt-2"
                />
              </div>
              <div>
                <Label htmlFor="city">City</Label>
                <Input
                  id="city"
                  value={formData.city}
                  onChange={(e) => updateFormData('city', e.target.value)}
                  className="mt-2"
                />
              </div>
              <div>
                <Label htmlFor="state">State/Province</Label>
                <Input
                  id="state"
                  value={formData.state}
                  onChange={(e) => updateFormData('state', e.target.value)}
                  className="mt-2"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="country">Country</Label>
              <Input
                id="country"
                value={formData.country}
                onChange={(e) => updateFormData('country', e.target.value)}
                className="mt-2"
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="company">Company *</Label>
                <Input
                  id="company"
                  value={formData.company}
                  onChange={(e) => updateFormData('company', e.target.value)}
                  className="mt-2"
                />
              </div>
              <div>
                <Label htmlFor="jobTitle">Job Title *</Label>
                <Input
                  id="jobTitle"
                  value={formData.jobTitle}
                  onChange={(e) => updateFormData('jobTitle', e.target.value)}
                  className="mt-2"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="linkedin">LinkedIn Profile</Label>
              <Input
                id="linkedin"
                value={formData.linkedin}
                onChange={(e) => updateFormData('linkedin', e.target.value)}
                className="mt-2"
                placeholder="https://linkedin.com/in/yourprofile"
              />
            </div>
            
            <div>
              <Label htmlFor="bio">Professional Bio *</Label>
              <Textarea
                id="bio"
                value={formData.bio}
                onChange={(e) => updateFormData('bio', e.target.value)}
                className="mt-2"
                rows={4}
                placeholder="Brief overview of your professional background and expertise..."
              />
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="pastSpeaking">Past Speaking Experience</Label>
              <Textarea
                id="pastSpeaking"
                value={formData.pastSpeaking}
                onChange={(e) => updateFormData('pastSpeaking', e.target.value)}
                className="mt-2"
                rows={3}
                placeholder="Describe your previous speaking engagements, conferences, or presentations..."
              />
            </div>
            
            <div>
              <Label htmlFor="expertise">Areas of Expertise *</Label>
              <Textarea
                id="expertise"
                value={formData.expertise}
                onChange={(e) => updateFormData('expertise', e.target.value)}
                className="mt-2"
                rows={3}
                placeholder="List your key technical skills, technologies, and expertise areas..."
              />
            </div>
            
            <div>
              <Label htmlFor="onePager">Speaker One-Pager (Optional)</Label>
              <div className="mt-2 border-2 border-dashed border-border rounded-lg p-6 text-center">
                <Upload className="h-8 w-8 mx-auto text-foreground-muted mb-2" />
                <p className="text-sm text-foreground-muted">
                  Upload your speaker one-pager or portfolio (PDF, max 5MB)
                </p>
                <Button variant="outline" className="mt-2" size="sm">
                  Choose File
                </Button>
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="proposalTitle">Talk Title *</Label>
              <Input
                id="proposalTitle"
                value={formData.proposalTitle}
                onChange={(e) => updateFormData('proposalTitle', e.target.value)}
                className="mt-2"
                placeholder="Enter your proposed talk title..."
              />
            </div>
            
            <div>
              <Label htmlFor="proposalDescription">Talk Description *</Label>
              <Textarea
                id="proposalDescription"
                value={formData.proposalDescription}
                onChange={(e) => updateFormData('proposalDescription', e.target.value)}
                className="mt-2"
                rows={5}
                placeholder="Provide a detailed description of your proposed talk, including key topics and takeaways..."
              />
            </div>
            
            <div>
              <Label htmlFor="whyYou">Why You? *</Label>
              <Textarea
                id="whyYou"
                value={formData.whyYou}
                onChange={(e) => updateFormData('whyYou', e.target.value)}
                className="mt-2"
                rows={4}
                placeholder="Explain why you're the right person to give this talk and what unique value you bring..."
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto bg-surface border-border">
        <DialogHeader className="relative">
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle className="text-xl text-foreground">
                Speaker Application
              </DialogTitle>
              {opportunityTitle && (
                <p className="text-sm text-foreground-muted mt-1">
                  Applying for: <span className="font-medium">{opportunityTitle}</span>
                </p>
              )}
            </div>
            
            {/* AI Fill Button */}
            <Button
              onClick={handleAutoFill}
              variant="outline"
              size="sm"
              className="flex items-center gap-2"
              disabled={isAutoFilled}
            >
              {isAutoFilled ? (
                <>
                  <CheckCircle className="h-4 w-4 text-success" />
                  AI Filled
                </>
              ) : (
                <>
                  <Sparkles className="h-4 w-4" />
                  AI Fill
                </>
              )}
            </Button>
          </div>
        </DialogHeader>

        <div className="space-y-6">
          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span className="text-foreground-muted">Step {currentStep} of {totalSteps}</span>
              <span className="text-foreground-muted">{Math.round(progress)}% Complete</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {/* Step Indicators */}
          <div className="flex justify-between">
            {steps.map((step) => (
              <div key={step.id} className="flex flex-col items-center text-center">
                <div className={`w-8 h-8 rounded-full border-2 flex items-center justify-center text-sm font-medium ${
                  step.id === currentStep 
                    ? 'border-primary bg-primary text-primary-foreground' 
                    : step.id < currentStep 
                    ? 'border-success bg-success text-success-foreground' 
                    : 'border-border bg-surface text-foreground-muted'
                }`}>
                  {step.id < currentStep ? <CheckCircle className="h-4 w-4" /> : step.id}
                </div>
                <div className="mt-1">
                  <div className="text-xs font-medium text-foreground">{step.title}</div>
                  <div className="text-xs text-foreground-muted">{step.description}</div>
                </div>
              </div>
            ))}
          </div>

          {/* Form Content */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">{steps[currentStep - 1].title}</CardTitle>
              <CardDescription>{steps[currentStep - 1].description}</CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit}>
                {renderStepContent()}
              </form>
            </CardContent>
          </Card>

          {/* Navigation Buttons */}
          <div className="flex justify-between pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handlePrevious}
              disabled={currentStep === 1}
              className="flex items-center gap-2"
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>

            <div className="flex gap-2">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancel
              </Button>
              
              {currentStep === totalSteps ? (
                <Button 
                  type="submit" 
                  onClick={handleSubmit}
                  className="bg-primary hover:bg-primary-hover flex items-center gap-2"
                >
                  <CheckCircle className="h-4 w-4" />
                  Submit Application
                </Button>
              ) : (
                <Button 
                  type="button" 
                  onClick={handleNext}
                  className="bg-primary hover:bg-primary-hover flex items-center gap-2"
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}