from sqlalchemy import create_engine, Column, Integer, String, Text, Float, DateTime, Enum, func, JSON
from sqlalchemy.ext.declarative import declarative_base
from app.config.config import config

Base = declarative_base()

class Speaker(Base):
    __tablename__ = "speakers"
    
    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True, comment='Primary key for the speaker')
    
    # Basic information
    title = Column(String(255), comment='Title or honorific of the speaker')
    name = Column(String(255), nullable=False, comment='Full name of the speaker')
    email = Column(String(255), comment='Email address of the speaker')
    phone_number = Column(String(50), comment='Phone number of the speaker')
    
    # Location information
    city = Column(String(255), comment='City where the speaker is based')
    state = Column(String(255), comment='State where the speaker is based')
    country = Column(String(255), comment='Country where the speaker is based')
    
    # Authentication and affiliation
    affiliate_id = Column(Integer, comment='Affiliate ID associated with the speaker')
    password = Column(String(255), comment='Password of the speaker')
    
    # Professional information
    linkedin = Column(String(255), comment='LinkedIn profile URL of the speaker')
    bio = Column(Text, comment='Biography of the speaker')
    company = Column(String(255), comment='Company or organization of the speaker')
    speaker_website = Column(String(255), comment='Official website of the speaker')
    
    # Status and source
    status = Column(Enum('active', 'inactive', 'pending', name='speaker_status'), 
                   default='active', comment='Status of the speaker (active or inactive)')
    source = Column(String(255), comment='Source from where the speaker was added')
    
    # Media and payment
    headshot = Column(String(255), comment='URL or path to the speaker headshot image')
    stripe_customer_id = Column(String(255), comment='Stripe customer ID associated with the speaker')
    
    # Categorization and topics
    primary_category = Column(String(255), comment='Primary category of the speaker')
    subcategory = Column(String(255), comment='Subcategory of the speaker')
    topic = Column(String(255), comment='Main topic of the speaker')
    
    # Content and objectives
    learning_objectives = Column(Text, comment='Learning objectives of the speaker sessions')
    takeaways = Column(Text, comment='Key takeaways from the speaker sessions')
    challenges = Column(Text, comment='Challenges addressed by the speaker')
    
    # Preferences and credentials
    preferred_speaker_geography = Column(String(255), comment='Preferred geography for speaking engagements')
    speaker_credentials = Column(Text, comment='Credentials of the speaker')
    top_keywords = Column(Text, comment='Top keywords associated with the speaker')
    differentiators = Column(Text, comment='Unique differentiators of the speaker')
    
    # Voiceprint metrics (JSON format)
    voiceprint_text_metrics = Column(JSON, comment='Text analysis metrics')
    voiceprint_audio_metrics = Column(JSON, comment='Audio analysis metrics')
    voiceprint_video_metrics = Column(JSON, comment='Video analysis metrics')
    voiceprint = Column(JSON, comment='Complete voiceprint tone profile with scores')
    
    # Timestamps
    created_at = Column(DateTime, default=func.current_timestamp(), comment='Date and time when the speaker was created')
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='Date and time when the speaker was updated')


def create_speaker_table():
    db_url = config.get_database_url()
    engine = create_engine(db_url)
    Base.metadata.create_all(engine)

if __name__ == "__main__":
    create_speaker_table()
    print("Table 'speakers' created in MySQL database")
