import json
import os
import sys
import time
import asyncio
from datetime import datetime
 
from sqlalchemy import select, update, or_, and_
from sqlalchemy.exc import OperationalError
from sqlalchemy.orm import Session

# Add app directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.config.config import config
from app.config.logger import get_logger
from app.models.opportunities_url import OpportunitiesUrl
from app.helpers.database_manager import db_manager
from app.services.scraper_service import process_single_url
from app.helpers.database import save_to_db

logger = get_logger(__name__, file_name="scraper.log")

# Configuration from config
POLL_INTERVAL_SECONDS = config.WORKER_POLL_INTERVAL_SECONDS
CLAIM_BATCH_SIZE = config.WORKER_BATCH_SIZE
 
def claim_pending_rows(session: Session, limit: int) -> list[OpportunitiesUrl]:
    """Claim pending URLs from opportunities_url table with row locking."""
    # Use SELECT ... FOR UPDATE SKIP LOCKED to avoid double picking
    # Works on MySQL 8.0+ and InnoDB
    stmt = (
        select(OpportunitiesUrl)
        .where(
            and_(
                or_(OpportunitiesUrl.status == 'pending', OpportunitiesUrl.status == 'failed'),
                OpportunitiesUrl.retry_count < config.MAX_RETRIES_COUNT
            )
        )
        .order_by(OpportunitiesUrl.id.asc())
        .limit(limit)
        .with_for_update()
    )
    rows = session.execute(stmt).scalars().all()
    for row in rows:
        row.status = 'processing'
        row.updated_at = datetime.utcnow()
    if rows:
        session.flush()
    return rows
 
 
def process_row(session: Session, row: OpportunitiesUrl) -> None:
    """Process a single URL row by calling the scraper service."""
    try:
        result = asyncio.run(process_single_url_from_worker(row))
        
        # Update status based on result
        if isinstance(result, dict):
            if result.get("success"):
                row.status = 'completed'
            elif result.get("skip_reason") == "not_call_for_speaker":
                row.status = 'expired'
            elif result.get("skip_reason") == "closed_for_speaker":
                row.status = 'registration_closed'
            elif result.get("skip_reason") == "multiple_events":
                row.status = 'multiple_events'
            else:
                row.status = 'failed'
                row.retry_count += 1
                time.sleep(5)
        else:
            # Fallback for old boolean return format
            if result:
                row.status = 'completed'
            else:
                row.status = 'failed'
                row.retry_count += 1
                time.sleep(5)
            
    except Exception as e:
        logger.error(f"💥 Error processing URL {row.id}: {e}")
        row.status = 'failed'
        row.retry_count += 1
        time.sleep(3)


async def process_single_url_from_worker(url_record: OpportunitiesUrl) -> bool:
    """
    Process a single URL from the worker and save data to database.
    
    This function:
    1. Extracts opportunity data from the URL
    2. Saves the data to the opportunities table
    3. Triggers background matching process
    4. Returns True if scraping was successful, False otherwise
    
    Args:
        url_record: OpportunitiesUrl database record
        
    Returns:
        bool: True if data was extracted and saved successfully
    """
    try:
        # Create a mock hit object for the build_row function
        hit = {
            'url': url_record.url,
            'title': '',  # Will be extracted from page
            'content': '',  # Will be extracted from page
            'provider': url_record.browser or 'unknown'
        }
        
        # Process the URL using the scraper service
        opportunity_data = await process_single_url(url_record, hit)
        
        # Check if this was skipped due to not being a call for speakers, being closed, missing location data, or multiple events
        if isinstance(opportunity_data, dict) and opportunity_data.get("skip_reason"):
            skip_reason = opportunity_data.get("skip_reason")
            if skip_reason == "not_call_for_speaker":
                return {"success": False, "skip_reason": "not_call_for_speaker"}
            elif skip_reason == "closed_for_speaker":
                return {"success": False, "skip_reason": "closed_for_speaker"}
            elif skip_reason == "multiple_events":
                logger.info(f"Skipping URL {url_record.id} - page contains multiple events (listing page, not a single event)")
                return {"success": False, "skip_reason": "multiple_events"}
        
        if opportunity_data:
            # Save the opportunity data to database
            try:
                save_to_db([opportunity_data])
                # Get the opportunity ID from the saved data
                opportunity_id = get_latest_opportunity_id()
                if opportunity_id:
                    trigger_background_matching(opportunity_id)
                return {"success": True}
            except Exception as save_error:
                logger.error(f"❌ Error saving opportunity data for URL {url_record.id}: {save_error}")
                return {"success": False, "error": str(save_error)}
        else:
            logger.warning(f"⚠️ No opportunity data extracted for URL {url_record.id}")
            return {"success": False, "reason": "no_data_extracted"}
            
    except Exception as e:
        logger.error(f"💥 Error processing URL {url_record.id}: {e}")
        return {"success": False, "error": str(e)}


def get_latest_opportunity_id() -> int:
    """
    Get the ID of the most recently created opportunity.
    
    Returns:
        int: Opportunity ID or None if not found
    """
    try:
        from app.models.opportunities import SpeakerOpportunity
        
        with db_manager.get_session() as session:
            # Get the latest opportunity by ID (highest ID = most recent)
            latest_opportunity = session.query(SpeakerOpportunity).order_by(
                SpeakerOpportunity.id.desc()
            ).first()
            
            if latest_opportunity:
                return latest_opportunity.id
            else:
                logger.warning("⚠️ No opportunities found in database")
                return None
            
    except Exception as e:
        logger.error(f"❌ Error getting latest opportunity ID: {e}")
        return None


def trigger_background_matching(opportunity_id: int) -> None:
    """
    Trigger background speaker matching for a specific opportunity.
    This runs the matching process in a background thread without blocking the scraper.
    
    Args:
        opportunity_id: ID of the opportunity to match
    """
    import threading
    
    def background_matching_task():
        """Background task that handles MySQL-only speaker matching"""
        try:
            trigger_speaker_matching_for_opportunity(opportunity_id)
        except Exception as e:
            logger.error(f"❌ Error in background matching for opportunity {opportunity_id}: {e}")
    
    # Start background task in a separate thread (non-blocking)
    thread = threading.Thread(target=background_matching_task, daemon=True)
    thread.start()

def trigger_speaker_matching_for_opportunity(opportunity_id: int) -> dict:
    """
    Trigger speaker matching for a specific opportunity.
    
    Args:
        opportunity_id: ID of the opportunity to match
        
    Returns:
        dict: Matching result
    """
    try:
        from app.services.scraper_chroma_integration import ScraperChromaIntegration
        # Create integration instance
        integration = ScraperChromaIntegration()
        # Match this specific opportunity with all active speakers
        matching_result = integration.match_new_opportunities_with_speakers([opportunity_id])
        # Clean up
        del integration
        return matching_result
    except Exception as e:
        logger.error(f"❌ Error triggering speaker matching for opportunity {opportunity_id}: {e}")
        return {"success": False, "error": str(e)}


def run_worker_loop() -> None:
    """Run the continuous worker loop with retry logic and proper error handling."""
    # Use centralized database manager
    Session = db_manager.SessionLocal
    consecutive_errors = 0
    
    while True:
        try:
            with Session() as session:
                claimed = claim_pending_rows(session, CLAIM_BATCH_SIZE)
                if not claimed:
                    time.sleep(POLL_INTERVAL_SECONDS)
                    continue
                for row in claimed:
                    try:
                        process_row(session, row)
                    except Exception as exc:
                        logger.error(f"❌ Error processing URL {row.id}: {exc}")
                        row.status = 'failed'
                        row.retry_count += 1
                    # Update timestamp
                    row.updated_at = datetime.utcnow()
                # Commit all changes
                session.commit()
                
        except Exception as exc:
            consecutive_errors += 1
            logger.error(f"💥 Unexpected error: {exc}")
            logger.info("⏳ Waiting 10 seconds before retry...")
            time.sleep(10)


if __name__ == "__main__":
    """Run the worker when executed directly."""
    run_worker_loop()