import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Upload, Palette, Type, Globe, DollarSign, GripVertical, Eye, X } from 'lucide-react';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useGetLandingPageDataQuery, useUpdateLandingPageDataMutation } from '../../apis/affiliatesApi';
import { useUploadFileMutation } from '../../apis/fileUploadApi';
import { useToast } from '../../hooks/use-toast';

// Draggable Section Component for Landing Page Preview
const DraggableLandingSection: React.FC<{
  section: any;
  index: number;
  moveSection: (dragIndex: number, hoverIndex: number) => void;
  children: React.ReactNode;
}> = ({ section, index, moveSection, children }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'landingSection',
    item: { index },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  });

  const [, drop] = useDrop({
    accept: 'landingSection',
    hover: (item: { index: number }) => {
      if (item.index !== index) {
        moveSection(item.index, index);
        item.index = index;
      }
    }
  });

  const dragDropRef = (node: HTMLDivElement | null) => {
    drag(drop(node));
  };

  return (
    <div
      ref={dragDropRef}
      style={{ opacity: isDragging ? 0.5 : 1 }}
      className="relative group"
    >
      <div className="absolute -left-8 top-2 opacity-0 group-hover:opacity-100 transition-opacity cursor-move">
        <GripVertical className="h-4 w-4 text-muted-foreground" />
      </div>
      {children}
    </div>
  );
};

// Section Components for Preview
const HeroSection: React.FC<{ data: any }> = ({ data }) => (
  <div className="bg-gradient-to-br from-primary/10 to-secondary/10 py-16 px-6 text-center">
    {data.logo ? (
      <div className="mb-4 flex justify-center">
        <img src={data.logo} alt="Affiliate Logo" className="h-12 object-contain" />
      </div>
    ) : null}
    <h1 className="text-4xl font-bold mb-4" style={{ color: data.primaryColor }}>
      {data.affiliateName}
    </h1>
    <p className="text-xl mb-8" style={{ color: data.accentColor }}>
      Transform your speaking career with our proven system
    </p>
    <Button
      className="text-lg px-8 py-3"
      style={{
        backgroundColor: data.buttonColor,
        color: data.buttonTextColor
      }}
    >
      Get Started Free
    </Button>
    {data.speakerbotLogo ? (
      <div className="mt-8 flex justify-center opacity-80">
        <img src={data.speakerbotLogo} alt="Speakerbot" className="h-8 object-contain" />
      </div>
    ) : null}
  </div>
);

const OfferSection: React.FC<{ data: any }> = ({ data }) => (
  <div className="py-12 px-6 bg-card">
    <div className="max-w-4xl mx-auto text-center">
      <h2 className="text-3xl font-bold mb-6" style={{ color: data.primaryColor }}>
        {data.offerTitle}
      </h2>
      <div className="grid md:grid-cols-3 gap-4 mb-6">
        {data.offerOptions.map((option: string, index: number) => (
          <div key={index} className="p-4 bg-background rounded-lg border">
            <p className="text-sm">{option}</p>
          </div>
        ))}
      </div>
      <p className="text-2xl font-bold text-accent mb-2">
        Value: ${data.xpValue.toLocaleString()}
      </p>
      <p className="text-sm text-muted-foreground">{data.disclaimer}</p>
    </div>
  </div>
);

const LearningSection: React.FC<{ data: any }> = ({ data }) => (
  <div className="py-12 px-6">
    <div className="max-w-4xl mx-auto">
      <h2 className="text-3xl font-bold mb-8 text-center" style={{ color: data.primaryColor }}>
        What You'll Learn
      </h2>
      <div className="grid md:grid-cols-1 gap-4">
        {data.learningObjectives.map((objective: string, index: number) => (
          <div key={index} className="flex items-start gap-3 p-4 bg-card rounded-lg">
            <div className="w-6 h-6 rounded-full bg-accent flex items-center justify-center text-white text-sm font-bold flex-shrink-0 mt-0.5">
              {index + 1}
            </div>
            <p>{objective}</p>
          </div>
        ))}
      </div>
    </div>
  </div>
);

const ChallengesSection: React.FC<{ data: any }> = ({ data }) => (
  <div className="py-12 px-6 bg-card">
    <div className="max-w-4xl mx-auto">
      <h2 className="text-3xl font-bold mb-8 text-center" style={{ color: data.primaryColor }}>
        Challenges We Solve
      </h2>
      <div className="grid md:grid-cols-1 gap-4">
        {data.challengesSolved.map((challenge: string, index: number) => (
          <div key={index} className="flex items-start gap-3 p-4 bg-background rounded-lg">
            <div className="w-6 h-6 rounded-full bg-destructive flex items-center justify-center text-white text-sm font-bold flex-shrink-0 mt-0.5">
              ✗
            </div>
            <p>{challenge}</p>
          </div>
        ))}
      </div>
    </div>
  </div>
);

const MainOfferSection: React.FC<{ data: any }> = ({ data }) => (
  <div className="py-12 px-6">
    <div className="max-w-4xl mx-auto text-center">
      <h2 className="text-3xl font-bold mb-6" style={{ color: data.primaryColor }}>
        Our Main Offer
      </h2>
      <p className="text-lg mb-8">{data.overarchingOffer}</p>
      <Button
        className="text-lg px-8 py-3"
        style={{
          backgroundColor: data.buttonColor,
          color: data.buttonTextColor
        }}
      >
        Learn More
      </Button>
    </div>
  </div>
);

const DifferentiatorSection: React.FC<{ data: any }> = ({ data }) => (
  <div className="py-12 px-6 bg-card">
    <div className="max-w-4xl mx-auto text-center">
      <h2 className="text-3xl font-bold mb-6" style={{ color: data.primaryColor }}>
        What Makes Us Different
      </h2>
      <p className="text-lg">{data.differentiator}</p>
    </div>
  </div>
);

const CoreBenefitSection: React.FC<{ data: any }> = ({ data }) => (
  <div className="py-12 px-6">
    <div className="max-w-4xl mx-auto text-center">
      <h2 className="text-3xl font-bold mb-6" style={{ color: data.primaryColor }}>
        Core Benefit
      </h2>
      <p className="text-lg">{data.coreBenefit}</p>
    </div>
  </div>
);

const ContactSection: React.FC<{ data: any }> = ({ data }) => (
  <div className="py-12 px-6 bg-card">
    <div className="max-w-4xl mx-auto text-center">
      <h2 className="text-3xl font-bold mb-6" style={{ color: data.primaryColor }}>
        Contact Us
      </h2>
      <div className="space-y-2">
        <p><strong>Email:</strong> {data.email}</p>
        <p><strong>Phone:</strong> {data.phone}</p>
        <p><strong>Website:</strong> {data.website}</p>
      </div>
    </div>
  </div>
);

const LandingPageBuilder: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [showPreview, setShowPreview] = useState(false);
  const logoInputRef = useRef<HTMLInputElement | null>(null);
  const signatureInputRef = useRef<HTMLInputElement | null>(null);

  // API hooks
  const { data: landingPageData, isLoading, isError, refetch } = useGetLandingPageDataQuery(id as string, { skip: !id });
  const [updateLandingPageData, { isLoading: isUpdating }] = useUpdateLandingPageDataMutation();
  const [uploadFile, { isLoading: isUploading }] = useUploadFileMutation();

  // Form state
  const [formData, setFormData] = useState({
    // Section 1 - Basic Information
    affiliateName: "The Speaker's Playhouse",
    legalBusinessName: "Speaker's Playhouse LLC",
    email: "<EMAIL>",
    phone: "",
    website: "https://speakersplayhouse.com",

    // Section 2 - Media and Branding
    logo: null,
    productImages: [],
    primaryColor: '#0ea5e9',
    secondaryColor: '#64748b',
    accentColor: '#22c55e',
    fontStack: 'Inter, system-ui, sans-serif',

    // Section 3 - Free Offer for Speakers
    offerTitle: 'Free offer for new speakers',
    offerOptions: ['Sample: One hour consult. Value 1500.', 'One offer per speaker.', 'Editable, sample only.'],
    xpValue: 1500,
    disclaimer: 'Terms and conditions apply. Limited time offer.',

    // Section 4 - Value to Speakers
    learningObjectives: ['How to create a weekly stage pipeline in one hour.', 'How to turn free stages into list growth and paid bookings.', 'How to fix a talk title and abstract in fifteen minutes.'],
    challengesSolved: ['No steady flow of stages to apply to.', 'Confusing offer and weak call to action.', 'No proof assets such as reel and testimonials.'],
    overarchingOffer: 'We help growth minded speakers build a repeatable booking system. Coaching and community built around a weekly cadence and a talk that sells.',
    differentiator: 'Pipeline first with visible movement in seven days.',
    coreBenefit: 'Leave with a system that fills your calendar and a talk that converts.',

    // Section 5 - Affiliate Program Details
    affiliateCommission: 10,
    trackingLink: 'https://affiliate.example.com/track',
    foundingBadge: true,
    exclusivityWindow: '',

    // Section 6 - Admin and Compliance
    businessEntityType: 'llc',
    reachEstimate: '10,000 monthly visitors',
    promoChannels: 'Newsletter, LinkedIn, Webinars',
    taxDocs: null,
    termsAccepted: false,
    digitalSignature: '',

    // Landing Page Design
    landingPageFont: 'Inter, system-ui, sans-serif',
    buttonColor: '#3b82f6',
    buttonTextColor: '#ffffff'
  });

  // Landing page sections state
  const [sections, setSections] = useState([
    { id: 'hero', title: 'Hero Section', type: 'hero' },
    { id: 'offer', title: 'Free Offer', type: 'offer' },
    { id: 'learning', title: 'What You Will Learn', type: 'learning' },
    { id: 'challenges', title: 'Challenges We Solve', type: 'challenges' },
    { id: 'main-offer', title: 'Our Main Offer', type: 'main-offer' },
    { id: 'differentiator', title: 'What Makes Us Different', type: 'differentiator' },
    { id: 'core-benefit', title: 'Core Benefit', type: 'core-benefit' },
    { id: 'contact', title: 'Contact', type: 'contact' }
  ]);

  // Map API data to form data structure
  const mapApiDataToFormData = (apiData: any) => {
    if (!apiData) return {};

    return {
      // Basic Information
      affiliateName: apiData.name || "",
      legalBusinessName: apiData.company_legal || "",
      email: apiData.contact?.email || "",
      phone: apiData.contact?.phone || "",
      website: apiData.contact?.website || "",

      // Branding
      logo: apiData.branding?.logo_url || null,
      speakerbotLogo: apiData.branding?.speakerbot_logo_url || "",
      fontUrl: apiData.branding?.font_url || "",
      productImages: apiData.branding?.images || [],
      // Map API: title_color -> primaryColor, text_color -> accentColor, background_color -> secondaryColor
      primaryColor: apiData.branding?.title_color || '#1D4ED8',
      secondaryColor: apiData.branding?.background_color || '#64748b',
      accentColor: apiData.branding?.text_color || '#0ea5e9',
      fontStack: apiData.branding?.font_stack || 'Inter, system-ui, sans-serif',

      // Free Offer
      offerTitle: apiData.free_offer?.title || "",
      offerOptions: apiData.free_offer?.options || [],
      xpValue: apiData.free_offer?.xp_value || 0,
      disclaimer: apiData.free_offer?.disclaimer || "",

      // Value to Speakers
      learningObjectives: apiData.value_to_speakers?.takeaways || [],
      challengesSolved: apiData.value_to_speakers?.challenges || [],
      overarchingOffer: apiData.value_to_speakers?.overarching_offer || "",
      differentiator: apiData.value_to_speakers?.differentiator || "",
      coreBenefit: apiData.value_to_speakers?.core_benefit || "",

      // Affiliate Program
      affiliateCommission: parseFloat(apiData.affiliate_program?.commission_percent || "0"),
      trackingLink: apiData.affiliate_program?.affiliate_link_url || "",

      // Admin and Compliance
      businessEntityType: apiData.admin_and_compliance?.business_type_entity?.toLowerCase() || "llc",
      reachEstimate: apiData.admin_and_compliance?.reach_estimate || "",
      promoChannels: apiData.admin_and_compliance?.promo_channels?.join(", ") || "",
      termsAccepted: apiData.admin_and_compliance?.accept_terms_and_conditions || false,
      digitalSignature: apiData.admin_and_compliance?.digital_signature || "",

      // Landing Page Design
      landingPageFont: apiData.branding?.font_stack || 'Inter, system-ui, sans-serif',
      buttonColor: apiData.branding?.button_color || '#3b82f6',
      buttonTextColor: apiData.branding?.button_text_color || '#ffffff'
    };
  };

  // Update form data when API data loads
  useEffect(() => {
    if (landingPageData) {
      const mappedData = mapApiDataToFormData(landingPageData);
      setFormData(prev => ({ ...prev, ...mappedData }));
    }
  }, [landingPageData]);

  const updateFormData = (key: string, value: any) => {
    setFormData(prev => ({ ...prev, [key]: value }));
  };

  const updateArrayField = (field: string, index: number, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: prev[field].map((item: string, i: number) => i === index ? value : item)
    }));
  };


  // Handlers for uploads
  const handleLogoPick = () => {
    logoInputRef.current?.click();
  };

  const [logoFile, setLogoFile] = useState<File | null>(null);
  const [signatureFile, setSignatureFile] = useState<File | null>(null);

  const handleLogoSelected: React.ChangeEventHandler<HTMLInputElement> = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    setLogoFile(file);
    toast({ description: 'Uploading logo...' });
    
    try {
      // Upload immediately when file is selected
      const result = await uploadFile({ file, type: 'logo', affiliateId: id }).unwrap();
      if (result.success) {
        // Update form data with the URL immediately
        setFormData(prev => ({ ...prev, logo: result.url }));
        toast({ description: 'Logo uploaded successfully!' });
      } else {
        throw new Error(result.message || 'Failed to upload logo');
      }
    } catch (error: any) {
      console.error('Logo upload failed:', error);
      toast({ description: 'Failed to upload logo', variant: 'destructive' });
      setLogoFile(null); // Clear the file if upload failed
    }
    
    // Clear the input
    if (logoInputRef.current) logoInputRef.current.value = '';
  };

  const handleSignaturePick = () => {
    signatureInputRef.current?.click();
  };

  const handleSignatureSelected: React.ChangeEventHandler<HTMLInputElement> = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    setSignatureFile(file);
    toast({ description: 'Uploading signature...' });
    
    try {
      // Upload immediately when file is selected
      const result = await uploadFile({ file, type: 'signature', affiliateId: id }).unwrap();
      if (result.success) {
        // Update form data with the URL immediately
        setFormData(prev => ({ ...prev, digitalSignature: result.url }));
        toast({ description: 'Signature uploaded successfully!' });
      } else {
        throw new Error(result.message || 'Failed to upload signature');
      }
    } catch (error: any) {
      console.error('Signature upload failed:', error);
      toast({ description: 'Failed to upload signature', variant: 'destructive' });
      setSignatureFile(null); // Clear the file if upload failed
    }
    
    // Clear the input
    if (signatureInputRef.current) signatureInputRef.current.value = '';
  };

  // Map form data to API format
  const mapFormDataToApiFormat = (formData: any) => {
    return {
      name: formData.affiliateName,
      company_legal: formData.legalBusinessName || null,
      contact: {
        email: formData.email,
        phone: formData.phone || "",
        website: formData.website || ""
      },
      affiliate_program: {
        affiliate_link_url: formData.trackingLink,
        commission_percent: formData.affiliateCommission.toString()
      },
      branding: {
        images: formData.productImages || [],
        speakerbot_logo_url: formData.speakerbotLogo || "",
        font_url: formData.fontUrl || formData.fontStack,
        logo_url: formData.logo || "",
        font_stack: formData.fontStack,
        text_color: formData.accentColor,
        title_color: formData.primaryColor,
        button_color: formData.buttonColor,
        background_color: formData.secondaryColor,
        button_text_color: formData.buttonTextColor
      },
      free_offer: {
        title: formData.offerTitle,
        options: formData.offerOptions || [],
        xp_value: formData.xpValue,
        disclaimer: formData.disclaimer,
        is_placeholder: true
      },
      value_to_speakers: {
        takeaways: formData.learningObjectives || [],
        challenges: formData.challengesSolved || [],
        core_benefit: formData.coreBenefit,
        differentiator: formData.differentiator,
        overarching_offer: formData.overarchingOffer
      },
      admin_and_compliance: {
        promo_channels: formData.promoChannels ? formData.promoChannels.split(',').map((s: string) => s.trim()).filter(Boolean) : [],
        reach_estimate: formData.reachEstimate || "",
        digital_signature: formData.digitalSignature || "",
        compliance_link_url: "https://dsa.to/compliance/playhouse",
        business_type_entity: formData.businessEntityType ? formData.businessEntityType.toUpperCase() : "LLC",
        accept_terms_and_conditions: formData.termsAccepted || false
      }
    };
  };

  // Save landing page data
  const handleSave = async () => {
    if (!id) {
      toast({ description: 'No affiliate ID found', variant: 'destructive' });
      return;
    }

    try {
      // Files are already uploaded and URLs are stored in formData
      // Just create the API payload with the current form data
      const apiData = mapFormDataToApiFormat(formData);
      
     

      const result = await updateLandingPageData({ 
        affiliateId: id, 
        data: apiData,
      }).unwrap();

      if (result.success) {
        toast({ description: 'Landing page saved successfully!' });
        // Clear file states after successful save
        setLogoFile(null);
        setSignatureFile(null);
        // Refetch the data to get updated values from server
        await refetch();
      } else {
        toast({ description: result.message || 'Failed to save landing page', variant: 'destructive' });
      }
    } catch (error: any) {
      console.error('Save failed:', error);
      toast({
        description: error?.data?.message || error?.message || 'Failed to save landing page',
        variant: 'destructive'
      });
    }
  };

  const moveSection = (dragIndex: number, hoverIndex: number) => {
    setSections(prev => {
      const dragSection = prev[dragIndex];
      const newSections = [...prev];
      newSections.splice(dragIndex, 1);
      newSections.splice(hoverIndex, 0, dragSection);
      return newSections;
    });
  };

  const renderSection = (section: any) => {
    switch (section.type) {
      case 'hero':
        return <HeroSection data={formData} />;
      case 'offer':
        return <OfferSection data={formData} />;
      case 'learning':
        return <LearningSection data={formData} />;
      case 'challenges':
        return <ChallengesSection data={formData} />;
      case 'main-offer':
        return <MainOfferSection data={formData} />;
      case 'differentiator':
        return <DifferentiatorSection data={formData} />;
      case 'core-benefit':
        return <CoreBenefitSection data={formData} />;
      case 'contact':
        return <ContactSection data={formData} />;
      default:
        return null;
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background border flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading landing page data...</p>
        </div>
      </div>
    );
  }

  // Error state
  if (isError) {
    return (
      <div className="min-h-screen bg-background border flex items-center justify-center">
        <div className="text-center">
          <p className="text-destructive mb-4">Failed to load landing page data</p>
          <Button onClick={() => navigate(`/admin/affiliates/${id}`)}>
            Back to Affiliate Detail
          </Button>
        </div>
      </div>
    );
  }

  return (
    // <DndProvider backend={HTML5Backend}>
    <div className="min-h-screen bg-background border rounded-md">
      {/* Header */}
      <div className="bg-tertiary">
        <div className="flex items-center justify-between p-4 px-6 border-b">
          <div className="flex items-center gap-4 ">
            <button

              className='border text-sm flex py-1.5 px-3 rounded-md'
              onClick={() => navigate(`/admin/affiliates/${id}`)}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Affiliate Detail
            </button>
            <h1 className="text-base font-semibold text-center flex-1">Landing Page Builder</h1>
          </div>
          <div className="flex items-center gap-2">
            {/* <Button
              variant="default"
              onClick={() => setShowPreview(!showPreview)}
              className="flex items-center gap-2"
            >
              <Eye className="h-4 w-4" />
              {showPreview ? 'Hide Preview' : 'Show Preview'}
            </Button> */}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div>
        {/* Form - Full Width */}
        <div className="w-full bg-card overflow-y-auto ">
          <div className="p-3 text-sm">
            <Tabs defaultValue="basic">
              <TabsList className="grid w-full grid-cols-2 mb-3 bg-tertiary h-12">
                <TabsTrigger value="basic" className=" p-2 h-10">Basic Info</TabsTrigger>
                <TabsTrigger value="advanced" className=" p-2 h-10">Advanced</TabsTrigger>
              </TabsList>

              <TabsContent value="basic" className="space-y-3">
                <Card className='bg-tertiary'>
                  <CardHeader className="pb-4">
                    <CardTitle className="text-xl flex items-center gap-2">
                      <Globe className="size-5" />
                      Basic Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="affiliateName" className="mb-2 block">Affiliate Name *</Label>
                      <Input
                        id="affiliateName"
                        className=" mt-1.5"
                        disabled
                        value={formData.affiliateName}
                        onChange={(e) => updateFormData('affiliateName', e.target.value)}
                        placeholder="Enter affiliate name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="legalBusinessName" className=" mb-2 block">Legal Business Name *</Label>
                      <Input
                        id="legalBusinessName"
                        className=" mt-1.5"
                        disabled
                        value={formData.legalBusinessName}
                        onChange={(e) => updateFormData('legalBusinessName', e.target.value)}
                        placeholder="Enter legal business name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email" className=" mb-2 block">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        className=" mt-1.5"
                        disabled
                        value={formData.email}
                        onChange={(e) => updateFormData('email', e.target.value)}
                        placeholder="Enter email address"
                      />
                    </div>
                    <div>
                      <Label htmlFor="phone" className=" mb-2 block">Phone *</Label>
                      <Input
                        id="phone"
                        className=" mt-1.5"
                        value={formData.phone}
                        disabled
                        onChange={(e) => updateFormData('phone', e.target.value)}
                        placeholder="Enter phone number"
                      />
                    </div>
                    <div>
                      <Label htmlFor="website" className=" mb-2 block">Website</Label>
                      <Input
                        id="website"
                        className=" mt-1.5"
                        value={formData.website}
                        disabled
                        onChange={(e) => updateFormData('website', e.target.value)}
                        placeholder="https://www.example.com"
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Section 2 - Media and Branding */}
                <Card className='bg-tertiary'>
                  <CardHeader className="pb-4">
                    <CardTitle className="text-xl flex items-center gap-2">
                      <Palette className="size-5" />
                      Media and Branding
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label className=" mb-2 block">Logo Upload</Label>
                      <input ref={logoInputRef} type="file" accept="image/*" className="hidden" onChange={handleLogoSelected} />
                      <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 my-6 text-center mt-1.5 cursor-pointer" onClick={handleLogoPick}>
                        {formData.logo ? (
                          <img src={String(formData.logo)} alt="Logo Preview" className="h-12 mx-auto object-contain" />
                        ) : (
                          <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                        )}
                        <p className=" text-muted-foreground">Upload logo (PNG, JPG up to 5MB)</p>
                      </div>
                    </div>
                    {/* <div>
                      <Label className=" mb-1.5 block">Product Images</Label>
                      <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 text-center mt-1.5">
                        <Upload className="h-8 w-8 mx-auto mb-2 text-muted-foreground" />
                        <p className=" text-muted-foreground">Upload product images</p>
                      </div>
                    </div> */}
                    <div className="grid grid-cols-3 gap-2">
                    <div>
                        <Label htmlFor="accentColor" className=" mb-1.5 block">Background Color</Label>
                        <Input
                          id="accentColor"
                          type="color"
                          className="h-8  mt-1.5 w-full"
                          value={formData.accentColor}
                          onChange={(e) => updateFormData('accentColor', e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="secondaryColor" className=" mb-1.5 block">Title Color</Label>
                        <Input
                          id="secondaryColor"
                          type="color"
                          className="h-8  mt-1.5 w-full"
                          value={formData.secondaryColor}
                          onChange={(e) => updateFormData('secondaryColor', e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="primaryColor" className=" mb-1.5 block">Text Color</Label>
                        <Input
                          id="primaryColor"
                          type="color"
                          className="h-8  mt-1.5 w-full"
                          value={formData.primaryColor}
                          onChange={(e) => updateFormData('primaryColor', e.target.value)}
                        />
                      </div>
                      
                      
                    </div>
                    <div>
                      <Label htmlFor="fontStack" className=" mb-2 block">Font Family</Label>
                      <Select value={formData.fontStack} onValueChange={(value) => updateFormData('fontStack', value)}>
                        <SelectTrigger className=" mt-1.5">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={formData.fontStack}>{formData.fontStack}</SelectItem>
                          <SelectItem value="Georgia, serif">Georgia</SelectItem>
                          <SelectItem value="Arial, sans-serif">Arial</SelectItem>
                          <SelectItem value="Times New Roman, serif">Times New Roman</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="grid grid-cols-3 gap-2">
                      <div>
                        <Label htmlFor="buttonColor" className=" mb-1.5 block">Button Color</Label>
                        <Input
                          id="buttonColor"
                          type="color"
                          className="h-8  mt-2 w-full"
                          value={formData.primaryColor}
                          onChange={(e) => updateFormData('primaryColor', e.target.value)}
                        />
                      </div>
                      <div>
                        <Label htmlFor="buttonText" className=" mb-1.5 block">Button Text Color</Label>
                        <Input
                          id="buttonText"
                          type="color"
                          className="h-8  mt-2 w-full"
                          value={formData.buttonColor}
                          onChange={(e) => updateFormData('primaryColor', e.target.value)}
                        />
                      </div>
                    </div>

                  </CardContent>
                </Card>

                {/* Section 3 - Free Offer for Speakers */}
                <Card className='bg-tertiary'>
                  <CardHeader className="pb-4">
                    <CardTitle className="text-xl flex items-center gap-2">
                      <DollarSign className="size-5" />
                      Free Offer for Speakers
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="offerTitle" className=" mb-2 block">Offer Title</Label>
                      <Input
                        id="offerTitle"
                        className=" mt-1.5"
                        value={formData.offerTitle}
                        onChange={(e) => updateFormData('offerTitle', e.target.value)}
                        placeholder="Free offer for new speakers"
                      />
                    </div>
                    <div>
                      <Label className=" mb-2 block">Offer Options</Label>
                      {formData.offerOptions.map((option: string, index: number) => (
                        <Input
                          key={index}
                          className=" mt-1.5 mb-2"
                          value={option}
                          onChange={(e) => updateArrayField('offerOptions', index, e.target.value)}
                          placeholder={`Option ${index + 1}`}
                        />
                      ))}
                    </div>
                    <div>
                      <Label htmlFor="xpValue" className="mb-2 block">Expected Value ($)</Label>
                      <Input
                        id="xpValue"
                        type="number"
                        className="mt-1.5"
                        value={formData.xpValue}
                        onChange={(e) => updateFormData('xpValue', parseInt(e.target.value))}
                        placeholder="1500"
                      />
                    </div>
                    <div>
                      <Label htmlFor="disclaimer" className="mb-1.5 block">Disclaimer</Label>
                      <Textarea
                        id="disclaimer"
                        className="mt-1.5"
                        rows={1}
                        value={formData.disclaimer}
                        onChange={(e) => updateFormData('disclaimer', e.target.value)}
                        placeholder="Terms and conditions apply..."
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Section 4 - Value to Speakers */}

              </TabsContent>

              <TabsContent value="advanced" className="space-y-4">
                <Card className='bg-tertiary'>
                  <CardHeader className="pb-4">
                    <CardTitle className="text-sm flex items-center gap-2">
                      <Type className="h-4 w-4" />
                      Value to Speakers
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      {formData.learningObjectives.length!==0 &&<Label className="mb-2 bloc text-md">Learning Objectives</Label>}
                      {formData.learningObjectives.map((objective: string, index: number) => (
                        <Textarea
                          key={index}
                          className="mt-1.5 mb-2 min-h-[50px]"
                          value={objective}
                          onChange={(e) => updateArrayField('learningObjectives', index, e.target.value)}
                          placeholder={`Learning objective ${index + 1}`}
                        />
                      ))}
                    </div>
                    <div>
                    {formData.learningObjectives.length!==0 &&<Label className="mb-2 block text-">Challenges Solved</Label>}
                      
                      {formData.challengesSolved.map((challenge: string, index: number) => (
                        <Textarea
                          key={index}
                          className="mt-1.5 mb-2 min-h-[60px]"
                          value={challenge}
                          onChange={(e) => updateArrayField('challengesSolved', index, e.target.value)}
                          placeholder={`Challenge ${index + 1}`}
                        />
                      ))}
                    </div>
                    <div>
                      <Label htmlFor="overarchingOffer" className=" mb-2 block text-md">Overarching Offer</Label>
                      <Textarea
                        id="overarchingOffer"
                        className=" mt-1.5 min-h-[80px]"
                        value={formData.overarchingOffer}
                        onChange={(e) => updateFormData('overarchingOffer', e.target.value)}
                        placeholder="Describe your main offer..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="differentiator" className=" mb-2 block text-md">Differentiator</Label>
                      <Input
                        id="differentiator"
                        className=" mt-1.5"
                        value={formData.differentiator}
                        onChange={(e) => updateFormData('differentiator', e.target.value)}
                        placeholder="What makes you different..."
                      />
                    </div>
                    <div>
                      <Label htmlFor="coreBenefit" className=" mb-2 block text-md">Core Benefit</Label>
                      <Input
                        id="coreBenefit"
                        className=" mt-1.5"
                        value={formData.coreBenefit}
                        onChange={(e) => updateFormData('coreBenefit', e.target.value)}
                        placeholder="Main benefit for speakers..."
                      />
                    </div>
                  </CardContent>
                </Card>
                {/* Section 5 - Affiliate Program Details */}
                <Card className='bg-tertiary'>
                  <CardHeader className="pb-4">
                    <CardTitle className="text-xl flex items-center gap-2">
                      <DollarSign className="size-5" />
                      Affiliate Program Details
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="affiliateCommission" className="mb-2 block">Affiliate Commission (%)</Label>
                      <Input
                        id="affiliateCommission"
                        type="number"
                        className=" mt-1.5"
                        value={formData.affiliateCommission}
                        onChange={(e) => updateFormData('affiliateCommission', parseInt(e.target.value))}
                        placeholder="10"
                      />
                    </div>
                    <div>
                      <Label htmlFor="trackingLink" className=" mb-2 block">Tracking Link</Label>
                      <Input
                        id="trackingLink"
                        className=" mt-1.5"
                        value={formData.trackingLink}
                        onChange={(e) => updateFormData('trackingLink', e.target.value)}
                        placeholder="https://affiliate.example.com/track"
                      />
                    </div>
                  </CardContent>
                </Card>

                {/* Section 6 - Admin and Compliance */}
                <Card className='bg-tertiary'>
                  <CardHeader className="pb-4">
                    <CardTitle className="text-md flex items-center gap-2">
                      <Upload className="h-4 w-4" />
                      Admin and Compliance
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="businessEntityType" className=" mb-2 block">Business Entity Type</Label>
                      <Select value={formData.businessEntityType} onValueChange={(value) => updateFormData('businessEntityType', value)}>
                        <SelectTrigger className=" mt-1.5">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value={formData.businessEntityType}>{formData.businessEntityType}</SelectItem>
                          {/* <SelectItem value="corporation">Corporation</SelectItem>
                          <SelectItem value="partnership">Partnership</SelectItem>
                          <SelectItem value="sole-proprietorship">Sole Proprietorship</SelectItem> */}
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="reachEstimate" className=" mb-2 block">Reach Estimate</Label>
                      <Input
                        id="reachEstimate"
                        className="h-8  mt-1.5"
                        value={formData.reachEstimate}
                        onChange={(e) => updateFormData('reachEstimate', e.target.value)}
                        placeholder="10,000 monthly visitors"
                      />
                    </div>
                    <div>
                      <Label htmlFor="promoChannels" className=" mb-2 block">Promotional Channels</Label>
                      <Textarea
                        id="promoChannels"
                        className=" mt-1.5 min-h-[60px]"
                        value={formData.promoChannels}
                        onChange={(e) => updateFormData('promoChannels', e.target.value)}
                        placeholder="List your promotional channels..."
                      />
                    </div>
                    <div className="flex items-center space-x-2 mb-5">
                      <Checkbox
                        id="termsAccepted"
                        checked={formData.termsAccepted}
                        onCheckedChange={(checked) => updateFormData('termsAccepted', checked)}
                      />
                      <Label htmlFor="termsAccepted">I accept the terms and conditions</Label>
                    </div>
                    <div>
                      <Label className=" mb-2 text-md block">Digital Signature</Label>
                      <input ref={signatureInputRef} type="file" accept="image/*" className="hidden" onChange={handleSignatureSelected} />
                      <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-4 text-center mt-1.5 cursor-pointer" onClick={handleSignaturePick}>
                        {formData.digitalSignature ? (
                          <img src={String(formData.digitalSignature)} alt="Signature Preview" className="h-10 mx-auto object-contain" />
                        ) : (
                          <Upload className="h-4 w-4 mx-auto mb-1 text-muted-foreground" />
                        )}
                        <p className=" text-muted-foreground">Upload signature (PNG, JPG)</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>

            {/* Save Button at Bottom */}
            <div className="py-4">
              <Button
                className="w-full "
                onClick={handleSave}
                disabled={isUpdating || isLoading}
              >
                {isUpdating ? 'Saving...' : 'Save Landing Page'}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Full Size Preview Modal */}
      <Dialog open={showPreview} onOpenChange={setShowPreview}>
        <DialogContent className="max-w-[95vw] max-h-[95vh] w-full h-full p-0">
          <DialogHeader className="p-4 border-b">
            <DialogTitle className="flex items-center justify-between">
              <span>Landing Page Preview</span>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowPreview(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </DialogTitle>
          </DialogHeader>
          <div className="flex-1 overflow-y-auto">
            <div
              className="min-h-full"
              style={{
                fontFamily: formData.landingPageFont,
                '--primary': formData.primaryColor,
                '--secondary': formData.secondaryColor,
                '--accent': formData.accentColor
              } as React.CSSProperties}
            >
              {sections.map((section, index) => (
                <DraggableLandingSection
                  key={section.id}
                  section={section}
                  index={index}
                  moveSection={moveSection}
                >
                  {renderSection(section)}
                </DraggableLandingSection>
              ))}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
    // </DndProvider>
  );
};

export default LandingPageBuilder;
