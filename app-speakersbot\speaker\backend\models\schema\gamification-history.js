const connection = require('../connection');
const { DataTypes } = require('sequelize');

const GamificationHistory = connection.define('GamificationHistory', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    comment: 'Primary key for the setting',
  },
  speaker_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  gamification_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  points: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  type:{
    type: DataTypes.ENUM('credit', 'redemption'),
    allowNull: true,
    comment: 'Additional metadata related to the gamification event',
  },
  balance:{ 
    type: DataTypes.DECIMAL(10, 2),
    allowNull: true,
    comment: 'Balance after the gamification event',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Record creation timestamp',
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Record last update timestamp',
  },
  deleted_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Record deletion timestamp(soft delete)',
  }
}, {
  tableName: 'gamification_history',
  timestamps: true,
  createdAt: "created_at",
  updatedAt: "updated_at",
  paranoid: true,
  deletedAt: "deleted_at",

});
module.exports = GamificationHistory;