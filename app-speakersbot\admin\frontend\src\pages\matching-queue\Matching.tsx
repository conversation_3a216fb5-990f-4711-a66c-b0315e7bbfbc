import { Skeleton } from "@/components/ui/skeleton";
import { useDebouncedCallback } from "@/utils/debounce";
import { getStatusBadgeClass } from "@/utils/status-colors";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExportOutlined,
  EyeOutlined,
  HeartOutlined,
  InfoCircleOutlined,
  TagsOutlined,
} from "@ant-design/icons";
import { zodResolver } from "@hookform/resolvers/zod";
import dayjs from "dayjs";
import relativeTime from "dayjs/plugin/relativeTime";
import { ExternalLink } from "lucide-react";
import React, { useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { Link, useNavigate } from "react-router-dom";
import { z } from "zod";
import {
  useGetMatchesQuery,
  useLazyExportMatchingQueueQuery,
} from "../../apis/matchingApi";
import { useGetEventTypesQuery } from "../../apis/opportunitiesApi";
import ButtonLoader from "../../components/common/ButtonLoader";
import { InvalidTokenHandler } from "../../components/common/InvalidTokenHandler";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "../../components/ui/alert-dialog";
import { Badge } from "../../components/ui/badge";
import { Button } from "../../components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "../../components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "../../components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../components/ui/form";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "../../components/ui/pagination";
import { Progress } from "../../components/ui/progress";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../components/ui/select";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
} from "../../components/ui/sheet";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../components/ui/table";
import { Textarea } from "../../components/ui/textarea";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "../../components/ui/tooltip";
import { toast } from "../../hooks/use-toast";
import type { MatchStatus, UserRole } from "../../types";
import { getPermissions } from "../../utils/permissions";
import FilterBar from "./components/FilterBar";
import StatsCards from "./components/StatsCards";
import MatchingTableSkeleton from "./skeleton/MatchingTableSkeleton";

dayjs.extend(relativeTime);


const Matching: React.FC = () => {
  const navigate = useNavigate();
  // Shadcn form for rejection modal
  const rejectSchema = z.object({
    reason: z.string().min(1, "Please provide a reason for rejection"),
  });
  type RejectFormValues = z.infer<typeof rejectSchema>;
  const rejectForm = useForm<RejectFormValues>({
    resolver: zodResolver(rejectSchema),
    defaultValues: { reason: "" },
  });


  // Mock user for permissions
  const user = { role: "admin" as UserRole, id: "admin-1" };
  const permissions = getPermissions(user?.role);
  const [dateRange, setDateRange] = useState(null);
  // Load filters from localStorage
  const [filters, setFilters] = useState(() => {
    return {
      statusFilter: [],
      eventIdFilter: "",
      eventTypeFilter: "",
    };
  });

  // State management
  const [isRejectModalVisible, setIsRejectModalVisible] = useState(false);
  const [isDrawerVisible, setIsDrawerVisible] = useState(false);
  const [selectedMatch, setSelectedMatch] = useState<any | null>(null);
  const [confirmExportOpen, setConfirmExportOpen] = useState(false);
  // Server-side pagination state
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  // Combobox popover states
  const [isEventTypeOpen, setIsEventTypeOpen] = useState(false);
  const [isStatusOpen, setIsStatusOpen] = useState(false);
  const [searchText, setSearchText] = useState<string>("");

  // Save filters to localStorage
  useEffect(() => {
    setPage(1);
  }, [filters, dateRange]);

  const updateFilter = (key: string, value: any) => {
    setFilters((prev) => ({ ...prev, [key]: value }));
  };

  const setSearchDebounced = useDebouncedCallback<[string]>((value) => {
    updateFilter("eventIdFilter", value);
  }, 400);

  const apiQueryParams = useMemo(
    () => ({
      page,
      limit: pageSize,
      search: filters.eventIdFilter,

      filter: (() => {
        const filterObj: Record<any, any> = {};
        // Only send status when not 'all'
        if (filters.statusFilter[0] && filters.statusFilter[0] !== "all") {
          filterObj.status = String(filters.statusFilter[0]);
        }
        if (filters.eventTypeFilter && filters.eventTypeFilter !== "all") {
          filterObj.event_type = String(filters.eventTypeFilter);
        }
        return Object.keys(filterObj).length
          ? JSON.stringify(filterObj)
          : undefined;
      })(),
      dateRange: (() => {
        if (!dateRange || !dateRange[0] || !dateRange[1]) return undefined;
        return JSON.stringify({
          start_date: dateRange[0].format("YYYY-MM-DD"),
          end_date: dateRange[1].format("YYYY-MM-DD"),
        });
      })(),
    }),
    [
      page,
      pageSize,
      filters.statusFilter,
      filters.eventTypeFilter,
      filters.eventIdFilter,
      dateRange,
    ]
  );

  // Fetch matches from API
  const {
    data: apiMatchesData,
    isLoading: isMatchesLoading,
    refetch: refetchMatches,
    isFetching: isMatchesFetching,
    error: matchesError,
  } = useGetMatchesQuery(apiQueryParams, {
    refetchOnMountOrArgChange: true,
    refetchOnReconnect: true,
  });

  const [triggerExport, { isLoading: isExporting }] = useLazyExportMatchingQueueQuery();

  // Fetch event types for filter
  const { data: eventTypesResponse, isLoading: isEventTypesLoading } =
    useGetEventTypesQuery();
  // Source matches from API
  const sourceMatches = useMemo(() => {
    const apiList =
      (apiMatchesData as any)?.data?.matchingQueue ??
      (apiMatchesData as any)?.data ??
      apiMatchesData;
    return Array.isArray(apiList) ? (apiList as any[]) : [];
  }, [apiMatchesData]);

  // Derive event types from opportunity data; fallback to API list
  const derivedEventTypes = useMemo(() => {
    const set = new Set<string>();
    for (const m of sourceMatches) {
      const et = m?.opportunity?.event_type || m?.opportunity?.eventType;
      if (et && typeof et === "string") set.add(et);
    }
    return Array.from(set);
  }, [sourceMatches]);
  const eventTypes: string[] = useMemo(() => {
    // if (derivedEventTypes.length > 0) return derivedEventTypes;
    const raw = (eventTypesResponse as any)?.data ?? eventTypesResponse;
    return Array.isArray(raw) ? raw.filter(Boolean) : [];
  }, [eventTypesResponse, derivedEventTypes]);

  // Summary from API
  const summaryData = useMemo(() => {
    return (apiMatchesData as any)?.data?.summary || null;
  }, [apiMatchesData]);

  // API pagination data
  const apiPagination = useMemo(() => {
    const p = (apiMatchesData as any)?.pagination;
    const total = typeof p?.total === "number" ? p.total : sourceMatches.length;
    const limit = typeof p?.limit === "number" ? p.limit : pageSize;
    const current = typeof p?.page === "number" ? p.page : page;
    const totalPages =
      typeof p?.totalPages === "number"
        ? p.totalPages
        : Math.max(1, Math.ceil((total || 1) / (limit || 1)));
    return { total, limit, page: current, totalPages };
  }, [apiMatchesData, sourceMatches.length, pageSize, page]);

  // Enhanced matches with normalized fields
  const enhancedMatches = useMemo(() => {
    return sourceMatches.map((match: any) => {
      // Normalize API fields (snake_case -> camelCase) and compute matchingScore
      const normalized = {
        id: match.id ?? match.match_id ?? match.matchId,
        speakerId: match.speakerId ?? match.speaker_id,
        opportunityId: match.opportunityId ?? match.opportunity_id,
        speakerEmail:
          match.speakerEmail ??
          match.speaker_email ??
          match.speaker?.email ??
          "",
        status: match.status ?? "pending",
        createdAt: match.createdAt ?? match.created_at,
        updatedAt: match.updatedAt ?? match.updated_at,
        matchingScore:
          match.matchingScore ??
          (typeof match.overall_score === "number"
            ? Math.round(match.overall_score)
            : 0),
        proposalSummary:
          match.proposalSummary ??
          match.proposal_summary ??
          match.recommendation ??
          "",
        keyThemes: Array.isArray(match.keyThemes)
          ? match.keyThemes
          : match.key_themes
          ? String(match.key_themes)
              .split(",")
              .map((s) => s.trim())
              .filter(Boolean)
          : [],
        matchStrengths: Array.isArray(match.matchStrengths)
          ? match.matchStrengths
          : match.match_strengths
          ? [String(match.match_strengths)]
          : [],
        matchConcerns: Array.isArray(match.matchConcerns)
          ? match.matchConcerns
          : match.match_concerns
          ? [String(match.match_concerns)]
          : [],
      } as any;

      const speaker = match.speaker || null;
      const opportunity = match.opportunity || null;

      // Follow-through (placeholder, backend can supply later)
      const followThroughData = {
        hasApplied:
          typeof match.application_submitted === "boolean"
            ? match.application_submitted
            : false,
        applicationDate: null as string | null,
        isBooked: false,
        bookingDate: null as string | null,
        bookingValue: null as number | null,
        feedbackScore: null as number | null,
      };

      return {
        ...normalized,
        speaker,
        opportunity,
        speakerName: speaker?.name || "---",
        opportunityTitle: opportunity?.title || "---",
        opportunityUrl: opportunity?.event_url || "---",
        opportunityBudget: opportunity?.budget || 0,
        ...followThroughData,
      };
    });
  }, [sourceMatches]);

  // Filter matches with enhanced logic (disabled: rely on server)
  const filteredMatches = useMemo(() => {
    return enhancedMatches;
  }, [enhancedMatches]);

  // Shadcn table state (server-side pagination)
  const totalRows = apiPagination.total;
  const totalPages = apiPagination.totalPages;
  const currentPage = apiPagination.page;
  const pagedMatches = enhancedMatches; // API already returns a single page

  // Helpers and actions
  const getPhoneNumber = (speaker: any) => speaker?.phone_number || "";

  // Get status icon
  const getStatusIcon = (status: MatchStatus) => {
    switch (status) {
      case "interested":
        return <HeartOutlined />;
      case "accepted":
        return <CheckCircleOutlined />;
      case "rejected":
        return <CloseCircleOutlined />;
      default:
        return null;
    }
  };

  // Open match drawer
  const openMatchDrawer = (match: any) => {
    setSelectedMatch({ ...match });
    setIsDrawerVisible(true);
  };

  // Handle status segment change
  const handleStatusSegmentChange = (value: string) => {
    if (!selectedMatch || !permissions.canEdit) return;
    if (value === "rejected") {
      setIsRejectModalVisible(true);
    } else {
      // This function is not defined in the provided code, so it will cause an error.
      // Assuming it will be implemented elsewhere or is a placeholder.
      // For now, we'll just update the status in the drawer.
      setSelectedMatch((prev: any) => ({
        ...prev,
        status: value as MatchStatus,
      }));
    }
  };

  // Handle reject submit
  const handleRejectSubmit = (values: { reason: string }) => {
    if (!selectedMatch) return;
    // This function is not defined in the provided code, so it will cause an error.
    // Assuming it will be implemented elsewhere or is a placeholder.
    // For now, we'll just update the status in the drawer.
    setSelectedMatch((prev: any) => ({
      ...prev,
      status: "rejected",
      rejectionReason: values.reason,
    }));
    setIsRejectModalVisible(false);
    rejectForm.reset({ reason: "" });
  };

  // Handle export
  const handleExport = async () => {
    try {
      const params = { ...apiQueryParams } as any;
      const blob = await triggerExport(params).unwrap();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `matching-queue-${dayjs().format("YYYY-MM-DD")}.csv`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
      toast({ description: `Export started` });
    } catch (error: any) {
      toast({
        description: error?.data?.message || "Failed to export",
        variant: "destructive",
      });
    } finally {
      setConfirmExportOpen(false);
    }
  };

  // Status counts from summary (fallback to compute)
  const statusCounts = useMemo(() => {
    if (summaryData) {
      return {
        total: summaryData.totalMatches ?? sourceMatches.length,
        pending: summaryData.pendingMatches ?? 0,
        interested: summaryData.interestedMatches ?? 0,
        accepted: summaryData.acceptedMatches ?? 0,
        rejected: summaryData.rejectedMatches ?? 0,
      };
    }
    const counts = sourceMatches.reduce((acc: any, match: any) => {
      acc[match.status] = (acc[match.status] || 0) + 1;
      return acc;
    }, {} as Record<MatchStatus, number>);
    return {
      total: sourceMatches.length,
      pending: counts.pending || 0,
      interested: counts.interested || 0,
      accepted: counts.accepted || 0,
      rejected: counts.rejected || 0,
    };
  }, [summaryData, sourceMatches]);

  // Follow-through metrics from summary (fallback to mock)
  const followThroughStats = useMemo(() => {
    if (summaryData) {
      return {
        appliedCount: summaryData.appliedMatches ?? 0,
        bookedCount: summaryData.bookedMatches ?? 0,
        bookingRate: summaryData.conversionMatches ?? 0,
      };
    }
    const appliedCount = enhancedMatches.filter(
      (m: any) => m.hasApplied
    ).length;
    const bookedCount = enhancedMatches.filter((m: any) => m.isBooked).length;
    const bookingRate =
      appliedCount > 0 ? Math.round((bookedCount / appliedCount) * 100) : 0;
    return { appliedCount, bookedCount, bookingRate };
  }, [summaryData, enhancedMatches]);

  return (
    <>
      <InvalidTokenHandler error={matchesError} />

      <div>
        {/* Header */}
        <div
          className="flex justify-between items-center"
          style={{ marginBottom: 24 }}
        >
          <div>
            <h1 className="m-0 text-2xl font-bold text-foreground">
              Matching Queue
            </h1>
            <p className="text-sm text-muted-foreground mt-2">
              Comprehensive speaker-opportunity match management
            </p>
          </div>
          <div>
            <div className="flex gap-2">
              <AlertDialog
                open={confirmExportOpen}
                onOpenChange={setConfirmExportOpen}
              >
                <AlertDialogTrigger asChild>
                  <ButtonLoader
                    variant="default"
                    loading={isExporting}
                    disabled={!permissions.canImportExport}
                  >
                    <ExportOutlined className="mr-2" />
                    Export CSV
                  </ButtonLoader>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Export matching queue?</AlertDialogTitle>
                    <AlertDialogDescription>
                      This will export the current filtered results to CSV.
                      Continue?
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel disabled={isExporting}>
                      Cancel
                    </AlertDialogCancel>
                    <AlertDialogAction
                      onClick={handleExport}
                      disabled={isExporting}
                    >
                      {isExporting ? "Exporting..." : "Confirm Export"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        <StatsCards
          isLoading={isMatchesLoading}
          statusCounts={statusCounts}
          followThroughStats={followThroughStats}
        />

        {/* Enhanced Filter Bar */}
        <FilterBar
          isLoading={isMatchesLoading}
          filters={filters}
          eventTypes={eventTypes}
          dateRange={dateRange}
          isEventTypeOpen={isEventTypeOpen}
          isStatusOpen={isStatusOpen}
          setIsEventTypeOpen={setIsEventTypeOpen}
          setIsStatusOpen={setIsStatusOpen}
          searchText={searchText}
          setSearchText={setSearchText}
          setSearchDebounced={setSearchDebounced}
          updateFilter={updateFilter}
          setDateRange={setDateRange}
        />
        {/* Table */}
        <div className="mt-6">
          <div className="flex items-center justify-between mb-4">
            <span className="text-muted-foreground text-sm">
              {isMatchesLoading ? (
                <Skeleton className="h-[20px] w-[200px] rounded-lg" />
              ) : (
                `Showing ${pagedMatches.length} of ${totalRows} matches`
              )}
            </span>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground text-sm">
                Rows per page
              </span>
              <Select
                value={String(pageSize)}
                onValueChange={(v) => {
                  setPageSize(Number(v));
                  setPage(1);
                }}
              >
                <SelectTrigger className="w-[80px]">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="10">10</SelectItem>
                  <SelectItem value="25">25</SelectItem>
                  <SelectItem value="50">50</SelectItem>
                  <SelectItem value="100">100</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {isMatchesLoading || isMatchesFetching ? (
            <MatchingTableSkeleton />
          ) : pagedMatches.length === 0 ? (
            <Card className="bg-tertiary">
              <div className="flex flex-col items-center justify-center py-12">
                <div className="text-center">
                  <h4 className="text-lg font-semibold mb-2">No Matches Found</h4>
                  <p className="text-muted-foreground">
                    {searchText || dateRange 
                      ? 'No matches match your current filters. Try adjusting your search criteria.'
                      : 'No speaker-event matches are available at the moment.'
                    }
                  </p>
                </div>
              </div>
            </Card>
          ) : (
            <div className="bg-tertiary border rounded-lg">
              <Table className="whitespace-nowrap">
                <TableHeader>
                  <TableRow>
                    <TableHead className="select-none">Speaker Info</TableHead>
                    <TableHead className="select-none">Event Name</TableHead>
                    <TableHead className="select-none">Match %</TableHead>
                    <TableHead className="select-none">Status</TableHead>
                    <TableHead className="select-none">
                      Start-End Date
                    </TableHead>
                    <TableHead className="select-none">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {pagedMatches.map((record: any) => (
                    <TableRow key={record.id} className="cursor-pointer">
                      <TableCell>
                        {(() => {
                          const speaker = record.speaker;
                          if (!speaker)
                            return (
                              <span className="text-sm text-muted-foreground">
                                Speaker not found
                              </span>
                            );
                          return (
                            <div className="flex flex-col space-y-1 pr-2">
                              <span className="font-medium text-foreground truncate">
                                {speaker.name}
                              </span>
                              <span className="text-sm text-muted-foreground truncate">
                                {speaker.email}
                              </span>
                              <span className="text-xs text-muted-foreground">
                                {getPhoneNumber(speaker)}
                              </span>
                            </div>
                          );
                        })()}
                      </TableCell>
                      <TableCell className="">
                        {(() => {
                          const opportunity = record.opportunity;
                          const location = opportunity
                            ? [
                                opportunity.city,
                                opportunity.state,
                                opportunity.country,
                              ]
                                .filter(Boolean)
                                .join(", ") || "Location TBD"
                            : "Location TBD";
                          return (
                            <div className="flex items-center gap-2">
                              <div className="flex flex-col">
                                <div
                                  className="text-xs text-muted-foreground mb-1 max-w-[220px] truncate"
                                  title={location}
                                >
                                  {location}
                                </div>
                                <div
                                  className="text-sm font-medium max-w-[220px] truncate"
                                  title={record.opportunityTitle}
                                >
                                  {record.opportunityTitle}
                                </div>
                              </div>
                              <Link
                                className="h-6 w-6 shrink-0 bg-muted rounded-md flex items-center justify-center"
                                to={record?.opportunityUrl}
                                target="_blank"
                              >
                                <ExternalLink className="h-3 w-3" />
                              </Link>
                            </div>
                          );
                        })()}
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <div className="w-[50px]">
                            <Progress value={record.matchingScore} />
                          </div>
                          <span className="text-xs font-medium">
                            {record.matchingScore}%
                          </span>
                        </div>
                      </TableCell>
                      <TableCell>
                        {(() => {
                          const tag = (
                            record.status && (
                            <Badge
                              className={`${getStatusBadgeClass(
                                record.status
                              )} px-2 py-1`}
                            >
                              <span className="inline-flex items-center gap-2">
                                {getStatusIcon(record.status)}
                                {record.status.charAt(0).toUpperCase() +
                                  record.status.slice(1)}
                              </span>
                            </Badge>
                            )
                          );
                          if (
                            record.status == "rejected" &&
                            record.rejectionReason
                          ) {
                            return (
                              <div className="flex items-center gap-2">
                                {tag}
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <InfoCircleOutlined className="text-muted-foreground cursor-help" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      {record.rejectionReason}
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            );
                          }
                          if (record.status === "accepted") {
                            return (
                              <div className="flex items-center gap-2">
                                {tag}
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <InfoCircleOutlined className="text-green-500 cursor-help" />
                                    </TooltipTrigger>
                                    <TooltipContent>
                                      Match has been accepted by the speaker
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            );
                          }
                          return tag;
                        })()}
                      </TableCell>
                      <TableCell>
                        {(() => {
                          const opportunity = record.opportunity;
                          const startRaw =
                            opportunity?.start_date || opportunity?.startDate;
                          const endRaw =
                            opportunity?.end_date || opportunity?.endDate;
                          if (!startRaw && !endRaw)
                            return (
                              <span className="text-xs text-muted-foreground">
                                No date
                              </span>
                            );
                          const startDate = startRaw ? dayjs(startRaw) : null;
                          const endDate = endRaw
                            ? dayjs(endRaw)
                            : startDate
                            ? startDate.add(1, "day")
                            : null;
                          return (
                            <div className="text-xs">
                              <div>
                                {startDate ? startDate.format("MMM DD") : ""} -{" "}
                                {endDate ? endDate.format("MMM DD, YYYY") : ""}
                              </div>
                              {/* {startDate && endDate && (
                                <div className="text-muted-foreground">
                                  {startDate.format("HH:mm")} -{" "}
                                  {endDate.format("HH:mm")}
                                </div>
                              )} */}
                            </div>
                          );
                        })()}
                      </TableCell>
                      <TableCell>
                        <div>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    openMatchDrawer(record);
                                  }}
                                >
                                  <EyeOutlined />
                                </Button>
                              </TooltipTrigger>
                              <TooltipContent>View Details</TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              <div className="flex items-center justify-center p-4 gap-5">
                <Pagination style={{ margin: 0, width: "auto" }}>
                  <PaginationContent>
                    <PaginationItem>
                      <PaginationPrevious
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          setPage((p) => Math.max(1, (p ?? currentPage) - 1));
                        }}
                      />
                    </PaginationItem>
                    {(() => {
                      const maxPage = totalPages;
                      const pages: (number | "ellipsis")[] = [];
                      const showRange = 2;
                      const start = Math.max(1, currentPage - showRange);
                      const end = Math.min(maxPage, currentPage + showRange);
                      if (start > 1) {
                        pages.push(1);
                        if (start > 2) pages.push("ellipsis");
                      }
                      for (let i = start; i <= end; i++) pages.push(i);
                      if (end < maxPage) {
                        if (end < maxPage - 1) pages.push("ellipsis");
                        pages.push(maxPage);
                      }
                      return pages.map((p, idx) => {
                        if (p === "ellipsis")
                          return <PaginationEllipsis key={`e-${idx}`} />;
                        return (
                          <PaginationItem key={p}>
                            <PaginationLink
                              href="#"
                              isActive={p === currentPage}
                              onClick={(e) => {
                                e.preventDefault();
                                setPage(p as number);
                              }}
                            >
                              {p}
                            </PaginationLink>
                          </PaginationItem>
                        );
                      });
                    })()}
                    <PaginationItem>
                      <PaginationNext
                        href="#"
                        onClick={(e) => {
                          e.preventDefault();
                          const maxPage = totalPages;
                          setPage((p) =>
                            Math.min(maxPage, (p ?? currentPage) + 1)
                          );
                        }}
                      />
                    </PaginationItem>
                  </PaginationContent>
                </Pagination>
              </div>
            </div>
          )}
        </div>

        {/* Match Detail Sheet */}
        <Sheet open={isDrawerVisible} onOpenChange={setIsDrawerVisible}>
          <SheetContent className="w-[600px] p-0">
            <SheetHeader className="px-[25px] py-4">
              <SheetTitle className="text-base font-semibold">
                Match Details
              </SheetTitle>
            </SheetHeader>
            {selectedMatch && (
              <div className="w-full px-[25px] py-8 border-t space-y-6 overflow-y-auto max-h-[calc(100vh-56px)]">
                {/* Match Information */}
                <Card className="bg-tertiary">
                  <CardHeader className="border-b py-2.5 px-3 rounded-lg">
                    <CardTitle className="text-sm font-semibold">
                      Match Information
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4 p-3">
                    <div className="grid grid-cols-1 border rounded-lg">
                      <div className="flex items-center">
                        <div className="bg-tertiary-foreground basis-1/3 px-4 py-2.5 rounded-tl-lg">
                          <label className="text-sm font-medium text-gray-500">
                            Match ID
                          </label>
                        </div>
                        <div className="basis-2/3 px-4 py-2.5 rounded-tr-lg border-b">
                          <p className="text-sm font-mono">
                            {selectedMatch.id}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <div className="bg-tertiary-foreground basis-1/3 px-4 py-2.5">
                          <label className="text-sm font-medium text-gray-500">
                            Speaker Email
                          </label>
                        </div>
                        <div className="basis-2/3 px-4 py-2.5 border-b">
                          <p className="text-sm">
                            {selectedMatch.speakerEmail}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <div className="bg-tertiary-foreground basis-1/3 px-4 py-2.5">
                          <label className="text-sm font-medium text-gray-500">
                            Event ID
                          </label>
                        </div>
                        <div className="basis-2/3 px-4 py-2.5 border-b">
                          <p className="text-sm font-mono">
                            {selectedMatch.opportunityId}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <div className="bg-tertiary-foreground basis-1/3 px-4 py-2.5">
                          <label className="text-sm font-medium text-gray-500">
                            Overall Score
                          </label>
                        </div>
                        <div className="basis-2/3 px-4 py-2.5 border-b">
                          <p
                            className="text-sm font-bold"
                            style={{
                              color:
                                selectedMatch.matchingScore >= 80
                                  ? "#52c41a"
                                  : selectedMatch.matchingScore >= 60
                                  ? "#faad14"
                                  : "#ff4d4f",
                            }}
                          >
                            {selectedMatch.matchingScore}%
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <div className="bg-tertiary-foreground basis-1/3 px-4 py-2.5 rounded-bl-lg">
                          <label className="text-sm font-medium text-gray-500">
                            Created At
                          </label>
                        </div>
                        <div className="basis-2/3 px-4 py-2.5 rounded-br-lg">
                          <p className="text-sm">
                            {dayjs(selectedMatch.createdAt).format(
                              "MMM DD, YYYY HH:mm:ss"
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Proposal Summary */}
                <Card className="bg-tertiary">
                  <CardHeader className="border-b py-2.5 px-3 rounded-lg">
                    <CardTitle className="text-sm font-semibold leading-[1.6]">
                      Proposal Summary
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4 p-3">
                    <p className="text-sm text-muted-foreground">
                      {selectedMatch.proposalSummary}
                    </p>
                  </CardContent>
                </Card>

                {/* Key Themes */}
                <Card className="bg-tertiary">
                  <CardHeader className="border-b py-2.5 px-3 rounded-lg">
                    <CardTitle className="text-sm font-semibold leading-[1.6]">
                      Key Themes
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4 p-3">
                    <div>
                      {selectedMatch.keyThemes?.map((theme: string) => (
                        <Badge
                          key={theme}
                          className="bg-blue-500/15 text-blue-600 border border-blue-600/30"
                        >
                          <span className="inline-flex items-center gap-1">
                            <TagsOutlined />
                            {theme}
                          </span>
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>

                {/* Match Strengths & Concerns */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="w-full">
                    <Card className="bg-tertiary">
                      <CardHeader className="border-b py-2.5 px-3 rounded-lg">
                        <CardTitle className="text-sm font-semibold leading-[1.6]">
                          Match Strengths
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4 p-3 text-sm">
                        <ul
                          style={{
                            color: "#52c41a",
                            paddingLeft: 16,
                            margin: 0,
                          }}
                        >
                          {selectedMatch.matchStrengths?.map(
                            (strength: string, idx: number) => (
                              <li key={idx}>{strength}</li>
                            )
                          )}
                        </ul>
                      </CardContent>
                    </Card>
                  </div>
                  <div className="w-full">
                    <Card className="bg-tertiary">
                      <CardHeader className="border-b py-2.5 px-3 rounded-lg">
                        <CardTitle className="text-sm font-semibold leading-[1.6]">
                          Match Concerns
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-4 p-3 text-sm">
                        <ul
                          style={{
                            color: "#ff4d4f",
                            paddingLeft: 16,
                            margin: 0,
                          }}
                        >
                          {selectedMatch.matchConcerns?.map(
                            (concern: string, idx: number) => (
                              <li key={idx}>{concern}</li>
                            )
                          )}
                        </ul>
                      </CardContent>
                    </Card>
                  </div>
                </div>
                {/* Status Management */}
                <Card className="bg-tertiary">
                  <CardHeader className="border-b py-2.5 px-3 rounded-lg">
                    <CardTitle className="text-sm font-semibold leading-[1.6]">
                      Match Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4 p-3">
                    <div className="flex items-center w-full gap-1">
                      <p className="font-semibold text-sm">Current Status:</p>
                      <span style={{ marginLeft: 8 }}>
                       {
                        selectedMatch.status && (
                          <Badge
                          className={getStatusBadgeClass(selectedMatch.status)}
                        >
                          <span className="inline-flex items-center gap-1">
                            {getStatusIcon(selectedMatch.status)}
                            {selectedMatch.status.charAt(0).toUpperCase() +
                              selectedMatch.status.slice(1)}
                          </span>
                        </Badge>
                        )
                       }
                      </span>
                    </div>
                  </CardContent>
                </Card>

                {/* Follow-Through Tracking */}
                {/* <Card className="bg-tertiary">
                  <CardHeader className="border-b py-2.5 px-3 rounded-lg">
                    <CardTitle className="text-sm font-semibold leading-[1.6]">Speaker Follow-Through</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4 p-3">
                    {permissions.canEdit && (
                      <div>
                        <p className="font-semibold">Update Status:</p>
                        <div className="flex gap-2 mt-2">
                          {[
                            { label: "Pending", value: "pending" },
                            { label: "Interested", value: "interested" },
                            { label: "Accepted", value: "accepted" },
                            { label: "Rejected", value: "rejected" },
                          ].map((option) => (
                            <Button
                              key={option.value}
                              variant={
                                selectedMatch.status === option.value
                                  ? "default"
                                  : "outline"
                              }
                              size="sm"
                              onClick={() =>
                                handleStatusSegmentChange(option.value)
                              }
                            >
                              {option.label}
                            </Button>
                          ))}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card> */}

                {/* Follow-Through Tracking */}
                <Card className="bg-tertiary">
                  <CardHeader className="border-b py-2.5 px-3 rounded-lg">
                    <CardTitle className="text-sm font-semibold leading-[1.6]">
                      Speaker Follow-Through
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4 p-3">
                    <div className="grid grid-cols-1 border rounded-lg">
                      <div className="flex items-center">
                        <div className="bg-tertiary-foreground basis-1/3 px-4 py-2.5 rounded-tl-lg">
                          <label className="text-sm font-medium text-gray-500">
                            Application Status
                          </label>
                        </div>
                        <div
                          className="basis-2/3 px-4 py-2.5 rounded-tr-lg border-b text-sm"
                          style={{
                            color: selectedMatch.hasApplied
                              ? "#722ed1"
                              : "hsl(var(--muted-foreground))",
                          }}
                        >
                          {selectedMatch.hasApplied ? (
                            <div className="flex items-center gap-2">
                              <CheckCircleOutlined />
                              Applied
                              {selectedMatch.applicationDate && (
                                <p className="text-muted-foreground">
                                  (
                                  {dayjs(selectedMatch.applicationDate).format(
                                    "MMM DD, YYYY"
                                  )}
                                  )
                                </p>
                              )}
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <CloseCircleOutlined />
                              Not Applied
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center">
                        <div className="bg-tertiary-foreground basis-1/3 px-4 py-2.5 rounded-bl-lg">
                          <label className="text-sm font-medium text-gray-500">
                            Booking Status
                          </label>
                        </div>
                        <div
                          className="basis-2/3 px-4 py-2.5 rounded-br-lg border-b text-sm"
                          style={{
                            color: selectedMatch.isBooked
                              ? "#13c2c2"
                              : "hsl(var(--muted-foreground))",
                          }}
                        >
                          {selectedMatch.isBooked ? (
                            <div className="flex items-center gap-2">
                              <CheckCircleOutlined />
                              Booked
                              {selectedMatch.bookingDate && (
                                <p className="text-muted-foreground">
                                  (
                                  {dayjs(selectedMatch.bookingDate).format(
                                    "MMM DD, YYYY"
                                  )}
                                  )
                                </p>
                              )}
                            </div>
                          ) : (
                            <div className="flex items-center gap-2">
                              <CloseCircleOutlined />
                              Not Booked
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}
          </SheetContent>
        </Sheet>

        {/* Reject Reason Dialog */}
        <Dialog
          open={isRejectModalVisible}
          onOpenChange={setIsRejectModalVisible}
        >
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Reject Match</DialogTitle>
            </DialogHeader>
            <Form {...rejectForm}>
              <form
                className="space-y-4"
                onSubmit={rejectForm.handleSubmit(handleRejectSubmit)}
              >
                <FormField
                  control={rejectForm.control}
                  name="reason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Rejection Reason</FormLabel>
                      <FormControl>
                        <Textarea
                          rows={4}
                          placeholder="Explain why this match is being rejected..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <div style={{ marginBottom: 0, textAlign: "right" }}>
                  <div>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => {
                        setIsRejectModalVisible(false);
                        rejectForm.reset({ reason: "" });
                      }}
                    >
                      Cancel
                    </Button>
                    <Button type="submit" variant="destructive">
                      Reject Match
                    </Button>
                  </div>
                </div>
              </form>
            </Form>
          </DialogContent>
        </Dialog>
      </div>
    </>
  );
};

export default Matching;
