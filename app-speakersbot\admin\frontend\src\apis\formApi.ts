import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../store';

// Define the base URL for your API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

export const formApi = createApi({
  reducerPath: 'formApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Get the token from the state
      headers.set("ngrok-skip-browser-warning", "true");
      const token = (getState() as RootState).auth?.token;
      
      // If we have a token, set the authorization header
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      
      // Set content type
      headers.set('content-type', 'application/json');
      
      return headers;
    },
  }),
  tagTypes: ['Form', 'FormField'],
  keepUnusedDataFor: 0,
  endpoints: (builder) => ({
    // Get forms with priorities
    getFormsWithPriorities: builder.query({
      query: () => '/form/priority',
      providesTags: ['Form'],
    }),
    
    // Create form
    createForm: builder.mutation({
      query: (formData) => ({
        url: '/form/create',
        method: 'POST',
        body: formData,
      }),
      invalidatesTags: ['Form'],
    }),
    
    // Update form priority
    updateFormPriority: builder.mutation({
      query: (priorityData) => ({
        url: '/form/update/priority',
        method: 'PUT',
        body: priorityData,
      }),
      invalidatesTags: ['Form'],
    }),
    
    // Update form (title only)
    updateForm: builder.mutation({
      query: ({ id, ...formData }) => ({
        url: `/form/update/${id}`,
        method: 'PUT',
        body: formData,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Form', id }],
    }),
    
    // Delete form (soft delete)
    deleteForm: builder.mutation({
      query: (id) => ({
        url: `/form/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['Form'],
    }),
    
    // Get all fields for a form
    getFormFields: builder.query({
      query: (formId) => `/form/${formId}`,
      providesTags: (result, error, formId) => [{ type: 'FormField', id: formId }],
    }),
    
    // Create new field
    createFormField: builder.mutation({
      query: ({ formId, ...fieldData }) => ({
        url: `/form/field/${formId}`,
        method: 'POST',
        body: fieldData,
      }),
      invalidatesTags: (result, error, { formId }) => [{ type: 'FormField', id: formId }],
    }),
    
    // Update field and options
    updateFormField: builder.mutation({
      query: ({ id, ...fieldData }) => ({
        url: `/form/field/${id}`,
        method: 'PUT',
        body: fieldData,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'FormField', id }],
    }),
    
    // Delete field (soft delete)
    deleteFormField: builder.mutation({
      query: (id) => ({
        url: `/form/field/${id}`,
        method: 'DELETE',
      }),
      invalidatesTags: ['FormField'],
    }),
  }),
});

export const {
  useGetFormsWithPrioritiesQuery,
  useCreateFormMutation,
  useUpdateFormPriorityMutation,
  useUpdateFormMutation,
  useDeleteFormMutation,
  useGetFormFieldsQuery,
  useCreateFormFieldMutation,
  useUpdateFormFieldMutation,
  useDeleteFormFieldMutation,
} = formApi;
