"""
Database Operations Module for Speaker Opportunity Scraper

This module provides comprehensive database operations for the scraper service,
including topic management, URL deduplication, data persistence, and status tracking.
It handles all interactions with the MySQL database using SQLAlchemy ORM.

Key Features:
- Topic management and retrieval
- URL deduplication to prevent duplicates
- Data persistence with error handling
- Status tracking and logging
- Database connection management
- Transaction handling

Author: Speaker Bot Team
Version: 2.0 (Optimized)
Last Updated: 2024
"""

from typing import List, Dict, Set
from sqlalchemy import update
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime

from app.helpers.database_manager import db_manager
from app.models.opportunities import SpeakerOpportunity, Base
from app.models.scraping import ScrapingLogging, ScrapingStatus
from app.models.subcategory import SubCategory
from app.models.opportunity_form import OpportunityForm

# =============================================================================
# URL DEDUPLICATION
# =============================================================================

def get_seen_urls_from_db() -> Set[str]:
    """
    Get existing URLs from database to avoid duplicates.
    
    This function retrieves all existing event URLs from the database
    to prevent duplicate entries during scraping. It filters out null
    and empty URLs for efficiency.
    
    Returns:
        Set[str]: Set of existing URLs
        
    Example:
        >>> seen_urls = get_seen_urls_from_db()
        >>> print(f"Found {len(seen_urls)} existing URLs")
    """
    with db_manager.get_session() as session:
        # Only fetch non-null, non-empty URLs to avoid duplicates
        urls = set(
            url for url, in session.query(SpeakerOpportunity.event_url)
            .filter(SpeakerOpportunity.event_url.isnot(None))
            .filter(SpeakerOpportunity.event_url != "")
            .all()
        )
        return urls


# =============================================================================
# DATA PERSISTENCE
# =============================================================================

def save_to_db(rows: List[Dict]):
    """
    Save scraped rows to the database using SQLAlchemy with comprehensive error handling.
    
    This function saves a list of scraped opportunity data to the database,
    handling both SpeakerOpportunity and OpportunityForm records. It includes
    transaction management and detailed logging.
    
    Args:
        rows (List[Dict]): List of opportunity data dictionaries to save
        
    Example:
        >>> opportunities = [{"title": "Tech Conf", "url": "https://example.com"}]
        >>> save_to_db(opportunities)
        >>> print("Saved to database successfully")
    """
    try:
        from app.config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    except Exception:
        from config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    
    with db_manager.get_session() as session:
        try:
            for row in rows:
                # Remove 'id' so DB can autogenerate primary key
                row = dict(row)
                row.pop("id", None)
                row.pop("status", None)
                
                # Extract form data and submission type before creating opportunity
                form_data = row.pop("form_data", None)
                contact_email = row.pop("contact_email", "")
                submission_type = row.pop("submission_type", None)
                
                # Create the opportunity
                opp = SpeakerOpportunity(**row)
                session.add(opp)
                session.flush()  # Flush to get the ID
                
                # Save form data or email to opportunity_form table
                if submission_type and (form_data or contact_email):
                    import json
                    
                    if submission_type == "form" and form_data:
                        # Extract form_url and fields separately
                        form_url = form_data.get('form_url', '') if isinstance(form_data, dict) else ''
                        fields_only = form_data.get('fields', []) if isinstance(form_data, dict) else []
                        
                        # Store only field names in form_data JSON
                        form_data_json = json.dumps(fields_only)
                        
                        opp_form = OpportunityForm(
                            opportunity_id=opp.id,
                            type="form",
                            form_data=form_data_json,
                            form_url=form_url
                        )
                        session.add(opp_form)
                        logger.info(f"Saved form data for opportunity ID: {opp.id} with form URL: {form_url}")
                    elif submission_type == "email" and contact_email:
                        email_data = {"email": contact_email}
                        email_data_json = json.dumps(email_data)
                        opp_form = OpportunityForm(
                            opportunity_id=opp.id,
                            type="email",
                            form_data=email_data_json
                        )
                        session.add(opp_form)
                        logger.info(f"Saved email data for opportunity ID: {opp.id}")
            
            session.commit()
            logger.info("Database save complete.")
            
        except Exception as e:
            session.rollback()
            logger.error(f"Error saving to database: {e}")
            raise


# =============================================================================
# LOGGING OPERATIONS
# =============================================================================

def log_scraping_result(topic: str, engine: str, urls_count: int, status: str, reason: str = None) -> None:
    """
    Log scraping results to the scraping_logging table.
    
    This function logs the results of scraping operations to the database,
    providing comprehensive tracking of discovery and scraping activities.
    It handles both success and error cases with proper database transaction management.
    
    Args:
        topic (str): Topic that was scraped
        engine (str): Search engine used for scraping
        urls_count (int): Number of URLs discovered
        status (str): Status of the scraping (success, error, running)
        reason (str, optional): Optional reason for the status
        
    Example:
        >>> log_scraping_result("AI Conference", "tavily", 15, "success")
        >>> log_scraping_result("Tech Event", "brave", 0, "error", "Connection timeout")
    """
    try:
        from app.config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    except Exception:
        from config.logger import get_logger
        logger = get_logger(__name__, file_name="scraper.log")
    
    with db_manager.get_session() as session:
        try:
            # Determine the appropriate status
            if status == "success":
                scraping_status = ScrapingStatus.SUCCESS
            elif status == "error":
                scraping_status = ScrapingStatus.ERROR
            else:
                scraping_status = ScrapingStatus.SUCCESS  # Default to success for other cases
            
            # Create log entry
            log_entry = ScrapingLogging(
                topic=f"{topic}",  # Include engine in topic for better tracking
                search_engine=engine,
                item_count=urls_count,
                status=scraping_status,
                reason=reason or (f"Found {urls_count} URLs" if urls_count > 0 else "No URLs found"),
                started_at=datetime.utcnow(),
                ended_at=datetime.utcnow(),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            session.add(log_entry)
            session.commit()
            logger.info(f"Logged scraping result: {topic} ({engine}) - {urls_count} URLs - {status}")
            
        except SQLAlchemyError as e:
            session.rollback()
            logger.error(f"Database error logging scraping result for {topic} ({engine}): {e}")
        except Exception as e:
            session.rollback()
            logger.error(f"Unexpected error logging scraping result for {topic} ({engine}): {e}")
            raise


