import React from "react";

import { Skeleton } from "@/components/ui/skeleton";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";

const UserSkeletion = () => {
  return (
    <div className="bg-tertiary border rounded-lg">
      <Table className="whitespace-nowrap">
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Role</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Created</TableHead>
            <TableHead className="w-[140px]">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {Array.from({ length: 10 }).map((_, index) => (
            <TableRow key={index}>
              <TableCell>
                <Skeleton className="h-[20px] w-[120px] rounded" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-[20px] w-[180px] rounded" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-[24px] w-[80px] rounded-full" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-[24px] w-[70px] rounded-full" />
              </TableCell>
              <TableCell>
                <Skeleton className="h-[20px] w-[100px] rounded" />
              </TableCell>
              <TableCell>
                <div className="flex items-center gap-1.5">
                  <Skeleton className="h-[32px] w-[32px] rounded" />
                  <Skeleton className="h-[32px] w-[32px] rounded" />
                  <Skeleton className="h-[32px] w-[32px] rounded" />
                  <Skeleton className="h-[32px] w-[32px] rounded" />
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default UserSkeletion;


