
const connection = require("../connection");
const { DataTypes } = require("sequelize");

const OpportunitiesUrl = connection.define("OpportunitiesUrl", {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: 'Primary key for the opportunities_url record',
    },
    url: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: 'URL of the opportunity/event'
    },
    topic: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Topic associated with the opportunity/event'
    },
    browser: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'Browser used to access the opportunity/event'
    },
    status: { 
        type: DataTypes.STRING,
        defaultValue: 'pending',
        comment: 'Status of the opportunity/event URL'
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: 'Record creation timestamp',
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: 'Record last update timestamp',
    }
},
    {
        tableName: "opportunities_url",
        timestamps: true,
        createdAt: 'created_at',
        updatedAt: 'updated_at',
        paranoid: false
    });

module.exports = OpportunitiesUrl;