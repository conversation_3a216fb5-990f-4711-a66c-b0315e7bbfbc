const { DataTypes } = require("sequelize");
const connection = require("../connection");


const FormQuestions = connection.define("FormQuestions", {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: 'Primary key for the form question',
    },
    question: {
        type: DataTypes.TEXT,
        allowNull: false,
        comment: 'Text of the question',
    },
    placeholder: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'Placeholder for the question (input field)',
    },
    form_type_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Foreign key referencing the form type',
    },
    field_type: {
        type: DataTypes.ENUM('Text', 'Textarea', 'Number', 'Select', 'Multi-Select', 'Multi-Text', 'Radio', 'File-upload', 'Date'),
        allowNull: false,
        comment: 'Type of the form field (select, text, radio, file-upload)',
    },
    field_id: {
        type: DataTypes.STRING,
        allowNull: false,
         unique: true,
        comment: 'Unique identifier for the form field',
        
    },
    is_required: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: false,
        comment: 'Indicates if the field is required',
    },
    options_json: {
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'JSON string of options for select/radio fields',
    },
    is_deletable: {
        type: DataTypes.ENUM('0', '1'), // 0: not deletable, 1: deletable
        allowNull: true,
        defaultValue: '1',
        comment: 'Indicates if the field is deletable',
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
        comment: 'Record creation timestamp',
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
        comment: 'Record last update timestamp',
    },
    deleted_at: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "Record deletion timestamp(soft delete)",
    }
}, {
    tableName: 'form_questions',
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    paranoid: true, // enables soft deletes
    deletedAt: "deleted_at",
});

module.exports = FormQuestions;