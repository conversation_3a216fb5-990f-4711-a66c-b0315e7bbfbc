const ApiResponse = require("../helpers/api-response");
const settingService = require("../services/setting-service");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");
/**
 * Save or update setting by key
 */
exports.saveSetting = async (req, res) => {
    try {
        const { key, value } = req.body;
        if (!key || !value) {
            return res.status(400).json({ error: "Key and value are required" });
        }

        const { status, message, data } = await settingService.saveSetting(key, value);
        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ data, message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (err) {
        res.status(500).json({ error: "Error saving setting", details: err.message });
    }
};

/**
 * Get setting by key
 */
exports.getSettingByKey = async (req, res) => {
    try {
        const { key } = req.query;
        
        if (!key) {
            return res.status(400).json({ error: "Key parameter is required" });
        }

        const { status, data, message } = await settingService.getSettingByKey(key);
        
        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ data, message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (err) {
        res.status(500).json({ error: "Error fetching setting", details: err.message });
    }
};

/**
 * Add/update(upsert) email template
 */
exports.upsertEmailTemplate = async (req, res, next) => {
    try {
        const { key, value } = req.body;
        
        if (!key || !value) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Key and value are required");
        }

        const status = await settingService.upsertEmailTemplate(key, value);

        return res.status(RESPONSE_CODES.SUCCESS)
        .json(ApiResponse(
                { message: `Email template ${status ? "Created" : "Updated"} successfully` }
            ));
    } catch (err) {
        next(err);
    }
}

/**
 * Get email template by key
 */
exports.getEmailTemplateByKey = async (req, res, next) => {
    try {
        const { key } = req.query;

        if (!key) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Key parameter is required");
        
        const data = await settingService.getEmailTemplateByKey(key);
        if(!data) throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email template not found");

        return res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ data, message: "Data fetched successfully" }));
    } catch (err) {
        next(err);
    }
}
