import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';
import type { RootState } from '../store';

// Define the base URL for your API
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

export const opportunitiesApi = createApi({
  reducerPath: 'opportunitiesApi',
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Get the token from the state
      const token = (getState() as RootState).auth?.token;
      
      // If we have a token, set the authorization header
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }

      // Default to JSON for most endpoints; specific endpoints override as needed
      if (!headers.has('Accept')) headers.set('Accept', 'application/json');
      return headers;
    },
  }),
  tagTypes: ['Opportunity'],
  keepUnusedDataFor: 0,
  endpoints: (builder) => ({
    // Fetch available event types for filtering
    getEventTypes: builder.query<{ data?: string[] } | string[] | any, void>({
      query: () => ({
        url: '/opportunity/event-types',
        headers: {
          Accept: 'application/json',
        },
      }),
    }),
    getAllCity: builder.query({
      query: () => ({
        url: '/opportunity/cities',
        headers: {
          Accept: 'application/json',
        },
      }),
    }),
    getOpportunities: builder.query({
      query: (params = {}) => ({
        url: '/opportunities',
        params,
        headers: {
          Accept: 'application/json',
        },
      }),
      providesTags: ['Opportunity'],
    }),
    
    getOpportunityById: builder.query({
      query: (id) => ({
        url: `/opportunity/${id}`,
        headers: {
          Accept: 'application/json',
        },
      }),
      providesTags: (result, error, id) => [{ type: 'Opportunity', id }],
    }),
    
    createOpportunity: builder.mutation({
      query: (opportunityData) => ({
        url: '/opportunities',
        method: 'POST',
        body: opportunityData,
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      }),
      invalidatesTags: ['Opportunity'],
    }),
    
    updateOpportunity: builder.mutation({
      query: ({ id, ...opportunityData }) => ({
        url: `/opportunity/${id}`,
        method: 'PUT',
        body: opportunityData,
        headers: {
          Accept: 'application/json',
          'Content-Type': 'application/json',
        },
      }),
      invalidatesTags: (result, error, { id }) => [{ type: 'Opportunity', id }],
    }),
    
    deleteOpportunity: builder.mutation({
      query: (id) => ({
        url: `/opportunity/${id}`,
        method: 'DELETE',
        headers: {
          Accept: 'application/json',
        },
      }),
      invalidatesTags: ['Opportunity'],
    }),

    // Export opportunities to CSV
    exportOpportunities: builder.mutation({
      query: ({body, params}) => ({
        url: '/export/opportunities',
        method: 'POST',
        params: params,
        body: body && body.ids && body.ids.length ? { ids: body.ids } : undefined,
        headers: {
          Accept: 'text/csv',
          'Content-Type': 'application/json',
        },
        // Force blob response for CSV download
        responseHandler: async (response) => await response.blob(),
      }),
    }),

    // Get all speakers for an opportunity (supports pagination via params)
    getAllSpeakersByOpportunityId: builder.query({
      query: ({ id, page, limit }: { id: string | number, page?: number, limit?: number }) => ({
        url: `/opportunity/speakers/${id}`,
        params: {
          ...(page ? { page } : {}),
          ...(limit ? { limit } : {}),
        },
        headers: {
          Accept: 'application/json',
        },
      }),
      providesTags: (result, error, arg) => [{ type: 'Opportunity', id: (arg as any)?.id }],
    }),

    // Import opportunities via CSV (multipart/form-data)
    importOpportunities: builder.mutation<any, File | FormData>({
      query: (fileOrFormData) => {
        const body = fileOrFormData instanceof FormData ? fileOrFormData : (() => {
          const fd = new FormData();
          fd.append('csvFile', fileOrFormData as File);
          return fd;
        })();
        return {
          url: '/import/opportunities',
          method: 'POST',
          body,
          // Do not set Content-Type so the browser can set the multipart boundary automatically
          headers: {
            Accept: 'application/json',
          },
        };
      },
      invalidatesTags: ['Opportunity'],
    }),

    // Download sample CSV template
    downloadSampleCsv: builder.mutation({
      query: () => ({
        url: '/opportunity/sample-csv',
        method: 'GET',
        headers: {
          Accept: 'text/csv',
        },
        // Force blob response for CSV download
        responseHandler: async (response) => await response.blob(),
      }),
    }),
  }),
});

export const {
  useGetOpportunitiesQuery,
  useGetEventTypesQuery,
  useGetAllCityQuery,
  useGetOpportunityByIdQuery,
  useCreateOpportunityMutation,
  useUpdateOpportunityMutation,
  useDeleteOpportunityMutation,
  useGetAllSpeakersByOpportunityIdQuery,
  useExportOpportunitiesMutation,
  useImportOpportunitiesMutation,
  useDownloadSampleCsvMutation,
} = opportunitiesApi;
