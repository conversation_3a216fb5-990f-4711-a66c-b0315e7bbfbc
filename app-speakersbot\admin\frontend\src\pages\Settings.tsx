import React, { useState, useEffect, useCallback } from "react";
import { Trophy, Gift, Save, CreditCard, Search, Eye, EyeOff } from "lucide-react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Switch } from "@/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  useGetPaymentSettingsQuery,
  useUpdatePaymentSettingsMutation,
  useGetGamificationRulesQuery,
  useUpdateGamificationRulesMutation,
  useGetGamificationHistoryQuery,
} from "../apis/settingsApi";
import { useToast } from "@/hooks/use-toast";
import { ConfigItem } from "@/types";
import { formatDateYMD } from "../utils/helper";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { InvalidTokenHandler } from "../components/common/InvalidTokenHandler";
// Static form schema for payment settings
const paymentFormSchema = z.object({
  gateway_key: z.string().optional(),
  api_key: z.string().min(1, "Please enter API key"),
  secret_key: z.string().min(1, "Please enter secret key"),
});

// Gamification rule type definition - completely dynamic
interface GamificationRule {
  id: number;
  key: string;
  points: number;
  limit_count: number | null;
  limit_period: string | null;
  is_active: boolean;
  type: string; // Dynamic - can be any string from API
  description?: string;
  created_at: string;
  updated_at: string;
}

// Dynamic gamification form schema - will be created based on API data
const createGamificationFormSchema = (rules: GamificationRule[]) => {
  const schemaFields: Record<string, z.ZodNumber> = {};
  rules.forEach((rule) => {
    schemaFields[rule.key] = z.number().min(0, "Points must be at least 0");
  });
  return z.object(schemaFields);
};

// Component to render individual gamification rule dynamically
const GamificationRuleCard: React.FC<{
  rule: GamificationRule;
  value: number;
  isActive: boolean;
  onValueChange: (value: number) => void;
  onActiveChange: (isActive: boolean) => void;
}> = ({ rule, value, isActive, onValueChange, onActiveChange }) => {
  return (
    <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
      <div className="flex-1">
        <div className="space-y-2">
          <div className="flex items-center gap-3">
            <h4 className="font-medium text-sm">
              {rule.description ||
                rule.key
                  .replace(/_/g, " ")
                  .replace(/\b\w/g, (l) => l.toUpperCase())}
            </h4>
          </div>
        </div>
        <div className="flex items-center mt-3 justify-between gap-3 w-full">
          <div className="relative w-full">
            <Input
              type="number"
              placeholder="Enter points"
              className="w-full pr-16"
              disabled={!isActive}
              value={value}
              onChange={(e) => onValueChange(Number(e.target.value))}
            />
            <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-sm text-muted-foreground">
              points
            </span>
          </div>
          <Switch checked={isActive} onCheckedChange={onActiveChange} />
        </div>
      </div>
    </div>
  );
};

// Component to render individual payment gateway form
const PaymentGatewayForm: React.FC<{
  gateway: ConfigItem;
  onSave: (gatewayKey: string, values: any) => void;
  isUpdating?: boolean;
}> = ({ gateway, onSave, isUpdating = false }) => {
  // State for password visibility
  const [showSecretKey, setShowSecretKey] = useState(false);
  
  // Initialize form with static schema
  const form = useForm({
    resolver: zodResolver(paymentFormSchema),
    defaultValues: {
      gateway_key: gateway.key || "",
      api_key: gateway.value?.api_key || "",
      secret_key: gateway.value?.secret_key || "",
    },
  });

  // Reset form when gateway data changes
  useEffect(() => {
    if (gateway.value) {
      form.reset({
        gateway_key: gateway.key || "",
        api_key: gateway.value.api_key || "",
        secret_key: gateway.value.secret_key || "",
      });
    }
  }, [gateway.value, form]);

  return (
    <div className="bg-card rounded-lg shadow-sm">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit((values: any) =>
            onSave(values.gateway_key, values)
          )}
          className="space-y-4 p-4"
        >
          <FormField
            control={form.control}
            name="gateway_key"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Payment Gateway</FormLabel>
                <FormControl>
                  <Input
                    disabled
                    readOnly
                    value={gateway.key}
                    {...field}
                    onChange={() => {}}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="api_key"
            render={({ field }) => (
              <FormItem>
                <FormLabel>API Key</FormLabel>
                <FormControl>
                  <Input type="text" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="secret_key"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Secret Key</FormLabel>
                <FormControl>
                  <div className="relative">
                    <Input
                      autoComplete="new-password" // disables Chrome's password manager
                      type={showSecretKey ? "text" : "password"}
                      {...field}
                      className="pr-10"
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2"
                      onClick={() => setShowSecretKey(!showSecretKey)}
                    >
                      {showSecretKey ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <Button
            type="submit"
            disabled={isUpdating}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            {isUpdating ? "Saving..." : "Save Payment Settings"}
          </Button>
        </form>
      </Form>
    </div>
  );
};

const Settings: React.FC = () => {
  const { toast } = useToast();

  // Tab state - must be declared before API queries that use it
  const [activeTab, setActiveTab] = useState("payment");

  const { data: settingsData, error: settingsError } =
    useGetPaymentSettingsQuery(
      { key: "stripe" },
      {
        skip: activeTab !== "payment", // Only fetch when payment tab is active
      }
    );

  const [updateSettings, { isLoading: isUpdating }] =
    useUpdatePaymentSettingsMutation();

  // Gamification settings state - consolidated
  const [gamificationState, setGamificationState] = useState({
    search: "",
    perPage: 10,
    currentPage: 1,
    debouncedSearch: "",
    isChanging: false,
    filter: "", // Filter for credit/redemption
  });

  // Debounced search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      if (gamificationState.search !== gamificationState.debouncedSearch) {
        setGamificationState((prev) => ({ ...prev, isChanging: true }));
      }
      setGamificationState((prev) => ({
        ...prev,
        debouncedSearch: gamificationState.search,
      }));
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [gamificationState.search, gamificationState.debouncedSearch]);

  // Gamification settings query - only fetch when gamification tab is active
  const {
    data: gamificationHistoryData,
    isLoading: gamificationLoading,
    isFetching: gamificationFetching,
  } = useGetGamificationHistoryQuery(
    {
      search: gamificationState.debouncedSearch,
      perPage: gamificationState.perPage,
      currentPage: gamificationState.currentPage,
      type: gamificationState.filter,
    },
    {
      skip: activeTab !== "gamification", // Only fetch when gamification tab is active
    }
  );

  const {
    data: gamificationRulesData,
    isLoading: gamificationRulesLoading,
    isFetching: gamificationRulesFetching,
  } = useGetGamificationRulesQuery(
    {},
    {
      skip: activeTab !== "gamification", // Only fetch when gamification tab is active
    }
  );

  const [
    updateGamificationRules,
    { isLoading: isUpdatingGamificationRules, error: gamificationRulesError },
  ] = useUpdateGamificationRulesMutation();

  // Filter combobox state
  const [isFilterComboboxOpen, setIsFilterComboboxOpen] = useState(false);
  const [filterComboboxValue, setFilterComboboxValue] = useState("");

  // Filter options
  const filterOptions = [
    { value: "", label: "All" },
    { value: "credit", label: "Credit" },
    { value: "redemption", label: "Redemption" },
  ];

  // State for dynamic payment gateway forms
  const [paymentGateway, setPaymentGateway] = useState<null | ConfigItem | {}>(
    null
  );

  // Initialize payment gateways when API data changes
  useEffect(() => {
    if (settingsData) {
      setPaymentGateway(settingsData.data || {});
    }
  }, [settingsData]);

  // Handle settings API error
  useEffect(() => {
    if (settingsError) {
      toast({
        title: "Failed to load settings. Please try again later.",
        variant: "destructive",
      });
    }
  }, [settingsError, toast]);

  // Get the actual data from API or fallback to empty structure
  const gamificationData = gamificationRulesData?.data || {};

  // Get all rules dynamically from API data
  const allRules = React.useMemo(() => {
    const dataSource = gamificationData;
    const allRulesArray = [];

    // Dynamically collect all rules from any category
    Object.keys(dataSource).forEach((category) => {
      if (Array.isArray(dataSource[category])) {
        allRulesArray.push(...dataSource[category]);
      }
    });

    return allRulesArray;
  }, [gamificationData]);

  // Create dynamic form schema based on rules
  const gamificationFormSchema = React.useMemo(() => {
    return createGamificationFormSchema(allRules);
  }, [allRules]);

  // Gamification form setup - now dynamic
  const gamificationForm = useForm({
    resolver: zodResolver(gamificationFormSchema),
    defaultValues: {},
  });

  // Initialize form values when rules data loads
  React.useEffect(() => {
    if (allRules.length > 0) {
      const formValues: Record<string, number> = {};
      const activeStates: Record<string, boolean> = {};

      allRules.forEach((rule) => {
        formValues[rule.key] = rule.points;
        activeStates[rule.key] = rule.is_active;
      });

      // Reset form with new values
      gamificationForm.reset(formValues);

      // Set active states
      setGamificationActive(activeStates);

      // Force re-render to ensure UI updates
      forceUpdate();
    }
  }, [allRules, gamificationForm]);

  // Gamification rules active status - now dynamic
  const [gamificationActive, setGamificationActive] = useState<
    Record<string, boolean>
  >({});

  // Force re-render when form values change
  const [, forceUpdate] = React.useReducer((x) => x + 1, 0);

  // Get form value with fallback
  const getFormValue = (key: string, fallback: number) => {
    const formValue = gamificationForm.watch(key);
    return formValue !== undefined ? formValue : fallback;
  };

  // Handlers for dynamic gamification rules
  const handleGamificationValueChange = (key: string, value: number) => {
    gamificationForm.setValue(key as any, value);
    // Trigger form validation
    gamificationForm.trigger(key as any);
  };

  const handleGamificationActiveChange = (key: string, isActive: boolean) => {
    setGamificationActive((prev) => ({
      ...prev,
      [key]: isActive,
    }));

    // Don't reset the value when disabling - keep the current form value
    // The input will just be disabled but retain its current value
  };

  // Handlers for gamification filters
  const handleGamificationSearch = useCallback(
    (search: string) => {
      // Only update if the search is actually different
      if (search !== gamificationState.search) {
        setGamificationState((prev) => ({
          ...prev,
          search,
          currentPage: 1, // Reset to first page on search
          isChanging: true,
        }));
      }
    },
    [gamificationState.search]
  );

  const handleGamificationPageChange = useCallback(
    (page: number) => {
      // Only update if the page is actually different
      if (page !== gamificationState.currentPage) {
        setGamificationState((prev) => ({
          ...prev,
          currentPage: page,
          isChanging: true,
        }));
      }
    },
    [gamificationState.currentPage]
  );

  const handleGamificationPerPageChange = useCallback(
    (perPage: number) => {
      // Only update if the perPage is actually different
      if (perPage !== gamificationState.perPage) {
        setGamificationState((prev) => ({
          ...prev,
          perPage,
          currentPage: 1, // Reset to first page on perPage change
          isChanging: true,
        }));
      }
    },
    [gamificationState.perPage]
  );

  const handleGamificationFilterChange = useCallback((filter: string) => {
    setGamificationState((prev) => ({
      ...prev,
      filter,
      currentPage: 1, // Reset to first page when changing filter
    }));
  }, []);

  const handleFilterSelect = useCallback(
    (value: string) => {
      setFilterComboboxValue(value);
      handleGamificationFilterChange(value);
      setIsFilterComboboxOpen(false);
    },
    [handleGamificationFilterChange]
  );

  // Reset pagination when search changes
  useEffect(() => {
    if (gamificationState.debouncedSearch !== gamificationState.search) {
      setGamificationState((prev) => ({ ...prev, currentPage: 1 }));
    }
  }, [gamificationState.debouncedSearch, gamificationState.search]);

  // Reset loading state when API call completes
  useEffect(() => {
    if (!gamificationLoading && !gamificationFetching) {
      // Add a small delay to ensure skeleton is visible
      const timer = setTimeout(() => {
        setGamificationState((prev) => ({ ...prev, isChanging: false }));
      }, 100);
      return () => clearTimeout(timer);
    }
  }, [gamificationLoading, gamificationFetching]);

  // Get gamification data from API
  const gamificationHistory =
    gamificationHistoryData?.data?.gamification_data || [];
  const total = gamificationHistoryData?.data?.pageData?.total || 0;
  const totalPages = gamificationHistoryData?.data?.pageData?.totalPage || 0;

  // Pagination items calculation (similar to Users.tsx)
  const pageItems = React.useMemo<(number | "ellipsis")[]>(() => {
    const totalP = totalPages;
    const currentP = gamificationState.currentPage;
    if (totalP <= 7) {
      return Array.from({ length: totalP }, (_, i) => i + 1);
    }
    const pages: number[] = [];
    pages.push(1);
    const start = Math.max(2, currentP - 1);
    const end = Math.min(totalP - 1, currentP + 1);
    for (let i = start; i <= end; i++) pages.push(i);
    pages.push(totalP);

    // Build with ellipses where gaps exist
    const output: (number | "ellipsis")[] = [];
    let last = 0;
    for (const p of pages) {
      if (last && p - last > 1) output.push("ellipsis");
      output.push(p);
      last = p;
    }
    return output;
  }, [gamificationState.currentPage, totalPages]);

  const handleSavePaymentSettings = async (gatewayKey: string, values: any) => {
    try {
      // Format payload as required by API
      const payload = {
        key: gatewayKey,
        value: {
          api_key: values.api_key,
          secret_key: values.secret_key,
        },
      };

      await updateSettings(payload).unwrap();

      toast({
        title: "Payment settings saved successfully",
      });
    } catch (error) {
      toast({
        title: "Failed to save payment settings",
        variant: "destructive",
      });
    }
  };

  const handleSaveGamificationRules = async (values: any) => {
    try {
      // Create array of all items with current state
      const allItems = allRules.map((rule) => {
        // Get current form value (this will have the updated value if changed)
        // Use nullish coalescing to properly handle 0 values
        const formValues = gamificationForm.getValues();
        const currentPoints =
          formValues[rule.key] !== undefined
            ? formValues[rule.key]
            : rule.points;
        const currentActive =
          gamificationActive[rule.key] !== undefined
            ? gamificationActive[rule.key]
            : rule.is_active;

        return {
          id: rule.id,
          points: currentPoints,
          is_active: currentActive,
        };
      });

      // Send the allItems array to the API
      await updateGamificationRules(allItems).unwrap();

      toast({
        title: "Gamification rules updated successfully!",
      });
    } catch (error: any) {
      console.error("Failed to update gamification rules:", error);
      toast({
        title: "Failed to update gamification rules",
        variant: "destructive",
      });
    }
  };

  return (
    <div>
      <InvalidTokenHandler error={gamificationRulesError || settingsError} />
      <h1 className="mb-4 text-2xl font-bold">Settings</h1>
      <div>
        <div className="bg-card rounded-t-xl p-0 flex gap-0 border border-border border-b-0 shadow-[0_2px_4px_rgba(0,0,0,0.1)]">
          <div
            onClick={() => setActiveTab("payment")}
            className={`
              px-7 py-[18px] cursor-pointer text-[15px] transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]
              rounded-tl-xl text-center flex-1 relative
              ${
                activeTab === "payment"
                  ? "text-primary bg-tertiary border-b-[3px] border-primary font-semibold"
                  : "text-muted-foreground bg-muted border-b-[3px] border-transparent font-normal"
              }
            `}
          >
            Payment Settings
          </div>
          <div
            onClick={() => setActiveTab("gamification")}
            className={`
              px-7 py-[18px] cursor-pointer text-[15px] transition-all duration-300 ease-[cubic-bezier(0.4,0,0.2,1)]
              rounded-tr-xl text-center flex-1 relative flex items-center justify-center gap-2
              ${
                activeTab === "gamification"
                  ? "text-primary bg-tertiary border-b-[3px] border-primary font-semibold"
                  : "text-muted-foreground bg-muted border-b-[3px] border-transparent font-normal"
              }
            `}
          >
            <Trophy className="h-4 w-4" />
            Gamification
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-card rounded-b-xl border border-border border-t-0 shadow-[0_4px_6px_rgba(0,0,0,0.1)]">
          {activeTab === "payment" && (
            <div className="p-6 bg-tertiary rounded-b-xl">
              <div className="bg-card border border-border rounded-lg shadow-sm">
                <div className="p-3 px-6 border-b border-border">
                  <h3 className="flex items-center gap-2 text-lg font-semibold">
                    <CreditCard className="h-4 w-4" /> Payment Settings
                  </h3>
                </div>
                <div className="space-y-6">
                  {/* Render dynamic payment gateway forms */}
                  {paymentGateway === null ? (
                    settingsError ? (
                      <p className="text-sm text-red-500">
                        Failed to load payment settings. Please try refreshing
                        the page.
                      </p>
                    ) : (
                      <div className="space-y-10 m-5">
                        <Skeleton className="h-7 w-full" />
                        <Skeleton className="h-7 w-full" />
                        <Skeleton className="h-7 w-full" />
                      </div>
                    )
                  ) : Object.keys(paymentGateway).length === 0 ? (
                    <p className="text-sm text-muted-foreground">
                      No payment gateways configured
                    </p>
                  ) : (
                    <PaymentGatewayForm
                      gateway={paymentGateway as ConfigItem}
                      onSave={handleSavePaymentSettings}
                      isUpdating={isUpdating}
                    />
                  )}
                </div>
              </div>
            </div>
          )}

          {activeTab === "gamification" && (
            <div className="p-6 bg-tertiary rounded-b-xl">
              <div className="bg-card border border-border rounded-lg shadow-sm mb-6">
                <div className="p-3 px-6 border-b border-border">
                  <h3 className="flex items-center gap-2 text-lg font-semibold">
                    <Trophy className="h-4 w-4" />
                    Gamification Rules
                  </h3>
                </div>
                <div className="p-6">
                  {gamificationRulesLoading ? (
                    <div className="space-y-4">
                      <Skeleton className="h-8 w-48" />
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {Array.from({ length: 4 }).map((_, i) => (
                          <Skeleton key={i} className="h-24 w-full" />
                        ))}
                      </div>
                    </div>
                  ) : gamificationData &&
                    Object.keys(gamificationData).length > 0 &&
                    allRules.length > 0 ? (
                    <Form {...gamificationForm}>
                      <form
                        onSubmit={gamificationForm.handleSubmit(
                          handleSaveGamificationRules
                        )}
                        className="space-y-8"
                      >
                        {/* Dynamic Rule Categories */}
                        {Object.keys(gamificationData).map((category) => {
                          const rules = gamificationData[category] || [];
                          if (rules.length === 0) return null;

                          return (
                            <div key={category} className="space-y-4">
                              <h3 className="text-lg font-semibold text-foreground border-b pb-2">
                                {category}
                              </h3>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                {rules.map((rule) => (
                                  <GamificationRuleCard
                                    key={rule.id}
                                    rule={rule}
                                    value={getFormValue(rule.key, rule.points)}
                                    isActive={
                                      gamificationActive[rule.key] !== undefined
                                        ? gamificationActive[rule.key]
                                        : rule.is_active
                                    }
                                    onValueChange={(value) =>
                                      handleGamificationValueChange(
                                        rule.key,
                                        value
                                      )
                                    }
                                    onActiveChange={(isActive) =>
                                      handleGamificationActiveChange(
                                        rule.key,
                                        isActive
                                      )
                                    }
                                  />
                                ))}
                              </div>
                            </div>
                          );
                        })}

                        <Button
                          type="submit"
                          disabled={isUpdatingGamificationRules}
                          className="flex items-center gap-2"
                        >
                          <Save className="h-4 w-4" />
                          {isUpdatingGamificationRules
                            ? "Saving..."
                            : "Save Gamification Rules"}
                        </Button>
                      </form>
                    </Form>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-muted-foreground">
                        No gamification rules found
                      </p>
                    </div>
                  )}
                </div>
              </div>

              {/* Gamification History Card */}
              <div className="bg-card border border-border rounded-lg shadow-sm">
                <div className="p-3 px-6 border-b border-border">
                  <div className="flex flex-wrap w-full items-center gap-4 justify-between">
                    <h3 className="flex items-center gap-2 text-lg font-semibold whitespace-nowrap">
                      <Gift className="h-4 w-4" />
                      Gamification History (Credit & Redemption)
                    </h3>
                  </div>
                  <div className="flex gap-4 mt-2">
                    <div className="relative flex-1">
                      {gamificationLoading || gamificationFetching ? (
                        <Skeleton className="h-10 w-full" />
                      ) : (
                        <>
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            type="search"
                            placeholder="Search gamification history..."
                            value={gamificationState.search}
                            onChange={(e) =>
                              handleGamificationSearch(e.target.value)
                            }
                            className="pl-10"
                            disabled={
                              gamificationLoading || gamificationFetching
                            }
                          />
                        </>
                      )}
                    </div>
                    <div className="w-[200px]">
                      {gamificationLoading || gamificationFetching ? (
                        <Skeleton className="h-10 w-full" />
                      ) : (
                        <Popover
                          open={isFilterComboboxOpen}
                          onOpenChange={setIsFilterComboboxOpen}
                        >
                          <PopoverTrigger asChild>
                            <Button
                              variant="outline"
                              role="combobox"
                              aria-expanded={isFilterComboboxOpen}
                              className="w-full justify-between"
                              disabled={
                                gamificationLoading || gamificationFetching
                              }
                            >
                              {filterComboboxValue
                                ? filterOptions.find(
                                    (option) =>
                                      option.value === filterComboboxValue
                                  )?.label
                                : "Filter by Type"}
                              <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                            </Button>
                          </PopoverTrigger>
                          <PopoverContent className="w-[200px] p-0">
                            <Command>
                              <CommandInput placeholder="Search type..." />
                              <CommandList>
                                <CommandEmpty>No type found.</CommandEmpty>
                                <CommandGroup>
                                  {filterOptions.map((option) => (
                                    <CommandItem
                                      key={option.value}
                                      value={option.value}
                                      onSelect={() =>
                                        handleFilterSelect(option.value)
                                      }
                                    >
                                      <Check
                                        className={cn(
                                          "mr-2 h-4 w-4",
                                          filterComboboxValue === option.value
                                            ? "opacity-100"
                                            : "opacity-0"
                                        )}
                                      />
                                      {option.label}
                                    </CommandItem>
                                  ))}
                                </CommandGroup>
                              </CommandList>
                            </Command>
                          </PopoverContent>
                        </Popover>
                      )}
                    </div>
                  </div>
                </div>
                <div className="p-6">
                  <div className="bg-card border rounded-xl">
                    <div className="flex items-center justify-between mb-4 p-4 border-b">
                      <span className="text-muted-foreground text-sm">
                        {gamificationLoading ? (
                          <Skeleton className="h-[20px] w-[200px] rounded-lg" />
                        ) : (
                          `Showing ${
                            total > 0
                              ? (gamificationState.currentPage - 1) *
                                  gamificationState.perPage +
                                1
                              : 0
                          }-${Math.min(
                            gamificationState.currentPage *
                              gamificationState.perPage,
                            total
                          )} of ${total || 0} records`
                        )}
                      </span>
                      <div className="flex items-center gap-2">
                        <span className="text-muted-foreground text-sm">
                          Rows per page
                        </span>
                        <Select
                          value={String(gamificationState.perPage)}
                          onValueChange={(value) =>
                            handleGamificationPerPageChange(Number(value))
                          }
                        >
                          <SelectTrigger className="w-[80px]">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="10">10</SelectItem>
                            <SelectItem value="25">25</SelectItem>
                            <SelectItem value="50">50</SelectItem>
                            <SelectItem value="100">100</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>
                    <Table className="whitespace-nowrap">
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[150px]">Speaker</TableHead>
                          <TableHead className="w-[120px]">Action</TableHead>
                          <TableHead className="w-[120px]">Type</TableHead>
                          <TableHead className="w-[100px]">Points</TableHead>
                          <TableHead className="w-[120px]">Date</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {gamificationLoading ||
                        gamificationFetching ||
                        gamificationState.isChanging ? (
                          // Loading skeleton
                          Array.from({ length: gamificationState.perPage }).map(
                            (_, index) => (
                              <TableRow key={index}>
                                <TableCell className="font-medium">
                                  <Skeleton className="h-4 w-32" />
                                </TableCell>
                                <TableCell>
                                  <Skeleton className="h-4 w-32" />
                                </TableCell>
                                <TableCell>
                                  <Skeleton className="h-6 w-20" />
                                </TableCell>
                                <TableCell>
                                  <Skeleton className="h-4 w-12" />
                                </TableCell>
                                <TableCell>
                                  <Skeleton className="h-4 w-32" />
                                </TableCell>
                              </TableRow>
                            )
                          )
                        ) : gamificationHistory?.length === 0 ? (
                          <TableRow>
                            <TableCell
                              colSpan={6}
                              className="text-center py-8 text-muted-foreground"
                            >
                              No gamification history found
                            </TableCell>
                          </TableRow>
                        ) : (
                          gamificationHistory?.map((record: any) => (
                            <TableRow key={record.key || record.id}>
                              <TableCell className="font-medium">
                                {record.speaker || "--"}
                              </TableCell>
                              <TableCell>
                                {record.description || "--"}
                              </TableCell>

                              <TableCell>
                                <div
                                  className={`rounded-md capitalize font-medium px-2 py-1 flex items-center justify-center w-max border
                                  ${
                                    record.type === "credit"
                                      ? "text-green-500 bg-green-500/10 border-green-500"
                                      : "text-red-500 bg-red-500/10 border-red-500"
                                  }`}
                                >
                                  {record.type || "--"}
                                </div>
                              </TableCell>
                              <TableCell>
                                <span
                                  className={`font-bold ${
                                    record.points > 0
                                      ? "text-green-500"
                                      : record.points < 0
                                      ? "text-red-500"
                                      : ""
                                  }`}
                                >
                                  {record.points || "--"}
                                </span>
                              </TableCell>
                              <TableCell>
                                {formatDateYMD(record.updated_at) || "--"}
                              </TableCell>
                            </TableRow>
                          ))
                        )}
                      </TableBody>
                    </Table>

                    {/* Only show pagination when there's data and not loading */}
                    {totalPages > 0 &&
                      gamificationHistoryData?.data?.pageData?.totalCount > 0 &&
                      !gamificationLoading &&
                      !gamificationFetching && (
                        <div className="flex items-center justify-center p-4">
                          <Pagination style={{ margin: 0, width: "auto" }}>
                            <PaginationContent>
                              <PaginationItem>
                                <PaginationPrevious
                                  href="#"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    if (
                                      gamificationState.currentPage > 1 &&
                                      !gamificationLoading &&
                                      !gamificationFetching
                                    )
                                      handleGamificationPageChange(
                                        gamificationState.currentPage - 1
                                      );
                                  }}
                                  className={
                                    gamificationState.currentPage === 1 ||
                                    gamificationLoading ||
                                    gamificationFetching
                                      ? "pointer-events-none opacity-50"
                                      : ""
                                  }
                                />
                              </PaginationItem>
                              {pageItems.map((item, idx) => (
                                <PaginationItem key={`${item}-${idx}`}>
                                  {item === "ellipsis" ? (
                                    <PaginationEllipsis />
                                  ) : (
                                    <PaginationLink
                                      href="#"
                                      isActive={
                                        item === gamificationState.currentPage
                                      }
                                      onClick={(e) => {
                                        e.preventDefault();
                                        if (
                                          !gamificationLoading &&
                                          !gamificationFetching
                                        ) {
                                          handleGamificationPageChange(
                                            item as number
                                          );
                                        }
                                      }}
                                      className={
                                        gamificationLoading ||
                                        gamificationFetching
                                          ? "pointer-events-none opacity-50"
                                          : ""
                                      }
                                    >
                                      {item}
                                    </PaginationLink>
                                  )}
                                </PaginationItem>
                              ))}
                              <PaginationItem>
                                <PaginationNext
                                  href="#"
                                  onClick={(e) => {
                                    e.preventDefault();
                                    if (
                                      gamificationState.currentPage <
                                        totalPages &&
                                      !gamificationLoading &&
                                      !gamificationFetching
                                    )
                                      handleGamificationPageChange(
                                        gamificationState.currentPage + 1
                                      );
                                  }}
                                  className={
                                    gamificationState.currentPage ===
                                      totalPages ||
                                    gamificationLoading ||
                                    gamificationFetching
                                      ? "pointer-events-none opacity-50"
                                      : ""
                                  }
                                />
                              </PaginationItem>
                            </PaginationContent>
                          </Pagination>
                        </div>
                      )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default Settings;
