{"scripts": {"start": "node index.js", "server": "nodemon index.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.887.0", "@faker-js/faker": "^10.0.0", "aws-sdk": "^2.1692.0", "axios": "^1.12.2", "bcrypt": "^6.0.0", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^17.2.1", "express": "^5.1.0", "fast-csv": "^4.3.6", "json2csv": "^5.0.7", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "mysql2": "^3.14.4", "nodemailer": "^7.0.6", "nodemodule": "^0.3.0", "nodemon": "^3.1.10", "sequelize": "^6.37.7", "stripe": "^18.5.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "yamljs": "^0.3.0"}, "keywords": [], "author": "", "license": "ISC", "description": ""}