const ApiResponse = require("../helpers/api-response");
const CustomError = require("../helpers/custome-error");
const matchingQueueService = require("../services/matching-queue-service");

const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const { exportAsCSV } = require("../helpers/csv-helper");

/**
 * Get matching queue with filters and pagination
 */
exports.getMatchingQueue = async (req, res, next) => {
    try {
        const { status, data, message, pageData } = await matchingQueueService.getMatchingQueue(req);

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data, pagination: pageData }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
}

/**
 * Get matching queue item by id
 */
exports.getMatchingQueueById = async (req, res, next) => {
    try {
        const { status, data, message } = await matchingQueueService.getMatchingQueueById(req);

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        console.error("Error in getMatchingQueueById:", error);
        next(error);
    }
}

/**
 * Export matching queue to csv
//  */
// exports.exportMatchingQueue = async (req, res, next) => {
//     try {
//         const result = await matchingQueueService.exportMatchingQueue(req);

//         const {data, fileName } = result;

//         return exportAsCSV(res, data, fileName || 'opportunities.csv');

//     } catch (error) {
//         console.error("Error in exportMatchingQueue:", error);
//         next(error);
//     }
// }
exports.exportMatchingQueue = async (req, res, next) => {
  try {
    
    const matchingQueue = await matchingQueueService.exportMatchingQueue(req);
    return exportAsCSV(res, matchingQueue, "matching-queue.csv");

  } catch (error) {
    console.error("Error in exportMatchingQueue:", error);
    next(error);
  }
};



