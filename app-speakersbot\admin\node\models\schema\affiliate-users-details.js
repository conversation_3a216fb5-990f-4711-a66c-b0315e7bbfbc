const { DataTypes } = require("sequelize");
const connection = require("../connection");


const AffiliateUsersDetails = connection.define("AffiliateUsersDetails", {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: 'Primary key for the affiliate user details record',
    },
    affiliate_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        
        comment: 'Foreign key referencing the user who is an affiliate',
    },
    legal_business_name: { 
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'Legal business name for the affiliate',
    },
    website: { 
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'Website URL for the affiliate',
    },
    commission: {
        type: DataTypes.DECIMAL(5, 2),
        allowNull: false,
        comment: 'Commission  assigned to the affiliate',
    },
    commission_type:{ 
        type: DataTypes.ENUM('percentage', 'fixed'),
        allowNull: false,
        defaultValue: 'percentage',
        comment: 'Type of commission (percentage or fixed amount)',
    },
    click_count: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'Click count for the affiliate',
    },
    signup_count: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'Signup count for the affiliate',
    },
    conversion_count: {
        type: DataTypes.INTEGER,
        allowNull: true,
        comment: 'Conversion count for the affiliate',
    },
    referral_code: {
        type: DataTypes.STRING,
        allowNull: true,
        comment: 'Unique referral code for the affiliate',
    },
    metadata:{
        type: DataTypes.JSON,
        allowNull: false,
        comment: 'Additional metadata related to the affiliate',
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
        comment: 'Record creation timestamp',
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
        comment: 'Record last update timestamp',
    },
    deleted_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Record deletion timestamp(soft delete)',
    }
}, {

    tableName: 'affiliate_users_details',
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    paranoid: true,
    deletedAt: "deleted_at",

});

module.exports = AffiliateUsersDetails;