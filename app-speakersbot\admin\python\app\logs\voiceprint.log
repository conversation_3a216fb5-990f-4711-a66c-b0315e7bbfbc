2025-11-03 16:14:52,677 - INFO - Processing text sample for speaker 1 from URL: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_1_opportunity_1_report.pdf
2025-11-03 16:14:52,719 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_1_opportunity_1_report.pdf
2025-11-03 16:14:54,721 - INFO - Text file downloaded: voiceprint\text\temp\1_20251103_161452_6a7b8a25.pdf (0.02 MB)
2025-11-03 16:14:54,721 - INFO - Starting text analysis for speaker 1
2025-11-03 16:14:55,887 - INFO - Extracted 16581 characters from PDF
2025-11-03 16:14:55,897 - INFO - Normalized PDF text: 16597 characters
2025-11-03 16:15:00,395 - INFO - OpenAI API call successful, response length: 300 characters
2025-11-03 16:15:00,399 - INFO - Storing text metrics for speaker 1
2025-11-03 16:15:00,464 - INFO - Text metrics stored successfully for speaker 1
2025-11-03 16:15:00,467 - INFO - Deleted temporary file: voiceprint\text\temp\1_20251103_161452_6a7b8a25.pdf (size: 16711 bytes)
2025-11-03 16:15:00,469 - INFO - Text processing completed successfully for speaker 1
2025-11-03 16:15:00,469 - INFO - Text processing completed for speaker 1
2025-11-03 16:20:29,301 - INFO - Saving uploaded text file for testing: speaker_intro_ethan_roy.txt
2025-11-03 16:20:29,306 - INFO - Text file saved for testing: voiceprint\text\temp\test_20251103_162029_0fe58fd9.txt (0.00 MB)
2025-11-03 16:20:29,308 - INFO - Starting direct text analysis for test file
2025-11-03 16:20:29,311 - INFO - Direct text analysis for file: voiceprint\text\temp\test_20251103_162029_0fe58fd9.txt
2025-11-03 16:20:29,326 - INFO - Extracted 3028 characters from TXT
2025-11-03 16:20:34,483 - INFO - OpenAI API call successful, response length: 300 characters
2025-11-03 16:20:34,492 - INFO - Direct text analysis completed successfully
2025-11-03 16:20:34,495 - INFO - Cleaned up test file: voiceprint\text\temp\test_20251103_162029_0fe58fd9.txt
2025-11-03 16:20:34,498 - INFO - Direct text analysis completed successfully
2025-11-03 16:52:29,457 - INFO - Saving uploaded text file for testing: speaker_intro_ethan_roy.txt
2025-11-03 16:52:29,458 - INFO - Text file saved for testing: voiceprint\text\temp\test_20251103_165229_4f310da4.txt (0.00 MB)
2025-11-03 16:52:29,463 - INFO - Starting direct text analysis for test file
2025-11-03 16:52:29,465 - INFO - Direct text analysis for file: voiceprint\text\temp\test_20251103_165229_4f310da4.txt
2025-11-03 16:52:29,473 - INFO - Extracted 3028 characters from TXT
2025-11-03 16:52:39,427 - INFO - OpenAI API call successful, response length: 773 characters
2025-11-03 16:52:39,428 - INFO - Direct text analysis completed successfully
2025-11-03 16:52:39,429 - INFO - Cleaned up test file: voiceprint\text\temp\test_20251103_165229_4f310da4.txt
2025-11-03 16:52:39,430 - INFO - Direct text analysis completed successfully
2025-11-03 17:03:53,086 - INFO - Saving uploaded text file for testing: speaker_intro_ethan_roy.txt
2025-11-03 17:03:53,087 - INFO - Text file saved for testing: voiceprint\text\temp\test_20251103_170353_77fae273.txt (0.00 MB)
2025-11-03 17:03:53,088 - INFO - Starting direct text analysis for test file
2025-11-03 17:03:53,089 - INFO - Direct text analysis for file: voiceprint\text\temp\test_20251103_170353_77fae273.txt
2025-11-03 17:03:53,089 - INFO - Extracted 3028 characters from TXT
2025-11-03 17:03:59,388 - INFO - OpenAI API call successful, response length: 763 characters
2025-11-03 17:03:59,388 - INFO - Direct text analysis completed successfully
2025-11-03 17:03:59,389 - INFO - Cleaned up test file: voiceprint\text\temp\test_20251103_170353_77fae273.txt
2025-11-03 17:03:59,389 - INFO - Direct text analysis completed successfully
2025-11-03 17:07:09,677 - INFO - Saving uploaded text file for testing: speaker_intro_ethan_roy.txt
2025-11-03 17:07:09,677 - INFO - Text file saved for testing: voiceprint\text\temp\test_20251103_170709_652f1377.txt (0.00 MB)
2025-11-03 17:07:09,681 - INFO - Starting direct text analysis for test file
2025-11-03 17:07:09,681 - INFO - Direct text analysis for file: voiceprint\text\temp\test_20251103_170709_652f1377.txt
2025-11-03 17:07:09,681 - INFO - Extracted 3028 characters from TXT
2025-11-03 17:07:16,481 - INFO - OpenAI API call successful, response length: 1214 characters
2025-11-03 17:07:16,482 - INFO - Direct text analysis completed successfully
2025-11-03 17:07:16,483 - INFO - Cleaned up test file: voiceprint\text\temp\test_20251103_170709_652f1377.txt
2025-11-03 17:07:16,483 - INFO - Direct text analysis completed successfully
2025-11-03 17:10:37,740 - INFO - Saving uploaded text file for testing: speaker_intro_ethan_roy.txt
2025-11-03 17:10:37,744 - INFO - Text file saved for testing: voiceprint\text\temp\test_20251103_171037_04005315.txt (0.00 MB)
2025-11-03 17:10:37,748 - INFO - Starting direct text analysis for test file
2025-11-03 17:10:37,751 - INFO - Direct text analysis for file: voiceprint\text\temp\test_20251103_171037_04005315.txt
2025-11-03 17:10:37,782 - INFO - Extracted 3028 characters from TXT
2025-11-03 17:10:41,764 - INFO - OpenAI API call successful, response length: 791 characters
2025-11-03 17:10:41,765 - INFO - Direct text analysis completed successfully
2025-11-03 17:10:41,766 - INFO - Cleaned up test file: voiceprint\text\temp\test_20251103_171037_04005315.txt
2025-11-03 17:10:41,766 - INFO - Direct text analysis completed successfully
2025-11-03 17:11:48,528 - INFO - Processing text sample for speaker 4 from URL: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_1_opportunity_1_report.pdf
2025-11-03 17:11:48,570 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_1_opportunity_1_report.pdf
2025-11-03 17:11:50,055 - INFO - Text file downloaded: voiceprint\text\temp\4_20251103_171148_b0d40aad.pdf (0.02 MB)
2025-11-03 17:11:50,056 - INFO - Starting text analysis for speaker 4
2025-11-03 17:11:50,476 - INFO - Extracted 16581 characters from PDF
2025-11-03 17:11:50,487 - INFO - Normalized PDF text: 16597 characters
2025-11-03 17:11:55,385 - INFO - OpenAI API call successful, response length: 807 characters
2025-11-03 17:11:55,385 - INFO - Storing text metrics for speaker 4
2025-11-03 17:11:55,401 - INFO - Text metrics stored successfully for speaker 4
2025-11-03 17:11:55,402 - INFO - Deleted temporary file: voiceprint\text\temp\4_20251103_171148_b0d40aad.pdf (size: 16711 bytes)
2025-11-03 17:11:55,403 - INFO - Text processing completed successfully for speaker 4
2025-11-03 17:11:55,403 - INFO - Text processing completed for speaker 4
2025-11-04 13:16:30,283 - INFO - Processing audio sample for speaker 78 from URL: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3-1762169470624
2025-11-04 13:16:33,209 - ERROR - Error processing audio sample for speaker 78: Unsupported audio file format: .mp3-1762169470624
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 71, in process_audio_sample
    raise ValueError(f"Unsupported audio file format: {file_ext}")
ValueError: Unsupported audio file format: .mp3-1762169470624
2025-11-04 13:16:33,211 - ERROR - Validation error for speaker 78: Unsupported audio file format: .mp3-1762169470624
2025-11-04 13:16:51,871 - INFO - Processing audio sample for speaker 78 from URL: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3
2025-11-04 13:16:53,006 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3
2025-11-04 13:16:54,924 - ERROR - Error processing audio sample for speaker 78: Failed to download file from S3: HTTP 403
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 81, in process_audio_sample
    self.s3_service.download_file(s3_url, str(temp_file_path))
  File "D:\speakerbot\app-speakersbot\admin\python\app\helpers\s3_service.py", line 86, in download_file
    raise RuntimeError(error_msg)
RuntimeError: Failed to download file from S3: HTTP 403
2025-11-04 13:16:54,963 - ERROR - Runtime error for speaker 78: Failed to download file from S3: HTTP 403
2025-11-04 13:47:52,648 - INFO - Processing audio sample for speaker 78 from URL: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3-1762169470624
2025-11-04 13:47:55,628 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3-1762169470624
2025-11-04 13:47:58,020 - INFO - Audio file downloaded: voiceprint\audio\temp\78_20251104_134755_49b03b9b.mp3 (0.13 MB)
2025-11-04 13:47:58,023 - INFO - Starting audio analysis for speaker 78
2025-11-04 13:47:58,024 - INFO - Starting analysis for: voiceprint\audio\temp\78_20251104_134755_49b03b9b.mp3
2025-11-04 13:49:04,393 - INFO - Storing audio metrics for speaker 78
2025-11-04 13:49:05,873 - INFO - Audio metrics stored successfully for speaker 78
2025-11-04 13:49:05,878 - INFO - Deleted temporary file: voiceprint\audio\temp\78_20251104_134755_49b03b9b.mp3 (size: 138825 bytes)
2025-11-04 13:49:05,890 - INFO - Audio processing completed successfully for speaker 78
2025-11-04 13:49:05,892 - INFO - Audio processing completed for speaker 78
2025-11-04 13:53:24,736 - INFO - Processing video sample for speaker 78 from URL: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting2.mp4-1762169428514
2025-11-04 13:53:25,831 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting2.mp4-1762169428514
2025-11-04 13:53:28,705 - INFO - Video file downloaded: voiceprint\video\temp\78_20251104_135325_b60fe305.mp4 (0.49 MB)
2025-11-04 13:53:28,705 - INFO - Starting video analysis for speaker 78
2025-11-04 13:53:28,705 - INFO - Starting video analysis for: voiceprint\video\temp\78_20251104_135325_b60fe305.mp4
2025-11-04 13:53:29,233 - INFO - Extracting audio from video (duration: 10.98s)
2025-11-04 13:53:29,234 - INFO - Writing extracted audio to: voiceprint\video\temp\78_20251104_135325_b60fe305_extracted_audio.wav
2025-11-04 13:53:29,438 - INFO - Audio extracted successfully: voiceprint\video\temp\78_20251104_135325_b60fe305_extracted_audio.wav (1936950 bytes)
2025-11-04 13:53:29,438 - INFO - Analyzing extracted audio for prosody metrics
2025-11-04 13:53:29,439 - INFO - Starting analysis for: voiceprint\video\temp\78_20251104_135325_b60fe305_extracted_audio.wav
2025-11-04 13:53:35,017 - INFO - Video analysis completed successfully
2025-11-04 13:53:35,058 - INFO - Storing video metrics for speaker 78
2025-11-04 13:53:36,432 - INFO - Video metrics stored successfully for speaker 78
2025-11-04 13:53:36,433 - INFO - Deleted temporary file: voiceprint\video\temp\78_20251104_135325_b60fe305.mp4 (size: 514381 bytes)
2025-11-04 13:53:36,433 - INFO - Deleted temporary file: voiceprint\video\temp\78_20251104_135325_b60fe305_extracted_audio.wav (size: 1936950 bytes)
2025-11-04 13:53:36,434 - INFO - Video processing completed successfully for speaker 78
2025-11-04 13:53:36,434 - INFO - Video processing completed for speaker 78
2025-11-04 14:02:03,788 - INFO - Processing audio sample for speaker 78 from URL: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting2.mp3-1762169510752
2025-11-04 14:02:04,929 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting2.mp3-1762169510752
2025-11-04 14:02:11,552 - INFO - Audio file downloaded: voiceprint\audio\temp\78_20251104_140204_e0bd5067.mp3 (1.83 MB)
2025-11-04 14:02:11,553 - INFO - Starting audio analysis for speaker 78
2025-11-04 14:02:11,573 - INFO - Starting analysis for: voiceprint\audio\temp\78_20251104_140204_e0bd5067.mp3
2025-11-04 14:02:38,873 - INFO - Storing audio metrics for speaker 78
2025-11-04 14:02:40,261 - INFO - Audio metrics stored successfully for speaker 78
2025-11-04 14:02:40,261 - INFO - Deleted temporary file: voiceprint\audio\temp\78_20251104_140204_e0bd5067.mp3 (size: 1920940 bytes)
2025-11-04 14:02:40,261 - INFO - Audio processing completed successfully for speaker 78
2025-11-04 14:02:40,273 - INFO - Audio processing completed for speaker 78
2025-11-06 12:59:10,720 - INFO - Processing combined voiceprint for speaker 3
2025-11-06 12:59:10,882 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3-1762169470624
2025-11-06 12:59:10,895 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_10_opportunity_43_report_20251105_120244.pdf
2025-11-06 12:59:11,071 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting2.mp4-1762169428514
2025-11-06 12:59:13,467 - INFO - Text file downloaded: voiceprint\text\temp\3_20251106_125910_62eb6c43.pdf (0.02 MB)
2025-11-06 12:59:13,507 - INFO - Starting text analysis for speaker 3
2025-11-06 12:59:14,171 - INFO - Audio file downloaded: voiceprint\audio\temp\3_20251106_125910_2666646a.mp3 (0.13 MB)
2025-11-06 12:59:14,171 - INFO - Starting audio analysis for speaker 3
2025-11-06 12:59:14,172 - INFO - Starting analysis for: voiceprint\audio\temp\3_20251106_125910_2666646a.mp3
2025-11-06 12:59:15,246 - INFO - Extracted 16818 characters from PDF
2025-11-06 12:59:15,258 - INFO - Normalized PDF text: 16810 characters
2025-11-06 12:59:16,355 - INFO - Video file downloaded: voiceprint\video\temp\3_20251106_125911_8df9a606.mp4 (0.49 MB)
2025-11-06 12:59:16,356 - INFO - Starting video analysis for speaker 3
2025-11-06 12:59:16,356 - INFO - Starting video analysis for: voiceprint\video\temp\3_20251106_125911_8df9a606.mp4
2025-11-06 12:59:17,343 - INFO - Extracting audio from video (duration: 10.98s)
2025-11-06 12:59:17,343 - INFO - Writing extracted audio to: voiceprint\video\temp\3_20251106_125911_8df9a606_extracted_audio.wav
2025-11-06 12:59:17,772 - INFO - Audio extracted successfully: voiceprint\video\temp\3_20251106_125911_8df9a606_extracted_audio.wav (1936950 bytes)
2025-11-06 12:59:17,772 - INFO - Analyzing extracted audio for prosody metrics
2025-11-06 12:59:17,772 - INFO - Starting analysis for: voiceprint\video\temp\3_20251106_125911_8df9a606_extracted_audio.wav
2025-11-06 12:59:20,465 - INFO - OpenAI API call successful, response length: 299 characters
2025-11-06 12:59:20,466 - INFO - Storing text metrics for speaker 3
2025-11-06 12:59:20,473 - INFO - Text metrics stored successfully for speaker 3
2025-11-06 12:59:20,482 - INFO - Deleted temporary file: voiceprint\text\temp\3_20251106_125910_62eb6c43.pdf (size: 17949 bytes)
2025-11-06 12:59:20,483 - INFO - Text processing completed successfully for speaker 3
2025-11-06 12:59:30,978 - INFO - Video analysis completed successfully
2025-11-06 12:59:30,993 - INFO - Storing video metrics for speaker 3
2025-11-06 12:59:31,006 - INFO - Video metrics stored successfully for speaker 3
2025-11-06 12:59:31,010 - INFO - Deleted temporary file: voiceprint\video\temp\3_20251106_125911_8df9a606.mp4 (size: 514381 bytes)
2025-11-06 12:59:31,012 - INFO - Deleted temporary file: voiceprint\video\temp\3_20251106_125911_8df9a606_extracted_audio.wav (size: 1936950 bytes)
2025-11-06 12:59:31,012 - INFO - Video processing completed successfully for speaker 3
2025-11-06 12:59:38,958 - INFO - Storing audio metrics for speaker 3
2025-11-06 12:59:38,973 - INFO - Audio metrics stored successfully for speaker 3
2025-11-06 12:59:38,973 - INFO - Deleted temporary file: voiceprint\audio\temp\3_20251106_125910_2666646a.mp3 (size: 138825 bytes)
2025-11-06 12:59:38,979 - INFO - Audio processing completed successfully for speaker 3
2025-11-06 12:59:38,979 - INFO - Combined voiceprint processing completed for speaker 3
2025-11-06 13:41:19,665 - INFO - Processing combined voiceprint for speaker 3
2025-11-06 13:41:19,717 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_10_opportunity_43_report_20251105_120244.pdf
2025-11-06 13:41:19,722 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3-1762169470624
2025-11-06 13:41:19,736 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting2.mp4-1762169428514
2025-11-06 13:41:21,450 - INFO - Text file downloaded: voiceprint\text\temp\3_20251106_134119_e9defbf7.pdf (0.02 MB)
2025-11-06 13:41:21,451 - INFO - Starting text analysis for speaker 3
2025-11-06 13:41:21,840 - INFO - Extracted 16818 characters from PDF
2025-11-06 13:41:21,848 - INFO - Normalized PDF text: 16810 characters
2025-11-06 13:41:22,065 - INFO - Audio file downloaded: voiceprint\audio\temp\3_20251106_134119_c3635d60.mp3 (0.13 MB)
2025-11-06 13:41:22,067 - INFO - Starting audio analysis for speaker 3
2025-11-06 13:41:22,067 - INFO - Starting analysis for: voiceprint\audio\temp\3_20251106_134119_c3635d60.mp3
2025-11-06 13:41:23,022 - INFO - Video file downloaded: voiceprint\video\temp\3_20251106_134119_fef1b1c1.mp4 (0.49 MB)
2025-11-06 13:41:23,030 - INFO - Starting video analysis for speaker 3
2025-11-06 13:41:23,031 - INFO - Starting video analysis for: voiceprint\video\temp\3_20251106_134119_fef1b1c1.mp4
2025-11-06 13:41:23,840 - INFO - Extracting audio from video (duration: 10.98s)
2025-11-06 13:41:23,841 - INFO - Writing extracted audio to: voiceprint\video\temp\3_20251106_134119_fef1b1c1_extracted_audio.wav
2025-11-06 13:41:24,421 - INFO - Audio extracted successfully: voiceprint\video\temp\3_20251106_134119_fef1b1c1_extracted_audio.wav (1936950 bytes)
2025-11-06 13:41:24,422 - INFO - Analyzing extracted audio for prosody metrics
2025-11-06 13:41:24,422 - INFO - Starting analysis for: voiceprint\video\temp\3_20251106_134119_fef1b1c1_extracted_audio.wav
2025-11-06 13:41:25,758 - INFO - OpenAI API call successful, response length: 300 characters
2025-11-06 13:41:25,760 - INFO - Storing text metrics for speaker 3
2025-11-06 13:41:25,792 - INFO - Text metrics stored successfully for speaker 3
2025-11-06 13:41:25,793 - INFO - Deleted temporary file: voiceprint\text\temp\3_20251106_134119_e9defbf7.pdf (size: 17949 bytes)
2025-11-06 13:41:25,796 - INFO - Text processing completed successfully for speaker 3
2025-11-06 13:41:31,697 - INFO - Video analysis completed successfully
2025-11-06 13:41:31,732 - INFO - Storing video metrics for speaker 3
2025-11-06 13:41:31,741 - INFO - Video metrics stored successfully for speaker 3
2025-11-06 13:41:31,742 - INFO - Deleted temporary file: voiceprint\video\temp\3_20251106_134119_fef1b1c1.mp4 (size: 514381 bytes)
2025-11-06 13:41:31,744 - INFO - Deleted temporary file: voiceprint\video\temp\3_20251106_134119_fef1b1c1_extracted_audio.wav (size: 1936950 bytes)
2025-11-06 13:41:31,745 - INFO - Video processing completed successfully for speaker 3
2025-11-06 13:41:40,795 - INFO - Storing audio metrics for speaker 3
2025-11-06 13:41:40,798 - INFO - Audio metrics stored successfully for speaker 3
2025-11-06 13:41:40,799 - INFO - Deleted temporary file: voiceprint\audio\temp\3_20251106_134119_c3635d60.mp3 (size: 138825 bytes)
2025-11-06 13:41:40,800 - INFO - Audio processing completed successfully for speaker 3
2025-11-06 13:41:40,801 - INFO - Combined voiceprint processing completed for speaker 3
2025-11-06 13:45:57,871 - INFO - Processing combined voiceprint for speaker 2
2025-11-06 13:45:57,877 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting2.mp4-1762169428514
2025-11-06 13:45:57,890 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3-1762169470624
2025-11-06 13:45:57,953 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_10_opportunity_43_report_20251105_120244.pdf
2025-11-06 13:45:59,942 - INFO - Text file downloaded: voiceprint\text\temp\2_20251106_134557_fe5eb407.pdf (0.02 MB)
2025-11-06 13:45:59,943 - INFO - Starting text analysis for speaker 2
2025-11-06 13:46:00,375 - INFO - Extracted 16818 characters from PDF
2025-11-06 13:46:00,380 - INFO - Normalized PDF text: 16810 characters
2025-11-06 13:46:00,571 - INFO - Audio file downloaded: voiceprint\audio\temp\2_20251106_134557_e3bcde4a.mp3 (0.13 MB)
2025-11-06 13:46:00,572 - INFO - Starting audio analysis for speaker 2
2025-11-06 13:46:00,572 - INFO - Starting analysis for: voiceprint\audio\temp\2_20251106_134557_e3bcde4a.mp3
2025-11-06 13:46:01,433 - INFO - Video file downloaded: voiceprint\video\temp\2_20251106_134557_15a115b8.mp4 (0.49 MB)
2025-11-06 13:46:01,437 - INFO - Starting video analysis for speaker 2
2025-11-06 13:46:01,441 - INFO - Starting video analysis for: voiceprint\video\temp\2_20251106_134557_15a115b8.mp4
2025-11-06 13:46:09,637 - INFO - OpenAI API call successful, response length: 299 characters
2025-11-06 13:46:09,639 - INFO - Storing text metrics for speaker 2
2025-11-06 13:46:09,693 - INFO - Text metrics stored successfully for speaker 2
2025-11-06 13:46:09,697 - INFO - Deleted temporary file: voiceprint\text\temp\2_20251106_134557_fe5eb407.pdf (size: 17949 bytes)
2025-11-06 13:46:09,700 - INFO - Text processing completed successfully for speaker 2
2025-11-06 13:46:09,712 - INFO - Storing audio metrics for speaker 2
2025-11-06 13:46:09,725 - INFO - Audio metrics stored successfully for speaker 2
2025-11-06 13:46:09,729 - INFO - Deleted temporary file: voiceprint\audio\temp\2_20251106_134557_e3bcde4a.mp3 (size: 138825 bytes)
2025-11-06 13:46:09,733 - INFO - Audio processing completed successfully for speaker 2
2025-11-06 13:46:09,876 - INFO - Extracting audio from video (duration: 10.98s)
2025-11-06 13:46:09,877 - INFO - Writing extracted audio to: voiceprint\video\temp\2_20251106_134557_15a115b8_extracted_audio.wav
2025-11-06 13:46:10,115 - INFO - Audio extracted successfully: voiceprint\video\temp\2_20251106_134557_15a115b8_extracted_audio.wav (1936950 bytes)
2025-11-06 13:46:10,116 - INFO - Analyzing extracted audio for prosody metrics
2025-11-06 13:46:10,116 - INFO - Starting analysis for: voiceprint\video\temp\2_20251106_134557_15a115b8_extracted_audio.wav
2025-11-06 13:46:15,769 - INFO - Video analysis completed successfully
2025-11-06 13:46:15,799 - INFO - Storing video metrics for speaker 2
2025-11-06 13:46:15,812 - INFO - Video metrics stored successfully for speaker 2
2025-11-06 13:46:15,814 - INFO - Deleted temporary file: voiceprint\video\temp\2_20251106_134557_15a115b8.mp4 (size: 514381 bytes)
2025-11-06 13:46:15,815 - INFO - Deleted temporary file: voiceprint\video\temp\2_20251106_134557_15a115b8_extracted_audio.wav (size: 1936950 bytes)
2025-11-06 13:46:15,816 - INFO - Video processing completed successfully for speaker 2
2025-11-06 13:46:15,817 - INFO - Combined voiceprint processing completed for speaker 2
2025-11-06 13:48:13,569 - INFO - Processing combined voiceprint for speaker 2
2025-11-06 13:48:13,613 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_10_opportunity_43_report_20251105_120244.pdf
2025-11-06 13:48:13,613 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3-1762169470624
2025-11-06 13:48:13,616 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting2.mp4-1762169428514
2025-11-06 13:48:16,010 - INFO - Text file downloaded: voiceprint\text\temp\2_20251106_134813_b8a5dcff.pdf (0.02 MB)
2025-11-06 13:48:16,011 - INFO - Starting text analysis for speaker 2
2025-11-06 13:48:16,377 - INFO - Extracted 16818 characters from PDF
2025-11-06 13:48:16,383 - INFO - Normalized PDF text: 16810 characters
2025-11-06 13:48:20,031 - INFO - Audio file downloaded: voiceprint\audio\temp\2_20251106_134813_aebf2e9e.mp3 (0.13 MB)
2025-11-06 13:48:20,031 - INFO - Starting audio analysis for speaker 2
2025-11-06 13:48:20,032 - INFO - Starting analysis for: voiceprint\audio\temp\2_20251106_134813_aebf2e9e.mp3
2025-11-06 13:48:20,339 - INFO - OpenAI API call successful, response length: 299 characters
2025-11-06 13:48:20,347 - INFO - Storing text metrics for speaker 2
2025-11-06 13:48:20,369 - INFO - Text metrics stored successfully for speaker 2
2025-11-06 13:48:20,371 - INFO - Deleted temporary file: voiceprint\text\temp\2_20251106_134813_b8a5dcff.pdf (size: 17949 bytes)
2025-11-06 13:48:20,372 - INFO - Text processing completed successfully for speaker 2
2025-11-06 13:48:22,273 - INFO - Video file downloaded: voiceprint\video\temp\2_20251106_134813_29335590.mp4 (0.49 MB)
2025-11-06 13:48:22,278 - INFO - Starting video analysis for speaker 2
2025-11-06 13:48:22,282 - INFO - Starting video analysis for: voiceprint\video\temp\2_20251106_134813_29335590.mp4
2025-11-06 13:48:22,694 - INFO - Extracting audio from video (duration: 10.98s)
2025-11-06 13:48:22,695 - INFO - Writing extracted audio to: voiceprint\video\temp\2_20251106_134813_29335590_extracted_audio.wav
2025-11-06 13:48:22,943 - INFO - Audio extracted successfully: voiceprint\video\temp\2_20251106_134813_29335590_extracted_audio.wav (1936950 bytes)
2025-11-06 13:48:22,943 - INFO - Analyzing extracted audio for prosody metrics
2025-11-06 13:48:22,944 - INFO - Starting analysis for: voiceprint\video\temp\2_20251106_134813_29335590_extracted_audio.wav
2025-11-06 13:48:29,401 - INFO - Video analysis completed successfully
2025-11-06 13:48:29,417 - INFO - Storing video metrics for speaker 2
2025-11-06 13:48:29,426 - INFO - Video metrics stored successfully for speaker 2
2025-11-06 13:48:29,428 - INFO - Deleted temporary file: voiceprint\video\temp\2_20251106_134813_29335590.mp4 (size: 514381 bytes)
2025-11-06 13:48:29,430 - INFO - Deleted temporary file: voiceprint\video\temp\2_20251106_134813_29335590_extracted_audio.wav (size: 1936950 bytes)
2025-11-06 13:48:29,431 - INFO - Video processing completed successfully for speaker 2
2025-11-06 13:48:37,825 - INFO - Storing audio metrics for speaker 2
2025-11-06 13:48:37,828 - INFO - Audio metrics stored successfully for speaker 2
2025-11-06 13:48:37,829 - INFO - Deleted temporary file: voiceprint\audio\temp\2_20251106_134813_aebf2e9e.mp3 (size: 138825 bytes)
2025-11-06 13:48:37,830 - INFO - Audio processing completed successfully for speaker 2
2025-11-06 13:48:37,830 - INFO - Generating tone profile for speaker 2
2025-11-06 13:48:37,831 - INFO - Generating tone profile for speaker 2 using LLM
2025-11-06 13:48:44,710 - INFO - LLM response received for speaker 2, length: 359 characters
2025-11-06 13:48:44,710 - INFO - Tone profile generated successfully for speaker 2
2025-11-06 13:48:44,711 - INFO - Tone profile generated successfully for speaker 2
2025-11-06 13:48:44,712 - INFO - Combined voiceprint processing completed for speaker 2
2025-11-06 14:39:25,442 - INFO - Processing combined voiceprint for speaker 2
2025-11-06 14:39:25,509 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_10_opportunity_43_report_20251105_120244.pdf
2025-11-06 14:39:25,520 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3-1762169470624
2025-11-06 14:39:25,520 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting2.mp4-1762169428514
2025-11-06 14:39:28,066 - INFO - Text file downloaded: voiceprint\text\temp\2_20251106_143925_785dfe61.pdf (0.02 MB)
2025-11-06 14:39:28,066 - INFO - Starting text analysis for speaker 2
2025-11-06 14:39:28,696 - INFO - Audio file downloaded: voiceprint\audio\temp\2_20251106_143925_63ff8340.mp3 (0.13 MB)
2025-11-06 14:39:29,084 - INFO - Starting audio analysis for speaker 2
2025-11-06 14:39:29,722 - INFO - Starting analysis for: voiceprint\audio\temp\2_20251106_143925_63ff8340.mp3
2025-11-06 14:39:29,745 - INFO - Extracted 16818 characters from PDF
2025-11-06 14:39:29,944 - INFO - Normalized PDF text: 16810 characters
2025-11-06 14:39:29,954 - INFO - Video file downloaded: voiceprint\video\temp\2_20251106_143925_adbf85ee.mp4 (0.49 MB)
2025-11-06 14:39:30,044 - INFO - Starting video analysis for speaker 2
2025-11-06 14:39:30,113 - INFO - Starting video analysis for: voiceprint\video\temp\2_20251106_143925_adbf85ee.mp4
2025-11-06 14:39:31,600 - INFO - Extracting audio from video (duration: 10.98s)
2025-11-06 14:39:31,629 - INFO - Writing extracted audio to: voiceprint\video\temp\2_20251106_143925_adbf85ee_extracted_audio.wav
2025-11-06 14:39:32,576 - INFO - Audio extracted successfully: voiceprint\video\temp\2_20251106_143925_adbf85ee_extracted_audio.wav (1936950 bytes)
2025-11-06 14:39:32,578 - INFO - Analyzing extracted audio for prosody metrics
2025-11-06 14:39:32,580 - INFO - Starting analysis for: voiceprint\video\temp\2_20251106_143925_adbf85ee_extracted_audio.wav
2025-11-06 14:39:38,243 - INFO - OpenAI API call successful, response length: 300 characters
2025-11-06 14:39:38,244 - INFO - Storing text metrics for speaker 2
2025-11-06 14:39:38,262 - INFO - Text metrics stored successfully for speaker 2
2025-11-06 14:39:38,266 - INFO - Deleted temporary file: voiceprint\text\temp\2_20251106_143925_785dfe61.pdf (size: 17949 bytes)
2025-11-06 14:39:38,266 - INFO - Text processing completed successfully for speaker 2
2025-11-06 14:40:00,909 - INFO - Video analysis completed successfully
2025-11-06 14:40:00,924 - INFO - Storing video metrics for speaker 2
2025-11-06 14:40:00,933 - INFO - Video metrics stored successfully for speaker 2
2025-11-06 14:40:00,937 - INFO - Deleted temporary file: voiceprint\video\temp\2_20251106_143925_adbf85ee.mp4 (size: 514381 bytes)
2025-11-06 14:40:00,940 - INFO - Deleted temporary file: voiceprint\video\temp\2_20251106_143925_adbf85ee_extracted_audio.wav (size: 1936950 bytes)
2025-11-06 14:40:00,945 - INFO - Video processing completed successfully for speaker 2
2025-11-06 14:40:09,920 - INFO - Storing audio metrics for speaker 2
2025-11-06 14:40:09,923 - INFO - Audio metrics stored successfully for speaker 2
2025-11-06 14:40:09,924 - INFO - Deleted temporary file: voiceprint\audio\temp\2_20251106_143925_63ff8340.mp3 (size: 138825 bytes)
2025-11-06 14:40:09,924 - INFO - Audio processing completed successfully for speaker 2
2025-11-06 14:40:09,926 - INFO - Generating tone profile for speaker 2
2025-11-06 14:40:09,933 - INFO - Generating tone profile for speaker 2 using LLM
2025-11-06 14:40:14,214 - INFO - LLM response received for speaker 2, length: 359 characters
2025-11-06 14:40:14,214 - INFO - Tone profile generated successfully for speaker 2
2025-11-06 14:40:14,219 - INFO - Tone profile generated successfully for speaker 2
2025-11-06 14:40:14,222 - INFO - Combined voiceprint processing completed for speaker 2
2025-11-06 15:00:19,356 - INFO - Processing combined voiceprint for speaker 1
2025-11-06 15:00:19,900 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_10_opportunity_43_report_20251105_120244.pdf
2025-11-06 15:00:19,937 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting2.mp4-1762169428514
2025-11-06 15:00:19,941 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3-1762169470624
2025-11-06 15:00:25,770 - INFO - Text file downloaded: voiceprint\text\temp\1_20251106_150019_a67b0153.pdf (0.02 MB)
2025-11-06 15:00:25,771 - INFO - Starting text analysis for speaker 1
2025-11-06 15:00:26,207 - INFO - Extracted 16818 characters from PDF
2025-11-06 15:00:26,217 - INFO - Normalized PDF text: 16810 characters
2025-11-06 15:00:26,820 - INFO - Audio file downloaded: voiceprint\audio\temp\1_20251106_150019_dd632cc9.mp3 (0.13 MB)
2025-11-06 15:00:26,830 - INFO - Starting audio analysis for speaker 1
2025-11-06 15:00:26,830 - INFO - Starting analysis for: voiceprint\audio\temp\1_20251106_150019_dd632cc9.mp3
2025-11-06 15:00:27,533 - INFO - Video file downloaded: voiceprint\video\temp\1_20251106_150019_bd9ee371.mp4 (0.49 MB)
2025-11-06 15:00:27,533 - INFO - Starting video analysis for speaker 1
2025-11-06 15:00:27,534 - INFO - Starting video analysis for: voiceprint\video\temp\1_20251106_150019_bd9ee371.mp4
2025-11-06 15:00:28,259 - INFO - Extracting audio from video (duration: 10.98s)
2025-11-06 15:00:28,261 - INFO - Writing extracted audio to: voiceprint\video\temp\1_20251106_150019_bd9ee371_extracted_audio.wav
2025-11-06 15:00:28,589 - INFO - Audio extracted successfully: voiceprint\video\temp\1_20251106_150019_bd9ee371_extracted_audio.wav (1936950 bytes)
2025-11-06 15:00:28,591 - INFO - Analyzing extracted audio for prosody metrics
2025-11-06 15:00:28,591 - INFO - Starting analysis for: voiceprint\video\temp\1_20251106_150019_bd9ee371_extracted_audio.wav
2025-11-06 15:00:29,885 - INFO - OpenAI API call successful, response length: 299 characters
2025-11-06 15:00:29,886 - INFO - Storing text metrics for speaker 1
2025-11-06 15:00:29,903 - INFO - Text metrics stored successfully for speaker 1
2025-11-06 15:00:29,904 - INFO - Deleted temporary file: voiceprint\text\temp\1_20251106_150019_a67b0153.pdf (size: 17949 bytes)
2025-11-06 15:00:29,905 - INFO - Text processing completed successfully for speaker 1
2025-11-06 15:00:35,630 - INFO - Video analysis completed successfully
2025-11-06 15:00:35,663 - INFO - Storing video metrics for speaker 1
2025-11-06 15:00:35,679 - INFO - Video metrics stored successfully for speaker 1
2025-11-06 15:00:35,681 - INFO - Deleted temporary file: voiceprint\video\temp\1_20251106_150019_bd9ee371.mp4 (size: 514381 bytes)
2025-11-06 15:00:35,684 - INFO - Deleted temporary file: voiceprint\video\temp\1_20251106_150019_bd9ee371_extracted_audio.wav (size: 1936950 bytes)
2025-11-06 15:00:35,685 - INFO - Video processing completed successfully for speaker 1
2025-11-06 15:00:43,736 - INFO - Storing audio metrics for speaker 1
2025-11-06 15:00:43,747 - INFO - Audio metrics stored successfully for speaker 1
2025-11-06 15:00:43,747 - INFO - Deleted temporary file: voiceprint\audio\temp\1_20251106_150019_dd632cc9.mp3 (size: 138825 bytes)
2025-11-06 15:00:43,748 - INFO - Audio processing completed successfully for speaker 1
2025-11-06 15:00:43,749 - INFO - Generating tone profile for speaker 1
2025-11-06 15:00:43,758 - INFO - Generating tone profile for speaker 1 using LLM
2025-11-06 15:00:46,865 - INFO - LLM response received for speaker 1, length: 359 characters
2025-11-06 15:00:46,865 - INFO - Tone profile generated successfully for speaker 1
2025-11-06 15:00:46,876 - INFO - Voiceprint tone profile stored successfully for speaker 1
2025-11-06 15:00:46,877 - INFO - Tone profile generated and stored successfully for speaker 1
2025-11-06 15:00:46,878 - INFO - Combined voiceprint processing completed for speaker 1
2025-11-06 15:43:06,129 - INFO - Processing combined voiceprint for speaker 8
2025-11-06 15:43:06,220 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_10_opportunity_43_report_20251105_120244.pdf
2025-11-06 15:43:06,225 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3-1762169470624
2025-11-06 15:43:06,226 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting2.mp4-1762169428514
2025-11-06 15:43:08,684 - INFO - Text file downloaded: voiceprint\text\temp\8_20251106_154306_00de31d7.pdf (0.02 MB)
2025-11-06 15:43:08,684 - INFO - Starting text analysis for speaker 8
2025-11-06 15:43:09,327 - INFO - Extracted 16818 characters from PDF
2025-11-06 15:43:09,339 - INFO - Normalized PDF text: 16810 characters
2025-11-06 15:43:09,366 - INFO - Audio file downloaded: voiceprint\audio\temp\8_20251106_154306_96d1c31b.mp3 (0.13 MB)
2025-11-06 15:43:09,366 - INFO - Starting audio analysis for speaker 8
2025-11-06 15:43:09,366 - INFO - Starting analysis for: voiceprint\audio\temp\8_20251106_154306_96d1c31b.mp3
2025-11-06 15:43:09,990 - INFO - Video file downloaded: voiceprint\video\temp\8_20251106_154306_228a64d3.mp4 (0.49 MB)
2025-11-06 15:43:09,993 - INFO - Starting video analysis for speaker 8
2025-11-06 15:43:09,994 - INFO - Starting video analysis for: voiceprint\video\temp\8_20251106_154306_228a64d3.mp4
2025-11-06 15:43:11,352 - INFO - Extracting audio from video (duration: 10.98s)
2025-11-06 15:43:11,354 - INFO - Writing extracted audio to: voiceprint\video\temp\8_20251106_154306_228a64d3_extracted_audio.wav
2025-11-06 15:43:11,716 - INFO - Audio extracted successfully: voiceprint\video\temp\8_20251106_154306_228a64d3_extracted_audio.wav (1936950 bytes)
2025-11-06 15:43:11,717 - INFO - Analyzing extracted audio for prosody metrics
2025-11-06 15:43:11,717 - INFO - Starting analysis for: voiceprint\video\temp\8_20251106_154306_228a64d3_extracted_audio.wav
2025-11-06 15:43:15,194 - INFO - OpenAI API call successful, response length: 480 characters
2025-11-06 15:43:15,194 - INFO - Storing text metrics for speaker 8
2025-11-06 15:43:15,215 - INFO - Text metrics stored successfully for speaker 8
2025-11-06 15:43:15,218 - INFO - Deleted temporary file: voiceprint\text\temp\8_20251106_154306_00de31d7.pdf (size: 17949 bytes)
2025-11-06 15:43:15,220 - INFO - Text processing completed successfully for speaker 8
2025-11-06 15:43:24,175 - INFO - Video analysis completed successfully
2025-11-06 15:43:24,191 - INFO - Storing video metrics for speaker 8
2025-11-06 15:43:24,202 - INFO - Video metrics stored successfully for speaker 8
2025-11-06 15:43:24,205 - INFO - Deleted temporary file: voiceprint\video\temp\8_20251106_154306_228a64d3.mp4 (size: 514381 bytes)
2025-11-06 15:43:24,205 - INFO - Deleted temporary file: voiceprint\video\temp\8_20251106_154306_228a64d3_extracted_audio.wav (size: 1936950 bytes)
2025-11-06 15:43:24,205 - INFO - Video processing completed successfully for speaker 8
2025-11-06 15:43:39,486 - INFO - Storing audio metrics for speaker 8
2025-11-06 15:43:39,493 - INFO - Audio metrics stored successfully for speaker 8
2025-11-06 15:43:39,496 - INFO - Deleted temporary file: voiceprint\audio\temp\8_20251106_154306_96d1c31b.mp3 (size: 138825 bytes)
2025-11-06 15:43:39,497 - INFO - Audio processing completed successfully for speaker 8
2025-11-06 15:43:39,498 - INFO - Generating tone profile for speaker 8
2025-11-06 15:43:39,523 - INFO - Generating tone profile for speaker 8 using LLM
2025-11-06 15:43:43,293 - INFO - LLM response received for speaker 8, length: 359 characters
2025-11-06 15:43:43,294 - INFO - Tone profile generated successfully for speaker 8
2025-11-06 15:43:43,319 - INFO - Voiceprint tone profile stored successfully for speaker 8
2025-11-06 15:43:43,324 - INFO - Tone profile generated and stored successfully for speaker 8
2025-11-06 15:43:43,324 - INFO - Combined voiceprint processing completed for speaker 8
2025-11-06 15:46:42,716 - INFO - Processing combined voiceprint for speaker 8
2025-11-06 15:46:42,792 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_10_opportunity_43_report_20251105_120244.pdf
2025-11-06 15:46:42,799 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3-1762169470624
2025-11-06 15:46:42,875 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting2.mp4-1762169428514
2025-11-06 15:46:46,359 - INFO - Text file downloaded: voiceprint\text\temp\8_20251106_154642_9fef3c3b.pdf (0.02 MB)
2025-11-06 15:46:46,359 - INFO - Starting text analysis for speaker 8
2025-11-06 15:46:47,121 - INFO - Audio file downloaded: voiceprint\audio\temp\8_20251106_154642_561d7982.mp3 (0.13 MB)
2025-11-06 15:46:47,124 - INFO - Extracted 16818 characters from PDF
2025-11-06 15:46:47,124 - INFO - Starting audio analysis for speaker 8
2025-11-06 15:46:47,135 - INFO - Normalized PDF text: 16810 characters
2025-11-06 15:46:47,135 - INFO - Starting analysis for: voiceprint\audio\temp\8_20251106_154642_561d7982.mp3
2025-11-06 15:46:47,624 - INFO - Video file downloaded: voiceprint\video\temp\8_20251106_154642_fa6d2eff.mp4 (0.49 MB)
2025-11-06 15:46:47,635 - INFO - Starting video analysis for speaker 8
2025-11-06 15:46:47,636 - INFO - Starting video analysis for: voiceprint\video\temp\8_20251106_154642_fa6d2eff.mp4
2025-11-06 15:46:48,612 - INFO - Extracting audio from video (duration: 10.98s)
2025-11-06 15:46:48,631 - INFO - Writing extracted audio to: voiceprint\video\temp\8_20251106_154642_fa6d2eff_extracted_audio.wav
2025-11-06 15:46:49,141 - INFO - Audio extracted successfully: voiceprint\video\temp\8_20251106_154642_fa6d2eff_extracted_audio.wav (1936950 bytes)
2025-11-06 15:46:49,143 - INFO - Analyzing extracted audio for prosody metrics
2025-11-06 15:46:49,144 - INFO - Starting analysis for: voiceprint\video\temp\8_20251106_154642_fa6d2eff_extracted_audio.wav
2025-11-06 15:46:53,048 - INFO - OpenAI API call successful, response length: 480 characters
2025-11-06 15:46:53,048 - INFO - Storing text metrics for speaker 8
2025-11-06 15:46:53,066 - INFO - Text metrics stored successfully for speaker 8
2025-11-06 15:46:53,071 - INFO - Deleted temporary file: voiceprint\text\temp\8_20251106_154642_9fef3c3b.pdf (size: 17949 bytes)
2025-11-06 15:46:53,075 - INFO - Text processing completed successfully for speaker 8
2025-11-06 15:47:03,335 - INFO - Video analysis completed successfully
2025-11-06 15:47:03,445 - INFO - Storing video metrics for speaker 8
2025-11-06 15:47:03,548 - INFO - Video metrics stored successfully for speaker 8
2025-11-06 15:47:03,550 - INFO - Deleted temporary file: voiceprint\video\temp\8_20251106_154642_fa6d2eff.mp4 (size: 514381 bytes)
2025-11-06 15:47:03,551 - INFO - Deleted temporary file: voiceprint\video\temp\8_20251106_154642_fa6d2eff_extracted_audio.wav (size: 1936950 bytes)
2025-11-06 15:47:03,561 - INFO - Video processing completed successfully for speaker 8
2025-11-06 15:47:16,331 - INFO - Storing audio metrics for speaker 8
2025-11-06 15:47:16,337 - INFO - Audio metrics stored successfully for speaker 8
2025-11-06 15:47:16,338 - INFO - Deleted temporary file: voiceprint\audio\temp\8_20251106_154642_561d7982.mp3 (size: 138825 bytes)
2025-11-06 15:47:16,339 - INFO - Audio processing completed successfully for speaker 8
2025-11-06 15:47:16,342 - INFO - Generating tone profile for speaker 8
2025-11-06 15:47:16,386 - INFO - Generating tone profile for speaker 8 using LLM
2025-11-06 15:47:21,643 - INFO - LLM response received for speaker 8, length: 359 characters
2025-11-06 15:47:21,645 - INFO - Tone profile generated successfully for speaker 8
2025-11-06 15:47:21,711 - INFO - Voiceprint tone profile stored successfully for speaker 8
2025-11-06 15:47:21,714 - INFO - Tone profile generated and stored successfully for speaker 8
2025-11-06 15:47:21,715 - INFO - Combined voiceprint processing completed for speaker 8
2025-11-06 15:52:33,385 - INFO - Processing combined voiceprint for speaker 9
2025-11-06 15:52:33,473 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting2.mp4-1762169428514
2025-11-06 15:52:33,493 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3-1762169470624
2025-11-06 15:52:33,540 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_10_opportunity_43_report_20251105_120244.pdf
2025-11-06 15:52:37,204 - INFO - Text file downloaded: voiceprint\text\temp\9_20251106_155233_12f881db.pdf (0.02 MB)
2025-11-06 15:52:37,204 - INFO - Starting text analysis for speaker 9
2025-11-06 15:52:37,834 - INFO - Audio file downloaded: voiceprint\audio\temp\9_20251106_155233_6bfa102b.mp3 (0.13 MB)
2025-11-06 15:52:37,842 - INFO - Starting audio analysis for speaker 9
2025-11-06 15:52:37,856 - INFO - Starting analysis for: voiceprint\audio\temp\9_20251106_155233_6bfa102b.mp3
2025-11-06 15:52:38,324 - INFO - Extracted 16818 characters from PDF
2025-11-06 15:52:38,336 - INFO - Normalized PDF text: 16810 characters
2025-11-06 15:52:38,342 - INFO - Video file downloaded: voiceprint\video\temp\9_20251106_155233_3134fa6d.mp4 (0.49 MB)
2025-11-06 15:52:38,343 - INFO - Starting video analysis for speaker 9
2025-11-06 15:52:38,344 - INFO - Starting video analysis for: voiceprint\video\temp\9_20251106_155233_3134fa6d.mp4
2025-11-06 15:52:39,139 - INFO - Extracting audio from video (duration: 10.98s)
2025-11-06 15:52:39,140 - INFO - Writing extracted audio to: voiceprint\video\temp\9_20251106_155233_3134fa6d_extracted_audio.wav
2025-11-06 15:52:40,230 - INFO - Audio extracted successfully: voiceprint\video\temp\9_20251106_155233_3134fa6d_extracted_audio.wav (1936950 bytes)
2025-11-06 15:52:40,233 - INFO - Analyzing extracted audio for prosody metrics
2025-11-06 15:52:40,235 - INFO - Starting analysis for: voiceprint\video\temp\9_20251106_155233_3134fa6d_extracted_audio.wav
2025-11-06 15:52:45,140 - INFO - OpenAI API call successful, response length: 481 characters
2025-11-06 15:52:45,145 - INFO - Storing text metrics for speaker 9
2025-11-06 15:52:45,179 - INFO - Text metrics stored successfully for speaker 9
2025-11-06 15:52:45,184 - INFO - Deleted temporary file: voiceprint\text\temp\9_20251106_155233_12f881db.pdf (size: 17949 bytes)
2025-11-06 15:52:45,184 - INFO - Text processing completed successfully for speaker 9
2025-11-06 15:52:53,671 - INFO - Video analysis completed successfully
2025-11-06 15:52:53,707 - INFO - Storing video metrics for speaker 9
2025-11-06 15:52:53,727 - INFO - Video metrics stored successfully for speaker 9
2025-11-06 15:52:53,728 - INFO - Deleted temporary file: voiceprint\video\temp\9_20251106_155233_3134fa6d.mp4 (size: 514381 bytes)
2025-11-06 15:52:53,731 - INFO - Deleted temporary file: voiceprint\video\temp\9_20251106_155233_3134fa6d_extracted_audio.wav (size: 1936950 bytes)
2025-11-06 15:52:53,732 - INFO - Video processing completed successfully for speaker 9
2025-11-06 15:53:05,880 - INFO - Storing audio metrics for speaker 9
2025-11-06 15:53:05,890 - INFO - Audio metrics stored successfully for speaker 9
2025-11-06 15:53:05,891 - INFO - Deleted temporary file: voiceprint\audio\temp\9_20251106_155233_6bfa102b.mp3 (size: 138825 bytes)
2025-11-06 15:53:05,893 - INFO - Audio processing completed successfully for speaker 9
2025-11-06 15:53:05,894 - INFO - Generating tone profile for speaker 9
2025-11-06 15:53:05,908 - INFO - Generating tone profile for speaker 9 using LLM
2025-11-06 15:53:10,658 - INFO - LLM response received for speaker 9, length: 359 characters
2025-11-06 15:53:10,663 - INFO - Tone profile generated successfully for speaker 9
2025-11-06 15:53:10,680 - INFO - Voiceprint tone profile stored successfully for speaker 9
2025-11-06 15:53:10,681 - INFO - Tone profile generated and stored successfully for speaker 9
2025-11-06 15:53:10,681 - INFO - Combined voiceprint processing completed for speaker 9
2025-11-06 15:57:44,847 - INFO - Processing combined voiceprint for speaker 9
2025-11-06 15:57:44,922 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3-1762169470624
2025-11-06 15:57:44,926 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_10_opportunity_43_report_20251105_120244.pdf
2025-11-06 15:57:44,932 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting2.mp4-1762169428514
2025-11-06 15:57:47,473 - INFO - Text file downloaded: voiceprint\text\temp\9_20251106_155744_a66545cf.pdf (0.02 MB)
2025-11-06 15:57:47,481 - INFO - Starting text analysis for speaker 9
2025-11-06 15:57:48,629 - INFO - Video file downloaded: voiceprint\video\temp\9_20251106_155744_a6d7f6c8.mp4 (0.49 MB)
2025-11-06 15:57:48,629 - INFO - Starting video analysis for speaker 9
2025-11-06 15:57:48,638 - INFO - Starting video analysis for: voiceprint\video\temp\9_20251106_155744_a6d7f6c8.mp4
2025-11-06 15:57:49,033 - INFO - Extracted 16818 characters from PDF
2025-11-06 15:57:49,042 - INFO - Normalized PDF text: 16810 characters
2025-11-06 15:57:49,246 - INFO - Extracting audio from video (duration: 10.98s)
2025-11-06 15:57:49,246 - INFO - Writing extracted audio to: voiceprint\video\temp\9_20251106_155744_a6d7f6c8_extracted_audio.wav
2025-11-06 15:57:49,292 - INFO - Audio file downloaded: voiceprint\audio\temp\9_20251106_155744_69a7f1c2.mp3 (0.13 MB)
2025-11-06 15:57:49,293 - INFO - Starting audio analysis for speaker 9
2025-11-06 15:57:49,299 - INFO - Starting analysis for: voiceprint\audio\temp\9_20251106_155744_69a7f1c2.mp3
2025-11-06 15:57:50,897 - INFO - Audio extracted successfully: voiceprint\video\temp\9_20251106_155744_a6d7f6c8_extracted_audio.wav (1936950 bytes)
2025-11-06 15:57:50,898 - INFO - Analyzing extracted audio for prosody metrics
2025-11-06 15:57:50,899 - INFO - Starting analysis for: voiceprint\video\temp\9_20251106_155744_a6d7f6c8_extracted_audio.wav
2025-11-06 15:57:53,807 - INFO - OpenAI API call successful, response length: 480 characters
2025-11-06 15:57:53,809 - INFO - Storing text metrics for speaker 9
2025-11-06 15:57:53,844 - INFO - Text metrics stored successfully for speaker 9
2025-11-06 15:57:53,845 - INFO - Deleted temporary file: voiceprint\text\temp\9_20251106_155744_a66545cf.pdf (size: 17949 bytes)
2025-11-06 15:57:53,847 - INFO - Text processing completed successfully for speaker 9
2025-11-06 15:58:03,115 - INFO - Video analysis completed successfully
2025-11-06 15:58:03,133 - INFO - Storing video metrics for speaker 9
2025-11-06 15:58:03,143 - INFO - Video metrics stored successfully for speaker 9
2025-11-06 15:58:03,144 - INFO - Deleted temporary file: voiceprint\video\temp\9_20251106_155744_a6d7f6c8.mp4 (size: 514381 bytes)
2025-11-06 15:58:03,145 - INFO - Deleted temporary file: voiceprint\video\temp\9_20251106_155744_a6d7f6c8_extracted_audio.wav (size: 1936950 bytes)
2025-11-06 15:58:03,145 - INFO - Video processing completed successfully for speaker 9
2025-11-06 15:58:17,277 - INFO - Storing audio metrics for speaker 9
2025-11-06 15:58:17,290 - INFO - Audio metrics stored successfully for speaker 9
2025-11-06 15:58:17,291 - INFO - Deleted temporary file: voiceprint\audio\temp\9_20251106_155744_69a7f1c2.mp3 (size: 138825 bytes)
2025-11-06 15:58:17,291 - INFO - Audio processing completed successfully for speaker 9
2025-11-06 15:58:17,293 - INFO - Generating tone profile for speaker 9
2025-11-06 15:58:17,300 - INFO - Generating tone profile for speaker 9 using LLM
2025-11-06 15:58:20,128 - INFO - LLM response received for speaker 9, length: 359 characters
2025-11-06 15:58:20,128 - INFO - Tone profile generated successfully for speaker 9
2025-11-06 15:58:20,163 - INFO - Voiceprint tone profile stored successfully for speaker 9
2025-11-06 15:58:20,166 - INFO - Tone profile generated and stored successfully for speaker 9
2025-11-06 15:58:20,167 - INFO - Combined voiceprint processing completed for speaker 9
2025-11-06 15:58:34,153 - INFO - Processing combined voiceprint for speaker 8
2025-11-06 15:58:34,171 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting2.mp4-1762169428514
2025-11-06 15:58:34,172 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_10_opportunity_43_report_20251105_120244.pdf
2025-11-06 15:58:34,173 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting1.mp3-1762169470624
2025-11-06 15:58:36,415 - INFO - Text file downloaded: voiceprint\text\temp\8_20251106_155834_0525324e.pdf (0.02 MB)
2025-11-06 15:58:36,415 - INFO - Starting text analysis for speaker 8
2025-11-06 15:58:37,143 - INFO - Extracted 16818 characters from PDF
2025-11-06 15:58:37,154 - INFO - Normalized PDF text: 16810 characters
2025-11-06 15:58:37,158 - INFO - Audio file downloaded: voiceprint\audio\temp\8_20251106_155834_11291ed5.mp3 (0.13 MB)
2025-11-06 15:58:37,159 - INFO - Starting audio analysis for speaker 8
2025-11-06 15:58:37,159 - INFO - Starting analysis for: voiceprint\audio\temp\8_20251106_155834_11291ed5.mp3
2025-11-06 15:58:37,736 - INFO - Video file downloaded: voiceprint\video\temp\8_20251106_155834_a0757989.mp4 (0.49 MB)
2025-11-06 15:58:37,738 - INFO - Starting video analysis for speaker 8
2025-11-06 15:58:37,739 - INFO - Starting video analysis for: voiceprint\video\temp\8_20251106_155834_a0757989.mp4
2025-11-06 15:58:49,463 - INFO - OpenAI API call successful, response length: 480 characters
2025-11-06 15:58:49,487 - INFO - Storing text metrics for speaker 8
2025-11-06 15:58:49,493 - INFO - Extracting audio from video (duration: 10.98s)
2025-11-06 15:58:49,495 - INFO - Writing extracted audio to: voiceprint\video\temp\8_20251106_155834_a0757989_extracted_audio.wav
2025-11-06 15:58:49,540 - INFO - Text metrics stored successfully for speaker 8
2025-11-06 15:58:49,543 - INFO - Deleted temporary file: voiceprint\text\temp\8_20251106_155834_0525324e.pdf (size: 17949 bytes)
2025-11-06 15:58:49,544 - INFO - Text processing completed successfully for speaker 8
2025-11-06 15:58:49,565 - INFO - Storing audio metrics for speaker 8
2025-11-06 15:58:49,579 - INFO - Audio metrics stored successfully for speaker 8
2025-11-06 15:58:49,587 - INFO - Deleted temporary file: voiceprint\audio\temp\8_20251106_155834_11291ed5.mp3 (size: 138825 bytes)
2025-11-06 15:58:49,592 - INFO - Audio processing completed successfully for speaker 8
2025-11-06 15:58:49,775 - INFO - Audio extracted successfully: voiceprint\video\temp\8_20251106_155834_a0757989_extracted_audio.wav (1936950 bytes)
2025-11-06 15:58:49,775 - INFO - Analyzing extracted audio for prosody metrics
2025-11-06 15:58:49,776 - INFO - Starting analysis for: voiceprint\video\temp\8_20251106_155834_a0757989_extracted_audio.wav
2025-11-06 15:58:57,868 - INFO - Video analysis completed successfully
2025-11-06 15:58:57,918 - INFO - Storing video metrics for speaker 8
2025-11-06 15:58:57,925 - INFO - Video metrics stored successfully for speaker 8
2025-11-06 15:58:57,926 - INFO - Deleted temporary file: voiceprint\video\temp\8_20251106_155834_a0757989.mp4 (size: 514381 bytes)
2025-11-06 15:58:57,927 - INFO - Deleted temporary file: voiceprint\video\temp\8_20251106_155834_a0757989_extracted_audio.wav (size: 1936950 bytes)
2025-11-06 15:58:57,928 - INFO - Video processing completed successfully for speaker 8
2025-11-06 15:58:57,929 - INFO - Generating tone profile for speaker 8
2025-11-06 15:58:57,954 - INFO - Generating tone profile for speaker 8 using LLM
2025-11-06 15:59:00,766 - INFO - LLM response received for speaker 8, length: 359 characters
2025-11-06 15:59:00,767 - INFO - Tone profile generated successfully for speaker 8
2025-11-06 15:59:00,778 - INFO - Voiceprint tone profile stored successfully for speaker 8
2025-11-06 15:59:00,779 - INFO - Tone profile generated and stored successfully for speaker 8
2025-11-06 15:59:00,779 - INFO - Combined voiceprint processing completed for speaker 8
2025-11-06 18:42:47,556 - INFO - Processing combined voiceprint for speaker 2
2025-11-06 18:42:48,748 - ERROR - Error processing text sample for speaker 2: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 2, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'speakers.voiceprint' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 399, in process_text_sample
    self._validate_speaker(speaker_id)
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 141, in _validate_speaker
    speaker = session.query(Speaker).filter_by(id=speaker_id).first()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 2, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:42:48,853 - ERROR - Error processing audio sample for speaker 2: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 2, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'speakers.voiceprint' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 70, in process_audio_sample
    self._validate_speaker(speaker_id)
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 141, in _validate_speaker
    speaker = session.query(Speaker).filter_by(id=speaker_id).first()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 2, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:42:49,041 - ERROR - Error processing text for speaker 2: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 2, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'speakers.voiceprint' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\controllers\voiceprint_controller.py", line 637, in process_sample
    return await asyncio.to_thread(process_func, speaker_id, url)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 399, in process_text_sample
    self._validate_speaker(speaker_id)
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 141, in _validate_speaker
    speaker = session.query(Speaker).filter_by(id=speaker_id).first()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 2, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:42:49,138 - ERROR - Error processing audio for speaker 2: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 2, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'speakers.voiceprint' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\controllers\voiceprint_controller.py", line 637, in process_sample
    return await asyncio.to_thread(process_func, speaker_id, url)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 70, in process_audio_sample
    self._validate_speaker(speaker_id)
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 141, in _validate_speaker
    speaker = session.query(Speaker).filter_by(id=speaker_id).first()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 2, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:42:50,398 - ERROR - Error processing video sample for speaker 2: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 2, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'speakers.voiceprint' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 260, in process_video_sample
    self._validate_speaker(speaker_id)
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 141, in _validate_speaker
    speaker = session.query(Speaker).filter_by(id=speaker_id).first()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 2, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:42:50,449 - ERROR - Error processing video for speaker 2: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 2, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'speakers.voiceprint' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\controllers\voiceprint_controller.py", line 637, in process_sample
    return await asyncio.to_thread(process_func, speaker_id, url)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 260, in process_video_sample
    self._validate_speaker(speaker_id)
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 141, in _validate_speaker
    speaker = session.query(Speaker).filter_by(id=speaker_id).first()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 2, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:42:50,586 - WARNING - Not generating tone profile for speaker 2 - one or more samples failed. Text: False, Audio: False, Video: False
2025-11-06 18:42:50,602 - INFO - Combined voiceprint processing completed for speaker 2
2025-11-06 18:54:46,896 - INFO - Processing combined voiceprint for speaker 110
2025-11-06 18:54:51,342 - ERROR - Error processing audio sample for speaker 110: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 110, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'speakers.voiceprint' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 70, in process_audio_sample
    self._validate_speaker(speaker_id)
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 141, in _validate_speaker
    speaker = session.query(Speaker).filter_by(id=speaker_id).first()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 110, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:54:51,627 - ERROR - Error processing text sample for speaker 110: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 110, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'speakers.voiceprint' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 375, in process_text_sample
    self._validate_speaker(speaker_id)
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 141, in _validate_speaker
    speaker = session.query(Speaker).filter_by(id=speaker_id).first()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 110, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:54:51,819 - ERROR - Error processing video sample for speaker 110: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 110, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'speakers.voiceprint' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 236, in process_video_sample
    self._validate_speaker(speaker_id)
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 141, in _validate_speaker
    speaker = session.query(Speaker).filter_by(id=speaker_id).first()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 110, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:54:52,094 - ERROR - Error processing audio for speaker 110: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 110, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'speakers.voiceprint' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\controllers\voiceprint_controller.py", line 637, in process_sample
    return await asyncio.to_thread(process_func, speaker_id, url)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 70, in process_audio_sample
    self._validate_speaker(speaker_id)
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 141, in _validate_speaker
    speaker = session.query(Speaker).filter_by(id=speaker_id).first()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 110, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:54:52,812 - ERROR - Error processing text for speaker 110: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 110, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'speakers.voiceprint' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\controllers\voiceprint_controller.py", line 637, in process_sample
    return await asyncio.to_thread(process_func, speaker_id, url)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 375, in process_text_sample
    self._validate_speaker(speaker_id)
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 141, in _validate_speaker
    speaker = session.query(Speaker).filter_by(id=speaker_id).first()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 110, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:54:53,048 - ERROR - Error processing video for speaker 110: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 110, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1054, "Unknown column 'speakers.voiceprint' in 'field list'")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\controllers\voiceprint_controller.py", line 637, in process_sample
    return await asyncio.to_thread(process_func, speaker_id, url)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 236, in process_video_sample
    self._validate_speaker(speaker_id)
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 141, in _validate_speaker
    speaker = session.query(Speaker).filter_by(id=speaker_id).first()
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2759, in first
    return self.limit(1)._iter().first()  # type: ignore
           ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\query.py", line 2857, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
                                                  ^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2365, in execute
    return self._execute_internal(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2251, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\context.py", line 306, in orm_execute_statement
    result = conn.execute(
             ^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1054, "Unknown column 'speakers.voiceprint' in 'field list'")
[SQL: SELECT speakers.id AS speakers_id, speakers.title AS speakers_title, speakers.name AS speakers_name, speakers.email AS speakers_email, speakers.phone_number AS speakers_phone_number, speakers.city AS speakers_city, speakers.state AS speakers_state, speakers.country AS speakers_country, speakers.affiliate_id AS speakers_affiliate_id, speakers.password AS speakers_password, speakers.linkedin AS speakers_linkedin, speakers.bio AS speakers_bio, speakers.company AS speakers_company, speakers.speaker_website AS speakers_speaker_website, speakers.status AS speakers_status, speakers.source AS speakers_source, speakers.headshot AS speakers_headshot, speakers.stripe_customer_id AS speakers_stripe_customer_id, speakers.primary_category AS speakers_primary_category, speakers.subcategory AS speakers_subcategory, speakers.topic AS speakers_topic, speakers.learning_objectives AS speakers_learning_objectives, speakers.takeaways AS speakers_takeaways, speakers.challenges AS speakers_challenges, speakers.preferred_speaker_geography AS speakers_preferred_speaker_geography, speakers.speaker_credentials AS speakers_speaker_credentials, speakers.top_keywords AS speakers_top_keywords, speakers.differentiators AS speakers_differentiators, speakers.voiceprint_text_metrics AS speakers_voiceprint_text_metrics, speakers.voiceprint_audio_metrics AS speakers_voiceprint_audio_metrics, speakers.voiceprint_video_metrics AS speakers_voiceprint_video_metrics, speakers.voiceprint AS speakers_voiceprint, speakers.created_at AS speakers_created_at, speakers.updated_at AS speakers_updated_at 
FROM speakers 
WHERE speakers.id = %(id_1)s 
 LIMIT %(param_1)s]
[parameters: {'id_1': 110, 'param_1': 1}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:54:53,290 - WARNING - Not generating tone profile for speaker 110 - one or more samples failed. Text: False, Audio: False, Video: False
2025-11-06 18:54:53,430 - INFO - Combined voiceprint processing completed for speaker 110
2025-11-06 18:57:33,730 - INFO - Processing combined voiceprint for speaker 110
2025-11-06 18:57:34,854 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_1_opportunity_1_report.pdf
2025-11-06 18:57:34,875 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting1.mp4
2025-11-06 18:57:34,885 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting2.mp3
2025-11-06 18:57:37,448 - INFO - Text file downloaded: voiceprint\text\temp\110_20251106_185734_0737508f.pdf (0.02 MB)
2025-11-06 18:57:37,450 - INFO - Starting text analysis for speaker 110
2025-11-06 18:57:37,873 - INFO - Extracted 16581 characters from PDF
2025-11-06 18:57:37,881 - INFO - Normalized PDF text: 16597 characters
2025-11-06 18:57:42,643 - INFO - OpenAI API call successful, response length: 480 characters
2025-11-06 18:57:42,645 - INFO - Storing text metrics for speaker 110
2025-11-06 18:57:44,072 - INFO - Text metrics stored successfully for speaker 110
2025-11-06 18:57:44,075 - INFO - Deleted temporary file: voiceprint\text\temp\110_20251106_185734_0737508f.pdf (size: 16711 bytes)
2025-11-06 18:57:44,077 - INFO - Text processing completed successfully for speaker 110
2025-11-06 18:57:59,961 - INFO - Video file downloaded: voiceprint\video\temp\110_20251106_185734_3518bb5e.mp4 (1.40 MB)
2025-11-06 18:57:59,962 - INFO - Starting video analysis for speaker 110
2025-11-06 18:57:59,962 - INFO - Starting video analysis for: voiceprint\video\temp\110_20251106_185734_3518bb5e.mp4
2025-11-06 18:58:00,393 - INFO - Extracting audio from video (duration: 19.82s)
2025-11-06 18:58:00,394 - INFO - Writing extracted audio to: voiceprint\video\temp\110_20251106_185734_3518bb5e_extracted_audio.wav
2025-11-06 18:58:00,656 - INFO - Audio extracted successfully: voiceprint\video\temp\110_20251106_185734_3518bb5e_extracted_audio.wav (3496326 bytes)
2025-11-06 18:58:00,656 - INFO - Analyzing extracted audio for prosody metrics
2025-11-06 18:58:00,657 - INFO - Starting analysis for: voiceprint\video\temp\110_20251106_185734_3518bb5e_extracted_audio.wav
2025-11-06 18:58:01,309 - INFO - Audio file downloaded: voiceprint\audio\temp\110_20251106_185734_50832cda.mp3 (1.83 MB)
2025-11-06 18:58:01,310 - INFO - Starting audio analysis for speaker 110
2025-11-06 18:58:01,310 - INFO - Starting analysis for: voiceprint\audio\temp\110_20251106_185734_50832cda.mp3
2025-11-06 18:58:13,416 - INFO - Video analysis completed successfully
2025-11-06 18:58:13,460 - INFO - Storing video metrics for speaker 110
2025-11-06 18:58:43,161 - INFO - Storing audio metrics for speaker 110
2025-11-06 18:58:43,799 - ERROR - Error storing video metrics for speaker 110: (pymysql.err.OperationalError) (1213, 'Deadlock found when trying to get lock; try restarting transaction')
[SQL: UPDATE speakers SET voiceprint_video_metrics=%(voiceprint_video_metrics)s, updated_at=CURRENT_TIMESTAMP WHERE speakers.id = %(speakers_id)s]
[parameters: {'voiceprint_video_metrics': '{"audio_analysis": {"pitch_variance": 1577.5848849419422, "pitch_mean": 123.20230041880268, "pitch_range": 155.8680557097781, "tempo": {"speech_rate_ ... (411 characters truncated) ... 9.4, "syllables_estimated": 51.2}, "pace_characteristics": {"words_per_minute": 119.31, "syllables_per_minute": 155.1, "average_pause_length": 0.1}}}', 'speakers_id': 110}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1213, 'Deadlock found when trying to get lock; try restarting transaction')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 323, in _store_video_metrics
    session.commit()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 137, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 137, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 85, in save_obj
    _emit_update_statements(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 912, in _emit_update_statements
    c = connection.execute(
        ^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1213, 'Deadlock found when trying to get lock; try restarting transaction')
[SQL: UPDATE speakers SET voiceprint_video_metrics=%(voiceprint_video_metrics)s, updated_at=CURRENT_TIMESTAMP WHERE speakers.id = %(speakers_id)s]
[parameters: {'voiceprint_video_metrics': '{"audio_analysis": {"pitch_variance": 1577.5848849419422, "pitch_mean": 123.20230041880268, "pitch_range": 155.8680557097781, "tempo": {"speech_rate_ ... (411 characters truncated) ... 9.4, "syllables_estimated": 51.2}, "pace_characteristics": {"words_per_minute": 119.31, "syllables_per_minute": 155.1, "average_pause_length": 0.1}}}', 'speakers_id': 110}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:58:43,940 - ERROR - Error processing video sample for speaker 110: (pymysql.err.OperationalError) (1213, 'Deadlock found when trying to get lock; try restarting transaction')
[SQL: UPDATE speakers SET voiceprint_video_metrics=%(voiceprint_video_metrics)s, updated_at=CURRENT_TIMESTAMP WHERE speakers.id = %(speakers_id)s]
[parameters: {'voiceprint_video_metrics': '{"audio_analysis": {"pitch_variance": 1577.5848849419422, "pitch_mean": 123.20230041880268, "pitch_range": 155.8680557097781, "tempo": {"speech_rate_ ... (411 characters truncated) ... 9.4, "syllables_estimated": 51.2}, "pace_characteristics": {"words_per_minute": 119.31, "syllables_per_minute": 155.1, "average_pause_length": 0.1}}}', 'speakers_id': 110}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1213, 'Deadlock found when trying to get lock; try restarting transaction')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 274, in process_video_sample
    self._store_video_metrics(speaker_id, video_metrics)
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 323, in _store_video_metrics
    session.commit()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 137, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 137, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 85, in save_obj
    _emit_update_statements(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 912, in _emit_update_statements
    c = connection.execute(
        ^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1213, 'Deadlock found when trying to get lock; try restarting transaction')
[SQL: UPDATE speakers SET voiceprint_video_metrics=%(voiceprint_video_metrics)s, updated_at=CURRENT_TIMESTAMP WHERE speakers.id = %(speakers_id)s]
[parameters: {'voiceprint_video_metrics': '{"audio_analysis": {"pitch_variance": 1577.5848849419422, "pitch_mean": 123.20230041880268, "pitch_range": 155.8680557097781, "tempo": {"speech_rate_ ... (411 characters truncated) ... 9.4, "syllables_estimated": 51.2}, "pace_characteristics": {"words_per_minute": 119.31, "syllables_per_minute": 155.1, "average_pause_length": 0.1}}}', 'speakers_id': 110}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:58:44,003 - INFO - Deleted temporary file: voiceprint\video\temp\110_20251106_185734_3518bb5e.mp4 (size: 1473140 bytes)
2025-11-06 18:58:44,004 - INFO - Deleted temporary file: voiceprint\video\temp\110_20251106_185734_3518bb5e_extracted_audio.wav (size: 3496326 bytes)
2025-11-06 18:58:44,006 - ERROR - Error processing video for speaker 110: (pymysql.err.OperationalError) (1213, 'Deadlock found when trying to get lock; try restarting transaction')
[SQL: UPDATE speakers SET voiceprint_video_metrics=%(voiceprint_video_metrics)s, updated_at=CURRENT_TIMESTAMP WHERE speakers.id = %(speakers_id)s]
[parameters: {'voiceprint_video_metrics': '{"audio_analysis": {"pitch_variance": 1577.5848849419422, "pitch_mean": 123.20230041880268, "pitch_range": 155.8680557097781, "tempo": {"speech_rate_ ... (411 characters truncated) ... 9.4, "syllables_estimated": 51.2}, "pace_characteristics": {"words_per_minute": 119.31, "syllables_per_minute": 155.1, "average_pause_length": 0.1}}}', 'speakers_id': 110}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
pymysql.err.OperationalError: (1213, 'Deadlock found when trying to get lock; try restarting transaction')

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\speakerbot\app-speakersbot\admin\python\app\controllers\voiceprint_controller.py", line 637, in process_sample
    return await asyncio.to_thread(process_func, speaker_id, url)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\asyncio\threads.py", line 25, in to_thread
    return await loop.run_in_executor(None, func_call)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\concurrent\futures\thread.py", line 59, in run
    result = self.fn(*self.args, **self.kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 274, in process_video_sample
    self._store_video_metrics(speaker_id, video_metrics)
  File "D:\speakerbot\app-speakersbot\admin\python\app\services\voiceprint_service.py", line 323, in _store_video_metrics
    session.commit()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 2032, in commit
    trans.commit(_to_root=True)
  File "<string>", line 2, in commit
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 137, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1313, in commit
    self._prepare_impl()
  File "<string>", line 2, in _prepare_impl
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\state_changes.py", line 137, in _go
    ret_value = fn(self, *arg, **kw)
                ^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 1288, in _prepare_impl
    self.session.flush()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4345, in flush
    self._flush(objects)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4480, in _flush
    with util.safe_reraise():
         ^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\util\langhelpers.py", line 224, in __exit__
    raise exc_value.with_traceback(exc_tb)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\session.py", line 4441, in _flush
    flush_context.execute()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 466, in execute
    rec.execute(self)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\unitofwork.py", line 642, in execute
    util.preloaded.orm_persistence.save_obj(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 85, in save_obj
    _emit_update_statements(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\orm\persistence.py", line 912, in _emit_update_statements
    c = connection.execute(
        ^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1419, in execute
    return meth(
           ^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\sql\elements.py", line 526, in _execute_on_connection
    return connection._execute_clauseelement(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1641, in _execute_clauseelement
    ret = self._execute_context(
          ^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 2355, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\sqlalchemy\engine\default.py", line 951, in do_execute
    cursor.execute(statement, parameters)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 153, in execute
    result = self._query(query)
             ^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\cursors.py", line 322, in _query
    conn.query(q)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 575, in query
    self._affected_rows = self._read_query_result(unbuffered=unbuffered)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 826, in _read_query_result
    result.read()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 1203, in read
    first_packet = self.connection._read_packet()
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\connections.py", line 782, in _read_packet
    packet.raise_for_error()
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\protocol.py", line 219, in raise_for_error
    err.raise_mysql_exception(self._data)
  File "D:\speakerbot\app-speakersbot\admin\python\venv\Lib\site-packages\pymysql\err.py", line 150, in raise_mysql_exception
    raise errorclass(errno, errval)
sqlalchemy.exc.OperationalError: (pymysql.err.OperationalError) (1213, 'Deadlock found when trying to get lock; try restarting transaction')
[SQL: UPDATE speakers SET voiceprint_video_metrics=%(voiceprint_video_metrics)s, updated_at=CURRENT_TIMESTAMP WHERE speakers.id = %(speakers_id)s]
[parameters: {'voiceprint_video_metrics': '{"audio_analysis": {"pitch_variance": 1577.5848849419422, "pitch_mean": 123.20230041880268, "pitch_range": 155.8680557097781, "tempo": {"speech_rate_ ... (411 characters truncated) ... 9.4, "syllables_estimated": 51.2}, "pace_characteristics": {"words_per_minute": 119.31, "syllables_per_minute": 155.1, "average_pause_length": 0.1}}}', 'speakers_id': 110}]
(Background on this error at: https://sqlalche.me/e/20/e3q8)
2025-11-06 18:58:45,445 - INFO - Audio metrics stored successfully for speaker 110
2025-11-06 18:58:45,446 - INFO - Deleted temporary file: voiceprint\audio\temp\110_20251106_185734_50832cda.mp3 (size: 1920940 bytes)
2025-11-06 18:58:45,449 - INFO - Audio processing completed successfully for speaker 110
2025-11-06 18:58:45,450 - WARNING - Not generating tone profile for speaker 110 - one or more samples failed. Text: True, Audio: True, Video: False
2025-11-06 18:58:45,451 - INFO - Combined voiceprint processing completed for speaker 110
2025-11-06 19:13:37,062 - INFO - Processing combined voiceprint for speaker 1
2025-11-06 19:13:37,152 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_9_opportunity_43_report_20251106_094851.pdf
2025-11-06 19:13:37,154 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting2.mp3
2025-11-06 19:13:37,241 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting1.mp4
2025-11-06 19:13:39,704 - INFO - Text file downloaded: voiceprint\text\temp\1_20251106_191337_b4e664c3.pdf (0.02 MB)
2025-11-06 19:13:39,706 - INFO - Starting text analysis for speaker 1
2025-11-06 19:13:41,069 - INFO - Extracted 16520 characters from PDF
2025-11-06 19:13:41,082 - INFO - Normalized PDF text: 16515 characters
2025-11-06 19:13:45,437 - INFO - OpenAI API call successful, response length: 481 characters
2025-11-06 19:13:45,445 - INFO - Storing text metrics for speaker 1
2025-11-06 19:13:45,467 - INFO - Text metrics stored successfully for speaker 1
2025-11-06 19:13:45,467 - INFO - Deleted temporary file: voiceprint\text\temp\1_20251106_191337_b4e664c3.pdf (size: 18067 bytes)
2025-11-06 19:13:45,467 - INFO - Text processing completed successfully for speaker 1
2025-11-06 19:14:14,544 - INFO - Audio file downloaded: voiceprint\audio\temp\1_20251106_191337_05a16c1e.mp3 (1.83 MB)
2025-11-06 19:14:14,546 - INFO - Starting audio analysis for speaker 1
2025-11-06 19:14:14,547 - INFO - Starting analysis for: voiceprint\audio\temp\1_20251106_191337_05a16c1e.mp3
2025-11-06 19:14:49,439 - INFO - Storing audio metrics for speaker 1
2025-11-06 19:14:49,449 - INFO - Audio metrics stored successfully for speaker 1
2025-11-06 19:14:49,451 - INFO - Deleted temporary file: voiceprint\audio\temp\1_20251106_191337_05a16c1e.mp3 (size: 1920940 bytes)
2025-11-06 19:14:49,453 - INFO - Audio processing completed successfully for speaker 1
2025-11-06 19:14:58,589 - INFO - Video file downloaded: voiceprint\video\temp\1_20251106_191337_ae81442f.mp4 (1.40 MB)
2025-11-06 19:14:58,591 - INFO - Starting video analysis for speaker 1
2025-11-06 19:14:58,593 - INFO - Starting video analysis for: voiceprint\video\temp\1_20251106_191337_ae81442f.mp4
2025-11-06 19:14:59,060 - INFO - Extracting audio from video (duration: 19.82s)
2025-11-06 19:14:59,060 - INFO - Writing extracted audio to: voiceprint\video\temp\1_20251106_191337_ae81442f_extracted_audio.wav
2025-11-06 19:14:59,350 - INFO - Audio extracted successfully: voiceprint\video\temp\1_20251106_191337_ae81442f_extracted_audio.wav (3496326 bytes)
2025-11-06 19:14:59,351 - INFO - Analyzing extracted audio for prosody metrics
2025-11-06 19:14:59,352 - INFO - Starting analysis for: voiceprint\video\temp\1_20251106_191337_ae81442f_extracted_audio.wav
2025-11-06 19:15:10,344 - INFO - Video analysis completed successfully
2025-11-06 19:15:10,372 - INFO - Storing video metrics for speaker 1
2025-11-06 19:15:10,388 - INFO - Video metrics stored successfully for speaker 1
2025-11-06 19:15:10,390 - INFO - Deleted temporary file: voiceprint\video\temp\1_20251106_191337_ae81442f.mp4 (size: 1473140 bytes)
2025-11-06 19:15:10,392 - INFO - Deleted temporary file: voiceprint\video\temp\1_20251106_191337_ae81442f_extracted_audio.wav (size: 3496326 bytes)
2025-11-06 19:15:10,392 - INFO - Video processing completed successfully for speaker 1
2025-11-06 19:15:10,393 - INFO - Generating tone profile for speaker 1
2025-11-06 19:15:10,423 - INFO - Generating tone profile for speaker 1 using LLM
2025-11-06 19:15:14,722 - INFO - LLM response received for speaker 1, length: 359 characters
2025-11-06 19:15:14,723 - INFO - Tone profile generated successfully for speaker 1
2025-11-06 19:15:14,744 - INFO - Voiceprint tone profile stored successfully for speaker 1
2025-11-06 19:15:14,746 - INFO - Tone profile generated and stored successfully for speaker 1
2025-11-06 19:15:14,753 - INFO - Combined voiceprint processing completed for speaker 1
2025-11-06 19:22:15,193 - INFO - Processing combined voiceprint for speaker 1
2025-11-06 19:22:15,304 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_9_opportunity_43_report_20251106_094851.pdf
2025-11-06 19:22:15,316 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting1.mp4
2025-11-06 19:22:15,317 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting2.mp3
2025-11-06 19:22:18,082 - INFO - Text file downloaded: voiceprint\text\temp\1_20251106_192215_0b223fb9.pdf (0.02 MB)
2025-11-06 19:22:18,083 - INFO - Starting text analysis for speaker 1
2025-11-06 19:22:18,835 - INFO - Extracted 16520 characters from PDF
2025-11-06 19:22:18,843 - INFO - Normalized PDF text: 16515 characters
2025-11-06 19:22:23,126 - INFO - OpenAI API call successful, response length: 480 characters
2025-11-06 19:22:23,130 - INFO - Storing text metrics for speaker 1
2025-11-06 19:22:23,210 - INFO - Text metrics stored successfully for speaker 1
2025-11-06 19:22:23,217 - INFO - Deleted temporary file: voiceprint\text\temp\1_20251106_192215_0b223fb9.pdf (size: 18067 bytes)
2025-11-06 19:22:23,218 - INFO - Text processing completed successfully for speaker 1
2025-11-06 19:22:32,768 - INFO - Video file downloaded: voiceprint\video\temp\1_20251106_192215_7d3ea47b.mp4 (1.40 MB)
2025-11-06 19:22:32,770 - INFO - Starting video analysis for speaker 1
2025-11-06 19:22:32,774 - INFO - Starting video analysis for: voiceprint\video\temp\1_20251106_192215_7d3ea47b.mp4
2025-11-06 19:22:33,342 - INFO - Extracting audio from video (duration: 19.82s)
2025-11-06 19:22:33,343 - INFO - Writing extracted audio to: voiceprint\video\temp\1_20251106_192215_7d3ea47b_extracted_audio.wav
2025-11-06 19:22:33,668 - INFO - Audio extracted successfully: voiceprint\video\temp\1_20251106_192215_7d3ea47b_extracted_audio.wav (3496326 bytes)
2025-11-06 19:22:33,669 - INFO - Analyzing extracted audio for prosody metrics
2025-11-06 19:22:33,670 - INFO - Starting analysis for: voiceprint\video\temp\1_20251106_192215_7d3ea47b_extracted_audio.wav
2025-11-06 19:22:55,559 - INFO - Video analysis completed successfully
2025-11-06 19:22:55,578 - INFO - Storing video metrics for speaker 1
2025-11-06 19:22:55,583 - INFO - Video metrics stored successfully for speaker 1
2025-11-06 19:22:55,585 - INFO - Deleted temporary file: voiceprint\video\temp\1_20251106_192215_7d3ea47b.mp4 (size: 1473140 bytes)
2025-11-06 19:22:55,589 - INFO - Deleted temporary file: voiceprint\video\temp\1_20251106_192215_7d3ea47b_extracted_audio.wav (size: 3496326 bytes)
2025-11-06 19:22:55,591 - INFO - Video processing completed successfully for speaker 1
2025-11-06 19:23:12,242 - INFO - Audio file downloaded: voiceprint\audio\temp\1_20251106_192215_7bcff9a3.mp3 (1.83 MB)
2025-11-06 19:23:12,244 - INFO - Starting audio analysis for speaker 1
2025-11-06 19:23:12,245 - INFO - Starting analysis for: voiceprint\audio\temp\1_20251106_192215_7bcff9a3.mp3
2025-11-06 19:23:46,297 - INFO - Storing audio metrics for speaker 1
2025-11-06 19:23:46,305 - INFO - Audio metrics stored successfully for speaker 1
2025-11-06 19:23:46,306 - INFO - Deleted temporary file: voiceprint\audio\temp\1_20251106_192215_7bcff9a3.mp3 (size: 1920940 bytes)
2025-11-06 19:23:46,306 - INFO - Audio processing completed successfully for speaker 1
2025-11-06 19:23:46,307 - INFO - Generating tone profile for speaker 1
2025-11-06 19:23:46,317 - INFO - Generating tone profile for speaker 1 using LLM
2025-11-06 19:23:51,108 - INFO - LLM response received for speaker 1, length: 359 characters
2025-11-06 19:23:51,109 - INFO - Tone profile generated successfully for speaker 1
2025-11-06 19:23:51,126 - INFO - Voiceprint tone profile stored successfully for speaker 1
2025-11-06 19:23:51,127 - INFO - Tone profile generated and stored successfully for speaker 1
2025-11-06 19:23:51,129 - INFO - Combined voiceprint processing completed for speaker 1
2025-11-07 11:08:55,762 - INFO - Processing combined voiceprint for speaker 2
2025-11-07 11:08:55,868 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_10_opportunity_43_report_20251105_120244.pdf
2025-11-07 11:08:55,876 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting2.mp3
2025-11-07 11:08:55,879 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting1.mp4
2025-11-07 11:08:57,914 - INFO - Text file downloaded: voiceprint\text\temp\2_20251107_110855_c7f9ae51.pdf (0.02 MB)
2025-11-07 11:08:57,915 - INFO - Starting text analysis for speaker 2
2025-11-07 11:08:58,344 - INFO - Extracted 16818 characters from PDF
2025-11-07 11:08:58,350 - INFO - Normalized PDF text: 16810 characters
2025-11-07 11:09:05,029 - INFO - OpenAI API call successful, response length: 481 characters
2025-11-07 11:09:05,030 - INFO - Storing text metrics for speaker 2
2025-11-07 11:09:05,040 - INFO - Text metrics stored successfully for speaker 2
2025-11-07 11:09:05,042 - INFO - Deleted temporary file: voiceprint\text\temp\2_20251107_110855_c7f9ae51.pdf (size: 17949 bytes)
2025-11-07 11:09:05,042 - INFO - Text processing completed successfully for speaker 2
2025-11-07 11:09:13,211 - INFO - Video file downloaded: voiceprint\video\temp\2_20251107_110855_8b363a08.mp4 (1.40 MB)
2025-11-07 11:09:13,212 - INFO - Starting video analysis for speaker 2
2025-11-07 11:09:13,212 - INFO - Starting video analysis for: voiceprint\video\temp\2_20251107_110855_8b363a08.mp4
2025-11-07 11:09:13,675 - INFO - Extracting audio from video (duration: 19.82s)
2025-11-07 11:09:13,676 - INFO - Writing extracted audio to: voiceprint\video\temp\2_20251107_110855_8b363a08_extracted_audio.wav
2025-11-07 11:09:14,468 - INFO - Audio extracted successfully: voiceprint\video\temp\2_20251107_110855_8b363a08_extracted_audio.wav (3496326 bytes)
2025-11-07 11:09:14,469 - INFO - Analyzing extracted audio for prosody metrics
2025-11-07 11:09:14,474 - INFO - Starting analysis for: voiceprint\video\temp\2_20251107_110855_8b363a08_extracted_audio.wav
2025-11-07 11:09:20,976 - INFO - Audio file downloaded: voiceprint\audio\temp\2_20251107_110855_097bfa7b.mp3 (1.83 MB)
2025-11-07 11:09:20,976 - INFO - Starting audio analysis for speaker 2
2025-11-07 11:09:20,976 - INFO - Starting analysis for: voiceprint\audio\temp\2_20251107_110855_097bfa7b.mp3
2025-11-07 11:09:41,418 - INFO - Video analysis completed successfully
2025-11-07 11:09:41,441 - INFO - Storing video metrics for speaker 2
2025-11-07 11:09:41,462 - INFO - Video metrics stored successfully for speaker 2
2025-11-07 11:09:41,469 - INFO - Deleted temporary file: voiceprint\video\temp\2_20251107_110855_8b363a08.mp4 (size: 1473140 bytes)
2025-11-07 11:09:41,472 - INFO - Deleted temporary file: voiceprint\video\temp\2_20251107_110855_8b363a08_extracted_audio.wav (size: 3496326 bytes)
2025-11-07 11:09:41,475 - INFO - Video processing completed successfully for speaker 2
2025-11-07 11:10:10,414 - INFO - Storing audio metrics for speaker 2
2025-11-07 11:10:10,430 - INFO - Audio metrics stored successfully for speaker 2
2025-11-07 11:10:10,445 - INFO - Deleted temporary file: voiceprint\audio\temp\2_20251107_110855_097bfa7b.mp3 (size: 1920940 bytes)
2025-11-07 11:10:10,445 - INFO - Audio processing completed successfully for speaker 2
2025-11-07 11:10:10,447 - INFO - Generating tone profile for speaker 2
2025-11-07 11:10:10,462 - INFO - Generating tone profile for speaker 2 using LLM
2025-11-07 11:10:14,439 - INFO - LLM response received for speaker 2, length: 359 characters
2025-11-07 11:10:14,486 - INFO - Tone profile generated successfully for speaker 2
2025-11-07 11:10:14,529 - INFO - Voiceprint tone profile stored successfully for speaker 2
2025-11-07 11:10:14,529 - INFO - Tone profile generated and stored successfully for speaker 2
2025-11-07 11:10:14,529 - INFO - Combined voiceprint processing completed for speaker 2
2025-11-07 11:12:45,172 - INFO - Processing combined voiceprint for speaker 2
2025-11-07 11:12:45,253 - INFO - Downloading audio file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/audiotesting2.mp3
2025-11-07 11:12:45,253 - INFO - Downloading text file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/dsa-pdf/speaker_10_opportunity_43_report_20251105_120244.pdf
2025-11-07 11:12:45,253 - INFO - Downloading video file from S3: https://speaker-bot-dev.s3.us-east-2.amazonaws.com/speakers/videotesting1.mp4
2025-11-07 11:12:47,816 - INFO - Text file downloaded: voiceprint\text\temp\2_20251107_111245_abd13c2a.pdf (0.02 MB)
2025-11-07 11:12:47,817 - INFO - Starting text analysis for speaker 2
2025-11-07 11:12:48,252 - INFO - Extracted 16818 characters from PDF
2025-11-07 11:12:48,258 - INFO - Normalized PDF text: 16810 characters
2025-11-07 11:12:50,330 - INFO - Video file downloaded: voiceprint\video\temp\2_20251107_111245_52a85151.mp4 (1.40 MB)
2025-11-07 11:12:50,330 - INFO - Starting video analysis for speaker 2
2025-11-07 11:12:50,331 - INFO - Starting video analysis for: voiceprint\video\temp\2_20251107_111245_52a85151.mp4
2025-11-07 11:12:50,751 - INFO - Extracting audio from video (duration: 19.82s)
2025-11-07 11:12:50,752 - INFO - Writing extracted audio to: voiceprint\video\temp\2_20251107_111245_52a85151_extracted_audio.wav
2025-11-07 11:12:51,056 - INFO - Audio extracted successfully: voiceprint\video\temp\2_20251107_111245_52a85151_extracted_audio.wav (3496326 bytes)
2025-11-07 11:12:51,056 - INFO - Analyzing extracted audio for prosody metrics
2025-11-07 11:12:51,057 - INFO - Starting analysis for: voiceprint\video\temp\2_20251107_111245_52a85151_extracted_audio.wav
2025-11-07 11:12:51,813 - INFO - Audio file downloaded: voiceprint\audio\temp\2_20251107_111245_1d02ddfe.mp3 (1.83 MB)
2025-11-07 11:12:51,814 - INFO - Starting audio analysis for speaker 2
2025-11-07 11:12:51,815 - INFO - Starting analysis for: voiceprint\audio\temp\2_20251107_111245_1d02ddfe.mp3
2025-11-07 11:12:52,097 - INFO - OpenAI API call successful, response length: 480 characters
2025-11-07 11:12:52,097 - INFO - Storing text metrics for speaker 2
2025-11-07 11:12:52,114 - INFO - Text metrics stored successfully for speaker 2
2025-11-07 11:12:52,115 - INFO - Deleted temporary file: voiceprint\text\temp\2_20251107_111245_abd13c2a.pdf (size: 17949 bytes)
2025-11-07 11:12:52,117 - INFO - Text processing completed successfully for speaker 2
2025-11-07 11:13:05,614 - INFO - Video analysis completed successfully
2025-11-07 11:13:05,643 - INFO - Storing video metrics for speaker 2
2025-11-07 11:13:05,660 - INFO - Video metrics stored successfully for speaker 2
2025-11-07 11:13:05,662 - INFO - Deleted temporary file: voiceprint\video\temp\2_20251107_111245_52a85151.mp4 (size: 1473140 bytes)
2025-11-07 11:13:05,666 - INFO - Deleted temporary file: voiceprint\video\temp\2_20251107_111245_52a85151_extracted_audio.wav (size: 3496326 bytes)
2025-11-07 11:13:05,667 - INFO - Video processing completed successfully for speaker 2
2025-11-07 11:13:35,426 - INFO - Storing audio metrics for speaker 2
2025-11-07 11:13:35,430 - INFO - Audio metrics stored successfully for speaker 2
2025-11-07 11:13:35,431 - INFO - Deleted temporary file: voiceprint\audio\temp\2_20251107_111245_1d02ddfe.mp3 (size: 1920940 bytes)
2025-11-07 11:13:35,431 - INFO - Audio processing completed successfully for speaker 2
2025-11-07 11:13:35,432 - INFO - Generating tone profile for speaker 2
2025-11-07 11:13:35,441 - INFO - Generating tone profile for speaker 2 using LLM
2025-11-07 11:13:37,856 - INFO - LLM response received for speaker 2, length: 359 characters
2025-11-07 11:13:37,857 - INFO - Tone profile generated successfully for speaker 2
2025-11-07 11:13:37,867 - INFO - Voiceprint tone profile stored successfully for speaker 2
2025-11-07 11:13:37,868 - INFO - Tone profile generated and stored successfully for speaker 2
2025-11-07 11:13:37,868 - INFO - Combined voiceprint processing completed for speaker 2
