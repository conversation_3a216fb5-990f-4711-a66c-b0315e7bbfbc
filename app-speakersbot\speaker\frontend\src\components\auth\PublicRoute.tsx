import { ReactNode, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Navigate } from "react-router-dom";
import type { RootState } from "@/store";
import { setAuthenticated } from "@/store/slices/authSlice";

interface PublicRouteProps {
  children: ReactNode;
}

export default function PublicRoute({ children }: PublicRouteProps) {
  const dispatch = useDispatch();
  const { isAuthenticated, token } = useSelector((state: RootState) => state.auth);

  useEffect(() => {
    if (!isAuthenticated && (token || localStorage.getItem("token"))) {
      dispatch(setAuthenticated(true));
    }
  }, [dispatch, isAuthenticated, token]);

  if (isAuthenticated || localStorage.getItem("token")) {
    return <Navigate to="/speaker/dashboard" replace />;
  }

  return <>{children}</>;
}


