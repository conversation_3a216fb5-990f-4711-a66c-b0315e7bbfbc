const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");

const subscriptionService = require("../services/subscription-service");
const ApiResponse = require("../helpers/api-response");
const { exportAsCSV } = require("../helpers/csv-helper");

/**
 * Get subscriptions with filters and pagination
 */
exports.getSubscriptions = async (req, res, next) => {
    try {
        const { status, data, message } = await subscriptionService.getSubscriptions(req.query);
        
        if (status) {
             res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ status, data, message }));
        } else {
            res.status(RESPONSE_CODES.NOT_FOUND).send({ status: false, data: [], message: message || RESPONSE_MESSAGES.NOT_FOUND });
        }

    } catch (error) {
        console.error("Error fetching subscriptions:", error);
        next(error);
    }
}

exports.exportSubscriptions = async (req, res, next) => {
    try {
        const result = await subscriptionService.exportSubscriptions(req);

        if (!result || !result.status) {
            return res.status(RESPONSE_CODES.NOT_FOUND).json({ status: false, data: [], message: result?.message || 'No subscriptions found' });
        }

        const { data, fileName } = result;
        return exportAsCSV(res, data, fileName || 'subscriptions.csv');

    } catch (error) {
        console.error("Error exporting subscriptions:", error);
        next(error);
    }
}