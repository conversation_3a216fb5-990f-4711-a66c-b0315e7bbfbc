const { Parser } = require("json2csv");
const csv = require("csv-parser");
const stream = require("stream");

/**
 * Export data as CSV and send as a response
 * @param {Object} res - Express response object
 * @param {Array} data - Data array to convert to CSV
 * @param {String} fileName - File name for download
 * @param {Array} fields - Optional: Specific fields to include in CSV
 */
const exportAsCSV = (res, data, fileName = "export.csv", fields = null) => {
  if (!data || data.length === 0) {
    return res.status(404).json({ message: "No data found" });
  }

  try {
    const json2csvParser = new Parser({ fields });
    const csv = json2csvParser.parse(data);

    res.setHeader("Content-Type", "text/csv");
    res.setHeader("Content-Disposition", `attachment; filename=${fileName}`);

    return res.status(200).send(csv);
  } catch (error) {
    console.error("Error exporting CSV:", error); 
    return res.status(500).json({ message: "Error exporting data" });
  }
};

/**
 * Parse CSV from memory buffer and return data as array of objects
 * @param {Buffer} buffer - CSV file buffer
 * @param {Object} headerMap - Mapping of CSV headers to model fields
 * @param {Function} convertValue - Function to convert values to correct types
 * @returns {Promise<Array>} Parsed CSV data
 */
const parseCSVFromBuffer = (buffer, headerMap, convertValue) => {
  return new Promise((resolve, reject) => {
    const results = [];
    const bufferStream = new stream.PassThrough();
    bufferStream.end(buffer);

    bufferStream
      .pipe(csv())
      .on('data', (row) => {
        const mapped = {};
        for (const [csvKey, modelKey] of Object.entries(headerMap)) {
          if (row.hasOwnProperty(csvKey)) {
            mapped[modelKey] = convertValue ? convertValue(modelKey, row[csvKey]) : row[csvKey];
          }
        }
        results.push(mapped);
      })
      .on('end', () => {
        resolve(results);
      })
      .on('error', (error) => {
        reject(error);
      });
  });
};

module.exports = { exportAsCSV, parseCSVFromBuffer };