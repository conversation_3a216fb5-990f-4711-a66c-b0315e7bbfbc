export const getLightThemeConfig = () => ({
  algorithm: [],
  token: {
    // Light Theme Color Scheme
    colorPrimary: '#3B82F6',              // Blue primary
    colorSuccess: '#10B981',             // Green
    colorWarning: '#F59E0B',             // Amber  
    colorError: '#EF4444',               // Red
    colorInfo: '#3B82F6',                // Blue
    
    // Background colors
    colorBgBase: '#FFFFFF',              // White background
    colorBgContainer: '#F9FAFB',         // Light gray container
    colorBgElevated: '#FFFFFF',          // White elevated surfaces
    colorBgLayout: '#F3F4F6',            // Layout background
    colorBgSpotlight: '#F3F4F6',         // Spotlight/hover backgrounds
    
    // Border colors
    colorBorder: '#E5E7EB',              // Default borders
    colorBorderSecondary: '#F3F4F6',     // Secondary borders
    
    // Text colors
    colorText: '#111827',                // Primary text (dark)
    colorTextSecondary: '#6B7280',       // Secondary text
    colorTextTertiary: '#9CA3AF',        // Tertiary text
    colorTextQuaternary: '#D1D5DB',      // Quaternary text
    
    // Component specific
    colorFillAlter: '#F9FAFB',           // Alternate fill
    colorFillContent: '#F3F4F6',         // Content fill
    colorFillContentHover: '#E5E7EB',    // Content fill on hover
    colorFillSecondary: '#F9FAFB',       // Secondary fill
    
    // Sizing
    borderRadius: 6,
    borderRadiusLG: 8,
    borderRadiusSM: 4,
    
    // Typography
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fontSize: 14,
    fontSizeHeading1: 32,
    fontSizeHeading2: 24,
    fontSizeHeading3: 20,
    fontSizeHeading4: 18,
    fontSizeHeading5: 16,
    
    // Spacing
    padding: 16,
    paddingLG: 24,
    paddingSM: 12,
    paddingXS: 8,
    
    // Shadows
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)',
    boxShadowSecondary: '0 4px 16px rgba(0, 0, 0, 0.08)',
    
    // Control components
    controlHeight: 32,
    controlHeightLG: 40,
    controlHeightSM: 24,
  },
  components: {
    Layout: {
      bodyBg: '#FFFFFF',
      headerBg: '#F9FAFB',
      footerBg: '#F9FAFB',
      siderBg: '#F9FAFB',
      triggerBg: '#F3F4F6',
      triggerColor: '#374151',
    },
    Menu: {
      itemBg: '#F9FAFB',
      subMenuItemBg: '#F9FAFB',
      itemSelectedBg: '#1890ff',
      itemHoverBg: '#E6F4FF',
      itemColor: '#6B7280',
      itemSelectedColor: '#FFFFFF',
      itemHoverColor: '#1890ff',
      itemActiveBg: '#1890ff',
    },
    Button: {
      colorPrimary: '#3B82F6',
      colorPrimaryHover: '#2563EB',
      colorPrimaryActive: '#1D4ED8',
      primaryShadow: '0 2px 4px rgba(59, 130, 246, 0.2)',
    },
    Table: {
      headerBg: '#F9FAFB',
      headerColor: '#374151',
      bodySortBg: '#F3F4F6',
      rowHoverBg: '#F9FAFB',
      borderColor: '#E5E7EB',
    },
    Card: {
      colorBgContainer: '#FFFFFF',
      colorBorderSecondary: '#E5E7EB',
    },
    Input: {
      colorBgContainer: '#FFFFFF',
      colorBorder: '#D1D5DB',
      colorText: '#111827',
      colorTextPlaceholder: '#9CA3AF',
      activeBorderColor: '#3B82F6',
    },
    Select: {
      colorBgContainer: '#FFFFFF',
      colorBgElevated: '#FFFFFF',
      colorBorder: '#D1D5DB',
      colorText: '#111827',
      optionSelectedBg: '#EBF8FF',
    },
    DatePicker: {
      colorBgContainer: '#FFFFFF',
      colorBgElevated: '#FFFFFF',
      colorBorder: '#D1D5DB',
      colorText: '#111827',
    },
    Modal: {
      contentBg: '#FFFFFF',
      headerBg: '#FFFFFF',
      footerBg: '#FFFFFF',
    },
    Drawer: {
      colorBgElevated: '#FFFFFF',
    },
    Dropdown: {
      colorBgElevated: '#FFFFFF',
    },
    Badge: {
      colorError: '#EF4444',
      colorSuccess: '#10B981',
    },
    Progress: {
      defaultColor: '#3B82F6',
    },
    Tag: {
      defaultBg: '#F3F4F6',
      defaultColor: '#374151',
    },
    Tooltip: {
      colorBgSpotlight: '#374151',
      colorTextLightSolid: '#FFFFFF',
    },
    Popover: {
      colorBgElevated: '#FFFFFF',
    }
  },
});

export const getDarkThemeConfig = () => ({
  algorithm: [],
  token: {
    // Dark Blue Theme - matching index.css
    colorPrimary: '#009BC9',              // Dashboard dark blue
    colorSuccess: '#39C9E3',              // Dashboard medium blue
    colorWarning: '#A3DCED',              // Dashboard light blue  
    colorError: '#ff4d4f',                // Red for errors
    colorInfo: '#39C9E3',                 // Dashboard medium blue
    
    // Background colors - matching index.css dark theme
    colorBgBase: '#00070E',               // Very dark blue background
    colorBgContainer: '#00152A',          // Dark blue for cards
    colorBgElevated: '#00152A',           // Dark blue elevated surfaces
    colorBgLayout: '#00070E',             // Layout background
    colorBgSpotlight: '#002445',          // Darker blue for hover
    
    // Border colors
    colorBorder: '#003261',               // Medium blue for borders
    colorBorderSecondary: '#002445',      // Darker blue secondary borders
    
    // Text colors - using white and light blues
    colorText: '#FFFFFF',                 // Pure white primary text
    colorTextSecondary: '#A3DCED',        // Dashboard light blue secondary text
    colorTextTertiary: '#39C9E3',         // Dashboard medium blue tertiary text
    colorTextQuaternary: '#009BC9',       // Dashboard dark blue quaternary text
    
    // Component specific
    colorFillAlter: '#00152A',            // Dark blue alternate fill
    colorFillContent: '#002445',          // Darker blue content fill
    colorFillContentHover: '#003261',     // Medium blue content fill on hover
    colorFillSecondary: '#00152A',        // Dark blue secondary fill
    
    // Sizing
    borderRadius: 6,
    borderRadiusLG: 8,
    borderRadiusSM: 4,
    
    // Typography
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    fontSize: 14,
    fontSizeHeading1: 32,
    fontSizeHeading2: 24,
    fontSizeHeading3: 20,
    fontSizeHeading4: 18,
    fontSizeHeading5: 16,
    
    // Spacing
    padding: 16,
    paddingLG: 24,
    paddingSM: 12,
    paddingXS: 8,
    
    // Shadows with blue glow
    boxShadow: '0 4px 20px rgba(0, 155, 201, 0.3)',
    boxShadowSecondary: '0 8px 24px rgba(0, 155, 201, 0.15)',
    
    // Control components
    controlHeight: 32,
    controlHeightLG: 40,
    controlHeightSM: 24,
  },
  components: {
    Layout: {
      bodyBg: '#00070E',
      headerBg: '#00152A',
      footerBg: '#00152A',
      siderBg: '#00070E',
      triggerBg: '#002445',
      triggerColor: '#FFFFFF',
    },
    Menu: {
      darkItemBg: '#00070E',
      darkSubMenuItemBg: '#00152A',
      darkItemSelectedBg: '#009BC9',
      darkItemHoverBg: '#002445',
      darkItemColor: '#A3DCED',
      darkItemSelectedColor: '#FFFFFF',
      darkItemHoverColor: '#FFFFFF',
      itemActiveBg: '#009BC9',
    },
    Button: {
      colorPrimary: '#009BC9',
      colorPrimaryHover: '#39C9E3',
      colorPrimaryActive: '#009BC9',
      primaryShadow: '0 2px 8px rgba(0, 155, 201, 0.3)',
    },
    Table: {
      headerBg: '#00152A',
      headerColor: '#FFFFFF',
      bodySortBg: '#002445',
      rowHoverBg: '#002445',
      borderColor: '#003261',
    },
    Card: {
      colorBgContainer: '#00152A',
      colorBorderSecondary: '#003261',
    },
    Input: {
      colorBgContainer: '#00152A',
      colorBorder: '#003261',
      colorText: '#FFFFFF',
      colorTextPlaceholder: '#A3DCED',
      activeBorderColor: '#009BC9',
    },
    Select: {
      colorBgContainer: '#00152A',
      colorBgElevated: '#00152A',
      colorBorder: '#003261',
      colorText: '#FFFFFF',
      optionSelectedBg: '#002445',
    },
    DatePicker: {
      colorBgContainer: '#00152A',
      colorBgElevated: '#00152A',
      colorBorder: '#003261',
      colorText: '#FFFFFF',
    },
    Modal: {
      contentBg: '#00152A',
      headerBg: '#00152A',
      footerBg: '#00152A',
    },
    Drawer: {
      colorBgElevated: '#00152A',
    },
    Dropdown: {
      colorBgElevated: '#00152A',
    },
    Badge: {
      colorError: '#ff4d4f',
      colorSuccess: '#39C9E3',
    },
    Progress: {
      defaultColor: '#009BC9',
    },
    Tag: {
      defaultBg: '#002445',
      defaultColor: '#FFFFFF',
    },
    Tooltip: {
      colorBgSpotlight: '#002445',
      colorTextLightSolid: '#FFFFFF',
    },
    Popover: {
      colorBgElevated: '#00152A',
    }
  },
});