
const { RESPONSE_CODES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");
const jwtHelper = require("../helpers/jwt-helper");
const { Users, Speakers, Roles } = require("../models");
const bcrypt = require("bcrypt");
const emailService = require("./email-service");
const CONFIG = require("../config/config");
const jwt = require("jsonwebtoken");

const authService = {};

// ------------------------- auth-service -------------------------

/**
 * Authenticate user and generate JWT token.
 * Handles login for both Users and Speakers tables.
 * Validates email and password, then generates JWT token.
 * 
 * @param {Object} loginReq - The request object containing login credentials
 * @param {string} loginReq.body.email - User email address
 * @param {string} loginReq.body.password - User password
 * @returns {Promise<Object>} Result of login attempt with JWT token
 * @throws {CustomError} When email/password missing, user not found, or invalid credentials
 */
authService.login = async (loginReq) => {
    try {

        const { email, password } = loginReq.body;

        if (!email || !password) {

            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email and password are required");
        }

        // Try finding user in Users table
        let user = await Users.findOne({ where: { email }, raw: true });

        if (!user) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "User not found");
        }

        if (user.is_active == false) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "User is not active");
        }


        // Validate password
        const isPasswordValid = await bcrypt.compare(password, user.password);
        if (!isPasswordValid) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Invalid password");
        }

        // Generate JWT token
        const token = jwtHelper.generateToken({ id: user.id, role_id: user.role_id });

        return {
            status: true,
            message: "Login successful",
            data: { token }
        };
    } catch (error) {
        console.error("Error during login:", error);
        throw error;
    }
};

/**
 * Send forgot password email with JWT reset token
 * @param {Object} forgotReq - Request object containing email
 * @returns {Promise<Object>} Result of forgot password request
 */
authService.forgotPassword = async (email) => {
    try {

        let roleId = null;
        let userType = null;

        // Find user in Users table first
        let user = await Users.findOne({ where: { email }, include: [{ model: Roles, as: 'role' }] });
        if (user) {
            roleId = user.role_id;
            userType = user.role.name || 'user';
        }

        // If not found in Users, check Speakers table
        if (!user) {
            user = await Speakers.findOne({ where: { email } });
            if (user) {
                roleId = 0;
                userType = 'speaker';
            }
        }

        if (!user) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email not found or exists");
        }

        return {
            id: user.id,
            name: user.name || 'User',
            roleId,
            userType,
        };

    } catch (error) {
        console.error("Error in forgotPassword:", error);
        throw error;
    }
};

/**
 * Reset password using JWT token
 * @param {Object} resetReq - Request object containing token and new password
 * @returns {Promise<Object>} Result of password reset
 */
authService.resetPassword = async (token, newPassword) => {
    try {
        // Verify JWT token
        let decoded;
        try {
            decoded = jwt.verify(token, CONFIG.JWT_SECRET);
        } catch (jwtError) {
            if (jwtError instanceof jwt.TokenExpiredError) {
                throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Token expired");
            }
            if (jwtError instanceof jwt.JsonWebTokenError) {
                throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Invalid token");
            }
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Invalid or expired reset token");
        }

        // Check if token is for password reset
        if (decoded.type !== 'password_reset') {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Invalid token type");
        }

        const { userId, role, roleId } = decoded;

        // Update password based on user type
        if (role !== 'speaker') {
            const user = await Users.findByPk(userId);
            if (!user) {
                throw new CustomError(RESPONSE_CODES.NOT_FOUND, "User not found");
            }
            if (user.password_changed_at && user.password_changed_at > new Date(decoded.iat * 1000)) {
                throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Password already changed");
            }
            const isSamePassword = await bcrypt.compare(newPassword, user.password);
            if (isSamePassword) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "New password cannot be the same as the old password");
            }
            user.password = await bcrypt.hash(newPassword, 10);
            user.password_changed_at = new Date();
            await user.save();
        } else if (role === 'speaker') {
            const speaker = await Speakers.findByPk(userId);
            if (!speaker) {
                throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Speaker not found");
            }
            if (speaker.password_changed_at && speaker.password_changed_at > new Date(decoded.iat * 1000)) {
                throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Password already changed");
            }
            const isSamePassword = await bcrypt.compare(newPassword, speaker.password);
            if (isSamePassword) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "New password cannot be the same as the old password");
            }
            speaker.password = await bcrypt.hash(newPassword, 10);
            speaker.password_changed_at = new Date();
            await speaker.save();
        } else {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid user role");
        }
        return true;
    } catch (error) {
        console.error("Error in resetPassword:", error);
        throw error;
    }
};

/**
 * Change password for authenticated user
 * @param {Object} changeReq - Request object containing current and new password
 * @returns {Promise<Object>} Result of password change
 */
authService.changePassword = async (changeReq) => {
    try {
        const { currentPassword, newPassword, userId, userType } = changeReq.body;

        if (!currentPassword || !newPassword || !userId || !userType) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "All fields are required");
        }

        // Find user
        let user;
        if (userType === 'user') {
            user = await Users.findByPk(userId);
        } else if (userType === 'speaker') {
            user = await Speakers.findByPk(userId);
        } else {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid user type");
        }

        if (!user) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "User not found");
        }

        // Verify current password
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, user.password);
        if (!isCurrentPasswordValid) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Current password is incorrect");
        }

        const isSamePassword = await bcrypt.compare(newPassword, user.password);
        if (isSamePassword) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "New password cannot be the same as the old password");
        }
        // Update password
        user.password = await bcrypt.hash(newPassword, 10);
        user.password_changed_at = new Date();
        await user.save();

        return {
            status: true,
            message: "Password changed successfully"
        };

    } catch (error) {
        console.error("Error in changePassword:", error);
        throw error;
    }
};

module.exports = authService;