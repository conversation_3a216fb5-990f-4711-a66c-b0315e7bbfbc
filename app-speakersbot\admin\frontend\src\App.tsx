import React, { useEffect } from 'react';
import { BrowserRouter, Navigate, Route, Routes } from "react-router-dom";
import ProtectedRoute from './components/ProtectedRoute';
import SmartRedirect from './components/SmartRedirect';
import { Toaster } from './components/ui/toaster';
import { AppStateProvider, useAppState } from './state/AppStateProvider';
import { AuthProvider, useAuth } from './state/AuthContext';
import { getDarkThemeConfig, getLightThemeConfig } from './utils/theme-config';
// import Login from './pages/Login';
import AnalyticsDashboard from './pages/AnalyticsDashboard';
import Opportunities from './pages/opportunity/Opportunities';
import OpportunityDetail from './pages/opportunity/OpportunityDetail';
import SpeakerDetail from './pages/speakerDetail/SpeakerDetail';
import Speakers from './pages/speakerManagement';
import SpeakerOnboarding from './pages/SpeakerOnboarding';

import Affiliates from './pages/Affilates';
import AffiliateDetail from './pages/AffilatesDetail';
import ForgotPassword from './pages/auth/ForgotPassword';
import Login from './pages/auth/Login';
import ResetPassword from './pages/auth/ResetPassword';
import IntakeForm from './pages/intake-form/IntakeForm';
import Users from './pages/manage-users/Users';
import Matching from './pages/matching-queue/Matching';
import NotFound from "./pages/NotFound";
import Pricing from './pages/Pricing';
import Scraping from './pages/Scraping';
import Settings from './pages/Settings';

import LandingPageBuilder from './pages/landingPageBuilder';

const PublicRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { isAuthenticated } = useAuth();
  return isAuthenticated ? <Navigate to="/admin/dashboard" replace /> : <>{children}</>;
};

const ThemedApp: React.FC = () => {
  const { ui } = useAppState();

  // Apply theme class to document
  useEffect(() => {
    if (ui.theme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [ui.theme]);

  const themeConfig = ui.theme === 'dark' ? getDarkThemeConfig() : getLightThemeConfig();

  return (
    // <ConfigProvider theme={themeConfig}>
      <BrowserRouter>
        <Routes>
          <Route path="/admin/login" element={
            <PublicRoute>
              <Login />
            </PublicRoute>
          } />
          <Route path="/admin/dashboard" element={
            <ProtectedRoute requiredPermissions={['read_dashboard']}>
              <AnalyticsDashboard />
            </ProtectedRoute>
          } />
          <Route path="/admin/analytics" element={
            <ProtectedRoute requiredPermissions={['read_dashboard']}>
              <AnalyticsDashboard />
            </ProtectedRoute>
          } />
          <Route path="/admin/opportunities" element={
            <ProtectedRoute requiredPermissions={['read_opportunities']}>
              <Opportunities />
            </ProtectedRoute>
          } />
          <Route path="/admin/speakers" element={
            <ProtectedRoute requiredPermissions={['read_speakers']}>
              <Speakers />
            </ProtectedRoute>
          } />
          <Route path="/admin/speaker-onboarding" element={
            <ProtectedRoute requiredPermissions={['write_speakers']}>
              <SpeakerOnboarding />
            </ProtectedRoute>
          } />
          <Route path="/admin/matching" element={
            <ProtectedRoute requiredPermissions={['read_matching_queue']}>
              <Matching />
            </ProtectedRoute>
          } />
          <Route path="/admin/opportunities/:id" element={
            <ProtectedRoute requiredPermissions={['read_opportunities']}>
              <OpportunityDetail />
            </ProtectedRoute>
          } />
          <Route path="/admin/speakers/:id" element={
            <ProtectedRoute requiredPermissions={['read_speakers']}>
              <SpeakerDetail />
            </ProtectedRoute>
          } />
          <Route path="/admin/users" element={
            <ProtectedRoute requiredPermissions={['read_users']}>
              <Users />
            </ProtectedRoute>
          } />
          <Route path="/admin/STAiGENT-identity-profile" element={
            <ProtectedRoute requiredPermissions={['read_form_types']}>
              <IntakeForm />
            </ProtectedRoute>
          } />
          <Route path="/admin/scraping" element={
            <ProtectedRoute requiredPermissions={['read_scraping_logging']}>
              <Scraping />
            </ProtectedRoute>
          } />
          <Route path="/admin/settings" element={
            <ProtectedRoute requiredPermissions={['write_settings']}>
              <Settings />
            </ProtectedRoute>
          } />
          <Route path="/admin/pricing" element={
            <ProtectedRoute requiredPermissions={['read_pricing_plans']}>
              <Pricing />
            </ProtectedRoute>
          } />
          <Route path="/admin/affiliates" element={
            <ProtectedRoute requiredPermissions={['read_affiliates', 'read_affiliate_users_details']} requireAll={false}>
              <Affiliates />
            </ProtectedRoute>
          } />
          <Route path="/admin/affiliates/:id" element={
            <ProtectedRoute requiredPermissions={['read_affiliates', 'read_affiliate_users_details']} requireAll={false}>
              <AffiliateDetail />
            </ProtectedRoute>
          } />
          <Route path="/admin/affiliates/:id/landing-page" element={
            <ProtectedRoute>
              <LandingPageBuilder />
            </ProtectedRoute>
          } />
          <Route path="/admin/gamification" element={<Navigate to="/admin/settings" replace />} />
          <Route path="/admin/forgot-password" element={
            <PublicRoute>
              <ForgotPassword />
            </PublicRoute>
          } />
          <Route path="/admin/reset-password" element={
            <PublicRoute>
              <ResetPassword />
            </PublicRoute>
          } />
          <Route path="/admin" element={<SmartRedirect />} />
          <Route path="/" element={<Navigate to="/admin" replace />} />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </BrowserRouter>
    // </ConfigProvider>
  );
};

const App = () => (
  <AuthProvider>
    <AppStateProvider>
      <ThemedApp />
      <Toaster />
    </AppStateProvider>
  </AuthProvider>
);

export default App;
