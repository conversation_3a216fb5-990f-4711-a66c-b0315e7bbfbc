
const { DataTypes } = require("sequelize");
const connection = require("../connection");


const Streaks = connection.define("Streaks", {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: 'Primary key for the subscription record',
    },
    speaker_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Foreign key referencing the speaker',
    },
    count:{
        type: DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        comment: 'Login streak count',
    }, 
    last_login:{
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Timestamp of the last login',
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: 'Record creation timestamp',
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: 'Record last update timestamp',
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    }
}, {
    tableName: 'streaks',
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at"

});

module.exports = Streaks;