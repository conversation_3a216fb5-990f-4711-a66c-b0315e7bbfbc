import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import {
  CrownOutlined,
  DownloadOutlined,
  FilterOutlined,
  StarOutlined,
  ThunderboltOutlined,
} from "@ant-design/icons";
import { RotateCcw, Search } from "lucide-react";
import React, { useEffect, useState } from "react";
import DatePicker from "react-multi-date-picker";
import {
  useGetPlansQuery,
  useGetSubscriptionsQuery,
  useLazyExportSubscriptionsQuery,
} from "../apis/pricingPlanApi";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "../components/ui/alert-dialog";
import { Badge } from "../components/ui/badge";
import { But<PERSON> } from "../components/ui/button";
import {
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>it<PERSON>,
} from "../components/ui/card";
import { Input } from "../components/ui/input";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "../components/ui/pagination";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../components/ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../components/ui/table";
import { formatCurrency } from "../utils/helper";
import { toast } from "../hooks/use-toast";
import { InvalidTokenHandler } from "../components/common/InvalidTokenHandler";
const Pricing: React.FC = () => {
  // Filter states
  const [logSearchText, setLogSearchText] = useState("");
  const [debouncedLogSearch, setDebouncedLogSearch] = useState("");
  const [logStatusFilter, setLogStatusFilter] = useState<string[]>([]);
  const [logDateRange, setLogDateRange] = useState<any>(null);

  // Pagination states for financial logs
  const [logPage, setLogPage] = useState(1);
  const [logPageSize, setLogPageSize] = useState(10);

  // State for expanded features per plan
  const [expandedPlans, setExpandedPlans] = useState<Set<number>>(new Set());

  // API hooks with parameters
  const {
    data: plansData,
    isLoading: plansLoading,
    error: plansError,
  } = useGetPlansQuery(undefined, {
    refetchOnMountOrArgChange: true,
    refetchOnReconnect: true,
  });
  const {
    data: subscriptionsData,
    isLoading: subscriptionsLoading,
    isFetching: subscriptionsFetching,
    error: subscriptionsError,
  } = useGetSubscriptionsQuery(
    {
      search: debouncedLogSearch || undefined,
      status: logStatusFilter.length > 0 ? logStatusFilter[0] : undefined,
      startDate:
        logDateRange && logDateRange.length === 2
          ? logDateRange[0]?.format("YYYY-MM-DD")
          : undefined,
      endDate:
        logDateRange && logDateRange.length === 2
          ? logDateRange[1]?.format("YYYY-MM-DD")
          : undefined,
      page: logPage,
      limit: logPageSize,
    },
    { refetchOnMountOrArgChange: true, refetchOnReconnect: true }
  );
  const [exportSubscriptions, { isLoading: isExporting }] =
    useLazyExportSubscriptionsQuery();

  // Separate loading states
  const isInitialLoading = subscriptionsLoading; // Only for initial load
  const isTableLoading = subscriptionsLoading || subscriptionsFetching; // For table and pagination

  // Load filters from localStorage
  useEffect(() => {
    const savedFilters = localStorage.getItem("pricing_log_filters");
    if (savedFilters) {
      const filters = JSON.parse(savedFilters);
      setLogSearchText(filters.logSearchText || "");
      setLogStatusFilter(filters.logStatusFilter || []);
    }
  }, []);

  // Save filters to localStorage
  useEffect(() => {
    const filters = { logSearchText, logStatusFilter };
    localStorage.setItem("pricing_log_filters", JSON.stringify(filters));
  }, [logSearchText, logStatusFilter]);

  // Debounce search
  useEffect(() => {
    const id = setTimeout(() => setDebouncedLogSearch(logSearchText), 400);
    return () => clearTimeout(id);
  }, [logSearchText]);

  // Extract data from API responses
  const allPlans = plansData?.data || [];
  const subscriptions = (subscriptionsData?.data as any)?.subscriptions || [];

  // Separate registration fee from main pricing plans
  const registrationFee = allPlans.find(
    (plan) => plan.name === "registration_fee"
  );
  const pricingPlans = allPlans.filter(
    (plan) => plan.name !== "registration_fee"
  );

  // Add mock features to test expandable functionality
  const pricingPlansWithFeatures = pricingPlans.map((plan) => ({
    ...plan,
    features: (() => {
      try {
        // Try to parse as JSON first (in case it's a stringified array)
        if (typeof plan?.description === "string") {
          const parsed = JSON.parse(plan.description);
          return Array.isArray(parsed) ? parsed : [plan.description];
        }
        // If it's already an array, use it
        if (Array.isArray(plan?.description)) {
          return plan.description;
        }
        // If it's a string but not JSON, wrap it in an array
        if (plan?.description) {
          return [plan.description];
        }
        // Fallback to default features
        return [
          "Basic speaker profile",
          "Up to 3 opportunities per month",
          "Standard matching algorithm",
          "Email notifications",
          "Basic analytics dashboard",
          "Customer support",
        ];
      } catch (error) {
        // If JSON.parse fails, treat as a regular string
        return plan?.description
          ? [plan.description]
          : [
              "Basic speaker profile",
              "Up to 3 opportunities per month",
              "Standard matching algorithm",
              "Email notifications",
              "Basic analytics dashboard",
              "Customer support",
            ];
      }
    })(),
  }));

  // Process subscription data for table display
  const financialLogs = subscriptions.map((sub) => ({
    id: sub.id.toString(),
    date: sub.start_date
      ? new Date(sub.start_date).toISOString().split("T")[0]
      : new Date().toISOString().split("T")[0],
    type: "Subscription",
    plan: sub.plan_name,
    user: sub.speaker_name,
    amount: parseFloat(sub.amount) || 0,
    currency: sub.currency || "USD",
    status: sub.status,
  }));

  // Reset filters when component mounts
  useEffect(() => {
    handleResetLogFilters();
  }, []);

  // Reset filters
  const handleResetLogFilters = () => {
    setLogSearchText("");
    setLogStatusFilter([]);
    setLogDateRange(null);
  };

  // Pagination info from API response
  const logPagination = subscriptionsData?.data?.pageData || {
    total: 0,
    limit: logPageSize,
    page: logPage,
    totalPage: 1,
  };

  // Pagination functions
  const changeLogPage = (newPage: number) => {
    setLogPage(newPage);
  };

  const changeLogPageSize = (newSize: number) => {
    setLogPageSize(newSize);
    setLogPage(1);
  };

  // Reset pagination when filters change
  useEffect(() => {
    setLogPage(1);
  }, [debouncedLogSearch, logStatusFilter, logDateRange]);

  const handleExportLogs = async () => {
    try {
      // Build query parameters for the export
      const exportParams = {
        search: debouncedLogSearch || undefined,
        status: logStatusFilter.length > 0 ? logStatusFilter[0] : undefined,
        startDate:
          logDateRange && logDateRange.length === 2
            ? logDateRange[0]?.format("YYYY-MM-DD")
            : undefined,
        endDate:
          logDateRange && logDateRange.length === 2
            ? logDateRange[1]?.format("YYYY-MM-DD")
            : undefined,
      };

      // Call the export API using the mutation
      const blob = await exportSubscriptions(exportParams).unwrap();

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `financial-logs-${
        new Date().toISOString().split("T")[0]
      }.csv`;
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
      toast({ description: `Export started` });
    } catch (error) {
      toast({
        description: error?.data?.message || "Failed to export",
        variant: "destructive",
      });
      console.error("Export failed:", error);
      // You might want to show a toast notification here
    }
  };

  // No need for local filtering since API handles it
  const getFilteredLogs = () => financialLogs;

  const renderPlanName = (name: string) => (
    <div className="flex items-center gap-2">
      {name === "Enterprise" && (
        <CrownOutlined style={{ color: "hsl(var(--primary))" }} />
      )}
      {name === "Premium" && (
        <StarOutlined style={{ color: "hsl(var(--dashboard-medium-blue))" }} />
      )}
      {name === "Basic" && (
        <ThunderboltOutlined
          style={{ color: "hsl(var(--dashboard-light-blue))" }}
        />
      )}
      <strong>{name}</strong>
    </div>
  );

  const renderPrice = (
    amount: string,
    currency: string,
    billingInterval: string
  ) => {
    const planAmount = Number(amount);
    const registrationAmount = registrationFee
      ? Number(registrationFee.amount)
      : 0;
    const totalAmount = planAmount + registrationAmount;

    const planFormatted =
      formatCurrency(planAmount, currency) ?? `${currency} ${planAmount}`;
    const registrationFormatted =
      formatCurrency(registrationAmount, currency) ??
      `${currency} ${registrationAmount}`;
    const totalFormatted =
      formatCurrency(planAmount, currency) ?? `${currency} ${totalAmount}`;

    return (
      <div className="flex flex-col gap-0.5 text-base">
        <div className="flex items-center gap-1 flex-wrap">
          <span className="font-semibold text-lg">{totalFormatted}</span>
          <span className="text-foreground text-sm">
            / {billingInterval === "one-time" ? "one-time" : billingInterval}
          </span>
          {billingInterval == "month" && (
            <span className="text-xs text-muted-foreground">
              (Cancel anytime)
            </span>
          )}
        </div>
        <div className="text-sm">
          {billingInterval == "one-time" ? (
            <div>
              <p className="text-sm font-semibold">Includes Registration Fee</p>
            </div>
          ) : (
            registrationAmount > 0 && (
              <div>
                <span className="text-lg font-semibold text-foreground">
                  + {registrationFormatted}
                </span>{" "}
                <span className="text-sm text-muted-foreground">One Time</span>
              </div>
            )
          )}
          {billingInterval !== "one-time" && (
            <p className="text-sm font-semibold text-foreground">
              Registration Fee
            </p>
          )}
        </div>
      </div>
    );
  };

  const renderFeatures = (features: string[], planId: number) => {
    const isExpanded = expandedPlans.has(planId);
    const displayFeatures = isExpanded ? features : features.slice(0, 3);
    const remainingCount = features.length - 3;

    const toggleExpanded = () => {
      const newExpandedPlans = new Set(expandedPlans);
      if (isExpanded) {
        newExpandedPlans.delete(planId);
      } else {
        newExpandedPlans.add(planId);
      }
      setExpandedPlans(newExpandedPlans);
    };

    return (
      <div className="text-sm text-foreground max-w-xs">
        {displayFeatures.map((feature, index) => (
          <div key={index}>
            <div className="py-0.5">{feature}</div>
            {index < displayFeatures.length - 1 && (
              <div className="border-t border-blue-500/20 my-1"></div>
            )}
          </div>
        ))}
        {!isExpanded && remainingCount > 0 && (
          <div>
            <div className="border-t border-blue-500/20 my-1"></div>
            <button
              onClick={toggleExpanded}
              className="text-foreground hover:text-blue-300 cursor-pointer py-0.5"
            >
              + {remainingCount} more features
            </button>
          </div>
        )}
        {isExpanded && remainingCount > 0 && (
          <div>
            <div className="border-t border-blue-500/20 my-1"></div>
            <button
              onClick={toggleExpanded}
              className="text-foreground hover:text-blue-300 cursor-pointer py-0.5"
            >
              Show less
            </button>
          </div>
        )}
      </div>
    );
  };

  const renderStatus = (isActive: boolean) => (
    <Badge
      variant={isActive ? "default" : "destructive"}
      className={cn(
        "px-2 py-1 rounded-md text-xs font-medium",
        isActive
          ? "bg-blue-500/15 text-blue-300 border border-blue-500/30"
          : "bg-destructive/15 text-destructive border border-destructive/30"
      )}
    >
      {isActive ? "Active" : "Inactive"}
    </Badge>
  );

  const renderLogDate = (date: string) => new Date(date).toLocaleDateString();

  const renderLogAmount = (amount: any, currency: string) => {
    const numeric = Number(amount);
    const formatted =
      formatCurrency(Math.abs(numeric), currency) ??
      `${currency} ${Math.abs(numeric)}`;
    return (
      <span
        className={cn(
          "font-medium",
          numeric < 0 ? "text-destructive" : "text-blue-600"
        )}
      >
        {formatted}
      </span>
    );
  };

  const renderLogStatus = (status: string) => {
    const getStatusVariant = (status: string) => {
      switch (status) {
        case "Completed":
          return "default";
        case "Processed":
          return "secondary";
        case "Pending":
          return "outline";
        default:
          return "outline";
      }
    };

    const getStatusClassName = (status: string) => {
      switch (status) {
        case "Completed":
          return "bg-blue-500/15 text-blue-300 border border-blue-500/30";
        case "Processed":
          return "bg-blue-600/15 text-blue-400 border border-blue-600/30";
        case "Pending":
          return "bg-muted text-muted-foreground border border-border";
        default:
          return "bg-muted text-foreground border border-border";
      }
    };

    return (
      <Badge
        variant={getStatusVariant(status) as any}
        className={cn(
          "px-2 py-1 rounded-md text-xs font-medium",
          getStatusClassName(status)
        )}
      >
        {status}
      </Badge>
    );
  };

  const totalRevenue = getFilteredLogs()
    .filter((log) => log.amount > 0)
    .reduce((sum, log) => sum + log.amount, 0);

  const totalRefunds = Math.abs(
    getFilteredLogs()
      .filter((log) => log.amount < 0)
      .reduce((sum, log) => sum + log.amount, 0)
  );

  return (
    <>
      <InvalidTokenHandler error={plansError || subscriptionsError} />
      <div className="min-h-screen space-y-6">
        <div className="mb-6">
          <h2 className="text-2xl font-bold text-foreground m-0">
            Pricing Plans
          </h2>
          <p className="text-muted-foreground mt-2 text-sm">
            Manage subscription plans and view financial metrics
          </p>
        </div>

        {/* <Alert className="bg-tertiary border-border rounded-xl shadow-sm">
        <Info className="h-4 w-4" />
        <AlertDescription>
          <strong>Plan Management</strong> 
          <p className="text-muted-foreground">Plan edits are managed at the database level. Contact the development team for updates.</p>  
        </AlertDescription>
      </Alert> */}

        <div className="space-y-6">
          {/* Available Pricing Plans */}
          <Card className="bg-tertiary border-border rounded-xl shadow-sm">
            <CardHeader>
              <CardTitle>Available Pricing Plans</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto w-full">
                <Table className="min-w-[800px]">
                  <TableHeader>
                    <TableRow>
                      <TableHead>Plan Name</TableHead>
                      <TableHead>Price</TableHead>
                      <TableHead>Features</TableHead>
                      <TableHead>Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {plansLoading ? (
                      // Skeleton rows for pricing plans
                      Array.from({ length: 3 }).map((_, index) => (
                        <TableRow key={index}>
                          <TableCell>
                            <Skeleton className="h-4 w-32" />
                          </TableCell>
                          <TableCell>
                            <Skeleton className="h-4 w-24" />
                          </TableCell>
                          <TableCell>
                            <div className="space-y-2">
                              <Skeleton className="h-3 w-40" />
                              <Skeleton className="h-3 w-36" />
                              <Skeleton className="h-3 w-32" />
                            </div>
                          </TableCell>
                          <TableCell>
                            <Skeleton className="h-6 w-16 rounded-full" />
                          </TableCell>
                        </TableRow>
                      ))
                    ) : plansError ? (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center py-8">
                          <div className="text-destructive">
                            Error loading pricing plans
                          </div>
                        </TableCell>
                      </TableRow>
                    ) : pricingPlansWithFeatures.length > 0 ? (
                      pricingPlansWithFeatures.map((plan) => (
                        <TableRow key={plan.id}>
                          <TableCell>{renderPlanName(plan.name)}</TableCell>
                          <TableCell>
                            {renderPrice(
                              plan.amount,
                              plan.currency,
                              plan.billing_interval
                            )}
                          </TableCell>
                          <TableCell>
                            {renderFeatures(plan.features, plan.id)}
                          </TableCell>
                          <TableCell>{renderStatus(plan.is_active)}</TableCell>
                        </TableRow>
                      ))
                    ) : (
                      <TableRow>
                        <TableCell colSpan={4} className="text-center py-8">
                          <div className="text-muted-foreground">
                            No pricing plans found
                          </div>
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>

          {subscriptionsLoading ? (
            <div className="flex items-center justify-between mb-4">
              <Skeleton className="h-4 w-36" />
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-10 w-20" />
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-between mb-4">
              <span className="text-muted-foreground text-sm">
                Showing{" "}
                {Math.min(getFilteredLogs().length, logPagination.limit)} of{" "}
                {logPagination.total} logs
              </span>
              <div className="flex items-center gap-2">
                <span className="text-muted-foreground text-sm">
                  Rows per page
                </span>
                <Select
                  value={String(logPageSize)}
                  onValueChange={(v) => changeLogPageSize(Number(v))}
                >
                  <SelectTrigger className="w-[80px]">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          )}

          {/* Basic Financial Logs */}
          <Card className="bg-tertiary border-border rounded-xl shadow-sm">
            <CardHeader>
              <div className="flex justify-between items-center">
                <CardTitle className="flex items-center gap-2">
                  <FilterOutlined />
                  Basic Financial Logs
                </CardTitle>
                <div className="flex gap-2">
                  {isInitialLoading ? (
                    <>
                      <Skeleton className="h-10 w-24" />
                      <Skeleton className="h-10 w-28" />
                    </>
                  ) : (
                    <>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="outline"
                            className="bg-card"
                            style={{ borderColor: "hsl(var(--border))" }}
                          >
                            <DownloadOutlined className="mr-2 h-4 w-4" />
                            Export CSV
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>
                              Export financial logs
                            </AlertDialogTitle>
                            <AlertDialogDescription>
                              Do you want to export the current logs to CSV?
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction onClick={handleExportLogs}>
                              Export
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                      <Button
                        variant="outline"
                        onClick={handleResetLogFilters}
                        className="bg-card"
                        style={{ borderColor: "hsl(var(--border))" }}
                      >
                        Reset Filters
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {isInitialLoading ? (
                // Skeleton for filters section
                <Card className="bg-tertiary border-border rounded-xl shadow-sm">
                  <CardHeader>
                    <Skeleton className="h-6 w-20" />
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Search and Reset - First Row */}
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="flex-1">
                        <Skeleton className="h-10 w-full" />
                      </div>
                      <Skeleton className="h-10 w-24" />
                    </div>

                    {/* Main Filters - Second Row */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Skeleton className="h-10 w-full" />
                      <Skeleton className="h-10 w-full" />
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Card className="bg-tertiary border-border rounded-xl shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-lg font-semibold text-foreground">
                      Filters
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4 bg">
                    {/* Search and Reset - First Row */}
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="flex-1">
                        <div className="relative">
                          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                          <Input
                            placeholder="Search by plan, user, or status..."
                            value={logSearchText}
                            onChange={(e) => setLogSearchText(e.target.value)}
                            className="pl-10"
                          />
                        </div>
                      </div>

                      <Button
                        variant="outline"
                        className="w-full md:w-auto"
                        onClick={handleResetLogFilters}
                      >
                        <RotateCcw className="h-4 w-4 mr-2" />
                        Reset
                      </Button>
                    </div>

                    {/* Main Filters - Second Row */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <Select
                        value={
                          logStatusFilter.length > 0 ? logStatusFilter[0] : ""
                        }
                        onValueChange={(value) =>
                          setLogStatusFilter(value ? [value] : [])
                        }
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Filter by Status" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="inactive">Inactive</SelectItem>
                        </SelectContent>
                      </Select>

                      <DatePicker
                        value={logDateRange}
                        onChange={setLogDateRange}
                        range
                        numberOfMonths={2}
                        arrow={false}
                        className="custom-calendar"
                        placeholder="Select Date Range"
                        inputClass="h-10 rounded-md border border-input bg-background w-full px-2 text-sm placeholder:text-foreground"
                      />
                    </div>
                  </CardContent>
                </Card>
              )}

              <div className="mt-6">
                <div className="overflow-x-auto w-full">
                  <Table className="min-w-[800px]">
                    <TableHeader>
                      <TableRow>
                        <TableHead>Date</TableHead>
                        <TableHead>Plan</TableHead>
                        <TableHead>User</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isTableLoading ? (
                        // Skeleton rows for financial logs
                        Array.from({ length: 10 }).map((_, index) => (
                          <TableRow key={index}>
                            <TableCell>
                              <Skeleton className="h-4 w-20" />
                            </TableCell>
                            <TableCell>
                              <Skeleton className="h-4 w-24" />
                            </TableCell>
                            <TableCell>
                              <Skeleton className="h-4 w-28" />
                            </TableCell>
                            <TableCell>
                              <Skeleton className="h-4 w-16" />
                            </TableCell>
                            <TableCell>
                              <Skeleton className="h-6 w-16 rounded-full" />
                            </TableCell>
                          </TableRow>
                        ))
                      ) : subscriptionsError ? (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-8">
                            <div className="text-destructive">
                              Error loading subscriptions
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : getFilteredLogs().length > 0 ? (
                        getFilteredLogs().map((log) => (
                          <TableRow key={log.id}>
                            <TableCell>{renderLogDate(log.date)}</TableCell>
                            <TableCell>{log.plan}</TableCell>
                            <TableCell>{log.user}</TableCell>
                            <TableCell>
                              {renderLogAmount(log.amount, log.currency)}
                            </TableCell>
                            <TableCell>{renderLogStatus(log.status)}</TableCell>
                          </TableRow>
                        ))
                      ) : (
                        <TableRow>
                          <TableCell colSpan={5} className="text-center py-8">
                            <div className="flex flex-col items-center gap-2">
                              <div className="text-muted-foreground">
                                No financial logs found
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>

              {/* Pagination */}
              <div className="flex items-center justify-center mt-4 bg-tertiary">
                {(() => {
                  const total = Number(logPagination.total || 0);
                  const limit = Number(
                    logPagination.limit || logPageSize || 10
                  );
                  const page = Number(logPagination.page || logPage || 1);
                  const totalPages = Number(
                    logPagination.totalPage ||
                      Math.ceil(total / (limit || 1)) ||
                      0
                  );
                  const isPrevDisabled =
                    page <= 1 || totalPages <= 1 || total === 0;
                  const isNextDisabled =
                    page >= totalPages || totalPages <= 1 || total === 0;
                  const start = total === 0 ? 0 : (page - 1) * limit + 1;
                  const end = Math.min(page * limit, total);
                  return (
                    <>
                      {/* <div className="text-sm text-muted-foreground">
                          {`${start}-${end} of ${total} logs`}
                        </div> */}
                      <div className="flex items-center gap-3 bg-tertiary">
                        <Pagination>
                          <PaginationContent>
                            <PaginationItem>
                              <PaginationPrevious
                                onClick={() => {
                                  if (isPrevDisabled) return;
                                  changeLogPage(page - 1);
                                }}
                                aria-disabled={isPrevDisabled}
                              />
                            </PaginationItem>
                            {totalPages > 0 &&
                              (() => {
                                const current = page;
                                const pages: number[] = [];
                                const startPg = Math.max(1, current - 2);
                                const endPg = Math.min(totalPages, startPg + 4);
                                for (let p = startPg; p <= endPg; p++)
                                  pages.push(p);
                                return pages.map((p) => (
                                  <PaginationItem key={p}>
                                    <PaginationLink
                                      isActive={p === current}
                                      onClick={() => {
                                        if (p === current) return;
                                        changeLogPage(p);
                                      }}
                                    >
                                      {p}
                                    </PaginationLink>
                                  </PaginationItem>
                                ));
                              })()}
                            {totalPages > 5 && (
                              <PaginationItem>
                                <PaginationEllipsis />
                              </PaginationItem>
                            )}
                            <PaginationItem>
                              <PaginationNext
                                onClick={() => {
                                  if (isNextDisabled) return;
                                  changeLogPage(page + 1);
                                }}
                                aria-disabled={isNextDisabled}
                              />
                            </PaginationItem>
                          </PaginationContent>
                        </Pagination>
                      </div>
                    </>
                  );
                })()}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </>
  );
};

export default Pricing;
