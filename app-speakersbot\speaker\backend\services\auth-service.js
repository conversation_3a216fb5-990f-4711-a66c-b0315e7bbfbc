
const { RESPONSE_CODES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");
const jwtHelper = require("../helpers/jwt-helper");
const { Users, Speakers, Roles, SpeakerHistory, sequelize } = require("../models");
const bcrypt = require("bcrypt");
const emailService = require("./email-service");
const CONFIG = require("../config/config");
const jwt = require("jsonwebtoken");
const speakerHistoryService = require("./gamification-service");

const authService = {};

// ------------------------- auth-service -------------------------

/**
 * Authenticate speaker and generate JWT token.
 * Only handles speaker authentication for this service.
 * 
 * @param {Object} loginReq - The request object containing login credentials
 * @param {string} loginReq.body.email - Speaker email address
 * @param {string} loginReq.body.password - Speaker password
 * @returns {Promise<Object>} Result of login attempt with JWT token
 * @throws {CustomError} When email/password missing, speaker not found, or invalid credentials
 */
authService.login = async (loginReq) => {
    try {
        const { email, password } = loginReq.body;

        if (!email || !password) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email and password are required");
        }

        // Only look for speakers in this service
        const speaker = await Speakers.findOne({
            where: { email: email.trim().toLowerCase() },
            raw: true
        });

        if (!speaker) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Speaker not found with this email");
        }

        if (speaker.status !== 'active') {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Speaker account is not active. Please contact support.");
        }

        // Validate password
        const isPasswordValid = await bcrypt.compare(password, speaker.password);
        if (!isPasswordValid) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Invalid password");
        }

        // Generate JWT token
        const token = jwtHelper.generateToken({ id: speaker.id });

        return {
            status: true,
            message: "Login successful",
            data: { token }
        };
    } catch (error) {
        console.error("Error during login:", error);
        throw error;
    }
};


authService.signup = async (signupReq) => {
    try {

        const { name, email, phone, password } = signupReq.body;

        if (!name || !email || !phone || !password) {

            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Name, email, phone, and password are required");
        }

        const isEmailExist = await Speakers.findOne({ where: { email } });

        if (isEmailExist) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email already exists");
        }

        // Check if phone already exists
        const isPhoneExist = await Speakers.findOne({ where: { phone_number: phone } });

        if (isPhoneExist) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Phone number already exists");
        }

        const hashedPassword = await bcrypt.hash(password, 10);

        let { ref } = signupReq.query;
        let referral_speaker_id = null;
        let referral_affiliate_id = null;



        if (ref) {

            if (ref.startsWith('SPK')) {
                referral_speaker_id = await Speakers.findOne({ where: { referral_code: ref }, attributes: ['id'], raw: true }); 
                 
                const response = await speakerHistoryService.callEventPoints(referral_speaker_id?.id, 'referral_points');
                if (response.status) {
                    await SpeakerHistory.update({
                        xp_points: sequelize.literal(`xp_points + ${response.points}`),
                        where: { speaker_id: referral_speaker_id?.id }
                    });
                }
            }

            if (ref.startsWith('AFF')) {
                referral_affiliate_id = await AffiliateUsersDetails.findOne({ where: { referral_code: ref }, attributes: ['affiliate_id'], raw: true });
            }

        }

        let speaker_ref = 'SPK' + Math.random().toString(36).substring(2, 8).toUpperCase();

        // Create speaker with hashed password
        const speakerData = {
            name: name.trim(),
            email: email.trim().toLowerCase(),
            phone_number: phone.trim(),
            password: hashedPassword,
            referral_code: speaker_ref,
            referral_speaker_id: referral_speaker_id?.id || null,
            affiliate_id: referral_affiliate_id?.affiliate_id || null
        };

        const speaker = await Speakers.create(speakerData);

        return speaker;

    } catch (error) {
        console.error('Error signing up speaker:', error);
        throw error;
    }
}

/**
 * Send forgot password email with JWT reset token
 * Only handles speakers for this service
 * @param {string} email - Speaker email address
 * @returns {Promise<Object>} Result of forgot password request
 */
authService.forgotPassword = async (email) => {
    try {
        // Only check Speakers table for speaker backend
        const speaker = await Speakers.findOne({ where: { email: email.trim().toLowerCase() } });

        if (!speaker) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Speaker email not found");
        }

        // Check if speaker account is active
        if (speaker.status !== 'active') {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Speaker account is not active. Please contact support.");
        }

        return {
            id: speaker.id,
            name: speaker.name || 'Speaker',
            roleId: 0,
            userType: 'speaker',
        };

    } catch (error) {
        console.error("Error in forgotPassword:", error);
        throw error;
    }
};

/**
 * Reset password using JWT token
 * Only handles speakers for this service
 * @param {string} token - JWT reset token
 * @param {string} newPassword - New password
 * @returns {Promise<boolean>} Result of password reset
 */
authService.resetPassword = async (token, newPassword) => {
    try {
        // Verify JWT token
        let decoded;
        try {
            decoded = jwtHelper.verifyToken(token);
        } catch (jwtError) {
            if (jwtError instanceof jwt.TokenExpiredError) {
                throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Token expired");
            }
            if (jwtError instanceof jwt.JsonWebTokenError) {
                throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Invalid token");
            }
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Invalid or expired reset token");
        }

        // Check if token is for password reset
        if (decoded.type !== 'password_reset') {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Invalid token type");
        }

        // Only handle speakers
        if (decoded.role !== 'speaker') {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Invalid token for speaker reset");
        }

        const { userId } = decoded;

        // Find speaker
        const speaker = await Speakers.findByPk(userId);
        if (!speaker) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Speaker not found");
        }

        // Check if password was already changed after token issuance
        if (speaker.password_changed_at && speaker.password_changed_at > new Date(decoded.iat * 1000)) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Password already changed");
        }

        // Hash new password
        const hashedPassword = await bcrypt.hash(newPassword, 10);

        // Check if new password is same as current
        const isSamePassword = await bcrypt.compare(newPassword, speaker.password);
        if (isSamePassword) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "New password cannot be the same as the old password");
        }

        // Update speaker password
        speaker.password = hashedPassword;
        speaker.password_changed_at = new Date();
        await speaker.save();

        return true;
    } catch (error) {
        console.error("Error in resetPassword:", error);
        throw error;
    }
};

/**
 * Change password for authenticated speaker
 * Only handles speakers for this service
 * @param {Object} changeReq - Request object containing current and new password
 * @returns {Promise<Object>} Result of password change
 */
authService.changePassword = async (changeReq) => {
    try {
        const { currentPassword, newPassword, userId } = changeReq.body;

        if (!currentPassword || !newPassword || !userId) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Current password, new password, and user ID are required");
        }

        // Only find speakers
        const speaker = await Speakers.findByPk(userId);
        if (!speaker) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Speaker not found");
        }

        // Check if speaker account is active
        if (speaker.status !== 'active') {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Speaker account is not active");
        }

        // Verify current password
        const isCurrentPasswordValid = await bcrypt.compare(currentPassword, speaker.password);
        if (!isCurrentPasswordValid) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Current password is incorrect");
        }

        // Check if new password is same as current
        const isSamePassword = await bcrypt.compare(newPassword, speaker.password);
        if (isSamePassword) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "New password cannot be the same as the old password");
        }

        // Hash new password
        const hashedPassword = await bcrypt.hash(newPassword, 10);

        // Update speaker password
        speaker.password = hashedPassword;
        speaker.password_changed_at = new Date();
        await speaker.save();

        return {
            status: true,
            message: "Password changed successfully"
        };

    } catch (error) {
        console.error("Error in changePassword:", error);
        throw error;
    }
};

module.exports = authService;