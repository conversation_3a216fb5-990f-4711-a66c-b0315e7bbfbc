import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import type { ApiResponse } from "./types";

const BASE_URL = import.meta.env.VITE_PUBLIC_BASE_URL;

export interface Category {
  id: number;
  name: string;
}

export interface Subcategory {
  id: number;
  name: string;
}

export interface SubcategoryResponse {
  data: Subcategory[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

export interface RegistrationRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  city: string;
  state: string;
  country: string;
  primaryCategory: string;
  subCategory: string;
}

export const categoryApi = createApi({
  reducerPath: "categoryApi",
  baseQuery: fetchBaseQuery({
    baseUrl: BASE_URL,
    prepareHeaders: (headers) => {
      headers.set("content-type", "application/json");
      return headers;
    },
  }),
  endpoints: (builder) => ({
    getPrimaryCategories: builder.query<ApiResponse<Category[]>, void>({
      query: () => "/early-access/primary-category",
    }),
    getSubCategories: builder.query<ApiResponse<Subcategory[]>, { primaryCategoryId: number; page?: number; limit?: number }>({
      query: ({ primaryCategoryId, page = 1, limit = 20 }) =>
        `/early-access/sub-category/${primaryCategoryId}?page=${page}&limit=${limit}`,
    }),
    submitRegistration: builder.mutation<ApiResponse<{}>, RegistrationRequest>({
      query: (body) => ({
        url: "/early-access/landing-register",
        method: "POST",
        body: {
          first_name: body.firstName,
          last_name: body.lastName,
          email: body.email,
          phone_number: body.phone,
          city: body.city,
          state: body.state,
          country: body.country,
          primary_category: body.primaryCategory,
          sub_category: body.subCategory,
        },
      }),
    }),
  }),
});

export const {
  useGetPrimaryCategoriesQuery,
  useGetSubCategoriesQuery,
  useSubmitRegistrationMutation,
} = categoryApi;
