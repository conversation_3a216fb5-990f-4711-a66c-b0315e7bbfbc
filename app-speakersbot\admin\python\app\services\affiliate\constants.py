"""
Affiliate Constants
Configuration constants and settings for affiliate scraping
"""

# HTTP Configuration
DEFAULT_TIMEOUT = (10, 60)  # (connect timeout, read timeout)
RETRY_TIMEOUT = (5, 30)
MAX_RETRIES = 3
BACKOFF_FACTOR = 1

# HTTP Status codes to retry
RETRY_STATUS_CODES = [429, 500, 502, 503, 504]

# Browser headers for realistic requests
DEFAULT_HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Accept-Encoding': 'gzip, deflate',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1'
}

# Image file extensions
IMAGE_EXTENSIONS = ['.png', '.jpg', '.jpeg', '.svg', '.webp', '.ico', '.gif']

# Logo detection keywords
LOGO_KEYWORDS = ['logo', 'brand', 'company', 'site-logo', 'navbar-brand']

# Non-logo keywords to exclude
NON_LOGO_KEYWORDS = [
    'call', 'phone', 'icon-', 'arrow', 'menu', 'hamburger', 'search', 
    'user', 'cart', 'button', 'btn', 'play', 'facebook', 'twitter', 
    'instagram', 'pinterest', 'tiktok', 'youtube'
]

# Color patterns for extraction
COLOR_PATTERNS = [
    r'--text-color:\s*([^;]+)',
    r'--title-color:\s*([^;]+)',
    r'--button-color:\s*([^;]+)',
    r'--background-color:\s*([^;]+)',
    r'--primary-color:\s*([^;]+)',
    r'--secondary-color:\s*([^;]+)',
    r'--accent-color:\s*([^;]+)',
    r'color:\s*([^;]+)',
    r'background-color:\s*([^;]+)',
    r'border-color:\s*([^;]+)'
]

# Font patterns
FONT_PATTERNS = [
    r'font-family:\s*([^;]+)'
]

# Learning objective patterns - More flexible and natural language
LEARNING_OBJECTIVE_PATTERNS = [
    r'learn(?:ing)?\s+(?:about|how|to|that)\s+[^.!?]*[.!?]',
    r'takeaway[s]?\s*:?\s*([^.!?]*[.!?])',
    r'objective[s]?\s*:?\s*([^.!?]*[.!?])',
    r'you\s+will\s+learn[^.!?]*[.!?]',
    r'discover[^.!?]*[.!?]',
    r'understand[^.!?]*[.!?]',
    # More flexible patterns
    r'what\s+you\s+get[^.!?]*[.!?]',
    r'benefits\s+include[^.!?]*[.!?]',
    r'you\s+will\s+(?:gain|acquire|develop)[^.!?]*[.!?]',
    r'outcome[s]?\s*:?\s*([^.!?]*[.!?])',
    r'result[s]?\s*:?\s*([^.!?]*[.!?])',
    r'achieve[^.!?]*[.!?]',
    r'master[^.!?]*[.!?]',
    r'skills?\s+(?:you\s+)?(?:will\s+)?(?:learn|gain|develop)[^.!?]*[.!?]'
]

# Challenge patterns - More flexible and natural language
CHALLENGE_PATTERNS = [
    r'challenge[s]?\s+(?:we\s+)?solve[^.!?]*[.!?]',
    r'problem[s]?\s+(?:we\s+)?solve[^.!?]*[.!?]',
    r'address[^.!?]*challenge[^.!?]*[.!?]',
    r'overcome[^.!?]*[.!?]',
    r'solution[s]?\s+for[^.!?]*[.!?]',
    r'help[s]?\s+(?:you\s+)?with[^.!?]*[.!?]',
    # More flexible patterns
    r'struggle[s]?\s+(?:with|to)[^.!?]*[.!?]',
    r'difficult[y]?\s+(?:to|with)[^.!?]*[.!?]',
    r'frustrat[^.!?]*[.!?]',
    r'pain\s+point[s]?[^.!?]*[.!?]',
    r'issue[s]?\s+(?:you\s+)?(?:face|have)[^.!?]*[.!?]',
    r'problem[s]?\s+(?:you\s+)?(?:face|have|encounter)[^.!?]*[.!?]',
    r'struggle[s]?\s+(?:you\s+)?(?:face|have)[^.!?]*[.!?]',
    r'challenge[s]?\s+(?:you\s+)?(?:face|have|encounter)[^.!?]*[.!?]',
    r'barrier[s]?\s+(?:to|that)[^.!?]*[.!?]',
    r'obstacle[s]?\s+(?:to|that)[^.!?]*[.!?]'
]

# Business entity patterns
ENTITY_PATTERNS = [
    r'(?:LLC|L\.L\.C\.|Limited Liability Company)',
    r'(?:Inc\.|Incorporated|Corporation)',
    r'(?:LLP|L\.L\.P\.|Limited Liability Partnership)',
    r'(?:Partnership|Partners)',
    r'(?:Sole Proprietorship|Sole Proprietor)',
    r'(?:Co\.|Company)',
    r'(?:Ltd\.|Limited)'
]

# Reach estimate patterns
REACH_PATTERNS = [
    r'(\d+(?:,\d+)*(?:\s*(?:million|billion|thousand|k|m|b))?)\s+(?:users|customers|clients|audience|reach)',
    r'reach[^.!?]*(\d+(?:,\d+)*(?:\s*(?:million|billion|thousand|k|m|b))?)[^.!?]*[.!?]',
    r'audience[^.!?]*(\d+(?:,\d+)*(?:\s*(?:million|billion|thousand|k|m|b))?)[^.!?]*[.!?]',
    r'(\d+(?:,\d+)*(?:\s*(?:million|billion|thousand|k|m|b))?)\s+people'
]

# Promo channel patterns
CHANNEL_PATTERNS = [
    r'(?:facebook|fb)',
    r'(?:twitter|tweet)',
    r'(?:instagram|ig)',
    r'(?:linkedin)',
    r'(?:youtube|youtu\.be)',
    r'(?:tiktok)',
    r'(?:pinterest)',
    r'(?:snapchat)',
    r'(?:email\s+marketing|newsletter)',
    r'(?:google\s+ads|adwords)',
    r'(?:facebook\s+ads)',
    r'(?:influencer)',
    r'(?:affiliate)',
    r'(?:content\s+marketing)',
    r'(?:seo|search\s+engine\s+optimization)'
]

# Tax document patterns
TAX_PATTERNS = [
    r'(?:W-9|W9|W_9)',
    r'(?:W-8BEN|W8BEN|W_8BEN)',
    r'(?:1099|1099-MISC|1099-NEC)',
    r'(?:tax\s+document|tax\s+form|tax\s+paperwork)',
    r'(?:IRS\s+form|Internal\s+Revenue)',
    r'(?:tax\s+identification|TIN|EIN)',
    r'(?:affiliate\s+agreement.*tax|partnership.*tax)',
    r'(?:business\s+license|tax\s+license)',
    r'(?:compliance.*tax|tax.*compliance)',
    r'(?:withholding\s+tax|tax\s+withholding)'
]

# Terms acceptance patterns
TERMS_PATTERNS = [
    r'(?:terms\s+and\s+conditions|terms\s+&?\s+conditions)',
    r'(?:terms\s+of\s+service|terms\s+of\s+use)',
    r'(?:user\s+agreement|user\s+terms)',
    r'(?:accept\s+terms|agree\s+to\s+terms)',
    r'(?:checkbox.*terms|terms.*checkbox)',
    r'(?:privacy\s+policy.*accept|accept.*privacy)',
    r'(?:affiliate\s+terms|partnership\s+terms)',
    r'(?:legal\s+terms|legal\s+agreement)',
    r'(?:contract\s+terms|agreement\s+terms)',
    r'(?:I\s+agree|I\s+accept|agree\s+and\s+continue)',
    r'(?:read\s+and\s+accept|read\s+and\s+agree)',
    r'(?:terms.*required|required.*terms)',
    r'(?:mandatory\s+terms|terms.*mandatory)'
]

# Digital signature patterns
SIGNATURE_PATTERNS = [
    r'(?:digital\s+signature|digital\s+sign)',
    r'(?:electronic\s+signature|electronic\s+sign)',
    r'(?:e-signature|e-sign)',
    r'(?:sign\s+digitally|sign\s+electronically)',
    r'(?:online\s+signature|web\s+signature)',
    r'(?:affiliate\s+signature|partnership\s+signature)',
    r'(?:contract\s+signature|agreement\s+signature)',
    r'(?:sign\s+here|click\s+to\s+sign)',
    r'(?:signature\s+required|signature\s+needed)',
    r'(?:sign\s+and\s+submit|sign\s+and\s+send)',
    r'(?:authorize\s+signature|authorized\s+signature)',
    r'(?:legal\s+signature|binding\s+signature)',
    r'(?:signature\s+pad|signature\s+field)',
    r'(?:draw\s+signature|create\s+signature)',
    r'(?:signature\s+canvas|signature\s+area)'
]

# Named colors for validation
NAMED_COLORS = {
    'red', 'blue', 'green', 'yellow', 'orange', 'purple', 'pink', 'brown',
    'black', 'white', 'gray', 'grey', 'transparent', 'inherit', 'initial'
}

# Color field mapping
COLOR_FIELD_MAPPING = {
    0: 'primary_color',
    1: 'secondary_color', 
    2: 'accent_color',
    3: 'text_color',
    4: 'title_color',
    5: 'button_color',
    6: 'background_color',
    7: 'button_text_color'
}

# Product image selectors
PRODUCT_IMAGE_SELECTORS = [
    '.product img', '.product-picture img', '.product-card img', '.product-media img',
    '.service img', '.gallery img', '.carousel img', '.slider img', '.collection img',
    '[class*="product" i] img', '[data-testid*="product" i] img'
]

# Logo selectors
LOGO_SELECTORS = [
    '[class*="logo" i]', '[id*="logo" i]', '.navbar-brand', '.site-logo', 
    'header .brand', 'header [class*="brand" i]'
]

# Meta tags for product images
PRODUCT_META_TAGS = ['og:image', 'og:image:secure_url', 'twitter:image', 'twitter:image:src']

# Schema types for product extraction
PRODUCT_SCHEMA_TYPES = ['Product', 'Offer', 'ItemList']
