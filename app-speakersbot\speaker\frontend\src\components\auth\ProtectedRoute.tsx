import { ReactNode, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import { Navigate, useLocation } from "react-router-dom";
import type { RootState } from "@/store";
import { setAuthenticated } from "@/store/slices/authSlice";

interface ProtectedRouteProps {
  children: ReactNode;
}

export default function ProtectedRoute({ children }: ProtectedRouteProps) {
  const location = useLocation();
  const dispatch = useDispatch();
  const { isAuthenticated, token } = useSelector((state: RootState) => state.auth);

  // If token exists in localStorage but isAuthenticated is false (e.g., page refresh), mark authenticated
  useEffect(() => {
    if (!isAuthenticated && (token || localStorage.getItem("token"))) {
      dispatch(setAuthenticated(true));
    }
  }, [dispatch, isAuthenticated, token]);

  if (!isAuthenticated && !localStorage.getItem("token")) {
    return <Navigate to="/speaker" replace state={{ from: location }} />;
  }

  return <>{children}</>;
}


