import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import type { RootState } from "../store";

// Define the base URL for your API
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:3000/api";

export const scrapingApi = createApi({
  reducerPath: "scrapingApi",
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Get the token from the state
      const token = (getState() as RootState).auth?.token;

      // If we have a token, set the authorization header
      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }

      // Set content type
      headers.set("content-type", "application/json");

      return headers;
    },
  }),
  tagTypes: ["Scraping"],
  keepUnusedDataFor: 0, // Disable caching - always fetch fresh data
  endpoints: (builder) => ({
    updateScrapingStatus: builder.mutation({
      query: (body) => ({
        url: "/scraping/control",
        method: "POST",
        body,
      }),
      // Don't invalidate other queries - status change doesn't affect logs/topics
    }),
    getScrapingStatus: builder.query({
      query: () => ({
        url: "/scraping/status",
        method: "GET",
      }),
      providesTags: ["Scraping"],
    }),
    exportScrapingLogsCSV: builder.mutation({
      query: (params = {}) => ({
        url: "/scraping/logs/csv/export",
        method: "GET",
        params: params,
        responseHandler: (response) => response.blob(),
      }),
    }),
    getScrapingLogs: builder.query({
      query: (params = {}) => ({
        url: "/scraping/logs",
        method: "GET",
        params,
      }),
      providesTags: ["Scraping"],
    }),
    getScrapingSubCategories: builder.query({
      query: (params = {}) => ({
        url: "/scraping/subcategories",
        method: "GET",
        params,
      }),
      providesTags: ["Scraping"],
    }),
    createScrapingTopic: builder.mutation({
      query: (body) => ({
        url: "/scraping/topics",
        method: "POST",
        body,
      }),
      invalidatesTags: ["Scraping"],
    }),
    updateScrapingTopic: builder.mutation({
      query: ({ id, ...body }) => ({
        url: `/scraping/topics/${id}`,
        method: "PUT",
        body,
      }),
      invalidatesTags: ["Scraping"],
    }),
    deleteScrapingTopic: builder.mutation({
      query: (id) => ({
        url: `/scraping/topics/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["Scraping"],
    }),
    fetchTopicsForLogsFilter: builder.query({
      query: (_params?: any) => ({
        url: "/scraping/logs/topics",
        method: "GET",
      }),
      providesTags: ["Scraping"],
    }),
    toggleScrapingSubCategoryActive: builder.mutation({
      query: (id) => ({
        url: `/scraping/subcategories/toggle-active`,
        method: "PUT",
        body: { subcategory_id: id },
      }),
    }),
    fetchScrapingCategories: builder.query({
      query: () => ({
        url: "/scraping/categories",
        method: "GET",
      }),
      providesTags: ["Scraping"],
    }),
    createSubcategory: builder.mutation({
      query: (body) => ({
        url: "/scraping/subcategories",
        method: "POST",
        body,
      }),
      invalidatesTags: ["Scraping"],
    }),
  }),
});

export const {
  useUpdateScrapingStatusMutation,
  useGetScrapingStatusQuery,
  useExportScrapingLogsCSVMutation,
  useGetScrapingLogsQuery,
  useGetScrapingSubCategoriesQuery,
  useCreateScrapingTopicMutation,
  useUpdateScrapingTopicMutation,
  useDeleteScrapingTopicMutation,
  useFetchTopicsForLogsFilterQuery,
  useToggleScrapingSubCategoryActiveMutation,
  useFetchScrapingCategoriesQuery,
  useCreateSubcategoryMutation,
} = scrapingApi;
