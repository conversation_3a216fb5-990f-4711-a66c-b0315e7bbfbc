import { useResetPasswordMutation } from "@/apis/authApi";
import loginImg from "@/assets/images/login-img.png";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import {
  LockOutlined,
  EyeInvisibleOutlined,
  EyeOutlined,
  ArrowLeftOutlined,
} from "@ant-design/icons";
import React from "react";
import { Link, useNavigate, useSearchParams } from "react-router-dom";
import loginLogo from "/login-logo.png";
import ButtonLoader from "@/components/common/ButtonLoader";

const ResetPassword: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [resetPasswordMutation, { isLoading }] = useResetPasswordMutation();
  const { toast } = useToast();

  const [password, setPassword] = React.useState("");
  const [confirmPassword, setConfirmPassword] = React.useState("");
  const [showPassword, setShowPassword] = React.useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = React.useState(false);
  const [isSubmitted, setIsSubmitted] = React.useState(false);

  const token = searchParams.get("token");

  React.useEffect(() => {
    if (!token) {
      toast({
        title: "Invalid Reset Link",
        description:
          "The reset link is invalid or has expired. Please request a new one.",
        variant: "destructive",
      });
      navigate("/admin/forgot-password");
    }
  }, [token, navigate, toast]);

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (password !== confirmPassword) {
      toast({
        title: "Passwords don't match",
        description: "Please make sure both passwords are the same.",
        variant: "destructive",
      });
      return;
    }

    if (password.length < 4) {
      toast({
        title: "Password too short",
        description: "Password must be at least 4 characters long.",
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await resetPasswordMutation({
        token,
        newPassword: password,
      }).unwrap();

      if (result.status) {
        toast({
          title: "Password Reset Successful!",
          description:
            "Your password has been updated. You can now log in with your new password.",
        });
        setIsSubmitted(true);
        setTimeout(() => {
          navigate("/admin/login");
        }, 2000);
      }
    } catch (error: any) {
      console.error("Reset password error:", error);
      toast({
        title: error?.data?.error?.message || "Failed to reset password",
        description:
          "The reset link may have expired. Please request a new one.",
        variant: "destructive",
      });
    }
  };

  if (!token) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className="min-h-screen bg-foreground flex items-center justify-center p-6 bg-[hsla(217, 100%, 98%, 1)] bg-[#F4F8FF]">
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: `url(${loginImg})`,
          clipPath: "polygon(0 0, 70% 0, 65% 100%, 0% 100%)",
        }}
      />
      <div className="[width:min(1100px,100%)] grid grid-cols-[1.25fr_1fr] xl:grid-cols-[1.5fr_1fr] bg-foreground rounded-[50px] overflow-hidden border border-[hsl(var(--dashboard-light-blue)/0.35)] shadow-lg relative z-20">
        <div
          className="relative min-h-[520px] bg-cover bg-center saturate-125 m-4 mr-0 rounded-[50px] overflow-hidden"
          style={{
            backgroundImage: `url(${loginImg})`,
            clipPath: "polygon(0 0, 102% 0, 85% 100%, 0% 100%)",
          }}
        >
          <div className="absolute inset-0 bg-[linear-gradient(200deg,rgba(0,0,0,0.15)_10%,rgba(0,0,0,0.55)_70%)]" />
        </div>

        <div className="bg-foreground flex flex-col justify-around py-[50px] px-10">
          <div>
            <img
              src={loginLogo}
              alt="login"
              className="w-full h-full max-h-[50px] object-contain"
            />
          </div>
          <div className="flex-1 flex flex-col justify-center">
            <div className="text-center">
              <h1 className="m-0 text-[38px] font-bold tracking-[0.4px] bg-muted-gradient bg-clip-text text-transparent">
                Reset Password
              </h1>
              <div className="mt-1.5 text-[14px] text-[#464646]">
                {isSubmitted
                  ? "Password updated successfully! Redirecting to login..."
                  : "Enter your new password below"}
              </div>
            </div>

            {!isSubmitted ? (
              <form onSubmit={handleSubmit} className="px-5 py-5">
                <div className="mb-4 relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    required
                    placeholder="Enter new password"
                    className="h-11 rounded-[10px] bg-foreground text-background placeholder:text-background pr-10 autofill:bg-foreground"
                    startIcon={<LockOutlined />}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                  />
                  <button
                    type="button"
                    aria-label={
                      showPassword ? "Hide password" : "Show password"
                    }
                    onClick={() => setShowPassword((v) => !v)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-background/80 hover:text-background transition-colors"
                  >
                    {showPassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                  </button>
                </div>

                <div className="mb-2 relative">
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? "text" : "password"}
                    required
                    placeholder="Confirm new password"
                    className="h-11 rounded-[10px] bg-foreground text-background placeholder:text-background pr-10 autofill:bg-foreground"
                    startIcon={<LockOutlined />}
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                  />
                  <button
                    type="button"
                    aria-label={
                      showConfirmPassword ? "Hide password" : "Show password"
                    }
                    onClick={() => setShowConfirmPassword((v) => !v)}
                    className="absolute right-3 top-1/2 -translate-y-1/2 text-background/80 hover:text-background transition-colors"
                  >
                    {showConfirmPassword ? (
                      <EyeInvisibleOutlined />
                    ) : (
                      <EyeOutlined />
                    )}
                  </button>
                </div>

                <ButtonLoader
                  type="submit"
                  disabled={isLoading || !password || !confirmPassword}
                  loading={isLoading}
                  className={`mt-5 h-12 w-full rounded-[50px] bg-muted-gradient border-0 shadow-[0_8px_24px_rgba(2,132,199,0.35)] text-white transition-transform duration-500 ${
                    isLoading
                      ? "animate-pulse cursor-wait"
                      : "hover:shadow-[0_12px_28px_rgba(2,132,199,0.45)] hover:-translate-y-0.5 active:translate-y-0 active:scale-[0.98]"
                  }`}
                >
                  Reset Password
                </ButtonLoader>
              </form>
            ) : (
              <div className="px-5 py-5 text-center">
                <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                  <p className="text-green-800 text-sm">
                    Your password has been successfully updated!
                  </p>
                </div>
              </div>
            )}

            <div className="text-center">
              <Link
                to="/admin/login"
                className="inline-flex items-center text-sm text-[#464646] hover:text-[#2c5aa0] transition-colors"
              >
                <ArrowLeftOutlined className="mr-1" />
                Back to Login
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ResetPassword;
