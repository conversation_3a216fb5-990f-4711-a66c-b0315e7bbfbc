
const connection = require("../connection");
const { DataTypes } = require("sequelize");



const SpeakerOpportunityHistory = connection.define("SpeakerOpportunityHistory", {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true
    },
    speaker_id:{ 
     type :DataTypes.INTEGER,
     allowNull:false
    },
    speaker_opportunity_id: {
        type: DataTypes.INTEGER,
        allowNull: false
    },
    status: {
        type: DataTypes.ENUM('pending', 'accepted', 'rejected', 'interested', 'applied', 'booked', 'interviewing'),
        defaultValue: 'pending',
        comment: 'Status of the speaker opportunity',
    },
    reason: {
        type: DataTypes.STRING,
        allowNull: true
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: false
    }
}, {
    tableName: "speaker_opportunity_history",
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    paranoid: false
});


module.exports = SpeakerOpportunityHistory;
