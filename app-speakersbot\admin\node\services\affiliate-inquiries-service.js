const { where } = require("sequelize");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const { AffiliateUsersDetails, AffiliateInquiries, Speakers, AffiliateInquiriesHistory, GamificationHistory } = require("../models");
const { parseJSONSafely, parsePagination, getPagingData } = require("../helpers/app-hepler");
const affiliateInquiriesService = {};
const { Op } = require("sequelize");
// ------------------------- affiliate-inquiries-service -------------------------

/**
 * Create a new affiliate inquiry.
 * Links a speaker with an affiliate for potential collaboration.
 * 
 * @param {Object} req - Request object
 * @param {number} req.userId - Speaker ID from authenticated user
 * @param {Object} req.body - Request body
 * @param {number} req.body.affiliate_id - Affiliate ID to inquire about
 * @param {string} req.body.speaker_note - Speaker's note/message
 * @returns {Promise<Object>} Success message
 * @throws {CustomError} When affiliate not found or creation fails
 */
affiliateInquiriesService.createAffiliateInquiry = async (req) => {
    try {
        const id = req.userId;
        const { affiliate_id, note, inquiry_date, inquiry_time } = req.body;
    
        // find affiliate by affiliate_id
        const affiliate = await AffiliateUsersDetails.findOne({ where: { affiliate_id: affiliate_id } });

        if (!affiliate) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Affiliate not found");
        }
        // create affiliate inquiry
        
        const affiliateInquiry = await AffiliateInquiries.create({
            speaker_id: id,
            affiliate_id: affiliate_id, 
        });

        const affiliateInquiryHistory = await AffiliateInquiriesHistory.create({
            inquiry_id: affiliateInquiry.id,
            actor: "Speaker",
            note: note,
            status: "pending",
            inquiry_date: inquiry_date,
            inquiry_time: inquiry_time
        });

        return { status: true, message: "Affiliate inquiry created successfully" };
    }
    catch (error) {
        console.error("Error creating affiliate inquiry:", error);
        throw error;
    }
}

/**
 * Update an existing affiliate inquiry.
 * Allows updating affiliate notes and reason for inquiry.
 * 
 * @param {Object} req - Request object
 * @param {number} req.params.id - Inquiry ID to update
 * @param {Object} req.body - Update data
 * @param {string} [req.body.affiliate_note] - Updated affiliate note
 * @param {string} [req.body.reason] - Reason for inquiry
 * @returns {Promise<Object>} Success message
 * @throws {CustomError} When inquiry not found or update fails
 */
affiliateInquiriesService.updateAffiliateInquiry = async (req) => {
    try {
        // inquiry id
        const { id } = req.params; 

        const inquiry_data = await AffiliateInquiries.findByPk(id, { raw: true });

        const speaker_id = inquiry_data?.speaker_id;

        const { note, status, inquiry_date = null, inquiry_time = null } = req.body;

        if (!id) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Inquiry id is required");
        }


        // Build a minimal updates object with only provided fields
        const updates = {};
        updates.inquiry_id = id;
        updates.actor = "Affiliate";

        if (note !== undefined) updates.note = note;
        if (status !== undefined) updates.status = status;
        if (inquiry_date !== undefined) updates.inquiry_date = inquiry_date;
        if (inquiry_time !== undefined) updates.inquiry_time = inquiry_time; 

          
        // If nothing to update, still confirm record exists and return success 
        if(status =='accepted'){ 
           await AffiliateInquiries.update({xp_points_status :'captured'},{where:{id}}); 
           await GamificationHistory.create({ 
            speaker_id,
            gamification_id  : 4,
            points :2500,   
            type:'redemption'
           });
        }
        else if (status =='rejected'){ 
              await AffiliateInquiries.update({xp_points_status :'release'},{where:{id}});
              await Speakers.update({ xp_points: sequelize.literal('xp_points + 2500') }, { where: { id: speaker_id } });
        }


        if (Object.keys(updates).length === 0) {
            const exists = await AffiliateInquiriesHistory.count({ where: { id } });
            if (!exists) {
                throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Affiliate inquiry not found");
            }
            return { status: true, message: "Affiliate inquiry updated successfully" };
        }
        
        await AffiliateInquiriesHistory.create(updates);

        return { status: true, message: "Affiliate inquiry updated successfully" };

    } catch (error) {
        console.error("Error updating affiliate inquiry:", error);
        throw error;
    }
}


/**
 * Get affiliate inquiries
 */


affiliateInquiriesService.getAffiliateInquiries = async (req) => {
    try {
        const { page, limit, offset } = parsePagination(req.query);

        const dateRange = parseJSONSafely(req.query.dateRange, "Invalid JSON dateRange");
        const filter = parseJSONSafely(req.query.filter, "Invalid JSON filter");
        const search = req.query.search;
        const sort = parseJSONSafely(req.query.sort, "Invalid JSON sort");
        const affiliate_id = req?.params.id;

        // Separate where for Speaker search
        let speakerWhere = {};
        let historyWhere = {};
        if (search) {
            speakerWhere = { name: { [Op.like]: `%${search}%` } };
        }

        if (filter && typeof filter === "object") {
            if (filter && filter.status) {
                historyWhere.status = filter.status;
            }
        }

        if(dateRange && typeof dateRange === "object") { 
            if(dateRange.start_date && dateRange.end_date) {
                historyWhere.inquiry_date = {
                    [Op.between]: [dateRange.start_date, dateRange.end_date]
                };
            }
        }

        // Always sort by the latest updated entry (created_at DESC) for each inquiry
        // User sort input is ignored to ensure only the latest entry is shown and sorted by its creation date
        let sortOrder = [['created_at', 'DESC']];

        // Fetch affiliate inquiries, always show the latest updated entry for each inquiry
        // Only the most recent AffiliateInquiriesHistory entry is shown, sorted by its creation date
        const affiliateInquiries = await AffiliateInquiries.findAll({
            where: { affiliate_id },
            attributes: ['id', 'affiliate_id', 'speaker_id'],
            include: [
                {
                    model: AffiliateInquiriesHistory,
                    as: 'affiliateInquiriesHistory',
                    attributes: ['status', 'id', 'inquiry_date', 'inquiry_time', 'created_at'],
                    where: { ...historyWhere },
                    separate: true,
                    limit: 1,
                    order: [['created_at', 'DESC']]
                },
                {
                    model: Speakers,
                    as: 'speaker',
                    attributes: ['id', 'name'],
                    where: speakerWhere
                }
            ],
            order: [['id', 'DESC']], // Optionally sort inquiries themselves by id DESC
            offset,
            limit
        });

        // Count total for pagination
        const totalAffiliateInquiries = await AffiliateInquiries.count({
            where: { affiliate_id },
            include: [
                {
                    model: Speakers,
                    as: 'speaker',
                    where: speakerWhere
                }
            ]
        });

        const pageData = getPagingData(totalAffiliateInquiries, limit, page);

        // Format response

        let formattedData = affiliateInquiries.map(item => {
            const rawDate = item?.affiliateInquiriesHistory?.[0]?.inquiry_date || null;
            return {
                id: item?.id || null,
                affiliateId: item?.affiliate_id || null,
                speakerId: item?.speaker_id || null,
                speakerName: item?.speaker?.name || null,
                inquiryDate: rawDate ? rawDate.toISOString().split('T')[0] : null,
                inquiryDateRaw: rawDate instanceof Date ? rawDate : (rawDate ? new Date(rawDate) : null),
                status: item?.affiliateInquiriesHistory?.[0]?.status || null
            };
        });

        // Sort in-memory by inquiryDate if requested
        if (sort && typeof sort === "object" && Object.prototype.hasOwnProperty.call(sort, 'inquiry_date')) {
            const dir = String(sort.inquiry_date).toUpperCase() === 'ASC' ? 1 : -1;
            formattedData = formattedData.sort((a, b) => {
                if (!a.inquiryDateRaw && !b.inquiryDateRaw) return 0;
                if (!a.inquiryDateRaw) return dir === 1 ? 1 : -1;
                if (!b.inquiryDateRaw) return dir === 1 ? -1 : 1;
                if (a.inquiryDateRaw < b.inquiryDateRaw) return -1 * dir;
                if (a.inquiryDateRaw > b.inquiryDateRaw) return 1 * dir;
                return 0;
            });
        }

        // Remove inquiryDateRaw from response
        formattedData = formattedData.map(({ inquiryDateRaw, ...rest }) => rest);

        return {
            status: true,
            message: "Affiliate inquiries fetched successfully",
            data: formattedData,
            pageData
        };

    } catch (error) {
        console.error("Error getting affiliate inquiries:", error);
        throw error;
    }
};




/**
 * Get affiliate inquiry by affiliate and speaker
 */
affiliateInquiriesService.getAffiliateInquiryRequest = async (req) => {
    try {
        const { id } = req.params;

        const affiliateInquiry = await AffiliateInquiries.findOne({
            where: { id },
            include: [
                {
                    model: AffiliateInquiriesHistory,
                    as: 'affiliateInquiriesHistory',
                    attributes: [
                        'id',
                        'note',
                        'status',
                        'actor',
                        'inquiry_date',
                        'inquiry_time',
                        'created_at'
                    ],
                    separate: true,
                    order: [['created_at', 'DESC']]
                },
                {
                    model: Speakers,
                    as: 'speaker',
                    attributes: ['id', 'name', 'email', 'phone_number']
                }
            ]
        });

        if (!affiliateInquiry) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Affiliate inquiry not found");
        }

        const histories = affiliateInquiry.affiliateInquiriesHistory || [];

        // histories are ordered DESC by created_at. To get the first event, look from the end
        const firstSpeakerHistory = [...histories].reverse().find(h => (h.actor || '').toLowerCase() === 'speaker');

        // latest reschedule by either explicit action or status field
        const latestReschedule = histories.find(h => {
            // const actionLower = (h.action || '').toLowerCase();
            const statusLower = (h.status || '').toLowerCase();
            return statusLower === 'rescheduled';
        });

        // Fallback date/time: pick earliest history that has date/time
        const earliestWithDate = [...histories].reverse().find(h => h.inquiry_date || h.inquiry_time) || null;

        const responseData = {
            speakerName: affiliateInquiry.speaker?.name || null,
            speakerNote: firstSpeakerHistory?.note || null,
            inquiryDate: latestReschedule?.inquiry_date || earliestWithDate?.inquiry_date || null,
            inquiryTime: latestReschedule?.inquiry_time || earliestWithDate?.inquiry_time || null,
            status: latestReschedule ? 'rescheduled' : (histories[0]?.status || null)
        };

        return {
            status: true,
            message: "Affiliate inquiry fetched successfully",
            data: responseData
        };

    } catch (error) {
        console.error("Error getting affiliate inquiry by affiliate and speaker:", error);
        throw error;
    }
};


/**
 * Get affiliate inquiries history
 */
affiliateInquiriesService.getAffiliateInquiriesHistory = async (req) => {
    try {
        const { page, limit, offset } = parsePagination(req.query);
        const { id } = req.params;

        const affiliateInquiriesHistory = await AffiliateInquiriesHistory.findAll({ where: { inquiry_id: id }, limit, offset });
        const totalAffiliateInquiriesHistory = await AffiliateInquiriesHistory.count({ where: { inquiry_id: id } });
        const pageData = getPagingData(totalAffiliateInquiriesHistory, limit, page);

        return { status: true, message: "Affiliate inquiries history fetched successfully", data: affiliateInquiriesHistory, pageData: pageData };

    }
    catch (error) {
        console.error("Error getting affiliate inquiries history:", error);
        throw error;
    }
}


module.exports = affiliateInquiriesService;