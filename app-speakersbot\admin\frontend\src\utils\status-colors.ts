/**
 * Standardized status colors for the entire system
 * 
 * Usage:
 * - Green for active/success/accepted/resolved states
 * - Orange/Yellow for pending/processing/warning states  
 * - Red for rejected/error/critical states
 * - Gold/Golden for premium status
 */

export type StatusType = 'active' | 'pending' | 'rejected' | 'premium' | 'success' | 'error' | 'warning' | 'processing' | 'accepted' | 'cancelled' | 'completed' | 'draft' | 'paused' | 'inactive' | 'suspended';

/**
 * Get the standardized color for a given status
 * @param status - The status to get color for
 * @returns Ant Design color name
 */
export const getStatusColor = (status: string): string => {
  const normalizedStatus = status?.toLowerCase();
  
  // Green for active/success states
  if (['active', 'success', 'accepted', 'resolved', 'completed', 'paid'].includes(normalizedStatus)) {
    return 'green';
  }
  
  // Orange/Yellow for pending/processing states
  if (['pending', 'processing', 'warning', 'draft', 'paused', 'inactive', 'under_review'].includes(normalizedStatus)) {
    return 'orange';
  }
  
  // Red for rejected/error states
  if (['rejected', 'error', 'critical', 'cancelled', 'suspended', 'failed'].includes(normalizedStatus)) {
    return 'red';
  }
  
  // Gold for premium states
  if (['premium', 'gold', 'vip'].includes(normalizedStatus)) {
    return 'gold';
  }
  
  // Blue for informational states
  if (['interested', 'running', 'in_progress'].includes(normalizedStatus)) {
    return 'blue';
  }
  
  // Default for unknown states
  return 'default';
};

/**
 * Get CSS class names for Badge components with consistent status styling
 * @param status - The status to get styling for
 * @returns CSS class string for Badge components
 */
export const getStatusBadgeClass = (status: string): string => {
  const normalizedStatus = status.toLowerCase();
  
  // Green for active/success states
  if (['active', 'success', 'accepted', 'resolved', 'completed', 'paid'].includes(normalizedStatus)) {
    return 'bg-success/25 text-success border border-success rounded-md hover:bg-success/25';
  }
  
  // Orange for pending/processing states
  if (['pending', 'processing', 'warning', 'draft', 'paused', 'inactive', 'under_review'].includes(normalizedStatus)) {
    return 'bg-dashboard-dark/20 text-dashboard-dark border border-dashboard-dark/50 rounded-md hover:bg-dashboard-dark/20 hover:text-dashboard-dark';
  }
  
  // Red for rejected/error states
  if (['rejected', 'error', 'critical', 'cancelled', 'suspended', 'failed'].includes(normalizedStatus)) {
    return 'bg-destructive-superLight text-destructive border border-destructive rounded-md hover:bg-destructive-superLight hover:text-destructive';
  }
  
  // Gold for premium states
  if (['premium', 'gold', 'vip'].includes(normalizedStatus)) {
    return 'bg-dashboard-superLight text-dashboard-light border border-dashboard-medium rounded-md hover:bg-dashboard-superLight hover:text-dashboard-light';
  }
  
  // Blue for informational states
  if (['interested', 'running', 'in_progress'].includes(normalizedStatus)) {
    return 'bg-dashboard-medium/20 text-dashboard-light border border-dashboard-medium/50 rounded-md hover:bg-dashboard-medium/20 hover:text-dashboard-light';
  }
  
  // Default gray
  return 'bg-dashboard-superLight text-dashboard-light border border-dashboard-medium rounded-md hover:bg-dashboard-superLight hover:text-dashboard-light';
};

/**
 * Get Tailwind CSS color classes for progress bars and indicators
 * @param status - The status to get color for
 * @returns Tailwind color class
 */
export const getStatusTailwindColor = (status: string): string => {
  const normalizedStatus = status.toLowerCase();
  
  // Green for active/success states
  if (['active', 'success', 'accepted', 'resolved', 'completed', 'paid'].includes(normalizedStatus)) {
    return 'bg-green-500';
  }
  
  // Orange for pending/processing states
  if (['pending', 'processing', 'warning', 'draft', 'paused', 'inactive', 'under_review'].includes(normalizedStatus)) {
    return 'bg-orange-500';
  }
  
  // Red for rejected/error states
  if (['rejected', 'error', 'critical', 'cancelled', 'suspended', 'failed'].includes(normalizedStatus)) {
    return 'bg-red-500';
  }
  
  // Gold for premium states
  if (['premium', 'gold', 'vip'].includes(normalizedStatus)) {
    return 'bg-yellow-500';
  }
  
  // Blue for informational states
  if (['interested', 'running', 'in_progress'].includes(normalizedStatus)) {
    return 'bg-blue-500';
  }
  
  // Default gray
  return 'bg-gray-500';
};