
const { Op } = require("sequelize");
const CustomError = require("./custome-error");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("./response-codes");


exports.getPagingData = (count, resultPerPage, page) => {

    const totalPages = Math.ceil(count / resultPerPage) || 0;

    const pageData = {

        totalPage: totalPages,
        page: page,
        total: count,
        limit: resultPerPage,
    };

    return pageData;
};

exports.parsePagination = (query) => {
    console.log("query", query);
    const { page = 1, limit = 10 } = query;

    if (isNaN(page) || page < 1)
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid page number");
    if (isNaN(limit) || limit < 1)
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid limit number");

    const pageInt = parseInt(page, 10);
    const limitInt = parseInt(limit, 10);
    const offset = (pageInt - 1) * limitInt;

    return { page: pageInt, limit: limitInt, offset };
};


exports.parseJSONSafely = (jsonString, errorMessage) => {
    try {
        return jsonString ? JSON.parse(jsonString) : {};
    } catch (error) {
        console.error(`${errorMessage}:`, error.message);
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, errorMessage);
    }
};


exports.buildSearchWhereClause = (search, fields) => {
    if (!search || !fields || fields.length === 0) return {};

    return {
        [Op.or]: fields.map(field => ({
            [field]: { [Op.like]: `%${search}%` }
        }))
    }
};

exports.buildFilterWhereClause = (filters) => {
    if (!filters || typeof filters !== 'object' || Object.keys(filters).length === 0) return {};

    // Get all filter fields that have values
    const validFilters = Object.keys(filters).filter(field => {
        const value = filters[field];
        return value !== null && value !== undefined && value !== '';
    });

    if (validFilters.length === 0) return {};

    // Create AND conditions for each field with exact match
    const whereClause = {};
    validFilters.forEach(field => {
        whereClause[field] = filters[field];
    });

    return whereClause;
};

exports.buildSortClause = (sort) => {
    if (!sort || typeof sort !== 'object' || Object.keys(sort).length === 0) {
        // Default sort by id DESC
        return [['id', 'DESC']];
    }

    // Get all sort fields that have values
    const validSorts = Object.keys(sort).filter(field => {
        const direction = sort[field];
        return direction && (direction.toUpperCase() === 'ASC' || direction.toUpperCase() === 'DESC');
    });

    if (validSorts.length === 0) {
        // Default sort by id DESC if no valid sorts
        return [['id', 'DESC']];
    }

    // Create sort array for each field
    return validSorts.map(field => [field, sort[field].toUpperCase()]);
};

exports.encodeToBase64 = (inputString) => {
    const buffer = Buffer.from(inputString, "utf8");
    const base64String = buffer.toString("base64");
    return base64String;
};

exports.decodeFromBase64 = (encodedData) => {
    const decodedBuffer = Buffer.from(encodedData, "base64");
    return decodedBuffer.toString("utf8");
};
exports.generateReferralCode = () => {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}
