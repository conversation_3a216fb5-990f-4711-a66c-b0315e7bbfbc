"""
LLM-Powered Dynamic Query Generator for Speaker Opportunities

Uses OpenAI GPT to generate simple, natural language queries optimized
for Exa and Tavily search engines. Natural language works best for
AI-powered search engines.

Key Features:
- Natural language queries (NO boolean operators)
- Simple, conversational format
- Dynamic and unique for each run
- Proper time range via API parameters

Author: Speaker Bot Team
Version: 2.0 (Natural Language)
Last Updated: 2025
"""

import asyncio
from typing import List, Tuple
from datetime import datetime

from app.config.config import config
from app.config.logger import get_logger

logger = get_logger(__name__, file_name="scraper.log")


class LLMQueryGenerator:
    """Generate dynamic natural language queries using GPT."""
    
    def __init__(self):
        """Initialize the query generator."""
        self.current_year = datetime.now().year
        self.next_year = self.current_year + 1
        self.current_month = datetime.now().month
        
        # Year filter logic based on current month
        if self.current_month >= 6:  # Second half of year, focus on next year
            self.year_filter = f"{self.next_year}"
            self.year_description = f"{self.next_year}"
        else:  # First half, include both current and next year
            self.year_filter = f"{self.current_year} or {self.next_year}"
            self.year_description = f"{self.current_year} or {self.next_year}"
        
    def _get_openai_client(self):
        """Get OpenAI client."""
        try:
            from openai import OpenAI
            return OpenAI(api_key=config.OPENAI_API_KEY)
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI client: {e}")
            return None
    
    def _build_prompt(self, topic: str) -> str:
        """Build expert prompt for generating optimal natural language queries."""
        
        return f"""You are an expert at creating optimal natural language search queries for Exa and Tavily AI search engines.

MAIN FOCUS: Find "call for speakers" opportunities (conferences and events seeking speakers)

TOPIC: {topic}
YEAR FILTER: {self.year_filter} ({self.year_description})

Create 2 HIGH-QUALITY search queries in PURE NATURAL LANGUAGE specifically for finding "call for speakers" opportunities.

CRITICAL REQUIREMENTS (ALL MUST BE IN NATURAL LANGUAGE):
1. PRIMARY FOCUS: Always include "call for speakers" or equivalent phrases - this is the MAIN goal
2. Include the topic: {topic}
3. YEAR FILTER: Must include {self.year_description} in the query
4. Add RECENCY keywords like: Use "recent", "latest", "new", "just posted", "now accepting", "currently open" to get fresh opportunities
5. MUST use one of these phrases: "call for speakers", "call for presenters", "seeking speakers", "looking for speakers", "speaker submissions open"
6. Make queries DIFFERENT from each other - vary the speaker phrase used
7. Specify preferred domains using NATURAL PHRASES like: "from associations", "university conferences", "professional societies", NOT "site:.org"


DOMAIN PREFERENCE (Natural Language):
- Use: "from associations", "university", "professional societies", "academic conferences"
- This tells search engines to prefer .org and .edu domains naturally

RECENCY KEYWORDS (Natural Language):
- Use: "recent", "latest", "new", "just posted", "now accepting", "currently open", "recently announced"
- This ensures you get fresh, recently posted opportunities

Create exactly 2 queries in NATURAL LANGUAGE. NO boolean operators, NO "-jobs", NO "site:". Just natural conversational queries.

IMPORTANT - Make queries DIVERSE:
- Vary recency keywords: mix "recent", "latest", "new", "now accepting"
- Vary speaker phrases like: (for example) "call for speakers" or "seeking presenters" or "looking for speakers"
- Vary domain hints: mix "associations", "university", "professional societies"
- Vary formats: different ordering of elements

Output format:
1. [first diverse query in natural language]
2. [second diverse query in natural language - must be different structure from first]"""

    async def _generate_queries_with_gpt(self, topic: str) -> List[str]:
        """Generate 2 optimal natural language queries using GPT."""
        client = self._get_openai_client()
        if not client:
            logger.error("OpenAI not available")
            raise Exception("OpenAI client not available")
        
        prompt = self._build_prompt(topic)
        
        response = await asyncio.to_thread(
            client.chat.completions.create,
            model=config.OPENAI_MODEL,
            messages=[
                {"role": "system", "content": "You create optimal natural language search queries for Exa and Tavily AI search engines."},
                {"role": "user", "content": prompt}
            ],
            temperature=0.9,
            top_p=0.95,
        )
        
        content = response.choices[0].message.content.strip()
        
        # Extract queries
        queries = []
        for line in content.split('\n'):
            line = line.strip()
            if not line:
                continue
            # Remove numbering
            if line[0].isdigit():
                line = line.split('.', 1)[-1].strip()
            if line and len(line) > 15:
                queries.append(line)
        
        # Ensure we have exactly 2 queries
        if len(queries) < 2:
            raise Exception(f"GPT returned only {len(queries)} queries, expected 2")
        
        logger.info(f"Generated 2 optimal queries for '{topic}'")
        return queries[:2]


# Global instance
llm_query_generator = LLMQueryGenerator()


async def build_dynamic_queries(topic: str) -> List[Tuple[str, str]]:
    """
    Build 2 optimal natural language queries using LLM.
    
    Generates exactly 2 high-quality queries optimized for Exa and Tavily search engines.
    Queries are natural language, focused on .org/.edu domains, and exclude job boards.
    
    Args:
        topic: The topic to search for
        
    Returns:
        List[Tuple[str, str]]: List of 2 (query_type, query_string) tuples
    """
    queries = await llm_query_generator._generate_queries_with_gpt(topic)
    
    # Return exactly 2 queries
    result = []
    for i, query in enumerate(queries[:2], 1):
        result.append((f"query_{i}", query))
    
    return result
