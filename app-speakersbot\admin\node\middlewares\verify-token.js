const jwtHelper = require('../helpers/jwt-helper');
const { User } = require("../models")
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES } = require("../helpers/response-codes");

// Middleware to verify JWT and extract userId and roleId
function verifyToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, 'No token provided');
    }
    const token = authHeader.split(' ')[1];
    const decoded = jwtHelper.verifyToken(token);

    if (!decoded || decoded instanceof Error) {
        throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, 'Invalid or expired token');
    }

    req.userId = decoded.id;
    req.roleId = decoded.role_id;

    next();
}

module.exports = verifyToken;
