import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar as CalendarIcon, Clock, Zap, CheckCircle, XCircle, Pause, MapPin, Heart, Search, Filter } from 'lucide-react';
import { CalendarModal } from '@/components/calendar/CalendarModal';
import { OpportunityDetailModal } from '@/components/opportunities/OpportunityDetailModal';
import { ReasonModal } from '@/components/ui/reason-modal';
import { 
  Pagination, 
  PaginationContent, 
  PaginationItem, 
  PaginationLink, 
  PaginationNext, 
  PaginationPrevious 
} from '@/components/ui/pagination';

export function TodayTab() {
  const [isCalendarOpen, setIsCalendarOpen] = useState(false);
  const [isOpportunityDetailOpen, setIsOpportunityDetailOpen] = useState(false);
  const [selectedOpportunity, setSelectedOpportunity] = useState<any>(null);
  const [isReasonModalOpen, setIsReasonModalOpen] = useState(false);
  const [reasonModalTitle, setReasonModalTitle] = useState('');
  const [pendingAction, setPendingAction] = useState<{type: string, id: number} | null>(null);
  const [acceptedMatches, setAcceptedMatches] = useState<Set<number>>(new Set());
  const [rejectedMatches, setRejectedMatches] = useState<Set<number>>(new Set());
  const [pendingMatches, setPendingMatches] = useState<Set<number>>(new Set());
  const [interestedMatches, setInterestedMatches] = useState<Set<number>>(new Set());
  const [bookedMatches, setBookedMatches] = useState<Set<number>>(new Set());
  const [notBookedMatches, setNotBookedMatches] = useState<Set<number>>(new Set());
  const [acceptedTasks, setAcceptedTasks] = useState<Set<number>>(new Set());
  const [rejectedTasks, setRejectedTasks] = useState<Set<number>>(new Set());
  const [pendingTasks, setPendingTasks] = useState<Set<number>>(new Set());
  const [interestedTasks, setInterestedTasks] = useState<Set<number>>(new Set());
  const [bookedTasks, setBookedTasks] = useState<Set<number>>(new Set());
  const [notBookedTasks, setNotBookedTasks] = useState<Set<number>>(new Set());

  // Filter states
  const [searchQuery, setSearchQuery] = useState('');
  const [locationFilter, setLocationFilter] = useState('all');
  const [eventDateFilter, setEventDateFilter] = useState('all');
  const [topicFilter, setTopicFilter] = useState('all');
  const [compensationFilter, setCompensationFilter] = useState('all');

  // Pagination states
  const [matchesCurrentPage, setMatchesCurrentPage] = useState(1);
  const [tasksCurrentPage, setTasksCurrentPage] = useState(1);
  const itemsPerPage = 15;

  // Demo data for new matches
  const allMatches = [
    {
      id: 1,
      title: "AI Conference 2024",
      organizer: "TechEvents Corp",
      location: "San Francisco, CA",
      eventDate: "2024-03-15",
      lastApplyDate: "2024-02-15",
      type: "Keynote",
      topics: ["AI", "Machine Learning", "Neural Networks"],
      compensation: "$5,000",
      matchScore: 95,
      description: "Join industry leaders at the premier AI conference.",
      reason: "Your expertise in machine learning perfectly aligns with their keynote requirements."
    },
    {
      id: 2,
      title: "Cloud Computing Summit",
      organizer: "CloudTech Inc",
      location: "Seattle, WA",
      eventDate: "2024-04-22",
      lastApplyDate: "2024-03-20",
      type: "Workshop",
      topics: ["AWS", "Cloud Computing", "Container Orchestration"],
      compensation: "$3,200",
      matchScore: 88,
      description: "Lead a hands-on workshop on cloud computing.",
      reason: "Strong match based on your AWS certifications and container orchestration experience."
    },
    {
      id: 3,
      title: "React Developer Conference",
      organizer: "Frontend Masters",
      location: "New York, NY",
      eventDate: "2024-05-10",
      lastApplyDate: "2024-04-01",
      type: "Panel Discussion",
      topics: ["React", "Frontend", "JavaScript"],
      compensation: "$4,500",
      matchScore: 92,
      description: "Participate in a panel discussion about the future of React.",
      reason: "Your extensive React experience makes you an ideal panelist."
    },
    {
      id: 4,
      title: "DevOps World 2024",
      organizer: "DevOps Alliance",
      location: "Austin, TX",
      eventDate: "2024-06-20",
      lastApplyDate: "2024-05-15",
      type: "Workshop",
      topics: ["DevOps", "CI/CD", "Docker"],
      compensation: "$3,800",
      matchScore: 85,
      description: "Conduct a hands-on DevOps workshop for enterprise teams.",
      reason: "Your DevOps expertise and Docker knowledge align perfectly with our needs."
    },
    {
      id: 5,
      title: "Mobile Development Summit",
      organizer: "Mobile First Events",
      location: "Los Angeles, CA",
      eventDate: "2024-07-08",
      lastApplyDate: "2024-06-01",
      type: "Keynote",
      topics: ["Mobile", "iOS", "Android"],
      compensation: "$6,000",
      matchScore: 90,
      description: "Deliver the opening keynote on mobile development trends.",
      reason: "Your mobile development portfolio showcases exactly what we're looking for."
    },
    {
      id: 6,
      title: "Blockchain Technology Forum",
      organizer: "Crypto Events Ltd",
      location: "Miami, FL",
      eventDate: "2024-08-15",
      lastApplyDate: "2024-07-10",
      type: "Technical Talk",
      topics: ["Blockchain", "Cryptocurrency", "Web3"],
      compensation: "$7,200",
      matchScore: 93,
      description: "Present on blockchain implementation strategies.",
      reason: "Your blockchain projects demonstrate the technical depth we need."
    },
    {
      id: 7,
      title: "Data Science Symposium",
      organizer: "Analytics Pro",
      location: "Chicago, IL",
      eventDate: "2024-09-12",
      lastApplyDate: "2024-08-01",
      type: "Workshop",
      topics: ["Data Science", "Python", "Machine Learning"],
      compensation: "$4,200",
      matchScore: 87,
      description: "Lead a data science workshop for beginners to advanced practitioners.",
      reason: "Your data science background and teaching experience are perfect for this role."
    },
    {
      id: 8,
      title: "GraphQL Summit 2024",
      organizer: "Apollo GraphQL",
      location: "Boston, MA",
      eventDate: "2024-10-15",
      lastApplyDate: "2024-09-01",
      type: "Technical Talk",
      topics: ["GraphQL", "API", "Backend"],
      compensation: "$4,800",
      matchScore: 89,
      description: "Present advanced GraphQL techniques and best practices.",
      reason: "Your GraphQL expertise and API development experience are highly valued."
    },
    {
      id: 9,
      title: "Kubernetes Conference",
      organizer: "Cloud Native Foundation",
      location: "Denver, CO",
      eventDate: "2024-11-03",
      lastApplyDate: "2024-09-20",
      type: "Workshop",
      topics: ["Kubernetes", "Container Orchestration", "DevOps"],
      compensation: "$5,500",
      matchScore: 91,
      description: "Lead a hands-on Kubernetes workshop for enterprise developers.",
      reason: "Your container orchestration skills match perfectly with our requirements."
    },
    {
      id: 10,
      title: "JavaScript Conf 2024",
      organizer: "JS Foundation",
      location: "Portland, OR",
      eventDate: "2024-12-08",
      lastApplyDate: "2024-10-15",
      type: "Keynote",
      topics: ["JavaScript", "Web Development", "Node.js"],
      compensation: "$6,500",
      matchScore: 94,
      description: "Deliver the opening keynote on the future of JavaScript.",
      reason: "Your extensive JavaScript experience makes you an ideal keynote speaker."
    },
    {
      id: 11,
      title: "Cybersecurity Summit",
      organizer: "InfoSec World",
      location: "Las Vegas, NV",
      eventDate: "2025-01-22",
      lastApplyDate: "2024-11-30",
      type: "Panel Discussion",
      topics: ["Cybersecurity", "Threat Detection", "Security"],
      compensation: "$5,800",
      matchScore: 88,
      description: "Join a panel discussion on emerging cybersecurity threats.",
      reason: "Your security expertise aligns with our panel requirements."
    },
    {
      id: 12,
      title: "Angular Connect",
      organizer: "Angular Team",
      location: "London, UK",
      eventDate: "2025-02-14",
      lastApplyDate: "2024-12-01",
      type: "Workshop",
      topics: ["Angular", "Frontend", "TypeScript"],
      compensation: "$4,200",
      matchScore: 86,
      description: "Conduct an Angular workshop for enterprise developers.",
      reason: "Your Angular and TypeScript skills are exactly what we need."
    },
    {
      id: 13,
      title: "Machine Learning Expo",
      organizer: "ML Research Institute",
      location: "Stanford, CA",
      eventDate: "2025-03-10",
      lastApplyDate: "2025-01-15",
      type: "Technical Talk",
      topics: ["Machine Learning", "AI", "Neural Networks"],
      compensation: "$7,000",
      matchScore: 96,
      description: "Present cutting-edge machine learning research and applications.",
      reason: "Your ML research background perfectly matches our technical requirements."
    },
    {
      id: 14,
      title: "Vue.js Conference",
      organizer: "Vue Core Team",
      location: "Paris, France",
      eventDate: "2025-04-18",
      lastApplyDate: "2025-02-01",
      type: "Workshop",
      topics: ["Vue.js", "Frontend", "JavaScript"],
      compensation: "$4,000",
      matchScore: 83,
      description: "Lead a Vue.js workshop for intermediate developers.",
      reason: "Your Vue.js experience and teaching skills are highly valued."
    },
    {
      id: 15,
      title: "Serverless Architecture Summit",
      organizer: "Serverless Inc",
      location: "San Francisco, CA",
      eventDate: "2025-05-12",
      lastApplyDate: "2025-03-01",
      type: "Panel Discussion",
      topics: ["Serverless", "Cloud", "Architecture"],
      compensation: "$5,200",
      matchScore: 90,
      description: "Participate in a serverless architecture panel discussion.",
      reason: "Your serverless experience makes you an ideal panelist."
    },
    {
      id: 16,
      title: "Flutter Developer Conference",
      organizer: "Google Flutter Team",
      location: "Mountain View, CA",
      eventDate: "2025-06-15",
      lastApplyDate: "2025-04-01",
      type: "Technical Talk",
      topics: ["Flutter", "Mobile", "Dart"],
      compensation: "$5,500",
      matchScore: 87,
      description: "Present advanced Flutter development techniques.",
      reason: "Your Flutter expertise aligns with our technical session requirements."
    },
    {
      id: 17,
      title: "Database Technologies Summit",
      organizer: "DB Tech Alliance",
      location: "Chicago, IL",
      eventDate: "2025-07-20",
      lastApplyDate: "2025-05-01",
      type: "Workshop",
      topics: ["Database", "SQL", "NoSQL"],
      compensation: "$4,800",
      matchScore: 85,
      description: "Conduct a workshop on modern database technologies.",
      reason: "Your database expertise and hands-on experience are perfect for this role."
    },
    {
      id: 18,
      title: "Progressive Web Apps Conference",
      organizer: "PWA Alliance",
      location: "Seattle, WA",
      eventDate: "2025-08-25",
      lastApplyDate: "2025-06-01",
      type: "Keynote",
      topics: ["PWA", "Web Development", "Mobile"],
      compensation: "$6,800",
      matchScore: 92,
      description: "Deliver a keynote on the future of Progressive Web Apps.",
      reason: "Your PWA development experience makes you an ideal keynote speaker."
    },
    {
      id: 19,
      title: "Microservices Architecture Summit",
      organizer: "Microservices Foundation",
      location: "Austin, TX",
      eventDate: "2025-09-18",
      lastApplyDate: "2025-07-01",
      type: "Technical Talk",
      topics: ["Microservices", "Architecture", "Distributed Systems"],
      compensation: "$5,800",
      matchScore: 89,
      description: "Present on microservices architecture patterns and best practices.",
      reason: "Your distributed systems experience perfectly matches our requirements."
    },
    {
      id: 20,
      title: "WebAssembly Conference",
      organizer: "WASM Community",
      location: "Berlin, Germany",
      eventDate: "2025-10-22",
      lastApplyDate: "2025-08-01",
      type: "Workshop",
      topics: ["WebAssembly", "Performance", "Web Development"],
      compensation: "$4,500",
      matchScore: 84,
      description: "Lead a WebAssembly workshop for advanced developers.",
      reason: "Your WebAssembly knowledge and performance optimization skills are highly valued."
    },
    {
      id: 21,
      title: "Edge Computing Summit",
      organizer: "Edge Tech Alliance",
      location: "Tokyo, Japan",
      eventDate: "2025-11-14",
      lastApplyDate: "2025-09-01",
      type: "Panel Discussion",
      topics: ["Edge Computing", "IoT", "Distributed Systems"],
      compensation: "$6,200",
      matchScore: 91,
      description: "Join a panel on edge computing technologies and applications.",
      reason: "Your edge computing experience makes you a valuable panelist."
    },
    {
      id: 22,
      title: "Quantum Computing Workshop",
      organizer: "Quantum Research Lab",
      location: "Cambridge, MA",
      eventDate: "2025-12-10",
      lastApplyDate: "2025-10-01",
      type: "Workshop",
      topics: ["Quantum Computing", "Algorithms", "Research"],
      compensation: "$7,500",
      matchScore: 95,
      description: "Conduct a quantum computing workshop for researchers.",
      reason: "Your quantum computing research background is exactly what we need."
    }
  ];

  // Demo data for tasks due today
  const allTasks = [
    {
      id: 1,
      title: "React Summit 2024",
      organizer: "GitNation",
      location: "Amsterdam, NL",
      eventDate: "2024-06-15",
      lastApplyDate: "2024-01-20",
      type: "Conference",
      topics: ["React", "Frontend", "JavaScript"],
      compensation: "$4,500",
      matchScore: 96,
      description: "Submit your speaking proposal for the premier React conference.",
      dueType: "Apply by"
    },
    {
      id: 2,
      title: "Python Developer Conference",
      organizer: "Python Software Foundation",
      location: "Portland, OR",
      eventDate: "2024-05-25",
      lastApplyDate: "2024-02-10",
      type: "Technical Talk",
      topics: ["Python", "Backend", "Django"],
      compensation: "$3,800",
      matchScore: 89,
      description: "Present on advanced Python development techniques.",
      dueType: "Apply by"
    },
    {
      id: 3,
      title: "Cybersecurity Expo 2024",
      organizer: "SecureTech Events",
      location: "Washington, DC",
      eventDate: "2024-04-30",
      lastApplyDate: "2024-01-25",
      type: "Panel Discussion",
      topics: ["Cybersecurity", "Encryption", "Network Security"],
      compensation: "$5,500",
      matchScore: 91,
      description: "Join a panel on emerging cybersecurity threats.",
      dueType: "Apply by"
    },
    {
      id: 4,
      title: "UI/UX Design Conference",
      organizer: "Design Masters",
      location: "San Diego, CA",
      eventDate: "2024-07-18",
      lastApplyDate: "2024-03-01",
      type: "Workshop",
      topics: ["UI/UX", "Design", "User Experience"],
      compensation: "$4,000",
      matchScore: 94,
      description: "Conduct a workshop on modern UI/UX design principles.",
      dueType: "Apply by"
    },
    {
      id: 5,
      title: "Docker Workshop Series",
      organizer: "Container Technologies Inc",
      location: "San Francisco, CA",
      eventDate: "2024-08-20",
      lastApplyDate: "2024-01-28",
      type: "Workshop",
      topics: ["Docker", "Containerization", "DevOps"],
      compensation: "$3,500",
      matchScore: 88,
      description: "Lead a comprehensive Docker workshop series.",
      dueType: "Apply by"
    },
    {
      id: 6,
      title: "Machine Learning Conference",
      organizer: "AI Research Institute",
      location: "Boston, MA",
      eventDate: "2024-09-15",
      lastApplyDate: "2024-02-05",
      type: "Technical Talk",
      topics: ["Machine Learning", "AI", "Data Science"],
      compensation: "$6,200",
      matchScore: 93,
      description: "Present cutting-edge ML research and applications.",
      dueType: "Apply by"
    },
    {
      id: 7,
      title: "Vue.js Developer Meetup",
      organizer: "Vue Community",
      location: "Austin, TX",
      eventDate: "2024-10-12",
      lastApplyDate: "2024-02-15",
      type: "Panel Discussion",
      topics: ["Vue.js", "Frontend", "JavaScript"],
      compensation: "$2,800",
      matchScore: 85,
      description: "Join a Vue.js panel discussion for developers.",
      dueType: "Apply by"
    },
    {
      id: 8,
      title: "Blockchain Developers Summit",
      organizer: "Blockchain Alliance",
      location: "Miami, FL",
      eventDate: "2024-11-08",
      lastApplyDate: "2024-02-20",
      type: "Workshop",
      topics: ["Blockchain", "Smart Contracts", "Web3"],
      compensation: "$5,800",
      matchScore: 90,
      description: "Conduct a blockchain development workshop.",
      dueType: "Apply by"
    },
    {
      id: 9,
      title: "Cloud Architecture Conference",
      organizer: "Cloud First Events",
      location: "Seattle, WA",
      eventDate: "2024-12-03",
      lastApplyDate: "2024-02-25",
      type: "Keynote",
      topics: ["Cloud", "Architecture", "AWS"],
      compensation: "$7,000",
      matchScore: 95,
      description: "Deliver keynote on modern cloud architecture patterns.",
      dueType: "Apply by"
    },
    {
      id: 10,
      title: "Mobile App Development Workshop",
      organizer: "Mobile Dev Community",
      location: "Los Angeles, CA",
      eventDate: "2025-01-15",
      lastApplyDate: "2024-03-01",
      type: "Workshop",
      topics: ["Mobile", "iOS", "Android"],
      compensation: "$4,200",
      matchScore: 87,
      description: "Lead a hands-on mobile app development workshop.",
      dueType: "Apply by"
    },
    {
      id: 11,
      title: "GraphQL API Design Summit",
      organizer: "API Developers Guild",
      location: "New York, NY",
      eventDate: "2025-02-20",
      lastApplyDate: "2024-03-10",
      type: "Technical Talk",
      topics: ["GraphQL", "API", "Backend"],
      compensation: "$4,800",
      matchScore: 89,
      description: "Present on GraphQL API design best practices.",
      dueType: "Apply by"
    },
    {
      id: 12,
      title: "Kubernetes Operations Conference",
      organizer: "Cloud Native Foundation",
      location: "Denver, CO",
      eventDate: "2025-03-25",
      lastApplyDate: "2024-03-15",
      type: "Workshop",
      topics: ["Kubernetes", "DevOps", "Container Orchestration"],
      compensation: "$5,200",
      matchScore: 91,
      description: "Conduct a Kubernetes operations workshop.",
      dueType: "Apply by"
    },
    {
      id: 13,
      title: "JavaScript Performance Optimization",
      organizer: "Web Performance Guild",
      location: "Chicago, IL",
      eventDate: "2025-04-18",
      lastApplyDate: "2024-03-20",
      type: "Technical Talk",
      topics: ["JavaScript", "Performance", "Web Development"],
      compensation: "$3,800",
      matchScore: 86,
      description: "Present on JavaScript performance optimization techniques.",
      dueType: "Apply by"
    },
    {
      id: 14,
      title: "Serverless Architecture Workshop",
      organizer: "Serverless Inc",
      location: "Portland, OR",
      eventDate: "2025-05-22",
      lastApplyDate: "2024-03-25",
      type: "Workshop",
      topics: ["Serverless", "Cloud", "Architecture"],
      compensation: "$4,600",
      matchScore: 88,
      description: "Lead a comprehensive serverless architecture workshop.",
      dueType: "Apply by"
    },
    {
      id: 15,
      title: "Flutter Mobile Development Conference",
      organizer: "Flutter Community",
      location: "San Diego, CA",
      eventDate: "2025-06-15",
      lastApplyDate: "2024-04-01",
      type: "Panel Discussion",
      topics: ["Flutter", "Mobile", "Cross-platform"],
      compensation: "$3,200",
      matchScore: 84,
      description: "Join a Flutter development panel discussion.",
      dueType: "Apply by"
    },
    {
      id: 16,
      title: "Database Design Workshop",
      organizer: "Data Architecture Institute",
      location: "Phoenix, AZ",
      eventDate: "2025-07-10",
      lastApplyDate: "2024-04-10",
      type: "Workshop",
      topics: ["Database", "SQL", "Data Architecture"],
      compensation: "$4,000",
      matchScore: 87,
      description: "Conduct a database design and optimization workshop.",
      dueType: "Apply by"
    },
    {
      id: 17,
      title: "Progressive Web Apps Summit",
      organizer: "PWA Alliance",
      location: "Las Vegas, NV",
      eventDate: "2025-08-12",
      lastApplyDate: "2024-04-15",
      type: "Technical Talk",
      topics: ["PWA", "Web Development", "Performance"],
      compensation: "$5,000",
      matchScore: 90,
      description: "Present on Progressive Web Apps development strategies.",
      dueType: "Apply by"
    },
    {
      id: 18,
      title: "Microservices Architecture Conference",
      organizer: "Distributed Systems Group",
      location: "Atlanta, GA",
      eventDate: "2025-09-18",
      lastApplyDate: "2024-04-20",
      type: "Keynote",
      topics: ["Microservices", "Architecture", "Distributed Systems"],
      compensation: "$6,800",
      matchScore: 92,
      description: "Deliver keynote on microservices architecture patterns.",
      dueType: "Apply by"
    },
    {
      id: 19,
      title: "WebAssembly Performance Workshop",
      organizer: "WASM Performance Lab",
      location: "Berlin, Germany",
      eventDate: "2025-10-25",
      lastApplyDate: "2024-04-25",
      type: "Workshop",
      topics: ["WebAssembly", "Performance", "Optimization"],
      compensation: "$4,400",
      matchScore: 85,
      description: "Lead a WebAssembly performance optimization workshop.",
      dueType: "Apply by"
    },
    {
      id: 20,
      title: "Edge Computing Technologies Summit",
      organizer: "Edge Tech Consortium",
      location: "Tokyo, Japan",
      eventDate: "2025-11-20",
      lastApplyDate: "2024-05-01",
      type: "Panel Discussion",
      topics: ["Edge Computing", "IoT", "5G"],
      compensation: "$5,600",
      matchScore: 89,
      description: "Participate in edge computing technologies panel.",
      dueType: "Apply by"
    },
    {
      id: 21,
      title: "Artificial Intelligence Ethics Conference",
      organizer: "AI Ethics Institute",
      location: "Cambridge, MA",
      eventDate: "2025-12-15",
      lastApplyDate: "2024-05-10",
      type: "Technical Talk",
      topics: ["AI Ethics", "Machine Learning", "Responsible AI"],
      compensation: "$6,500",
      matchScore: 94,
      description: "Present on ethical considerations in AI development.",
      dueType: "Apply by"
    },
    {
      id: 22,
      title: "Quantum Computing Workshop",
      organizer: "Quantum Research Consortium",
      location: "Palo Alto, CA",
      eventDate: "2026-01-22",
      lastApplyDate: "2024-05-15",
      type: "Workshop",
      topics: ["Quantum Computing", "Algorithms", "Research"],
      compensation: "$7,200",
      matchScore: 96,
      description: "Conduct an advanced quantum computing workshop.",
      dueType: "Apply by"
    }
  ];

  const calendarOpportunities = [
    {
      id: 1,
      title: "AI Summit 2025",
      location: "San Francisco, CA",
      eventDate: "2025-09-28",
      status: 'accepted' as const,
      matchScore: 95,
      compensation: "$5,200"
    }
  ];

  // Filtering logic
  const filteredMatches = useMemo(() => {
    return allMatches.filter(match => {
      // Search filter
      if (searchQuery && !match.title.toLowerCase().includes(searchQuery.toLowerCase()) && 
          !match.organizer.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }
      
      // Location filter
      if (locationFilter !== 'all' && !match.location.toLowerCase().includes(locationFilter.toLowerCase())) {
        return false;
      }
      
      // Event date filter
      if (eventDateFilter !== 'all') {
        const eventDate = new Date(match.eventDate);
        const now = new Date();
        const diffDays = Math.ceil((eventDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        
        if (eventDateFilter === 'next30' && diffDays > 30) return false;
        if (eventDateFilter === 'next60' && diffDays > 60) return false;
        if (eventDateFilter === 'next90' && diffDays > 90) return false;
      }
      
      // Topic filter
      if (topicFilter !== 'all' && !match.topics.some(topic => 
        topic.toLowerCase().includes(topicFilter.toLowerCase()))) {
        return false;
      }
      
      // Compensation filter
      if (compensationFilter !== 'all') {
        const compensation = parseInt(match.compensation.replace(/[$,]/g, ''));
        if (compensationFilter === '0-3000' && compensation > 3000) return false;
        if (compensationFilter === '3000-5000' && (compensation <= 3000 || compensation > 5000)) return false;
        if (compensationFilter === '5000+' && compensation <= 5000) return false;
      }
      
      return true;
    });
  }, [allMatches, searchQuery, locationFilter, eventDateFilter, topicFilter, compensationFilter]);

  const filteredTasks = useMemo(() => {
    return allTasks.filter(task => {
      // Search filter
      if (searchQuery && !task.title.toLowerCase().includes(searchQuery.toLowerCase()) && 
          !task.organizer.toLowerCase().includes(searchQuery.toLowerCase())) {
        return false;
      }
      
      // Location filter
      if (locationFilter !== 'all' && !task.location.toLowerCase().includes(locationFilter.toLowerCase())) {
        return false;
      }
      
      // Event date filter
      if (eventDateFilter !== 'all') {
        const eventDate = new Date(task.eventDate);
        const now = new Date();
        const diffDays = Math.ceil((eventDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
        
        if (eventDateFilter === 'next30' && diffDays > 30) return false;
        if (eventDateFilter === 'next60' && diffDays > 60) return false;
        if (eventDateFilter === 'next90' && diffDays > 90) return false;
      }
      
      // Topic filter
      if (topicFilter !== 'all' && !task.topics.some(topic => 
        topic.toLowerCase().includes(topicFilter.toLowerCase()))) {
        return false;
      }
      
      // Compensation filter
      if (compensationFilter !== 'all') {
        const compensation = parseInt(task.compensation.replace(/[$,]/g, ''));
        if (compensationFilter === '0-3000' && compensation > 3000) return false;
        if (compensationFilter === '3000-5000' && (compensation <= 3000 || compensation > 5000)) return false;
        if (compensationFilter === '5000+' && compensation <= 5000) return false;
      }
      
      return true;
    });
  }, [allTasks, searchQuery, locationFilter, eventDateFilter, topicFilter, compensationFilter]);

  // Pagination logic
  const paginatedMatches = useMemo(() => {
    const startIndex = (matchesCurrentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredMatches.slice(startIndex, endIndex);
  }, [filteredMatches, matchesCurrentPage, itemsPerPage]);

  const paginatedTasks = useMemo(() => {
    const startIndex = (tasksCurrentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredTasks.slice(startIndex, endIndex);
  }, [filteredTasks, tasksCurrentPage, itemsPerPage]);

  const matchesTotalPages = Math.ceil(filteredMatches.length / itemsPerPage);
  const tasksTotalPages = Math.ceil(filteredTasks.length / itemsPerPage);

  const handleShowOpportunityDetail = (opportunity: any) => {
    setSelectedOpportunity(opportunity);
    setIsOpportunityDetailOpen(true);
  };

  const handleShowReasonModal = (actionType: string, itemId: number, itemType: 'match' | 'task') => {
    const titles = {
      accept: 'Reason of Acceptance',
      reject: 'Reason of Rejection',  
      interested: 'Reason of Interest'
    };
    
    setReasonModalTitle(titles[actionType as keyof typeof titles] || 'Provide Reason');
    setPendingAction({ type: `${actionType}_${itemType}`, id: itemId });
    setIsReasonModalOpen(true);
  };

  const handleReasonSubmit = (reason: string) => {
    if (!pendingAction) return;
    
    const { type, id } = pendingAction;
    
    // Handle different action types
    if (type === 'accept_match') {
      handleAccept(id);
    } else if (type === 'reject_match') {
      handleRejectMatch(id);
    } else if (type === 'interested_match') {
      handleInterestedMatch(id);
    } else if (type === 'accept_task') {
      handleAcceptTask(id);
    } else if (type === 'reject_task') {
      handleRejectTask(id);
    } else if (type === 'interested_task') {
      handleInterestedTask(id);
    }
    
    // Log the reason (in real app, send to backend)
    console.log(`${type} for item ${id}: ${reason}`);
    
    setPendingAction(null);
  };

  const handleInterestedMatch = (matchId: number) => {
    setInterestedMatches(prev => new Set([...prev, matchId]));
    setAcceptedMatches(prev => {
      const newSet = new Set(prev);
      newSet.delete(matchId);
      return newSet;
    });
    setRejectedMatches(prev => {
      const newSet = new Set(prev);
      newSet.delete(matchId);
      return newSet;
    });
  };

  const handleInterestedTask = (taskId: number) => {
    setInterestedTasks(prev => new Set([...prev, taskId]));
    setAcceptedTasks(prev => {
      const newSet = new Set(prev);
      newSet.delete(taskId);
      return newSet;
    });
    setRejectedTasks(prev => {
      const newSet = new Set(prev);
      newSet.delete(taskId);
      return newSet;
    });
  };

  const handleAccept = (matchId: number) => {
    setAcceptedMatches(prev => new Set([...prev, matchId]));
    setRejectedMatches(prev => {
      const newSet = new Set(prev);
      newSet.delete(matchId);
      return newSet;
    });
    setPendingMatches(prev => {
      const newSet = new Set(prev);
      newSet.delete(matchId);
      return newSet;
    });
    setInterestedMatches(prev => {
      const newSet = new Set(prev);
      newSet.delete(matchId);
      return newSet;
    });
  };

  const handleAcceptTask = (taskId: number) => {
    setAcceptedTasks(prev => new Set([...prev, taskId]));
    setRejectedTasks(prev => {
      const newSet = new Set(prev);
      newSet.delete(taskId);
      return newSet;
    });
    setPendingTasks(prev => {
      const newSet = new Set(prev);
      newSet.delete(taskId);
      return newSet;
    });
    setInterestedTasks(prev => {
      const newSet = new Set(prev);
      newSet.delete(taskId);
      return newSet;
    });
  };

  const handleRejectMatch = (matchId: number) => {
    setRejectedMatches(prev => new Set([...prev, matchId]));
    setAcceptedMatches(prev => {
      const newSet = new Set(prev);
      newSet.delete(matchId);
      return newSet;
    });
    setPendingMatches(prev => {
      const newSet = new Set(prev);
      newSet.delete(matchId);
      return newSet;
    });
    setInterestedMatches(prev => {
      const newSet = new Set(prev);
      newSet.delete(matchId);
      return newSet;
    });
  };

  const handleRejectTask = (taskId: number) => {
    setRejectedTasks(prev => new Set([...prev, taskId]));
    setAcceptedTasks(prev => {
      const newSet = new Set(prev);
      newSet.delete(taskId);
      return newSet;
    });
    setPendingTasks(prev => {
      const newSet = new Set(prev);
      newSet.delete(taskId);
      return newSet;
    });
    setInterestedTasks(prev => {
      const newSet = new Set(prev);
      newSet.delete(taskId);
      return newSet;
    });
  };

  const handleBookMatch = (matchId: number) => {
    setBookedMatches(prev => new Set([...prev, matchId]));
    setNotBookedMatches(prev => {
      const newSet = new Set(prev);
      newSet.delete(matchId);
      return newSet;
    });
  };

  const handleNotBookMatch = (matchId: number) => {
    setNotBookedMatches(prev => new Set([...prev, matchId]));
    setBookedMatches(prev => {
      const newSet = new Set(prev);
      newSet.delete(matchId);
      return newSet;
    });
  };

  const handleBookTask = (taskId: number) => {
    setBookedTasks(prev => new Set([...prev, taskId]));
    setNotBookedTasks(prev => {
      const newSet = new Set(prev);
      newSet.delete(taskId);
      return newSet;
    });
  };

  const handleNotBookTask = (taskId: number) => {
    setNotBookedTasks(prev => new Set([...prev, taskId]));
    setBookedTasks(prev => {
      const newSet = new Set(prev);
      newSet.delete(taskId);
      return newSet;
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-foreground">Today's Dashboard</h2>
          <p className="text-foreground-muted">Your marching orders for maximum impact</p>
        </div>
        <Button 
          variant="outline" 
          className="flex items-center gap-2"
          onClick={() => setIsCalendarOpen(true)}
        >
          <CalendarIcon className="h-4 w-4" />
          View Full Calendar
        </Button>
      </div>

      {/* Filters */}
      <Card className="bg-surface border-border">
        <CardHeader>
          <CardTitle className="text-foreground flex items-center gap-2">
            <Filter className="h-5 w-5 text-primary" />
            Filters
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            {/* Search */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Search</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground-muted" />
                <Input
                  placeholder="Search opportunities..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>

            {/* Location */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Location</label>
              <Select value={locationFilter} onValueChange={setLocationFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All locations" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Locations</SelectItem>
                  <SelectItem value="california">California</SelectItem>
                  <SelectItem value="new york">New York</SelectItem>
                  <SelectItem value="seattle">Seattle</SelectItem>
                  <SelectItem value="austin">Austin</SelectItem>
                  <SelectItem value="chicago">Chicago</SelectItem>
                  <SelectItem value="miami">Miami</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Event Date */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Event Date</label>
              <Select value={eventDateFilter} onValueChange={setEventDateFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All dates" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Dates</SelectItem>
                  <SelectItem value="next30">Next 30 days</SelectItem>
                  <SelectItem value="next60">Next 60 days</SelectItem>
                  <SelectItem value="next90">Next 90 days</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Topic */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Topic</label>
              <Select value={topicFilter} onValueChange={setTopicFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All topics" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Topics</SelectItem>
                  <SelectItem value="ai">AI & Machine Learning</SelectItem>
                  <SelectItem value="react">React & Frontend</SelectItem>
                  <SelectItem value="cloud">Cloud Computing</SelectItem>
                  <SelectItem value="devops">DevOps</SelectItem>
                  <SelectItem value="mobile">Mobile Development</SelectItem>
                  <SelectItem value="blockchain">Blockchain</SelectItem>
                  <SelectItem value="data">Data Science</SelectItem>
                  <SelectItem value="cybersecurity">Cybersecurity</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Compensation */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">Compensation</label>
              <Select value={compensationFilter} onValueChange={setCompensationFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All ranges" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Ranges</SelectItem>
                  <SelectItem value="0-3000">$0 - $3,000</SelectItem>
                  <SelectItem value="3000-5000">$3,000 - $5,000</SelectItem>
                  <SelectItem value="5000+">$5,000+</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* New Matches */}
      <Card className="bg-surface border-border">
        <CardHeader>
          <CardTitle className="text-foreground flex items-center gap-2">
            <Zap className="h-5 w-5 text-primary" />
            New Matches Since Last Login
            <Badge variant="secondary" className="bg-primary/10 text-primary border-primary/20">
              {filteredMatches.length} New
            </Badge>
          </CardTitle>
          <CardDescription className="text-foreground-muted">
            Fresh opportunities matched to your profile
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {paginatedMatches.map((match) => (
            <Card 
              key={match.id} 
              className="bg-card border-border shadow-sm cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleShowOpportunityDetail(match)}
            >
              <CardContent className="p-0">
                <div className="flex">
                  {/* Match Score Circle - Left Side */}
                  <div className="flex flex-col items-center justify-center p-6 bg-surface-elevated border-r border-border-subtle rounded-md">
                    <div className="relative w-16 h-16 mb-2">
                      <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                        <path
                          d="M18 2.0845
                            a 15.9155 15.9155 0 0 1 0 31.831
                            a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="hsl(var(--muted))"
                          strokeWidth="2"
                        />
                        <path
                          d="M18 2.0845
                            a 15.9155 15.9155 0 0 1 0 31.831
                            a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="hsl(var(--primary))"
                          strokeWidth="2.5"
                          strokeDasharray={`${match.matchScore}, 100`}
                          className="drop-shadow-sm"
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-lg font-bold text-primary">{match.matchScore}%</span>
                      </div>
                    </div>
                    <span className="text-xs font-medium text-foreground-muted uppercase tracking-wide text-center">Match Score</span>
                  </div>

                  {/* Main Content Area */}
                  <div className="flex-1 p-5">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-foreground mb-1">{match.title}</h3>
                        <p className="text-sm text-foreground-muted">Organized by {match.organizer}</p>
                      </div>
                      
                       {/* Status Indicator - Top Right */}
                       <div className="flex gap-1 ml-4">
                         {acceptedMatches.has(match.id) && (
                           <Badge variant="outline" className="border-green-600 text-green-600 bg-green-50">
                             <CheckCircle className="h-3 w-3 mr-1" />
                             Accepted
                           </Badge>
                         )}
                         {interestedMatches.has(match.id) && (
                           <Badge variant="outline" className="border-yellow-600 text-yellow-600 bg-yellow-50">
                             <Heart className="h-3 w-3 mr-1" />
                             Interested
                           </Badge>
                         )}
                         {rejectedMatches.has(match.id) && (
                           <Badge variant="outline" className="border-red-600 text-red-600 bg-red-50">
                             <XCircle className="h-3 w-3 mr-1" />
                             Rejected
                           </Badge>
                         )}
                         {pendingMatches.has(match.id) && (
                           <Badge variant="outline" className="border-blue-600 text-blue-600 bg-blue-50">
                             <Pause className="h-3 w-3 mr-1" />
                             Pending
                           </Badge>
                         )}
                       </div>
                    </div>
                    
                    {/* Meta Information - Side by side with proper spacing */}
                    <div className="flex items-center gap-6 text-sm text-foreground-muted mb-4 flex-wrap">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-primary/60 flex-shrink-0" />
                        <span>{match.location}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CalendarIcon className="h-4 w-4 text-primary/60 flex-shrink-0" />
                        <span>Event: {new Date(match.eventDate).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-primary/60 flex-shrink-0" />
                        <span>Apply by: {new Date(match.lastApplyDate).toLocaleDateString()}</span>
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-sm text-foreground-muted mb-4 leading-relaxed">
                      {match.description}
                    </p>

                    {/* Tags Row */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {match.topics.map((topic, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary border border-primary/20"
                        >
                          {topic}
                        </span>
                      ))}
                    </div>

                    {/* Bottom Row - Show Apply button only if accepted */}
                    <div className="flex items-center justify-between pt-3 border-t border-border-subtle">
                      <div className="flex items-center gap-4">
                        <div className="text-lg font-bold text-foreground">
                          {match.compensation}
                        </div>
                        <div className="text-sm text-foreground-muted">
                          compensation
                        </div>
                      </div>
                      
                       <div className="flex items-center gap-2">
                         {/* Action buttons based on status */}
                         {!acceptedMatches.has(match.id) && !rejectedMatches.has(match.id) && !pendingMatches.has(match.id) && !interestedMatches.has(match.id) && (
                           <>
                             <Button
                               size="sm"
                               variant="outline"
                               className="border-[#16A34A] bg-white text-green-600 hover:bg-green-50"
                               onClick={(e) => {
                                 e.stopPropagation();
                                 handleShowReasonModal('accept', match.id, 'match');
                               }}
                             >
                               <CheckCircle className="h-4 w-4 mr-1" />
                               Accept
                             </Button>
                             <Button
                               size="sm"
                               variant="outline"
                               className="border-[#CA8A04] bg-white text-yellow-600 hover:bg-yellow-50"
                               onClick={(e) => {
                                 e.stopPropagation();
                                 handleShowReasonModal('interested', match.id, 'match');
                               }}
                             >
                               <Heart className="h-4 w-4 mr-1" />
                               Interested
                             </Button>
                             <Button
                               size="sm"
                               variant="outline"
                               className="border-[#DC2626] bg-white text-red-600 hover:bg-red-50"
                               onClick={(e) => {
                                 e.stopPropagation();
                                 handleShowReasonModal('reject', match.id, 'match');
                               }}
                             >
                               <XCircle className="h-4 w-4 mr-1" />
                               Reject
                             </Button>
                           </>
                         )}
                         
                         {acceptedMatches.has(match.id) && 
                          !bookedMatches.has(match.id) && 
                          !notBookedMatches.has(match.id) && (
                           <>
                             <Button
                               variant="outline"
                               size="sm"
                               className="border-blue-600 text-blue-600 hover:bg-blue-50"
                               onClick={(e) => {
                                 e.stopPropagation();
                                 handleBookMatch(match.id);
                               }}
                             >
                               <CheckCircle className="h-4 w-4 mr-1" />
                               Book
                             </Button>
                             <Button
                               variant="outline"
                               size="sm"
                               className="border-gray-600 text-gray-600 hover:bg-gray-50"
                               onClick={(e) => {
                                 e.stopPropagation();
                                 handleNotBookMatch(match.id);
                               }}
                             >
                               <XCircle className="h-4 w-4 mr-1" />
                               Not Booked
                             </Button>
                           </>
                         )}
                         
                         {bookedMatches.has(match.id) && (
                           <Badge variant="outline" className="border-blue-600 text-blue-600 bg-blue-50">
                             <CheckCircle className="h-3 w-3 mr-1" />
                             Booked
                           </Badge>
                         )}
                         
                         {notBookedMatches.has(match.id) && (
                           <Badge variant="outline" className="border-gray-600 text-gray-600 bg-gray-50">
                             <XCircle className="h-3 w-3 mr-1" />
                             Not Booked
                           </Badge>
                         )}
                       </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </CardContent>
        
        {/* Matches Pagination */}
        {matchesTotalPages > 1 && (
          <div className="px-6 pb-6">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious 
                    onClick={() => setMatchesCurrentPage(Math.max(1, matchesCurrentPage - 1))}
                    className={matchesCurrentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>
                
                {Array.from({ length: matchesTotalPages }, (_, i) => i + 1).map((pageNum) => (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      onClick={() => setMatchesCurrentPage(pageNum)}
                      isActive={pageNum === matchesCurrentPage}
                      className="cursor-pointer"
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                ))}
                
                <PaginationItem>
                  <PaginationNext 
                    onClick={() => setMatchesCurrentPage(Math.min(matchesTotalPages, matchesCurrentPage + 1))}
                    className={matchesCurrentPage === matchesTotalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </Card>

      {/* Tasks Due */}
      <Card className="bg-surface border-border">
        <CardHeader>
          <CardTitle className="text-foreground flex items-center gap-2">
            <Clock className="h-5 w-5 text-primary" />
            Tasks Due Today & Next 72 Hours
          </CardTitle>
          <CardDescription className="text-foreground-muted">
            Priority actions to keep your pipeline moving
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {paginatedTasks.map((task) => (
            <Card 
              key={task.id} 
              className="bg-card   border-border shadow-sm cursor-pointer hover:shadow-md transition-shadow"
              onClick={() => handleShowOpportunityDetail(task)}
            >
              <CardContent className="p-0">
                <div className="flex">
                  {/* Match Score Circle - Left Side */}
                  <div className="flex flex-col items-center justify-center p-6 bg-surface-elevated border-r border-border-subtle rounded-md">
                    <div className="relative w-16 h-16 mb-2">
                      <svg className="w-16 h-16 transform -rotate-90" viewBox="0 0 36 36">
                        <path
                          d="M18 2.0845
                            a 15.9155 15.9155 0 0 1 0 31.831
                            a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="hsl(var(--muted))"
                          strokeWidth="2"
                        />
                        <path
                          d="M18 2.0845
                            a 15.9155 15.9155 0 0 1 0 31.831
                            a 15.9155 15.9155 0 0 1 0 -31.831"
                          fill="none"
                          stroke="hsl(var(--primary))"
                          strokeWidth="2.5"
                          strokeDasharray={`${task.matchScore}, 100`}
                          className="drop-shadow-sm"
                        />
                      </svg>
                      <div className="absolute inset-0 flex items-center justify-center">
                        <span className="text-lg font-bold text-primary">{task.matchScore}%</span>
                      </div>
                    </div>
                    <span className="text-xs font-medium text-foreground-muted uppercase tracking-wide text-center">Match Score</span>
                  </div>

                  {/* Main Content Area */}
                  <div className="flex-1 p-5">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-foreground mb-1">{task.title}</h3>
                        <p className="text-sm text-foreground-muted">Organized by {task.organizer}</p>
                      </div>
                      
                       {/* Status Indicator - Top Right */}
                       <div className="flex gap-1 ml-4">
                         {acceptedTasks.has(task.id) && (
                           <Badge variant="outline" className="border-green-600 text-green-600 bg-green-50">
                             <CheckCircle className="h-3 w-3 mr-1" />
                             Accepted
                           </Badge>
                         )}
                         {interestedTasks.has(task.id) && (
                           <Badge variant="outline" className="border-yellow-600 text-yellow-600 bg-yellow-50">
                             <Heart className="h-3 w-3 mr-1" />
                             Interested
                           </Badge>
                         )}
                         {rejectedTasks.has(task.id) && (
                           <Badge variant="outline" className="border-red-600 text-red-600 bg-red-50">
                             <XCircle className="h-3 w-3 mr-1" />
                             Rejected
                           </Badge>
                         )}
                         {pendingTasks.has(task.id) && (
                           <Badge variant="outline" className="border-blue-600 text-blue-600 bg-blue-50">
                             <Pause className="h-3 w-3 mr-1" />
                             Pending
                           </Badge>
                         )}
                       </div>
                    </div>
                    
                    {/* Meta Information - Side by side with highlighted apply by date */}
                    <div className="flex items-center gap-6 text-sm text-foreground-muted mb-4 flex-wrap">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-primary/60 flex-shrink-0" />
                        <span>{task.location}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <CalendarIcon className="h-4 w-4 text-primary/60 flex-shrink-0" />
                        <span>Event: {new Date(task.eventDate).toLocaleDateString()}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-destructive flex-shrink-0" />
                        <Badge variant="destructive" className="bg-destructive text-destructive-foreground font-medium px-2 py-1">
                          Apply by: {new Date(task.lastApplyDate).toLocaleDateString()}
                        </Badge>
                      </div>
                    </div>

                    {/* Description */}
                    <p className="text-sm text-foreground-muted mb-4 leading-relaxed">
                      {task.description}
                    </p>

                    {/* Tags Row */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {task.topics.map((topic, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary border border-primary/20"
                        >
                          {topic}
                        </span>
                      ))}
                    </div>

                    {/* Bottom Row - Show Apply button only if accepted */}
                    <div className="flex items-center justify-between pt-3 border-t border-border-subtle">
                      <div className="flex items-center gap-4">
                        <div className="text-lg font-bold text-foreground">
                          {task.compensation}
                        </div>
                        <div className="text-sm text-foreground-muted">
                          compensation
                        </div>
                      </div>
                      
                       <div className="flex items-center gap-2">
                         {/* Action buttons based on status */}
                         {!acceptedTasks.has(task.id) && !rejectedTasks.has(task.id) && !pendingTasks.has(task.id) && !interestedTasks.has(task.id) && (
                           <>
                             <Button
                               size="sm"
                               variant="outline"
                               className="border-green-600 text-green-600 hover:bg-green-50"
                               onClick={(e) => {
                                 e.stopPropagation();
                                 handleShowReasonModal('accept', task.id, 'task');
                               }}
                             >
                               <CheckCircle className="h-4 w-4 mr-1" />
                               Accept
                             </Button>
                             <Button
                               size="sm"
                               variant="outline"
                               className="border-yellow-600 text-yellow-600 hover:bg-yellow-50"
                               onClick={(e) => {
                                 e.stopPropagation();
                                 handleShowReasonModal('interested', task.id, 'task');
                               }}
                             >
                               <Heart className="h-4 w-4 mr-1" />
                               Interested
                             </Button>
                             <Button
                               size="sm"
                               variant="outline"
                               className="border-red-600 text-red-600 hover:bg-red-50"
                               onClick={(e) => {
                                 e.stopPropagation();
                                 handleShowReasonModal('reject', task.id, 'task');
                               }}
                             >
                               <XCircle className="h-4 w-4 mr-1" />
                               Reject
                             </Button>
                           </>
                         )}
                         
                         {acceptedTasks.has(task.id) && 
                          !bookedTasks.has(task.id) && 
                          !notBookedTasks.has(task.id) && (
                           <>
                             <Button
                               variant="outline"
                               size="sm"
                               className="border-blue-600 text-blue-600 hover:bg-blue-50"
                               onClick={(e) => {
                                 e.stopPropagation();
                                 handleBookTask(task.id);
                               }}
                             >
                               <CheckCircle className="h-4 w-4 mr-1" />
                               Book
                             </Button>
                             <Button
                               variant="outline"
                               size="sm"
                               className="border-gray-600 text-gray-600 hover:bg-gray-50"
                               onClick={(e) => {
                                 e.stopPropagation();
                                 handleNotBookTask(task.id);
                               }}
                             >
                               <XCircle className="h-4 w-4 mr-1" />
                               Not Booked
                             </Button>
                           </>
                         )}
                         
                         {bookedTasks.has(task.id) && (
                           <Badge variant="outline" className="border-blue-600 text-blue-600 bg-blue-50">
                             <CheckCircle className="h-3 w-3 mr-1" />
                             Booked
                           </Badge>
                         )}
                         
                         {notBookedTasks.has(task.id) && (
                           <Badge variant="outline" className="border-gray-600 text-gray-600 bg-gray-50">
                             <XCircle className="h-3 w-3 mr-1" />
                             Not Booked
                           </Badge>
                         )}
                       </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </CardContent>
        
        {/* Tasks Pagination */}
        {tasksTotalPages > 1 && (
          <div className="px-6 pb-6">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious 
                    onClick={() => setTasksCurrentPage(Math.max(1, tasksCurrentPage - 1))}
                    className={tasksCurrentPage === 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>
                
                {Array.from({ length: tasksTotalPages }, (_, i) => i + 1).map((pageNum) => (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      onClick={() => setTasksCurrentPage(pageNum)}
                      isActive={pageNum === tasksCurrentPage}
                      className="cursor-pointer"
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                ))}
                
                <PaginationItem>
                  <PaginationNext 
                    onClick={() => setTasksCurrentPage(Math.min(tasksTotalPages, tasksCurrentPage + 1))}
                    className={tasksCurrentPage === tasksTotalPages ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        )}
      </Card>

      {/* Calendar Modal */}
      <CalendarModal 
        isOpen={isCalendarOpen}
        onClose={() => setIsCalendarOpen(false)}
        opportunities={calendarOpportunities}
      />

      {/* Opportunity Detail Modal */}
      {selectedOpportunity && (
        <OpportunityDetailModal
          isOpen={isOpportunityDetailOpen}
          onClose={() => {
            setIsOpportunityDetailOpen(false);
            setSelectedOpportunity(null);
          }}
          opportunity={selectedOpportunity}
          acceptedOpportunities={new Set([...acceptedMatches, ...acceptedTasks])}
          bookedOpportunities={new Set([...bookedMatches, ...bookedTasks])}
          notBookedOpportunities={new Set([...notBookedMatches, ...notBookedTasks])}
          interestedOpportunities={new Set([...interestedMatches, ...interestedTasks])}
          rejectedOpportunities={new Set([...rejectedMatches, ...rejectedTasks])}
          onAccept={(id) => {
            // Determine if it's a match or task based on the selected opportunity
            const isMatch = allMatches.some(match => match.id === id);
            if (isMatch) {
              handleAccept(id);
            } else {
              handleAcceptTask(id);
            }
          }}
          onReject={(id) => {
            const isMatch = allMatches.some(match => match.id === id);
            if (isMatch) {
              handleRejectMatch(id);
            } else {
              handleRejectTask(id);
            }
          }}
          onInterested={(id) => {
            const isMatch = allMatches.some(match => match.id === id);
            if (isMatch) {
              handleInterestedMatch(id);
            } else {
              handleInterestedTask(id);
            }
          }}
          onBook={(id) => {
            const isMatch = allMatches.some(match => match.id === id);
            if (isMatch) {
              handleBookMatch(id);
            } else {
              handleBookTask(id);
            }
          }}
          onNotBook={(id) => {
            const isMatch = allMatches.some(match => match.id === id);
            if (isMatch) {
              handleNotBookMatch(id);
            } else {
              handleNotBookTask(id);
            }
          }}
        />
      )}

      {/* Reason Modal */}
      <ReasonModal
        isOpen={isReasonModalOpen}
        onClose={() => setIsReasonModalOpen(false)}
        onSubmit={handleReasonSubmit}
        title={reasonModalTitle}
      />
    </div>
  );
}