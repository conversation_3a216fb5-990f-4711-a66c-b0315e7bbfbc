import React from 'react';
import { Button, ButtonProps } from './ui/button';
import { usePermissions } from '../hooks/usePermissions';
import { Permission } from '../store/slices/rbacSlice';

interface PermissionButtonProps extends ButtonProps {
  permission?: Permission;
  permissions?: Permission[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  disabledMessage?: string;
}

/**
 * Button component that is disabled or hidden based on user permissions
 */
const PermissionButton: React.FC<PermissionButtonProps> = ({
  children,
  permission,
  permissions = [],
  requireAll = false,
  fallback = null,
  disabledMessage = "You don't have permission to perform this action",
  disabled,
  ...buttonProps
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions } = usePermissions();

  // Determine which permissions to check
  const permissionsToCheck = permission ? [permission] : permissions;

  // Check permissions
  const hasRequiredPermissions = permissionsToCheck.length === 0 || 
    (requireAll
      ? hasAllPermissions(permissionsToCheck)
      : hasAnyPermission(permissionsToCheck));

  // If user doesn't have required permissions, show fallback or disabled button
  if (!hasRequiredPermissions) {
    if (fallback) {
      return <>{fallback}</>;
    }
    
    return (
      <Button
        {...buttonProps}
        disabled={true}
        title={disabledMessage}
        className={`${buttonProps.className || ''} opacity-50 cursor-not-allowed`}
      >
        {children}
      </Button>
    );
  }

  // User has required permissions, render normal button
  return (
    <Button {...buttonProps} disabled={disabled}>
      {children}
    </Button>
  );
};

export default PermissionButton;
