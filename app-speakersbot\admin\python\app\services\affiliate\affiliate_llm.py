"""
Affiliate LLM Extractor
LLM-based data extraction for affiliate scraping using OpenAI
"""

import json
from typing import Dict, Any
from bs4 import BeautifulSoup
from app.config.logger import get_logger
from app.config.config import config
logger = get_logger(__name__, file_name="scraper.log")
import openai

class AffiliateLLMExtractor:
    """LLM-based data extraction for affiliate scraping"""
    def __init__(self):
        """Initialize LLM extractor with OpenAI configuration"""
        self.client = None
        self.model = "gpt-4o-mini"  # Cost-effective model
        self.max_tokens = 4000
        self.temperature = 0.1  # Low temperature for consistent extraction
        self._initialize_openai()
    
    def _initialize_openai(self):
        """Initialize OpenAI client with configuration"""
        try:
            api_key = config.OPENAI_API_KEY
            if not api_key:
                logger.error("❌ OpenAI API key not found in configuration")
                return
            # Initialize OpenAI client
            openai.api_key = api_key
            self.client = openai
            # Override model if specified in config
            if hasattr(config, 'OPENAI_MODEL') and config.OPENAI_MODEL:
                self.model = config.OPENAI_MODEL
        except Exception as e:
            logger.error(f"❌ Failed to initialize OpenAI client: {e}")
            logger.error(f"🔍 Error details: {str(e)}", exc_info=True)
            self.client = None
    
    def extract_affiliate_data_with_llm(self, soup: BeautifulSoup, url: str) -> Dict[str, Any]:
        """
        Extract affiliate data using LLM from webpage content
        Args:
            soup (BeautifulSoup): Parsed HTML content
            url (str): The URL being scraped
        Returns:
            Dict[str, Any]: Extracted affiliate data
        """
        if not self.client:
            return {}
        
        try:
            page_content = self._prepare_content_for_llm(soup, url)
            prompt = self._create_extraction_prompt(page_content, url)
            response = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": "You are an expert data extraction assistant specializing in affiliate program information. Extract the requested data accurately and return it in valid JSON format."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                max_tokens=self.max_tokens,
                temperature=self.temperature,
                response_format={"type": "json_object"}
            )
            # Parse LLM response
            raw_response = response.choices[0].message.content
            llm_data = self._parse_llm_response(raw_response)
            return llm_data
        except Exception as e:
            logger.error(f"❌ Error in LLM extraction for {url}: {e}")
            logger.error(f"🔍 Error details: {str(e)}", exc_info=True)
            return {}
    
    def _prepare_content_for_llm(self, soup: BeautifulSoup, url: str) -> str:
        """
        Prepare webpage content for LLM processing
        Args:
            soup (BeautifulSoup): Parsed HTML content
            url (str): The URL being scraped
        Returns:
            str: Cleaned content for LLM
        """
        try:
            # Remove script and style elements
            for script in soup(["script", "style", "nav", "footer", "header"]):
                script.decompose()
            # Get text content
            text_content = soup.get_text()
            # Clean up text
            lines = (line.strip() for line in text_content.splitlines())
            chunks = (phrase.strip() for line in lines for phrase in line.split("  "))
            text = ' '.join(chunk for chunk in chunks if chunk)
            # Limit content length to avoid token limits
            max_length = 8000  # Conservative limit for GPT-4o-mini
            if len(text) > max_length:
                text = text[:max_length] + "..."
            # Extract key metadata
            title = soup.find('title')
            title_text = title.get_text().strip() if title else ""
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            meta_desc_text = meta_desc.get('content', '').strip() if meta_desc else ""
            
            # Combine metadata with content (no need for images/CSS for these 8 fields)
            prepared_content = f"""
                URL: {url}
                Title: {title_text}
                Meta Description: {meta_desc_text}

                Content:
                {text}
                """            
            return prepared_content
            
        except Exception as e:
            logger.error(f"❌ Error preparing content for LLM: {e}")
            logger.error(f"🔍 Error details: {str(e)}", exc_info=True)
            return f"URL: {url}\nContent: Error preparing content"
    
    def _create_extraction_prompt(self, content: str, url: str) -> str:
        """
        Create extraction prompt for LLM
        Args:
            content (str): Prepared webpage content
            url (str): The URL being scraped
        Returns:
            str: Complete extraction prompt
        """
        prompt = f"""
            Extract ONLY the following 8 specific fields from this website content. Return the data as a JSON object with the exact field names specified below.

            Website Content:
            {content}

            Extract these 8 specific fields and return as JSON:

            {{
                "meta_description": "Extract meta description from the page (use the provided meta description or extract from content)",
                "promo_channels": "Extract marketing channels used (look for social media mentions, platform names like Facebook, Twitter, Instagram, YouTube, LinkedIn, TikTok, Pinterest - semicolon-separated)",
                "reach_estimate": "Extract audience/reach estimates (look for numbers with 'users', 'customers', 'audience', 'reach', 'members', 'subscribers', 'community', 'network', 'thousands', 'millions')",
                "learning_objectives": "Extract 3 learning objectives or takeaways (look for 'learn', 'understand', 'discover', 'gain', 'master' - semicolon-separated)",
                "challenges_solved": "Extract 3 challenges this service solves (look for 'problem', 'challenge', 'struggle', 'difficulty', 'pain point' - semicolon-separated)",
                "overarching_offer": "Extract the main offer description (look for 'offer', 'provide', 'deliver', 'service', 'solution' - one paragraph)",
                "differentiator": "Extract what makes this service unique (look for 'unique', 'different', 'exclusive', 'only', 'special' - one paragraph)",
                "core_benefit": "Extract the main benefit in one sentence (look for 'benefit', 'advantage', 'value', 'help', 'get')"
            }}

            Instructions:
            - Extract ONLY these 8 fields - do not extract any other fields
            - If a field is not found or not applicable, use "Not found" as the value
            - For lists (learning_objectives, challenges_solved, promo_channels), separate items with semicolons
            - For reach_estimate, extract specific numbers and audience descriptions
            - For social media channels, look for mentions of platforms in the content
            - For reach estimates, look for numbers followed by words like "users", "members", "subscribers", "customers"
            - Keep text concise but informative
            - Return only valid JSON, no additional text
            """
        
        logger.info(f"📋 Extraction prompt created: {len(prompt)} characters")
        
        return prompt
    
    def _parse_llm_response(self, response_content: str) -> Dict[str, Any]:
        """
        Parse LLM response and validate JSON
        Args:
            response_content (str): Raw LLM response
        Returns:
            Dict[str, Any]: Parsed and validated data
        """
        try:
            # Parse JSON response
            data = json.loads(response_content)
            cleaned_data = {}
            # Define expected fields (only 8 fields for LLM)
            expected_fields = [
                "meta_description", "promo_channels", "reach_estimate",
                "learning_objectives", "challenges_solved", "overarching_offer",
                "differentiator", "core_benefit"
            ]
            # Clean and validate each field
            for field in expected_fields:
                value = data.get(field, "Not found")
                # Clean up common issues
                if isinstance(value, str):
                    value = value.strip()
                    if value.lower() in ["not found", "n/a", "none", "null", ""]:
                        value = "Not found"
                cleaned_data[field] = value
            return cleaned_data
        except json.JSONDecodeError as e:
            logger.error(f"❌ Failed to parse LLM response as JSON: {e}")
            return {}
        except Exception as e:
            logger.error(f"❌ Error parsing LLM response: {e}")
            logger.error(f"🔍 Error details: {str(e)}", exc_info=True)
            return {}