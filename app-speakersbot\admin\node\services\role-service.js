
const { RESPONSE_CODES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");
const { Roles, Permissions, Users } = require("../models");

const roleService = {};

// ------------------------- role-service -------------------------

/**
 * Create a new role with associated permissions.
 * Validates role name uniqueness and permission IDs before creation.
 * 
 * @param {Object} createReq - The request object containing role data
 * @param {Object} createReq.body - Role creation data
 * @param {string} createReq.body.name - Role name
 * @param {string} createReq.body.description - Role description
 * @param {Array<number>} createReq.body.permissionIds - Array of permission IDs
 * @returns {Promise<Object>} Result of role creation
 * @throws {CustomError} When role already exists or invalid permissions provided
 */
roleService.createRole = async (createReq) => {
    try {
        const { name, description, permissionIds } = createReq.body;

        // check if role already exists
        const isRoleExist = await Roles.findOne({ where: { name } });
        if (isRoleExist) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Role already exists");
        }

        // create new role
        const newRole = await Roles.create({ name, description });

        // find permissions by IDs
        const permissions = await Permissions.findAll({ where: { id: permissionIds } });
        if (!permissions.length) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "No valid permissions found for given IDs");
        }

        // associate permissions with the new role
        await newRole.setPermissions(permissions);

        return {
            status: true,
            message: "Role created successfully"
        };
    } catch (error) {
        console.error("Error creating role:", error);
        throw error;
    }
};

/**
 * Update an existing role and its permissions.
 * Updates role details and reassigns permissions.
 * 
 * @param {Object} updateReq - The request object containing update data
 * @param {number} updateReq.params.id - Role ID to update
 * @param {Object} updateReq.body - Update data
 * @param {string} [updateReq.body.name] - New role name
 * @param {string} [updateReq.body.description] - New role description
 * @param {Array<number>} [updateReq.body.permissionIds] - New permission IDs
 * @returns {Promise<Object>} Result of role update
 * @throws {CustomError} When role not found or invalid permissions provided
 */
roleService.updateRole = async (updateReq) => {
    try {
        const roleId = updateReq.params.id;
        const { name, description, permissionIds } = updateReq.body;

        const role = await Roles.findByPk(roleId);
        if (!role) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Role not found");
        }

        role.name = name || role.name;
        role.description = description || role.description;
        await role.save();

        const permissions = await Permissions.findAll({ where: { id: permissionIds } });
        if (!permissions.length) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "No valid permissions found for given IDs");
        }

        await role.setPermissions(permissions);
        return {
            status: true,
            message: "Role updated successfully"
        };
    } catch (error) {
        console.error("Error updating role:", error);
        throw error;
    }
};

/**
 * Delete a role (soft delete).
 * Checks if role is assigned to any active users before deletion.
 * 
 * @param {Object} deleteReq - The request object
 * @param {number} deleteReq.query.id - Role ID to delete
 * @returns {Promise<Object>} Result of role deletion
 * @throws {CustomError} When role not found or assigned to active users
 */
roleService.deleteRole = async (deleteReq) => {
    try {
        const roleId = deleteReq.query.id;
        console.log("Role ID to delete:", roleId);

        const role = await Roles.findByPk(roleId);
        if (!role) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Role not found");
        }

        // check if role is assigned to any users
        const usersWithRole = await Users.count({
            where: {
                role_id: roleId,
                status: "active" // only active users
            }
        });

        if (usersWithRole > 0) {
            throw new CustomError(
                RESPONSE_CODES.BAD_REQUEST,
                `Cannot delete role. It is currently assigned to ${usersWithRole} user(s).`
            );
        }

        // soft delete role
        await role.update({ is_active: 0 });
        return {
            status: true,
            message: "Role deleted successfully"
        };
    } catch (error) {
        console.error("Error deleting role:", error);
        throw error;
    }
}

/**
 * Get all roles with user assignments and permissions.
 * Returns user-wise role and permission data for active users only.
 * 
 * @returns {Promise<Object>} User-wise roles and permissions data
 * @throws {Error} When database operation fails
 */
roleService.getRoles = async () => {
    try {
        const usersWithRoles = await Users.findAll({
            where: { status: "active" },
            attributes: ['id', 'name', 'email'],
            include: [
                {
                    model: Roles,
                    as: 'role',
                    where: { is_active: 1 },
                    attributes: ['id', 'name', 'description'],
                    include: [
                        {
                            model: Permissions,
                            as: 'permissions',
                            attributes: ['id', 'name', 'description'],
                            through: { attributes: [] } // Exclude junction table attributes
                        }
                    ]
                }
            ]
        });

        // transform the data to a cleaner format
        const userRoleData = usersWithRoles.map(user => ({
            user_id: user.id,
            user_name: user.name,
            user_email: user.email,
            role: {
                role_id: user.role?.id || null,
                role_name: user.role?.name || null,
                role_description: user.role?.description || null,
                permissions: user.role?.permissions?.map(permission => ({
                    permission_id: permission.id,
                    permission_name: permission.name,
                    permission_description: permission.description
                })) || []
            }
        }));

        return {
            status: true,
            message: "User-wise roles and permissions fetched successfully",
            data: userRoleData
        };
    } catch (error) {
        console.error("Error fetching user-wise roles:", error);
        throw error;
    }
};

 
roleService.getPermissions = async () => {
    try {
        const permissions = await Permissions.findAll();
        if (!permissions.length) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "No permissions found");
        }
        const permissionsData = permissions.map(permission =>{ return { permission_id: permission.id, permission_name: permission.name } });
        return {
            status: true,
            message: "Permissions fetched successfully",
            data: permissionsData
        };
    } catch (error) {
        console.error("Error fetching permissions:", error);
        throw error;
    }
}


module.exports = roleService;