const { DataTypes } = require("sequelize");
const connection = require("../connection");


const Feedback = connection.define("Feedback", {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true
    },
    speaker_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: "speakers",
            key: "id"
        },
        onUpdate: "CASCADE",
        onDelete: "CASCADE"
    },
    type: {
        type: DataTypes.ENUM("feedback", "feature"),
        allowNull: false,
        defaultValue: "feedback"
    },
    tags: {
        type: DataTypes.JSON,
        allowNull: true,
        defaultValue: []
    },
    note: {
        type: DataTypes.TEXT,
        allowNull: true
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
    }
}, {
    tableName: "feedbacks",
    timestamps: false
});

module.exports = Feedback;