"""
Optimized Speaker Matching Service
===============================

This service provides comprehensive speaker-to-opportunity matching using a rule-based scoring, and manual analysis.

Author: Speaker Bot Team
Version: 2.0 (Optimized)
Last Updated: 2024
"""

import logging
import time
from datetime import datetime, date
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from contextlib import contextmanager

from app.helpers.database_manager import db_manager
from openai import OpenAI

from app.config.config import config
from app.models.speakers import Speaker
from app.models.speaker_opportunities import SpeakerOpportunity
from app.models.speaker_details import SpeakerDetails
from app.models.speaker_engagement import SpeakerEngagement
from app.models.opportunity_form import OpportunityForm

# Configure structured logging
logger = logging.getLogger(__name__)

@dataclass
class MatchingConfig:
    """Configuration constants for matching operations."""
    MIN_SCORE_THRESHOLD: float = 0.1
    
    # Score weights (must add up to 1.0)
    PRIMARY_CATEGORY_WEIGHT: float = 0.40
    SUBCATEGORY_WEIGHT: float = 0.30
    GEO_LOCATION_WEIGHT: float = 0.20
    TOPIC_WEIGHT: float = 0.10

@dataclass
class MatchResult:
    """Structured result data for matching operations."""
    success: bool
    matches: List[Dict[str, Any]]
    total_count: int
    llm_enhanced_count: int
    processing_time: float
    error_message: Optional[str] = None

class SpeakerMatchingService:
    """
    Optimized speaker matching service with hybrid scoring and manual analysis.
    This service combines:
    1. Vector similarity search (ChromaDB + OpenAI embeddings)
    2. Rule-based scoring with weighted criteria
    3. Manual analysis for detailed matching
    """
    
    def __init__(self):
        """Initialize the matching service with database connections and clients."""
        self.config = MatchingConfig()
        # Use centralized database manager instead of creating new engine
        self.SessionLocal = db_manager.SessionLocal
        self.openai_client = OpenAI(api_key=config.OPENAI_API_KEY)
        
        logger.info("SpeakerMatchingService initialized with centralized database manager")
    

    @contextmanager
    def get_db_session(self):
        """Context manager for database sessions with automatic cleanup."""
        with db_manager.get_session() as session:
            yield session
    

    def get_speaker_data(self, speaker_id: int) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Retrieve comprehensive speaker data from database.
        Args:
            speaker_id: Unique identifier for the speaker
        Returns:
            Tuple of (speaker_data_dict, error_message)
        """
        try:
            with self.get_db_session() as session:
                speaker = session.query(Speaker).filter_by(id=speaker_id).first()
                if not speaker:
                    return None, f"Speaker {speaker_id} not found in database"
                
                speaker_details = session.query(SpeakerDetails).filter_by(
                    speaker_id=speaker_id
                        ).all()
                
                # Build comprehensive speaker data dictionary
                speaker_data = {
                    "speaker_id": speaker.id,
                    "name": speaker.name,
                    "email": speaker.email,
                    "bio": speaker.bio or "",
                    "status": speaker.status,
                    "location": {
                        "city": speaker.city or "",
                        "state": speaker.state or "",
                        "country": speaker.country or ""
                    },
                    "company": speaker.company or "",
                    # Core matching fields with weights
                    "primary_category": speaker.primary_category or "",
                    "subcategory": speaker.subcategory or "",
                    "topic": speaker.topic or "",
                    "preferred_speaker_geography": speaker.preferred_speaker_geography or "",
                }
                
                # Add speaker details fields
                for detail in speaker_details:
                    if detail.key and detail.value:
                        speaker_data[detail.key] = detail.value
            
                logger.info(f"Retrieved speaker data for {speaker.name} ({speaker.email}) - {len(speaker_details)} detail fields")
                return speaker_data, None
                
        except Exception as e:
            logger.error(f"Database error retrieving speaker {speaker_id}: {e}")
            return None, f"Database error: {str(e)}"

    def validate_speaker_data(self, speaker_id: int) -> bool:
        """
        Validate speaker data exists in MySQL database.
        
        Args:
            speaker_id: Unique identifier for the speaker
            
        Returns:
            Boolean indicating if speaker data is valid
        """
        try:
            speaker_data, error = self.get_speaker_data(speaker_id)
            if error or not speaker_data:
                logger.error(f"Speaker validation failed for ID {speaker_id}: {error}")
                return False
            
            logger.info(f"✅ Speaker {speaker_id} data validated successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error validating speaker {speaker_id}: {e}")
            return False
    
    def check_opportunities_availability(self) -> bool:
        """
        Check if opportunities are available in MySQL database.
        
        Returns:
            Boolean indicating if opportunities are available
        """
        try:
            with self.get_db_session() as session:
                from app.models.opportunities import SpeakerOpportunity as Opportunity
                from sqlalchemy import and_
                
                # Count future opportunities
                today = date.today()
                opp_count = session.query(Opportunity).filter(
                    and_(
                        Opportunity.start_date >= today,
                        Opportunity.is_active == True,
                        Opportunity.deleted_at.is_(None)
                    )
                ).count()
                
                logger.info(f"MySQL opportunities check - Found {opp_count} future opportunities")
                return opp_count > 0
                
        except Exception as e:
            logger.error(f"MySQL opportunities check failed: {e}")
            return False
    
    def get_all_future_opportunities(self, speaker_id: int, limit: int = None, offset: int = 0) -> List[Dict[str, Any]]:
        """
        Get opportunities from MySQL database with future start_date.
        
        Args:
            speaker_id: Unique identifier for the speaker (for logging purposes)
            limit: Maximum number of opportunities to return (for batch processing)
            offset: Number of opportunities to skip (for batch processing)
            
        Returns:
            List of opportunity dictionaries from MySQL database
        """
        start_time = time.time()
        logger.info(f"Getting opportunities from MySQL for speaker {speaker_id} (limit={limit}, offset={offset})")
        
        try:
            with self.get_db_session() as session:
                from app.models.opportunities import SpeakerOpportunity as Opportunity
                from sqlalchemy import and_
                
                # Build query with optional pagination
                today = date.today()
                query = session.query(Opportunity).filter(
                    and_(
                        Opportunity.start_date >= today,
                        Opportunity.is_active == True,
                        Opportunity.deleted_at.is_(None)
                    )
                )
                
                # Apply pagination if specified
                if limit:
                    query = query.limit(limit).offset(offset)
                
                opportunities = query.all()
                
                if not opportunities:
                    logger.warning("No future opportunities found in MySQL database")
                    return []
                
                # Convert to dictionary format
                opportunity_list = []
                for opp in opportunities:
                    opp_data = {
                        'opportunity_id': opp.id,
                        'title': opp.title or '',
                        'description': opp.description or '',
                        'organization': opp.organization or '',
                        'start_date': str(opp.start_date) if opp.start_date else '',
                        'end_date': str(opp.end_date) if opp.end_date else '',
                        'event_url': opp.event_url or '',
                        'city': opp.city or '',
                        'state': opp.state or '',
                        'country': opp.country or '',
                        'venue': opp.venue or '',
                        'is_virtual': opp.is_virtual or False,
                        'industry': opp.industry or '',
                        'event_type': opp.event_type or '',
                        'subcategory': opp.subcategory or '',
                        'source': opp.source or '',
                        'vector_similarity_score': 0.0  # No vector similarity anymore
                    }
                    opportunity_list.append(opp_data)
                
                processing_time = time.time() - start_time
                logger.info(f"MySQL query completed in {processing_time:.2f}s - Found {len(opportunity_list)} future opportunities")
                
                return opportunity_list
                
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error getting opportunities from MySQL after {processing_time:.2f}s: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return []

    def get_opportunities_for_website(self, speaker_primary_category: str = None, speaker_subcategory: str = None) -> List[Dict[str, Any]]:
        """
        Get all opportunities for website matching filtered by category/subcategory and form type.
        
        Args:
            speaker_primary_category: Speaker's primary category (for filtering)
            speaker_subcategory: Speaker's subcategory (for filtering)
            
        Returns:
            List of opportunity dictionaries with form type, or empty list if not found
        """
        start_time = time.time()
        logger.info(f"Getting opportunities for website matching (primary_category={speaker_primary_category}, subcategory={speaker_subcategory})")
        
        try:
            with self.get_db_session() as session:
                from app.models.opportunities import SpeakerOpportunity as Opportunity
                from sqlalchemy import and_
                
                today = date.today()
                
                # Build base query for future active opportunities
                query = session.query(Opportunity).join(
                    OpportunityForm,
                    Opportunity.id == OpportunityForm.opportunity_id
                ).filter(
                    and_(
                        Opportunity.start_date >= today,
                        Opportunity.is_active == True,
                        Opportunity.deleted_at.is_(None),
                        OpportunityForm.type == "form"
                    )
                )
                
                # Filter by subcategory if provided
                if speaker_subcategory:
                    query = query.filter(Opportunity.subcategory == speaker_subcategory)
                
                # Get all opportunities matching subcategory (no limit yet)
                all_opportunities = query.all()
                
                if not all_opportunities:
                    logger.info(f"No opportunities found matching criteria")
                    return []
                
                # Use get_category_from_subcategory to find primary category of opportunity
                # and match with speaker's primary category - filter ALL opportunities first
                from app.helpers.matching_helpers import DatabaseRelationshipHelper
                database_helper = DatabaseRelationshipHelper()
                
                filtered_opportunities = []
                if speaker_primary_category:
                    speaker_primary_category_lower = speaker_primary_category.lower().strip()
                    # Filter all opportunities by primary category
                    for opp in all_opportunities:
                        if opp.subcategory:
                            opp_primary_category = database_helper.get_category_from_subcategory(opp.subcategory)
                            if opp_primary_category and opp_primary_category.lower().strip() == speaker_primary_category_lower:
                                filtered_opportunities.append(opp)
                else:
                    # If no primary category specified, use all opportunities
                    filtered_opportunities = all_opportunities
                
                if not filtered_opportunities:
                    logger.info(f"No opportunities found with matching criteria")
                    return []
                
                # Convert all filtered opportunities to dictionary format
                opportunities_list = []
                for opp in filtered_opportunities:
                    opp_data = {
                        'opportunity_id': opp.id,
                        'opportunity_url_id': opp.opportunity_url_id,
                        'title': opp.title or '',
                        'description': opp.description or '',
                        'organization': opp.organization or '',
                        'start_date': str(opp.start_date) if opp.start_date else '',
                        'end_date': str(opp.end_date) if opp.end_date else '',
                        'event_url': opp.event_url or '',
                        'source_url': opp.source_url or '',
                        'city': opp.city or '',
                        'state': opp.state or '',
                        'country': opp.country or '',
                        'venue': opp.venue or '',
                        'is_virtual': opp.is_virtual or False,
                        'is_active': opp.is_active or False,
                        'industry': opp.industry or '',
                        'event_type': opp.event_type or '',
                        'subcategory': opp.subcategory or '',
                        'source': opp.source or '',
                        'vector_similarity_score': 0.0
                    }
                    opportunities_list.append(opp_data)
                
                processing_time = time.time() - start_time
                logger.info(f"Found {len(opportunities_list)} opportunities for website matching in {processing_time:.2f}s")
                
                return opportunities_list
                
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Error getting opportunities for website after {processing_time:.2f}s: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return None
    
    def get_opportunities_count(self) -> int:
        """Get total count of future opportunities."""
        try:
            with self.get_db_session() as session:
                from app.models.opportunities import SpeakerOpportunity as Opportunity
                from sqlalchemy import and_
                
                today = date.today()
                count = session.query(Opportunity).filter(
                    and_(
                        Opportunity.start_date >= today,
                        Opportunity.is_active == True,
                        Opportunity.deleted_at.is_(None)
                    )
                ).count()
                return count
        except Exception as e:
            logger.error(f"Error getting opportunities count: {e}")
            return 0

    def save_matches_to_database(self, matches: List[Dict[str, Any]]) -> int:
        """
        Save matches to database with optimized bulk operations.
        
        Args:
            matches: List of match dictionaries to save
            
        Returns:
            Number of matches successfully saved
        """
        if not matches:
            return 0
            
        start_time = time.time()
        logger.info(f"Saving {len(matches)} matches to database using bulk operations")
        
        try:
            with self.get_db_session() as session:
                # Prepare data for bulk operations
                new_matches = []
                update_matches = []
                
                # Get existing matches in bulk to avoid N+1 queries
                speaker_ids = [match.get("speaker_id") for match in matches if match.get("speaker_id")]
                opportunity_ids = [match.get("opportunity_id") for match in matches if match.get("opportunity_id")]
                
                if not speaker_ids or not opportunity_ids:
                    logger.warning("No valid speaker_id or opportunity_id found in matches")
                    return 0
                
                # Bulk query existing matches
                existing_matches = session.query(SpeakerOpportunity).filter(
                    SpeakerOpportunity.speaker_id.in_(speaker_ids),
                    SpeakerOpportunity.opportunity_id.in_(opportunity_ids)
                ).all()
                
                # Create lookup set for existing matches
                existing_keys = {(m.speaker_id, m.opportunity_id): m for m in existing_matches}
                
                # Categorize matches into new and updates
                for match in matches:
                    speaker_id = match.get("speaker_id")
                    opportunity_id = match.get("opportunity_id")
                    
                    if not speaker_id or not opportunity_id:
                        continue
                    
                    key = (speaker_id, opportunity_id)
                    
                    if key in existing_keys:
                        # Prepare for bulk update
                        update_matches.append({
                            'speaker_id': speaker_id,
                            'opportunity_id': opportunity_id,
                            'overall_score': match.get("overall_score"),
                            'topic_score': match.get("topic_score"),
                            'primary_category_score': match.get("primary_category_score"),
                            'subcategory_score': match.get("subcategory_score"),
                            'geo_location_score': match.get("geo_location_score"),
                            'match_strength': match.get("match_strength"),
                            'match_concern': match.get("match_concern"),
                            'recommendation': match.get("recommendation"),
                            'match_considerations': match.get("match_considerations"),
                            'updated_at': datetime.now()
                        })
                    else:
                        # Prepare for bulk insert
                        new_matches.append({
                            'speaker_id': speaker_id,
                            'opportunity_id': opportunity_id,
                            'speaker_email': match.get("speaker_email"),
                            'overall_score': match.get("overall_score"),
                            'topic_score': match.get("topic_score"),
                            'primary_category_score': match.get("primary_category_score"),
                            'subcategory_score': match.get("subcategory_score"),
                            'geo_location_score': match.get("geo_location_score"),
                            'match_strength': match.get("match_strength"),
                            'match_concern': match.get("match_concern"),
                            'recommendation': match.get("recommendation"),
                            'match_considerations': match.get("match_considerations"),
                            'ai_match': True,
                            'created_at': datetime.now(),
                            'updated_at': datetime.now()
                        })
                
                # Perform bulk operations
                saved_count = 0
                updated_count = 0
                
                # Bulk insert new matches
                if new_matches:
                    session.bulk_insert_mappings(SpeakerOpportunity, new_matches)
                    saved_count = len(new_matches)
                    logger.info(f"Bulk inserted {saved_count} new matches")
                
                # Bulk update existing matches
                if update_matches:
                    self._bulk_update_matches(session, update_matches)
                    updated_count = len(update_matches)
                    logger.info(f"Bulk updated {updated_count} existing matches")
                
                # Commit all changes
                session.commit()
                
                processing_time = time.time() - start_time
                logger.info(f"Database save completed in {processing_time:.2f}s - Saved: {saved_count}, Updated: {updated_count}")
                
                return saved_count + updated_count
                
        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"Database save failed after {processing_time:.2f}s: {e}")
            import traceback
            logger.error(f"Traceback: {traceback.format_exc()}")
            return 0

    def _bulk_update_matches(self, session, update_matches: List[Dict[str, Any]]) -> None:
        """
        Perform bulk update of matches using optimized SQL.
        
        Args:
            session: Database session
            update_matches: List of match dictionaries to update
        """
        try:
            from sqlalchemy import text
            
            # Build bulk update query using CASE statements
            speaker_ids = [m['speaker_id'] for m in update_matches]
            opportunity_ids = [m['opportunity_id'] for m in update_matches]
            
            # Create CASE statements for each field
            overall_score_cases = []
            topic_score_cases = []
            primary_category_score_cases = []
            subcategory_score_cases = []
            geo_location_score_cases = []
            match_strength_cases = []
            match_concern_cases = []
            recommendation_cases = []
            match_considerations_cases = []
            
            for match in update_matches:
                speaker_id = match['speaker_id']
                opportunity_id = match['opportunity_id']
                
                overall_score_cases.append(f"WHEN speaker_id = {speaker_id} AND opportunity_id = {opportunity_id} THEN {match['overall_score'] or 'NULL'}")
                topic_score_cases.append(f"WHEN speaker_id = {speaker_id} AND opportunity_id = {opportunity_id} THEN {match['topic_score'] or 'NULL'}")
                primary_category_score_cases.append(f"WHEN speaker_id = {speaker_id} AND opportunity_id = {opportunity_id} THEN {match['primary_category_score'] or 'NULL'}")
                subcategory_score_cases.append(f"WHEN speaker_id = {speaker_id} AND opportunity_id = {opportunity_id} THEN {match['subcategory_score'] or 'NULL'}")
                geo_location_score_cases.append(f"WHEN speaker_id = {speaker_id} AND opportunity_id = {opportunity_id} THEN {match['geo_location_score'] or 'NULL'}")
                match_strength_cases.append(f"WHEN speaker_id = {speaker_id} AND opportunity_id = {opportunity_id} THEN '{match['match_strength'] or ''}'")
                match_concern_cases.append(f"WHEN speaker_id = {speaker_id} AND opportunity_id = {opportunity_id} THEN '{match['match_concern'] or ''}'")
                recommendation_cases.append(f"WHEN speaker_id = {speaker_id} AND opportunity_id = {opportunity_id} THEN '{match['recommendation'] or ''}'")
                match_considerations_cases.append(f"WHEN speaker_id = {speaker_id} AND opportunity_id = {opportunity_id} THEN '{match['match_considerations'] or ''}'")
            
            # Build the bulk update query
            update_query = f"""
            UPDATE speaker_opportunities 
            SET 
                overall_score = CASE {' '.join(overall_score_cases)} END,
                topic_score = CASE {' '.join(topic_score_cases)} END,
                primary_category_score = CASE {' '.join(primary_category_score_cases)} END,
                subcategory_score = CASE {' '.join(subcategory_score_cases)} END,
                geo_location_score = CASE {' '.join(geo_location_score_cases)} END,
                match_strength = CASE {' '.join(match_strength_cases)} END,
                match_concern = CASE {' '.join(match_concern_cases)} END,
                recommendation = CASE {' '.join(recommendation_cases)} END,
                match_considerations = CASE {' '.join(match_considerations_cases)} END,
                updated_at = NOW()
            WHERE (speaker_id, opportunity_id) IN ({','.join([f'({s},{o})' for s, o in zip(speaker_ids, opportunity_ids)])})
            """
            
            # Execute the bulk update
            session.execute(text(update_query))
            
        except Exception as e:
            logger.error(f"Bulk update failed, falling back to individual updates: {e}")
            # Fallback to individual updates if bulk update fails
            for match in update_matches:
                try:
                    session.query(SpeakerOpportunity).filter_by(
                        speaker_id=match['speaker_id'],
                        opportunity_id=match['opportunity_id']
                    ).update({
                        'overall_score': match.get("overall_score"),
                        'topic_score': match.get("topic_score"),
                        'primary_category_score': match.get("primary_category_score"),
                        'subcategory_score': match.get("subcategory_score"),
                        'geo_location_score': match.get("geo_location_score"),
                        'match_strength': match.get("match_strength"),
                        'match_concern': match.get("match_concern"),
                        'recommendation': match.get("recommendation"),
                        'match_considerations': match.get("match_considerations"),
                        'updated_at': datetime.now()
                    })
                except Exception as update_error:
                    logger.error(f"Failed to update match {match['speaker_id']}-{match['opportunity_id']}: {update_error}")

    def _update_existing_match(self, existing: SpeakerOpportunity, match: Dict[str, Any]) -> None:
        """Update existing match with new data."""
        
        # Update scores and analysis fields
        field_mapping = {
            "speaker_email": "speaker_email",
            "overall_score": "overall_score", 
            "topic_score": "topic_score",
            "primary_category_score": "primary_category_score",
            "subcategory_score": "subcategory_score",
            "geo_location_score": "geo_location_score",
            "match_strength": "match_strength",
            "match_concern": "match_concern", 
            "recommendation": "recommendation",
            "application_submitted": "application_submitted",
            "match_consideration": "match_considerations",
            "status": "pending",
            "ai_match": "ai_match",
            "reasons": "reasons"
        }
        
        for db_field, match_field in field_mapping.items():
            if match_field in match:
                value = match[match_field]
                if db_field in ["overall_score", "topic_score", "primary_category_score", 
                              "subcategory_score", "geo_location_score"]:
                    value = float(value)
                elif db_field == "ai_match":
                    value = bool(value)
                
                setattr(existing, db_field, value)
        existing.updated_at = datetime.now()

    def _create_new_match(self, match: Dict[str, Any]) -> SpeakerOpportunity:
        """Create new SpeakerOpportunity record."""
        
        return SpeakerOpportunity(
            speaker_id=match["speaker_id"],
            opportunity_id=match["opportunity_id"],
            speaker_email=match.get("speaker_email", ""),
            overall_score=float(match.get("overall_score", 5.0)),
            topic_score=float(match.get("topic_score", 5.0)),
            primary_category_score=float(match.get("primary_category_score", 5.0)),
            subcategory_score=float(match.get("subcategory_score", 5.0)),
            geo_location_score=float(match.get("geo_location_score", 5.0)),
            match_strength=match.get("match_strength", ""),
            match_concern=match.get("match_concern", ""),
            recommendation=match.get("recommendation", ""),
            application_submitted=match.get("application_submitted", False),
            match_considerations=match.get("match_consideration", ""),
            ai_match=match.get("ai_match", False),
            status="pending",
            reasons=match.get("reasons", ""),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

    def _save_match_to_database(self, speaker_id: int, email: str, top_match: Dict[str, Any], opportunity: Dict[str, Any]) -> None:
        """
        Helper method to save match to database.
        
        Args:
            speaker_id: Speaker ID
            email: Speaker email
            top_match: Match dictionary with scores
            opportunity: Opportunity dictionary
        """
        opportunity_id = top_match.get("opportunity_id")
        
        with self.get_db_session() as session:
            # Check if match already exists
            existing_match = session.query(SpeakerOpportunity).filter(
                SpeakerOpportunity.speaker_id == speaker_id,
                SpeakerOpportunity.opportunity_id == opportunity_id
            ).first()
            
            if existing_match:
                # Update existing match
                existing_match.overall_score = top_match.get("overall_score")
                existing_match.topic_score = top_match.get("topic_score")
                existing_match.primary_category_score = top_match.get("primary_category_score")
                existing_match.subcategory_score = top_match.get("subcategory_score")
                existing_match.geo_location_score = top_match.get("geo_location_score")
                existing_match.match_strength = top_match.get("match_strength", "")
                existing_match.match_concern = top_match.get("match_concern", "")
                existing_match.recommendation = top_match.get("recommendation", "")
                existing_match.match_considerations = top_match.get("match_considerations", top_match.get("match_consideration", ""))
                existing_match.speaker_email = email
                existing_match.ai_match = True
                existing_match.updated_at = datetime.now()
            else:
                # Create new match
                new_match = SpeakerOpportunity(
                    speaker_id=speaker_id,
                    opportunity_id=opportunity_id,
                    speaker_email=email,
                    overall_score=top_match.get("overall_score"),
                    topic_score=top_match.get("topic_score"),
                    primary_category_score=top_match.get("primary_category_score"),
                    subcategory_score=top_match.get("subcategory_score"),
                    geo_location_score=top_match.get("geo_location_score"),
                    match_strength=top_match.get("match_strength", ""),
                    match_concern=top_match.get("match_concern", ""),
                    recommendation=top_match.get("recommendation", ""),
                    match_considerations=top_match.get("match_considerations", top_match.get("match_consideration", "")),
                    ai_match=True,
                    status="pending",
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                session.add(new_match)
            
            session.commit()
            overall_score = top_match.get("overall_score", 0)
            logger.info(f"Saved match for speaker {speaker_id}, opportunity {opportunity_id} with score {overall_score}")

    def website_matching(self, name: str, email: str, city: str = None, state: str = None,
                        country: str = None, primary_category: str = None, sub_category: str = None,
                        topic: str = None, preferred_speaker_geography: str = None, speaker_id: int = None) -> Dict[str, Any]:
        """
        Website matching function for external speaker submissions.
        Processes opportunities in batches of 20 with early exit if score > 7.0 is found.
        """
        BATCH_SIZE = 20
        SCORE_THRESHOLD = 7.0
       
        try:
            if speaker_id is None:
                return {"error": "speaker_id is required"}
            
            opportunities = self.get_opportunities_for_website(
                speaker_primary_category=primary_category,
                speaker_subcategory=sub_category
            )
            
            if not opportunities:
                return {"error": "No opportunities found"}
            
            # Sort opportunities by opportunity_id in descending order (highest ID first)
            opportunities.sort(key=lambda x: x.get("opportunity_id", 0), reverse=True)
           
            speaker_data = {
                "speaker_id": speaker_id,
                "name": name,
                "email": email,
                "city": city or "",
                "state": state or "",
                "country": country or "",
                "primary_category": primary_category or "",
                "subcategory": sub_category or "",
                "topic": topic or "",
                "preferred_speaker_geography": preferred_speaker_geography or ""
            }
            
            from app.background_tasks.match_worker import match_worker
            
            best_match = None
            best_opportunity = None
            
            # Process in batches of 20
            for i in range(0, len(opportunities), BATCH_SIZE):
                batch = opportunities[i:i + BATCH_SIZE]
                scored = match_worker._calculate_hybrid_scores_batch(speaker_data, batch)
               
                if not scored:
                    continue
                
                scored.sort(key=lambda x: x.get("overall_score", 0), reverse=True)
                
                # Track best match
                if not best_match or scored[0].get("overall_score", 0) > best_match.get("overall_score", 0):
                    best_match = scored[0]
                    opp_id = best_match.get("opportunity_id")
                    best_opportunity = next((opp for opp in batch if opp.get("opportunity_id") == opp_id), None)
                
                # Check for high score match (> 7.0) - early exit
                for match in scored:
                    if match.get("overall_score", 0) > SCORE_THRESHOLD:
                        opp_id = match.get("opportunity_id")
                        opp = next((o for o in batch if o.get("opportunity_id") == opp_id), None)
                        if opp:
                            self._save_match_to_database(speaker_id, email, match, opp)
                            return {"success": True, "message": "Match found and saved successfully"}
            
            # No high score found - save best match
            if best_match and best_opportunity:
                self._save_match_to_database(speaker_id, email, best_match, best_opportunity)
                return {"success": True, "message": "Match found and saved successfully"}
            
            return {"error": "No matches found"}
           
        except Exception as e:
            logger.error(f"Website matching error: {e}")
            return {"error": str(e)}

    
# Create singleton instance for global access
matching_service = SpeakerMatchingService()