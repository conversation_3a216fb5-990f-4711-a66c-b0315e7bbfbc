const ApiResponse = require("../helpers/api-response");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const scrapingService = require("../services/scraping-service");
const { exportAsCSV } = require("../helpers/csv-helper");

/**
 * Get all scraping logs with filters, sorting, and pagination
 */
exports.getScrapingLogs = async (req, res, next) => {
  try {
    const {
      search,
      status,
      topic,
      startDate,
      endDate,
      error,
      page = 1,
      limit = 10,
      sortBy = 'started_at',
      sortOrder = 'DESC'
    } = req.query;

    // Validate pagination parameters
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    
    if (pageNum < 1) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Page number must be greater than 0"
      );
    }
    
    if (limitNum < 1 || limitNum > 100) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Limit must be between 1 and 100"
      );
    }

    // Validate sort parameters
    const validSortFields = ['started_at', 'items_collected'];
    const validSortOrders = ['ASC', 'DESC'];
    
    if (!validSortFields.includes(sortBy)) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Invalid sort field. Must be one of: started_at, items_collected"
      );
    }
    
    if (!validSortOrders.includes(sortOrder.toUpperCase())) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Invalid sort order. Must be ASC or DESC"
      );
    }

    // Validate date parameters
    if (startDate && isNaN(new Date(startDate).getTime())) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Invalid start date format"
      );
    }
    
    if (endDate && isNaN(new Date(endDate).getTime())) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Invalid end date format"
      );
    }

    // Validate status parameter
    if (status && !['success', 'error', 'running', 'paused'].includes(status.toLowerCase())) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Invalid status. Must be: success, error, running, or paused"
      );
    }

    const filters = {
      search,
      status: status ? status.toLowerCase() : undefined,
      topic,
      startDate,
      endDate,
      error
    };

    const pagination = {
      page: pageNum,
      limit: limitNum
    };

    const sorting = {
      sortBy,
      sortOrder: sortOrder.toUpperCase()
    };

    const result = await scrapingService.getScrapingLogs(filters, pagination, sorting);

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(
        ApiResponse({
          data: result.data,
          pagination: {total: result.pagination.totalCount, ...result.pagination} ,
          message: "Scraping logs fetched successfully"
        })
      );
  } catch (err) {
    next(err);
  }
};

/**
 * Get scraping log by ID
 */
exports.getScrapingLogById = async (req, res, next) => {
  try {
    const { id } = req.params;

    if (!id || isNaN(parseInt(id))) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Valid log ID is required"
      );
    }

    const log = await scrapingService.getScrapingLogById(parseInt(id));

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(
        ApiResponse({
          data: log,
          message: "Scraping log fetched successfully"
        })
      );
  } catch (err) {
    next(err);
  }
};

/**
 * Get available topics for filtering
 */
exports.getAvailableTopics = async (req, res, next) => {
  try {
    const topics = await scrapingService.getAvailableTopics();

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(
        ApiResponse({
          data: topics,
          message: "Available topics fetched successfully"
        })
      );
  } catch (err) {
    next(err);
  }
};

/**
 * Get scraping statistics
 */
exports.getScrapingStats = async (req, res, next) => {
  try {
    const stats = await scrapingService.getScrapingStats();

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(
        ApiResponse({
          data: stats,
          message: "Scraping statistics fetched successfully"
        })
      );
  } catch (err) {
    next(err);
  }
};

// ==================== SUBCATEGORIES CRUD CONTROLLERS ====================

/**
 * Get all subcategories with filters, sorting, and pagination
 */
exports.getSubcategories = async (req, res, next) => {
  try {
    const {
      search,
      is_active,
      page = 1,
      limit = 10,
      sortBy = 'name',
      sortOrder = 'ASC'
    } = req.query;

    // Validate pagination parameters
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    
    if (pageNum < 1) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Page number must be greater than 0"
      );
    }
    
    if (limitNum < 1 || limitNum > 100) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Limit must be between 1 and 100"
      );
    }

    // Validate sort parameters
    const validSortFields = ['name', 'category_name', 'is_active'];
    const validSortOrders = ['ASC', 'DESC'];
    
    if (!validSortFields.includes(sortBy)) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Invalid sort field. Must be one of: name, category_name, is_active"
      );
    }
    
    if (!validSortOrders.includes(sortOrder.toUpperCase())) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Invalid sort order. Must be ASC or DESC"
      );
    }

    const filters = {
      search,
      is_active
    };

    const pagination = {
      page: pageNum,
      limit: limitNum
    };

    const sorting = {
      sortBy,
      sortOrder: sortOrder.toUpperCase()
    };

    const result = await scrapingService.getSubcategories(filters, pagination, sorting);

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(
        ApiResponse({
          data: result.data,
          pagination: {total: result.pagination.totalCount, ...result.pagination},
          message: "Subcategories fetched successfully"
        })
      );
  } catch (err) {
    next(err);
  }
};

/**
 * Get all categories for subcategory creation
 */
exports.getAllCategories = async (req, res, next) => {
  try {
    const categories = await scrapingService.getAllCategories();

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(
        ApiResponse({
          data: categories,
          message: "Categories fetched successfully"
        })
      );
  } catch (err) {
    next(err);
  }
};

/**
 * Toggle subcategory active status
 */
exports.toggleSubcategoryActive = async (req, res, next) => {
  try {
    const { subcategory_id } = req.body;

    // Validate required fields
    if (!subcategory_id || isNaN(parseInt(subcategory_id))) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Valid subcategory ID is required"
      );
    }

    const result = await scrapingService.toggleSubcategoryActive(parseInt(subcategory_id));

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(
        ApiResponse({
          data: result,
          message: "Subcategory status toggled successfully"
        })
      );
  } catch (err) {
    next(err);
  }
};

/**
 * Create a new subcategory
 */
exports.createSubcategory = async (req, res, next) => {
  try {
    const { category_id, name } = req.body;

    // Validate required fields
    if (!category_id || isNaN(parseInt(category_id))) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Valid category ID is required"
      );
    }

    if (!name || name.trim() === '') {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Subcategory name is required"
      );
    }

    const subcategoryData = {
      category_id: parseInt(category_id),
      name: name.trim()
    };

    const newSubcategory = await scrapingService.createSubcategory(subcategoryData);

    res
      .status(RESPONSE_CODES.ACCEPTED)
      .json(
        ApiResponse({
          data: newSubcategory,
          message: "Subcategory created successfully"
        })
      );
  } catch (err) {
    next(err);
  }
};


/**
 * Export scraping logs as CSV
 */
exports.exportScrapingLogsCSV = async (req, res, next) => {
  try {
    const {
      search,
      status,
      topic,
      startDate,
      endDate,
      error,
      sortBy = 'started_at',
      sortOrder = 'DESC'
    } = req.query;

    // Validate sort parameters
    const validSortFields = ['started_at', 'items_collected'];
    const validSortOrders = ['ASC', 'DESC'];
    
    if (!validSortFields.includes(sortBy)) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Invalid sort field. Must be one of: started_at, items_collected"
      );
    }
    
    if (!validSortOrders.includes(sortOrder.toUpperCase())) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Invalid sort order. Must be ASC or DESC"
      );
    }

    // Validate date parameters
    if (startDate && isNaN(new Date(startDate).getTime())) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Invalid start date format"
      );
    }
    
    if (endDate && isNaN(new Date(endDate).getTime())) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Invalid end date format"
      );
    }

    // Validate status parameter
    if (status && !['success', 'error', 'running', 'paused'].includes(status.toLowerCase())) {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "Invalid status. Must be: success, error, running, or paused"
      );
    }

    const filters = {
      search,
      status: status ? status.toLowerCase() : undefined,
      topic,
      startDate,
      endDate,
      error
    };

    const sorting = {
      sortBy,
      sortOrder: sortOrder.toUpperCase()
    };

    // Get CSV data
    const csvData = await scrapingService.exportScrapingLogsCSV(filters, sorting);

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `scraping_logs_${timestamp}.csv`;

    if (!csvData || csvData.length === 0) {
      throw new CustomError(
        RESPONSE_CODES.SUCCESS,
        "No scraping logs data found to export!"
      );
    }

    // Export as CSV
    return exportAsCSV(res, csvData, filename);

  } catch (err) {
    next(err);
  }
};

/**
 * Control scraping (start/stop)
 */
exports.controlScraping = async (req, res, next) => {
  try {
    const { isRunning } = req.body;

    // Validate input
    if (typeof isRunning !== 'boolean') {
      throw new CustomError(
        RESPONSE_CODES.BAD_REQUEST,
        "isRunning must be a boolean value (true or false)"
      );
    }

    const result = await scrapingService.controlScraping(isRunning);

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(
        ApiResponse({
          data: result,
          message: result.message
        })
      );
  } catch (err) {
    next(err);
  }
};

/**
 * Get current scraping status
 */
exports.getScrapingStatus = async (req, res, next) => {
  try {
    const result = await scrapingService.getScrapingStatus();

    res
      .status(RESPONSE_CODES.SUCCESS)
      .json(
        ApiResponse({
          data: result,
          message: result.message
        })
      );
  } catch (err) {
    next(err);
  }
};