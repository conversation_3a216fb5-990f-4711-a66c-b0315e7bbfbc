import React from 'react';
import { Link, LinkProps } from 'react-router-dom';
import { usePermissions } from '../hooks/usePermissions';
import { Permission } from '../store/slices/rbacSlice';
import { cn } from '../lib/utils';

interface PermissionLinkProps extends LinkProps {
  permission?: Permission;
  permissions?: Permission[];
  requireAll?: boolean;
  fallback?: React.ReactNode;
  disabledClassName?: string;
  disabledMessage?: string;
}

/**
 * Link component that is disabled or hidden based on user permissions
 */
const PermissionLink: React.FC<PermissionLinkProps> = ({
  children,
  permission,
  permissions = [],
  requireAll = false,
  fallback = null,
  disabledClassName = "opacity-50 cursor-not-allowed pointer-events-none",
  disabledMessage = "You don't have permission to access this page",
  className,
  ...linkProps
}) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions } = usePermissions();

  // Determine which permissions to check
  const permissionsToCheck = permission ? [permission] : permissions;

  // Check permissions
  const hasRequiredPermissions = permissionsToCheck.length === 0 || 
    (requireAll
      ? hasAllPermissions(permissionsToCheck)
      : hasAnyPermission(permissionsToCheck));

  // If user doesn't have required permissions, show fallback or disabled link
  if (!hasRequiredPermissions) {
    if (fallback) {
      return <>{fallback}</>;
    }
    
    return (
      <span
        className={cn(className, disabledClassName)}
        title={disabledMessage}
      >
        {children}
      </span>
    );
  }

  // User has required permissions, render normal link
  return (
    <Link {...linkProps} className={className}>
      {children}
    </Link>
  );
};

export default PermissionLink;
