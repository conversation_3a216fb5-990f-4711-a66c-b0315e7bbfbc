variables:
  NODE_VERSION: "22"

workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
      changes:
        - speaker/frontend/**/*
        - speaker/backend/**/*
    - if: '$CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"'
      changes:
        - speaker/frontend/**/*
        - speaker/backend/**/*


# Build stage optimized for development
build:
  stage: build
  image: node:${NODE_VERSION}-alpine
  before_script:
    - apk add --no-cache zip python3 make g++
  script:
    - echo "Building frontend for development...."
    - cd speaker/frontend
    - rm -rf node_modules .vite .vite-temp dist
    
    # Use development environment variables
    - echo "VITE_API_BASE_URL=\"$DEV_SPEAKER_VITE_API_BASE_URL\"" > .env
    - echo "VITE_BASE_URL=\"$DEV_SPEAKER_VITE_BASE_URL\"" >> .env
    - echo "VITE_PUBLIC_BASE_URL=\"$DEV_SPEAKER_VITE_PUBLIC_BASE_URL\"" >> .env
    - echo "VITE_ENVIRONMENT=\"development\"" >> .env
    - npm install
    - npm run build:dev
    
    - echo "Frontend build completed"
    - cd ../backend
    
    # Create development deployment package
    - echo "Creating development backend package..."
    - zip -r ../../dev-speaker-deploy.zip . -x "admin/frontend/node_modules/*" ".git/*" "node_modules/*" "*.log" "admin/frontend/dist/*"
  artifacts:
    paths:
      - dev-speaker-deploy.zip
    expire_in: 1 hour

# Development deployments
deploy:
  stage: deploy
  image: amazonlinux:latest
  dependencies:
    - build
  before_script:
    - yum install -y unzip aws-cli
  script:
    # Configure AWS credentials for dev environment
    - aws configure set aws_access_key_id $DEV_AWS_ACCESS_KEY_ID
    - aws configure set aws_secret_access_key $DEV_AWS_SECRET_ACCESS_KEY
    - aws configure set default.region $DEV_AWS_DEFAULT_REGION

    # Upload to S3 dev bucket
    - echo "Uploading to dev S3..."
    - aws s3 cp dev-speaker-deploy.zip s3://$DEV_S3_BUCKET/dev-speaker-deploy-${CI_COMMIT_SHORT_SHA}.zip
    
    # Deploy to dev Elastic Beanstalk
    - echo "Deploying to dev Elastic Beanstalk..."
    - export VERSION_LABEL=dev-${CI_COMMIT_REF_NAME}-$(date +%s)
    - aws elasticbeanstalk create-application-version --application-name $DEV_SPEAKER_EB_APPLICATION_NAME --version-label $VERSION_LABEL --source-bundle S3Bucket=${DEV_S3_BUCKET},S3Key=dev-speaker-deploy-${CI_COMMIT_SHORT_SHA}.zip
    - aws elasticbeanstalk update-environment --environment-name $DEV_SPEAKER_EB_ENVIRONMENT_NAME --version-label $VERSION_LABEL

    # Health check with shorter timeout for dev
    - echo "Checking dev deployment health..."
    - sleep 15
    - echo "Dev deployment completed successfully"
