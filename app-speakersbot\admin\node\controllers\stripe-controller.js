const stripeService = require("../services/stripe-service");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const ApiResponse = require("../helpers/api-response");
const { getStripe } = require("../helpers/stripe");

/**
 * Create Stripe checkout session
 */
exports.checkout = async (req, res) => {
  try {
    const { status, data, message } = await stripeService.checkout(req.body);
    if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ data, message }));
    else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

  } catch (err) {
    console.error(err);
    res.status(500).json({ error: err.message });
  }
};

exports.cancelSubscription = async (req, res) => {
  try {
    const { status, data, message } = await stripeService.cancelSubscription(req.body);
    if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ data, message }));
    else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);  
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: err.message });
  }
};

exports.updateSubscription = async (req, res) => {
  try {
    const { status, data, message } = await stripeService.updateSubscription(req.body);
    if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ data, message }));
    else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);  
  } catch (err) {
    console.error(err);
    res.status(500).json({ error: err.message });
  }
}


/**
 * Handle Stripe webhook events
 */
exports.webhooks = async (req, res) => {
  const sig = req.headers["stripe-signature"];
  try {
    const webhookSecret = await getStripe('webhook_secret');
    const stripe = await getStripe('secret_key');

    const event = stripe.webhooks.constructEvent(
      req.body,
      sig,
      webhookSecret
    );

    // Handle the event
    switch (event.type) {
      case "invoice.payment_succeeded":
        console.log("Payment intended succeeded:", JSON.stringify(event.data.object));
        await stripeService.invoicePaymentSucceeded(event.data.object);
        break;
      case "payment_intent.payment_failed":
        await stripeService.paymentFailed(event.data.object);
        break;  
      case "checkout.session.completed":
        await stripeService.handleCheckoutSession(event.data.object);
        break;
      default:
        console.log(`Unhandled event type ${event.type}`);
    }

    res.status(200).send("Received webhook");
  } catch (err) {
    console.error("Webhook error:", err.message);
    res.status(400).send(`Webhook error: ${err.message}`);
  }
}; 
