# SpeakersBot Admin Frontend

A comprehensive admin dashboard for the SpeakersBot platform, built with modern React technologies and featuring role-based access control (RBAC).

## 🚀 Overview

The SpeakersBot Admin Frontend is a sophisticated web application that provides administrators with tools to manage speakers, opportunities, users, subscriptions, and more. It features a robust RBAC system, modern UI components, and seamless integration with the backend API.

## ✨ Features

- **Role-Based Access Control (RBAC)** - Fine-grained permissions system
- **User Management** - Complete user lifecycle management
- **Speaker Management** - Profile and opportunity management
- **Dashboard Analytics** - Comprehensive data visualization
- **Subscription Management** - Stripe integration for payments
- **Gamification System** - Points and rewards management
- **Form Management** - Dynamic form creation and management
- **Matching Queue** - Speaker-opportunity matching system
- **Modern UI/UX** - Built with shadcn/ui and Tailwind CSS

## 🛠️ Tech Stack

### Core Technologies
- **React 19** - Modern React with latest features
- **TypeScript** - Type-safe development
- **Vite** - Fast build tool and dev server
- **React Router DOM** - Client-side routing

### UI & Styling
- **shadcn/ui** - Modern component library
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Accessible component primitives
- **Lucide React** - Beautiful icons
- **Recharts** - Data visualization

### State Management
- **Redux Toolkit** - Predictable state management
- **React Query (TanStack Query)** - Server state management
- **React Hook Form** - Form state management

### Development Tools
- **ESLint** - Code linting
- **TypeScript ESLint** - TypeScript-specific linting
- **PostCSS** - CSS processing
- **Autoprefixer** - CSS vendor prefixing

## 🚀 Getting Started

### Prerequisites

- **Node.js** (v18 or higher) - [Install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)
- **npm** or **yarn** package manager

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd app-speakersbot/admin/frontend
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Configure environment variables**
   - Copy `.env.example` to `.env` and update values as needed
   - Ensure backend API is running and accessible

4. **Start the development server**
   ```bash
   npm run dev
   # or
   yarn dev
   ```

5. **Open your browser**
   - Navigate to `http://localhost:5173` (or the port shown in terminal)

## 📜 Available Scripts

| Script | Description |
|--------|-------------|
| `npm run dev` | Start development server with hot reload |
| `npm run build` | Build for production with watch mode |
| `npm run build:dev` | Build for development environment |
| `npm run lint` | Run ESLint for code quality checks |
| `npm run preview` | Preview production build locally |

## 🏗️ Project Structure

```
src/
├── apis/                 # API service functions
├── components/           # Reusable UI components
│   ├── Layout/          # Layout components
│   ├── Forms/           # Form components
│   ├── Tables/          # Data table components
│   └── UI/              # Base UI components
├── hooks/               # Custom React hooks
├── lib/                 # Utility libraries
├── middleware/          # Redux middleware
├── pages/               # Page components
│   ├── dashboard/       # Dashboard pages
│   ├── manage-users/    # User management
│   ├── speakers/        # Speaker management
│   └── opportunities/   # Opportunity management
├── state/               # State management
├── store/               # Redux store configuration
├── types/               # TypeScript type definitions
└── utils/               # Utility functions
```

## 🔐 Authentication & Authorization

The application implements a comprehensive RBAC system:

### Roles
- **Admin** - Full system access with all permissions
- **Affiliate** - Limited access to affiliate-related features

### Permissions
The system supports 19 different permissions including:
- Dashboard access
- User management (read/write)
- Speaker management (read/write)
- Opportunity management (read/write)
- Form management (read/write)
- Settings management
- And more...

### Usage
```typescript
import { usePermissions } from '@/hooks/usePermissions';

const MyComponent = () => {
  const { hasPermission, canAccess } = usePermissions();
  
  if (!hasPermission('read_users')) {
    return <AccessDenied />;
  }
  
  return <UserManagement />;
};
```

## 🎨 UI Components

Built with shadcn/ui and custom components:

- **Layout Components** - AppLayout, Sidebar, Header
- **Form Components** - Input, Select, DatePicker, etc.
- **Data Display** - Tables, Cards, Charts
- **Navigation** - Breadcrumbs, Pagination, Tabs
- **Feedback** - Alerts, Toasts, Loading states

## 🔌 API Integration

The frontend integrates with the SpeakersBot Admin API:

- **Authentication** - JWT-based authentication
- **User Management** - CRUD operations for users
- **Speaker Management** - Speaker profile management
- **Opportunity Management** - Speaking opportunity handling
- **Subscription Management** - Stripe payment integration
- **Analytics** - Dashboard data and reporting

## 🚀 Deployment

### Development
```bash
npm run dev
```

### Production Build
```bash
npm run build
```

### Preview Production Build
```bash
npm run preview
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 Environment Variables

Create a `.env` file in the root directory:

```env
VITE_API_BASE_URL=http://localhost:3000
VITE_APP_NAME=SpeakersBot Admin
VITE_APP_VERSION=1.0.0
```

## 🐛 Troubleshooting

### Common Issues

1. **Port already in use**
   ```bash
   # Kill process on port 5173
   npx kill-port 5173
   ```

2. **Dependencies issues**
   ```bash
   # Clear cache and reinstall
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **TypeScript errors**
   ```bash
   # Check TypeScript configuration
   npx tsc --noEmit
   ```

## 📚 Documentation

- [RBAC Implementation Guide](./src/docs/RBAC_IMPLEMENTATION.md)
- [API Documentation](../node/openapi-spec.yaml)
- [Component Library](./src/components/README.md)

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation in the `/docs` folder

---

**Built with ❤️ by the SpeakersBot Team**
