
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");

const opportunityService = require("../services/opportunity-service");
const ApiResponse = require("../helpers/api-response");
const { exportAsCSV } = require("../helpers/csv-helper");

/**
 * Get all opportunities with filters and pagination
 */
exports.getAllOpportunities = async (req, res, next) => {

    try {
        const result = await opportunityService.getAllOpportunities(req);
        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data: { opportunities: data.opportunities, summary: data.summary     }, pagination: data.pageData }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
};

/**
 * Get opportunity by id
 */
exports.getOpportunityById = async (req, res, next) => {

    try {
        const result = await opportunityService.getOpportunityById(req);
        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data: { opportunity: data.opportunity, speakerCount: data.speakerCount } }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
};

/**
 * Update opportunity by id
 */
exports.updateOpportunity = async (req, res, next) => {

    try {
        const result = await opportunityService.updateOpportunity(req);
        const { status, message } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
};

exports.createOpportunity = async (req, res, next) => {

    try {
        const result = await opportunityService.createOpportunity(req);
        const { status, message } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
};
 
exports.exportOpportunities = async (req, res, next) => {
    try {
      const result = await opportunityService.exportOpportunities(req); 
     
      const { status, data, fileName } = result;

      return exportAsCSV(res, data, fileName || 'opportunities.csv');
        
        
    } catch (error) {
        next(error);
    }
};


exports.importOpportunities = async (req, res, next) => {

    try {
        const result = await opportunityService.importOpportunities(req,res);
        const { status, message } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);

    } catch (error) {
        next(error);
    }
};


exports.getOpportunitySpeakers = async (req, res, next) => {
    try {
        const result = await opportunityService.getOpportunitySpeakers(req);
        const { status, message, data, pageData } = result; 

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data,pagination: pageData }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    }
    catch (error) {
        next(error);
    }
};

exports.deleteOpportunity = async (req, res, next) => {
    try {
        const result = await opportunityService.deleteOpportunity(req);
        const { status, message } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
        
    }
    catch (error) {
        next(error);
    }
};

exports.getDistinctEventTypes = async (req, res, next) => {
    try {
        
        const result = await opportunityService.getDistinctEventTypes(req);
        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    }
    catch (error) {
        next(error);
    }
};

exports.getDistinctCities = async (req, res, next) => {
    try {
        const result = await opportunityService.getDistinctCities(req);
        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    }
    catch (error) {
        next(error);
    }
};


exports.generateSampleCSV = async (req, res, next) => {
    try {
        const result = await opportunityService.generateSampleCSV(req);
        const { status, data: columns, fileName } = result;

        if (!status || !Array.isArray(columns)) {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
        }

        // Ensure columns are strings and exclude any timestamp fields just in case
        const exclude = new Set(['created_at','updated_at','deleted_at','createdAt','updatedAt','deletedAt']);
        const headerCols = columns
            .map(c => String(c).trim())
            .filter(c => c && !exclude.has(c));

        const headerLine = headerCols.join(',') + '\n';

        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="${fileName || 'sample_opportunities.csv'}"`);

        return res.status(RESPONSE_CODES.SUCCESS).send(headerLine);
    } catch (error) {
        next(error);
    }
};
