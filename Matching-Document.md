# Speaker-Opportunity Matching System Documentation

## Overview

The SpeakerBot platform employs an optimized hybrid matching system that connects speakers with relevant speaking opportunities. The current implementation uses a weighted rule-based scorer augmented by semantic similarity (Sentence Transformers) for non-exact text comparisons, with batch processing and background workers for performance and reliability.

## Matching Techniques

### Hybrid Scoring Algorithm

The system employs a **hybrid approach** that combines multiple matching techniques:

#### Exact Matching (Primary Technique)
- **Purpose**: Fast, accurate matching for identical terms and phrases
- **Process**: Direct string comparison between speaker and opportunity fields
- **Advantage**: 100% accuracy for exact matches with minimal processing overhead
- **Application**: First-level check before applying semantic analysis

#### Semantic Similarity (Secondary Technique)
- **Technology**: Sentence Transformers (`all-MiniLM-L6-v2`)
- **Purpose**: Understand meaning and context beyond exact word matching
- **Process**: Embeddings + cosine similarity (manual NumPy implementation)
- **Advantage**: Matches related concepts even when different terminology is used
- **Application**: Applied when exact matching fails to find matches

#### Weighted Scoring System
The system uses a 40/30/20/10 weight distribution:
- **Primary Category**: 40%
- **Subcategory**: 30%
- **Geographic Location**: 20%
- **Topic**: 10%

## Fields Used for Matching

### Speaker Profile Fields

#### Primary Matching Fields
- **Primary Category**: Main expertise area (Technology, Business, Healthcare, Education, etc.)
- **Subcategory**: Specific specialization within the primary category (AI, Marketing, Cardiology, etc.)
- **Topic**: Detailed topic focus and expertise areas (Machine Learning, Digital Marketing, etc.)

#### Geographic Information
- **City, State, Country**: Speaker's physical location
- **Preferred Speaker Geography**: Preferred regions for speaking engagements



### Opportunity Profile Fields

#### Event Information
- **Title**: Event or opportunity title
- **Description**: Detailed event description and content
- **Subcategory**: Event categorization and classification

#### Location Details
- **City, State, Country**: Event location
- **Venue**: Specific venue details
- **Is Virtual**: Whether the event is virtual or in-person





The system computes a weighted score per speaker–opportunity pair using four components:

- Primary category (40%): Speaker `primary_category` vs opportunity primary category (derived from `subcategory`). Exact match first; else semantic similarity.
- Subcategory (30%): Speaker `subcategory` vs opportunity `subcategory`. Exact match first; else semantic similarity.
- Location (20%): Virtual or international preferences score 1.0; otherwise weighted country/state/city match (30/30/40). Sensible defaults if data is missing.
- Topic (10%): Speaker `topic` vs opportunity `title + description` via semantic similarity.

Each component is scored 0–1, then combined: `(0.4×primary) + (0.3×subcategory) + (0.2×location) + (0.1×topic)`. The combined score is scaled to 0–10 and rounded to two decimals. A pre-scale score ≥ 0.58 is considered a match. Explanations (reasons, strengths, concerns, recommendation, considerations) are generated from the component scores.


## Matching Algorithm Details

### Scoring Process

#### Primary Category Matching (40% Weight)
- **Derivation**: Get opportunity primary category from `subcategory` via DB join (`subcategories -> categories`)
- **Comparison**: Exact match first; else semantic similarity
- **Threshold**: Enforce minimum score floor, round to 2 decimals

#### Subcategory Matching (30% Weight)
- **Comparison**: Exact match first; else semantic similarity
- **Precision**: Round to 2 decimals

#### Geographic Location Matching (20% Weight)
- **Virtual Events**: Score 1.0
- **Global Preference**: If speaker prefers international → 1.0
- **Otherwise**: Weighted within-location components:
  - Country 30%, State 30%, City 40%
- **Multiple Preferences**: Handles comma/semicolon/and/or separated values
- **Fallback**: Default location score 0.2 when insufficient data

#### Topic Matching (10% Weight)
- **Content**: Speaker `topic` vs. opportunity `title + description`
- **Method**: Sentence Transformers semantic similarity

### Overall Score Calculation

#### Weighted Formula
- **Calculation**: (Primary × 0.4) + (Subcategory × 0.3) + (Location × 0.2) + (Topic × 0.1)
- **Scaling**: Multiply by 10 (0–10 output)
- **Precision**: Round to 2 decimals
- **Analysis**: Generate reasons, strengths, concerns, recommendation, considerations

#### Match Status Determination
- **Threshold**: Matched if overall score ≥ 0.58 (pre-scale)
- **Status**: "Matched" or "Not Matched"

## System Performance and Reliability

### Performance Optimizations
- **Batch Processing**: Process opportunities in batches of 50
- **Immediate Persistence**: Save each batch (bulk insert + bulk update)
- **DB Optimization**: Centralized session manager and bulk operations
- **Algorithm Efficiency**: Exact-match short-circuiting before semantic calls

### Reliability Features
- **Error Handling**: Comprehensive error handling and recovery
- **Data Integrity**: Transaction management and rollback capabilities
- **Monitoring**: Performance monitoring and logging
- **Scalability**: Designed to handle growing speaker and opportunity databases

<!-- API-specific sections removed per requirement to keep only matching logic -->

## Conclusion

The SpeakerBot matching system provides a sophisticated, reliable solution for connecting speakers with relevant opportunities. Through its hybrid scoring algorithm combining exact matching with semantic similarity, the system ensures accurate matches while maintaining high performance and scalability. The automatic background processing ensures that new opportunities are immediately matched with existing speakers, providing a seamless and efficient matching experience.

The system's weighted scoring approach prioritizes category relevance while considering geographic compatibility and topic alignment, resulting in meaningful matches that benefit both speakers and opportunity providers. With robust error handling, performance optimization, and scalable architecture, the matching system delivers consistent, reliable results for the SpeakerBot platform.
