"""
Utility Functions Module for Speaker Opportunity Scraper

This module contains utility functions for text processing, validation, and helper
operations used throughout the scraper service. These functions provide common
functionality for data cleaning, validation, and content analysis.

Key Functions:
- Date parsing and validation
- Text normalization and cleaning
- Location parsing and validation
- Content classification (job vs event detection)
- Domain analysis and scoring

Author: Speaker Bot Team
Version: 2.0 (Optimized)
Last Updated: 2024
"""

import re
from typing import Optional, Tuple, List, Dict, Any
from datetime import date, timedelta
from bs4 import BeautifulSoup
from dateparser import parse as parse_date

from .constants import (
    US_STATE_ABBR, US_STATE_FULL_NAMES, COUNTRY_NAMES, INTERNATIONAL_STATE_PATTERNS,
    VENUE_KEYWORDS, STREET_SUFFIXES, NOISE_WORDS, COMMON_CITY_PATTERNS,
    INTERNATIONAL_CITY_PATTERNS, EMAIL_RX, PHONE_RX, DATE_CANDIDATE_RX, ENHANCED_DATE_RX
)

# =============================================================================
# DATE PROCESSING UTILITIES
# =============================================================================


def parse_date_safe(date_string: str) -> Optional[date]:
    """
    Safely parse a date string using dateparser library.
    
    Args:
        date_string (str): Date string to parse
        
    Returns:
        Optional[date]: Parsed date object or None if parsing fails
        
    Example:
        >>> parse_date_safe("January 15, 2024")
        datetime.date(2024, 1, 15)
        >>> parse_date_safe("invalid date")
        None
    """
    try:
        dt = parse_date(date_string)
        return dt.date() if dt else None
    except Exception:
        return None


def validate_and_clean_dates(start_date: Optional[date], end_date: Optional[date]) -> Tuple[Optional[date], Optional[date]]:
    """
    Validate and clean event dates to ensure logical consistency.
    
    This function:
    - Filters out dates that are too far in the past (>1 year)
    - Ensures start_date comes before end_date
    - Handles cases where only one date is provided
    
    Args:
        start_date (Optional[date]): Event start date
        end_date (Optional[date]): Event end date
        
    Returns:
        Tuple[Optional[date], Optional[date]]: Cleaned (start_date, end_date)
        
    Example:
        >>> validate_and_clean_dates(date(2023, 1, 1), date(2024, 1, 1))
        (None, datetime.date(2024, 1, 1))  # start_date filtered as too old
    """
    if not start_date and not end_date:
        return None, None
    
    today = date.today()
    
    # Only filter out dates that are clearly in the past (more than 1 year ago)
    one_year_ago = today - timedelta(days=365)
    
    if start_date and start_date < one_year_ago:
        start_date = None
    
    if end_date and end_date < one_year_ago:
        end_date = None
    
    # If we have both dates, ensure start is before end
    if start_date and end_date:
        if start_date > end_date:
            # Swap dates if they're in wrong order
            start_date, end_date = end_date, start_date
    
    # If we only have end date, try to estimate start date
    if end_date and not start_date:
        # Assume event duration of 1-3 days
        start_date = end_date - timedelta(days=2)
    
    # If we only have start date, try to estimate end date
    if start_date and not end_date:
        # Assume event duration of 1-3 days
        end_date = start_date + timedelta(days=2)
    
    return start_date, end_date


def validate_and_clean_location(city: Optional[str], state: Optional[str], 
                               country: Optional[str], venue: Optional[str]) -> Tuple[Optional[str], Optional[str], Optional[str], Optional[str]]:
    """Validate and clean extracted location data"""
    # Clean city
    if city:
        city = city.strip()
        # Remove common prefixes/suffixes that aren't part of city name
        city = re.sub(r'^(?:city\s+of|town\s+of|village\s+of)\s+', '', city, flags=re.I)
        city = re.sub(r'\s+(?:city|town|village|borough)$', '', city, flags=re.I)
        # Ensure proper capitalization
        city = city.title()
        # Remove if too short or contains numbers
        if len(city) < 2 or any(c.isdigit() for c in city):
            city = None
    
    # Clean state
    if state:
        state = state.strip()
        # Convert to uppercase if it's a US state abbreviation
        if state.upper() in US_STATE_ABBR:
            state = state.upper()
        else:
            # Ensure proper capitalization for full state names
            state = state.title()
        # Remove if too short
        if len(state) < 2:
            state = None
    
    # Clean country
    if country:
        country = country.strip()
        # Normalize country name
        country_lower = country.lower()
        for name in COUNTRY_NAMES:
            if country_lower == name or country_lower in name or name in country_lower:
                if name in {'us', 'usa'}:
                    country = 'United States'
                elif name in {'uk', 'britain', 'great britain'}:
                    country = 'United Kingdom'
                elif name in {'uae', 'united arab emirates'}:
                    country = 'United Arab Emirates'
                elif name in {'holland'}:
                    country = 'Netherlands'
                else:
                    country = name.title()
                break
        # Remove if too short
        if len(country) < 2:
            country = None
    
    # Clean venue
    if venue:
        venue = venue.strip()
        # Remove common prefixes
        venue = re.sub(r'^(?:venue|location|hosted\s+at|held\s+at|takes\s+place\s+at)\s*[:\-]?\s*', '', venue, flags=re.I)
        # Remove common suffixes
        venue = re.sub(r'\s+(?:on|in|at|by|from|to|until|through|and|or|the|a|an)\s*$', '', venue, flags=re.I)
        # Remove if too short or doesn't contain venue keywords
        if len(venue) < 3 or not any(kw in venue.lower() for kw in VENUE_KEYWORDS):
            venue = None
    
    return city, state, country, venue


# =============================================================================
# TEXT PROCESSING UTILITIES
# =============================================================================

def normalize_text(text: Optional[str]) -> str:
    """
    Normalize text by stripping whitespace and handling None values.
    
    Args:
        text (Optional[str]): Text to normalize
        
    Returns:
        str: Normalized text (empty string if input is None)
        
    Example:
        >>> normalize_text("  Hello World  ")
        "Hello World"
        >>> normalize_text(None)
        ""
    """
    return (text or "").strip()


def strip_html(html_content: str) -> str:
    """
    Remove HTML tags from text content using BeautifulSoup for better parsing.
    
    This function uses BeautifulSoup to properly parse HTML and extract text,
    with a fallback to simple regex-based tag removal if BeautifulSoup fails.
    
    Args:
        html_content (str): HTML content to clean
        
    Returns:
        str: Plain text with HTML tags removed and normalized whitespace
        
    Example:
        >>> strip_html("<p>Hello <b>World</b></p>")
        "Hello World"
    """
    try:
        soup = BeautifulSoup(html_content, "html.parser")
        text = soup.get_text(" ", strip=True)
        text = re.sub(r"\s+", " ", text)
        return text.strip()
    except Exception:
        # Fallback simple tag removal if BeautifulSoup fails
        return re.sub(r"<[^>]+>", " ", html_content or "").strip()


# =============================================================================
# DOMAIN ANALYSIS UTILITIES
# =============================================================================

def org_from_domain(url: str) -> str:
    """
    Extract organization name from URL domain.
    
    This function extracts the main domain from a URL, handling various
    URL formats and subdomains to get the core organization domain.
    
    Args:
        url (str): Full URL to extract domain from
        
    Returns:
        str: Extracted domain name (e.g., "example.org" from "https://www.example.org/path")
        
    Example:
        >>> org_from_domain("https://www.example.org/conference")
        "example.org"
        >>> org_from_domain("https://subdomain.example.com")
        "example.com"
    """
    try:
        host = url.split("//", 1)[-1].split("/", 1)[0]
        host = host.lower()
        parts = host.split(".")
        if len(parts) >= 2:
            base = ".".join(parts[-2:])
        else:
            base = host
        return base
    except Exception:
        return ""


def domain_score(url: str) -> int:
    """
    Calculate domain score for prioritization based on domain type.
    
    This function assigns scores to domains based on their likelihood of
    containing legitimate speaking opportunities rather than job postings.
    
    Scoring:
    - Preferred domains (.org, association, society): +3 points
    - Secondary domains (.com): +2 points  
    - Tertiary domains (.gov, .edu): +1 point
    - Drop domains (job boards, social media): -999 points
    
    Args:
        url (str): URL to score
        
    Returns:
        int: Domain score (higher is better)
        
    Example:
        >>> domain_score("https://example.org/conference")
        3
        >>> domain_score("https://linkedin.com/jobs")
        -999
    """
    from .constants import DROP_DOMAINS, PREFERRED_DOMAINS, SECONDARY_DOMAINS, TERTIARY_DOMAINS
    
    host = org_from_domain(url)
    if any(d in host for d in DROP_DOMAINS):
        return -999
    score = 0
    # prefer association/society/org
    if any(key in host for key in PREFERRED_DOMAINS):
        score += 3
    elif any(key in host for key in SECONDARY_DOMAINS):
        score += 2
    if any(key in host for key in TERTIARY_DOMAINS):
        score += 1
    return score


# =============================================================================
# CONTENT CLASSIFICATION UTILITIES
# =============================================================================

def is_job_related(title: str, page_text: str) -> bool:
    """
    Determine if content is job-related rather than event-related.
    
    This function analyzes the content to identify job postings and filter them out,
    as they are not relevant for speaking opportunities.
    
    Args:
        title (str): Page title
        page_text (str): Full page text content
        
    Returns:
        bool: True if content appears to be job-related
        
    Example:
        >>> is_job_related("Software Engineer Position", "We are hiring...", "...")
        True
        >>> is_job_related("Tech Conference 2024", "Join us for...", "...")
        False
    """
    from .constants import JOB_KEYWORDS, EVENT_KEYWORDS
    
    hay = " ".join([title or "", page_text or ""]).lower()
    
    # Check for job-related keywords
    job_score = sum(1 for keyword in JOB_KEYWORDS if keyword in hay)
    
    # Check for event-related keywords
    event_score = sum(1 for keyword in EVENT_KEYWORDS if keyword in hay)
    
    # If job keywords significantly outnumber event keywords, it's likely a job posting
    if job_score > event_score and job_score >= 2:
        return True
    
    # Check for specific job patterns using regex
    job_patterns = [
        r"we\s+are\s+hiring",
        r"join\s+our\s+team",
        r"career\s+opportunities",
        r"full[\s-]?time|part[\s-]?time",
        r"contract\s+position",
        r"job\s+opening",
        r"salary|benefits\s+package",
        r"work\s+from\s+home\s+job",
        r"remote\s+job",
        r"freelance\s+work"
    ]
    
    for pattern in job_patterns:
        if re.search(pattern, hay):
            return True
    
    return False


def is_event_related(title: str, page_text: str) -> bool:
    """
    Determine if content is event-related (conferences, workshops, etc.).
    
    This function validates that content is actually about events rather than
    other types of content like job postings or general information.
    
    Args:
        title (str): Page title
        page_text (str): Full page text content
        
    Returns:
        bool: True if content appears to be event-related
        
    Example:
        >>> is_event_related("Tech Conference 2024", "Join us for...", "...")
        True
        >>> is_event_related("About Our Company", "We provide...", "...")
        False
    """
    from .constants import EVENT_KEYWORDS
    
    hay = " ".join([title or "", page_text or ""]).lower()
    
    # Must contain at least one event keyword
    event_keywords_found = sum(1 for keyword in EVENT_KEYWORDS if keyword in hay)
    
    # Check for event-specific patterns
    event_patterns = [
        r"call\s+for\s+speakers",
        r"call\s+for\s+proposals",
        r"speaker\s+application",
        r"speaking\s+opportunities",
        r"keynote\s+speakers",
        r"panel\s+discussion",
        r"breakout\s+session",
        r"annual\s+(conference|meeting|summit)",
        r"submit\s+(proposal|abstract|paper)",
        r"conference\s+program",
        r"event\s+schedule"
    ]
    
    event_patterns_found = sum(1 for pattern in event_patterns if re.search(pattern, hay))
    
    return event_keywords_found >= 1 or event_patterns_found >= 1


def _normalize_country_token(tok: str) -> Optional[str]:
    """Optimized country token normalization with better matching"""
    t = (tok or '').strip().lower().replace('.', '').replace(',', '')
    if not t:
        return None
    
    # Pre-compiled country mappings for better performance
    if not hasattr(_normalize_country_token, '_country_mappings'):
        _normalize_country_token._country_mappings = {
            'us': 'United States', 'usa': 'United States', 'u s a': 'United States', 
            'u.s.a': 'United States', 'u.s': 'United States', 'america': 'United States', 
            'united states of america': 'United States',
            'uk': 'United Kingdom', 'britain': 'United Kingdom', 'great britain': 'United Kingdom',
            'england': 'United Kingdom', 'scotland': 'United Kingdom', 'wales': 'United Kingdom',
            'uae': 'United Arab Emirates', 'united arab emirates': 'United Arab Emirates',
            'holland': 'Netherlands',
            'czech republic': 'Czech Republic', 'czechia': 'Czech Republic'
        }
        # Create a set for faster lookup
        _normalize_country_token._country_set = set(COUNTRY_NAMES)
    
    mappings = _normalize_country_token._country_mappings
    country_set = _normalize_country_token._country_set
    
    # Handle common variations and abbreviations
    if t in mappings:
        return mappings[t]
    
    # Direct lookup in country names (optimized)
    if t in country_set:
        return t.title() if t not in {'us', 'usa', 'uk', 'uae', 'holland'} else mappings.get(t, t.title())
    
    # Partial matching for compound country names (optimized with early exit)
    for name in COUNTRY_NAMES:
        if t in name or name in t:
            return name.title() if name not in {'us', 'usa', 'uk', 'uae', 'holland'} else mappings.get(name, name.title())
    
    return None