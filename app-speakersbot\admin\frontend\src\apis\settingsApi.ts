import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import type { RootState } from "../store";

// Define the base URL for your API
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:8000";

export const settingsApi = createApi({
  reducerPath: "settingsApi",
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      headers.set("ngrok-skip-browser-warning", "true");
      const token = (getState() as RootState).auth?.token;

      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }

      headers.set("content-type", "application/json");
      return headers;
    },
  }),
  tagTypes: ["Settings"],
  keepUnusedDataFor: 0, // Disable caching - always fetch fresh data
  endpoints: (builder) => ({
    getPaymentSettings: builder.query({
      query: (params) => ({
        url: "/setting",
        method: "GET",
        params,
      }),
      providesTags: ["Settings"],
    }),
    updatePaymentSettings: builder.mutation({
      query: (settings) => ({
        url: "/settings",
        method: "POST",
        body: settings,
      }),
      invalidatesTags: ["Settings"],
    }),
    getGamificationRules: builder.query({
      query: () => ({
        url: "/gamification-rules",
        method: "GET",
      }),
      providesTags: ["Settings"],
    }),

    updateGamificationRules: builder.mutation({
      query: (rules) => ({
        url: "/gamification-rules",
        method: "PUT",
        body: rules,
      }),
      invalidatesTags: ["Settings"],
    }),
    getGamificationHistory: builder.query({
      query: ({ search, perPage, currentPage, type }) => {
        const params = new URLSearchParams();
        if (search) params.append("search", search);
        if (perPage) params.append("perPage", perPage);
        if (currentPage) params.append("currentPage", currentPage);
        if (type) params.append("type", type);

        return `/gamification-history?${params.toString()}`;
      },
      providesTags: ["Settings"],
    }),
  }),
});

export const {
  useGetPaymentSettingsQuery,
  useUpdatePaymentSettingsMutation,
  useGetGamificationRulesQuery,
  useUpdateGamificationRulesMutation,
  useGetGamificationHistoryQuery,
} = settingsApi;
