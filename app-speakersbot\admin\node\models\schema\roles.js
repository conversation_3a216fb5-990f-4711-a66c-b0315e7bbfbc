const connection = require('../connection');
const { DataTypes } = require('sequelize');

const Roles = connection.define('Roles', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    comment: 'Primary key for the role',
  },
  name: {
    type: DataTypes.STRING(50),
    allowNull: false,
    comment: 'Name of the role (must be unique)',
  },
  description: {
    type: DataTypes.STRING(255),
    allowNull: true,
    comment: 'Description of the role',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Record creation timestamp',
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Record last update timestamp',
  },
  deleted_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Record deletion timestamp(soft delete)',
  }

}, {
  tableName: 'roles',
  timestamps: true,
  createdAt: "created_at",
  updatedAt: "updated_at",
  paranoid: true,
  deletedAt: "deleted_at", 

});

module.exports = Roles;
