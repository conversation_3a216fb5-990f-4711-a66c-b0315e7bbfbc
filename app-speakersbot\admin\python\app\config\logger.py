import logging
from logging.handlers import Rota<PERSON>FileHandler
import os
import threading
from pathlib import Path
from typing import Optional

_CONFIGURED = False
_FILE_HANDLERS = {}  # Cache for shared file handlers
_HANDLER_LOCK = threading.Lock()  # Thread-safe access to file handlers

def _ensure_handlers(root_dir: Path) -> None:
    global _CONFIGURED
    if _CONFIGURED:
        return
    # Root logger config
    root = logging.getLogger()
    if root.handlers:
        _CONFIGURED = True
        return
    root.setLevel(logging.INFO)

    log_dir = root_dir
    log_dir.mkdir(parents=True, exist_ok=True)

    # Console handler with simple format
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)
    ch.setFormatter(logging.Formatter("%(levelname)s - %(message)s"))
    root.addHandler(ch)

    _CONFIGURED = True

def _get_or_create_file_handler(file_path: str) -> RotatingFileHandler:
    """
    Get or create a shared file handler for the given file path.
    This prevents multiple handlers from trying to rotate the same file.
    """
    with _HANDLER_LOCK:
        if file_path not in _FILE_HANDLERS:
            # Create a new handler with Windows-friendly settings
            handler = RotatingFileHandler(
                file_path, 
                maxBytes=5 * 1024 * 1024, 
                backupCount=3, 
                encoding="utf-8",
                delay=True  # Delay opening the file until first write
            )
            handler.setLevel(logging.INFO)
            handler.setFormatter(logging.Formatter("%(asctime)s - %(levelname)s - %(message)s"))
            _FILE_HANDLERS[file_path] = handler
        return _FILE_HANDLERS[file_path]

def get_logger(name: str, *, file_name: Optional[str] = None) -> logging.Logger:
    """
    Return a module logger. If file_name is provided, attach a file handler
    under app/logs/<file_name> (created if not exists). Multiple calls are idempotent.
    """
    # Determine logs directory relative to this file: app/logs
    base_dir = Path(__file__).resolve().parents[1]
    logs_dir = base_dir / "logs"
    _ensure_handlers(logs_dir)

    logger = logging.getLogger(name)
    # Ensure this logger emits INFO and above regardless of root defaults
    logger.setLevel(logging.INFO)
    if file_name:
        file_path = logs_dir / file_name
        target_path = str(file_path.resolve())
        
        # Check if this logger already has a handler for this file
        found_same_file = False
        for h in logger.handlers:
            if isinstance(h, RotatingFileHandler):
                try:
                    if str(Path(getattr(h, 'baseFilename', '')).resolve()) == target_path:
                        found_same_file = True
                        break
                except Exception:
                    pass
        
        if not found_same_file:
            # Get or create a shared file handler
            fh = _get_or_create_file_handler(target_path)
            logger.addHandler(fh)
        
        # Prevent double logging to console if not desired
        logger.propagate = True
    return logger

def cleanup_logging():
    """
    Clean up all file handlers when the application shuts down.
    This should be called during application shutdown to properly close file handles.
    """
    with _HANDLER_LOCK:
        for handler in _FILE_HANDLERS.values():
            try:
                handler.close()
            except Exception:
                pass  # Ignore errors during cleanup
        _FILE_HANDLERS.clear()


