import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON>er, Card<PERSON>itle } from "../ui/card";
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>ltip,
  Legend,
} from "recharts";
import {
  DollarOutlined,
  UserOutlined,
  TrophyOutlined,
} from "@ant-design/icons";
import { Button } from "../ui/button";
import { Badge } from "../ui/badge";
import { ScrollArea } from "../ui/scroll-area";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import {
  ExternalLink,
  Users,
  TrendingUp,
  DollarSign,
  Eye,
  MousePointer,
  AlertTriangle,
} from "lucide-react";
import { useGetAffiliateQuery } from "../../apis/dashboardApi";
import { InvalidTokenHandler } from "../common/InvalidTokenHandler";
import { Skeleton } from "../ui/skeleton";
import StatusTag from "../common/StatusTag";
import { useNavigate } from "react-router-dom";
import { useToast } from "../../hooks/use-toast";

const AffiliateAnalytics: React.FC = () => {
  const { data, isLoading, error } = useGetAffiliateQuery(undefined, {
    refetchOnMountOrArgChange: true,
    refetchOnReconnect: true,
  });
  const affiliate = data?.data;
  const navigate = useNavigate();

  // State for affiliate data
  const [affiliateState, setAffiliateState] = useState<any>(null);
  const { toast } = useToast();

  // Effect to handle data and show success toast when data loads
  useEffect(() => {
    if (data) {
      setAffiliateState(data);
      // Show success toast when data loads
      // toast({
      //   title: "Affiliate analytics loaded successfully",
      // });
    }
  }, [data, toast]);

  // Show error toast when there's an error
  useEffect(() => {
    if (error) {
      toast({
        title: "Failed to load affiliate data",
        variant: "destructive",
      });
    }
  }, [error, toast]);

  const parseCurrencyToNumber = (val: any) => {
    if (val === undefined || val === null) return 0;
    if (typeof val === "number") return val;
    const cleaned = String(val).replace(/[$,\s]/g, "");
    const num = parseFloat(cleaned);
    return Number.isNaN(num) ? 0 : num;
  };

  const topAffiliates = (affiliate?.topAffiliateByPerformance || []).map(
    (a: any, idx: number) => ({
      id: idx + 1,
      name: a.affiliateName,
      signups: a.signups,
      revenue: a.revenue, // now numeric
      conversionRate: a.convRate, // now numeric
      commission: a.commission, // now numeric
      clicks: a.clicks,
      status: a.status,
      tier: a.tier,
      joinedDate: a.joined,
    })
  );
  const revenueByAffiliate = (affiliate?.revenueByAffiliate || []).map(
    (a: any) => ({
      name: a.label,
      revenue: a.revenue,
      commission: a.commission,
    })
  );

  const referralPipeline = (affiliate?.topRefferersByAffilate || []).map(
    (p: any) => ({
      referrer: p.referrer,
      speaker: p.speaker,
      signupDate: p.signupDate,
      opportunitiesApplied: p.applied,
      opportunitiesBooked: p.booked,
      totalRevenue: p.revenue, // now numeric
      commission: p.commission, // now numeric (may be null)
      status: p.status,
    })
  );

  if (isLoading) {
    return (
      <>
        <InvalidTokenHandler error={error} />
        <div className="space-y-6">
          <div>
            <Skeleton className="h-5 w-72" />
            <Skeleton className="h-4 w-96 mt-2" />
          </div>

          {/* KPI Cards Skeleton (5) */}
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            {Array.from({ length: 5 }).map((_, idx) => (
              <Card key={idx} className="bg-tertiary border-border">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="space-y-2">
                      <Skeleton className="h-4 w-24" />
                      <Skeleton className="h-7 w-20" />
                      <Skeleton className="h-3 w-28" />
                    </div>
                    <Skeleton className="h-8 w-8 rounded" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Top Affiliates Table Skeleton */}
          <Card className="bg-tertiary border-border">
            <CardHeader className="flex flex-row items-center justify-between">
              <Skeleton className="h-6 w-64" />
              <Skeleton className="h-9 w-36" />
            </CardHeader>
            <CardContent>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      <Skeleton className="h-4 w-28" />
                    </TableHead>
                    <TableHead>
                      <Skeleton className="h-4 w-16" />
                    </TableHead>
                    <TableHead>
                      <Skeleton className="h-4 w-12" />
                    </TableHead>
                    <TableHead>
                      <Skeleton className="h-4 w-14" />
                    </TableHead>
                    <TableHead className="text-right">
                      <Skeleton className="h-4 w-12 ml-auto" />
                    </TableHead>
                    <TableHead className="text-right">
                      <Skeleton className="h-4 w-12 ml-auto" />
                    </TableHead>
                    <TableHead className="text-right">
                      <Skeleton className="h-4 w-16 ml-auto" />
                    </TableHead>
                    <TableHead className="text-right">
                      <Skeleton className="h-4 w-16 ml-auto" />
                    </TableHead>
                    <TableHead className="text-right">
                      <Skeleton className="h-4 w-16 ml-auto" />
                    </TableHead>
                    <TableHead>
                      <Skeleton className="h-4 w-20" />
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Array.from({ length: 5 }).map((_, idx) => (
                    <TableRow key={idx}>
                      <TableCell className="font-medium">
                        <Skeleton className="h-4 w-40" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-6 w-20 rounded-full" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-6 w-16 rounded-full" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-6 w-20 rounded-full" />
                      </TableCell>
                      <TableCell className="text-right">
                        <Skeleton className="h-4 w-12 ml-auto" />
                      </TableCell>
                      <TableCell className="text-right">
                        <Skeleton className="h-4 w-10 ml-auto" />
                      </TableCell>
                      <TableCell className="text-right">
                        <Skeleton className="h-4 w-12 ml-auto" />
                      </TableCell>
                      <TableCell className="text-right">
                        <Skeleton className="h-4 w-16 ml-auto" />
                      </TableCell>
                      <TableCell className="text-right">
                        <Skeleton className="h-4 w-16 ml-auto" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-24" />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>

          {/* Referral Pipeline Table Skeleton */}
          <Card className="bg-tertiary border-border">
            <CardHeader>
              <Skeleton className="h-6 w-96" />
            </CardHeader>
            <CardContent>
              <div className="h-[400px]">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>
                        <Skeleton className="h-4 w-20" />
                      </TableHead>
                      <TableHead>
                        <Skeleton className="h-4 w-20" />
                      </TableHead>
                      <TableHead>
                        <Skeleton className="h-4 w-24" />
                      </TableHead>
                      <TableHead className="text-right">
                        <Skeleton className="h-4 w-12 ml-auto" />
                      </TableHead>
                      <TableHead className="text-right">
                        <Skeleton className="h-4 w-12 ml-auto" />
                      </TableHead>
                      <TableHead className="text-right">
                        <Skeleton className="h-4 w-16 ml-auto" />
                      </TableHead>
                      <TableHead className="text-right">
                        <Skeleton className="h-4 w-16 ml-auto" />
                      </TableHead>
                      <TableHead>
                        <Skeleton className="h-4 w-16" />
                      </TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Array.from({ length: 6 }).map((_, idx) => (
                      <TableRow key={idx}>
                        <TableCell className="font-medium">
                          <Skeleton className="h-4 w-32" />
                        </TableCell>
                        <TableCell>
                          <Skeleton className="h-4 w-28" />
                        </TableCell>
                        <TableCell>
                          <Skeleton className="h-4 w-24" />
                        </TableCell>
                        <TableCell className="text-right">
                          <Skeleton className="h-4 w-10 ml-auto" />
                        </TableCell>
                        <TableCell className="text-right">
                          <Skeleton className="h-4 w-10 ml-auto" />
                        </TableCell>
                        <TableCell className="text-right">
                          <Skeleton className="h-4 w-12 ml-auto" />
                        </TableCell>
                        <TableCell className="text-right">
                          <Skeleton className="h-4 w-12 ml-auto" />
                        </TableCell>
                        <TableCell>
                          <Skeleton className="h-6 w-24 rounded-full" />
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>

          {/* Chart Skeleton */}
          <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
            <Card className="bg-tertiary border-border">
              <CardHeader className="flex flex-row items-center justify-between">
                <Skeleton className="h-6 w-72" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-[300px] w-full" />
              </CardContent>
            </Card>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <InvalidTokenHandler error={error} />
      <div className="space-y-6">
        <div>
          <h3 className="text-xl font-semibold text-foreground mb-2">
            Affiliate and Referral View
          </h3>
          <p className="text-muted-foreground text-sm mb-6">
            Track affiliate partner performance and referral conversion metrics
          </p>
        </div>

        {/* Show error state */}
        {error && (
          <Card className="bg-tertiary border-border">
            <CardContent className="p-8 text-center">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-foreground mb-2">
                Error Loading Data
              </h3>
              <p className="text-muted-foreground">
                Unable to fetch affiliate analytics data. Please try again
                later.
              </p>
            </CardContent>
          </Card>
        )}

        {/* Show data when loaded and no error */}
        {!isLoading && !error && (
          <>
            {/* KPI Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4">
              <Card className="bg-tertiary border-border">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Total Revenue
                      </p>
                      <p className="text-2xl font-bold text-foreground">{`$${(
                        affiliate?.summary?.[0]?.value ?? 0
                      ).toLocaleString()}`}</p>
                      <p
                        className={`text-xs mt-1 ${
                          (affiliate?.summary?.[0]?.trend ?? 0) >= 0
                            ? "text-green-500"
                            : "text-red-500"
                        }`}
                      >
                        {`${
                          (affiliate?.summary?.[0]?.trend ?? 0) >= 0
                            ? "+"
                            : ""
                        }${
                          affiliate?.summary?.[0]?.trend ?? 0
                        }% vs last month`}
                      </p>
                    </div>
                    <DollarSign className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-tertiary border-border">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Active Affiliates
                      </p>
                      <p className="text-2xl font-bold text-foreground">
                        {affiliate?.summary?.[1]?.value ?? 0}
                      </p>
                      <p
                        className={`text-xs mt-1 ${
                          (affiliate?.summary?.[1]?.trend ?? 0) >= 0
                            ? "text-green-500"
                            : "text-red-500"
                        }`}
                      >
                        {`${
                          (affiliate?.summary?.[1]?.trend ?? 0) >= 0
                            ? "+"
                            : ""
                        }${affiliate?.summary?.[1]?.trend ?? 0} this month`}
                      </p>
                    </div>
                    <Users className="h-8 w-8 text-dashboard-medium-blue" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-tertiary border-border">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Avg Conversion
                      </p>
                      <p className="text-2xl font-bold text-foreground">
                        {affiliate?.summary?.[2]?.value ?? 0}
                      </p>
                      <p
                        className={`text-xs mt-1 ${
                          (affiliate?.summary?.[2]?.trend ?? 0) >= 0
                            ? "text-green-500"
                            : "text-red-500"
                        }`}
                      >
                        {`${
                          (affiliate?.summary?.[2]?.trend ?? 0) >= 0
                            ? "+"
                            : ""
                        }${
                          affiliate?.summary?.[2]?.trend ?? 0 
                        }% improvement`}
                      </p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-dashboard-light-blue" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-tertiary border-border">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Total Clicks
                      </p>
                      <p className="text-2xl font-bold text-foreground">
                        {(
                          affiliate?.summary?.[3]?.value ?? 0
                        ).toLocaleString()}
                      </p>
                      <p className="text-xs text-blue-500 mt-1">
                        {affiliate?.summary?.[3]?.subtext}
                      </p>
                    </div>
                    <MousePointer className="h-8 w-8 text-dashboard-dark-blue" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-tertiary border-border">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Total Commission
                      </p>
                      <p className="text-2xl font-bold text-foreground">
                        {affiliate?.summary?.[4]?.value ?? 0 }
                      </p>
                      <p className="text-xs text-green-500 mt-1">
                        {affiliate?.summary?.[4]?.subtext ?? 0}
                      </p>
                    </div>
                    <DollarOutlined className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Top Affiliates Detailed Table */}
            <Card className="bg-tertiary border-border">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-foreground">
                  Top Affiliates by Performance
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => navigate("/admin/affiliates")}
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Manage Affiliates
                </Button>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-center">Affiliate Name</TableHead>
                      <TableHead className="text-center">Status</TableHead>
                      <TableHead className="text-center">Tier</TableHead>
                      <TableHead className="text-center">Clicks</TableHead>
                      <TableHead className="text-center">Signups</TableHead>
                      <TableHead className="text-center">Conv. Rate</TableHead>
                      <TableHead className="text-center">Revenue</TableHead>
                      <TableHead className="text-center">Commission</TableHead>
                      <TableHead className="text-center">Joined</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    { topAffiliates.length > 0 && topAffiliates.map((affiliate) => (
                      <TableRow key={affiliate.id}>
                        <TableCell className="font-medium text-center">
                          {affiliate.name}
                        </TableCell>
                        <TableCell className="text-center">
                          {affiliate.status && (
                              <StatusTag status={affiliate.status?.charAt(0).toUpperCase() + affiliate.status?.slice(1)} />
                          )}
                        </TableCell>
                        <TableCell className="text-center">
                          {
                            affiliate.tier!=="" && (
                              <Badge 
                              variant="outline" 
                              className={`font-medium ${
                                affiliate.tier === 'Gold' ? 'text-yellow-800 border-yellow-300' :
                                affiliate.tier === 'Silver' ? 'text-gray-800 border-gray-300' :
                                affiliate.tier === 'Bronze' ? 'text-orange-800 border-orange-300' :
                                affiliate.tier === 'Platinum' ? 'text-blue-800 border-blue-300' :
                                'text-blue-800 border-blue-300'
                              }`}
                            >
                              {affiliate.tier}
                            </Badge>
                            )
                          }
                        </TableCell>
                        <TableCell className="text-center">
                          {affiliate.clicks.toLocaleString()}
                        </TableCell>
                        <TableCell className="font-medium text-center">
                          {affiliate.signups}
                        </TableCell>
                        <TableCell className="text-center">
                          {affiliate.conversionRate}%
                        </TableCell>
                        <TableCell className="font-medium text-center">
                          ${affiliate.revenue?.toLocaleString()}
                        </TableCell>
                        <TableCell className="text-green-600 font-medium text-center">
                          ${affiliate.commission?.toLocaleString()}
                        </TableCell>
                        <TableCell className="text-muted-foreground text-sm text-center">
                          {affiliate.joinedDate}
                        </TableCell>
                      </TableRow>
                    ))}
                    { topAffiliates.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={10} className="text-center">No Top Affiliates Data Available</TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>

            {/* Referral Pipeline */}
            <Card className="bg-tertiary border-border">
              <CardHeader>
                <CardTitle className="text-foreground">
                  Referrer → Speaker → Opportunity Pipeline
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* <ScrollArea className="h-[400px]"> */}
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-center">Referrer</TableHead>
                      <TableHead className="text-center">Speaker</TableHead>
                      <TableHead className="text-center">Signup Date</TableHead>
                      <TableHead className="text-center">Applied</TableHead>
                      <TableHead className="text-center">Booked</TableHead>
                      <TableHead className="text-center">Revenue</TableHead>
                      <TableHead className="text-center">Commission</TableHead>
                      <TableHead className="text-center">Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    { referralPipeline.length > 0 && referralPipeline.map((pipeline, index) => (
                      <TableRow key={index}>
                        <TableCell className="text-center font-medium">
                          {pipeline.referrer}
                        </TableCell>
                        <TableCell className="text-center">{pipeline.speaker}</TableCell>
                        <TableCell className="text-center text-muted-foreground text-sm">
                          {pipeline.signupDate}
                        </TableCell>
                        <TableCell className="text-center">
                          {pipeline.opportunitiesApplied}
                        </TableCell>
                        <TableCell className="font-medium text-center">
                          {pipeline.opportunitiesBooked}
                        </TableCell>
                        <TableCell className="text-center">
                          ${pipeline.totalRevenue}
                        </TableCell>
                        <TableCell className="text-center text-green-600 font-medium">
                          ${pipeline.commission}
                        </TableCell>
                        <TableCell className="text-center">
                          {pipeline.status && (
                            <StatusTag status={pipeline.status?.charAt(0).toUpperCase() + pipeline.status?.slice(1)} />
                          )}
                        </TableCell>
                      </TableRow>
                    ))}
                    { referralPipeline.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={10} className="text-center">No Referral Pipeline Data Available</TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
                {/* </ScrollArea> */}
              </CardContent>
            </Card>

            {/* Charts */}
            <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
              <Card className="bg-tertiary border-border">
                <CardHeader className="flex flex-row items-center justify-between">
                  <CardTitle className="text-foreground">
                    Revenue by Affiliate
                  </CardTitle>
                </CardHeader>
                <CardContent>
                 {
                  revenueByAffiliate.length > 0 ? (
                    <ResponsiveContainer width="100%" height={400}>
                    <BarChart
                      data={revenueByAffiliate}
                      margin={{ top: 20, right: 30, left: 20, bottom: 100 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="name"
                        angle={-45}
                        textAnchor="end"
                        height={80}
                      />
                      <YAxis />
                      <Tooltip
                        separator=""
                        formatter={(
                          value: any,
                          _name: any,
                          { payload }: any
                        ) => [`${payload?.name}: $${value}`, ""]}
                        labelFormatter={() => ""}
                        contentStyle={{
                          backgroundColor: "hsl(var(--muted-foreground))",
                          border: "1px solid hsl(var(--border))",
                          borderRadius: "6px",
                        }}
                      />
                      <Bar
                        dataKey="revenue"
                        fill="hsl(var(--dashboard-dark-blue))"
                        name="Revenue ($)"
                      />
                    </BarChart>
                  </ResponsiveContainer>
                  ) : (
                    <div className="flex justify-center items-center h-full">
                      <p className="text-muted-foreground text-sm">No Revenue Data Available</p>
                    </div>
                  )
                 }
                </CardContent>
              </Card>
            </div>
          </>
        )}
      </div>
    </>
  );
};
export default AffiliateAnalytics;
