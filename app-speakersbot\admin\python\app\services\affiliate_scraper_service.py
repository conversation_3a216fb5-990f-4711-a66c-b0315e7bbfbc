"""
Optimized Affiliate Service
Service for scraping affiliate information from websites using modular architecture

This service provides comprehensive web scraping functionality to extract affiliate
program information from websites. It uses a modular architecture with specialized
components for different aspects of the scraping process.

Key Features:
- Modular architecture with specialized components
- Robust web scraping with retry mechanisms
- Comprehensive data extraction from multiple sources
- Error handling and fallback strategies
- Performance monitoring and logging

Author: digitalspeakeragent.ai
Version: 1.0.0
"""
import time
from typing import Dict, Any, Optional
from datetime import datetime
from app.config.logger import get_logger
logger = get_logger(__name__, file_name="scraper.log")
from .affiliate.database import AffiliateDatabase
from .affiliate.extractors import AffiliateExtractors
from .affiliate.page_loader import Affiliate<PERSON>age<PERSON>oader, ScraperHttpError
from .affiliate.utils import AffiliateUtils

class AffiliateService:

    """
    Optimized service for scraping affiliate information from websites
    This class provides comprehensive web scraping capabilities for extracting
    affiliate program information using a modular architecture with specialized
    components for different aspects of the scraping process.
    """

    def __init__(self):
        """Initialize the AffiliateService with modular components"""
        # Initialize modular components
        self.database = AffiliateDatabase()
        self.extractors = AffiliateExtractors()
        self.page_loader = AffiliatePageLoader()
        self.utils = AffiliateUtils()

    def scrape_affiliate_data(self, url: str, affiliate_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Scrape comprehensive affiliate information from a website
        This method orchestrates the entire scraping process by:
        1. Loading the webpage with robust error handling
        2. Extracting various data fields using specialized methods
        3. Updating database if affiliate_id is provided
        4. Returning structured data
        Args:
            url (str): The URL of the website to scrape
            affiliate_id (str, optional): The affiliate ID for tracking purposes
        Returns:
            Dict[str, Any]: Dictionary containing all scraped affiliate data
        Raises:
            ScraperHttpError: If HTTP error occurs during page loading
            Exception: If scraping fails due to other errors
        """
        start_time = time.time()
        try:
            # Load webpage using page loader
            soup, loaded_url = self.page_loader.load_page(url)
            # Extract all affiliate data fields using LLM-based extraction
            affiliate_data = self.extractors.extract_all_data_with_llm(soup, loaded_url)
            # Add affiliate_id if provided
            if affiliate_id:
                affiliate_data["affiliate_id"] = affiliate_id
            processing_time = time.time() - start_time
            # Update database with scraped data if affiliate_id is provided
            if affiliate_id:
                self._update_database_if_needed(affiliate_id, affiliate_data)
            else:
                logger.info("No affiliate_id provided, skipping database update")
                affiliate_data['database_updated'] = False
            return affiliate_data

        except Exception as e:
            logger.error(f"Error scraping affiliate data from {url}: {e}", exc_info=True)
            raise Exception(f"Error scraping affiliate data: {str(e)}")

    

    def _update_database_if_needed(self, affiliate_id: str, affiliate_data: Dict[str, Any]) -> None:
        """Update database if affiliate exists"""
        try:
            if self.database.check_affiliate_exists(affiliate_id):
                db_update_success = self.database.update_affiliate_metadata(affiliate_id, affiliate_data)
                affiliate_data['database_updated'] = db_update_success
            else:
                affiliate_data['database_updated'] = False
                affiliate_data['error'] = f"No affiliate found with ID: {affiliate_id}"
        except Exception as e:
            logger.error(f"Database update error for affiliate_id {affiliate_id}: {e}")
            affiliate_data['database_updated'] = False
            affiliate_data['error'] = f"Database error: {str(e)}"