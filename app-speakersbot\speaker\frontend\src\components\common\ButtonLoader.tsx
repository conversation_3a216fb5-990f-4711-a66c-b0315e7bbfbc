import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import type { ButtonProps } from "@/components/ui/button";

interface ButtonLoaderProps extends Omit<ButtonProps, 'disabled'> {
  loading?: boolean;
  loadingText?: string;
  disabled?: boolean;
}

const ButtonLoader = React.forwardRef<HTMLButtonElement, ButtonLoaderProps>(
  (
    {
      children,
      loading = false,
      loadingText = "Loading...",
      disabled,
      className,
      variant = "default",
      size = "default",
      ...props
    },
    ref
  ) => {
    return (
      <Button
        ref={ref}
        className={cn(className)}
        variant={variant}
        size={size}
        disabled={disabled || loading}
        {...props}
      >
        {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
        {children}
      </Button>
    );
  }
);

ButtonLoader.displayName = "ButtonLoader";

export default ButtonLoader;
