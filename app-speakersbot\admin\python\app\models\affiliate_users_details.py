from sqlalchemy import create_engine, Column, Integer, String, Text, Float, DateTime, JSON, func
from sqlalchemy.ext.declarative import declarative_base
from app.config.config import config

Base = declarative_base()

class AffiliateUsersDetails(Base):
    __tablename__ = "affiliate_users_details"
    
    # Primary key
    id = Column(Integer, primary_key=True, autoincrement=True, comment='Primary key for the affiliate user details record')
    
    # Foreign key
    affiliate_id = Column(Integer, nullable=False, comment='Foreign key referencing the user who is an affiliate')
    
    # Business information
    legal_business_name = Column(String(255), comment='Legal business name of the affiliate')
    website = Column(String(500), comment='Website URL of the affiliate business')
    
    # Commission and tracking
    commission_percentage = Column(Float, nullable=False, comment='Commission percentage assigned to the affiliate')
    click_count = Column(Integer, default=0, comment='Number of clicks generated by the affiliate')
    signup_count = Column(Integer, default=0, comment='Number of signups generated by the affiliate')
    conversion_count = Column(Integer, default=0, comment='Number of conversions generated by the affiliate')
    
    # Referral system
    referral_code = Column(String(255), comment='Unique referral code for the affiliate')
    
    # Metadata (JSON field for storing complex data)
    metadata_info = Column('metadata', JSON, comment='JSON metadata containing branding, offers, and compliance information')
    
    # Timestamps
    created_at = Column(DateTime, default=func.current_timestamp(), comment='Record creation timestamp')
    updated_at = Column(DateTime, default=func.current_timestamp(), onupdate=func.current_timestamp(), comment='Record last update timestamp')
    deleted_at = Column(DateTime, nullable=True, comment='Soft delete timestamp')


def create_affiliate_users_details_table():
    db_url = config.get_database_url()
    engine = create_engine(db_url)
    Base.metadata.create_all(engine)

if __name__ == "__main__":
    create_affiliate_users_details_table()
    print("Table 'affiliate_users_details' created in MySQL database")
