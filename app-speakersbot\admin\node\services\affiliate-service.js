const { parsePagination, getPagingData, buildSearchWhere<PERSON>lause, buildFilterWhereClause, parseJSONSafely } = require("../helpers/app-hepler");
const { Op, where } = require("sequelize");
const { exportAsCSV } = require("../helpers/csv-helper");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES } = require("../helpers/response-codes");
const { AffiliateUsersDetails, Users, Speakers, Subscriptions, PricingPlan, Roles, Settings, SpeakerDetails, sequelize } = require("../models");
const affiliateService = {};

// ------------------------- affiliate-service -------------------------

/**
 * Helper function to build sort order for affiliate queries.
 * Handles sorting for both affiliate fields and related user fields.
 * 
 * @param {Object} sort - Sort configuration object
 * @returns {Array} Array of sort clauses for Sequelize
 */
//  helper function to build sort order only for affiliate
function buildSortClause(sort) {
    const affiliateFields = Object.keys(AffiliateUsersDetails.rawAttributes);

    return Object.entries(sort).map(([field, dir]) => {
        const direction = dir.toUpperCase();

        if (affiliateFields.includes(field)) {
            return [field, direction];
        } else {
            return [{ model: Users, as: "user" }, field, direction];
        }
    });

}

/**
 * Get summary for all affiliates (not paginated) 
*/
async function getAffiliatesSummary() {

    const affiliates = await AffiliateUsersDetails.findAll();
    let totalClick = 0;
    let totalSignup = 0;
    let totalConversion = 0;

    affiliates.forEach(affiliate => {
        totalClick += affiliate.click_count;
        totalSignup += affiliate.signup_count;
        totalConversion += affiliate.conversion_count;
    });

    let overallConversion = totalClick ? (totalConversion / totalClick) * 100 : 0;

    return {
        totalClick,
        totalSignup,
        totalConversion,
        overallConversion
    };

};

/**
 * Get all affiliates with advanced filtering and pagination.
 * Supports search, filtering, sorting, and includes related user data.
 * 
 * @param {Object} getReq - Request object with query parameters
 * @param {Object} getReq.query - Query parameters
 * @param {string} [getReq.query.search] - Search term for name/email
 * @param {string} [getReq.query.filter] - JSON filter object
 * @param {string} [getReq.query.sort] - JSON sort object
 * @param {number} [getReq.query.page] - Page number
 * @param {number} [getReq.query.limit] - Items per page
 * @returns {Promise<Object>} Paginated affiliates data with user information
 * @throws {Error} When database operation fails
 */
affiliateService.getAffiliates = async (getReq) => {

    try {
        const { page, limit, offset } = parsePagination(getReq.query);

        const search = getReq.query.search;
        const filter = parseJSONSafely(getReq.query.filter, "Invalid JSON filter");
        const sort = parseJSONSafely(getReq.query.sort, "Invalid JSON sort");

        const searchFields = ['name', 'email'];

        let where = {};

        let sortOrder;

        // let totalClick = 0;
        // let totalSignup = 0;
        // let totalConversion = 0;

        if (search) {
            const searchWhere = buildSearchWhereClause(search, searchFields);
            where = { ...where, ...searchWhere };
        }

        if (filter) {
            const filterWhere = buildFilterWhereClause(filter);
            where = { ...where, ...filterWhere };
        }

        if (!sort || typeof sort !== "object" || Object.keys(sort).length === 0) {
            // Default: sort by AffiliateUsersDetails.id DESC
            sortOrder = [["id", "DESC"]];
        } else {
            sortOrder = buildSortClause(sort);
        }

        const { rows: affiliates, count: totalAffiliates } = await AffiliateUsersDetails.findAndCountAll({
            include: [{
                model: Users,
                as: 'user',
                attributes: { exclude: ['password'] },
                where: { ...where }
            }],
            limit,
            offset,
            order: [...sortOrder],
            distinct: true
        });

        // affiliates.forEach(affiliate => {
        //     totalClick += affiliate.click_count;
        //     totalSignup += affiliate.signup_count;
        //     totalConversion += affiliate.conversion_count;
        // });
        // let overallConversion = Number(totalConversion) / totalClick * 100 || 0;

        const summary = await getAffiliatesSummary()

        const pageData = getPagingData(totalAffiliates, limit, page);

        return {
            status: true,
            message: "Affiliates fetched successfully",
            data: { affiliates, summary },
            pageData: pageData
        };


    } catch (error) {
        console.error("Error fetching affiliates:", error);
        throw error;
    }
}

affiliateService.getAffiliateById = async (getReq) => {
    try {

        const id = getReq.params.id;


        const sort = parseJSONSafely(getReq.query.sort, "Invalid JSON sort");
        let sortOrder = [['created_at', 'DESC']];

        const affiliate = await AffiliateUsersDetails.findOne({
            where: { affiliate_id: id },
            include: [{
                model: Users,
                as: 'user',
                attributes: { exclude: ['password'] },

            }],

        });

        if (!affiliate) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Affiliate not found");
        }

        // If sort parameter is provided, use it
        if (sort && typeof sort === "object" && Object.keys(sort).length > 0) {
            sortOrder = Object.entries(sort).map(([field, direction]) => {
                const dir = direction.toUpperCase();
                return [field, dir];
            });
        }

        // const speaker = await Speakers.findAll({
        //     where: { affiliate_id: id },
        //     include: [{
        //         model: Subscriptions,
        //         as: 'subscription',
        //         include: [{ model: PricingPlan, as: 'plan' }]
        //     }],
        //     limit,
        //     offset,
        //     order: sortOrder,
        // });

        // const commissionBreakdown = await Settings.findAll({
        //     exclude: ['created_at', 'updated_at'],
        //     raw: true
        // })

        // const getSettingNumber = (key, defaultValue = 0) => {
        //     const found = commissionBreakdown.find(setting => setting.key === key);
        //     const value = found ? parseFloat(found.value) : defaultValue;
        //     return isNaN(value) ? defaultValue : value;
        // };

        // const perClickRate = getSettingNumber('per_click', 0);
        // const perSignupRate = getSettingNumber('per_signup', 0);
        // const perConversionRate = getSettingNumber('per_conversion', 0);
        
        const totalCommission = await Subscriptions.sum('affiliate_commission', {
            where: { affiliate_id: id }
        }) || 0;

        const breakdown = {
            total_commission: totalCommission,
        };
    
        const summary = {
            totalClick: affiliate.click_count,
            totalSignup: affiliate.signup_count,
            totalConversion: affiliate.conversion_count,
            overallConversion: affiliate.conversion_count / affiliate.click_count * 100
        };

        if (!affiliate) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Affiliate not found");
        }

        return {
            status: true,
            message: "Affiliate fetched successfully",
            data: { affiliate: affiliate, summary: summary, breakdown: breakdown },


        };
    } catch (error) {
        console.error("Error fetching affiliate by ID:", error);
        throw error;
    }
}

affiliateService.getAffiliateNames = async (getReq) => {
    try {

        const role = await Roles.findOne({ where: { name: "Affileate" } });

        const affiliates = await Users.findAll({
            where: { role_id: 2 },
            distinct: true,
            col: 'name',
            attributes: ['id', 'name']
        })

        let names = affiliates.map(affiliate => affiliate.name);

        return {
            status: true,
            message: "Affiliate names fetched successfully",
            data: names
        };

    } catch (error) {
        console.error("Error fetching affiliate names:", error);
        throw error;
    }
}


affiliateService.exportAffiliates = async (req, res) => {
    try {
        const { ids } = req.body || {};
        const search = req.query.search;
        const filter = parseJSONSafely(req.query.filter, "Invalid JSON filter");

        const searchFields = ['name', 'email'];
        let where = {};
        let affiliateWhere = {};

        if (search) {
            const searchWhere = buildSearchWhereClause(search, searchFields);
            where = { ...where, ...searchWhere };
        }

        if (filter) {
            const filterWhere = buildFilterWhereClause(filter);
            const userFields = ['name', 'email', 'is_active', 'phone', 'role_id', 'permission_ids', 'created_at', 'updated_at', 'password_changed_at', 'deleted_at'];
            Object.keys(filterWhere).forEach(key => {
                if (userFields.includes(key)) {
                    where[key] = filterWhere[key];
                } else {
                    affiliateWhere[key] = filterWhere[key];
                }
            });
        }

        if (ids && Array.isArray(ids) && ids.length > 0) {
            affiliateWhere.id = affiliateWhere.id ? { [Op.and]: [affiliateWhere.id, { [Op.in]: ids }] } : { [Op.in]: ids };
        }


        const affiliates = await AffiliateUsersDetails.findAll({
            where: affiliateWhere,
            include: [{
                model: Users,
                as: 'user',
                attributes: { exclude: ['password'] },
                where: { ...where },
                required: true // <-- This makes it an INNER JOIN
            }],
        });

        if (!affiliates || affiliates.length === 0) {
            return { status: true, data: [] };
        }


        const formattedAffiliates = affiliates.map(affiliate => ({
            id: affiliate.id,
            name: affiliate.user.name,
            email: affiliate.user.email,
            commission: affiliate.commission,
            commission_type: affiliate.commission_type,
            click_count: affiliate.click_count,
            signup_count: affiliate.signup_count,
            conversion_count: affiliate.conversion_count,
            referral_code: affiliate.referral_code,
            created_at: affiliate.created_at,
            updated_at: affiliate.updated_at
        }));



        return { status: true, data: formattedAffiliates };
    } catch (error) {
        console.error("Error exporting affiliates:", error);
        res.status(500).json({ message: "Error exporting affiliates" });
        throw error;
    }
}

affiliateService.getAffiliateSpeakers = async (getReq) => {

    try {

        const { page, limit, offset } = parsePagination(getReq.query);

        const id = getReq.params.id;

        const search = getReq.query.search;

        const searchFields = ['name', 'email'];

        const sort = parseJSONSafely(getReq.query.sort, "Invalid JSON sort");

        const dataRange = parseJSONSafely(getReq.query.dateRange, "Invalid JSON date range");

        let sortOrder = [['created_at', 'DESC']];

        if (sort && typeof sort === "object" && Object.keys(sort).length > 0) {
            sortOrder = Object.entries(sort).map(([field, direction]) => {
                const dir = direction.toUpperCase();
                return [field, dir];
            });
        }

        let where = {};

        if (dataRange && (dataRange.start_date || dataRange.end_date)) {
            where.created_at = { [Op.between]: [dataRange.start_date, dataRange.end_date] };
        }

        const filter = parseJSONSafely(getReq.query.filter, "Invalid JSON filter");

        if (filter) {
            const filterWhere = buildFilterWhereClause(filter);
            where = { ...where, ...filterWhere };
        }
        if (search) {
            const searchWhere = buildSearchWhereClause(search, searchFields);
            where = { ...where, ...searchWhere };
        }

        // Coerce id to number to avoid SQL injection when used in literals/queries
        const affiliateIdNum = Number(id) || id;

        // Fetch paginated speakers and total count in a single call
        const { rows: speakers, count: totalSpeakers } = await Speakers.findAndCountAll({
            where: { affiliate_id: affiliateIdNum, ...where },
            include: [{
                model: SpeakerDetails,
                as: 'details'
            }],
            order: sortOrder,
            limit,
            offset,
            distinct: true
        });

        // If no speakers, short-circuit
        const speakerIds = (speakers || []).map(s => s.id).filter(Boolean);

        // Fetch commission sums per speaker using a single grouped query (efficient)
        const commissionSumsMap = {};
        if (speakerIds.length > 0) {
            const commissionRows = await Subscriptions.findAll({
                attributes: [
                    'speaker_id',
                    [sequelize.fn('SUM', sequelize.col('affiliate_commission')), 'total_commission']
                ],
                where: {
                    affiliate_id: affiliateIdNum,
                    speaker_id: { [Op.in]: speakerIds }
                },
                group: ['speaker_id'],
                raw: true
            });

            commissionRows.forEach(row => {
                commissionSumsMap[row.speaker_id] = Number(row.total_commission) || 0;
            });
        }

        // Transform speakers and attach aggregated affiliate commission value
        const transformedSpeakers = (speakers || []).map(speaker => {
            const transformedSpeaker = { ...speaker.toJSON() };
            if (Array.isArray(transformedSpeaker.details)) {
                transformedSpeaker.details.forEach(detail => {
                    transformedSpeaker[detail.field_id] = detail.value;
                });
            }
            transformedSpeaker.affiliate_commission = commissionSumsMap[speaker.id] || 0;
            delete transformedSpeaker.details;
            return transformedSpeaker;
        });

        const pageData = getPagingData(totalSpeakers, limit, page);

        return {
            status: true,
            message: "Speakers fetched successfully",
            data: { speakers: transformedSpeakers, },
            pageData,
        };
    } catch (error) {
        console.error("Error fetching affiliate speakers:", error);
        throw error;
    }
}

affiliateService.getLandingPageData = async (affiliate_id) => {
    try {
        const data = await AffiliateUsersDetails.findOne({
            where: { affiliate_id },
            include: [{
                model: Users,
                as: 'user',
                attributes: ['id', 'name', 'email', 'phone']
            }]
        });

        if (!data) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Affiliate data not found");
        }
        const affiliateData = {
            id: data.id,
            name: data.user ? data.user.name : "",
            company_legal: data.legal_business_name,
            contact: {
                email: data.user ? data.user.email : "",
                phone: data.user?.phone || "",
                website: data.website || ""
            },
            affiliate_program: {
                affiliate_link_url: data.referral_code,
                commission: data.commission,
                commission_type: data.commission_type
            },
            ...data?.metadata
        }

        return {
            status: true,
            message: "Landing page data fetched successfully",
            data: affiliateData
        };


    } catch (error) {
        console.error("Error fetching landing page data:", error);
        throw error;
    }
}

affiliateService.updateLandingPageData = async (affiliate_id, formData) => {
    try {
        // Parse input
        const updateData = typeof formData === 'string'
            ? JSON.parse(formData)
            : formData;

        const data = await AffiliateUsersDetails.findOne({
            where: { affiliate_id },
            include: [{
                model: Users,
                as: 'user',
                attributes: ['id', 'name', 'email', 'phone']
            }]
        });

        if (!data) {
            throw new CustomError(RESPONSE_CODES.NOT_FOUND, "Affiliate data not found");
        }

        const affiliateUpdates = {
            ...(updateData.company_legal && { legal_business_name: updateData.company_legal }),
            ...(updateData.contact?.website && { website: updateData.contact.website }),
            ...(updateData.affiliate_program?.commission && { commission: updateData.affiliate_program.commission })
        };

        const userUpdates = {
            ...(updateData.name && { name: updateData.name }),
            ...(updateData.contact?.phone && { phone: updateData.contact.phone })
        };

        const metadataFields = ['branding', 'free_offer', 'value_to_speakers', 'admin_and_compliance'];
        const metadata = Object.fromEntries(
            metadataFields
                .filter(field => updateData[field] !== undefined)
                .map(field => [field, updateData[field]])
        );
        if (Object.keys(metadata).length > 0) {
            affiliateUpdates.metadata = metadata;
        }

        await sequelize.transaction(async (transaction) => {
            if (Object.keys(userUpdates).length) {
                await Users.update(userUpdates, { where: { id: data.user.id }, transaction });
            }
            if (Object.keys(affiliateUpdates).length) {
                await AffiliateUsersDetails.update(affiliateUpdates, { where: { affiliate_id }, transaction });
            }
        });

        // Fetch updated data
        const updatedData = await AffiliateUsersDetails.findOne({
            where: { affiliate_id },
            include: [{
                model: Users,
                as: 'user',
                attributes: ['id', 'name', 'email', 'phone']
            }]
        });

        return {
            status: true,
            message: "Landing page data updated successfully",
            data: {
                id: updatedData.id,
                name: updatedData.user?.name || "",
                company_legal: updatedData.legal_business_name,
                contact: {
                    email: updatedData.user?.email || "",
                    phone: updatedData.user?.phone || "",
                    website: updatedData.website || ""
                },
                affiliate_program: {
                    affiliate_link_url: updatedData.referral_code,
                    commission: updatedData.commission
                },
                ...updatedData.metadata
            }
        };

    } catch (error) {
        console.error("Error updating landing page data:", error);
        throw error;
    }
};



module.exports = affiliateService;