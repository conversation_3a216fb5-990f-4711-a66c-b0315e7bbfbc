const connection = require('../connection');
const { DataTypes } = require('sequelize');

const Setting = connection.define('Setting', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
    comment: 'Primary key for the setting',
  },
  key: {
    type: DataTypes.STRING(100),
    allowNull: false,
    comment: 'Unique key for the setting',
  },
  value: {
    type: DataTypes.TEXT,
    allowNull: false,
    comment: 'Value associated with the setting key',
  },
  is_active: {
    type: DataTypes.BOOLEAN,
    defaultValue: true,
    comment: 'Indicates if the setting is active',
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Record creation timestamp',
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: connection.literal('CURRENT_TIMESTAMP'),
    comment: 'Record last update timestamp',
  },
  deleted_at: {
    type: DataTypes.DATE,
    allowNull: true,
    comment: 'Record deletion timestamp(soft delete)',
  }
}, {
  tableName: 'settings',
  timestamps: true,
  createdAt: "created_at",
  updatedAt: "updated_at",
  paranoid: true,
  deletedAt: "deleted_at", 

});
module.exports = Setting;