import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";
import type { RootState } from "../store";

// Define the base URL for your API
const API_BASE_URL =
  import.meta.env.VITE_API_BASE_URL || "http://localhost:8000";
export const usersApi = createApi({
  reducerPath: "usersApi",
  baseQuery: fetchBaseQuery({
    baseUrl: API_BASE_URL,
    prepareHeaders: (headers, { getState }) => {
      // Get the token from the state
      headers.set("ngrok-skip-browser-warning", "true");
      const token = (getState() as RootState).auth?.token;

      // If we have a token, set the authorization header
      if (token) {
        headers.set("authorization", `Bearer ${token}`);
      }

      // Set content type
      headers.set("content-type", "application/json");

      return headers;
    },
  }),
  tagTypes: ["User"],
  keepUnusedDataFor: 0,
  endpoints: (builder) => ({
    getUsers: builder.query({
      query: ({ page, limit, search, filter, sort }) => {
        const params = new URLSearchParams();
        if (page) params.append("page", page);
        if (limit) params.append("limit", limit);
        if (search) params.append("search", search);
        if (filter) params.append("filter", JSON.stringify(filter));
        if (sort) params.append("sort", JSON.stringify(sort));

        return `/getUsers?${params.toString()}`;
      },
    }),

    getUserById: builder.query({
      query: (id) => `/getUser/${id}`,
      providesTags: (result, error, id) => [{ type: "User", id }],
    }),

    updateUser: builder.mutation({
      query: (userData) => ({
        url: `/upsertUser`,
        method: "POST",
        body: userData,
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "User", id }],
    }),

    deleteUser: builder.mutation({
      query: (id) => ({
        url: `/deleteUser/${id}`,
        method: "DELETE",
      }),
      invalidatesTags: ["User"],
    }),
    getAuthUser: builder.query({
      query: () => ({
        url: "/authUser",
        method: "GET",
      }),
      providesTags: ["User"],
    }),
    getPermissions: builder.query({
      query: () => ({
        url: "/getPermissions",
        method: "GET",
      }),
      providesTags: ["User"],
    }),
  }),
});

export const {
  useGetUsersQuery,
  useGetUserByIdQuery,
  useLazyGetUserByIdQuery,
  useUpdateUserMutation,
  useDeleteUserMutation,
  useLazyGetAuthUserQuery,
  useGetPermissionsQuery,
} = usersApi;
