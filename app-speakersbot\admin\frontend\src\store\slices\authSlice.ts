import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { initializePermissions, clearPermissions } from './rbacSlice';


interface Role {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  updatedAt: string;
}
interface User {
  id: number;
  name: string;
  email: string;
  roleId: number;
  isActive: boolean;
  deletedAt: string | null;
  createdAt: string;
  updatedAt: string;
  role: Role;
  avatar?: string;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  permissions: string[];
  error: string | null;
}

const initialState: AuthState = {
  user: null,
  token: localStorage.getItem('token'),
  isAuthenticated: !!localStorage.getItem('token'),
  isLoading: false,
  permissions: [],
  error: null,
};

const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    // Store token immediately after login API success
    setToken: (state, action: PayloadAction<string>) => {
      state.token = action.payload;
      state.error = null;
      localStorage.setItem('token', action.payload);
      state.permissions = [];
      state.isAuthenticated = true;
    },
    // Store complete user data after getAuthUser API success
    setCredentials: (state, action: PayloadAction<{ user: any; token: string }>) => {
      const { user, token } = action.payload;
       // Map API response (snake_case) → Redux state (camelCase)
       const mappedUser: User = {
        id: user.id,
        name: user.name,
        email: user.email,
        roleId: user.role_id,
        isActive: user.is_active,
        deletedAt: user.deleted_at,
        createdAt: user.created_at,
        updatedAt: user.updated_at,
        role: {
          id: user.role.id,   // ✅ added this
          name: user.role.name,
          description: user.role.description,
          createdAt: user.role.created_at,
          updatedAt: user.role.updated_at,
        },
        avatar: user.avatar || undefined,
      };

      state.user = mappedUser;
      state.token = token;
      state.isAuthenticated = true;
      state.error = null;
      state.permissions = user.role.permissions;
      localStorage.setItem('token', token);
    },
    logout: (state) => {
      state.user = null;
      state.token = null;
      state.isAuthenticated = false;
      state.error = null;
      localStorage.removeItem('token');
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    updateUser: (state, action: PayloadAction<Partial<User>>) => {
      if (state.user) {
        state.user = { ...state.user, ...action.payload };
      }
    },
  },
});

export const {
  setToken,
  setCredentials,
  logout,
  setLoading,
  setError,
  clearError,
  updateUser,
} = authSlice.actions;

export default authSlice.reducer;
