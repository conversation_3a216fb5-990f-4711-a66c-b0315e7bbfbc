import {
  <PERSON>ertOutlined,
  <PERSON><PERSON>ownOutlined,
  <PERSON><PERSON><PERSON><PERSON>utlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON>Outlined,
  UserOutlined,
} from "@ant-design/icons";
import { AlertTriangle } from "lucide-react";
import React, { useEffect, useState } from "react";
import {
  Bar,
  BarChart,
  CartesianGrid,
  Cell,
  Pie,
  PieChart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis
} from "recharts";
import { useGetMatchingQuery } from "../../apis/dashboardApi";
import { useToast } from "../../hooks/use-toast";
import { InvalidTokenHandler } from "../common/InvalidTokenHandler";
import { Badge } from "../ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Skeleton } from "../ui/skeleton";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";

const MatchingEngineAnalytics: React.FC = () => {
  const { data, isLoading, error } = useGetMatchingQuery(undefined, {
    refetchOnMountOrArgChange: true,
    refetchOnReconnect: true,
  });
  const matching = data?.data;

  // State for matching data
  const [matchingState, setMatchingState] = useState<any>(null);
  const { toast } = useToast();

  // Effect to handle data and show success toast when data loads
  useEffect(() => {
    if (data) {
      setMatchingState(data);
      // Show success toast when data loads
      // toast({
      //   title: "Matching engine analytics loaded successfully",
      // });
    }
  }, [data, toast]);

  // Show error toast when there's an error
  useEffect(() => {
    if (error) {
      toast({
        title: "Failed to load matching engine data",
        variant: "destructive",
      });
    }
  }, [error, toast]);

  // Transform API data for charts
  const aiVsManualData = (matching?.aiVsManualMatch || []).map(
    (item: any, index: number) => {
      const key = Object.keys(item)[0];
      const value = item[key];
      const label = key === "ai" ? "AI Generated" : "Manual Override";
      return {
        type: label,
        value,
        percentage: value,
        color:
          label === "AI Generated"
            ? "hsl(var(--dashboard-dark-blue))"
            : "hsl(var(--dashboard-medium-blue))",
      };
    }
  );

  const matchRatePerOpportunity =
    matching?.matchRateByOpportunityType?.map((item: any) => ({
      category: item.label,
      matchRate: item.value,
    })) || [];

  const lowMatchOpportunities = (matching?.lowMatchOpportunities || []).map(
    (o: any) => ({
      ...o,
      matchRate: `${(Number(o.matchRate)).toFixed(1)}%`,
    })
  );

  if (isLoading) {
    return (
      <div className="space-y-7">
        <div>
          <Skeleton className="h-5 w-72" />
          <Skeleton className="h-4 w-96 mt-2" />
        </div>

        {/* KPI Cards Skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {Array.from({ length: 4 }).map((_, index) => (
            <Card key={index} className="bg-tertiary border-border">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-8 w-16" />
                    <Skeleton className="h-3 w-20" />
                  </div>
                  <Skeleton className="h-8 w-8 rounded" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Charts Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Array.from({ length: 2 }).map((_, index) => (
            <Card key={index} className="bg-tertiary border-border">
              <CardHeader>
                <Skeleton className="h-6 w-48" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-[300px] w-full" />
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Table Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
          <Card className="bg-tertiary border-border">
            <CardHeader>
              <Skeleton className="h-6 w-48" />
            </CardHeader>
            <CardContent className="p-0">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>
                      <Skeleton className="h-4 w-32" />
                    </TableHead>
                    <TableHead>
                      <Skeleton className="h-4 w-16" />
                    </TableHead>
                    <TableHead>
                      <Skeleton className="h-4 w-12" />
                    </TableHead>
                    <TableHead className="text-center">
                      <Skeleton className="h-4 w-20" />
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {Array.from({ length: 7 }).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell className="font-medium">
                        <Skeleton className="h-4 w-48" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-4 w-20" />
                      </TableCell>
                      <TableCell>
                        <Skeleton className="h-6 w-16 rounded-full" />
                      </TableCell>
                      <TableCell className="text-center">
                        <Skeleton className="h-6 w-12 rounded-full" />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  return (
    <>
      <InvalidTokenHandler error={error} />
      <div className="space-y-8">
        <div>
          <h3 className="text-2xl font-semibold text-foreground mb-2">
            Matching Engine Performance
          </h3>
          <p className="text-muted-foreground text-sm mb-8">
            AI-powered speaker-opportunity matching analytics and conversion
            tracking
          </p>
        </div>

        {/* Show error state */}
        {error && (
          <Card className="bg-tertiary border-border">
            <CardContent className="p-8 text-center">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-foreground mb-2">
                Error Loading Data
              </h3>
              <p className="text-muted-foreground">
                Unable to fetch matching engine analytics data. Please try again
                later.
              </p>
            </CardContent>
          </Card>
        )}

        {/* Show data when loaded and no error */}
        {!isLoading && !error && (
          <>
            {/* KPI Cards */}
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              <Card className="bg-tertiary border-border">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Avg Match Rate
                      </p>
                      <p className="text-2xl font-bold text-foreground">{`${
                        matching?.summary?.[0]?.value ?? 0
                      }%`}</p>
                      <p
                        className={`text-xs flex items-center mt-1 ${
                          (matching?.summary?.[0]?.trend ?? 0) >= 0
                            ? "text-green-500"
                            : "text-red-500"
                        }`}
                      >
                        {(matching?.summary?.[0]?.trend ?? 0) >= 0 ? (
                          <ArrowUpOutlined className="h-3 w-3 mr-1" />
                        ) : (
                          <ArrowDownOutlined className="h-3 w-3 mr-1" />
                        )}
                        {`${
                          (matching?.summary?.[0]?.trend ?? 0) > 0 ? "+" : ""
                        }${
                          matching?.summary?.[0]?.trend
                        }% from last week`}
                      </p>
                    </div>
                    <BarChartOutlined className="h-8 w-8 text-dashboard-dark-blue" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-tertiary border-border">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Applied After Match
                      </p>
                      <p className="text-2xl font-bold text-foreground">{`${
                        matching?.summary?.[1]?.value ?? 0
                      }%`}</p>
                      <p
                        className={`text-xs flex items-center mt-1 ${
                          (matching?.summary?.[1]?.trend ?? 0) >= 0
                            ? "text-green-500"
                            : "text-red-500"
                        }`}
                      >
                        {(matching?.summary?.[1]?.trend ?? 0) >= 0 ? (
                          <ArrowUpOutlined className="h-3 w-3 mr-1" />
                        ) : (
                          <ArrowDownOutlined className="h-3 w-3 mr-1" />
                        )}
                        {`${
                          (matching?.summary?.[1]?.trend ?? 0) >= 0 ? "+" : ""
                        }${matching?.summary?.[1]?.trend ?? 0} trend`}
                      </p>
                    </div>
                    <UserOutlined className="h-8 w-8 text-dashboard-medium-blue" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-tertiary border-border">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Revenue/Match
                      </p>
                      <p className="text-2xl font-bold text-foreground">{`$${(
                        matching?.summary?.[2]?.value ?? 0
                      ).toLocaleString()}`}</p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {matching?.summary?.[2]?.subtext || "Average value"}
                      </p>
                    </div>
                    <RobotOutlined className="h-8 w-8 text-green-500" />
                  </div>
                </CardContent>
              </Card>

              <Card className="bg-tertiary border-border">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-muted-foreground">
                        Low Match Opps
                      </p>
                      <p className="text-2xl font-bold text-foreground">
                        {matching?.summary?.[3]?.value ?? 0}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {matching?.summary?.[3]?.subtext || "Need attention"}
                      </p>
                    </div>
                    <AlertOutlined className="h-8 w-8 text-destructive" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Charts and Tables */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card className="bg-tertiary border-border">
                <CardHeader>
                  <CardTitle className="text-foreground">
                    AI vs Manual Matches
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    {aiVsManualData.length > 0 ? (  
                      <PieChart>
                      <Pie
                        data={aiVsManualData}
                        cx="50%"
                        cy="50%"
                        outerRadius={60}
                        dataKey="value"
                        label={({
                          type,
                          percentage,
                        }: {
                          type: string;
                          percentage: number;
                        }) => `${type}: ${percentage}%`}
                      >
                        {aiVsManualData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip
                        separator=""
                        formatter={(
                          value: any,
                          _name: any,
                          { payload }: any
                        ) => [`${payload?.type}: ${value}`, ""]}
                        labelFormatter={() => ""}
                        contentStyle={{
                          backgroundColor: "hsl(var(--muted-foreground))",
                          border: "1px solid hsl(var(--border))",
                          borderRadius: "6px",
                        }}
                      />
                    </PieChart>
                    ) : (
                      <p className="flex items-center justify-center h-[300px] text-muted-foreground">No AI vs Manual Data Available</p>
                    )}
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card className="bg-tertiary border-border">
                <CardHeader>
                  <CardTitle className="text-foreground">
                    Match Rate per Opportunity Type
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ResponsiveContainer width="100%" height={300}>
                    {matchRatePerOpportunity.length > 0 ? (  
                      <BarChart data={matchRatePerOpportunity}>
                      <CartesianGrid
                        strokeDasharray="3 3"
                        stroke="hsl(var(--border))"
                      />
                      <XAxis
                        dataKey="category"
                        stroke="hsl(var(--muted-foreground))"
                      />
                      <YAxis stroke="hsl(var(--muted-foreground))" />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: "hsl(var(--card))",
                          border: "1px solid hsl(var(--border))",
                          borderRadius: "6px",
                        }}
                      />
                      <Bar
                        dataKey="matchRate"
                        fill="hsl(var(--dashboard-medium-blue))"
                      />
                    </BarChart>
                    ) : (
                      <p className="flex items-center justify-center h-[300px] text-muted-foreground">No Match Rate per Opportunity Type Data Available</p>
                    )}
                    </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>

            {/* Detailed Tables */}
            <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
              <Card className="bg-tertiary border-border">
                <CardHeader>
                  <CardTitle className="text-foreground">
                    Low Match Opportunities
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="overflow-x-auto">
                    {lowMatchOpportunities.length > 0 ? (  
                      <Table className="min-w-[900px]">
                      <TableHeader>
                        <TableRow>
                          <TableHead>Opportunity Name</TableHead>
                          <TableHead>Date</TableHead>
                          <TableHead>Type</TableHead>
                          <TableHead className="text-center">
                            Match Rate
                          </TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {lowMatchOpportunities.map(
                          (opportunity: any, index: number) => (
                            <TableRow key={index}>
                              <TableCell className="font-medium">
                                {opportunity.opportunityName}
                              </TableCell>
                              <TableCell>{opportunity.date}</TableCell>
                              <TableCell>
                                <Badge variant="outline">
                                  {opportunity.type}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-center">
                                <Badge variant="destructive">
                                  {opportunity.matchRate}
                                </Badge>
                              </TableCell>
                            </TableRow>
                          )
                        )}
                      </TableBody>
                      </Table>
                    ) : (
                      <p className="tflex items-center justify-center h-[300px] text-muted-foreground">No Low Match Opportunities Data Available</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default MatchingEngineAnalytics;
