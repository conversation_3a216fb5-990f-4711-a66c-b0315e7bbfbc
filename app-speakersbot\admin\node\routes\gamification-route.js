
const gamificationController = require("../controllers/gamification-controller");
const verifyToken = require("../middlewares/verify-token");
const router = require("express").Router();

module.exports = (app) => { 
    router.get("/gamification-history", verifyToken, gamificationController.getPointsHistory);
    router.get("/gamification-rules", verifyToken, gamificationController.getAllRules)
    router.post('/auto-claim', verifyToken, gamificationController.processPoints);
    router.put("/gamification-rules", verifyToken, gamificationController.updateRules)

    app.use("/admin/api/v1", router);

};