"""
Affiliate Database Operations
Database operations for affiliate data management
"""
import json
from datetime import datetime
from typing import Dict, Any, Optional
from sqlalchemy import text
from app.helpers.database_manager import db_manager
from app.config.logger import get_logger
from app.config.config import config
logger = get_logger(__name__, file_name="scraper.log")

class AffiliateDatabase:
    """Database operations for affiliate data management"""
    def __init__(self):
        """Initialize database connection"""
        self.SessionLocal = None
        self._init_database()
    
    def _init_database(self):
        """Initialize database connection for affiliate data updates"""
        try:
            # Use centralized database manager instead of creating new engine
            self.SessionLocal = db_manager.SessionLocal
            logger.info("Database connection with pooling initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize database connection: {e}")
            self.SessionLocal = None
    
    def check_affiliate_exists(self, affiliate_id: str) -> bool:
        """
        Check if affiliate ID exists in the database
        
        Args:
            affiliate_id (str): The affiliate ID to check
            
        Returns:
            bool: True if affiliate exists, False otherwise
        """
        if not self.SessionLocal:
            logger.warning("Database not initialized, cannot check affiliate existence")
            return False
            
        try:
            with self.SessionLocal() as session:
                query = text("""
                    SELECT COUNT(*) 
                    FROM affiliate_users_details 
                    WHERE affiliate_id = :affiliate_id 
                    AND deleted_at IS NULL
                """)
                result = session.execute(query, {"affiliate_id": int(affiliate_id)}).fetchone()
                
                exists = result[0] > 0 if result else False
                if exists:
                    logger.info(f"Affiliate ID {affiliate_id} exists in database")
                else:
                    logger.warning(f"Affiliate ID {affiliate_id} not found in database")
                return exists
                    
        except Exception as e:
            logger.error(f"Error checking affiliate existence for {affiliate_id}: {e}")
            return False

    def get_affiliate_metadata(self, affiliate_id: str) -> Optional[Dict[str, Any]]:
        """
        Fetch existing metadata for an affiliate from the database
        Args:
            affiliate_id (str): The affiliate ID to fetch data for
        Returns:
            Optional[Dict[str, Any]]: Existing metadata or None if not found
        """
        if not self.SessionLocal:
            logger.warning("Database not initialized, cannot fetch affiliate metadata")
            return None
        try:
            with self.SessionLocal() as session:
                query = text("""
                    SELECT metadata 
                    FROM affiliate_users_details 
                    WHERE affiliate_id = :affiliate_id 
                    AND deleted_at IS NULL
                """)
                result = session.execute(query, {"affiliate_id": int(affiliate_id)}).fetchone()
                if result and result[0]:
                    return result[0] if isinstance(result[0], dict) else json.loads(result[0])
                else:
                    logger.info(f"No existing metadata found for affiliate_id: {affiliate_id}")
                    return {}
        except Exception as e:
            logger.error(f"Error fetching affiliate metadata for {affiliate_id}: {e}")
            return {}
    
    def update_affiliate_metadata(self, affiliate_id: str, scraped_data: Dict[str, Any]) -> bool:
        """
        Update affiliate metadata in the database with scraped data
        Args:
            affiliate_id (str): The affiliate ID to update
            scraped_data (Dict[str, Any]): The scraped data to add to metadata
        Returns:
            bool: True if update successful, False otherwise
        """
        if not self.SessionLocal:
            logger.warning("Database not initialized, cannot update affiliate metadata")
            return False
            
        try:
            # Get existing metadata
            existing_metadata = self.get_affiliate_metadata(affiliate_id)
            if existing_metadata is None:
                existing_metadata = {}
            # Ensure existing_metadata is a dictionary
            if isinstance(existing_metadata, str):
                try:
                    existing_metadata = json.loads(existing_metadata)
                except json.JSONDecodeError:
                    existing_metadata = {}
            elif not isinstance(existing_metadata, dict):
                existing_metadata = {}
            
            # Organize scraped data into the metadata structure format
            from .utils import AffiliateUtils
            utils = AffiliateUtils()
            updated_metadata = utils.organize_scraped_data(existing_metadata, scraped_data)
            # Add timestamp for this scraping session
            updated_metadata['last_scraped_at'] = datetime.now().isoformat()
            updated_metadata['scraped_url'] = scraped_data.get('scraped_url', '')
            
            with self.SessionLocal() as session:
                query = text("""
                    UPDATE affiliate_users_details 
                    SET metadata = :metadata, updated_at = NOW()
                    WHERE affiliate_id = :affiliate_id 
                    AND deleted_at IS NULL
                """)
                result = session.execute(query, {
                    "metadata": json.dumps(updated_metadata),
                    "affiliate_id": int(affiliate_id)
                })
                session.commit()
                
                if result.rowcount > 0:
                    return True
                else:
                    return False
                    
        except Exception as e:
            logger.error(f"Error updating affiliate metadata for {affiliate_id}: {e}")
            return False