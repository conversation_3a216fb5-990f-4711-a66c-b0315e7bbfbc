const CustomError = require("../helpers/custome-error");
const Setting = require("../models/schema/setting");
const appHelper = require("../helpers/app-hepler");
const settingService = {};

// ------------------------- setting-service -------------------------

/**
 * Save or update a setting by key.
 * Creates new setting if key doesn't exist, updates if it does.
 * Supports nested objects like stripe configuration.
 * 
 * @param {string} key - Setting key/name
 * @param {string|object} value - Setting value (can be object for nested settings)
 * @returns {Promise<Object>} Success message with setting data
 * @throws {Error} When database operation fails
 */
settingService.saveSetting = async (key, value) => {
  try {
    if (value === null || value === undefined) {
      throw new CustomError("Value is required");
    }

    const rawValueString = typeof value === 'object' ? JSON.stringify(value) : String(value);
    const valueToStore = appHelper.encryptValue(rawValueString);

    let setting = await Setting.findOne({ where: { key } });

    if (setting) {
      setting.value = valueToStore;
      await setting.save();
    } else {
      setting = await Setting.create({ key, value: valueToStore });
    }

    const responseData = {
      ...setting.dataValues,
      value: (() => {
        try {
          const stored = typeof setting.value === 'string' ? appHelper.decryptValue(setting.value) : setting.value;
          if (typeof stored === 'string' && (stored.startsWith('{') || stored.startsWith('['))) {
            return JSON.parse(stored);
          }
          return stored;
        } catch (error) {
          return setting.value;
        }
      })()
    };

    return { status: true, message: "Setting saved successfully", data: responseData };
  } catch (err) {
    console.error("Error saving setting:", err);
    throw err;
  }
};


settingService.getSettingByKey = async (key) => {
  try {
    
    const setting = await Setting.findOne({ where: { key } });
    
    if (!setting) {
      return { status: true, message: "Setting not found", data: null };
    }
  
    const parsedSetting = {
      ...setting.dataValues,
      value: (() => {
        try {
          let stored = setting.value;
          if (typeof stored === 'string') {
            stored = appHelper.decryptValue(stored);
          }
          if (typeof stored === 'string' && (stored.startsWith('{') || stored.startsWith('['))) {
            return JSON.parse(stored);
          }
          return stored;
        } catch (e) {
          return setting.value;
        }
      })()
    };

    return { status: true, message: "Setting fetched successfully", data: parsedSetting };
  } catch (err) {
    console.error("Error fetching setting by key:", err);
    throw err;
  }
};

/**
 * Add/update(upsert) email template
 */
settingService.upsertEmailTemplate = async (key, value) => {
  try {
    // stringify the value and store in the database
    const data = JSON.stringify(value);
    // const [ instance, created] = await Setting.upsert({ key, value: data }); 
    const getTemplate = await Setting.findOne({ where: { key } });
    if(getTemplate){
      getTemplate.value = data;
      getTemplate.updated_at = new Date();
      await getTemplate.save();
      return false;
    }
    await Setting.create({ key, value: data });
    return true;
  }
  catch (err) {
    throw err;
  }
}

/**
 * Get email template by key
 */
settingService.getEmailTemplateByKey = async (key) => {
  try {
    const setting = await Setting.findOne({ where: { key } });
    if (!setting) return null;
    
    try {
      const template = JSON.parse(setting.value);
      return template;
    } catch (parseError) {
      console.error(`Error parsing email template for key ${key}:`, parseError);
      return null;
    }
  }
  catch (err) {
    return null;
  }
}

module.exports = settingService;
