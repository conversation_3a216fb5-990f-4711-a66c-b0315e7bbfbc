// LocalStorage utilities for data persistence

const STORAGE_KEYS = {
  AUTH: 'speakerbot_auth',
  SPEAKERS: 'speakerbot_speakers',
  OPPORTUNITIES: 'speakerbot_opportunities', 
  MATCHES: 'speakerbot_matches',
  USERS: 'speakerbot_users',
  INTAKE_FORM: 'speakerbot_intake_form',
  SCRAPING: 'speakerbot_scraping',
  AFFILIATES: 'speakerbot_affiliates',
  SETTINGS: 'speakerbot_settings',
  UI: 'speakerbot_ui',
  ACTIVITY_LOG: 'speakerbot_activity_log',
} as const;

export const storage = {
  get: <T>(key: keyof typeof STORAGE_KEYS): T | null => {
    try {
      const item = localStorage.getItem(STORAGE_KEYS[key]);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error(`Error reading from localStorage:`, error);
      return null;
    }
  },

  set: <T>(key: keyof typeof STORAGE_KEYS, value: T): void => {
    try {
      localStorage.setItem(STORAGE_KEYS[key], JSON.stringify(value));
    } catch (error) {
      console.error(`Error writing to localStorage:`, error);
    }
  },

  remove: (key: keyof typeof STORAGE_KEYS): void => {
    try {
      localStorage.removeItem(STORAGE_KEYS[key]);
    } catch (error) {
      console.error(`Error removing from localStorage:`, error);
    }
  },

  clear: (): void => {
    try {
      Object.values(STORAGE_KEYS).forEach(key => {
        localStorage.removeItem(key);
      });
    } catch (error) {
      console.error(`Error clearing localStorage:`, error);
    }
  }
};