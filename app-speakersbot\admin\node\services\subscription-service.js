subscriptionService = {};
const { Speakers, PricingPlan, Subscriptions } = require("../models");
const { Op } = require("sequelize");
const { parsePagination, getPagingData } = require("../helpers/app-hepler");
const { exportAsCSV } = require("../helpers/csv-helper");
const csv = require('fast-csv');

// ------------------------- subscription-service -------------------------

/**
 * Get subscriptions with filters and pagination.
 * Groups subscriptions by speaker and includes speaker/plan information.
 * 
 * @param {Object} [filters={}] - Filter options
 * @param {string} [filters.status] - Filter by subscription status
 * @param {string} [filters.start_date] - Filter by start date
 * @param {string} [filters.end_date] - Filter by end date
 * @param {string} [filters.search] - Search in status, speaker name, or plan name
 * @param {number} [filters.page] - Page number for pagination
 * @param {number} [filters.limit] - Items per page
 * @returns {Promise<Object>} Paginated subscription data grouped by speaker
 * @throws {Error} When database operation fails
 */
subscriptionService.getSubscriptions = async (filters = {}) => {
  try {
    const { page, limit, offset } = parsePagination(filters);
    const { status, start_date, end_date, search } = filters;

    const where = {};

    // filter by status
    if (status) {
      where.status = status;
    }

    // filter by date range
    if (start_date && end_date) {
      where.start_date = {
        [Op.between]: [start_date, end_date],
      };
    } else if (start_date) {
      where.start_date = {
        [Op.gte]: start_date,
      };
    } else if (end_date) {
      where.start_date = {
        [Op.lte]: end_date,
      };
    }

    const include = [
      {
        model: Speakers,
        attributes: ["name", "email"],
        as: 'speaker'
      },
      {
        model: PricingPlan,
        attributes: ["name"],
        as: 'plan'
      },
    ];

    // search functionality
    if (search) {
      where[Op.or] = [
        { status: { [Op.like]: `%${search}%` } },
        { '$speaker.name$': { [Op.like]: `%${search}%` } },
        { '$plan.name$': { [Op.like]: `%${search}%` } },
      ];
    }

    // fetch paginated subscriptions directly (flat list)
    const { rows: subscriptionsData, count: totalCount } = await Subscriptions.findAndCountAll({
      where,
      include,
      order: [['start_date', 'DESC']],
      limit,
      offset
    });

    const formatted = subscriptionsData.map(subscription => ({
      id: subscription.id,
      speaker_id: subscription.speaker_id,
      speaker_name: subscription.speaker?.name || 'Unknown',
      speaker_email: subscription.speaker?.email || null,
      plan_name: subscription.plan?.name || null,
      amount: subscription.amount,
      currency: subscription.currency,
      start_date: subscription.start_date,
      end_date: subscription.end_date,
      status: subscription.status,
      created_at: subscription.created_at,
      updated_at: subscription.updated_at
    }));

    const pageData = getPagingData(totalCount, limit, page);

    return {
      status: true,
      message: "Subscriptions fetched successfully",
      data: { subscriptions: formatted, pageData },
    };
  } catch (error) {
    console.log("Error fetching subscriptions", error);
    throw error;
  }
};

subscriptionService.exportSubscriptions = async (req) => {
  try {
    const { ids } = req.body || {};
    const filters = { ...(req.query || {}), ...(req.body || {}) };
    const { status, start_date, end_date, search } = filters;

    const where = {};

    if (ids && Array.isArray(ids) && ids.length > 0) {
      where.id = ids;
    }

    // filter by status
    if (status) {
      where.status = status;
    }

    if (start_date && end_date) {
      where.start_date = { [Op.between]: [start_date, end_date] };
    } else if (start_date) {
      where.start_date = { [Op.gte]: start_date };
    } else if (end_date) {
      where.start_date = { [Op.lte]: end_date };
    }

    const include = [
      { model: Speakers, attributes: ["name", "email"], as: 'speaker' },
      { model: PricingPlan, attributes: ["name"], as: 'plan' }
    ];

    // search functionality
    if (search) {
      where[Op.or] = [
        { status: { [Op.like]: `%${search}%` } },
        { '$speaker.name$': { [Op.like]: `%${search}%` } },
        { '$plan.name$': { [Op.like]: `%${search}%` } },
      ];
    }

    const subscriptionsModels = await Subscriptions.findAll({
      where,
      include,
      order: [['start_date', 'DESC']],
    });

    const subscriptions = subscriptionsModels.map(s => ({
      id: s.id,
      speaker_id: s.speaker_id,
      speaker_name: s.speaker?.name || '',
      speaker_email: s.speaker?.email || '',
      plan_name: s.plan?.name || '',
      amount: s.amount,
      currency: s.currency,
      start_date: s.start_date,
      end_date: s.end_date,
      status: s.status,
      invoice_id: s.invoice_id,
      created_at: s.created_at,
      updated_at: s.updated_at
    }));

    if (!subscriptions || subscriptions.length === 0) {
      return { status: false, message: "No subscriptions found", data: [] };
    }

    return { status: true, data: subscriptions, fileName: 'subscriptions.csv' };
  } catch (error) {
    console.error("Error exporting subscriptions:", error);
    throw error;
  }
};


module.exports = subscriptionService;