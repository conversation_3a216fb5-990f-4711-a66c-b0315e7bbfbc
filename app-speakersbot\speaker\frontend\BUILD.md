# Build Configuration

This project has separate build configurations for different parts of the application:

## Build Configurations

### 1. Main App (`vite.config.ts`)
- **Base path**: `/speaker`
- **Port**: 8080
- **Output**: `../backend/dist`
- **Entry**: `src/main.tsx`
- **HTML**: `index.html`

### 2. Landing Page (`vite.config.landing.ts`)
- **Base path**: `/` (root)
- **Port**: 8081
- **Output**: `../backend/dist-landing`
- **Entry**: `src/main.landing.tsx`
- **HTML**: `index.landing.html`

### 3. Extension (`vite.config.extension.ts`)
- **Base path**: `/` (root)
- **Output**: `../backend/dist-extension`
- **Entry**: `src/main.extension.tsx`
- **HTML**: `index.extension.html`

## Build Commands

### Development
```bash
# Main app (port 8080)
npm run dev

# Landing page (port 8081)
npm run dev:landing

# Extension
npm run dev:extension
```

### Production Builds
```bash
# Main app only
npm run build

# Landing page only
npm run build:landing

# Extension only
npm run build:extension

# All builds
npm run build:all
```

## Output Directories

- **Main app**: `../backend/dist/`
- **Landing page**: `../backend/dist-landing/`
- **Extension**: `../backend/dist-extension/`

## Deployment

The backend can serve different static files based on the route:
- `/` → Serve files from `dist-landing/`
- `/speaker` → Serve files from `dist/`
- `/extension` → Serve files from `dist-extension/`
