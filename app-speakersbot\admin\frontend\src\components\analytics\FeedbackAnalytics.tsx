import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardH<PERSON>er, CardTitle } from "../ui/card";
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
} from "recharts";
import { MessageCircle, Star, Bug, Zap, AlertTriangle } from "lucide-react";
import { Badge } from "../ui/badge";
import { Skeleton } from "../ui/skeleton";
import { useToast } from "../../hooks/use-toast";
import { useGetDashboardFeedbackQuery } from "../../apis/dashboardApi";
import { InvalidTokenHandler } from "../common/InvalidTokenHandler";

const FeedbackAnalytics: React.FC = () => {
  // Fetch feedback data from API
  const {
    data: feedbackData,
    isLoading,
    error,
  } = useGetDashboardFeedbackQuery({});
  const { toast } = useToast();

  // State for feedback data
  const [feedbackState, setFeedbackState] = useState<any>(null);

  // Effect to handle data and show success toast when data loads
  useEffect(() => {
    if (feedbackData) {
      setFeedbackState(feedbackData);
      // Show success toast when data loads
      // toast({
      //   title: "Feedback analytics loaded successfully",
      // });
    }
  }, [feedbackData, toast]);

  // Show error toast when there's an error
  useEffect(() => {
    if (error) {
      toast({
        title: "Failed to load feedback data",
        variant: "destructive",
      });
    }
  }, [error, toast]);

  // Transform API data for charts
  const feedbackDistribution = feedbackData?.data?.feedbackDistribution
    ? Object.entries(feedbackData.data.feedbackDistribution)
        .filter(([key, value]) => typeof value === "number" && value > 0)
        .map(([key, count], index) => {
          const colors = [
            "hsl(var(--dashboard-dark-blue))",
            "hsl(var(--dashboard-light-blue))",
            "hsl(var(--dashboard-medium-blue))",
          ];

          // Convert camelCase to readable format
          const type = key
            .replace(/([A-Z])/g, " $1")
            .replace(/^./, (str) => str.toUpperCase())
            .trim();

          return {
            type,
            count,
            avgRating:
              feedbackData.data.feedbackDistribution[`${key}Rating`] || 0,
            color: colors[index % colors.length],
          };
        })
    : [];

  const satisfactionTrend = feedbackData?.data?.satisfactionTrend || [];

  const featureRequests =
    feedbackData?.data?.topFeatureRequests?.map((item) => ({
      feature: item.feature,
      votes: item.count,
      status: "Under Review",
      priority: "Medium",
    })) || [];

  const engagementTrend =
    feedbackData?.data?.userEngagementTrend?.map((item) => ({
      date: item.time,
      sessions: item.sessions,
      duration: 18.0,
      pageViews: item.sessions * 12,
    })) || [];

  return (
    <div className="space-y-6">
      <InvalidTokenHandler error={error} />
      {isLoading ? (
        <div>
          <Skeleton className="h-5 w-72" />
          <Skeleton className="h-4 w-96 mt-2" />
        </div>
      ) : (
        <div>
          <h3 className="text-xl font-semibold text-foreground mb-2">
            Feedback + User Behavior View
          </h3>
          <p className="text-muted-foreground text-sm mb-6">
            Analyze user feedback, feature requests, and engagement patterns
          </p>
        </div>
      )}

      {/* Show error state */}
      {error && (
        <Card className="bg-tertiary border-border">
          <CardContent className="p-8 text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-foreground mb-2">
              Error Loading Data
            </h3>
            <p className="text-muted-foreground">
              Unable to fetch feedback analytics data. Please try again later.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Show data when loaded and no error */}
      {!isLoading && !error && (
        <>
          {/* KPI Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
            <Card className="bg-tertiary border-border">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Avg Satisfaction
                    </p>
                    {isLoading ? (
                      <Skeleton className="h-8 w-16 mb-2" />
                    ) : (
                      <p className="text-2xl font-bold text-foreground">
                        {feedbackData?.data?.avgSatisfaction?.score || 0}/
                        {feedbackData?.data?.avgSatisfaction?.outOf || 5}
                      </p>
                    )}
                    {isLoading ? (
                      <Skeleton className="h-5 w-24" />
                    ) : (
                      <Badge variant="secondary" className="mt-1">
                        {feedbackData?.data?.avgSatisfaction?.change ||
                          "No change data"}
                      </Badge>
                    )}
                  </div>
                  <Star className="h-6 w-6 text-yellow-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-tertiary border-border">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Total Feedback
                    </p>
                    {isLoading ? (
                      <Skeleton className="h-8 w-16 mb-2" />
                    ) : (
                      <p className="text-2xl font-bold text-foreground">
                        {feedbackData?.data?.totalFeedback?.count || 0}
                      </p>
                    )}
                    {isLoading ? (
                      <Skeleton className="h-5 w-20" />
                    ) : (
                      <Badge variant="outline" className="mt-1">
                        {feedbackData?.data?.totalFeedback?.period ||
                          "This month"}
                      </Badge>
                    )}
                  </div>
                  <MessageCircle className="h-6 w-6 text-dashboard-medium-blue" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-tertiary border-border">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Bug Reports
                    </p>
                    {isLoading ? (
                      <Skeleton className="h-8 w-16 mb-2" />
                    ) : (
                      <p className="text-2xl font-bold text-foreground">
                        {feedbackData?.data?.bugReports?.count || 0}
                      </p>
                    )}
                    {isLoading ? (
                      <Skeleton className="h-5 w-24" />
                    ) : (
                      <Badge variant="destructive" className="mt-1">
                        {feedbackData?.data?.bugReports?.change ||
                          "No change data"}
                      </Badge>
                    )}
                  </div>
                  <Bug className="h-6 w-6 text-red-500" />
                </div>
              </CardContent>
            </Card>

            <Card className="bg-tertiary border-border">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-muted-foreground">
                      Feature Requests
                    </p>
                    {isLoading ? (
                      <Skeleton className="h-8 w-16 mb-2" />
                    ) : (
                      <p className="text-2xl font-bold text-foreground">
                        {feedbackData?.data?.featureRequests?.count || 0}
                      </p>
                    )}
                    {isLoading ? (
                      <Skeleton className="h-5 w-24" />
                    ) : (
                      <Badge variant="secondary" className="mt-1">
                        {feedbackData?.data?.featureRequests?.change ||
                          "No change data"}
                      </Badge>
                    )}
                  </div>
                  <Zap className="h-6 w-6 text-dashboard-light-blue" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            <Card className="bg-tertiary border-border">
              <CardHeader>
                <CardTitle className="text-foreground">
                  Feedback Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-[300px] w-full" />
                ) : (
                  <ResponsiveContainer width="100%" height={300}>
                    {feedbackDistribution.length > 0 ? (   
                    <PieChart>
                      <Pie
                        data={feedbackDistribution}
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        dataKey="count"
                        label={(entry: any) => `${entry.type}: ${entry.count}`}
                      >
                        {feedbackDistribution.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                    </PieChart>
                  ) : (
                    <div className="flex justify-center items-center h-full">
                      <p className="text-muted-foreground text-sm">No Feedback Distribution Data Available</p>
                    </div>
                  )}
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>

            <Card className="bg-tertiary border-border">
              <CardHeader>
                <CardTitle className="text-foreground">
                  Satisfaction Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-[300px] w-full" />
                ) : (
                  <ResponsiveContainer width="100%" height={300}>
                    {satisfactionTrend.length > 0 ? (   
                      <LineChart data={satisfactionTrend}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="month" />
                      <YAxis domain={[0, 5]} />
                      <Tooltip />
                      <Legend />
                      <Line 
                        type="monotone"
                        dataKey="satisfaction"
                        stroke="hsl(var(--dashboard-dark-blue))"
                        name="Satisfaction Rating"
                      />
                      <Line
                        type="monotone"
                        dataKey="responses"
                        stroke="hsl(var(--dashboard-medium-blue))"
                        name="Response Count"
                      />
                    </LineChart>
                  ) : (
                    <div className="flex justify-center items-center h-full">
                      <p className="text-muted-foreground text-sm">No Satisfaction Trend Data Available</p>
                    </div>
                  )}
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>

            <Card className="bg-tertiary border-border">
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-foreground">
                  Top Feature Requests
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-[300px] w-full" />
                ) : (
                  <ResponsiveContainer width="100%" height={300}>
                    {featureRequests.length > 0 ? (   
                      <BarChart data={featureRequests}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis
                        dataKey="feature"
                        angle={-45}
                        textAnchor="end"
                        height={100}
                      />
                      <YAxis />
                      <Tooltip />
                      <Bar
                        dataKey="votes"
                        fill="hsl(var(--dashboard-dark-blue))"
                        name="Votes"
                      />
                    </BarChart>
                  ) : (
                    <div className="flex justify-center items-center h-full">
                      <p className="text-muted-foreground text-sm">No Feature Requests Data Available</p>
                    </div>
                  )}
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>

            <Card className="bg-tertiary border-border">
              <CardHeader>
                <CardTitle className="text-foreground">
                  User Engagement Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isLoading ? (
                  <Skeleton className="h-[300px] w-full" />
                ) : (
                  <ResponsiveContainer width="100%" height={300}>
                    {engagementTrend.length > 0 ? (   
                      <AreaChart data={engagementTrend}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis />
                      <Tooltip />
                      <Legend />
                      <Area
                        type="monotone"
                        dataKey="sessions"
                        stackId="1"
                        stroke="hsl(var(--dashboard-dark-blue))"
                        fill="hsl(var(--dashboard-dark-blue))"
                        fillOpacity={0.3}
                        name="Sessions"
                      />
                    </AreaChart>
                  ) : (
                    <div className="flex justify-center items-center h-full">
                      <p className="text-muted-foreground text-sm">No Engagement Trend Data Available</p>
                    </div>
                  )}
                  </ResponsiveContainer>
                )}
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </div>
  );
};

export default FeedbackAnalytics;
