const fs = require('fs');
const path = require('path');
const csv = require('csv-parser');
const SpeakerOpportunity = require("../models/schema/speaker-opportunity");
const Speakers = require("../models/schema/speakers");
const Opportunities = require("../models/schema/opportunities");
const connection = require('../models/connection');

const csvFilePath = path.join(__dirname, 'csv', 'speaker_opportunity_matches 2.csv');

async function importSpeakerOpportunities() {
    try {
        await connection.authenticate();
        console.log('Database connection established.');

        const speakerOpportunities = [];
        let processedCount = 0;
        const batchSize = 1000; // Process in batches of 1000

        // Cache for existing speakers and opportunities to avoid repeated DB queries
        const existingSpeakers = new Set();
        const existingOpportunities = new Set();

        // Helper function to check if speaker and opportunity exist
        async function validateReferences(speakerId, opportunityId) {
            // Check cache first
            if (!existingSpeakers.has(speakerId)) {
                const speaker = await Speakers.findByPk(speakerId);
                if (speaker) {
                    existingSpeakers.add(speakerId);
                } else {
                    return false;
                }
            }

            if (!existingOpportunities.has(opportunityId)) {
                const opportunity = await Opportunities.findByPk(opportunityId);
                if (opportunity) {
                    existingOpportunities.add(opportunityId);
                } else {
                    return false;
                }
            }

            return true;
        }

        // Helper function to convert values to correct types
        function convertValue(key, value) {
            if (value === undefined || value === null || value === '') return null;
            
            // Trim whitespace
            value = value.toString().trim();
            if (value === '') return null;

            // Handle numeric fields
            const numericFields = ['speaker_id', 'opportunity_id', 'overall_score', 'topic_score', 'expertise_score', 'audience_score', 'format_score', 'geographic_score'];
            if (numericFields.includes(key)) {
                const num = parseFloat(value);
                return isNaN(num) ? null : num;
            }

            // Handle boolean fields
            if (key === 'application_submitted') {
                const boolValue = value.toLowerCase();
                if (['true', '1', 'yes'].includes(boolValue)) return true;
                if (['false', '0', 'no'].includes(boolValue)) return false;
                return false; // Default to false
            }

            // Handle status enum
            if (key === 'status') {
                const validStatuses = ['pending', 'accepted', 'rejected', 'interested'];
                const statusValue = value.toLowerCase();
                return validStatuses.includes(statusValue) ? statusValue : 'pending';
            }

            // Handle string length limits based on database schema
            const stringLimits = {
                'proposal_subject': 255, // Based on STRING type in schema
                'speaker_email': 255
            };

            if (stringLimits[key] && value.length > stringLimits[key]) {
                console.warn(`Truncating ${key} from ${value.length} to ${stringLimits[key]} characters`);
                return value.substring(0, stringLimits[key]);
            }

            return value;
        }

        // Helper function to process batch
        async function processBatch(batch) {
            let success = 0;
            let fail = 0;

            for (const data of batch) {
                try {
                    // Additional validation before insert
                    if (!data.speaker_id || !data.opportunity_id) {
                        console.warn('Skipping record with missing speaker_id or opportunity_id:', data);
                        fail++;
                        continue;
                    }

                    // Ensure numeric IDs are integers
                    data.speaker_id = parseInt(data.speaker_id);
                    data.opportunity_id = parseInt(data.opportunity_id);

                    if (isNaN(data.speaker_id) || isNaN(data.opportunity_id)) {
                        console.warn('Skipping record with invalid numeric IDs:', data);
                        fail++;
                        continue;
                    }

                    // Check if speaker and opportunity exist
                    const referencesValid = await validateReferences(data.speaker_id, data.opportunity_id);
                    if (!referencesValid) {
                        console.warn(`Skipping record - speaker_id ${data.speaker_id} or opportunity_id ${data.opportunity_id} not found in database`);
                        fail++;
                        continue;
                    }

                    await SpeakerOpportunity.create(data);
                    success++;
                } catch (err) {
                    fail++;
                    console.error('Insert error:', err.message, { 
                        speaker_id: data.speaker_id, 
                        opportunity_id: data.opportunity_id,
                        error: err.message,
                        details: err.errors ? err.errors.map(e => e.message) : null
                    });
                }
            }

            console.log(`Batch processed. Success: ${success}, Failed: ${fail}`);
            return { success, fail };
        }

        console.log('Starting CSV import...');
        console.log(`Reading from: ${csvFilePath}`);

        fs.createReadStream(csvFilePath)
            .pipe(csv())
            .on('data', (row) => {
                // Map CSV row to model fields
                const mapped = {
                    speaker_id: convertValue('speaker_id', row.speaker_id),
                    opportunity_id: convertValue('opportunity_id', row.opportunity_id),
                    speaker_email: convertValue('speaker_email', row.speaker_email),
                    overall_score: convertValue('overall_score', row.overall_score),
                    topic_score: convertValue('topic_score', row.topic_score),
                    expertise_score: convertValue('expertise_score', row.expertise_score),
                    audience_score: convertValue('audience_score', row.audience_score),
                    format_score: convertValue('format_score', row.format_score),
                    geographic_score: convertValue('geographic_score', row.geographic_score),
                    match_strengths: convertValue('match_strengths', row.match_strengths),
                    match_concerns: convertValue('match_concerns', row.match_concerns),
                    recommendation: convertValue('recommendation', row.recommendation),
                    proposal_subject: convertValue('proposal_subject', row.proposal_subject),
                    proposal_key_points: convertValue('proposal_key_points', row.proposal_key_points),
                    formatted_proposal: convertValue('formatted_proposal', row.formatted_proposal),
                    full_proposal: convertValue('full_proposal', row.full_proposal),
                    step1_prompt_ai_edits: convertValue('step1_prompt_ai_edits', row.step1_prompt_ai_edits),
                    application_submitted: convertValue('application_submitted', row.application_submitted),
                    match_considerations: convertValue('match_considerations', row.match_considerations),
                    proposal_summary: convertValue('proposal_summary', row.proposal_summary),
                    key_themes: convertValue('key_themes', row.key_themes),
                    status: convertValue('status', row.status),
                    reasons: convertValue('reasons', row.reasons)
                };

                // Only add if we have valid speaker_id and opportunity_id
                if (mapped.speaker_id && mapped.opportunity_id) {
                    speakerOpportunities.push(mapped);
                    processedCount++;

                    // Process batch when it reaches batchSize
                    if (speakerOpportunities.length >= batchSize) {
                        const batch = [...speakerOpportunities];
                        speakerOpportunities.length = 0; // Clear the array
                        
                        processBatch(batch).catch(err => {
                            console.error('Batch processing error:', err);
                        });
                    }
                }
            })
            .on('end', async () => {
                console.log(`Finished reading CSV. Total rows processed: ${processedCount}`);
                
                // Process remaining records
                if (speakerOpportunities.length > 0) {
                    console.log(`Processing final batch of ${speakerOpportunities.length} records...`);
                    await processBatch(speakerOpportunities);
                }

                console.log('Import completed successfully!');
                process.exit(0);
            })
            .on('error', (error) => {
                console.error('Error reading CSV file:', error);
                process.exit(1);
            });

    } catch (error) {
        console.error('Unable to connect to the database:', error);
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\nReceived SIGINT. Gracefully shutting down...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\nReceived SIGTERM. Gracefully shutting down...');
    process.exit(0);
});

// Run the import
importSpeakerOpportunities();
