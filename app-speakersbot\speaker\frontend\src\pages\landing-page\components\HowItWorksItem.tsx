interface HowItWorksItemProps {
  icon: string;
  title: string;
  description: string;
}

export function HowItWorksItem({
  icon,
  title,
  description,
}: HowItWorksItemProps) {
  return (
    <div className="bg-black rounded-[25px] border border-gradient-border lg:p-6 p-4 transition-all duration-300">
      <div className="flex items-center gap-5 max-md:flex-col max-md:text-center">
        <div className="lg:size-[60px] size-[50px] flex items-center justify-center bg-gradient-tertiary rounded-[15px] text-tertiary-foreground lg:p-4 p-3">
          <img
            src={icon}
            alt={title}
            className="w-full h-full object-contain"
          />
        </div>
        <div className="flex-1 md:pr-5 mt-2 max-md:mt-0">
          <h3 className="text-white font-poppins font-medium lg:text-[22px] md:text-xl text-lg mb-1.5">
            {title}
          </h3>
          <p className="text-white font-poppins font-light lg:text-base text-sm !leading-relaxed mt-2">
            {description}
          </p>
        </div>
      </div>
    </div>
  );
}
