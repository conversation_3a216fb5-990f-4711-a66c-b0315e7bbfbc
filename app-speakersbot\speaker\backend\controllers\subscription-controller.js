const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");

const subscriptionService = require("../services/subscription-service");
const ApiResponse = require("../helpers/api-response");

/**
 * Get subscriptions with filters and pagination
 */
exports.getSubscriptions = async (req, res, next) => {
    try {
        const { status, data, message } = await subscriptionService.getSubscriptions(req.query, req.userId);
        
        if (status) {
             res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ status, data, message }));
        } else {
            res.status(RESPONSE_CODES.NOT_FOUND).send({ status: false, data: [], message: message || RESPONSE_MESSAGES.NOT_FOUND });
        }

    } catch (error) {
        console.error("Error fetching subscriptions:", error);
        next(error);
    }
}

exports.getSubscriptionHistory = async (req, res, next) => {
    try {
        const { status, data, message } = await subscriptionService.getSubscriptionHistory(req.query, req.userId);
        if (status) {
             res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ status, data, message }));
        } else {
            res.status(RESPONSE_CODES.NOT_FOUND).send({ status: false, data: [], message: message || RESPONSE_MESSAGES.NOT_FOUND });
        }
    } catch (error) {
        console.error("Error fetching subscription history:", error);
        next(error);
    }
}