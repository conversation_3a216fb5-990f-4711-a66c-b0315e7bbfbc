const settingController = require("../controllers/setting-controller");
const router = require("express").Router();

module.exports = (app) => {

    // ------------------------- settings ------------------------

    // save or update setting
    router.post("/admin/api/v1/settings", settingController.saveSetting);

    // get setting by key (using query parameter)
    router.get("/admin/api/v1/setting", settingController.getSettingByKey);

    // add/update(upsert) email template
    router.post("/manual/email-template", settingController.upsertEmailTemplate);

    // get email template by key
    router.get("/manual/email-template", settingController.getEmailTemplateByKey);

    app.use(router);

}