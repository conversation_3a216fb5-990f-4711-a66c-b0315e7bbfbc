const nodemailer = require('nodemailer');
const CustomError = require('../helpers/custome-error');
const { RESPONSE_CODES } = require('../helpers/response-codes');
const CONFIG = require('../config/config');
const settingService = require('./setting-service');

// Password reset
// await emailService.sendPasswordResetEmail(email, name, token, baseUrl);

// Email verification  
// await emailService.sendEmailVerificationEmail(email, name, token, baseUrl);

// Welcome email
// await emailService.sendWelcomeEmail(email, name, dashboardUrl);

// Meeting schedule
// await emailService.sendMeetingScheduleEmail(affiliateEmail, affiliateName, speakerName, meetingDetails, acceptUrl, rejectUrl);

// Subscription confirmation
// await emailService.sendSubscriptionPurchasedEmail(email, name, subscriptionDetails, dashboardUrl);

// XP points notification
// await emailService.sendXPPointsEmail(email, name, pointsDetails, dashboardUrl);

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransport({
      host: CONFIG.SMTP.HOST,
      port: CONFIG.SMTP.PORT,
      secure: true,
      auth: {
        user: CONFIG.SMTP.USER,
        pass: CONFIG.SMTP.PASS
      }
    });
  }

  /**
   * Send password reset email
   * @param {string} email - Speaker's email address
   * @param {string} name - Speaker's name
   * @param {string} resetToken - Password reset token
   * @param {string} resetUrl - Base URL for password reset
   */
  async sendPasswordResetEmail(email, name, resetToken, resetUrl) {
    try {
      if(!email || !resetToken || !resetUrl){
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email, resetToken and resetUrl are required");
      }
      // get template from settings table
      const templateData = await settingService.getEmailTemplateByKey('password_reset_template');

      const resetLink = `${resetUrl}/reset-password?token=${resetToken}`;
      
      const fromName = templateData?.from_name || 'Digital Speaker Agent';
      const subject = templateData?.subject || 'Reset Your Password - Digital Speaker Agent';
      const html = templateData?.html ? 
        templateData.html
          .replace(/\{\{UserName\}\}/g, name || 'User')
          .replace(/\{\{ResetLink\}\}/g, resetLink)
        :
        `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
              <h1 style="color: #333; margin: 0;">Password Reset Request</h1>
            </div>
            <div style="padding: 30px 20px;">
              <h2 style="color: #333;">Hi ${name || 'User'},</h2>
              <p style="color: #666; line-height: 1.6;">
                We received a request to reset your password for your Digital Speaker Agent account. 
                Click asdfghj the button below to reset your password.
              </p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${resetLink}" 
                   style="background-color: #dc3545; color: white; padding: 12px 30px; text-decoration: none; 
                          border-radius: 5px; display: inline-block; font-weight: bold;">
                  Reset Password
                </a>
              </div>
              <p style="color: #666; line-height: 1.6; font-size: 14px;">
                If the button doesn't work, you can copy and paste this link into your browser:
                <br>
                <a href="${resetLink}" style="color: #dc3545; word-break: break-all;">${resetLink}</a>
              </p>
              <p style="color: #666; line-height: 1.6; font-size: 14px;">
                This reset link will expire in 6 hours for security reasons.
              </p>
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              <p style="color: #999; font-size: 12px; text-align: center;">
                If you didn't request a password reset, please ignore this email.
              </p>
            </div>
          </div>
        `;

      const mailOptions = {
        from: `"${fromName}" <${CONFIG.SMTP.USER}>`,
        to: email,
        subject: subject,
        html: html,
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`Password reset email sent to ${email}`, result.messageId, new Date().toISOString());
      return result;
    } catch (error) {
      console.error(`Error sending password reset email to ${email} ${new Date().toISOString()}`, error);
      throw new CustomError(RESPONSE_CODES.SERVER_ERROR, 'Failed to send password reset email');
    }
  }

  /**
   * Send email verification email
   * @param {string} email - Speaker's email address
   * @param {string} name - Speaker's name
   * @param {string} verificationToken - Email verification token
   * @param {string} verificationUrl - Base URL for email verification
   */
  async sendEmailVerificationEmail(email, name, verificationToken, verificationUrl) {
    try {
      if(!email || !verificationToken || !verificationUrl){
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email, verificationToken and verificationUrl are required");
      }
      
      // get template from settings table
      const templateData = await settingService.getEmailTemplateByKey('email_verification_template');

      const verificationLink = `${verificationUrl}/verify-email?token=${verificationToken}`;
      
      const fromName = templateData?.from_name || 'Digital Speaker Agent';
      const subject = templateData?.subject || 'Verify Your Email - Digital Speaker Agent';
      const html = templateData?.html ? 
        templateData.html
          .replace(/\{\{UserName\}\}/g, name || 'User')
          .replace(/\{\{VerificationLink\}\}/g, verificationLink)
        :
        `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
              <h1 style="color: #333; margin: 0;">Welcome to Digital Speaker Agent!</h1>
            </div>
            <div style="padding: 30px 20px;">
              <h2 style="color: #333;">Hi ${name || 'User'},</h2>
              <p style="color: #666; line-height: 1.6;">
                Thank you for signing up! Please verify your email address to complete your registration 
                and start connecting with amazing speaking opportunities.
              </p>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${verificationLink}" 
                   style="background-color: #28a745; color: white; padding: 12px 30px; text-decoration: none; 
                          border-radius: 5px; display: inline-block; font-weight: bold;">
                  Verify Email Address
                </a>
              </div>
              <p style="color: #666; line-height: 1.6; font-size: 14px;">
                If the button doesn't work, you can copy and paste this link into your browser:
                <br>
                <a href="${verificationLink}" style="color: #28a745; word-break: break-all;">${verificationLink}</a>
              </p>
              <p style="color: #666; line-height: 1.6; font-size: 14px;">
                This verification link will expire in 24 hours for security reasons.
              </p>
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              <p style="color: #999; font-size: 12px; text-align: center;">
                If you didn't create an account, please ignore this email.
              </p>
            </div>
          </div>
        `;

      const mailOptions = {
        from: `"${fromName}" <${CONFIG.SMTP.USER}>`,
        to: email,
        subject: subject,
        html: html,
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`Email verification sent to ${email}`, result.messageId, new Date().toISOString());
      return result;
    } catch (error) {
      console.error(`Error sending email verification to ${email} ${new Date().toISOString()}`, error);
      throw new CustomError(RESPONSE_CODES.SERVER_ERROR, 'Failed to send email verification');
    }
  }

  /**
   * Send welcome email to speaker
   * @param {string} email - Speaker's email address
   * @param {string} name - Speaker's name
   * @param {string} dashboardUrl - URL to speaker dashboard
   */
  async sendWelcomeEmail(email, name, dashboardUrl) {
    try {
      if(!email || !dashboardUrl){
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Email and dashboardUrl are required");
      }
      
      // get template from settings table
      const templateData = await settingService.getEmailTemplateByKey('welcome_template');

      const fromName = templateData?.from_name || 'Digital Speaker Agent';
      const subject = templateData?.subject || 'Welcome to Digital Speaker Agent!';
      const html = templateData?.html ? 
        templateData.html
          .replace(/\{\{UserName\}\}/g, name || 'User')
          .replace(/\{\{DashboardUrl\}\}/g, dashboardUrl)
        :
        `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
              <h1 style="color: #333; margin: 0;">Welcome to Digital Speaker Agent!</h1>
            </div>
            <div style="padding: 30px 20px;">
              <h2 style="color: #333;">Hi ${name || 'User'},</h2>
              <p style="color: #666; line-height: 1.6;">
                Congratulations! Your email has been verified and your account is now active. 
                You're ready to start your journey as a speaker with amazing opportunities.
              </p>
              <div style="background-color: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #0066cc; margin-top: 0;">What's Next?</h3>
                <ul style="color: #666; line-height: 1.8;">
                  <li>Complete your speaker profile</li>
                  <li>Upload your headshot and bio</li>
                  <li>Browse available speaking opportunities</li>
                  <li>Connect with event organizers</li>
                </ul>
              </div>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${dashboardUrl}" 
                   style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; 
                          border-radius: 5px; display: inline-block; font-weight: bold;">
                  Go to Dashboard
                </a>
              </div>
              <p style="color: #666; line-height: 1.6; font-size: 14px;">
                If you have any questions, feel free to reach out to our support team.
              </p>
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              <p style="color: #999; font-size: 12px; text-align: center;">
                Thank you for joining Digital Speaker Agent!
              </p>
            </div>
          </div>
        `;

      const mailOptions = {
        from: `"${fromName}" <${CONFIG.SMTP.USER}>`,
        to: email,
        subject: subject,
        html: html,
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`Welcome email sent to ${email}`, result.messageId, new Date().toISOString());
      return result;
    } catch (error) {
      console.error(`Error sending welcome email to ${email} ${new Date().toISOString()}`, error);
      throw new CustomError(RESPONSE_CODES.SERVER_ERROR, 'Failed to send welcome email');
    }
  }

  /**
   * Send meeting schedule email to affiliate
   * @param {string} email - Affiliate's email address
   * @param {string} affiliateName - Affiliate's name
   * @param {string} speakerName - Speaker's name
   * @param {Object} meetingDetails - Meeting details (date, time, topic, etc.)
   * @param {string} acceptUrl - URL to accept meeting
   * @param {string} rejectUrl - URL to reject meeting
   */
  async sendMeetingScheduleEmail(email, affiliateName, speakerName, meetingDetails, acceptUrl, rejectUrl) {
    try {
      if(!email || !affiliateName || !speakerName || !meetingDetails || !acceptUrl || !rejectUrl){
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "All parameters are required");
      }
      
      // get template from settings table
      const templateData = await settingService.getEmailTemplateByKey('meeting_schedule_template');

      const fromName = templateData?.from_name || 'Digital Speaker Agent';
      const subject = templateData?.subject || `Meeting Request from ${speakerName}`;
      const html = templateData?.html ? 
        templateData.html
          .replace(/\{\{AffiliateName\}\}/g, affiliateName)
          .replace(/\{\{SpeakerName\}\}/g, speakerName)
          .replace(/\{\{MeetingDate\}\}/g, meetingDetails.date || 'TBD')
          .replace(/\{\{MeetingTime\}\}/g, meetingDetails.time || 'TBD')
          .replace(/\{\{MeetingTopic\}\}/g, meetingDetails.topic || 'General Discussion')
          .replace(/\{\{AcceptUrl\}\}/g, acceptUrl)
          .replace(/\{\{RejectUrl\}\}/g, rejectUrl)
        :
        `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
              <h1 style="color: #333; margin: 0;">Meeting Request</h1>
            </div>
            <div style="padding: 30px 20px;">
              <h2 style="color: #333;">Hi ${affiliateName},</h2>
              <p style="color: #666; line-height: 1.6;">
                You have received a meeting request from <strong>${speakerName}</strong> through Digital Speaker Agent.
              </p>
              <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #333; margin-top: 0;">Meeting Details:</h3>
                <p style="color: #666; margin: 5px 0;"><strong>Date:</strong> ${meetingDetails.date || 'TBD'}</p>
                <p style="color: #666; margin: 5px 0;"><strong>Time:</strong> ${meetingDetails.time || 'TBD'}</p>
                <p style="color: #666; margin: 5px 0;"><strong>Topic:</strong> ${meetingDetails.topic || 'General Discussion'}</p>
                ${meetingDetails.location ? `<p style="color: #666; margin: 5px 0;"><strong>Location:</strong> ${meetingDetails.location}</p>` : ''}
                ${meetingDetails.notes ? `<p style="color: #666; margin: 5px 0;"><strong>Notes:</strong> ${meetingDetails.notes}</p>` : ''}
              </div>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${acceptUrl}" 
                   style="background-color: #28a745; color: white; padding: 12px 20px; text-decoration: none; 
                          border-radius: 5px; display: inline-block; font-weight: bold; margin-right: 10px;">
                  Accept Meeting
                </a>
                <a href="${rejectUrl}" 
                   style="background-color: #dc3545; color: white; padding: 12px 20px; text-decoration: none; 
                          border-radius: 5px; display: inline-block; font-weight: bold;">
                  Decline Meeting
                </a>
              </div>
              <p style="color: #666; line-height: 1.6; font-size: 14px;">
                Please respond to this meeting request within 48 hours.
              </p>
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              <p style="color: #999; font-size: 12px; text-align: center;">
                This meeting request was sent through Digital Speaker Agent.
              </p>
            </div>
          </div>
        `;

      const mailOptions = {
        from: `"${fromName}" <${CONFIG.SMTP.USER}>`,
        to: email,
        subject: subject,
        html: html,
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`Meeting schedule email sent to ${email}`, result.messageId, new Date().toISOString());
      return result;
    } catch (error) {
      console.error(`Error sending meeting schedule email to ${email} ${new Date().toISOString()}`, error);
      throw new CustomError(RESPONSE_CODES.SERVER_ERROR, 'Failed to send meeting schedule email');
    }
  }

  /**
   * Send subscription purchased email
   * @param {string} email - Speaker's email address
   * @param {string} name - Speaker's name
   * @param {Object} subscriptionDetails - Subscription details (plan, amount, duration, etc.)
   * @param {string} dashboardUrl - URL to speaker dashboard
   */
  async sendSubscriptionPurchasedEmail(email, name, subscriptionDetails, dashboardUrl) {
    try {
      if(!email || !name || !subscriptionDetails || !dashboardUrl){
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "All parameters are required");
      }
      
      // get template from settings table
      const templateData = await settingService.getEmailTemplateByKey('subscription_purchased_template');

      const fromName = templateData?.from_name || 'Digital Speaker Agent';
      const subject = templateData?.subject || 'Subscription Confirmation - Digital Speaker Agent';
      const html = templateData?.html ? 
        templateData.html
          .replace(/\{\{UserName\}\}/g, name)
          .replace(/\{\{PlanName\}\}/g, subscriptionDetails.planName || 'Premium Plan')
          .replace(/\{\{Amount\}\}/g, subscriptionDetails.amount || '$0')
          .replace(/\{\{Duration\}\}/g, subscriptionDetails.duration || '1 month')
          .replace(/\{\{StartDate\}\}/g, subscriptionDetails.startDate || new Date().toLocaleDateString())
          .replace(/\{\{EndDate\}\}/g, subscriptionDetails.endDate || 'N/A')
          .replace(/\{\{DashboardUrl\}\}/g, dashboardUrl)
        :
        `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
              <h1 style="color: #333; margin: 0;">Subscription Confirmed!</h1>
            </div>
            <div style="padding: 30px 20px;">
              <h2 style="color: #333;">Hi ${name},</h2>
              <p style="color: #666; line-height: 1.6;">
                Thank you for your subscription! Your payment has been processed successfully 
                and your account has been upgraded.
              </p>
              <div style="background-color: #e7f3ff; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #0066cc; margin-top: 0;">Subscription Details:</h3>
                <p style="color: #666; margin: 5px 0;"><strong>Plan:</strong> ${subscriptionDetails.planName || 'Premium Plan'}</p>
                <p style="color: #666; margin: 5px 0;"><strong>Amount:</strong> ${subscriptionDetails.amount || '$0'}</p>
                <p style="color: #666; margin: 5px 0;"><strong>Duration:</strong> ${subscriptionDetails.duration || '1 month'}</p>
                <p style="color: #666; margin: 5px 0;"><strong>Start Date:</strong> ${subscriptionDetails.startDate || new Date().toLocaleDateString()}</p>
                <p style="color: #666; margin: 5px 0;"><strong>End Date:</strong> ${subscriptionDetails.endDate || 'N/A'}</p>
              </div>
              <div style="background-color: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h4 style="color: #155724; margin-top: 0;">What's Included:</h4>
                <ul style="color: #155724; line-height: 1.8; margin: 0;">
                  <li>Access to premium speaking opportunities</li>
                  <li>Priority matching with event organizers</li>
                  <li>Advanced analytics and insights</li>
                  <li>24/7 customer support</li>
                </ul>
              </div>
              <div style="text-align: center; margin: 30px 0;">
                <a href="${dashboardUrl}" 
                   style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; 
                          border-radius: 5px; display: inline-block; font-weight: bold;">
                  Access Dashboard
                </a>
              </div>
              <p style="color: #666; line-height: 1.6; font-size: 14px;">
                If you have any questions about your subscription, please contact our support team.
              </p>
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              <p style="color: #999; font-size: 12px; text-align: center;">
                Thank you for choosing Digital Speaker Agent!
              </p>
            </div>
          </div>
        `;

      const mailOptions = {
        from: `"${fromName}" <${CONFIG.SMTP.USER}>`,
        to: email,
        subject: subject,
        html: html,
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`Subscription purchased email sent to ${email}`, result.messageId, new Date().toISOString());
      return result;
    } catch (error) {
      console.error(`Error sending subscription purchased email to ${email} ${new Date().toISOString()}`, error);
      throw new CustomError(RESPONSE_CODES.SERVER_ERROR, 'Failed to send subscription purchased email');
    }
  }

  /**
   * Send XP points credit/debit email
   * @param {string} email - Speaker's email address
   * @param {string} name - Speaker's name
   * @param {Object} pointsDetails - Points details (type, amount, reason, totalPoints, etc.)
   * @param {string} dashboardUrl - URL to speaker dashboard
   */
  async sendXPPointsEmail(email, name, pointsDetails, dashboardUrl) {
    try {
      if(!email || !name || !pointsDetails || !dashboardUrl){
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "All parameters are required");
      }
      
      // get template from settings table
      const templateData = await settingService.getEmailTemplateByKey('xp_points_template');

      const isCredit = pointsDetails.type === 'credit';
      const pointsText = isCredit ? 'earned' : 'deducted';
      const pointsColor = isCredit ? '#28a745' : '#dc3545';
      const pointsIcon = isCredit ? '🎉' : '📉';

      const fromName = templateData?.from_name || 'Digital Speaker Agent';
      const subject = templateData?.subject || `XP Points ${pointsText.charAt(0).toUpperCase() + pointsText.slice(1)} - Digital Speaker Agent`;
      const html = templateData?.html ? 
        templateData.html
          .replace(/\{\{UserName\}\}/g, name)
          .replace(/\{\{PointsType\}\}/g, pointsDetails.type || 'credit')
          .replace(/\{\{PointsAmount\}\}/g, pointsDetails.amount || '0')
          .replace(/\{\{PointsReason\}\}/g, pointsDetails.reason || 'Activity')
          .replace(/\{\{TotalPoints\}\}/g, pointsDetails.totalPoints || '0')
          .replace(/\{\{DashboardUrl\}\}/g, dashboardUrl)
        :
        `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
            <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
              <h1 style="color: #333; margin: 0;">${pointsIcon} XP Points Update</h1>
            </div>
            <div style="padding: 30px 20px;">
              <h2 style="color: #333;">Hi ${name},</h2>
              <p style="color: #666; line-height: 1.6;">
                You have ${pointsText} XP points for your activity on Digital Speaker Agent!
              </p>
              <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3 style="color: #333; margin-top: 0;">Points Details:</h3>
                <div style="text-align: center; margin: 20px 0;">
                  <div style="font-size: 48px; color: ${pointsColor}; font-weight: bold;">
                    ${isCredit ? '+' : '-'}${pointsDetails.amount || '0'}
                  </div>
                  <p style="color: #666; margin: 5px 0;"><strong>Reason:</strong> ${pointsDetails.reason || 'Activity'}</p>
                  <p style="color: #666; margin: 5px 0;"><strong>Total Points:</strong> ${pointsDetails.totalPoints || '0'}</p>
                </div>
              </div>
              ${isCredit ? `
                <div style="background-color: #d4edda; padding: 15px; border-radius: 8px; margin: 20px 0;">
                  <h4 style="color: #155724; margin-top: 0;">Keep Earning Points!</h4>
                  <ul style="color: #155724; line-height: 1.8; margin: 0;">
                    <li>Complete your speaker profile</li>
                    <li>Apply to speaking opportunities</li>
                    <li>Connect with event organizers</li>
                    <li>Share your success stories</li>
                  </ul>
                </div>
              ` : `
                <div style="background-color: #f8d7da; padding: 15px; border-radius: 8px; margin: 20px 0;">
                  <h4 style="color: #721c24; margin-top: 0;">Points Deducted</h4>
                  <p style="color: #721c24; margin: 0;">Points were deducted as per our terms and conditions.</p>
                </div>
              `}
              <div style="text-align: center; margin: 30px 0;">
                <a href="${dashboardUrl}" 
                   style="background-color: #007bff; color: white; padding: 12px 30px; text-decoration: none; 
                          border-radius: 5px; display: inline-block; font-weight: bold;">
                  View Dashboard
                </a>
              </div>
              <p style="color: #666; line-height: 1.6; font-size: 14px;">
                Track your progress and unlock more opportunities with your XP points!
              </p>
              <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
              <p style="color: #999; font-size: 12px; text-align: center;">
                Digital Speaker Agent - Gamification System
              </p>
            </div>
          </div>
        `;

      const mailOptions = {
        from: `"${fromName}" <${CONFIG.SMTP.USER}>`,
        to: email,
        subject: subject,
        html: html,
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log(`XP points email sent to ${email}`, result.messageId, new Date().toISOString());
      return result;
    } catch (error) {
      console.error(`Error sending XP points email to ${email} ${new Date().toISOString()}`, error);
      throw new CustomError(RESPONSE_CODES.SERVER_ERROR, 'Failed to send XP points email');
    }
  }
/**
 * Send Early Access Signup Email
 * @param {string} email - User's email address
 * @param {string} name - User's name
 */
async sendEarlyAccessEmail(email, name) {
  try {
    if (!email || !name) {
      throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "All parameters are required");
    }

    // Get template from settings table
    const templateData = await settingService.getEmailTemplateByKey('early_access_template');

    const fromName = templateData?.from_name || 'Digital Speaker Agent';
    const subject = templateData?.subject || 'Welcome to Early Access - Digital Speaker Agent';

    const html = templateData?.html
      ? templateData.html
          .replace(/\{\{UserName\}\}/g, name)
      : `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="background-color: #f8f9fa; padding: 20px; text-align: center;">
            <h1 style="color: #333; margin: 0;">Welcome to Early Access</h1>
          </div>
          <div style="padding: 30px 20px;">
            <h2 style="color: #333;">Hi ${name},</h2>
            <p style="color: #666; line-height: 1.6;">
              Thank you for signing up for early access to <strong>Digital Speaker Agent</strong>! 
              We're thrilled to have you on board as one of our early adopters.
            </p>
            <div style="background-color: #e9f7fe; padding: 20px; border-radius: 8px; margin: 25px 0;">
              <h3 style="color: #004085; margin-top: 0;">Next Steps</h3>
              <p style="color: #004085; line-height: 1.6; margin: 0;">
                We'll notify you as soon as early access is live. Stay tuned for updates in your inbox!
              </p>
            </div>
            <p style="color: #666; line-height: 1.6; margin: 20px 0; text-align: center; font-size: 15px;">
              We’re building something amazing — and you get to experience it first. Welcome aboard!
            </p>
            <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
            <p style="color: #999; font-size: 12px; text-align: center;">
              Digital Speaker Agent - Early Access Program
            </p>
          </div>
        </div>
      `;

    const mailOptions = {
      from: `"${fromName}" <${CONFIG.SMTP.USER}>`,
      to: email,
      subject: subject,
      html: html,
    };

    const result = await this.transporter.sendMail(mailOptions);
    console.log(`Early Access email sent to ${email}`, result.messageId, new Date().toISOString());
    return result;
  } catch (error) {
    console.error(`Error sending Early Access email to ${email} ${new Date().toISOString()}`, error);
    throw new CustomError(RESPONSE_CODES.SERVER_ERROR, 'Failed to send Early Access email');
  }
}


  /**
   * Test email configuration
   */
  async testConnection() {
    try {
      await this.transporter.verify();
      console.log('Email service is ready to send emails');
      return true;
    } catch (error) {
      console.error('Email service connection failed:', error);
      return false;
    }
  }
}

module.exports = new EmailService();
