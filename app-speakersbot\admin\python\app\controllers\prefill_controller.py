from fastapi import APIRouter, HTTPException
from app.services.prefill_service import prefill_speaker_opportunity

router = APIRouter(tags=["Form Filling"])

@router.post("/prefill-opportunity/{speaker_id}/{opportunity_id}")
def prefill_opportunity(speaker_id: int, opportunity_id: int):
    """
    Prefill speaker opportunity data based on opportunity form type.
    
    Args:
        speaker_id: ID of the speaker
        opportunity_id: ID of the opportunity
    
    Returns:
        Dictionary with prefill result
    """
    try:
        return prefill_speaker_opportunity(speaker_id, opportunity_id)
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
