"""
Digital Form API
Provides an endpoint to generate structured details for a speaker/opportunity pair.
Only handles API concerns; business logic lives in DigitalFormService.
"""

from fastapi import APIRouter, HTTPException
import logging
from app.services.prefill_service import prefill_speaker_opportunity
from app.services.digital_form_service import DigitalFormService
from app.helpers.pdf_builder import PDFGeneratorService

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/digital-form", tags=["Digital Form"])

service = DigitalFormService()
pdf_service = PDFGeneratorService()

@router.post("/generate-details")
async def generate_details(speaker_id: int, opportunity_id: int) -> dict:
    """Generate LLM-based details for the given speaker and opportunity IDs, upload PDF to S3, and return URL."""
    try:
        if speaker_id <= 0 or opportunity_id <= 0:
            raise HTTPException(status_code=400, detail="speaker_id and opportunity_id must be positive integers")
        
        # Generate the data first
        result = service.generate_details(speaker_id, opportunity_id)
        prefill_speaker_opportunity(speaker_id, opportunity_id)
        # Generate PDF and upload to S3
        pdf_service = PDFGeneratorService()
        pdf_url = pdf_service.generate_and_upload_pdf(result, speaker_id, opportunity_id)
        # Return PDF URL
        return {"pdf_url": pdf_url, "message": "PDF generated and uploaded successfully"}
        
    except Exception as e:
        logger.exception(f"Error generating digital form for speaker {speaker_id}, opportunity {opportunity_id}")
        raise HTTPException(status_code=500, detail=f"Error generating details: {e}")

