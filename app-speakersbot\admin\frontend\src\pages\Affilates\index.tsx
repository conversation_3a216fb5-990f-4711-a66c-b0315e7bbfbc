import React, { useState, useEffect } from 'react';
import {
  DownloadOutlined,
  UserAddOutlined,
  LinkOutlined,
  BarChartOutlined,
} from '@ant-design/icons';
import { Search, RotateCcw } from 'lucide-react';
import { Button as Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Card} from '../../components/ui/card';
import { Pagination, PaginationContent, PaginationEllipsis, PaginationItem, PaginationLink, PaginationNext, PaginationPrevious } from '../../components/ui/pagination';
import StatusTag from '@/components/common/StatusTag';
import { useAppState } from '../../state/AppStateProvider';
import { useAuth } from '../../state/AuthContext';
import { getPermissions } from '../../utils/permissions';
import { useLocation, useNavigate } from 'react-router-dom';
import type { Affiliate } from '../../types';
import dayjs from 'dayjs';
import { useGetAffiliatesQuery, useLazyExportAffiliatesCsvQuery } from '../../apis/affiliatesApi';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '../../components/ui/tooltip';
import { Badge } from '../../components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '../../components/ui/dialog';
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { useToast } from '../../hooks/use-toast';
import { useDebouncedCallback } from '../../utils/debounce';
import LoadingSkeleton from '../../components/common/LoadingSkeleton';
import { Skeleton } from '../../components/ui/skeleton';

const { } = {} as any;

const Affiliates: React.FC = () => {
  const { dispatch } = useAppState();
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const roleName = (user as any)?.role?.name ? String((user as any).role.name).toLowerCase() : undefined;
  const permissions = getPermissions(roleName as any);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { toast } = useToast();
  const [triggerExport, { isFetching: isExporting }] = useLazyExportAffiliatesCsvQuery();
  const [searchParam, setSearchParam] = useState<string>('');
  const [nameFilter, setNameFilter] = useState<string | undefined>(undefined);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(10);
  const [statusFilter, setStatusFilter] = useState<string | undefined>();

  // Debounced setter for search param to avoid spamming API while typing
  const debouncedSetSearchParam = useDebouncedCallback((value: string) => {
    setSearchParam(value);
  }, 400);

  const { data, isLoading, isError, isFetching } = useGetAffiliatesQuery({
    search: searchParam || undefined,
    filter: statusFilter === 'all' || !statusFilter ? undefined : { is_active: statusFilter === 'true' },
    page,
    limit: pageSize,
  });

  // Filter states
  const [searchText, setSearchText] = useState('');
  const [selectedAffiliate, setSelectedAffiliate] = useState<string[]>([]);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs] | null>(null);


  // Handle selectedUserId from navigation state (from Users page)
  useEffect(() => {
    const state = location.state as { selectedUserId?: string } | null;
    if (state?.selectedUserId) {
      // Navigate directly to affiliate detail page
      navigate(`/admin/affiliates/${state.selectedUserId}`);
      navigate(`/admin/affiliates/${state.selectedUserId}`);
    }
  }, [location.state, navigate]);

  // Load filters from localStorage
  useEffect(() => {
    const savedFilters = localStorage.getItem('affiliates_filters');
    if (savedFilters) {
      const filters = JSON.parse(savedFilters);
      setSearchText(filters.searchText || '');
      setSelectedAffiliate(filters.selectedAffiliate || []);
    }
  }, []);

  // Save filters to localStorage
  useEffect(() => {
    const filters = { searchText, selectedAffiliate };
    localStorage.setItem('affiliates_filters', JSON.stringify(filters));
  }, [searchText, selectedAffiliate]);

  // Backend affiliates mapped via RTK Query
  const affiliatesList: Affiliate[] = data?.affiliates || [];

  // Filter affiliates for affiliate users (they can only see their own data)
  const filteredAffiliates = roleName === 'affiliate'
    ? affiliatesList.filter(affiliate => affiliate.email === user.email)
    : affiliatesList;

  const handleSelectAffiliateUser = (affiliate: Affiliate) => {
    navigate(`/admin/affiliates/${affiliate?.affiliateId}`);
    handleResetFilters()
  };

  const handleAddAffiliate = (values: { name: string; email: string }) => {
    const affiliateId = `AFF${Date.now()}`;
    const affiliateLink = `https://speakerbot.com/signup?ref=${affiliateId}`;

    const newAffiliate: Affiliate = {
      id: Date.now().toString(),
      name: values.name,
      email: values.email,
      affiliateId,
      link: affiliateLink,
      clicks: 0,
      signups: 0,
      conversions: 0,
        createdAt: new Date().toISOString(),
        status: true,
    };

    dispatch({ type: 'ADD_AFFILIATE', payload: newAffiliate });
    toast({ description: 'Affiliate added successfully' });
    setIsModalVisible(false);
  };

  const handleCopyLink = (link: string) => {
    navigator.clipboard.writeText(link);
    toast({ description: 'Affiliate link copied to clipboard' });
  };

  const handleResetFilters = () => {
    setSearchText('');
    setSelectedAffiliate([]);
    setDateRange(null);
    debouncedSetSearchParam.cancel();
    setSearchParam('');
    setNameFilter(undefined);
    setStatusFilter("all")
    // toast({ description: 'Filters reset' });
  };

  const getFilteredAffiliates = () => {
    return filteredAffiliates.filter(affiliate => {
      const matchesSearch = !searchText ||
        affiliate.name.toLowerCase().includes(searchText.toLowerCase()) ||
        affiliate.email.toLowerCase().includes(searchText.toLowerCase()) ||
        affiliate.affiliateId.toLowerCase().includes(searchText.toLowerCase());
      const matchesAffiliate = selectedAffiliate.length === 0 || selectedAffiliate.includes(affiliate.id);
      const matchesDate = !dateRange || (
        dayjs(affiliate.createdAt).isAfter(dateRange[0]) &&
        dayjs(affiliate.createdAt).isBefore(dateRange[1])
      );

      return matchesSearch && matchesAffiliate && matchesDate;
    });
  };

  const handleExportMetrics = async () => {
    if (!permissions.canImportExport) {
      toast({ description: 'You do not have permission to export data' });
      return;
    }

    try {
      const csvData = await triggerExport({
        search: searchParam || undefined,
        filter: statusFilter === 'all' || !statusFilter ? undefined : { is_active: statusFilter === 'true' },
      }).unwrap();

      // Create and download the CSV file
      const blob = new Blob([csvData], { type: 'text/csv' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `affiliate-metrics-${new Date().toISOString().split('T')[0]}.csv`;
      a.click();
      window.URL.revokeObjectURL(url);

      toast({ description: 'Metrics exported successfully' });
    } catch (error) {
      console.error('Export failed:', error);
      toast({ description: 'Failed to export metrics. Please try again.' });
    }
  };

  // Table rendering moved to shadcn table below

  // Calculate overall metrics for all affiliates (prefer backend summary if present)
  const calculated = getFilteredAffiliates().reduce(
    (acc, affiliate) => ({
      totalClicks: acc.totalClicks + affiliate.clicks,
      totalSignups: acc.totalSignups + affiliate.signups,
      totalConversions: acc.totalConversions + affiliate.conversions,
    }),
    { totalClicks: 0, totalSignups: 0, totalConversions: 0 }
  );
  const displayMetrics = {
    totalClicks: data?.summary?.totalClick ?? calculated.totalClicks,
    totalSignups: data?.summary?.totalSignup ?? calculated.totalSignups,
    totalConversions: data?.summary?.totalConversion ?? calculated.totalConversions,
  };

  const overallConversionRate = displayMetrics.totalClicks > 0
    ? (displayMetrics.totalConversions / displayMetrics.totalClicks) * 100
    : 0;


  return (
    <div>
      {isError && (
        <Alert className="mb-4">
          <AlertDescription>Failed to load affiliates</AlertDescription>
        </Alert>
      )}
      <div className="mb-6 flex justify-between items-center">
        <h1 className="m-0 text-2xl font-bold text-foreground">
          {roleName === 'affiliate' ? 'My Affiliate Performance' : 'Manage Affiliates'}
        </h1>
        <TooltipProvider>
          <div className="flex gap-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <button className="bg-card border px-3 py-1 rounded-md text-sm" onClick={handleExportMetrics} disabled={!permissions.canImportExport || isExporting}>
                  <DownloadOutlined className="mr-2" />
                  Export Metrics
                </button>
              </TooltipTrigger>
              {!permissions.canImportExport && (
                <TooltipContent>You don't have permission to export data</TooltipContent>
              )}
            </Tooltip>
            {/* <Tooltip>
              <TooltipTrigger asChild>
                <Button onClick={() => setIsModalVisible(true)} disabled={!permissions.canCreate}>
                  <PlusOutlined className="mr-2" />
                  Add Affiliate
                </Button>
              </TooltipTrigger>
              {!permissions.canCreate && (
                <TooltipContent>You don't have permission to add affiliates</TooltipContent>
              )}
            </Tooltip> */}
          </div>
        </TooltipProvider>
      </div>

      {/* Affiliate Performance Overview */}
      {isLoading ? (
        <LoadingSkeleton type="card" rows={1} className='px-0 '/>
      ) : (
      <div className="mb-6 bg-tertiary rounded-md">
        <div className="flex items-center justify-between p-4 px-6 border-b rounded">
          <div>
            <h1 className="text-lg font-semibold text-foreground"> Affiliate Performance Overview</h1>
          </div>
        </div>

        <div className="flex gap-3 p-4 px-6">
          <div className="flex flex-col gap-2 py-3 rounded justify-start">
            <div className="text-sm text-muted-foreground">Total Clicks</div>
            <div className='flex items-center justify-start text-start gap-1'>
              <LinkOutlined className='text-[#1890FF]'/>
              <p className="text-xl font-semibold">{displayMetrics.totalClicks}</p>
            </div>


          </div>
          <div className="flex flex-col items-start gap-2 p-3 rounded">
            <div className="text-sm text-muted-foreground">Total Signups</div>
            <div className="flex items-center justify-start gap-1">
              <UserAddOutlined className='text-[#52C41A]'/>
              <div className="text-xl font-semibold">{displayMetrics.totalSignups}</div>
            </div>
          </div>
          <div className="flex justify-start flex-col gap-2 p-3 rounded">
            <div className="text-sm text-muted-foreground">Total Conversions</div>
            <div className="flex items-center gap-1 justify-start">
              <BarChartOutlined className='text-[#FA8C16]'/>
              <div className="text-xl font-semibold">{displayMetrics.totalConversions}</div>
            </div>
          </div>
          <div className="flex justify-start flex-col gap-2 p-3 rounded">
            <div className="text-sm text-muted-foreground justify-star gap-1">Overall Conversion Rate</div>
            <div className="flex justify-start gap-1">
              <div className="text-xl font-semibold">{overallConversionRate.toFixed(1)}<span className='text-[#FA8C16]'>%</span></div>
            </div>
          </div>

        </div>
      </div>
      )}

      {/* Search and Filters */}
      {isLoading ? (
        <Card className="mb-6 p-1 px-2 bg-tertiary">
          <LoadingSkeleton type="form" rows={1} className="p-0" />
        </Card>
      ) : (
          <Card className="mb-6 p-4 px-6 bg-tertiary">
            <div className="flex  gap-4 items-center">
              <div className="relative w-full max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search affiliates..."
                  value={searchText}
                  onChange={e => { const v = e.target.value; setSearchText(v); debouncedSetSearchParam(v); }}
                  className="pl-10"
                />
              </div>

                <div className="w-full">
                  <Select
                    value={statusFilter ?? 'all'}
                    onValueChange={(value) =>
                      setStatusFilter(value === 'all' ? 'all' : value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Filter by status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Affiliates</SelectItem>
                      <SelectItem value="true">Active</SelectItem>
                      <SelectItem value="false">Inactive</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

              <Button
                variant="outline"
                onClick={handleResetFilters}
                className="m-w-sm col-span-1"
              >
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset Filters
              </Button>
            </div>
          </Card>
      )}


      {/* Affiliates Table */}
      <div className="bg-tertiary rounded-md">
        <div className='flex items-center justify-between p-4 border-b'>
          <div className="text-lg font-semibold">Affiliate Users</div>
          <span className='text-muted-foreground text-sm flex items-center gap-3'>
            Row per page
            <Select value={String(pageSize)} onValueChange={(v) => { setPageSize(Number(v)); setPage(1); }}>
            <SelectTrigger className="w-[60px]">
              <SelectValue />
            </SelectTrigger>
            <SelectContent className="p-0">
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="15">25</SelectItem>
              <SelectItem value="20">50</SelectItem>
              <SelectItem value="50">100</SelectItem>
            </SelectContent>
          </Select>


          </span>

        </div>
        { (isLoading || isFetching) ? (
          <LoadingSkeleton type="table" rows={5} />
        ) : getFilteredAffiliates().length === 0 ? (
          <div className="text-center py-12">
            <div className="text-lg text-muted-foreground">No affiliate users found</div>
            <div className="text-sm text-muted-foreground mt-2">
              {searchText || statusFilter !== 'all' 
                ? 'Try adjusting your filters to see more results.'
                : 'No affiliates have been registered yet.'}
            </div>
          </div>
        ) : (
            <div className='border rounded-md mx-3 my-4'>
              <Table className='rounded-xl '>
                <TableHeader>
                  <TableRow className='text-white'>
                    <TableHead>Affiliate Name</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Clicks</TableHead>
                    <TableHead>Conversion Rate</TableHead>
                    <TableHead>Commission</TableHead>
                    <TableHead>Joined</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
            
                  {getFilteredAffiliates().map((record) => {
                    const rate = record.clicks > 0 ? (record.conversions / record.clicks) * 100 : 0;
                    const isActive = record.conversions > 0 || record.clicks > 10;
                    
                    return (
                      <TableRow key={record.id} className="cursor-pointer" onClick={() => handleSelectAffiliateUser(record)}>
                        <TableCell>
                          <div className="font-medium">{record.name}</div>
                          <div className="text-sm text-muted-foreground">{record.email}</div>
                        </TableCell>
                        <TableCell>
                          <StatusTag status={record.status ? 'active' : 'inactive'} label={record.status ? 'Active' : 'Inactive'}   className={record.status ? "border border-[#88E788]" : ""} />
                        </TableCell>
                        <TableCell>{record.clicks}</TableCell>
                        <TableCell>
                          <StatusTag
                            status="active"
                            label={`${Number(record?.conversions ?? 0).toFixed(1)}%`}
                          />

                        </TableCell>
                        <TableCell>
                          <span className="font-semibold">${(record.conversions * 29 * 0.1).toFixed(2)}</span>
                        </TableCell>
                        <TableCell>{new Date(record.createdAt).toLocaleDateString()}</TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>

            </div>
          
        )}
        {(isLoading || isFetching) ? (
          <div className="flex items-center p-4 justify-end">
            <div className="flex items-center gap-2">
              <Skeleton className="h-8 w-8 rounded" />
              <Skeleton className="h-8 w-40 rounded" />
              <Skeleton className="h-8 w-8 rounded" />
            </div>
          </div>
        ) : getFilteredAffiliates().length > 0 ? (
            <div className="flex items-center p-4 flex justify-between">
              <span>
                Showing {((data?.pagination?.page ?? 1) - 1) * pageSize + 1} to {Math.min((data?.pagination?.page ?? 1) * pageSize, data?.pagination?.total ?? 0)} of {data?.pagination?.total ?? 0} affiliates
              </span>
           
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious 
                    onClick={() => setPage((p) => Math.max(1, p - 1))} 
                    aria-disabled={(data?.pagination?.page ?? page) <= 1}
                    className={(data?.pagination?.page ?? page) <= 1 ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>
                {(() => {
                  const totalPages = data?.pagination?.totalPages ?? 1;
                  const current = data?.pagination?.page ?? page;
                  const pages: number[] = [];
                  const start = Math.max(1, current - 2);
                  const end = Math.min(totalPages, start + 4);

                  // Show first page if not included
                  if (start > 1) {
                    pages.push(1);
                  }
                  // Left ellipsis if gap after first page
                  const showLeftEllipsis = start > 2;

                  // Core window pages
                  const windowPages: number[] = [];
                  for (let p = start; p <= end; p++) windowPages.push(p);

                  // Right ellipsis if gap before last page
                  const showRightEllipsis = end < (totalPages - 1);

                  // Build JSX
                  return (
                    <>
                      {pages.map((p) => (
                        <PaginationItem key={`first-${p}`}>
                          <PaginationLink isActive={p === current} onClick={() => setPage(p)}>
                            {p}
                          </PaginationLink>
                        </PaginationItem>
                      ))}
                      {showLeftEllipsis && (
                        <PaginationItem key="left-ellipsis">
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}
                      {windowPages.map((p) => (
                        <PaginationItem key={p}>
                          <PaginationLink isActive={p === current} onClick={() => setPage(p)}>
                            {p}
                          </PaginationLink>
                        </PaginationItem>
                      ))}
                      {showRightEllipsis && (
                        <PaginationItem key="right-ellipsis">
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}
                      {end < totalPages && (
                        <PaginationItem key={`last-${totalPages}`}>
                          <PaginationLink isActive={totalPages === current} onClick={() => setPage(totalPages)}>
                            {totalPages}
                          </PaginationLink>
                        </PaginationItem>
                      )}
                    </>
                  );
                })()}
                <PaginationItem>
                  <PaginationNext 
                    onClick={() => setPage((p) => p + 1)} 
                    aria-disabled={(data?.pagination?.page ?? page) >= (data?.pagination?.totalPages ?? 1)}
                    className={(data?.pagination?.page ?? page) >= (data?.pagination?.totalPages ?? 1) ? 'pointer-events-none opacity-50' : 'cursor-pointer'}
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        ) : null}
      </div>


      {/* Add Affiliate Dialog */}
      <Dialog open={isModalVisible} onOpenChange={setIsModalVisible}>
        <DialogTrigger asChild>
          <span />
        </DialogTrigger>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Affiliate</DialogTitle>
          </DialogHeader>
          <div className="grid gap-4 py-2">
            <div className="grid gap-2">
              <label className="text-sm font-medium">Affiliate Name</label>
              <Input placeholder="Enter affiliate name..." id="aff_name" />
            </div>
            <div className="grid gap-2">
              <label className="text-sm font-medium">Email Address</label>
              <Input type="email" placeholder="Enter email address..." id="aff_email" />
            </div>
          </div>
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setIsModalVisible(false)}>Cancel</Button>
            <Button onClick={() => {
              const name = (document.getElementById('aff_name') as HTMLInputElement)?.value?.trim();
              const email = (document.getElementById('aff_email') as HTMLInputElement)?.value?.trim();
              if (!name || !email) { toast({ description: 'Please enter name and email' }); return; }
              handleAddAffiliate({ name, email });
            }}>Generate Affiliate Link</Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );

};

export default Affiliates;