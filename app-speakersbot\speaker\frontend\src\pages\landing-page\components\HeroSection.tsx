import { <PERSON><PERSON> } from "@/components/ui/button";
import HeroVideo from "@/assets/images/EA-heroSection.mp4";
import <PERSON><PERSON>ogo from "@/assets/images/LP-logo.png";

const HeroSection = () => {
  return (
    <section className="relative min-h-[100dvh] flex items-center justify-center md:px-10 px-5 xl:py-[200px] md:py-[120px] py-[80px]">
      <div className="absolute inset-0 w-full h-full">
        <video
          autoPlay
          muted
          loop
          playsInline
          className="w-full h-full object-cover"
        >
          <source src={HeroVideo} type="video/mp4" />
        </video>
        <div className="absolute inset-0 z-0 bg-gradient-background" />
      </div>
      <div className="relative z-10 max-w-[1820px] mx-auto">
        <div className="relative z-10 text-center">
          <p className="text-white font-poppins font-semibold xl:text-[42px] text-[34px]  leading-tight">
            Get More Gigs
          </p>

          <div className="flex justify-end md:my-3 my-4">
            <img
              src={LPLogo}
              alt=""
              className="w-auto mx-auto xl:h-[90px] md:h-[60px] h-auto opacity-80 pointer-events-none object-contain"
              aria-hidden="true"
            />
          </div>

          <p className="text-white font-poppins font-normal xl:text-[26px] md:text-[22px] text-lg xl:leading-[1.8] leading-[1.5] mx-auto xl:max-w-6xl lg:max-w-4xl">
            Most speakers spend more time hunting for gigs than speaking, and we
            flip that equation by booking you paid gigs on autopilot
          </p>

          <div className="flex justify-center xl:mt-12 mt-10">
            <Button
              size="lg"
              onClick={() => {
                const registrationForm =
                  document.getElementById("registration-form");
                if (registrationForm) {
                  registrationForm.scrollIntoView({ behavior: "smooth" });
                }
              }}
              className="bg-gradient-tertiary text-tertiary-foreground font-poppins font-semibold xl:text-xl lg:text-lg text-base lg:px-12 px-8 lg:py-4 py-3 rounded-[18px] h-auto hover:opacity-90 transition-opacity shadow-lg w-full max-w-[350px]"
            >
              Pre-Register Now
            </Button>
          </div>
        </div>
      </div>
      <div className="absolute inset-x-0 bottom-0 lg:h-[154px] h-[100px] z-[5] bg-gradient-background-overlay" />
    </section>
  );
};

export default HeroSection;
