
const connection = require('../connection');
const { DataTypes } = require('sequelize');


const ScrapingLogging = connection.define('ScrapingLogging', {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: 'Primary key for the scraping logging',
    },
    topic: {
        type: DataTypes.STRING,
        allowNull: false,
        comment: 'Topic of the scraping logging',
    },
    item_count: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Item count of the scraping logging',
    },
    status: {
        type: DataTypes.ENUM('success', 'error', 'running', 'paused'),
        allowNull: false,
        comment: 'Status of the scraping logging',
    },
    reason:{
        type: DataTypes.TEXT,
        allowNull: true,
        comment: 'Message of the scraping logging',
    },
    started_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: 'Start time of the scraping logging',
    },
    ended_at: {
        type: DataTypes.DATE,
        allowNull: false,
        comment: 'End time of the scraping logging',
    },
    created_at: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: DataTypes.NOW,
        comment: 'Record creation timestamp',
    },
    updated_at: {
        type: DataTypes.DATE,
        allowNull: true,
        defaultValue: DataTypes.NOW,
        comment: 'Record last update timestamp',
    },
    deleted_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'Record deletion timestamp(soft delete)',
    }
}, {
    tableName: 'scraping_logging',
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
    paranoid: true,
    deletedAt: "deleted_at",
});

module.exports = ScrapingLogging;