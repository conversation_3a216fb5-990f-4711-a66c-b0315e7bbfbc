const ApiResponse = require("../helpers/api-response");
const { exportAsCSV } = require("../helpers/csv-helper");
const CustomError = require("../helpers/custome-error");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");

const affiliateService = require("../services/affiliate-service");

/**
 * Get all affiliates with filters and pagination
 */
exports.getAffiliates = async (req, res, next) => {
    try {
        const result = await affiliateService.getAffiliates(req);
        const { status, message, data, pageData } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data:{affiliates:data.affiliates,summary:data.summary}, pagination:pageData }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
        

    } catch (error) {
        next(error);
    }
};

/**
 * Get affiliate by id
 */
exports.getAffiliateById = async (req, res, next) => {
    try {
        const result = await affiliateService.getAffiliateById(req);
        const { status, message, data, pageData } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data:data, pagination:pageData }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    } catch (error) {
        next(error);
    }
};

/**
 * Get affiliate names only
 */
exports.getAffiliateNames = async (req, res, next) => {
    try {
        const result = await affiliateService.getAffiliateNames(req);
        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    } catch (error) {
        next(error);
    }
};



exports.exportAffiliates = async (req, res, next) => {
    try {
        const result = await affiliateService.exportAffiliates(req, res);
       const { status , data } = result; 

       if(status){ 
        return exportAsCSV(res, data, 'affiliates.csv');
       }
 
    } catch (error) {
        next(error);
    }
};


exports.getAffiliateSpeakers = async (req, res, next) => {
    try {
        const result = await affiliateService.getAffiliateSpeakers(req);
        const { status, message, data ,pageData} = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data, pagination:pageData }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    } catch (error) {
        next(error);
    }
};

exports.getLandingPageData = async (req, res, next) => {
    try {
        const id = req.params.id;
        const result = await affiliateService.getLandingPageData(id);
        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    } catch (error) {
        next(error);
    }
}

exports.updateLandingPageData = async (req, res, next) => {
    try {
        const id = req.params.id;
        const formData = req.body;
        
        const result = await affiliateService.updateLandingPageData(id, formData);
        const { status, message, data } = result;

        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    } catch (error) {
        next(error);
    }
}
