import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Camera, Star, Calendar, MapPin, Upload, Edit3, Mail, Phone, Linkedin, Building, Tag, DollarSign, Clock, CheckCircle, AlertCircle, MessageSquare, FileText, Trophy, Users, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

const Profile = () => {
  const navigate = useNavigate();
  const [isOpen, setIsOpen] = useState(true);
  
  // Profile data state
  const [profile, setProfile] = useState({
    name: '<PERSON> <PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    linkedin: 'https://linkedin.com/in/alexjohnson',
    city: 'San Francisco',
    state: 'California',
    company: 'TechCorp Inc.',
    role: 'Professional', // Aspiring, Professional, Coach
    bio: 'Passionate software engineer with 8+ years of experience in full-stack development, cloud architecture, and team leadership. Love sharing knowledge about modern web technologies.',
    profileImage: '',
    points: 1250,
    acceptedOpportunities: 8,
    referrals: 3
  });

  const [files, setFiles] = useState({
    coverLetter: null,
    speakerOnePager: null
  });

  const handleProfileUpdate = (field: string, value: any) => {
    setProfile({ ...profile, [field]: value });
  };

  const handleFileUpload = (fileType: 'coverLetter' | 'speakerOnePager', file: File | null) => {
    setFiles({ ...files, [fileType]: file });
  };

  const handleClose = () => {
    setIsOpen(false);
    navigate('/speaker/dashboard');
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="text-2xl font-bold text-foreground">Speaker Profile</DialogTitle>
        </DialogHeader>

        <div className="p-6">
          <div className="space-y-6">
            {/* Profile Header with Metrics */}
            <div className="flex items-start gap-4 mb-6">
              <div className="relative">
                <Avatar className="h-16 w-16 border-2 border-muted">
                  <AvatarImage src={profile.profileImage} alt={profile.name} />
                  <AvatarFallback className="bg-muted text-foreground text-sm">
                    {profile.name.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <Button
                  size="sm"
                  className="absolute -bottom-1 -right-1 h-6 w-6 rounded-full p-0 bg-primary hover:bg-primary/90"
                >
                  <Camera className="h-3 w-3" />
                </Button>
              </div>
              <div className="flex-1">
                <div className="flex items-start justify-between">
                  <div>
                    <h3 className="text-lg font-bold text-foreground">{profile.name}</h3>
                    <p className="text-sm text-muted-foreground">{profile.role} Speaker</p>
                    <p className="text-xs text-muted-foreground">{profile.company}</p>
                    <Badge variant="secondary" className="text-xs mt-1">
                      {profile.city}, {profile.state}
                    </Badge>
                  </div>
                  
                  {/* Metrics Cards - 3x1 Layout */}
                  <div className="grid grid-cols-3 gap-2">
                    <Card className="bg-muted/10 border-border-subtle p-2 aspect-square flex flex-col items-center justify-center text-center min-w-[60px]">
                      <Trophy className="h-4 w-4 text-primary mb-1" />
                      <span className="text-sm font-bold text-primary">{profile.points}</span>
                      <span className="text-[10px] text-muted-foreground">XP Points</span>
                    </Card>
                    
                    <Card className="bg-muted/10 border-border-subtle p-2 aspect-square flex flex-col items-center justify-center text-center min-w-[60px]">
                      <Calendar className="h-4 w-4 text-success mb-1" />
                      <span className="text-sm font-bold text-success">{profile.acceptedOpportunities}</span>
                      <span className="text-[10px] text-muted-foreground">Talks</span>
                    </Card>
                    
                    <Card className="bg-muted/10 border-border-subtle p-2 aspect-square flex flex-col items-center justify-center text-center min-w-[60px]">
                      <Users className="h-4 w-4 text-warning mb-1" />
                      <span className="text-sm font-bold text-warning">{profile.referrals}</span>
                      <span className="text-[10px] text-muted-foreground">Referrals</span>
                    </Card>
                  </div>
                </div>
              </div>
            </div>

            {/* Personal Information Form */}
            <Card className="bg-card border-border">
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Personal Information</CardTitle>
                <CardDescription className="text-sm">Update your contact and professional details</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="space-y-1">
                    <Label htmlFor="name" className="text-xs">Full Name</Label>
                    <Input
                      id="name"
                      value={profile.name}
                      onChange={(e) => handleProfileUpdate('name', e.target.value)}
                      className="bg-background border-border h-8 text-sm"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="email" className="text-xs">Email Address</Label>
                    <Input
                      id="email"
                      type="email"
                      value={profile.email}
                      onChange={(e) => handleProfileUpdate('email', e.target.value)}
                      className="bg-background border-border h-8 text-sm"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <div className="space-y-1">
                    <Label htmlFor="phone" className="text-xs">Phone Number</Label>
                    <Input
                      id="phone"
                      value={profile.phone}
                      onChange={(e) => handleProfileUpdate('phone', e.target.value)}
                      className="bg-background border-border h-8 text-sm"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="linkedin" className="text-xs">LinkedIn URL</Label>
                    <Input
                      id="linkedin"
                      value={profile.linkedin}
                      onChange={(e) => handleProfileUpdate('linkedin', e.target.value)}
                      className="bg-background border-border h-8 text-sm"
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                  <div className="space-y-1">
                    <Label htmlFor="city" className="text-xs">City</Label>
                    <Input
                      id="city"
                      value={profile.city}
                      onChange={(e) => handleProfileUpdate('city', e.target.value)}
                      className="bg-background border-border h-8 text-sm"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="state" className="text-xs">State</Label>
                    <Input
                      id="state"
                      value={profile.state}
                      onChange={(e) => handleProfileUpdate('state', e.target.value)}
                      className="bg-background border-border h-8 text-sm"
                    />
                  </div>
                  <div className="space-y-1">
                    <Label htmlFor="role" className="text-xs">Speaker Role</Label>
                    <Select value={profile.role} onValueChange={(value) => handleProfileUpdate('role', value)}>
                      <SelectTrigger className="bg-background border-border h-8 text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Aspiring">Aspiring Speaker</SelectItem>
                        <SelectItem value="Professional">Professional Speaker</SelectItem>
                        <SelectItem value="Coach">Speaking Coach</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="space-y-1">
                  <Label htmlFor="company" className="text-xs">Company</Label>
                  <Input
                    id="company"
                    value={profile.company}
                    onChange={(e) => handleProfileUpdate('company', e.target.value)}
                    className="bg-background border-border h-8 text-sm"
                  />
                </div>

                <div className="space-y-1">
                  <Label htmlFor="bio" className="text-xs">Professional Bio</Label>
                  <Textarea
                    id="bio"
                    value={profile.bio}
                    onChange={(e) => handleProfileUpdate('bio', e.target.value)}
                    className="bg-background border-border min-h-[80px] text-sm resize-none"
                    placeholder="Tell us about your speaking experience and expertise..."
                  />
                </div>
              </CardContent>
            </Card>

            {/* File Upload Section */}
            <Card className="bg-card border-border">
              <CardHeader className="pb-3">
                <CardTitle className="text-base">Speaker Assets</CardTitle>
                <CardDescription className="text-sm">Upload your cover letter and speaker one-pager PDF</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Cover Letter Upload */}
                <div className="space-y-1">
                  <Label htmlFor="cover-letter" className="text-xs">Cover Letter (PDF)</Label>
                  <div className="border-2 border-dashed border-border rounded-lg p-4 text-center hover:border-primary/50 transition-colors">
                    <FileText className="h-6 w-6 text-muted-foreground mx-auto mb-2" />
                    <p className="text-xs text-muted-foreground mb-2">
                      {files.coverLetter ? files.coverLetter.name : "Upload your cover letter"}
                    </p>
                    <Button variant="outline" size="sm" className="h-7 text-xs">
                      <Upload className="h-3 w-3 mr-1" />
                      {files.coverLetter ? "Replace File" : "Choose File"}
                    </Button>
                  </div>
                </div>

                {/* Speaker One-Pager Upload */}
                <div className="space-y-1">
                  <Label htmlFor="speaker-pager" className="text-xs">Speaker One-Pager (PDF)</Label>
                  <div className="border-2 border-dashed border-border rounded-lg p-4 text-center hover:border-primary/50 transition-colors">
                    <FileText className="h-6 w-6 text-muted-foreground mx-auto mb-2" />
                    <p className="text-xs text-muted-foreground mb-2">
                      {files.speakerOnePager ? files.speakerOnePager.name : "Upload your speaker one-pager"}
                    </p>
                    <Button variant="outline" size="sm" className="h-7 text-xs">
                      <Upload className="h-3 w-3 mr-1" />
                      {files.speakerOnePager ? "Replace File" : "Choose File"}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Save Button at the bottom */}
            <Button className="w-full bg-primary hover:bg-primary/90">
              Save Changes
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default Profile;