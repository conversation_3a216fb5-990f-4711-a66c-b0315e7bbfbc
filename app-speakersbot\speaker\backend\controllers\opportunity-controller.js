
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("../helpers/response-codes");
const CustomError = require("../helpers/custome-error");

const opportunityService = require("../services/opportunity-service");
const ApiResponse = require("../helpers/api-response");

/**
 * Get opportunities for the currently authenticated speaker
 * This endpoint retrieves all opportunities associated with the logged-in speaker
 * Authentication is required and handled by middleware
 * 
 * @param {Object} req - Express request object
 * @param {Object} req.user - Authenticated user object (set by verifyToken middleware)
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 * @returns {Object} JSON response with speaker's opportunities
 * @throws {CustomError} When speaker authentication is missing or service fails
 */
exports.getAllOpportunities = async (req, res, next) => {
    try {
        // Extract speaker ID from authenticated user session
        const speakerId = req.user?.id || req.userId;
        
        // Validate that speaker authentication is present
        if (!speakerId) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Speaker authentication required");
        }

        // Pass speaker ID to service layer
        req.speakerId = speakerId;
        
        // Retrieve opportunities for the authenticated speaker
        const result = await opportunityService.getAllOpportunities(req);
        const { status, message, data } = result;

        if (status) {
            res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ 
                message, 
                data 
            }));
        } else {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
        }
    } catch (error) {
        next(error);
    }
};

exports.getAllTodayOpportunities = async (req, res, next) => {
    try {
        // Extract speaker ID from authenticated user session
        const speakerId = req.user?.id || req.userId;
        
        // Validate that speaker authentication is present
        if (!speakerId) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Speaker authentication required");
        }

        // Pass speaker ID to service layer
        req.speakerId = speakerId;

        // Retrieve opportunities for the authenticated speaker since last login
        const result = await opportunityService.getAllTodayOpportunities(req);
        const { status, message, data } = result;

        if (status) {
            res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({
                message,
                data
            }));
        } else {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
        }
    } catch (error) {
        next(error);
    }
};

/**
 * Update opportunity by id
 */
exports.updateOpportunity = async (req, res, next) => {
    try {
        const result = await opportunityService.updateOpportunity(req);
        const { status, message, data } = result;
        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    } catch (error) {
        next(error);
    }
};

/**
 * Get opportunities pipeline data for the authenticated speaker
 */
exports.getOpportunitiesPipeline = async (req, res, next) => {
    try {
        const result = await opportunityService.getOpportunitiesPipeline(req);
        const { status, message, data } = result;
        // console.log("filters", filters);
        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    } catch (error) {
        next(error);
    }
};

exports.getLostOpportunitiesTracker = async (req, res, next) => {
    try {
        const result = await opportunityService.getLostOpportunitiesTracker(req);
        const { status, message, data, pagination } = result;
        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data, pagination }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    } catch (error) {
        next(error);
    }
};

exports.getAllOpportunitiesDueIn72Hours = async (req, res, next) => {
    try {
        // Extract speaker ID from authenticated user session
        const speakerId = req.user?.id || req.userId;

        // Validate that speaker authentication is present
        if (!speakerId) {
            throw new CustomError(RESPONSE_CODES.UNAUTHORIZED, "Speaker authentication required");
        }

        // Pass speaker ID to service layer
        req.speakerId = speakerId;

        // Retrieve opportunities for the authenticated speaker due in 72 hours
        const result = await opportunityService.getAllOpportunitiesDueIn72Hours(req);
        const { status, message, data, pagination } = result;

        if (status) {
            res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({
                message,
                data,
                pagination
            }));
        } else {
            throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
        }
    } catch (error) {
        next(error);
    }
};

exports.getAllOpportunityTopics = async (req, res, next) => {
    try {
        const result = await opportunityService.getAllOpportunityTopics(req);
        const { status, message, data } = result;
        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    } catch (error) {
        next(error);
    }
};

exports.getAllOpportunityLocations = async (req, res, next) => {
    try {
        const result = await opportunityService.getAllOpportunityLocations(req);
        const { status, message, data } = result;
        if (status) res.status(RESPONSE_CODES.SUCCESS).json(ApiResponse({ message, data }));
        else throw new CustomError(RESPONSE_CODES.BAD_REQUEST, RESPONSE_MESSAGES.BAD_REQUEST);
    } catch (error) {
        next(error);
    }
};
