import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from "../ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import {
  ResponsiveContainer,
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
} from "recharts";
import {
  ArrowUpOutlined,
  ArrowDownOutlined,
  CreditCardOutlined,
  UserOutlined,
} from "@ant-design/icons";
import { AlertTriangle } from "lucide-react";
import { useGetRevenueQuery } from "../../apis/dashboardApi";
import { InvalidTokenHandler } from "../common/InvalidTokenHandler";
import { useToast } from "../../hooks/use-toast";

// Helper component for subscription cards
const SubscriptionCard: React.FC<{
  title: string;
  value: string | number;
  icon: React.ReactNode;
}> = ({ title, value, icon }) => {
  return (
    <Card className="bg-tertiary border-border">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm font-medium text-muted-foreground">{title}</p>
            <p className="text-2xl font-bold text-foreground">{value}</p>
          </div>
          <div className="h-8 w-8">{icon}</div>
        </div>
      </CardContent>
    </Card>
  );
};

// Helper component for subscription section
const SubscriptionSection: React.FC<{
  title: string;
  data: any;
}> = ({ title, data }) => (
  <div className="space-y-4">
    <div>
      <h4 className="text-lg font-semibold bg-gradient-to-r from-blue-500 to-purple-500 via-red-500 bg-clip-text text-transparent mb-2">
        {title} - Revenue + Subscription View
      </h4>
    </div>
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
      <SubscriptionCard
        title="New"
        value={data.newSubscriptions.count || 0}
        icon={<UserOutlined className="h-8 w-8 text-green-500" />}
      />
      <SubscriptionCard
        title="Recurring"
        value={data.recurringSubscriptions.count || 0}
        icon={<CreditCardOutlined className="h-8 w-8 text-blue-500" />}
      />
      <SubscriptionCard
        title="Expansion"
        value={data.expansionSubscriptions.count || 0}
        icon={<ArrowUpOutlined className="h-8 w-8 text-green-500" />}
      />
      <SubscriptionCard
        title="Churned"
        value={data.churnedSubscriptions.count || 0}
        icon={<ArrowDownOutlined className="h-8 w-8 text-red-500" />}
      />
    </div>
  </div>
);

const RevenueAnalytics: React.FC = () => {
  const { data, isLoading, error } = useGetRevenueQuery(undefined, {
    refetchOnMountOrArgChange: true,
    refetchOnReconnect: true,
  });

  const rev = data?.data;

  // State for revenue data
  const [revenueState, setRevenueState] = useState<any>(null);
  const { toast } = useToast();

  // Effect to handle data and show success toast when data loads
  useEffect(() => {
    if (data) {
      setRevenueState(data);
      // Show success toast when data loads
      // toast({
      //   title: "Revenue analytics loaded successfully",
      // });
    }
  }, [data, toast]);

  // Show error toast when there's an error
  useEffect(() => {
    if (error) {
      toast({
        title: "Failed to load revenue data",
        variant: "destructive",
      });
    }
  }, [error, toast]);

  // Map API data to the three subscription types
  const monthlySubscriptionData = {
    newSubscriptions: {
      count: rev?.newMonthlyPlanSubscriptions?.count || 0,
    },
    recurringSubscriptions: {
      count: rev?.recurringMonthlyPlanSubscriptions?.count || 0,
    },
    expansionSubscriptions: {
      count: rev?.expansionMonthlyPlanSubscriptions?.count || 0,
    },
    churnedSubscriptions: {
      count: rev?.churnedMonthlyPlanSubscriptions?.count || 0,
    },
  };

  const yearlySubscriptionData = {
    newSubscriptions: {
      count: rev?.newYearlyPlanSubscriptions?.count || 0,
    },
    recurringSubscriptions: {
      count: rev?.recurringYearlyPlanSubscriptions?.count || 0,
    },
    expansionSubscriptions: {
      count: rev?.expansionYearlyPlanSubscriptions?.count || 0,
    },
    churnedSubscriptions: {
      count: rev?.churnedYearlyPlanSubscriptions?.count || 0,
    },
  };

  const lifetimeSubscriptionData = {
    newSubscriptions: {
      count: rev?.newLifetimePlanSubscriptions?.count || 0    ,
    },
    recurringSubscriptions: {
      count: rev?.recurringLifetimePlanSubscriptions?.count || 0,
    },
    expansionSubscriptions: {
      count: rev?.expansionLifetimePlanSubscriptions?.count || 0,
    },
    churnedSubscriptions: {
      count: rev?.churnedLifetimePlanSubscriptions?.count || 0,
    },
  };

  const subscriptionTrend = (rev?.subscriptionTrends || []).map((t: any) => ({
    month: t.month,
    newSubs: t.newSub,
    expansions: t.expansion,
    churned: t.churned,
  }));

  const arrByPlan = (rev?.arrByPlan?.plans || []).map((p: any) => ({
    plan: p.planName,
    subscribers: Number(p.subscriptionCount),
    arr: Number(p.arr),
  }));

  const paymentFailures = (rev?.paymentFailuresRecovery?.monthlyData || []).map(
    (m: any) => ({
      date: m.month,
      failures: m.failedPayments,
      recovered: m.recoveredPayments,
      failureRate: Number(((m.failedPayments / m.totalRecurringPayments) * 100).toFixed(2)),
    })
  );

  if (isLoading) {
    return (
      <div className="space-y-8">
        {/* Monthly Section Skeleton */}
        <div className="space-y-4">
          <div>
            <Skeleton className="h-5 w-40" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i} className="bg-tertiary border-border">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-6 w-28" />
                    <Skeleton className="h-3 w-32" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Yearly Section Skeleton */}
        <div className="space-y-4">
          <div>
            <Skeleton className="h-5 w-40" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i} className="bg-tertiary border-border">
                <CardContent className="p-4">
                  <div className="space-y-3">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-6 w-28" />
                    <Skeleton className="h-3 w-32" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Lifetime Section Skeleton - Single Card */}
        <div className="space-y-4">
          <div>
            <Skeleton className="h-5 w-40" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <Card className="bg-tertiary border-border">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-6 w-28" />
                  <Skeleton className="h-3 w-32" />
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {Array.from({ length: 2 }).map((_, i) => (
            <Card key={i} className="bg-tertiary border-border">
              <CardHeader>
                <Skeleton className="h-5 w-40" />
              </CardHeader>
              <CardContent className="pt-6">
                <Skeleton className="h-[350px] w-full" />
              </CardContent>
            </Card>
          ))}
        </div>

        <Card className="bg-tertiary border-border">
          <CardHeader>
            <Skeleton className="h-5 w-60" />
            <Skeleton className="h-4 w-72 mt-2" />
          </CardHeader>
          <CardContent className="pt-6">
            <Skeleton className="h-[350px] w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <InvalidTokenHandler error={error} />
      <div className="space-y-8">
        {/* Show error state */}
        {error && (
          <Card className="bg-tertiary border-border">
            <CardContent className="p-8 text-center">
              <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-foreground mb-2">
                Error Loading Data
              </h3>
              <p className="text-muted-foreground">
                Unable to fetch revenue analytics data. Please try again later.
              </p>
            </CardContent>
          </Card>
        )}

        {/* Show data when loaded and no error */}
        {!isLoading && !error && (
          <>
            {/* Monthly Subscriptions Section */}
            <SubscriptionSection
              title="Monthly"
              data={monthlySubscriptionData}
            />

            {/* Yearly Subscriptions Section */}
            <SubscriptionSection title="Yearly" data={yearlySubscriptionData} />

            {/* Lifetime Subscriptions Section - Single Card */}
            <div className="space-y-4">
              <div>
                <h4 className="text-lg font-semibold bg-gradient-to-r from-blue-500 to-purple-500 via-red-500 bg-clip-text text-transparent mb-2">
                  Lifetime - Revenue + Subscription View
                </h4>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                <SubscriptionCard
                  title="New"
                  value={lifetimeSubscriptionData.newSubscriptions.count || 0 }
                  icon={<UserOutlined className="h-8 w-8 text-green-500" />}
                />
              </div>
            </div>

            {/* Charts */}
            <div className="grid grid-cols-1 xl:grid-cols-2 gap-8">
              <Card className="bg-tertiary border-border">
                <CardHeader>
                  <CardTitle className="text-foreground">
                    Subscription Trends
                  </CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <ResponsiveContainer width="100%" height={350}>
                    {subscriptionTrend.length > 0 ? (   
                    <LineChart data={subscriptionTrend}>
                      <CartesianGrid
                        strokeDasharray="3 3"
                        stroke="hsl(var(--border))"
                      />
                      <XAxis
                        dataKey="month"
                        tick={{ fill: "hsl(var(--foreground))" }}
                        axisLine={{ stroke: "hsl(var(--border))" }}
                      />
                      <YAxis
                        tick={{ fill: "hsl(var(--foreground))" }}
                        axisLine={{ stroke: "hsl(var(--border))" }}
                      />
                      <Tooltip
                        contentStyle={{
                          backgroundColor: "hsl(var(--popover))",
                          border: "1px solid hsl(var(--border))",
                          borderRadius: "8px",
                          color: "hsl(var(--popover-foreground))",
                        }}
                      />
                      <Legend />
                      <Line
                        type="monotone"
                        dataKey="newSubs"
                        stroke="hsl(var(--dashboard-dark-blue))"
                        name="New Subs"
                        strokeWidth={2}
                      />
                      <Line
                        type="monotone"
                        dataKey="expansions"
                        stroke="hsl(var(--dashboard-medium-blue))"
                        name="Expansions"
                        strokeWidth={2}
                      />
                      <Line
                        type="monotone"
                        dataKey="churned"
                        stroke="#ef4444"
                        name="Churned"
                        strokeWidth={2}
                      />
                    </LineChart>
                  ) : (
                    <div className="flex justify-center items-center h-full">
                      <p className="text-muted-foreground text-sm">No Subscription Trends Data Available</p>
                    </div>
                  )}
                  </ResponsiveContainer>
                </CardContent>
              </Card>

              <Card className="bg-tertiary border-border">
                <CardHeader>
                  <CardTitle className="text-foreground">ARR by Plan</CardTitle>
                </CardHeader>
                <CardContent className="pt-6">
                  <ResponsiveContainer width="100%" height={350}>
                    {arrByPlan.length > 0 ? (   
                      <BarChart data={arrByPlan} barCategoryGap="20%" barGap={2}>
                      <CartesianGrid
                        strokeDasharray="3 3"
                        stroke="hsl(var(--border))"
                      />
                      <XAxis
                        dataKey="plan"
                        interval={0}
                        tick={{ fill: "hsl(var(--foreground))", fontSize: 12 }}
                        tickMargin={8}
                        height={50}
                        angle={-15}
                        axisLine={{ stroke: "hsl(var(--border))" }}
                      />
                      <YAxis
                        tick={{ fill: "hsl(var(--foreground))" }}
                        axisLine={{ stroke: "hsl(var(--border))" }}
                      />
                      <Tooltip
                        content={({ active, payload, label }) => {
                          if (active && payload && payload.length) {
                            return (
                              <div className="bg-popover border border-border rounded-lg p-3 shadow-lg">
                                <p className="font-medium text-popover-foreground mb-2">{label}</p>
                                {payload.map((entry, index) => (
                                  <div key={index} className="flex items-center gap-2">
                                    <div 
                                      className="w-3 h-3 rounded-sm" 
                                      style={{ backgroundColor: entry.color }}
                                    />
                                    <span className="text-sm text-popover-foreground">
                                      {entry.name}: {entry.name === "ARR ($)" ? `$${entry.value?.toLocaleString()}` : entry.value}
                                    </span>
                                  </div>
                                ))}
                              </div>
                            );
                          }
                          return null;
                        }}
                      />
                      <Legend />
                      <Bar
                        dataKey="arr"
                        fill="hsl(var(--dashboard-dark-blue))"
                        name="ARR ($)"
                      />
                      <Bar
                        dataKey="subscribers"
                        fill="hsl(var(--dashboard-light-blue))"
                        name="Subscribers"
                      />
                    </BarChart>
                  ) : (
                    <div className="flex justify-center items-center h-full">
                      <p className="text-muted-foreground text-sm">No ARR by Plan Data Available</p>
                    </div>
                  )}
                  </ResponsiveContainer>
                </CardContent>
              </Card>
            </div>
            <Card className="bg-tertiary border-border">
              <CardHeader>
                <div>
                  <CardTitle className="text-foreground">
                    Payment Failures & Recovery
                  </CardTitle>
                  <p className="text-muted-foreground text-sm mt-1">
                    Daily payment processing metrics with failure rates and
                    recovery success
                  </p>
                </div>
              </CardHeader>
              <CardContent className="pt-6">
                <ResponsiveContainer width="100%" height={350}>
                  {paymentFailures.length > 0 ? (   
                    <BarChart data={paymentFailures}>
                    <CartesianGrid
                      strokeDasharray="3 3"
                      stroke="hsl(var(--border))"
                    />
                    <XAxis
                      dataKey="date"
                      tick={{ fill: "hsl(var(--foreground))" }}
                      axisLine={{ stroke: "hsl(var(--border))" }}
                    />
                    <YAxis
                      tick={{ fill: "hsl(var(--foreground))" }}
                      axisLine={{ stroke: "hsl(var(--border))" }}
                    />
                    <Tooltip
                      formatter={(value: any, name: any, item: any) => {
                        const key =
                          (item && item.dataKey) ||
                          (typeof name === "string" ? name : "");
                        if (key === "failureRate") {
                          return [`${value}%`, "Failure Rate"];
                        }
                        const label =
                          key === "failures"
                            ? "Failures"
                            : key === "recovered"
                            ? "Recovered"
                            : String(name);
                        return [value, label];
                      }}
                      contentStyle={{
                        backgroundColor: "hsl(var(--popover))",
                        border: "1px solid hsl(var(--border))",
                        borderRadius: "8px",
                        color: "hsl(var(--popover-foreground))",
                      }}
                    />
                    <Legend />
                    <Bar dataKey="failures" fill="#ef4444" name="Failures" />
                    <Bar
                      dataKey="recovered"
                      fill="hsl(var(--dashboard-medium-blue))"
                      name="Recovered"
                    />
                  </BarChart>
                  ) : (
                    <div className="flex justify-center items-center h-full">
                      <p className="text-muted-foreground text-sm">No Payment Failures & Recovery Data Available</p>
                    </div>
                  )}
                </ResponsiveContainer>
              </CardContent>
            </Card>
          </>
        )}
      </div>
    </>
  );
};

export default RevenueAnalytics;
