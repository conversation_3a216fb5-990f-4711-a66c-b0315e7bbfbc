
const { Op } = require("sequelize");
const CustomError = require("./custome-error");
const { RESPONSE_CODES, RESPONSE_MESSAGES } = require("./response-codes");
const crypto = require('crypto');

exports.getPagingData = (count, resultPerPage, page) => {

    const totalPages = Math.ceil(count / resultPerPage) || 0;

    const pageData = {

        totalPage: totalPages,
        page: page,
        total: count,
        limit: resultPerPage,
    };

    return pageData;
};

exports.parsePagination = (query) => {
    const { page = 1, limit = 10 } = query;

    if (isNaN(page) || page < 1)
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid page number");
    if (isNaN(limit) || limit < 1)
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, "Invalid limit number");

    const pageInt = parseInt(page, 10);
    const limitInt = parseInt(limit, 10);
    const offset = (pageInt - 1) * limitInt;

    return { page: pageInt, limit: limitInt, offset };
};


exports.parseJSONSafely = (jsonString, errorMessage) => {
    try {
        return jsonString ? JSON.parse(jsonString) : {};
    } catch (error) {
        console.error(`${errorMessage}:`, error.message);
        throw new CustomError(RESPONSE_CODES.BAD_REQUEST, errorMessage);
    }
};


exports.buildSearchWhereClause = (search, fields) => {
    if (!search || !fields || fields.length === 0) return {};

    return {
        [Op.or]: fields.map(field => ({
            [field]: { [Op.like]: `%${search}%` }
        }))
    }
};

exports.buildFilterWhereClause = (filters) => {
    if (!filters || typeof filters !== 'object' || Object.keys(filters).length === 0) return {};

    // Get all filter fields that have values
    const validFilters = Object.keys(filters).filter(field => {
        const value = filters[field];
        return value !== null && value !== undefined && value !== '';
    });

    if (validFilters.length === 0) return {};

    // Create AND conditions for each field with exact match
    const whereClause = {};
    validFilters.forEach(field => {
        whereClause[field] = filters[field];
    });

    return whereClause;
};

exports.buildSortClause = (sort) => {
    if (!sort || typeof sort !== 'object' || Object.keys(sort).length === 0) {
        // Default sort by id DESC
        return [['id', 'DESC']];
    }

    // Get all sort fields that have values
    const validSorts = Object.keys(sort).filter(field => {
        const direction = sort[field];
        return direction && (direction.toUpperCase() === 'ASC' || direction.toUpperCase() === 'DESC');
    });

    if (validSorts.length === 0) {
        // Default sort by id DESC if no valid sorts
        return [['id', 'DESC']];
    }

    // Create sort array for each field
    return validSorts.map(field => [field, sort[field].toUpperCase()]);
};

exports.encodeToBase64 = (inputString) => {
    const buffer = Buffer.from(inputString, "utf8");
    const base64String = buffer.toString("base64");
    return base64String;
};

exports.decodeFromBase64 = (encodedData) => {
    const decodedBuffer = Buffer.from(encodedData, "base64");
    return decodedBuffer.toString("utf8");
};
exports.generateReferralCode = () => {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// Week utilities
exports.getCurrentWeekMondayToSundayRange = () => {
    const now = new Date();
    const day = now.getDay();
    const diffToMonday = (day === 0 ? -6 : 1 - day);

    const monday = new Date(now);
    monday.setHours(0, 0, 0, 0);
    monday.setDate(monday.getDate() + diffToMonday);

    const sunday = new Date(monday);
    sunday.setDate(sunday.getDate() + 6);
    sunday.setHours(23, 59, 59, 999);

    return { start: monday, end: sunday };
};

// UTC-based week: Monday 00:00:00.000 UTC to Sunday 23:59:59.999 UTC
exports.getCurrentWeekMondayToSundayRangeUTC = () => {
    const now = new Date();
    console.log(now,"now")
    const day = now.getUTCDay(); // 0..6
    const diffToMonday = (day === 0 ? -6 : 1 - day);
     
    console.log(diffToMonday)
    const mondayUTC = new Date(Date.UTC(
        now.getUTCFullYear(), now.getUTCMonth(), now.getUTCDate(), 0, 0, 0, 0
    ));
    mondayUTC.setUTCDate(mondayUTC.getUTCDate() + diffToMonday);

    const sundayUTC = new Date(mondayUTC);
    sundayUTC.setUTCDate(sundayUTC.getUTCDate() + 6);
    sundayUTC.setUTCHours(23, 59, 59, 999);

    return { start: mondayUTC, end: sundayUTC };
}
// encryption helpers
const ENC_PREFIX = 'enc:';
const ALGORITHM = 'aes-256-gcm';
const IV_LENGTH = 12; // recommended for GCM

const getEncryptionKey = () => {
  const raw = process.env.SETTING_ENCRYPTION_KEY || '';
  if (!raw) throw new Error('Missing SETTING_ENCRYPTION_KEY env variable for settings encryption');
  // derive 32-byte key
  return crypto.createHash('sha256').update(String(raw)).digest();
};

exports.encryptValue = (plaintext) => {
  const key = getEncryptionKey();
  const iv = crypto.randomBytes(IV_LENGTH);
  const cipher = crypto.createCipheriv(ALGORITHM, key, iv);
  const encrypted = Buffer.concat([cipher.update(plaintext, 'utf8'), cipher.final()]);
  const tag = cipher.getAuthTag();
  // store iv:tag:ciphertext in base64
  return ENC_PREFIX + [iv.toString('base64'), tag.toString('base64'), encrypted.toString('base64')].join(':');
};

exports.decryptValue = (encString) => {
  if (!encString || !encString.startsWith(ENC_PREFIX)) return encString;
  const payload = encString.slice(ENC_PREFIX.length);
  const parts = payload.split(':');
  if (parts.length !== 3) throw new Error('Invalid encrypted payload');
  const [ivB64, tagB64, dataB64] = parts;
  const iv = Buffer.from(ivB64, 'base64');
  const tag = Buffer.from(tagB64, 'base64');
  const encrypted = Buffer.from(dataB64, 'base64');
  const key = getEncryptionKey();
  const decipher = crypto.createDecipheriv(ALGORITHM, key, iv);
  decipher.setAuthTag(tag);
  const decrypted = Buffer.concat([decipher.update(encrypted), decipher.final()]);
  return decrypted.toString('utf8');
};