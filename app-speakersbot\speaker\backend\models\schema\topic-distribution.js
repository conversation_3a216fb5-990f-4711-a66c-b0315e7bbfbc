
const connection = require("../connection");
const { DataTypes } = require("sequelize");

const TopicDistribution = connection.define("TopicDistribution", {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
        comment: 'Primary key',
    },
    search_engine: {
        type: DataTypes.STRING(50),
        allowNull: false,
        comment: 'Search engine name'
    },
    topic_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Topic ID from subcategories table',
    },
    topic_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        comment: 'Topic name for reference'
    },
    distribution_round: {
        type: DataTypes.INTEGER,
        allowNull: false,
        comment: 'Distribution round number'
    },
    is_rotating_topic: {
        type: DataTypes.TINYINT(1),
        allowNull: true,
        comment: 'Whether this is a rotating topic'
    },
    used_at: {
        type: DataTypes.DATE,
        allowNull: true,
        comment: 'When this topic was used'
    },
    is_active: {
        type: DataTypes.TINYINT(1),
        allowNull: true,
        comment: 'Whether this distribution is active'
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: 'Record creation timestamp',
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
        comment: 'Record last update timestamp',
    }
}, {
    tableName: "topic_distribution",
    timestamps: true,
    createdAt: 'created_at',
    updatedAt: 'updated_at',
    paranoid: false,

});

module.exports = TopicDistribution;