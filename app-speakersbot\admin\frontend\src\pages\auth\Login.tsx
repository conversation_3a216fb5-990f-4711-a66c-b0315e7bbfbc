import { useLoginMutation } from "@/apis/authApi";
import { useLazyGetAuthUserQuery } from "@/apis/usersApi";
import loginImg from "@/assets/images/login-img.png";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useToast } from "@/hooks/use-toast";
import { useAppDispatch } from "@/store/hooks";
import { setToken } from "@/store/slices/authSlice";
import {
  EyeInvisibleOutlined,
  EyeOutlined,
  LockOutlined,
  UserOutlined,
} from "@ant-design/icons";
import React from "react";
import { useNavigate, Link } from "react-router-dom";
import loginLogo from "/login-logo.png";

const Login: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const [loginMutation, { isLoading }] = useLoginMutation();
  const [getAuthUser] = useLazyGetAuthUserQuery();
  const { toast } = useToast();

  const [email, setEmail] = React.useState("");
  const [password, setPassword] = React.useState("");
  const [showPassword, setShowPassword] = React.useState(false);
  const [emailError, setEmailError] = React.useState("");

  // Email validation function
  const validateEmail = (email: string): boolean => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setEmail(value);
    
    if (value && !validateEmail(value)) {
      setEmailError("Please enter a valid email address");
    } else {
      setEmailError("");
    }
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Validate email before submission
    if (!validateEmail(email)) {
      setEmailError("Please enter a valid email address");
      return;
    }
    
    try {
      const result = await loginMutation({
        email,
        password,
      }).unwrap();

      if (result.status && result?.data?.token) {
        const { token } = result.data;
        toast({
          title: "Login Successful!",
          description: "Welcome back! Redirecting to dashboard...",
        });
        navigate("/admin/dashboard");
        dispatch(setToken(token));
      }
    } catch (error: any) {
      console.error("Login error:", error);
      toast({
        title: error?.data?.error?.message || "Invalid email or password",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="min-h-screen bg-foreground flex items-center justify-center p-6 bg-[hsla(217, 100%, 98%, 1)] bg-[#F4F8FF]">
      <div
        className="absolute inset-0 bg-cover bg-center"
        style={{
          backgroundImage: `url(${loginImg})`,
          clipPath: "polygon(0 0, 70% 0, 65% 100%, 0% 100%)",
        }}
      />
      <div className="[width:min(1100px,100%)] grid grid-cols-[1.25fr_1fr] xl:grid-cols-[1.5fr_1fr] bg-foreground rounded-[50px] overflow-hidden border border-[hsl(var(--dashboard-light-blue)/0.35)] shadow-lg relative z-20">
        <div
          className="relative min-h-[520px] bg-cover bg-center saturate-125 m-4 mr-0 rounded-[50px] overflow-hidden"
          style={{
            backgroundImage: `url(${loginImg})`,
            clipPath: "polygon(0 0, 102% 0, 85% 100%, 0% 100%)",
          }}
        >
          <div className="absolute inset-0 bg-[linear-gradient(200deg,rgba(0,0,0,0.15)_10%,rgba(0,0,0,0.55)_70%)]" />
        </div>

        <div className="bg-foreground flex flex-col justify-around py-[50px] px-10">
          <div>
            <img
              src={loginLogo}
              alt="login"
              className="w-full h-full max-h-[50px] object-contain"
            />
          </div>
          <div className="flex-1 flex flex-col justify-center">
            <div className="text-center">
              <h1 className="m-0 text-[58px] font-bold tracking-[0.4px] bg-muted-gradient bg-clip-text text-transparent">
                Hey There!
              </h1>
              <div className="mt-1.5 text-[12px] text-[#464646]">
                Welcome to Speaker bot AI
              </div>
            </div>

            <form onSubmit={handleSubmit} className="px-5 py-5">
              <div className="mb-4">
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email"
                  className={`rounded-[10px] bg-foreground text-background placeholder:text-background autofill:bg-foreground ${
                    emailError ? "border-red-500 focus:border-red-500" : ""
                  }`}
                  startIcon={<UserOutlined />}
                  value={email}
                  onChange={handleEmailChange}
                />
                {emailError && (
                  <p className="text-red-500 text-sm mt-1">{emailError}</p>
                )}
              </div>

              <div className="mb-2 relative">
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  required
                  placeholder="Enter your password"
                  className="rounded-[10px] bg-foreground text-background placeholder:text-background pr-10 autofill:bg-foreground"
                  startIcon={<LockOutlined />}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
                <button
                  type="button"
                  aria-label={showPassword ? "Hide password" : "Show password"}
                  onClick={() => setShowPassword((v) => !v)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-background/80 hover:text-background transition-colors"
                >
                  {showPassword ? <EyeInvisibleOutlined /> : <EyeOutlined />}
                </button>
              </div>
              <div className="text-right mt-2">
                <Link
                  to="/admin/forgot-password"
                  className="text-sm text-destructive transition-colors"
                >
                  Forget Password?
                </Link>
              </div>

              <Button
                type="submit"
                disabled={isLoading}
                className={`mt-5 h-12 w-full rounded-[50px] bg-muted-gradient border-0 shadow-[0_8px_24px_rgba(2,132,199,0.35)] text-white transition-transform duration-500 ${
                  isLoading
                    ? "animate-pulse cursor-wait"
                    : "hover:shadow-[0_12px_28px_rgba(2,132,199,0.45)] hover:-translate-y-0.5 active:translate-y-0 active:scale-[0.98]"
                }`}
              >
                {isLoading ? "Logging in..." : "Log in"}
              </Button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Login;
