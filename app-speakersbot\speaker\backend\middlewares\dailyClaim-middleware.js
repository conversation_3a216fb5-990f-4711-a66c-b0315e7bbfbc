// middleware/dailyClaimMiddleware.js
const { awardDailyAndStreaks } = require('../services/gamification-service');

exports.dailyClaimMiddleware = (options = {}) => {
  return async (req, res, next) => {
    try {
      if (!req.user || !req.user.id) {
        return res.status(401).json({ success: false, message: 'Unauthorized' });
      }

      // membershipStartDate: if you store this in user profile, fetch it here
      // assume req.user.membershipStartDate may exist; fallback null
      const membershipStartDate = req.user.membershipStartDate || null;

      // call service (idempotent)
      const result = await awardDailyAndStreaks(req.user.id, membershipStartDate);

      // attach to response for controller
      res.locals.gamification = result;
      next();
    } catch (err) {
      console.error('dailyClaimMiddleware error:', err);
      // fail silently or send error — prefer to fail gracefully so login doesn't break
      res.locals.gamification = { error: true, message: err.message };
      next();
    }
  };
};
