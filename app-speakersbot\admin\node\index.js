const express = require("express");
const cors = require('cors');
const db = require("./models");
const path = require("path");
const swaggerUi = require('swagger-ui-express');
const yaml = require('yamljs');
const swaggerDocument = yaml.load(path.join(__dirname, './openapi-spec.yaml'));
const { exceptionHandling } = require("./middlewares/exceptionalHandling");
const verifyToken = require("./middlewares/verify-token");
const verifyManualToken = require("./middlewares/verify-manual-token");

db.sequelize.sync({ alter: true }).then(() => {
    console.log("Synced DB");
}).catch((err) => {
    console.error("Failed to sync DB:", err);
});

const app = express();
const port = process.env.PORT || 3000;

app.set('trust proxy', true);
app.use(cors());

app.use("/admin/api/v1/*splat", verifyToken);
require("./routes/webhook-route")(app);

// health check
app.get('/health', (req, res) => {
    return res.status(200).json({ message: 'OK' });
})

app.use(express.json());
app.use(express.urlencoded({ extended: true }));

require("./routes/user-route")(app)
require("./routes/role-route")(app)
require("./routes/auth-route")(app)
require("./routes/pricing_plan-route")(app)
require("./routes/stripe-route")(app)
require("./routes/subscription-route")(app)
require("./routes/opportunity-route")(app)
require("./routes/setting-route")(app)
require("./routes/gamification-route")(app)
require("./routes/affiliate-route")(app)
require("./routes/form-route")(app)
require("./routes/speaker-route")(app)
require("./routes/opportunity-route")(app)
require("./routes/scraping-route")(app)
require("./routes/matching-queue-route")(app)
require("./routes/affiliate-inquiries-route")(app)
require("./routes/dashboard-route")(app)
require("./routes/upload-route")(app)

app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(swaggerDocument));

// Serve static files from the dist directory
app.use(express.static(path.join(__dirname, 'dist')));

app.use(exceptionHandling)

app.get("/admin/*splat", (req, res) => {
    res.sendFile(path.join(__dirname, "/dist/index.html"));
});

app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}/`);
});

// Handle uncaught exceptions to prevent server crashes
process.on('uncaughtException', (err) => {
    console.error('Uncaught Exception:', err);
    process.exit(1);

});
