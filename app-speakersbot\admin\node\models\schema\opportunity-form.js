const { DataTypes } = require("sequelize");
const connection = require("../connection");

const opportunityForm = connection.define("opportunityForm", {
    id: {
        type: DataTypes.INTEGER,
        autoIncrement: true,
        primaryKey: true,
        comment: "Primary key for the opportunity form record",
    },
    opportunity_id: {
        type: DataTypes.INTEGER,
        references: {
            model: "opportunities",
            key: "id",
        },
        comment: "Foreign key referencing the opportunity",
    },
    type: {
        type: DataTypes.ENUM('email', 'form'),
    },
    form_data: {
        type: DataTypes.JSON,
        allowNull: false,
        comment: "Form data",
    },
    form_url:{
        type: DataTypes.TEXT,
        allowNull: true,
        comment: "URL of the form",
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
        comment: "Record creation timestamp",
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: connection.literal('CURRENT_TIMESTAMP'),
        comment: "Record last update timestamp",
    }
}, {
    tableName: 'opportunity_form',
    timestamps: true,
    createdAt: "created_at",
    updatedAt: "updated_at",
});

module.exports = opportunityForm;