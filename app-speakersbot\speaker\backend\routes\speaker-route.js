const speakerController = require("../controllers/speaker-controller");
 const upload = require("../middlewares/upload-middleware");

const router = require("express").Router();

module.exports = (app) => {

    // Route to get all speakers
    router.get("/speakers", speakerController.getAllSpeakers);

    router.get("/speaker/:id", speakerController.getSpeakerById); 

    

    router.put("/speaker/match-opportunity", speakerController.updateSpeakerOpportunity); 

   
    router.put("/speaker/:id", speakerController.updateSpeaker);

    router.get("/export/speakers", speakerController.exportSpeakers);

    router.post("/intake-form", upload.any(), speakerController.intakeSpeakers);

    router.get("/speaker/opportunity/:id", speakerController.getSpeakerOpportunities);

    router.get("/speaker/history/:id", speakerController.getSpeakerHistory);

    router.get("/intake-form/progress", speakerController.getIntakeFormProgress);

    // ------------------------- speaker extensions-------------------------

    // signup speaker extension
    
    router.post("/speaker/extension/signup", speakerController.signupSpeakerExtension);

    // check if speaker verified (email verification)
    router.get("/speaker/email/verified/:speakerId", speakerController.checkEmailVerified);

    // verify email
    router.get("/speaker/email/verification", speakerController.verifyEmail);

    // resend verification email
    router.post("/speaker/email/resend-verification", speakerController.resendVerificationEmail);

    router.get("/intakeform", speakerController.getIntakeForm);

    app.use("/speaker/api/v1", router);


}
